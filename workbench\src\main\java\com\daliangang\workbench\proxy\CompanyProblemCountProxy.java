package com.daliangang.workbench.proxy;

import com.daliangang.workbench.entity.CompanyProblemCount;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import xyz.erupt.annotation.fun.DataProxy;
import xyz.erupt.jpa.dao.EruptDao;
import xyz.erupt.toolkit.utils.EruptDaoUtils;

import javax.annotation.Resource;
import javax.transaction.Transactional;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.List;
import java.util.Map;

@Service
@Slf4j

public class CompanyProblemCountProxy implements DataProxy<CompanyProblemCount> {

    @Resource
    private EruptDao eruptDao ;

    @Transactional
    @Override
    public void searchCondition(Map<String, Object> condition) {
        //获取本周的周一到周日日期
        SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd");
        Calendar now = Calendar.getInstance();
        now.set(Calendar.DAY_OF_WEEK,Calendar.MONDAY) ;
        String monday = formatter.format(now.getTime());

        now.add(Calendar.DATE,6);
        String sunday = formatter.format(now.getTime());

        String sql =
                " SELECT  '安全生产责任制' menu,"+
                        " ( SELECT concat('<a title =\"',GROUP_CONCAT(e.name),'\">',count( org_code ),'</a>') FROM tb_enterprise e WHERE e.org_code NOT IN ( SELECT company FROM tb_check_fill WHERE YEAR = DATE_FORMAT( now(), '%Y' ) AND submitted = 1 )) AS num UNION ALL"+
                        " SELECT '安全承诺公告' menu,"+
                        " ( SELECT concat('<a title =\"',GROUP_CONCAT(e.name),'\">',count( org_code ),'</a>') FROM tb_enterprise e WHERE e.org_code NOT IN ( SELECT company FROM tb_release WHERE filling_date = DATE_FORMAT( now(), '%Y-%m-%d' ) AND submitted = 1 )) AS num UNION ALL"+
                        " SELECT '可靠性报告单' menu,"+
                        " ( SELECT concat('<a title =\"',GROUP_CONCAT(e.name),'\">',count( org_code ),'</a>')  FROM tb_enterprise e WHERE e.org_code NOT IN ( SELECT company FROM tb_report WHERE modify_time = DATE_FORMAT( now(), '%Y-%m' ) AND is_report = 1 )) AS num UNION ALL"+
                        " SELECT '日检查' menu,"+
                        " ( SELECT concat('<a title =\"',GROUP_CONCAT(e.name),'\">',count( org_code ),'</a>')  FROM tb_enterprise e WHERE e.org_code NOT IN ( SELECT company FROM tb_daily_inspection WHERE inspection_time = DATE_FORMAT( now(), '%Y-%m-%d' ) AND submitted = 1 )) AS num UNION ALL"+
                        " SELECT '周报告' menu,"+
                        " ( SELECT concat('<a title =\"',GROUP_CONCAT(e.name),'\">',count( org_code ),'</a>')  FROM tb_enterprise e WHERE e.org_code NOT IN ( SELECT company FROM tb_weekly_report WHERE WEEKOFYEAR( inspection_time ) = WEEKOFYEAR( now()) AND submitted = 1 )) AS num UNION ALL"+
                        " SELECT '月调度' menu,"+
                        " ( SELECT concat('<a title =\"',GROUP_CONCAT(e.name),'\">',count( org_code ),'</a>')  FROM tb_enterprise e WHERE e.org_code NOT IN ( SELECT company FROM tb_monthly_scheduling WHERE report_month = DATE_FORMAT( now(), '%Y-%m' ) AND submitted = 1 )) AS num UNION ALL"+
                        " SELECT '码头结构检测' menu,"+
                        " ( SELECT concat('<a href =\\'#/build/table/WharfStructureInspection?condition=[{\"key\":\"checkStatus\",\"value\":\"已过期\"}]\\'>',count( * ),'</a>') FROM tb_wharf_structure_inspection WHERE DATE_FORMAT( validity_period, '%Y-%m-%d' ) <= DATE_FORMAT( now(), '%Y-%m-%d' ) ) AS num UNION ALL"+
                        " SELECT '消防设施检测' menu, "+
                        " ( SELECT concat('<a href =\\'#/build/table/FacilityInspection?condition=[{\"key\":\"checkStatus\",\"value\":\"已过期\"}]\\'>',count( * ),'</a>') FROM tb_facility_inspection WHERE DATE_FORMAT( validity_period, '%Y-%m-%d' ) <= DATE_FORMAT( now(), '%Y-%m-%d' )) AS num UNION ALL"+
                        " SELECT '防雷装置检测' menu," +
                        " ( SELECT concat('<a href =\\'#/build/table/LightningProtection?condition=[{\"key\":\"checkStatus\",\"value\":\"已过期\"}]\\'>',count( * ),'</a>') FROM tb_lightning_protection WHERE DATE_FORMAT( validity_period, '%Y-%m-%d' ) <= DATE_FORMAT( now(), '%Y-%m-%d' )) AS num UNION ALL"+
                        " SELECT '压力容器检测' menu," +
                        " ( SELECT concat('<a href =\\'#/build/table/PressureVessel?condition=[{\"key\":\"checkStatus\",\"value\":\"已过期\"}]\\'>',count( * ),'</a>') FROM tb_pressure_vessel WHERE DATE_FORMAT( validity_period, '%Y-%m-%d' ) <= DATE_FORMAT( now(), '%Y-%m-%d' )) AS num UNION ALL"+
                        " SELECT '储罐检测' menu, "+
                        " ( SELECT concat('<a href =\\'#/build/table/TankInspection?condition=[{\"key\":\"checkStatus\",\"value\":\"已过期\"}]\\'>',count( * ),'</a>') FROM tb_tank_inspection WHERE DATE_FORMAT( validity_period, '%Y-%m-%d' ) <= DATE_FORMAT( now(), '%Y-%m-%d' )) AS num UNION ALL"+
                        " SELECT '压力管道检测<' menu," +
                        " ( SELECT concat('<a href =\\'#/build/table/Penstock?condition=[{\"key\":\"checkStatus\",\"value\":\"已过期\"}]\\'>',count( * ),'</a>') FROM tb_penstock WHERE DATE_FORMAT( validity_period, '%Y-%m-%d' ) <= DATE_FORMAT( now(), '%Y-%m-%d' )) AS num UNION ALL"+
                        " SELECT '输油臂（软管）检测' menu," +
                        " ( SELECT concat('<a href =\\'#/build/table/Hose?condition=[{\"key\":\"checkStatus\",\"value\":\"已过期\"}]\\'>',count( * ),'</a>') FROM tb_hose WHERE DATE_FORMAT( validity_period, '%Y-%m-%d' ) <= DATE_FORMAT( now(), '%Y-%m-%d' )) AS num UNION ALL"+
                        " SELECT '压力表检测' menu," +
                        " ( SELECT concat('<a href =\\'#/build/table/PressureGauge?condition=[{\"key\":\"checkStatus\",\"value\":\"已过期\"}]\\'>',count( * ),'</a>') FROM tb_pressure_gauge WHERE DATE_FORMAT( validity_period, '%Y-%m-%d' ) <= DATE_FORMAT( now(), '%Y-%m-%d' )) AS num UNION ALL"+
                        " SELECT '安全阀检测' menu," +
                        " (SELECT concat('<a href =\\'#/build/table/SafetyValve?condition=[{\"key\":\"checkStatus\",\"value\":\"已过期\"}]\\'>',count( * ),'</a>') FROM tb_safety_valve WHERE DATE_FORMAT( validity_period, '%Y-%m-%d' ) <= DATE_FORMAT( now(), '%Y-%m-%d' )) AS num UNION ALL"+
                        " SELECT '可燃气体报警器检测' menu," +
                        " ( SELECT concat('<a href =\\'#/build/table/CombustibleGas?condition=[{\"key\":\"checkStatus\",\"value\":\"已过期\"}]\\'>',count( * ),'</a>') FROM tb_combustible_gas WHERE DATE_FORMAT( validity_period, '%Y-%m-%d' ) <= DATE_FORMAT( now(), '%Y-%m-%d' ) ) AS num UNION ALL"+
                        " SELECT  '起重机械检测' menu,"+
                        " ( SELECT concat('<a href =\\'#/build/table/HoistingMachinery?condition=[{\"key\":\"checkStatus\",\"value\":\"已过期\"}]\\'>',count( * ),'</a>') FROM tb_hoisting_machinery WHERE DATE_FORMAT( validity_period, '%Y-%m-%d' ) <= DATE_FORMAT( now(), '%Y-%m-%d' ) ) AS num UNION ALL"+
                        " SELECT '其他检测' menu," +
                        " ( SELECT concat('<a href =\\'#/build/table/OtherTests?condition=[{\"key\":\"checkStatus\",\"value\":\"已过期\"}]\\'>',count( * ),'</a>') FROM tb_other_tests WHERE DATE_FORMAT( validity_period, '%Y-%m-%d' ) <= DATE_FORMAT( now(), '%Y-%m-%d' )) AS num";

        List<CompanyProblemCount> companyProblemLists = EruptDaoUtils.selectOnes(sql, CompanyProblemCount.class);
        String truncateSql="truncate table tb_company_problem_count";
        eruptDao.getJdbcTemplate().execute(truncateSql);

        for(CompanyProblemCount companyProblemCount : companyProblemLists){
            eruptDao.persist(companyProblemCount);
        }

    }
}
