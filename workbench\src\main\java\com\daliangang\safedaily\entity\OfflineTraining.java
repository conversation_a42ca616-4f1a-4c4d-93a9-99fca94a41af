package com.daliangang.safedaily.entity;

import cn.hutool.json.JSONObject;
import com.daliangang.core.CompanyRenderHandler;
import com.daliangang.safedaily.operation.ButtonShowHandler;
import com.daliangang.safedaily.operation.DepartPowerHandler;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.SneakyThrows;
import xyz.erupt.annotation.Erupt;
import xyz.erupt.annotation.EruptField;
import xyz.erupt.annotation.expr.ExprBool;
import xyz.erupt.annotation.fun.DataProxy;
import xyz.erupt.annotation.query.Condition;
import xyz.erupt.annotation.sub_erupt.Power;
import xyz.erupt.annotation.sub_erupt.RowOperation;
import xyz.erupt.annotation.sub_field.*;
import xyz.erupt.annotation.sub_field.sub_edit.*;
import xyz.erupt.common.http.EruptRemoteUtils;
import xyz.erupt.core.exception.EruptApiErrorTip;
import xyz.erupt.core.util.EruptSpringUtil;
import xyz.erupt.toolkit.db.Comment;
import xyz.erupt.toolkit.handler.DelOperationHandler;
import xyz.erupt.toolkit.handler.EditOperationHandler;
import xyz.erupt.toolkit.handler.RemoteEntityChoiceFetchHandler;
import xyz.erupt.toolkit.handler.SqlChoiceFetchHandler;
import xyz.erupt.toolkit.remote.RemoteProxyService;
import xyz.erupt.toolkit.submit.CheckSubmit;
import xyz.erupt.toolkit.utils.TplUtils;
import xyz.erupt.upms.model.auth.DataAuthHandler;
import xyz.erupt.upms.model.auth.DataAuthModel;

import javax.persistence.Entity;
import javax.persistence.Lob;
import javax.persistence.Table;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * @Title: OfflineTraining
 * <AUTHOR>
 * @Package com.daliangang.safedaily.entity
 * @Date 2024/3/8 9:50
 * @description: 线下培训
 */

@Erupt(
        name = "培训考试日",
        power = @Power(powerHandler = DepartPowerHandler.class),
        dataProxy = OfflineTraining.class,
        orderBy = "OfflineTraining.trainBegin desc",
        rowOperation = {
//                @RowOperation(
//                        title = "上报",
//                        icon = "fa fa-arrow-circle-o-up",
//                        operationHandler = OfflineTrainingEscalationHandler.class,
//                        ifExpr = "item.submitted=='未上报'",
//                        mode = RowOperation.Mode.SINGLE,
//                        show = @ExprBool(exprHandler = ButtonShowHandler.class)
//                ),
                @RowOperation(
                        title = "编辑",
                        icon = "fa fa-edit",
                        code = TplUtils.EDIT_OPER_CODE,
                        eruptClass = OfflineTraining.class,
                        operationHandler = EditOperationHandler.class,
//                        ifExpr = "item.submitted=='未上报'",
                        ifExprBehavior = RowOperation.IfExprBehavior.HIDE,
                        mode = RowOperation.Mode.SINGLE,
                        show = @ExprBool(exprHandler = ButtonShowHandler.class)
                ),
                @RowOperation(
                        title = "删除",
                        icon = "fa fa-trash-o",
                        operationHandler = DelOperationHandler.class,
//                        ifExpr = "item.submitted=='未上报'",
                        ifExprBehavior = RowOperation.IfExprBehavior.HIDE,
                        mode = RowOperation.Mode.SINGLE,
                        show = @ExprBool(exprHandler = ButtonShowHandler.class)
                )
        })
@Table(name = "tb_offline_training")
@Entity
@Getter
@Setter
@Comment("培训考试日")
public class OfflineTraining extends DataAuthModel implements DataProxy<OfflineTraining> {

    @EruptField(
            views = @View(title = "单位名称",width = "260px"),
            edit = @Edit(title = "单位名称",search = @Search(ifRender = @ExprBool(exprHandler = CompanyRenderHandler.class)), readonly = @Readonly(exprHandler = DataAuthHandler.class), type = EditType.CHOICE, notNull = true,
                    //choiceType = @ChoiceType(fullSpan = true,fetchHandler = RemoteEntityChoiceFetchHandler.class, fetchHandlerParams = {"main", "Enterprise", "org_code,name"}, reload = true
                    choiceType = @ChoiceType(fetchHandler = SqlChoiceFetchHandler.class, fetchHandlerParams = "select code,name from e_upms_org",reload = true,fullSpan = true
                    )))
    @Comment("单位名称")
    @ApiModelProperty("单位名称")
    private String company;

    @EruptField(
            views = @View(title = "培训名称",width = "150px"),
            edit = @Edit(title = "培训名称", type = EditType.INPUT, notNull = true,
                    inputType = @InputType))
    @Comment("培训名称")
    private String trainName;

    @EruptField(
            views = {@View(
                    title = "培训时间",
                    width = "100px",
                    show = false
            )},
            edit = @Edit(
                    title = "培训时间",
                    type = EditType.DATE,
                    show = false,
                    search = @Search(
                            vague = true
                    ),
                    dateType = @DateType(
                            type = DateType.Type.DATE
                    )
            )
    )
    @Comment("培训时间")
    private String trainTime;


    @EruptField(
            views = {@View(
                    title = "培训开始时间",
                    width = "100px"
            )},
            edit = @Edit(
                    title = "培训开始时间",
                    type = EditType.DATE,
                    notNull = true,
                    dateType = @DateType(
                            type = DateType.Type.DATE_TIME
                    )
            )
    )
   @Comment("培训开始时间")
    private String trainBegin;

    @EruptField(
            views = {@View(
                    title = "培训结束时间",
                    width = "100px"
            )},
            edit = @Edit(
                    title = "培训结束时间",
                    type = EditType.DATE,
                    notNull = true,
                    dateType = @DateType(
                            type = DateType.Type.DATE_TIME
                    )
            )
    )
    @Comment("培训结束时间")
    private String trainEnd;

    @EruptField(
            views = @View(
                    title = "培训内容",width = "60px",show = false
            ),
            edit = @Edit(
                    title = "培训内容",
                    notNull = true,
                    type = EditType.TEXTAREA
            )
    )
    @Comment("培训内容")
    @Lob
    private String trainContent;

    @EruptField(
            views = @View(
                    title = "培训封面",width = "120px"
            ),
            edit = @Edit(
                    title = "培训封面",
                    notNull = true,
                    type = EditType.ATTACHMENT,
                    attachmentType = @AttachmentType(
                            type = AttachmentType.Type.IMAGE,
                            maxLimit = 1,
                            fileTypes = {".png", ".jpg", ".jpeg", ".webp", ".bmp"}
                    )
            )
    )
    @Comment("培训封面")
    private String trainImage;

    @EruptField(
            views = @View(title = "培训地址",width = "150px"),
            edit = @Edit(title = "培训地址", notNull = true,type = EditType.INPUT,
                    inputType = @InputType))
    @Comment("培训地址")
    private String trainAddress;

    @EruptField(
            views = @View(title = "培训方式",width = "150px"),
            edit = @Edit(title = "培训方式", notNull = true,type = EditType.INPUT,
                    inputType = @InputType))
    @Comment("培训方式")
    private String trainMethod;

    @EruptField(
            views = @View(title = "培训机构",width = "150px"),
            edit = @Edit(title = "培训机构", notNull = true,type = EditType.INPUT,
                    inputType = @InputType))
    @Comment("培训机构")
    private String trainInstitutions;

    @EruptField(
            views = @View(title = "培训联系人",width = "150px"),
            edit = @Edit(title = "培训联系人",notNull = true, type = EditType.INPUT,
                    inputType = @InputType))
    @Comment("培训联系人")
    private String trainContactName;

    @EruptField(
            views = @View(title = "联系电话",width = "150px"),
            edit = @Edit(title = "联系电话", notNull = true,type = EditType.INPUT,
                    inputType = @InputType))
    @Comment("联系电话")
    private String trainContactPhone;

    @EruptField(
            views = @View(
                    title = "讲师简介",width = "60px",show = false
            ),
            edit = @Edit(
                    title = "讲师简介",
                    notNull = true,
                    type = EditType.HTML_EDITOR
            )
    )
    @Comment("讲师简介")
    private String instructorProfile;

    @EruptField(
            views = @View(
                    title = "参培资料",width = "120px"
            ),
            edit = @Edit(
                    title = "参培资料",
                    notNull = true,
                    type = EditType.ATTACHMENT,
                    attachmentType = @AttachmentType(
                            type = AttachmentType.Type.BASE,
                            maxLimit = 1,
                            tipMsg = "只能上传word/excel/ppt/pdf/zip/rar文件",
                            fileTypes = {".doc",".docx",".xls", ".xlsx",".ppt",".pptx",".pdf",".zip",".rar"}
                    )
            )
    )
    @Comment("参培资料")
    private String admissionMaterials;

    @EruptField(
            views = @View(title = "组织部门",width = "150px"),
            edit = @Edit(title = "组织部门",notNull = true, type = EditType.INPUT,
                    inputType = @InputType))
    @Comment("组织部门")
    private String trainDepartments;

    @EruptField(
            views = @View(
                    title = "参培人员签到表",width = "120px"
            ),
            edit = @Edit(
                    title = "参培人员签到表",
                    notNull = true,
                    type = EditType.ATTACHMENT,
                    attachmentType = @AttachmentType(
                            type = AttachmentType.Type.BASE,
                            maxLimit = 1,
                            tipMsg = "可以上传jpg/jpeg/png/word/excel/ppt文件",
                            fileTypes = {".doc", ".docx", ".xls", ".xlsx", ".ppt", ".pptx", ".jpg", ".jpeg", ".png"}
                    )
            )
    )
    @Comment("参培人员签到表")
    private String sheetForTrainer;


    @EruptField(
            views = @View(title = "上报时间",width = "100px",show = false),
            edit = @Edit(title = "上报时间",notNull = true, type = EditType.DATE, search = @Search, show = false,
                    dateType = @DateType))
    @Comment("上报时间")
    @ApiModelProperty("上报时间")
    private Date fillingDate;

    @EruptField(
            views = @View(title = "上报状态",width = "100px",show = false),
            edit = @Edit(title = "上报状态",notNull = true, type = EditType.BOOLEAN, show = false,
                    boolType = @BoolType(trueText = "已上报", falseText = "未上报")))
    @CheckSubmit(linkProperty = "createTime", checkTimeDiffPeriod = 1)
    @Comment("上报状态")
    @ApiModelProperty("上报状态")
    private Boolean submitted;

    @Override
    public String beforeFetch(List<Condition> conditions) {
        String sql = "";
        for (Condition condition : conditions) {
            if(condition.getKey().equals("trainTime")){
                List<String> values = (List<String>)condition.getValue();
                // 获取年月格式
                String startYearMonth = values.get(0).substring(0, 7); // 获取 YYYY-MM
                String endYearMonth = values.get(1).substring(0, 7);   // 获取 YYYY-MM

                sql = "(trainBegin LIKE '" + startYearMonth + "%' OR trainEnd LIKE '" + endYearMonth + "%') AND " +
                        "(trainEnd LIKE '" + startYearMonth + "%' OR trainBegin LIKE '" + endYearMonth + "%')";

                // 移除原有的trainTime条件
                conditions.remove(condition);
            }
        }
        if(!sql.isEmpty()){
            return sql;
        }
//        if (DaliangangContext.isDepartmentUser()) return "submitted=true";
        return DataProxy.super.beforeFetch(conditions);
    }

    @Override
    public void addBehavior(OfflineTraining offlineTraining) {
        offlineTraining.setFillingDate(new Date());
//        offlineTraining.setSubmitted(Boolean.FALSE);
        offlineTraining.setSubmitted(Boolean.TRUE);
        // 创建一个SimpleDateFormat实例并定义日期格式
        SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        // 获取当前时间的Date对象
        Date date = new Date();
        // 将Date对象格式化为字符串
        String strDate = formatter.format(date);
        offlineTraining.setTrainBegin(strDate);
    }

    @SneakyThrows
    @Override
    public void beforeAdd(OfflineTraining offlineTraining) {
        // 创建一个SimpleDateFormat实例并定义日期格式
        SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        // 将Date对象格式化为字符串
        Date trainBegin = formatter.parse(offlineTraining.getTrainBegin());
        Date trainEnd = formatter.parse(offlineTraining.getTrainEnd());
        //两个时间做对比，若结束时间小于等于开始时间，则抛出异常
        if(trainEnd.before(trainBegin)){
            throw new EruptApiErrorTip("培训结束时间不能早于培训开始时间！");
        }

        //
//        EruptDao eruptDao = EruptSpringUtil.getBean(EruptDao.class);
//        List<OfflineTraining> offlineTrainings = eruptDao.queryEntityList(OfflineTraining.class, "company='" + offlineTraining.getCompany() + "'");
//        offlineTrainings = offlineTrainings.stream().filter(d -> FindDateUtil.isThisMonth(d.getFillingDate())).collect(Collectors.toList());
//        if(offlineTrainings.size()>0){
//            NotifyUtils.showErrorMsg("【"+offlineTraining.getCompany()+"】本月数据已经添加，请勿重复添加！");
//        }
    }


    @Override
    public void afterAdd(OfflineTraining offlineTraining) {
        exchangeData(offlineTraining);
    }

    @Override
    public void afterUpdate(OfflineTraining offlineTraining) {
        exchangeData(offlineTraining);
    }

    /**
     *
     * @param offlineTraining
     */
    private void exchangeData(OfflineTraining offlineTraining){
        RemoteProxyService remoteProxyService = EruptSpringUtil.getBean(RemoteProxyService.class);
        JSONObject inputData = new JSONObject();
        inputData.set("clazz", "OfflineTraining");
        inputData.set("insertData",offlineTraining);
        EruptRemoteUtils.doPost(remoteProxyService.getMainServerUrl() + "/erupt-api/exchange/data/exchange", inputData, Map.class);
    }

}
