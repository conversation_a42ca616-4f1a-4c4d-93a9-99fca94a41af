package com.daliangang.workbench.proxy;

import com.alibaba.fastjson2.JSON;
import com.daliangang.core.DaliangangContext;
import com.daliangang.workbench.entity.EmployeeView;
import com.daliangang.workbench.entity.EruptRoleTemplatePost;
import com.daliangang.workbench.service.EruptRoleTemplatePostService;
import com.google.gson.internal.LinkedTreeMap;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import xyz.erupt.annotation.fun.ChoiceFetchHandler;
import xyz.erupt.annotation.fun.TagsFetchHandler;
import xyz.erupt.annotation.fun.VLModel;
import xyz.erupt.core.config.GsonFactory;
import xyz.erupt.core.context.MetaContext;
import xyz.erupt.toolkit.service.EruptCacheRedis;
import xyz.erupt.upms.model.EruptUser;
import xyz.erupt.upms.service.EruptContextService;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * @Author: zengwensi
 * @Description:
 * @Date: Created in 2023/3/22 17:59
 * @Modified By
 */
@Component
@Slf4j
public class EmployeeInfoJobTitleDataProxy implements ChoiceFetchHandler, TagsFetchHandler {

    @Resource
    private EruptRoleTemplatePostService eruptRoleTemplatePostService;

    @Override
    public List<VLModel> fetch(String[] params) {
        List<EruptRoleTemplatePost> eruptRoleTemplatePosts = this.queryPosts(params);
        List<VLModel> list = new ArrayList<>();
        //去重
        eruptRoleTemplatePosts = eruptRoleTemplatePosts.stream().collect(Collectors.collectingAndThen(Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(EruptRoleTemplatePost::getName))), ArrayList::new));
        for (EruptRoleTemplatePost value : eruptRoleTemplatePosts) {
            list.add(new VLModel(value.getCode(), value.getName()));
        }
        return list;
    }

    @Override
    public List<String> fetchTags(String[] params) {
        //政府机构
        if (DaliangangContext.isDepartmentUser()) {
            params = new String[]{"0"};
        }

        List<EruptRoleTemplatePost> eruptRoleTemplatePosts = this.queryPosts(params);
        List<String> list = new ArrayList<>();
        //去重
        eruptRoleTemplatePosts = eruptRoleTemplatePosts.stream().collect(Collectors.collectingAndThen(Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(EruptRoleTemplatePost::getName))), ArrayList::new));
        for (EruptRoleTemplatePost value : eruptRoleTemplatePosts) {
            list.add(value.getName());
        }
        return list;
    }

    @Resource
    private EruptCacheRedis eruptCacheRedis;

    public static class EruptRoleTemplatePostWrapper {
        List<EruptRoleTemplatePost> posts;
    }

    private List<EruptRoleTemplatePost> queryPosts(String[] params) {
        String exclusive = "";
        if (params.length != 0) {
            exclusive = params[0];
        }

        String cacheKey = EmployeeView.EMPLOYEE_JOB_TITLES + "@" + MetaContext.getUser().getAccount() + "_" + exclusive;
        Object obj = eruptCacheRedis.get(cacheKey);
        if (obj == null) {
            EruptRoleTemplatePostWrapper wrapper = new EruptRoleTemplatePostWrapper();
            List<EruptRoleTemplatePost> posts = eruptRoleTemplatePostService.queryPost(exclusive);
            wrapper.posts = posts;
            obj = GsonFactory.getGson().toJson(wrapper);
            eruptCacheRedis.put(cacheKey, obj, TimeUnit.SECONDS.toMillis(10));
        }
        EruptRoleTemplatePostWrapper wrapper = GsonFactory.getGson().fromJson((String) obj, EruptRoleTemplatePostWrapper.class);
        return wrapper.posts;
    }

    @Override
    public List<String> convert(List<String> tags, String[] param) {
        List<String> tagsValue = new ArrayList<>();
        if (!tags.isEmpty()) {
            List<EruptRoleTemplatePost> posts = this.queryPosts(param);
            Map<String, EruptRoleTemplatePost> maps = new HashMap<>();//posts.stream().collect(Collectors.toMap(EruptRoleTemplatePost::getCode, Function.identity()));
            posts.forEach(post -> {
                if (!maps.containsKey(post.getName())) maps.put(post.getName(), post);
            });
            tags.forEach(tag -> {
                if (maps.containsKey(tag)) tagsValue.add(maps.get(tag).getName());
            });
        }
        return tagsValue;
    }

    @Override
    public List<String> provide(List<String> tags, String[] param) {
        List<String> tagsValue = new ArrayList<>();
        if (!tags.isEmpty()) {
            List<EruptRoleTemplatePost> posts = this.queryPosts(param);
            Map<String, EruptRoleTemplatePost> maps = new HashMap<>();
            posts.forEach(post -> {
                if (!maps.containsKey(post.getName())) maps.put(post.getName(), post);
            });
            tags.forEach(tag -> {
                if (maps.containsKey(tag)) tagsValue.add(maps.get(tag).getCode());
            });
        }
        return tagsValue;
    }
}
