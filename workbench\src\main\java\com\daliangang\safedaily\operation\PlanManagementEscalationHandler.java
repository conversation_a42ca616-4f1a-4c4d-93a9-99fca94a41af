/*
* Copyright © 2020-2035 yundingyun All rights reserved.
*/
package com.daliangang.safedaily.operation;

import cn.hutool.json.JSONObject;
import org.springframework.stereotype.*;
import xyz.erupt.annotation.fun.*;
import java.util.*;
import com.daliangang.safedaily.entity.*;
import xyz.erupt.common.http.EruptRemoteUtils;
import xyz.erupt.jpa.dao.EruptDao;
import xyz.erupt.toolkit.remote.RemoteProxyService;
import xyz.erupt.toolkit.utils.NotifyUtils;

import javax.annotation.Resource;
import javax.transaction.Transactional;

@Service
public class PlanManagementEscalationHandler implements OperationHandler<PlanManagement, Void> {
    @Resource
    EruptDao eruptDao;

    @Resource
    private RemoteProxyService remoteProxyService;

   @Override
   @Transactional
   public String exec(List<PlanManagement> data, Void unused, String[] param) {
       data.forEach(v->{
         //  v.setIsReport(true);
           v.setSubmitted(true);
           eruptDao.merge(v);

           //推送数据
           JSONObject inputData = new JSONObject();
           inputData.set("clazz", "PlanManagement");
           inputData.set("insertData",v);
           EruptRemoteUtils.doPost(remoteProxyService.getMainServerUrl() + "/erupt-api/exchange/data/exchange", inputData, Map.class);
       });
       return NotifyUtils.getSuccessNotify("上报成功！");
	}
}
