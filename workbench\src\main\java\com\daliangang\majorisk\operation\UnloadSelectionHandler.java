package com.daliangang.majorisk.operation;

import cn.hutool.json.JSONUtil;
import com.daliangang.rndpub.entity.InspectionItems;
import xyz.erupt.annotation.sub_erupt.Tpl;
import xyz.erupt.core.context.MetaDrill;
import xyz.erupt.toolkit.db.EruptResultMap;
import xyz.erupt.toolkit.utils.EruptDaoUtils;
import xyz.erupt.toolkit.utils.SqlUtils;
import xyz.erupt.tpl.avue.core.AvueForm;
import xyz.erupt.tpl.avue.core.component.AvueCheckBox;
import xyz.erupt.tpl.avue.core.component.AvueInput;
import xyz.erupt.tpl.avue.core.component.AvueProp;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class UnloadSelectionHandler implements Tpl.TplHandler {
    @Override
    public void bindTplData(Map<String, Object> binding, String[] params) {
        AvueForm form = AvueForm.builder().code("InspectionItemsManualSelection").build();
        form.setSubmitBtn(false);
        form.setEmptyBtn(false);

        HashMap<String, String> types = new HashMap<>();
        types.put("储罐","tb_storage_tank");
        types.put("堆场","tb_yard");
        types.put("仓库","tb_warehouse");
        types.put("泊位","tb_berth");
        types.put("码头","tb_wharf");
        for (Map.Entry<String, String> entry : types.entrySet()) {
            AvueCheckBox checkBox = AvueCheckBox.builder()
                    .label(entry.getKey())
                    .prop(entry.getKey())
//                    .labelWidth("240")
                    .row(true)
                    .build();
            //提取内容
            String contentSql = "select "+(entry.getValue().substring(entry.getValue().lastIndexOf("_")+1))+"_name as label,id as value from "+entry.getValue();
            List<AvueProp> typeList = EruptDaoUtils.selectOnes(contentSql, AvueProp.class);
            for (AvueProp type : typeList) {
                checkBox.getDicData().add(type);
            }
            form.getColumn().add(checkBox);
        }
        binding.put("avueForm", JSONUtil.toJsonStr(form));
    }
}
