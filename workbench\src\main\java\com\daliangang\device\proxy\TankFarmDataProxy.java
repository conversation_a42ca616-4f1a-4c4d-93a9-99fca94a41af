/*
* Copyright © 2020-2035 yundingyun All rights reserved.
*/
package com.daliangang.device.proxy;

import cn.hutool.json.JSONObject;
import com.daliangang.device.entity.TankFarm;
import com.daliangang.device.entity.TankGroup;
import com.google.gson.internal.LinkedTreeMap;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.stereotype.Service;
import xyz.erupt.annotation.fun.DataProxy;
import xyz.erupt.common.http.EruptRemoteUtils;
import xyz.erupt.toolkit.remote.RemoteProxyService;
import xyz.erupt.toolkit.remote.RemoteQuery;
import xyz.erupt.toolkit.utils.EruptDaoUtils;
import xyz.erupt.toolkit.utils.NotifyUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

@Service
public class TankFarmDataProxy implements DataProxy<TankFarm> {
    @Resource
    private RemoteProxyService remoteProxyService;
    @Override
    public void addBehavior(TankFarm tankFarm) {
        if (ObjectUtils.isNotEmpty(tankFarm.getCompany())) {
            List<LinkedTreeMap> enterprise = remoteProxyService.queryEntity("Enterprise", RemoteQuery.builder().condition("orgCode", tankFarm.getCompany()));
            if (ObjectUtils.isNotEmpty(enterprise)) {
                //  Integer portArea1 = (int) Float.parseFloat((String) enterprise.get(0).get("portArea"));
                //   String portArea = String.valueOf(enterprise.get(0).get("portArea"));
                String portArea=new BigDecimal(enterprise.get(0).get("portArea").toString()).stripTrailingZeros().toPlainString();
                tankFarm.setPortArea(portArea);
            }
        }
    }

    @Override
    public void beforeDelete(TankFarm tankFarm) {
        //所属罐区
        Long tankFarmId = tankFarm.getId();
        if(tankFarmId!=null){
            String selectSql="select * from tb_tank_group where tank_farm="+tankFarmId;
            TankGroup tankGroup = EruptDaoUtils.selectOne(selectSql, TankGroup.class);
            if(tankGroup!=null){
                NotifyUtils.showErrorMsg("当前罐区已有下级罐组信息，不可删除!");
            }
        }
    }


    @Override
    public void afterAdd(TankFarm tankFarm) {
        JSONObject inputData = new JSONObject();
        inputData.set("clazz", "TankFarm");
        inputData.set("insertData",tankFarm);
        EruptRemoteUtils.doPost(remoteProxyService.getMainServerUrl() + "/erupt-api/exchange/data/exchange", inputData, Map.class);
    }

    @Override
    public void afterUpdate(TankFarm tankFarm) {
        JSONObject inputData = new JSONObject();
        inputData.set("clazz", "TankFarm");
        inputData.set("insertData",tankFarm);
        EruptRemoteUtils.doPost(remoteProxyService.getMainServerUrl() + "/erupt-api/exchange/data/exchange", inputData, Map.class);
    }
}
