/*
* Copyright © 2020-2035 yundingyun All rights reserved.
*/
package com.daliangang.majorisk.operation;

import com.daliangang.majorisk.entity.CkReserveReporting;
import com.daliangang.majorisk.entity.DcReserveReporting;
import org.springframework.stereotype.Service;
import xyz.erupt.annotation.fun.OperationHandler;
import xyz.erupt.jpa.dao.EruptDao;
import xyz.erupt.toolkit.utils.NotifyUtils;

import javax.annotation.Resource;
import javax.transaction.Transactional;
import java.time.LocalDateTime;
import java.util.List;

@Service
public class DcReserveReportingEscalationHandler implements OperationHandler<DcReserveReporting, Void> {
    @Resource
    private EruptDao eruptDao;

   @Override
   @Transactional
   public String exec(List<DcReserveReporting> data, Void unused, String[] param) {
       for (DcReserveReporting dcReserveReporting:data) {
       //    dcReserveReporting.setSubmitted(true);
           dcReserveReporting.setUpdateTime(LocalDateTime.now());
           eruptDao.merge(dcReserveReporting);
       }

       return NotifyUtils.getSuccessNotify("上报成功！");
	}
}
