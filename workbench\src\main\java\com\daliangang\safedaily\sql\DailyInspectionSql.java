package com.daliangang.safedaily.sql;

import org.springframework.stereotype.Repository;

/**
 * <AUTHOR>
 * @since :2023/4/23:15:53
 */
@Repository
public class DailyInspectionSql {

    public String selectDailyInspectionNum (String orgCode) {

        String sql = "select COUNT(DISTINCT org_code) as num  from tb_daily_inspection  WHERE inspection_time = CURRENT_DATE() and submitted = 1 ";
        sql += " and org_code "+orgCode+"";
        return sql;
    }
}
