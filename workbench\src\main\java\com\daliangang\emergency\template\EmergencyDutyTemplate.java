package com.daliangang.emergency.template;

import com.daliangang.emergency.entity.EmergencyDuty;
import com.daliangang.emergency.entity.EmergencyDutyPerson;
import com.google.gson.internal.LinkedTreeMap;
import lombok.Data;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import xyz.erupt.core.util.EruptSpringUtil;
import xyz.erupt.excel.entity.CommonExcelObject;
import xyz.erupt.excel.model.ExcelHeader;
import xyz.erupt.excel.template.ExcelTemplateConvertHandler;
import xyz.erupt.jpa.dao.EruptDao;
import xyz.erupt.toolkit.remote.RemoteProxyService;
import xyz.erupt.toolkit.remote.RemoteQuery;
import xyz.erupt.toolkit.utils.EruptDaoUtils;
import xyz.erupt.toolkit.utils.NotifyUtils;
import xyz.erupt.toolkit.utils.SqlUtils;
import xyz.erupt.upms.model.EruptUser;
import xyz.erupt.upms.service.EruptUserService;

import javax.transaction.Transactional;
import java.sql.Timestamp;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.util.*;

/**
 * @Author: zengwensi
 * @Description:
 * @Date: Created in 2023/3/23 17:47
 * @Modified By
 */
@Data
@Slf4j
public class EmergencyDutyTemplate extends CommonExcelObject implements ExcelTemplateConvertHandler<EmergencyDutyTemplate> {

    @ExcelHeader(col = 0, name = "编号",required = true)
    private String dutyId;

    @ExcelHeader(col = 1, name = "开始值守时间（年月日时分）",required = true)
    private String dutyTime;

    @ExcelHeader(col = 2, name = "结束值守时间（年月日时分）")
    private String endDutyTime;

    @ExcelHeader(col = 3, name = "值守人")
    private String personOnDuty;

    @ExcelHeader(col = 4, name = "联系方式")
    private String tel;

    @ExcelHeader(col = 5, name = "所属单位")
    private String company;

    @Override
    @Transactional
    @SneakyThrows
    public void convert(List<EmergencyDutyTemplate> templates, CommonExcelObject parent) {
        //获取数据库连接池
        EruptDao eruptDao = EruptSpringUtil.getBean(EruptDao.class);
        //获取企业名称及code
        RemoteProxyService remoteProxyService = EruptSpringUtil.getBean(RemoteProxyService.class);
        List<LinkedTreeMap> dataList = remoteProxyService.queryEntity("EruptOrg", RemoteQuery.builder().multiProps("name,code"));
        Map<String, String> codeMap = new HashMap<>();
        for (LinkedTreeMap vo : dataList) {
            codeMap.put(vo.get("name").toString(), vo.get("code").toString());
        }
        //查询当前所有的应急值守数据
        List<EmergencyDuty> existEmergencyDuties = eruptDao.queryEntityList(EmergencyDuty.class);
        //应急值守做成map
        HashMap<String, EmergencyDuty> dutyHashMap = new HashMap<>();
        LocalDateTime now = LocalDateTime.now();
        //获取当前登录用户的用户名
        EruptUser currentEruptUser = EruptSpringUtil.getBean(EruptUserService.class).getCurrentEruptUser();
        String currentEruptUserName = currentEruptUser.getName();
        ArrayList<EmergencyDuty> insertList = new ArrayList<>();
        //遍历数据,添加主表
        for (EmergencyDutyTemplate template : templates) {
            if(!codeMap.containsKey(template.getCompany())){
                NotifyUtils.showErrorDialog("所属企业不存在，请检查后重试！");
            }
            //检验所属公司与当前是否相同
            if(StringUtils.isNotEmpty(codeMap.get(template.getCompany()))&&StringUtils.isNotEmpty(currentEruptUser.getEruptOrg().getCode())&&!codeMap.get(template.getCompany()).equals(currentEruptUser.getEruptOrg().getCode())){
                NotifyUtils.showErrorDialog("所属企业与当前企业不一致，请核对后重新导入！");
            }
            String selectSql="select * from tb_emergency_duty where company="+SqlUtils.wrapStr(codeMap.get(template.getCompany()))+" and duty_time="+SqlUtils.wrapStr(template.getDutyTime())+" and end_duty_time="+SqlUtils.wrapStr(template.getEndDutyTime());
            EmergencyDuty emergencyDuties = EruptDaoUtils.selectOne(selectSql, EmergencyDuty.class);
            if(emergencyDuties==null){
                EmergencyDuty duty = new EmergencyDuty();
                duty.setDutyId(Long.parseLong(template.getDutyId()));
                duty.setDutyTime(new Timestamp(new SimpleDateFormat("yyyy-MM-dd HH:mm").parse(template.getDutyTime()).getTime()));
                duty.setEndDutyTime(new Timestamp(new SimpleDateFormat("yyyy-MM-dd HH:mm").parse(template.getEndDutyTime()).getTime()));
//                duty.setOrgCode(codeMap.get(template.getCompany()));
                duty.setCompany(codeMap.get(template.getCompany()));
                duty.setCreateBy(currentEruptUserName);
                duty.setCreateTime(now);
                duty.setUpdateBy(currentEruptUserName);
                duty.setUpdateTime(now);
                dutyHashMap.put(codeMap.get(duty.getCompany()),duty);
                eruptDao.persist(duty);
            }
        }
        for (EmergencyDutyTemplate template : templates) {
            EmergencyDutyPerson person = new EmergencyDutyPerson();
            String selectSql="select * from tb_emergency_duty where company="+SqlUtils.wrapStr(codeMap.get(template.getCompany()))+" and duty_time="+SqlUtils.wrapStr(template.getDutyTime())+" and end_duty_time="+SqlUtils.wrapStr(template.getEndDutyTime());
            EmergencyDuty duty = EruptDaoUtils.selectOne(selectSql, EmergencyDuty.class);
            person.setDutyTime(duty.getDutyTime());
            person.setEndDutyTime(duty.getEndDutyTime());
            person.setTel(template.getTel());
            person.setPersonOnDuty(template.getPersonOnDuty());
            person.setCompany(codeMap.get(template.getCompany()));
            person.setCreateBy(currentEruptUserName);
            person.setCreateTime(now);
            person.setUpdateBy(currentEruptUserName);
            person.setUpdateTime(now);
            Set<EmergencyDutyPerson> emergencyDutyPeople = duty.getEmergencyDutyPeople();
            if (emergencyDutyPeople == null) {
                emergencyDutyPeople = new HashSet<>();
                duty.setEmergencyDutyPeople(emergencyDutyPeople);
            }
            emergencyDutyPeople.add(person);
            String personSql="select * from tb_emergency_duty_person where company="+SqlUtils.wrapStr(duty.getCompany())+" and person_on_duty="+SqlUtils.wrapStr(template.getPersonOnDuty())+" and tel="+SqlUtils.wrapStr(template.getTel())+" and duty_time="+SqlUtils.wrapStr(template.getDutyTime())+" and end_duty_time="+SqlUtils.wrapStr(template.getEndDutyTime());
            List<EmergencyDutyPerson> people = EruptDaoUtils.selectOnes(personSql, EmergencyDutyPerson.class);
            if(people.size()>0){
               continue;
            }
            String insertSql=String.format("insert into tb_emergency_duty_person(create_by,create_time,update_by,update_time,company,duty_time,person_on_duty,tel,emergency_duty_id,end_duty_time) values('%s','%s','%s','%s','%s','%s','%s','%s',%s,'%s')",
                    currentEruptUserName,now,currentEruptUserName,now,duty.getCompany(),duty.getDutyTime(),template.getPersonOnDuty(),template.getTel(),duty.getId(),duty.getEndDutyTime());
            eruptDao.getJdbcTemplate().execute(insertSql);
        }
//        for (EmergencyDuty emergencyDuty : insertList) {
//            eruptDao.merge(emergencyDuty);
//        }
    }
}
