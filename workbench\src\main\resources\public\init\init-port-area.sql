/*
 Navicat Premium Data Transfer

 Source Server         : localhost
 Source Server Type    : MySQL
 Source Server Version : 80027
 Source Host           : localhost:3306
 Source Schema         : erupt

 Target Server Type    : MySQL
 Target Server Version : 80027
 File Encoding         : 65001

 Date: 10/05/2023 00:59:59
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for tb_port_area
-- ----------------------------
DROP TABLE IF EXISTS `tb_port_area`;
CREATE TABLE `tb_port_area` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `create_by` varchar(255) DEFAULT NULL,
  `create_time` datetime DEFAULT NULL,
  `update_by` varchar(255) DEFAULT NULL,
  `update_time` datetime DEFAULT NULL,
  `land_area` varchar(255) DEFAULT NULL COMMENT '陆域面积（万平方米）',
  `map` longtext COMMENT '地图',
  `portarea_name` varchar(255) DEFAULT NULL COMMENT '港区名称',
  `shoreline_length` varchar(255) DEFAULT NULL COMMENT '港口生产已使用自然岸线长度（米）',
  `total_area` varchar(255) DEFAULT NULL COMMENT '合计面积（万平方米）',
  `water_area` varchar(255) DEFAULT NULL COMMENT '水域面积（万平方米）',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=10 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='港区管理';

-- ----------------------------
-- Records of tb_port_area
-- ----------------------------
BEGIN;
INSERT INTO `tb_port_area` (`id`, `create_by`, `create_time`, `update_by`, `update_time`, `land_area`, `map`, `portarea_name`, `shoreline_length`, `total_area`, `water_area`) VALUES (1, NULL, NULL, NULL, NULL, '6.87', NULL, '鲶鱼湾港区', '2500', '97.87', '91');
INSERT INTO `tb_port_area` (`id`, `create_by`, `create_time`, `update_by`, `update_time`, `land_area`, `map`, `portarea_name`, `shoreline_length`, `total_area`, `water_area`) VALUES (2, NULL, NULL, NULL, NULL, '154.92', NULL, '北良港区', '1667', '340.42', '185.5');
INSERT INTO `tb_port_area` (`id`, `create_by`, `create_time`, `update_by`, `update_time`, `land_area`, `map`, `portarea_name`, `shoreline_length`, `total_area`, `water_area`) VALUES (3, NULL, NULL, NULL, NULL, '', NULL, '甘井子港区', '', '', '');
INSERT INTO `tb_port_area` (`id`, `create_by`, `create_time`, `update_by`, `update_time`, `land_area`, `map`, `portarea_name`, `shoreline_length`, `total_area`, `water_area`) VALUES (4, NULL, NULL, NULL, NULL, '878.66', NULL, '大窑湾港区', '8219', '1528.8', '650.14');
INSERT INTO `tb_port_area` (`id`, `create_by`, `create_time`, `update_by`, `update_time`, `land_area`, `map`, `portarea_name`, `shoreline_length`, `total_area`, `water_area`) VALUES (5, NULL, NULL, NULL, NULL, '135.1', NULL, '大孤山南港区', '1771', '359.84', '224.74');
INSERT INTO `tb_port_area` (`id`, `create_by`, `create_time`, `update_by`, `update_time`, `land_area`, `map`, `portarea_name`, `shoreline_length`, `total_area`, `water_area`) VALUES (6, NULL, NULL, NULL, NULL, '99.77', NULL, '旅顺港区', '3103', '165.77', '66');
INSERT INTO `tb_port_area` (`id`, `create_by`, `create_time`, `update_by`, `update_time`, `land_area`, `map`, `portarea_name`, `shoreline_length`, `total_area`, `water_area`) VALUES (7, NULL, NULL, NULL, NULL, '', NULL, '瓦房店港区', '', '', '');
INSERT INTO `tb_port_area` (`id`, `create_by`, `create_time`, `update_by`, `update_time`, `land_area`, `map`, `portarea_name`, `shoreline_length`, `total_area`, `water_area`) VALUES (8, NULL, NULL, NULL, NULL, '', NULL, '长兴岛港区', '', '', '');
INSERT INTO `tb_port_area` (`id`, `create_by`, `create_time`, `update_by`, `update_time`, `land_area`, `map`, `portarea_name`, `shoreline_length`, `total_area`, `water_area`) VALUES (9, NULL, NULL, NULL, NULL, '', NULL, '长海县', '', '', '');
COMMIT;

SET FOREIGN_KEY_CHECKS = 1;
