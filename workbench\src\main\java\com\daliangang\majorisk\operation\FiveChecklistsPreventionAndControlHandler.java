package com.daliangang.majorisk.operation;

import com.daliangang.majorisk.entity.FiveChecklistsFoundation;
import com.daliangang.majorisk.entity.FiveChecklistsPreventionAndControl;
import com.daliangang.majorisk.handler.MajorRiskTypeLink2ByHandler;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import xyz.erupt.annotation.fun.OperationHandler;
import xyz.erupt.core.annotation.EruptRouter;
import xyz.erupt.core.util.EruptSpringUtil;
import xyz.erupt.jpa.dao.EruptDao;
import xyz.erupt.toolkit.db.EruptResultMap;
import xyz.erupt.toolkit.utils.EruptDaoUtils;
import xyz.erupt.toolkit.utils.NotifyUtils;

import javax.annotation.Resource;
import javax.transaction.Transactional;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @since :2023/4/14:17:02
 */
@Service
public class FiveChecklistsPreventionAndControlHandler implements OperationHandler<FiveChecklistsFoundation, FiveChecklistsPreventionAndControl> {
    @Resource
    private EruptDao eruptDao;

    @Override
    public String exec(List<FiveChecklistsFoundation> data, FiveChecklistsPreventionAndControl fiveChecklistsPreventionAndControl, String[] param) {
        if(ObjectUtils.isEmpty(fiveChecklistsPreventionAndControl.getRiskControlSystem())
        || ObjectUtils.isEmpty(fiveChecklistsPreventionAndControl.getRiskDisclosure())
        || ObjectUtils.isEmpty(fiveChecklistsPreventionAndControl.getPointsOfPrevention())
        ) {
            NotifyUtils.showErrorMsg("存在数据未填写！");
        }
        eruptDao.merge(fiveChecklistsPreventionAndControl);
        return NotifyUtils.getSuccessNotify("保存成功！");
    }

    public FiveChecklistsPreventionAndControl detail(Long id) {
        SendMessageController messageController = EruptSpringUtil.getBean(SendMessageController.class);
        FiveChecklistsPreventionAndControl fiveChecklistsPreventionAndControl = messageController.initValue(id);
        return fiveChecklistsPreventionAndControl;
    }

    @RestController
    @Transactional
    public static class SendMessageController {
        @Resource
        private MajorRiskTypeLink2ByHandler majorRiskTypeLink2ByHandler;

        @RequestMapping("erupt-api/data/FiveDetail/operator/preventionandcontrol")
        @EruptRouter(verifyType = EruptRouter.VerifyType.LOGIN)
        public String recreate(@RequestBody EruptResultMap reuquest) {
            FiveChecklistsPreventionAndControl form = (FiveChecklistsPreventionAndControl) reuquest.getAs("param", FiveChecklistsPreventionAndControl.class);
            List list = new ArrayList();
            list.add(form);
            return EruptSpringUtil.getBean(FiveChecklistsPreventionAndControlHandler.class).exec(list, form, null);
        }

        @RequestMapping("/erupt-api/data/FiveChecklistsPreventionAndControl/{id}")
        @EruptRouter(verifyType = EruptRouter.VerifyType.LOGIN)
        public FiveChecklistsPreventionAndControl initValue(@PathVariable("id") Long id) {
            EruptResultMap fiveDetail = EruptDaoUtils.selectOne("select * from tb_five_detail where id=" + id, EruptResultMap.class);
            FiveChecklistsFoundation fiveChecklistsFoundation = EruptDaoUtils.selectOne("select * from tb_fivechecklists_foundation where id=" + fiveDetail.getInt("five_checklists_foundation_id"), FiveChecklistsFoundation.class);
            FiveChecklistsPreventionAndControl fiveChecklistsPreventionAndControl = EruptDaoUtils.selectOne("select * from tb_fivechecklists_prevention_and_control where id=" + fiveDetail.getInt("five_checklists_prevention_and_control_id"), FiveChecklistsPreventionAndControl.class);
            Object fiveRiskType = fiveChecklistsFoundation.getFiveRiskType();
         //   String s = majorRiskTypeLink2ByHandler.placeHolder(fiveRiskType);
            fiveChecklistsPreventionAndControl.setFiveRiskType(fiveChecklistsFoundation.getFiveRiskType());
          //  fiveChecklistsPreventionAndControl.setPointsOfPrevention(s);
            return fiveChecklistsPreventionAndControl;
        }
    }

}
