/*
 * Copyright © 2020-2035 yundingyun All rights reserved.
 */
package com.daliangang.rndpub.proxy;

import cn.hutool.json.JSONUtil;
import com.daliangang.rndpub.entity.InspectionItems;
import com.daliangang.rndpub.entity.Procedure;
import com.daliangang.rndpub.operation.InspectionItemsSystemExtractionHandler;
import org.springframework.stereotype.Service;
import xyz.erupt.annotation.fun.DataProxy;
import xyz.erupt.annotation.sub_erupt.PageModel;
import xyz.erupt.core.context.MetaDrill;
import xyz.erupt.jpa.dao.EruptDao;
import xyz.erupt.toolkit.redismq.RedisMQService;
import xyz.erupt.toolkit.service.EruptCacheRedis;

import javax.annotation.Resource;
import java.util.List;

@Service
public class InspectionItemsDataProxy implements DataProxy<InspectionItems>, PageModel.PageModelProxy{

    @Resource
    private EruptDao eruptDao;

    @Resource
    private EruptCacheRedis eruptCacheRedis;

    @Resource
    private RedisMQService redisMQService;
    @Resource
    private InspectionItemsSystemExtractionHandler inspectionItemsSystemExtractionHandler ;


    public String getCacheKey(){
        long drillId = MetaDrill.getDrillId();
        return Procedure.class.getSimpleName() +"-"+ InspectionItems.class.getSimpleName() +"-"+ drillId + ":";
    }
    @Override
    public void open() {
//        把未抽取前的数据放入缓存
        long drillId = MetaDrill.getDrillId();
        List<InspectionItems> list = eruptDao.queryEntityList(InspectionItems.class, "procedure_id = " + drillId);
        String key = this.getCacheKey();
        String str = JSONUtil.toJsonStr(list);
        redisMQService.produce(key,str);
    }

    @Override
    public void close() {
        //关闭，就还原数据
        long drillId = MetaDrill.getDrillId();
        String key = this.getCacheKey();
        String json = redisMQService.consume(key, String.class);
        List<InspectionItems> list = JSONUtil.toList(json, InspectionItems.class);
        list.forEach(item ->{
            eruptDao.mergeAndFlush(item) ;
        });
        eruptCacheRedis.getStringRedisTemplate().delete(key);
        //还原条数
        Procedure procedure = eruptDao.queryEntity(Procedure.class, "id=" + drillId);
        inspectionItemsSystemExtractionHandler.updateCountAndName(procedure);
    }
}
