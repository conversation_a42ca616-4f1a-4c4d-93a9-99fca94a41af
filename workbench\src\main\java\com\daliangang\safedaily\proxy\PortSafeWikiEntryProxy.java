package com.daliangang.safedaily.proxy;

import com.daliangang.safedaily.entity.PortSafeWikiEntry;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import xyz.erupt.annotation.fun.DataProxy;
import xyz.erupt.toolkit.utils.FileUtils;

import java.util.StringTokenizer;

@Service
public class PortSafeWikiEntryProxy implements DataProxy<PortSafeWikiEntry> {
    @Override
    public void beforeAdd(PortSafeWikiEntry entry) {
        if (StringUtils.isNotEmpty(entry.getFile())) {
            String name = FileUtils.getFilename(entry.getFile()).replace(".pdf", "");
            if (name.contains("-")) {
                StringTokenizer st = new StringTokenizer(name, "-");
                name = st.nextToken().trim();
            }
            entry.setName(name);
        }
//        entry.setName(FileUtils.getFilename(entry.getFile()));
    }
}
