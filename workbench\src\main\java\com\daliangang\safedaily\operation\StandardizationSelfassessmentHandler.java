/*
* Copyright © 2020-2035 yundingyun All rights reserved.
*/
package com.daliangang.safedaily.operation;

import org.springframework.stereotype.*;
import xyz.erupt.annotation.fun.*;
import java.util.*;
import com.daliangang.safedaily.entity.*;

@Service
public class StandardizationSelfassessmentHandler implements OperationHandler<Standardization, Void> {
   @Override
   public String exec(List<Standardization> data, Void unused, String[] param) {
       return null;
	}
}