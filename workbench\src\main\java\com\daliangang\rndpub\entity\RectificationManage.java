package com.daliangang.rndpub.entity;

import com.daliangang.rndpub.proxy.RectificationManageProxy;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import xyz.erupt.annotation.Erupt;
import xyz.erupt.annotation.EruptField;
import xyz.erupt.annotation.sub_erupt.Power;
import xyz.erupt.annotation.sub_field.Edit;
import xyz.erupt.annotation.sub_field.EditType;
import xyz.erupt.annotation.sub_field.View;
import xyz.erupt.annotation.sub_field.ViewType;
import xyz.erupt.annotation.sub_field.sub_edit.DateType;
import xyz.erupt.annotation.sub_field.sub_edit.LinkSelfType;
import xyz.erupt.jpa.model.BaseModel;
import xyz.erupt.toolkit.db.Comment;

import javax.persistence.Entity;
import javax.persistence.Table;

@Erupt(name = "整改结果管理", importTruncate = true, power = @Power(add = false, delete = false, export = false, importable = false, viewDetails = false, edit = false)
        ,orderBy = "inspectionDate desc"
        , dataProxy = RectificationManageProxy.class
        , rowOperation = {

})
@Getter
@Setter
@Table(name = "tb_inspection_problem")
@Entity
public class RectificationManage extends BaseModel {

    public static final String LINK_TARGET = "#/build/table/InspectionResultsView";
    public static final String LINK_SQL = "select org_code as value, name as label from tb_enterprise where `name` in (\n" +
            "select enterprise_name from tb_enterprise_information where procedure_id=(select id from tb_procedure where name='${name}')\n" +
            "and state=1)";

    @EruptField(
            views = @View(title = "检查名称",
//                    type = ViewType.LINK_SELF,
//                    linkName="InspectionResultsView",
//                    condition="[{'code':'name','searchCode':'inspectionName'}]"
//            ),
                    type = ViewType.LINK_SELF,
                    linkSelfType = @LinkSelfType(target = RectificationManage.LINK_TARGET, filterSql = RectificationManage.LINK_SQL,
                            searchKeys = @LinkSelfType.SearchKey(key = "inspectionName", readonly = true),
                            filterKeys = {@LinkSelfType.FilterKey(leftKey = "name", rightKey = "company")})
            ),
            edit = @Edit(title = "检查名称", type = EditType.INPUT))
    private String name;

    @EruptField(
            views = @View(title = "检查企业数量", type = ViewType.LINK_SELF,
                    linkSelfType = @LinkSelfType(target = RectificationManage.LINK_TARGET, filterSql = RectificationManage.LINK_SQL,
                            searchKeys = @LinkSelfType.SearchKey(key = "inspectionName", readonly = true),
                            filterKeys = {@LinkSelfType.FilterKey(leftKey = "name", rightKey = "company")})
            ),
            edit = @Edit(title = "检查企业数量", type = EditType.INPUT))
    private String enterprisesNum;


    @EruptField(
            views = @View(title = "检查问题数量", type = ViewType.LINK_SELF,
                    linkSelfType = @LinkSelfType(target = RectificationManage.LINK_TARGET, filterSql = RectificationManage.LINK_SQL,
                            searchKeys = @LinkSelfType.SearchKey(key = "inspectionName", readonly = true),
                            filterKeys = {@LinkSelfType.FilterKey(leftKey = "name", rightKey = "company")})
            ),
            edit = @Edit(title = "检查问题数量", type = EditType.INPUT))
    private String publishQuestionNum;


    @EruptField(
            views = @View(title = "已整改问题数量", type = ViewType.LINK_SELF,
                    linkSelfType = @LinkSelfType(target = RectificationManage.LINK_TARGET, filterSql = RectificationManage.LINK_SQL,
                            searchKeys = @LinkSelfType.SearchKey(key = "inspectionName", readonly = true),
                            filterKeys = {@LinkSelfType.FilterKey(leftKey = "name", rightKey = "company")})
            ),
            edit = @Edit(title = "已整改问题数量", type = EditType.INPUT))
    private String passResult;

    @EruptField(
            views = @View(title = "整改率", type = ViewType.LINK_SELF,
                    linkSelfType = @LinkSelfType(target = RectificationManage.LINK_TARGET, filterSql = RectificationManage.LINK_SQL,
                            searchKeys = @LinkSelfType.SearchKey(key = "inspectionName", readonly = true),
                            filterKeys = {@LinkSelfType.FilterKey(leftKey = "name", rightKey = "company")})
            ),
            edit = @Edit(title = "整改率", type = EditType.INPUT))
    private String rectifiedRatio;

    @EruptField(
            views = @View(title = "逾期未整改问题数量", type = ViewType.LINK_SELF,
                    linkSelfType = @LinkSelfType(target = RectificationManage.LINK_TARGET, filterSql = RectificationManage.LINK_SQL,
                            searchKeys = @LinkSelfType.SearchKey(key = "inspectionName", readonly = true),
                            filterKeys = {@LinkSelfType.FilterKey(leftKey = "name", rightKey = "company")})
            ),
            edit = @Edit(title = "逾期未整改问题数量", type = EditType.INPUT))
    private String overDueNotPass;

    @EruptField(
            views = @View(title = "检查开始日期", show = false),
            edit = @Edit(title = "检查开始日期", type = EditType.DATE, notNull = true,
                    dateType = @DateType))
    @Comment("检查开始日期")
    @ApiModelProperty("检查开始日期")
    private java.util.Date inspectionDate;
}
