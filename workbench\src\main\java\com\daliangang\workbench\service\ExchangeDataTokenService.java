package com.daliangang.workbench.service;

import cn.hutool.core.lang.UUID;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONUtil;
import com.daliangang.workbench.controller.ExchangeDataController;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import xyz.erupt.core.annotation.EruptHandlerNaming;
import xyz.erupt.job.handler.EruptJobHandler;
import xyz.erupt.toolkit.remote.RemoteProxyService;
import xyz.erupt.toolkit.service.EruptCacheRedis;
import xyz.erupt.upms.platform.EruptOption;
import xyz.erupt.upms.platform.EruptOptions;
import xyz.erupt.upms.service.EruptPlatformService;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;

@EruptHandlerNaming("推送大数据平台——获取token")
@Service
@Slf4j
@EruptOptions({@EruptOption(name = ExchangeDataController.EXCHANGE_DATA_ERUPT_URL, value = "http://dlg-workbench:8080", desc = "大连港的ip，因为大连港内网的复杂，所以这里需要加一个"),})
public class ExchangeDataTokenService implements EruptJobHandler {

    @Resource
    private RemoteProxyService remoteProxyService;


    @Resource
    private EruptPlatformService eruptPlatformService ;

    @Resource
    private EruptCacheRedis eruptCacheRedis;

    @Override
    public String exec(String code, String param) {
        String inputStr = eruptPlatformService.getOption(ExchangeDataController.TOKEN_INPUT).getAsString() ;
        Map<String,Object> inputMap = JSONUtil.toBean(inputStr,Map.class);
        StringBuilder result = new StringBuilder();
        //循环去取对应的token
        for(String key : ExchangeDataController.BUSINESS_CODE.keySet()){

            String value = ExchangeDataController.BUSINESS_CODE.get(key);

            //随机32位
            UUID uuid = UUID.randomUUID();
            String serialnum = uuid.toString().replaceAll("-","");
            //10位数的时间戳
            long timestamp =  System.currentTimeMillis()/1000;

            String ip = eruptPlatformService.getOption(ExchangeDataController.TOKEN_IP).getAsString() ;
            String url = eruptPlatformService.getOption(ExchangeDataController.TOKEN_URL).getAsString() ;

            inputMap.put("serialnum",serialnum);
            inputMap.put("timestamp",timestamp);
            inputMap.put("service", value);

            try  {
                HttpResponse response = HttpUtil.createPost(ip + url).form(inputMap).execute();
                log.info("接口地址：" + ip + url + ",请求参数：" + JSONUtil.toJsonStr(inputMap) + ",返回参数：" + JSONUtil.toJsonStr(response.body()));
                boolean ok = response.isOk();
                if (ok) {
                    result.append("接口地址：").append(ip).append(url).append(",请求参数：").append(JSONUtil.toJsonStr(inputMap)).append(",返回参数：").append(JSONUtil.toJsonStr(response.body()));
                    String body = response.body();
                    ExchangeDataController.ExchangeDataResponse exchangeDataResponse = JSONUtil.toBean(body, ExchangeDataController.ExchangeDataResponse.class);
                    //存储到redis中，并设置过期时间为30分钟
                    eruptCacheRedis.getAndSet(ExchangeDataController.EXCHANGE_DATA_TOKEN_KEY.replace("{SERVICE}",value), TimeUnit.MINUTES.toMillis(30), () -> {
                        Map<String, Object> map = new HashMap<>();
                        map.put("token", exchangeDataResponse.getData());
                        map.put("appSecret", inputMap.get("appSecret"));
                        return map;
                    });
                }
            }catch (Exception e){
                result.append("<span class='e-tag'><font color='red'>").append("接口地址：").append(ip).append(url).append(",请求参数：").append(JSONUtil.toJsonStr(inputMap)).append(",请求失败了").append("</font></span>") ;
                log.error("接口地址：" + ip + url + ",请求参数：" + JSONUtil.toJsonStr(inputMap) + ",请求失败了" );
                e.printStackTrace();
                log.error(e.getMessage());
            }
        }

        //设置http ip到redis中，让安全培训那边调用
        eruptCacheRedis.getStringRedisTemplate().opsForValue().set(ExchangeDataController.EXCHANGE_DATA_ERUPT_URL,eruptPlatformService.getOption(ExchangeDataController.EXCHANGE_DATA_ERUPT_URL).getAsString());
        return  result.toString() ;
    }

}
