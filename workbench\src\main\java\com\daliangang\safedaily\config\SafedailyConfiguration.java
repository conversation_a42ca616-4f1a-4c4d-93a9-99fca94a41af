package com.daliangang.safedaily.config;

import com.daliangang.emergency.entity.Drill;
import com.daliangang.safedaily.entity.*;
import com.daliangang.safedaily.entity.Record;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.context.annotation.Configuration;
import xyz.erupt.core.constant.MenuTypeEnum;
import xyz.erupt.core.module.EruptModule;
import xyz.erupt.core.module.EruptModuleInvoke;
import xyz.erupt.core.module.MetaMenu;
import xyz.erupt.core.module.ModuleInfo;
import xyz.erupt.core.util.EruptSpringUtil;
import xyz.erupt.upms.service.EruptMenuService;
import xyz.erupt.upms.util.EruptMenuUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * @Author: zengwensi
 * @Description:
 * @Date: Created in 2023/3/17 15:18
 * @Modified By
 */
@Configuration
public class SafedailyConfiguration implements EruptModule, ApplicationRunner {

    static {
        EruptModuleInvoke.addEruptModule(SafedailyConfiguration.class);
    }

    @Override
    public ModuleInfo info() {
        return ModuleInfo.builder().name("daliangang-safedaily").build();
    }

    @Override
    public List<MetaMenu> initMenus() {
        EruptMenuService eruptMenuService = EruptSpringUtil.getBean(EruptMenuService.class);
        String moduleId = "safedaily";
        eruptMenuService.registerModule(moduleId, "港口危险货物安全日常业务监管系统", 2);
        AtomicInteger rootSort = new AtomicInteger(200);
        AtomicInteger sort = new AtomicInteger();
        List<MetaMenu> menus = new ArrayList<>();

        MetaMenu four = EruptMenuUtils.createRootMenu(null, "$dlg-four", "四项机制", "fa fa-foursquare", rootSort, moduleId);
        menus.add(four);
        menus.add(EruptMenuUtils.createMenu(CheckFill.class, four, sort, MenuTypeEnum.TABLE, "安全生产责任制", "CheckFill", moduleId, null));//安全生产责任制
        menus.add(EruptMenuUtils.createMenu(Release.class, four, sort, MenuTypeEnum.TABLE, "安全承诺公告", "Release", moduleId, null));//安全承诺公告
        menus.add(EruptMenuUtils.createMenu(Report.class, four, sort, MenuTypeEnum.TABLE, "可靠性报告单", "Report", moduleId, null));//可靠性报告单

        //安全三日管理
        MetaMenu anQuanSanRe = EruptMenuUtils.createRootMenu(null, "$dlg-sanre", "安全三日管理", "fa fa-bullhorn", rootSort, moduleId);
        menus.add(anQuanSanRe);
        menus.add(EruptMenuUtils.createMenu(OfflineTraining.class, anQuanSanRe, sort, MenuTypeEnum.TABLE, "培训考试日", "OfflineTraining", moduleId, ""));//应急演练日
        menus.add(EruptMenuUtils.createMenu(Drill.class, anQuanSanRe, sort, MenuTypeEnum.TABLE, "应急演练日", "Drill", moduleId, "fa fa-bell"));//应急演练日
        menus.add(EruptMenuUtils.createMenu(InspectExpertDaily.class, anQuanSanRe, sort, MenuTypeEnum.TABLE, "专家检查日", "InspectExpertDaily", moduleId, ""));//专家检查日

        //双倒查管理
        menus.add(EruptMenuUtils.createMenu(DoubleCheckManagement.class, null, rootSort, MenuTypeEnum.TABLE, "双倒查管理", "DoubleCheckManagement", moduleId, "fa fa-institution"));
        //安全吹哨人管理
        menus.add(EruptMenuUtils.createMenu(Whistleblower.class, null, rootSort, MenuTypeEnum.TABLE, "安全吹哨人管理", "Whistleblower", moduleId, "fa fa-bell-o"));
        //七一六安全警示
        menus.add(EruptMenuUtils.createMenu(SafetyAlertDayManagement.class, null, rootSort, MenuTypeEnum.TABLE, "七一六安全警示", "SafetyAlertDayManagement", moduleId, "fa fa-calendar"));

        //安全检查管理
        MetaMenu safetyInspectionMan = EruptMenuUtils.createRootMenu(four, "safetyInspectionMan", "安全检查管理", "fa fa-flag-checkered", rootSort, moduleId);
        menus.add(safetyInspectionMan);
        menus.add(EruptMenuUtils.createMenu(DailyInspection.class, safetyInspectionMan, sort, MenuTypeEnum.TABLE, "日检查", "DailyInspection", moduleId, null)); //日检查
        menus.add(EruptMenuUtils.createMenu(WeeklyReport.class, safetyInspectionMan, sort, MenuTypeEnum.TABLE, "周报告", "WeeklyReport", moduleId, null));//周报告
        menus.add(EruptMenuUtils.createMenu(MonthlyScheduling.class, safetyInspectionMan, sort, MenuTypeEnum.TABLE, "月调度", "MonthlyScheduling", moduleId, null));//月调度

        //备案管理
        MetaMenu filingMan = EruptMenuUtils.createRootMenu(null, "filingMan", "备案管理", "fa fa-file", rootSort, moduleId);
        menus.add(filingMan);
        menus.add(EruptMenuUtils.createMenu(PlanManagement.class, filingMan, sort, MenuTypeEnum.TABLE, "应急预案管理", "PlanManagement", moduleId, null));//应急预案管理
        menus.add(EruptMenuUtils.createMenu(Preplan.class, filingMan, sort, MenuTypeEnum.TABLE, "重大危险源", "Preplan", moduleId, null));//重大危险源
        menus.add(EruptMenuUtils.createMenu(PortRecord.class, filingMan, sort, MenuTypeEnum.TABLE, "安全评价管理", "PortRecord", moduleId, null));//安全评价管理
        menus.add(EruptMenuUtils.createMenu(Record.class, filingMan, sort, MenuTypeEnum.TABLE, "安全条件审查", "Record", moduleId, null));//安全条件审查
        menus.add(EruptMenuUtils.createMenu(Design.class, filingMan, sort, MenuTypeEnum.TABLE, "设施设计审查", "Design", moduleId, null));//设施设计审查
        menus.add(EruptMenuUtils.createSimpleMenu("portDangerMan", "港口危险货物审批管理", null, filingMan, sort.incrementAndGet(), null, moduleId).hide()); //港口危险货物审批管理
        menus.add(EruptMenuUtils.createMenu(Security.class, filingMan, sort, MenuTypeEnum.TABLE, "港口保安备案", "Security", moduleId, null));//港口保安备案
        menus.add(EruptMenuUtils.createMenu(QualificationManage.class, filingMan, sort, MenuTypeEnum.TABLE, "作业资质年审", "QualificationManage", moduleId, null)); //作业资质年审

        //港口企业安全生产标准化
        MetaMenu standard = EruptMenuUtils.createRootMenu(null, "$standard", "安全标准化", "fa fa-files-o", rootSort, moduleId);
        menus.add(standard);
        menus.add(EruptMenuUtils.createMenu(StandardizationItem.class, standard, sort, MenuTypeEnum.TABLE, "评价项目管理", "StandardizationItem", moduleId, null));
        menus.add(EruptMenuUtils.createMenu(Standardization.class, standard, sort, MenuTypeEnum.TABLE, "标准化管理", "Standardization", moduleId, null));
        menus.add(EruptMenuUtils.createMenu(StandardizationSenond.class, standard, sort, MenuTypeEnum.TABLE, "第二年自评", "StandardizationSenond", moduleId, null).hide());
        menus.add(EruptMenuUtils.createMenu(StandardizationThird.class, standard, sort, MenuTypeEnum.TABLE, "第三年自评", "StandardizationThird", moduleId, null).hide());

        //知识库
        MetaMenu wiki = EruptMenuUtils.createRootMenu(null, "portSafeWiki", "安全法规库", "fa fa-diamond", rootSort, moduleId);
        menus.add(wiki);
        menus.add(EruptMenuUtils.createMenu(PortSafeWikiDir.class, wiki, sort, MenuTypeEnum.TABLE, "法规库分类", "PortSafeWikiDir", moduleId, null));
        menus.add(EruptMenuUtils.createMenu(PortSafeWikiEntry.class, wiki, sort, MenuTypeEnum.TABLE, "安全法规", "PortSafeWikiEntry", moduleId, null));

        //MSDS
        menus.add(EruptMenuUtils.createMenu(MSDS.class, null, rootSort, MenuTypeEnum.TABLE, "MSDS", "MSDS", moduleId, "fa fa-truck"));

        return menus;
    }

    @Override
    public void run(ApplicationArguments args) throws Exception {

    }
}
