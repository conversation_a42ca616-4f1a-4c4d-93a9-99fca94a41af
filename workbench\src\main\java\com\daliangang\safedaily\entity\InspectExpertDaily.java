package com.daliangang.safedaily.entity;

import com.daliangang.core.CompanyRenderHandler;
import com.daliangang.safedaily.operation.ButtonShowHandler;
import com.daliangang.safedaily.operation.DepartPowerHandler;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import xyz.erupt.annotation.Erupt;
import xyz.erupt.annotation.EruptField;
import xyz.erupt.annotation.expr.ExprBool;
import xyz.erupt.annotation.fun.DataProxy;
import xyz.erupt.annotation.query.Condition;
import xyz.erupt.annotation.sub_erupt.Power;
import xyz.erupt.annotation.sub_erupt.RowOperation;
import xyz.erupt.annotation.sub_erupt.Tpl;
import xyz.erupt.annotation.sub_field.Edit;
import xyz.erupt.annotation.sub_field.EditType;
import xyz.erupt.annotation.sub_field.Readonly;
import xyz.erupt.annotation.sub_field.View;
import xyz.erupt.annotation.sub_field.sub_edit.*;
import xyz.erupt.toolkit.db.Comment;
import xyz.erupt.toolkit.handler.DelOperationHandler;
import xyz.erupt.toolkit.handler.EditOperationHandler;
import xyz.erupt.toolkit.handler.RemoteEntityChoiceFetchHandler;
import xyz.erupt.toolkit.submit.CheckSubmit;
import xyz.erupt.toolkit.utils.TplUtils;
import xyz.erupt.upms.model.auth.DataAuthHandler;
import xyz.erupt.upms.model.auth.DataAuthModel;

import javax.persistence.Entity;
import javax.persistence.Table;
import javax.persistence.Transient;
import java.util.Date;
import java.util.List;

/**
 * @Title: InspectExpert
 * <AUTHOR>
 * @Package com.daliangang.safedaily.entity
 * @Date 2024/3/7 9:48
 * @description: 专家检查日
 */
@Erupt(name = "专家检查日",
        power = @Power(powerHandler = DepartPowerHandler.class),
        dataProxy = InspectExpertDaily.class,
        orderBy = "InspectExpertDaily.inspectDate desc",
        rowOperation = {
//                @RowOperation(
//                        title = "上报",
//                        icon = "fa fa-arrow-circle-o-up",
//                        operationHandler = ExpertEscalationHandler.class,
//                        ifExpr = "item.submitted=='未上报'",
//                        mode = RowOperation.Mode.SINGLE,
//                        show = @ExprBool(exprHandler = ButtonShowHandler.class)
//                ),
                @RowOperation(
                        title = "编辑",
                        icon = "fa fa-edit",
                        code = TplUtils.EDIT_OPER_CODE,
                        eruptClass = InspectExpertDaily.class,
                        operationHandler = EditOperationHandler.class,
//                        ifExpr = "item.submitted=='未上报'",
                        ifExprBehavior = RowOperation.IfExprBehavior.HIDE,
                        mode = RowOperation.Mode.SINGLE,
                        show = @ExprBool(exprHandler = ButtonShowHandler.class)
                ),
                @RowOperation(
                        title = "删除",
                        icon = "fa fa-trash-o",
                        operationHandler = DelOperationHandler.class,
//                        ifExpr = "item.submitted=='未上报'",
                        ifExprBehavior = RowOperation.IfExprBehavior.HIDE,
                        mode = RowOperation.Mode.SINGLE,
                        show = @ExprBool(exprHandler = ButtonShowHandler.class)
                )
        })
@Table(name = "tb_inspect_expert_daily")
@Entity
@Getter
@Setter
@Comment("专家检查日")
public class InspectExpertDaily extends DataAuthModel implements DataProxy <InspectExpertDaily> {

        @EruptField(
                views = @View(title = "企业名称",width = "260px"),
                edit = @Edit(title = "企业名称",search = @Search(ifRender = @ExprBool(exprHandler = CompanyRenderHandler.class)), readonly = @Readonly(exprHandler = DataAuthHandler.class), type = EditType.CHOICE, notNull = true,
                        choiceType = @ChoiceType(fullSpan = true,fetchHandler = RemoteEntityChoiceFetchHandler.class, fetchHandlerParams = {"main", "Enterprise", "org_code,name"}, reload = true)))
        @Comment("企业名称")
        @ApiModelProperty("企业名称")
        private String company;

        @EruptField(
                views = @View(title = "检查日期",width = "100px"),
                edit = @Edit(title = "检查日期", type = EditType.DATE, search = @Search(vague = true), notNull = true,
                        dateType = @DateType))
        @Comment("检查日期")
        @ApiModelProperty("检查日期")
        private java.util.Date inspectDate;

        @EruptField(
                views = @View(title = "专家名称",width = "150px"),
                edit = @Edit(title = "专家名称", type = EditType.INPUT, notNull = true,
                        inputType = @InputType))
        @Comment("专家名称")
        @ApiModelProperty("专家名称")
        private String expertName;


        @EruptField(
                views = @View(title = "专家级别",width = "150px"),
                edit = @Edit(title = "专家级别", type = EditType.INPUT, notNull = true,
                        inputType = @InputType))
        @Comment("专家级别")
        @ApiModelProperty("专家级别")
        private String expertLevel;

        @Transient
        @EruptField(
                edit = @Edit(title = "注意事项", type = EditType.TPL, tplType = @Tpl(path = "tpl/intro.tpl")))
        @Comment("注意事项")
        private String attention;

        @EruptField(
                views = @View(title = "上报时间",width = "100px",show = false),
                edit = @Edit(title = "上报时间", type = EditType.DATE, search = @Search, show = false,
                        dateType = @DateType))
        @Comment("上报时间")
        @ApiModelProperty("上报时间")
        private java.util.Date fillingDate;

        @EruptField(
                views = @View(title = "上报状态",width = "100px",show = false),
                edit = @Edit(title = "上报状态", type = EditType.BOOLEAN, show = false,
                        boolType = @BoolType(trueText = "已上报", falseText = "未上报")))
        @CheckSubmit(linkProperty = "createTime", checkTimeDiffPeriod = 1)
        @Comment("上报状态")
        @ApiModelProperty("上报状态")
        private Boolean submitted;

        @Override
        public String beforeFetch(List<Condition> conditions) {
//                if (DaliangangContext.isDepartmentUser()) return "submitted=true";
                return DataProxy.super.beforeFetch(conditions);
        }

        @Override
        public void addBehavior(InspectExpertDaily inspectExpertDaily) {
//                inspectExpertDaily.setSubmitted(Boolean.FALSE);
                inspectExpertDaily.setSubmitted(Boolean.TRUE);
                inspectExpertDaily.setFillingDate(new Date());
        }

        @Override
        public void beforeAdd(InspectExpertDaily inspectExpertDaily) {
//                EruptDao eruptDao = EruptSpringUtil.getBean(EruptDao.class);
//                List<InspectExpertDaily> inspectExpertDailies = eruptDao.queryEntityList(InspectExpertDaily.class, "company='" + inspectExpertDaily.getCompany() + "'");
//                inspectExpertDailies = inspectExpertDailies.stream().filter(d -> FindDateUtil.isThisWeek(d.getFillingDate())).collect(Collectors.toList());
//                if(inspectExpertDailies.size()>0){
//                        NotifyUtils.showErrorMsg("本周数据已经添加，请勿重复添加！");
//                }

        }
}
