package com.daliangang.rndpub.entity;


import com.daliangang.rndpub.proxy.InspectionProblemManageProxy;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import xyz.erupt.annotation.Erupt;
import xyz.erupt.annotation.EruptField;
import xyz.erupt.annotation.sub_erupt.Power;
import xyz.erupt.annotation.sub_field.Edit;
import xyz.erupt.annotation.sub_field.EditType;
import xyz.erupt.annotation.sub_field.View;
import xyz.erupt.annotation.sub_field.ViewType;
import xyz.erupt.annotation.sub_field.sub_edit.DateType;
import xyz.erupt.annotation.sub_field.sub_edit.LinkSelfType;
import xyz.erupt.jpa.model.MetaModel;
import xyz.erupt.toolkit.db.Comment;

import javax.persistence.Entity;
import javax.persistence.Table;

@Erupt(name = "检查问题管理", importTruncate = true, power = @Power(add = false, delete = false, export = false, importable = false, viewDetails = false, edit = false)
        , dataProxy = InspectionProblemManageProxy.class,
        orderBy = "inspectionDate desc"
        , rowOperation = {

})
@Getter
@Setter
@Table(name = "tb_inspection_problem_self")
@Entity
public class InspectionProblemManage extends MetaModel {

    public static final String LINK_TARGET = "#/build/table/InspectionResults";
    public static final String LINK_SQL =
        " select id as value,name as label from tb_procedure where name='${name}' " ;

    @EruptField(
            views = @View(title = "检查名称id",show = false ),
            edit = @Edit(title = "检查名称id", type = EditType.INPUT,show = false))
    private String procedureId;

    @EruptField(
            views = @View(title = "检查名称",
//                    type = ViewType.LINK_SELF,
//                    linkName="InspectionResults",
//                    condition="[{'code':'name','searchCode':'inspectionName'}]"
//            ),
                    type = ViewType.LINK_SELF,
                    linkSelfType = @LinkSelfType(target = InspectionProblemManage.LINK_TARGET,
                            filterSql = LINK_SQL,
                            filterKeys = @LinkSelfType.FilterKey(leftKey = "name",rightKey = "inspectionName"),
                            searchKeys = @LinkSelfType.SearchKey(key = "inspectionName", readonly = true,field = "procedureId")
                            )
            ),
            edit = @Edit(title = "检查名称", type = EditType.INPUT))
    private String name;

    @EruptField(
            views = @View(title = "检查企业数量", type = ViewType.LINK_SELF,
                    linkSelfType = @LinkSelfType(target = InspectionProblemManage.LINK_TARGET,
                            filterSql = LINK_SQL,
                            filterKeys = @LinkSelfType.FilterKey(leftKey = "name",rightKey = "inspectionName"),
                            searchKeys = @LinkSelfType.SearchKey(key = "inspectionName", readonly = true,field = "procedureId"))
            ),
            edit = @Edit(title = "检查企业数量", type = EditType.INPUT))
    private String enterprisesNum;


    @EruptField(
            views = @View(title = "检查问题数量", type = ViewType.LINK_SELF,
                    linkSelfType = @LinkSelfType(target = InspectionProblemManage.LINK_TARGET,
                            filterSql = LINK_SQL,
                            filterKeys = @LinkSelfType.FilterKey(leftKey = "name",rightKey = "inspectionName"),
                            searchKeys = @LinkSelfType.SearchKey(key = "inspectionName", readonly = true,field = "procedureId"))
            ),
            edit = @Edit(title = "检查问题数量", type = EditType.INPUT))
    private String totalQuestion;

    @EruptField(
            views = @View(title = "已发布问题数量", type = ViewType.LINK_SELF,
                    linkSelfType = @LinkSelfType(target = InspectionProblemManage.LINK_TARGET,
                            filterSql = LINK_SQL,
                            filterKeys = @LinkSelfType.FilterKey(leftKey = "name",rightKey = "inspectionName"),
                            searchKeys = @LinkSelfType.SearchKey(key = "inspectionName", readonly = true,field = "procedureId"))
            ),
            edit = @Edit(title = "已发布问题数量", type = EditType.INPUT))
    private String publishQuestionNum;

    @EruptField(
            views = @View(title = "发布率", type = ViewType.LINK_SELF,
                    linkSelfType = @LinkSelfType(target = InspectionProblemManage.LINK_TARGET,
                            filterSql = LINK_SQL,
                            filterKeys = @LinkSelfType.FilterKey(leftKey = "name",rightKey = "inspectionName"),
                            searchKeys = @LinkSelfType.SearchKey(key = "inspectionName", readonly = true,field = "procedureId"))
            ),
            edit = @Edit(title = "发布率", type = EditType.INPUT))
    private String publishRatio;


    @EruptField(
            views = @View(title = "整改通过问题数量",show = false),
            edit = @Edit(title = "整改通过问题数量", type = EditType.INPUT))
    private String passResult;


    @EruptField(
            views = @View(title = "待整改问题数量",show = false),
            edit = @Edit(title = "待整改问题数量", type = EditType.INPUT))
    private String rectifiedResult;

    @EruptField(
            views = @View(title = "已逾期问题数量",show = false),
            edit = @Edit(title = "已逾期问题数量", type = EditType.INPUT))
    private String overDue;

    @EruptField(
            views = @View(title = "整改率",show = false),
            edit = @Edit(title = "整改率", type = EditType.INPUT))
    private String rectifiedRatio;


    @EruptField(
            views = @View(title = "检查开始日期", show = false),
            edit = @Edit(title = "检查开始日期", type = EditType.DATE, notNull = true,
                    dateType = @DateType))
    @Comment("检查开始日期")
    @ApiModelProperty("检查开始日期")
    private java.util.Date inspectionDate;

    @EruptField(
            views = @View(title = "逾期未整改问题数量", type = ViewType.LINK_SELF,show = false,
                    linkSelfType = @LinkSelfType(target = RectificationManage.LINK_TARGET,
                            filterSql = LINK_SQL,
                            filterKeys = @LinkSelfType.FilterKey(leftKey = "name",rightKey = "inspectionName"),
                            searchKeys = @LinkSelfType.SearchKey(key = "inspectionName", readonly = true,field = "procedureId"))
            ),
            edit = @Edit(title = "逾期未整改问题数量", type = EditType.INPUT,show = false))
    private String overDueNotPass;

}
