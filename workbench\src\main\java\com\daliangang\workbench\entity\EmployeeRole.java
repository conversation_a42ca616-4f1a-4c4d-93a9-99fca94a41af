/*
 * Copyright © 2020-2035 yundingyun All rights reserved.
 */
package com.daliangang.workbench.entity;

import com.daliangang.workbench.handler.EmployeeRoleRenderHandler;
import com.daliangang.workbench.proxy.EmployeeRoleDataProxy;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import xyz.erupt.annotation.Erupt;
import xyz.erupt.annotation.EruptField;
import xyz.erupt.annotation.constant.AnnotationConst;
import xyz.erupt.annotation.expr.ExprBool;
import xyz.erupt.annotation.sub_erupt.Filter;
import xyz.erupt.annotation.sub_field.Edit;
import xyz.erupt.annotation.sub_field.EditType;
import xyz.erupt.annotation.sub_field.Readonly;
import xyz.erupt.annotation.sub_field.View;
import xyz.erupt.annotation.sub_field.sub_edit.ChoiceType;
import xyz.erupt.annotation.sub_field.sub_edit.Search;
import xyz.erupt.toolkit.db.Comment;
import xyz.erupt.toolkit.handler.SqlChoiceFetchHandler;
import xyz.erupt.upms.handler.RoleMenuFilter;
import xyz.erupt.upms.model.EruptMenu;
import xyz.erupt.upms.model.auth.DataAuthHandler;
import xyz.erupt.upms.model.auth.DataAuthModel;

import javax.persistence.*;
import java.util.HashSet;
import java.util.Set;

@Erupt(name = "员工角色管理", dataProxy = EmployeeRoleDataProxy.class, rowOperation = {
//        @RowOperation(confirm = false, title = "启用", icon = "fa fa-toggle-off", ifExpr = "item.state=='禁用'", ifExprBehavior = RowOperation.IfExprBehavior.HIDE, operationHandler = EmployeeInformationEnableHandler.class, operationParam = "on", mode = RowOperation.Mode.MULTI),
//        @RowOperation(confirm = false, title = "禁用", icon = "fa fa-toggle-on", ifExpr = "item.state=='启用'", ifExprBehavior = RowOperation.IfExprBehavior.HIDE, operationHandler = EmployeeInformationEnableHandler.class, operationParam = "off", mode = RowOperation.Mode.MULTI),
//        @RowOperation(title = "重置密码", icon = "fa fa-key", operationHandler = EmployeeInformationResetPasswordHandler.class, mode = RowOperation.Mode.SINGLE),
})
@Table(name = "tb_employee_role")
@Entity
@Getter
@Setter
@Comment("员工角色管理")
@ApiModel("员工角色管理")
@Slf4j
public class EmployeeRole extends DataAuthModel  {

    @Column(length = AnnotationConst.CODE_LENGTH)
    @EruptField(views = @View(title = "编码"), edit = @Edit(title = "编码", notNull = true, readonly = @Readonly))
    private String code;

    @EruptField(views = @View(title = "名称"), edit = @Edit(title = "名称", notNull = true, search = @Search(vague = true)))
    private String name;

    @EruptField(views = @View(title = "所属组织", show = true), edit = @Edit(title = "所属组织", type = EditType.CHOICE, show = true, notNull = true, search = @Search(ifRender =@ExprBool(exprHandler = EmployeeRoleRenderHandler.class)), readonly = @Readonly(exprHandler = DataAuthHandler.class), choiceType = @ChoiceType(fullSpan = true,fetchHandler = SqlChoiceFetchHandler.class, fetchHandlerParams = "select code,name from e_upms_org")))
    @Comment("所属组织")
    @ApiModelProperty("所属组织")
    private String company;

    @ManyToMany(fetch = FetchType.EAGER)
    @EruptField(views = @View(title = "菜单权限"), edit = @Edit(filter = @Filter(conditionHandler = RoleMenuFilter.class), title = "菜单权限", type = EditType.TAB_TREE))
//    @Transient
    private Set<EruptMenu> menus;

    public Set<EruptMenu> getMenus() {
        if (this.menus == null || this.menus.isEmpty()) this.menus = new HashSet<>();
        return this.menus;
    }
}
