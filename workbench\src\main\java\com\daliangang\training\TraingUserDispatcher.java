package com.daliangang.training;

import org.springframework.stereotype.Service;
import xyz.erupt.upms.fun.UserDispatcher;

import java.util.Map;

@Service
public class TraingUserDispatcher implements UserDispatcher {
    
    @Override
    public int sort() {
        return 0;
    }

    @Override
    public void register(Map<String, Object> params) {

    }

    @Override
    public void login(Map<String, Object> params) {

    }

    @Override
    public void logout(Map<String, Object> params) {

    }

    @Override
    public void changePwd(Map<String, Object> params) {

    }
}
