package com.daliangang.statistics.handler;

import com.daliangang.core.DaliangangContext;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.stereotype.Component;
import xyz.erupt.bi.fun.EruptBiHandler;
import xyz.erupt.upms.model.EruptOrg;
import xyz.erupt.upms.service.EruptUserService;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * 检查次数处理类
 * @Author: zengwensi
 * @Description:
 * @Date: Created in 2023/5/8 16:49
 * @Modified By
 */
@Component
public class RndpubInspectCountHandler   implements EruptBiHandler {

    @Resource
    private CommonHandler commonHandler;
    @Resource
    private EruptUserService eruptUserService;

    @Override
    public String exprHandler(String param, Map<String, Object> condition, String expr) {

        expr = commonHandler.exprHandlerSearchDate(param,condition,expr);
        //企业登录
        if(DaliangangContext.isEnterpriseUser()){
            EruptOrg eruptOrg = eruptUserService.getCurrentEruptUser().getEruptOrg();
            if(null != eruptOrg){
                String orgcode = eruptOrg.getCode();
                String replaceSql =  " and id in (select inspection_name from tb_inspection_results where check_object = '"+orgcode+"' ) ";
                expr = expr.replaceAll("#REPLACESQL",replaceSql) ;
            }

        }else if(DaliangangContext.isDepartmentUser()){
            //政府登录
            expr = commonHandler.exprHandlerAuthorityInspect(param,condition,expr);
        }



        return expr ;

    }

    @Override
    public void resultHandler(String param, Map<String, Object> condition, List<Map<String, Object>> result) {

    }

    @Override
    public void exportHandler(String param, Map<String, Object> condition, Workbook workbook) {

    }
}
