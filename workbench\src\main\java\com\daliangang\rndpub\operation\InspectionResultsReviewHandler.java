/*
 * Copyright © 2020-2035 yundingyun All rights reserved.
 */
package com.daliangang.rndpub.operation;

import com.daliangang.rndpub.entity.InspectionResultsView;
import com.daliangang.rndpub.entity.Rectify;
import com.daliangang.rndpub.form.InspectionrResultsReviewForm;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import xyz.erupt.annotation.fun.OperationHandler;
import xyz.erupt.core.annotation.EruptRouter;
import xyz.erupt.core.context.MetaContext;
import xyz.erupt.jpa.dao.EruptDao;
import xyz.erupt.toolkit.utils.EruptDaoUtils;
import xyz.erupt.toolkit.utils.NotifyUtils;

import javax.annotation.Resource;
import javax.transaction.Transactional;
import java.util.Date;
import java.util.List;
import java.util.Map;

@Service
public class InspectionResultsReviewHandler implements OperationHandler<InspectionResultsView, InspectionrResultsReviewForm> {



    @RestController
    @Transactional
    public static class InspectionrResultsReviewController {

        @Resource
        private EruptDao eruptDao;

        @RequestMapping("erupt-api/data/InspectionrResultsReviewForm/{id}")
        @EruptRouter(verifyType = EruptRouter.VerifyType.LOGIN)
        public Map<String, Object> getById(@PathVariable("id") Long id) {

            InspectionResultsView inspectionResultsView = eruptDao.queryEntity(InspectionResultsView.class, "id = " + id);
            Map<String, Object> map = EruptDaoUtils.castMap(inspectionResultsView);

            return map;
        }
    }

    @Resource
    private EruptDao eruptDao;

    @Transactional
    @Override
    public String exec(List<InspectionResultsView> data, InspectionrResultsReviewForm inspectionrResultsReviewForm, String[] param) {

        //更新复查内容到整改表中
        Long rectifyId = eruptDao.getJdbcTemplate().queryForObject("select id from tb_rectify where inspection_results_id = " + inspectionrResultsReviewForm.getId() + "  order by create_time desc limit 1", Long.class);
        Rectify rectify = eruptDao.queryEntity(Rectify.class, "id = " + rectifyId);
        rectify.setReviewDate(new Date());//复查时间
        rectify.setReviewResult(inspectionrResultsReviewForm.getRectificationResults());//复查结果
        rectify.setDeadline(inspectionrResultsReviewForm.getRectificationTime1());//整改截止时间
        rectify.setRemark(inspectionrResultsReviewForm.getRemark());//备注
        eruptDao.merge(rectify);

        InspectionResultsView inspectionResultsView = eruptDao.queryEntity(InspectionResultsView.class, " id = " + inspectionrResultsReviewForm.getId());
        String result = null;
        if (inspectionrResultsReviewForm.getRectificationResults()) {
            result = "PASS";
        } else {
            result = "NOT_PASS";
            //未通过，则整改状态 为待整改
            inspectionResultsView.setRectificationStatus("TO_BE_RECTIFIED");
        }
        inspectionResultsView.setRectificationAuditStatus("Audited");//审核状态为 已审核
        inspectionResultsView.setInspectionResult(result); //复查结果 通过不通过 数据字典值
        inspectionResultsView.setRectificationResults(inspectionrResultsReviewForm.getRectificationResults()) ; //通过不通过布尔值
        inspectionResultsView.setReviewer(MetaContext.getUser().getName());//复查人员
        inspectionResultsView.setRemark(inspectionrResultsReviewForm.getRemark());//备注

        //如果有重置整改时间
        if(null != inspectionrResultsReviewForm.getRectificationTime1()){
            inspectionResultsView.setDeadline(inspectionrResultsReviewForm.getRectificationTime1());//整改截止时间
        }
        eruptDao.merge(inspectionResultsView);
        return NotifyUtils.getSuccessNotify("提交成功,审核后不能修改");
    }
}
