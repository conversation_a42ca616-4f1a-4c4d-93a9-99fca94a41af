package com.daliangang.workbench.sql;

import org.springframework.stereotype.Repository;

/**
 * <AUTHOR>
 * @since :2023/4/23:10:37
 */
@Repository
public class EnterpriseCertificateSql {
    // 统计企业附征
    public String selectEnterpriseCertificate (String orgCode) {

        String sql = "SELECT COUNT(tec.id) as num ,'已逾期' as type from tb_enterprise te left join   tb_enterprise_certificate tec  on te.id = tec.enterprise_id  where CURRENT_DATE() > tec.expiration_time  and ";
            sql += " tec.company "+orgCode+"";

            sql += " UNION  ALL SELECT COUNT(tec.id) as num ,'即将逾期' as type from tb_enterprise te left join   tb_enterprise_certificate tec  on te.id = tec.enterprise_id  where CURRENT_DATE() <= tec.expiration_time and datediff(tec.expiration_time,CURRENT_DATE()) < 90  and ";
            sql += " tec.company "+orgCode+"";

            sql += " UNION  ALL SELECT COUNT(tec.id) as num ,'正常' as type from tb_enterprise te left join   tb_enterprise_certificate tec  on te.id = tec.enterprise_id  where datediff(tec.expiration_time,CURRENT_DATE()) > 90  and ";
            sql += " tec.company "+orgCode+"";

        return sql;
    }

    // 统计企业数量
    public String selectEnterprisenum (String orgCode) {

        String sql = "SELECT COUNT(*) as num  from tb_enterprise ";
        sql += "where org_code "+orgCode+"";
        return sql;
    }


    public String selectEnterpriseTypeNum (String orgCode) {

        String sql = "select COUNT(*) as num,type   from tb_enterprise where ";

        sql += " org_code "+orgCode+"";

        sql+= "GROUP by type";
        return sql;
    }

    public String selectNumThree (String orgCode) {

        String sql = " select COUNT(*) as num,'无' as volume,'泊位数量' as type,'无' as hongKongCertificateValidity, '无' as zdnum,'无' as zsnum  from tb_berth tb where ";
        sql += " org_code ='" + orgCode + "'";

        sql += "  UNION all select  COUNT(*) as num,'无' as volume,'罐区数量' as type,'无' as hongKongCertificateValidity, '无' as zdnum,'无' as zsnum from tb_tank_farm ttf where ";
        sql += " org_code ='" + orgCode + "'";

        sql += "  UNION all  select  COUNT(*) as num,'无' as volume,'罐组数量' as type,'无' as hongKongCertificateValidity, '无' as zdnum,'无' as zsnum  from tb_tank_group ttg where ";
        sql += " org_code ='" + orgCode + "'";

        sql += "  UNION all  select COUNT(*) as num,SUM(volume) as volume,'储罐数量' as type,'无' as hongKongCertificateValidity, '无' as zdnum,'无' as zsnum  from tb_storage_tank tst where";
        sql += " org_code ='" + orgCode + "'";

        sql += " UNION all select  '无' as num,'无' as volume,'港经证有效期' as type, (CASE when CURRENT_DATE() > validity_period  then '过期' when CURRENT_DATE() <= validity_period then  validity_period  end) as hongKongCertificateValidity,'无' as zdnum,'无' as zsnum from tb_enterprise te where ";
        sql += " org_code ='" + orgCode + "'";

        sql += " UNION all select '无' as num,'无' as volume,'水路运输从业资格证' as type, '无' as hongKongCertificateValidity, '无' as zdnum,count(tec.id) as zsnum  from tb_employee_information tei  left join tb_employee_certificate tec on tei.id  = tec.employee_information_id  where tec.certificate_type = 'waterwayTransportation' and ";
        sql += " tei.org_code ='" + orgCode + "'";

        sql += " UNION all select '无' as num,'无' as volume,'特种设备操作证书' as type, '无' as hongKongCertificateValidity, '无' as zdnum,count(tec.id) as zsnum  from tb_employee_information tei  left join tb_employee_certificate tec on tei.id  = tec.employee_information_id where tec.certificate_type = 'specialEquipment' and ";
        sql += " tei.org_code ='" + orgCode + "'";

        sql += "  UNION all select '无' as num,'无' as volume,'特种作业操作证书' as type, '无' as hongKongCertificateValidity, '无' as zdnum,count(tec.id) as zsnum  from tb_employee_information tei  left join tb_employee_certificate tec on tei.id  = tec.employee_information_id where tec.certificate_type = 'specialOperations' and ";
        sql += " tei.org_code ='" + orgCode + "'";

        sql += "  UNION all select '无' as num,'无' as volume, '其他证书' as type,'无' as hongKongCertificateValidity, '无' as zdnum,count(tec.id) as zsnum  from tb_employee_information tei  left join tb_employee_certificate tec on tei.id  = tec.employee_information_id where tec.certificate_type = 'otherCertificates' and ";
        sql += " tei.org_code ='" + orgCode + "'";

        sql += " UNION all select  '无' as num,'无' as volume,'重大风险源备案' as type, '无' as hongKongCertificateValidity, COUNT(tpd.id) as zdnum,'无' as zsnum from tb_preplan tp left join tb_preplan_data tpd on tp.id = tpd.data_source_id where ";
        sql += " tp.org_code ='" + orgCode + "'";

        return sql;
    }


    public String selectFzThree (String orgCode) {

        String sql = " SELECT (CASE when tec.expiration_time < CURRENT_DATE() then '过期' when CURRENT_DATE() <= expiration_time then  expiration_time  end) as fz from   tb_enterprise te left join   tb_enterprise_certificate tec  on te.id = tec.enterprise_id  where ";
        sql += " te.org_code ='" + orgCode + "'";
        sql += " order by tec.expiration_time DESC limit 0,1 ";
        return sql;
    }

    public String selectBzhThree (String orgCode) {

        String sql = " SELECT  GROUP_CONCAT((CASE when ts.grade = 'LEVEL_1' then '一级' when ts.grade = 'LEVEL_II' then '二级' end), ',', (CASE when ts.effective_date < CURRENT_DATE() then '过期' when CURRENT_DATE() <= effective_date then  effective_date  end)) as bzh from   tb_standardization ts   where ";
        sql += " ts.org_code ='" + orgCode + "'";
        sql += " order by ts.effective_date DESC limit 0,1 ";
        return sql;
    }

    public String selectAqThree (String orgCode) {

        String sql = " SELECT (CASE when tpr.validity_period < CURRENT_DATE() then '过期' when CURRENT_DATE() <= validity_period then  validity_period  end) as aq from  tb_port_record tpr   where ";
        sql += " tpr.org_code ='" + orgCode + "'";
        sql += " order by tpr.validity_period DESC limit 0,1 ";
        return sql;
    }

    public String selectGkThree (String orgCode) {

        String sql = " SELECT (CASE when ts.effective_date < CURRENT_DATE() then '过期' when CURRENT_DATE() <= effective_date then  effective_date  end) as gk from  tb_security ts   where ";
        sql += " ts.org_code ='" + orgCode + "'";
        sql += " order by ts.effective_date DESC limit 0,1 ";
        return sql;
    }


    public String selectYaThree (String orgCode) {

        String sql = " SELECT (CASE when evaluation_time < CURRENT_DATE() then '过期' when CURRENT_DATE() <= evaluation_time then evaluation_time  end) as ya from  tb_plan_management  where ";
        sql += " org_code ='" + orgCode + "'";
        sql += "  order by evaluation_time DESC limit 0,1 ";
        return sql;
    }

    public String selectYaNumThree (String orgCode) {

        String sql = " SELECT COUNT(*) as ya  from tb_plan_management  where ";
        sql += " org_code ='" + orgCode + "'";

        return sql;
    }
}
