server:
  address: 0.0.0.0
  port: ${SERVER_PORT:8080}
  compression:
    mime-types: application/javascript,text/css,application/json,application/xml,text/html,text/xml,text/plain
    enabled: true
  error:
    includeException: true
    includeStacktrace: ALWAYS
    includeMessage: ALWAYS

#  undertow:
#    threads:
#      worker: ${UNDERTOW_WORKER:16}

#  tomcat:
#    uri-encoding: UTF-8
#    max-connections: 10000
#    accept-count: 10000
#    connection-timeout: 10000
#    threads:
#      min-spare: 16
#      max: 2048

erupt-site:
  domain: ${ERUPT_DOMAIN:} #后端服务器地址
  fileDomain: ${ERUPT_FILE_DOMAIN:http://dlg-mgmt.yundingyun.net:30900/dlg-minio} #附件上传地址
  sourceDomain: ${ERUPT_SOURCE_DOMAIN:} #跨域回源地址
  operateDomain: ${ERUPT_OPERATE_DOMAIN:http://dlg-mgmt.yundingyun.net} #运维管理地址
  riderDomain: ${ERUPT_RIDER_DOMAIN:} #移动端后端服务地址
  title: ${ERUPT_TITLE:大连港} #页面标题
  desc: ${ERUPT_DESC:危险货物港区重大安全风险管控平台} #应用名称
  copyright: ${ERUPT_COPURIGHT:false} #显示版权条
  amapKey: ${ERUPT_AMAP_KEY:f8fbb703b56992cc847a9fe3f5e24618} #地图通讯密钥
  logoPath: ${ERUPT_LOGO_PATH:} #封面图片自定义路径
  logoText: ${ERUPT_LOGO_TEXT:危险货物港区重大安全风险管控平台} #封面文字
  rightTopText: ${ERUPT_RIGHT_TOP_TEXT:} #顶栏右上角文字
  homePage: ${ERUPT_HOME_PAGE:} #自定义首页
  registerPage: ${ERUPT_REGISTER_PAGE:} #自定义注册页
  forgotPwdPage: ${ERUPT_FORGOT_PWD_PAGE:} #自定义密码重置页
  routes: ${ERUPT_ROUTE_RULES:} #后端服务路由转发规则
  showSelect: ${ERUPT_SHOW_SELECT:true} #是否显示header 切换系统控件
  tableLink: ${ERUPT_TABLE_LINK:false} #表格字段查看详情
erupt-app:
  # 登录失败几次出现验证码，值为0时表示一直需要登录验证码
  verifyCodeCount: ${VERIFY_CODE_COUNT:2}
  # 登录密码是否加密传输，特殊场景如：LDAP登录可关闭该功能获取密码明文
  pwdTransferEncrypt: true
  # 多语言配置，默认支持：简体中文、繁体中文、英文、日文；具体配置详见erupt-i18n模块
  locales: [ "zh-CN" ]
  # 自定义登录页路径，1.10.6 及以上版本支持
  loginPagePath:

erupt:
  # 是否开启csrf防御
  csrfInspect: false
  # 开启redis方式存储session，默认false，开启后需在配置文件中添加redis配置（同 Spring Boot）
  redisSession: true
  # redis session是否自动续期，1.10.8及以上版本支持
  redisSessionRefresh: true
  # 附件上传存储路径, 默认路径为：/opt/erupt-attachment
  uploadPath: D:/erupt/pictures
  # 是否保留上传文件原始名称
  keepUploadFileName: true
  # 项目初始化方式，默认值 file，1.10.10及以上版本支持，可选值：
  # NONE  不执行初始化代码
  # EVERY 每次启动都进行初始化
  # FILE  通过标识文件判断是否需要初始化
  initMethodEnum: file
  upms:
    # 登录session时长（redisSession为true时有效）
    expireTimeByLogin: ${ERUPT_UPMS_EXPIRETIMEBYLOGIN:180}
    # 严格的角色菜单策略，如果非管理员用户拥有“角色管理权限”则仅能编辑已有权限的角色菜单
    # 1.10.6 及以上版本支持
    strictRoleMenuLegal: true
  # 是否开启任务调度（导入erupt-job时有效）
  job.enable: true
  # 是否记录操作日志，默认true，该功能开启后可在【系统管理 → 操作日志】中查看操作日志
  security.recordOperateLog: true
  cloud-server:
    # cloud key 命名空间(可选配置)
    cloud-name-space: '${CLOUD_NAME_SPACE:daliangang-cloud}'
    # node节点持久化时长，单位：ms (可选配置)
    node-expire-time: 60000
    # node节点存活检查周期，单位：ms (可选配置)
    node-survive-check-time: 120000
  expert:
    applyLink: ${EXPERT_APPLYLINK:daliangang-cloud}


# application.yml写法
spring:
  main:
    allow-bean-definition-overriding: true
  cloud:
    config:
      enabled: false
  application:
    name: ${erupt-site.title}
  datasource:
    url: jdbc:mysql://${MYSQL_HOST:*************}:${MYSQL-PORT:30000}/${MYSQL_DB:erupt}?useSSL=false&useUnicode=true&characterEncoding=utf-8&zeroDateTimeBehavior=convertToNull&transformedBitIsBoolean=true&serverTimezone=GMT%2B8&nullCatalogMeansCurrent=true&allowPublicKeyRetrieval=true
    username: ${MYSQL_USER:root}
    password: ${MYSQL_PWD:1234@abcD}
    type: com.alibaba.druid.pool.DruidDataSource

    driver-class-name: com.mysql.cj.jdbc.Driver
    #    driver-class-name: ${MYSQL_DRIVER:com.p6spy.engine.spy.P6SpyDriver}
    hikari:
      maximum-pool-size: ${MYSQL_MAX_CONN:10000}  # 最大连接数
      minimum-idle: ${MYSQL_MIN_CONN:16}  # 最小空闲数
      connection-timeout: 20000 # 获取连接超时时间； 默认30s
      pool-name: ${MYSQL_POOL_NAME:daliangang}  # 连接池名称
      idle-timeout: 600000 # 空闲超时时间；默认是十分钟；空闲时间超过设定时间则会被回收
      auto-commit: true # 是否自动提交
      max-lifetime: 1800000 # 最大存活时间，默认30分钟
      connection-test-query: SELECT 1  # 连接数据库后测试语句
      validation-timeout: 10000 #
      # schema: 设置模式，例如 postgresql 有模式这个概念

    # druid 数据源专有配置
    # 不是druid-spring-boot-starter依赖，SpringBoot默认是不注入druid数据源专有属性值的，需要自己绑定
    druid:
      initialSize: ${MYSQL_MIN_CONN:16}
      minIdle: ${MYSQL_MIN_CONN:8}
      maxActive: ${MYSQL_MAX_CONN:2048}
      maxWait: 60000
      timeBetweenEvictionRunsMillis: 60000
      minEvictableIdleTimeMillis: 300000
      validationQuery: SELECT 1 FROM DUAL
      testWhileIdle: true
      testOnBorrow: false
      testOnReturn: false
      poolPreparedStatements: true
      maxPoolPreparedStatementPerConnectionSize: 20
      useGlobalDataSourceStat: true
      connectionProperties: druid.stat.mergeSql=true;druid.stat.slowSqlMillis=5000
      # 配置监控统计拦截的filters，去掉后监控界面sql无法统计。stat:监控统计 log4:日志记录 wall:防御sql注入
      # 如果运行时报错：ClassNotFoundException:orgapache.log4j.Priority，则导入log4j依赖即可
      filters: stat
      # 配置 DruidStatFilter
      web-stat-filter:
        enabled: true
        url-pattern: /*
        exclusions: .js,*.gif,*.jpg,*.bmp,*.png,*.css,*.ico,/druid/*
      # 配置 DruidStatViewServlet
      stat-view-servlet:
        url-pattern: /druid/*
        # 禁用 HTML 中 Reset All 按钮
        reset-enable: false
        # 登录用户名/密码
        login-username: ${spring.security.user.name}
        login-password: ${spring.security.user.password}
        # 需要设置enabled=true,否则会报出There was an unexpected error (type=Not Found, status=404).错误，或者将druid-spring-boot-starter的版本降低到1.1.10及以下
        # 是否启用StatViewServlet（监控页面）默认值为false（考虑到安全问题默认并未启动，如需启用建议设置密码或白名单以保障安全）
        enabled: true
        allow:


  jackson:
    dateFormat: yyyy-MM-dd HH:mm:ss
    timeZone: GMT+8

  task:
    execution:
      pool:
        core-size: 16
        max-size: 2048
        keep-alive: 10s
        queue-capacity: 2000

  jpa:
    show-sql: false
    generate-ddl: true
    open-in-view: true
    database-platform: org.hibernate.dialect.MySQL5InnoDBDialect
    database: mysql
    #jpa批量插入与更新
    properties:
      hibernate:
        order_insert: true
        order_update: true
        jdbc:
          batch_size: 500
          batch_versioned_data: true
  hibernate:
    ddl-auto: update

  servlet:
    multipart:
      max-file-size: ${MAX_FILE_SIZE:1024}MB
      max-request-size: ${MAX_FILE_SIZE:1024}MB
  #Redis配置
  redis:
    database: ${REDIS_DATABASE:0}
    host: ${REDIS_HOST:localhost}
    port: ${REDIS_PORT:6379}
    password: ${REDIS_PWD:}
    lettuce:
      pool:
        max-active: 128  # 最大活动连接数
        max-idle: 64     # 最大空闲连接数
        min-idle: 16     # 最小空闲连接数
        max-wait: -1ms  # 最大等待时间，-1表示无限制
      shutdown-timeout: 100ms  # 关闭连接的超时时间

  security:
    user:
      name: admin
      password: 1234@abcD

  #springboot-admin
  boot:
    admin:
      context-path: /admin
      client:
        url: http://localhost:${server.port}/admin
        username: ${spring.security.user.name}
        password: ${spring.security.user.password}
        enabled: ${SPRING_BOOT_ADMIN_ENABLE:false}

logging:
  #spring admin boot日志输出配置,需要跟logback-spring.xml配置中日志路径一致
  file:
    name: ./log/info/catalina.log

#yml - actuator 监控, 配置spring-boot-admin使用
management:
  endpoints:
    web:
      exposure:
        include: "*"
  endpoint:
    health:
      show-details: ALWAYS



knife4j:
  enable: true
  openapi:
    title: ${DOC_API_TITILE:项目开发文档}
    description: ${DOC_API_DESCRIPTION:项目开发文档}
    concat: ${DOC_API_COCAT:天津云顶云科技有限公司}
    version: ${DOC_API_VERSION:v1.0}
    group:
      erupt:
        group-name: ${DOC_API_GROUP:大连港}
        api-rule: package
        api-rule-resources:
          - ${DOC_API_PACKAGE:com.daliangang}
  production: false
  setting:
    custom-code: 500
    language: zh_cn
    enable-footer: false
    enable-debug: true
    enable-open-api: true
  documents:
    - name: ${DOC_MANUAL_TITILE:用户手册}
      group: ${DOC_MANUAL_GROUP:erupt}
      locations: classpath:markdown/*
  cors: true

#COS存储
cos:
  access_key: ${COS_ACCESS_KEY:AKID2va0DE1MUN2SffS0MWUM2Wr0zlRuAF8o}
  secret_key: ${COS_SECRET_KEY:Xs0vdAVgfS2PZWI1gDYhdQ6e3IPL4h9Y}
  bucket: ${COS_BUCKET:exhibition-**********}
  region: ${COS_REGION:ap-beijing}

#Minio存储
minio:
  endpoint: ${MINIO_ENDPOINT:http://************:30900}
  bucketName: ${MINIO_BUCKET_NAME:test}
  accesskey: ${MINIO_ACCESS_KEY:kJahLUsrzohb2g7a}
  secretKey: ${MINIO_SECRET_KEY:2fZYT2R0LnssPSkopmjLFRz9OFejVncI}

#心知天气
xinz:
  gy: POL9w-BnjtBF3h6BV
  sy: SswzIooTGlsdaFF-P

### 调度中心部署根地址 [选填]：如调度中心集群部署存在多个地址则用逗号分隔。执行器将会使用该地址进行"执行器心跳注册"和"任务结果回调"；为空则关闭自动注册；
xxl:
  job:
    admin:
      addresses: ${XXL_JOB_HOST:http://localhost:8888}/xxl-job-admin
      userName: admin
      password: 123456
    executor:
      ### 执行器AppName [选填]：执行器心跳注册分组依据；为空则关闭自动注册
      appname: ${XXL_JOB_APPNAME:daliangang}

#graylog
graylog:
  graylogHost: ${GRAYLOG_HOST:dlg-log.yundingyun.net} #日志中心地址
  graylogPort: ${GRAYLOG_PORT:30004} #日志中心工作端口
  app_name: ${erupt-site.title} #日志记录应用名称
  environment: ${GRAYLOG_ENV:dev} #日志所在环境(dev|test|pre|pro)

#markdown包含项
markdown:
  dict: portArea,type,educational,postTitle,certificateType,preplanType,dangerType,grade,technicalStatus,technicalStatus,yardNow,MaintenanceType,drillType,teamLevel,materialType
  erupt: OfflineTraining,DoubleCheckManagement,SafetyAlertDayManagement,Whistleblower,InspectExpertDaily

onelines:
  appId: ${ONELINE_APPID:d0124bbb074e60a97c63f8aa5e2db9f3e321112ab2835c1df2d2a4ae821260540f9bca310c219f6049eb1419a112df2d}
  security: ${ONELINE_SECURITY:fb2c9299c483e226430cf61721d0bde75b4ff7fef115b12c0aa4e3075e8a22b6}
