package com.daliangang.training.entity;

import lombok.*;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Date 2023/5/19
 * @Description:
 */

@Getter
@Setter
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class UserSyncEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    //用户id
    private Long id;

    //用户名称
    private String name;

    //用户手机
    private String phone;

    //用户性别
    private Integer sex;

    //用户状态
    private Integer state;

    //用户orgCode
    private String orgCode;

    //用户岗位 | 分割
    private String posts;

    //删除标识
    private Boolean delFlag;

    //更新/新增标识
    private Boolean updateFlag;

}
