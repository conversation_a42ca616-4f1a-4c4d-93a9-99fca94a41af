/*
 * Copyright © 2020-2035 yundingyun All rights reserved.
 */
package com.daliangang.rndpub.operation;

import com.daliangang.rndpub.entity.CheckPerson;
import org.springframework.stereotype.Service;
import xyz.erupt.annotation.fun.OperationHandler;
import xyz.erupt.jpa.dao.EruptDao;

import javax.annotation.Resource;
import javax.transaction.Transactional;
import java.util.List;

@Service
public class CheckpersonDisableHandler implements OperationHandler<CheckPerson, Void> {

    @Resource
    private EruptDao eruptDao;

    @Override
    @Transactional
    public String exec(List<CheckPerson> data, Void unused, String[] param) {
        CheckPerson checkperson = data.get(0);
        checkperson.setState(false);
        eruptDao.merge(checkperson);
        return null;
    }
}
