package com.daliangang.workbench.init;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import xyz.erupt.excel.model.ExcelHeader;

/**
 * @Author：chb
 * @Package：com.daliangang.workbench.init
 * @Project：erupt
 * @name：InitDepartment
 * @Date：2023/3/5 22:05
 * @Filename：InitDepartment
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class InitDepartment extends InitTemplate {
    @ExcelHeader(col = 0, name = "主管部门名称")
    private String name;

    @ExcelHeader(col = 1, name = "所属港区")
    private String portArea;

    @ExcelHeader(col = 2, name = "上级部门")
    private String parent;

    @ExcelHeader(col = 3, name = "所属权限")
    private String roleTemplate;

    @ExcelHeader(col = 4, name = "初始组织编码")
    private String initOrgCode;

    public static InitDepartment valueOf(InitTemplate template) {
        return InitDepartment.builder().name(template.getParam1())
                .portArea(template.getParam2())
                .parent(template.getParam3())
                .roleTemplate(template.getParam4())
                .initOrgCode(template.getParam5())
                .build();
    }
}
