package com.daliangang.majorisk.operation;

import com.daliangang.majorisk.entity.FiveChecklistsEmergencyDisposal;
import com.daliangang.majorisk.entity.FiveChecklistsFoundation;
import com.google.gson.internal.LinkedTreeMap;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import xyz.erupt.annotation.fun.OperationHandler;
import xyz.erupt.common.http.EruptRemoteUtils;
import xyz.erupt.core.annotation.EruptRouter;
import xyz.erupt.core.util.EruptSpringUtil;
import xyz.erupt.core.view.EruptApiModel;
import xyz.erupt.jpa.dao.EruptDao;
import xyz.erupt.toolkit.db.EruptResultMap;
import xyz.erupt.toolkit.remote.RemoteProxyService;
import xyz.erupt.toolkit.utils.EruptDaoUtils;
import xyz.erupt.toolkit.utils.NotifyUtils;

import javax.annotation.Resource;
import javax.transaction.Transactional;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @since :2023/4/4:11:14
 */
@Service
public class FiveChecklistsEmergencyDisposalHandler implements OperationHandler<FiveChecklistsFoundation, FiveChecklistsEmergencyDisposal> {

    @Resource
    private EruptDao eruptDao;

    @Override
    @Transactional
    public String exec(List<FiveChecklistsFoundation> data, FiveChecklistsEmergencyDisposal fiveChecklistsEmergencyDisposal, String[] param) {
        //fiveChecklistsEmergencyDisposal.setFiveChecklistsFoundation(data.get(0));
        if (ObjectUtils.isEmpty(fiveChecklistsEmergencyDisposal.getEmergencyPlan())
        || ObjectUtils.isEmpty(fiveChecklistsEmergencyDisposal.getEmergencyManagement())
        || ObjectUtils.isEmpty(fiveChecklistsEmergencyDisposal.getFile())
        || ObjectUtils.isEmpty(fiveChecklistsEmergencyDisposal.getEmergencyMeasure())
        || ObjectUtils.isEmpty(fiveChecklistsEmergencyDisposal.getEventDisposal())
        ) {
            NotifyUtils.showErrorMsg("存在数据未填写！");
        }
        eruptDao.merge(fiveChecklistsEmergencyDisposal);
        return NotifyUtils.getSuccessNotify("保存成功！");
    }

    public FiveChecklistsEmergencyDisposal detail(Long id) {
        SendMessageController messageController = EruptSpringUtil.getBean(SendMessageController.class);
        FiveChecklistsEmergencyDisposal fiveChecklistsEmergencyDisposal = messageController.initValue(id);
        return fiveChecklistsEmergencyDisposal;
    }


    @RestController
    @Transactional
    public static class SendMessageController {
        @RequestMapping("erupt-api/data/FiveDetail/operator/mergencydisposal")
        @EruptRouter(verifyType = EruptRouter.VerifyType.LOGIN)
        public String recreate(@RequestBody EruptResultMap reuquest) {
            List<String> ids = reuquest.getAsList("ids", String.class);
            long id = Double.valueOf(ids.get(0)).longValue();
            EruptResultMap fiveDetail = EruptDaoUtils.selectOne("select * from tb_five_detail where id=" + id, EruptResultMap.class);
            FiveChecklistsEmergencyDisposal form = (FiveChecklistsEmergencyDisposal) reuquest.getAs("param", FiveChecklistsEmergencyDisposal.class);
            form.setId(fiveDetail.getLong("five_checklists_emergency_disposal_id"));
            List list = new ArrayList();
            list.add(form);
            return EruptSpringUtil.getBean(FiveChecklistsEmergencyDisposalHandler.class).exec(list, form, null);
        }

        @RequestMapping("/erupt-api/data/FiveChecklistsEmergencyDisposal/{id}")
        @EruptRouter(verifyType = EruptRouter.VerifyType.LOGIN)
        public FiveChecklistsEmergencyDisposal initValue(@PathVariable("id") Long id) {
            //FiveChecklistsFoundation fiveChecklistsFoundation = EruptDaoUtils.selectOne("select org_code from tb_fivechecklists_foundation where id =" + id, FiveChecklistsFoundation.class);
            EruptResultMap fiveDetail = EruptDaoUtils.selectOne("select * from tb_five_detail where id=" + id, EruptResultMap.class);
            FiveChecklistsEmergencyDisposal fiveChecklistsEmergencyDisposal1 = EruptDaoUtils.selectOne("select * from tb_fivechecklists_mergencydisposal where id=" + fiveDetail.getInt("five_checklists_emergency_disposal_id"), FiveChecklistsEmergencyDisposal.class);
            FiveChecklistsEmergencyDisposal fiveChecklistsEmergencyDisposal = new FiveChecklistsEmergencyDisposal();
            if (ObjectUtils.isNotEmpty(fiveChecklistsEmergencyDisposal1)) {
                fiveChecklistsEmergencyDisposal.setEmergencyPlan(fiveChecklistsEmergencyDisposal1.getEmergencyPlan());
                fiveChecklistsEmergencyDisposal.setEmergencyManagement(fiveChecklistsEmergencyDisposal1.getEmergencyManagement());
                fiveChecklistsEmergencyDisposal.setFile(fiveChecklistsEmergencyDisposal1.getFile());
                fiveChecklistsEmergencyDisposal.setEmergencyMeasure(fiveChecklistsEmergencyDisposal1.getEmergencyMeasure());
                fiveChecklistsEmergencyDisposal.setEventDisposal(fiveChecklistsEmergencyDisposal1.getEventDisposal());
                fiveChecklistsEmergencyDisposal.setOrgCode(fiveChecklistsEmergencyDisposal1.getOrgCode());
            }
            RemoteProxyService remoteProxyService = EruptSpringUtil.getBean(RemoteProxyService.class);

            // 专（兼）职应急队伍
            EruptApiModel eruptApiModel = EruptRemoteUtils.doGet(remoteProxyService.getMainServerUrl() + "/erupt-api/Rescueteam/select", EruptApiModel.class);
            if (ObjectUtils.isNotEmpty(eruptApiModel.getData())) {
                fiveChecklistsEmergencyDisposal.setEmergencyTeam(eruptApiModel.getData().toString());
            }

            //  应急物资
            EruptApiModel eruptApiModel1 = EruptRemoteUtils.doGet(remoteProxyService.getMainServerUrl() + "/erupt-api/MaterialReserve/select", EruptApiModel.class);
            if (ObjectUtils.isNotEmpty(eruptApiModel1.getData())) {
                List<String> list = new ArrayList<>();
                StringBuffer stringBufferName = new StringBuffer();
                StringBuffer stringBufferType = new StringBuffer();
                List<LinkedTreeMap> data = (List<LinkedTreeMap>) eruptApiModel1.getData();
                data.forEach(v -> {
                    stringBufferName.append(v.get("materialReserve"));
                    String materialType = String.valueOf(v.get("materialType"));
                    List<String> collect = Stream.of(materialType.split("\\|")).collect(Collectors.toList());
                    list.addAll(collect);
                });
                List<String> goodsType = list.stream().distinct().collect(Collectors.toList());
                String join = StringUtils.join(goodsType, ",");
                goodsType.forEach(v -> {
                });
                String string = "本公司共有应急物资储备点" + data.size() + "个，为：" + stringBufferName + "，物资类型为：" + join + "，联系人：" + data.get(0).get("contacts") + "，联系电话是：" + data.get(0).get("contactNumber") + "";
                fiveChecklistsEmergencyDisposal.setEmergencySupplies(string);
            } else {
                fiveChecklistsEmergencyDisposal.setEmergencySupplies("无");
            }

            // 应急演练
            EruptApiModel eruptApiModel2 = EruptRemoteUtils.doGet(remoteProxyService.getMainServerUrl() + "/erupt-api/Drill/select", EruptApiModel.class);
//            fiveChecklistsEmergencyDisposal.setEmergencyDrillNum("0");
//            fiveChecklistsEmergencyDisposal.setEmergencyDrillName("无");
            if (ObjectUtils.isNotEmpty(eruptApiModel2.getData())) {
                LinkedTreeMap data = (LinkedTreeMap) eruptApiModel2.getData();
                fiveChecklistsEmergencyDisposal.setEmergencyDrillNum(data.get("num") != null ? data.get("num").toString() : "0");
                fiveChecklistsEmergencyDisposal.setEmergencyDrillName(data.get("name") != null ? data.get("name").toString() : "无");
            }

            return fiveChecklistsEmergencyDisposal;
        }
    }

}
