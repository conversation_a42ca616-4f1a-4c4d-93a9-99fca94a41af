/*
 * Copyright © 2020-2035 yundingyun All rights reserved.
 */
package com.daliangang.device.proxy;

import cn.hutool.json.JSONObject;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.daliangang.core.DaliangangContext;
import com.daliangang.device.entity.PositionDocking;
import com.daliangang.device.entity.StorageTank;
import com.daliangang.device.form.DockingForm;
import com.daliangang.device.operation.PositionDockingHttpHandler;
import com.daliangang.majorisk.entity.ReserveReporting;
import com.google.gson.Gson;
import com.google.gson.internal.LinkedTreeMap;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import xyz.erupt.annotation.fun.DataProxy;
import xyz.erupt.common.http.EruptRemoteUtils;
import xyz.erupt.core.config.GsonFactory;
import xyz.erupt.core.util.EruptSpringUtil;
import xyz.erupt.jpa.dao.EruptDao;
import xyz.erupt.toolkit.remote.RemoteProxyService;
import xyz.erupt.toolkit.remote.RemoteQuery;
import xyz.erupt.toolkit.utils.EruptDaoUtils;
import xyz.erupt.toolkit.utils.NotifyUtils;
import xyz.erupt.upms.service.EruptPlatformService;

import javax.annotation.Resource;
import javax.transaction.Transactional;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.regex.Pattern;

@Service
@Slf4j
public class StorageTankDataProxy implements DataProxy<StorageTank> {
    @Resource
    private RemoteProxyService remoteProxyService;
    @Resource
    private PositionDockingHttpHandler positionDockingHttpHandler;

    @Resource
    private EruptDao eruptDao;

    @Resource
    private EruptPlatformService eruptPlatformService ;

    public static final String IS_PUSH = "IS_PUSH" ;

    @Override
    public void addBehavior(StorageTank storageTank) {
        if (ObjectUtils.isNotEmpty(storageTank.getCompany())) {
            List<LinkedTreeMap> enterprise = remoteProxyService.queryEntity("Enterprise", RemoteQuery.builder().condition("orgCode", storageTank.getCompany()));
            if (ObjectUtils.isNotEmpty(enterprise)) {
              //  Integer portArea1 = (int) Float.parseFloat((String) enterprise.get(0).get("portArea"));
             //   String portArea = String.valueOf(enterprise.get(0).get("portArea"));
                String portArea=new BigDecimal(enterprise.get(0).get("portArea").toString()).stripTrailingZeros().toPlainString();
                storageTank.setPortArea(portArea);
            }
        }
    }

    @SneakyThrows
    @Override
    public void beforeAdd(StorageTank storageTank) {
        if (ObjectUtils.isNotEmpty(storageTank.getPositionId())) {
            // 数据转换
            Gson gson = GsonFactory.getGson();
            Map map =new HashMap();
            JSONArray objects = JSON.parseArray(storageTank.getMap());
            // JSONObject jsonObject = JSON.parseObject(warehouse.getMap());
            map.put("map", objects);
            String json = JSON.toJSONString(map);
            DockingForm positionDockingForm = gson.fromJson(json, DockingForm.class);
            String listToJsonString = "";
            // 处理风控数据中经纬度数据
            // 判断是否为圆
            if (positionDockingForm.getMap().get(0).getRy() != 0.0) {
                Map mapY = new HashMap();
                mapY.put("type","circle");
                mapY.put("lng",positionDockingForm.getMap().get(0).getHt().get(0).getLng());
                mapY.put("lat",positionDockingForm.getMap().get(0).getHt().get(0).getLat());
                mapY.put("radius",positionDockingForm.getMap().get(0).getRy());
                listToJsonString = gson.toJson(mapY);
            } else {
                Map mapD = new HashMap();
                mapD.put("type","polygon");
                mapD.put("points",positionDockingForm.getMap().get(0).getHt());
                listToJsonString = gson.toJson(mapD);
            }
            storageTank.setMap(listToJsonString);
        }
        //时间格式转化
        //建成年份
        String yearBuilt = storageTank.getYearBuilt();
        if (StringUtils.isNotEmpty(yearBuilt)&&StringUtils.isNumeric(yearBuilt)){
                if(yearBuilt.length()==4){
                    if (ObjectUtils.isNotEmpty(storageTank.getPositionId())){
                        String time = getTime(yearBuilt);
                        storageTank.setYearBuilt(time);
                    }else{
                        storageTank.setYearBuilt(yearBuilt);
                    }
                }else{
                    String time = getTime(yearBuilt);
                    storageTank.setYearBuilt(time);
                }
        }
        else if(StringUtils.isNotEmpty(yearBuilt)&&yearBuilt.contains("/")){
            storageTank.setYearBuilt(yearBuilt.substring(0,yearBuilt.indexOf("/")));
        }
        //罐区和罐组转换
        //罐区名称
        String tankFarm = storageTank.getTankFarm();
        //罐组名称
        String tankGroup = storageTank.getTankGroup();
        //汉字格式
        Pattern p = Pattern.compile("[\u4e00-\u9fa5]");
        //罐区
        if(tankFarm!=null&&p.matcher(tankFarm).find()){
            String tankFarmId = DaliangangContext.getTankFarmId(tankFarm);
            storageTank.setTankFarm(tankFarmId);
        }
        //罐组
        if(tankGroup!=null&&p.matcher(tankGroup).find()){
            String tankGroupId = DaliangangContext.getTankGroupId(tankGroup);
            storageTank.setTankGroup(tankGroupId);
        }


    }

    @Transactional
    @Override
    public void afterAdd(StorageTank storageTank) {
        // 同步保存每日储量上报,判断是否为导入初始化数据

        ReserveReporting reserveReporting = EruptSpringUtil.getBean(ReserveReporting.class);
        reserveReporting.setCompany(storageTank.getCompany());
        reserveReporting.setOrgCode(storageTank.getCompany());
        reserveReporting.setNumber(String.valueOf(storageTank.getId()));
        eruptDao.merge(reserveReporting);

        String isPush = eruptPlatformService.getOption(StorageTankDataProxy.IS_PUSH).getAsString() ;
        if (isPush.equals("true")) {
            if (StringUtils.isNotEmpty(storageTank.getOrgCode()) && ObjectUtils.isEmpty(storageTank.getPositionId())) {
                // 推送系统x新增的数据
                String token = DaliangangContext.getToken();
                positionDockingHttpHandler.AddHttpDocking(storageTank, "1", storageTank.getOrgCode(),token);
                log.info("推送储罐数据成功 ->  token=" + token);
            }
        }


        // 保存导入的三方地点关系
        if (ObjectUtils.isNotEmpty(storageTank.getPositionId())) {
            // 删除中间表数据
            String sql1 = "delete from tb_position_docking where position_type = '1' and b_id = "+storageTank.getId();
            EruptDaoUtils.updateNoForeignKeyChecks(sql1);

            PositionDocking positionDocking = EruptSpringUtil.getBean(PositionDocking.class);
            positionDocking.setBId(storageTank.getId());
            positionDocking.setPositionId(storageTank.getPositionId());
            positionDocking.setPositionType("1");
            eruptDao.merge(positionDocking);
        }

        JSONObject inputData = new JSONObject();
        inputData.set("clazz", "StorageTank");
        inputData.set("insertData",storageTank);
        EruptRemoteUtils.doPost(remoteProxyService.getMainServerUrl() + "/erupt-api/exchange/data/exchange", inputData, Map.class);

    }

    @Override
    public void beforeUpdate(StorageTank storageTank) {
        //罐组名称
        String tankGroup = storageTank.getTankGroup();
        //汉字格式
        Pattern p = Pattern.compile("[\u4e00-\u9fa5]");
        //罐组
        if(tankGroup!=null&&p.matcher(tankGroup).find()){
            String tankGroupId = DaliangangContext.getTankGroupId(tankGroup);
            if(StringUtils.isEmpty(tankGroupId)){
                NotifyUtils.showWarnMsg("所属罐组不在所属罐区下，请重新选择所属罐组");
            }
            storageTank.setTankGroup(tankGroupId.substring(0,tankGroupId.indexOf(".")));
        }
    }

    @Override
    public void afterUpdate(StorageTank storageTank) {

        String isPush = eruptPlatformService.getOption(StorageTankDataProxy.IS_PUSH).getAsString() ;
        if (isPush.equals("true")) {
            if (StringUtils.isNotEmpty(storageTank.getOrgCode())) {
                String token = DaliangangContext.getToken();
                positionDockingHttpHandler.updateHttpDocking(storageTank, "1", storageTank.getOrgCode(), storageTank.getId(), token);
                log.info("推送修改储罐数据成功 ->  token=" + token);
            }
        }
        JSONObject inputData = new JSONObject();
        inputData.set("clazz", "StorageTank");
        inputData.set("insertData",storageTank);
        EruptRemoteUtils.doPost(remoteProxyService.getMainServerUrl() + "/erupt-api/exchange/data/exchange", inputData, Map.class);
    }

    @Transactional
    @Override
    public void afterDelete(StorageTank storageTank) {
        // 同步保存每日储量上报
     //   ReserveReporting positionDocking = EruptSpringUtil.getBean(ReserveReporting.class);
        ReserveReporting reserveReporting = EruptDaoUtils.selectOne("select * from tb_reserve_reporting where number = " + storageTank.getId(), ReserveReporting.class);
        eruptDao.getEntityManager().remove(eruptDao.getEntityManager().merge(reserveReporting));
        String token = DaliangangContext.getToken();
        positionDockingHttpHandler.deleteHttpDocking(storageTank, "1", storageTank.getOrgCode(), storageTank.getId(), token);
    }

    //判断并转换时间格式
    public static String getTime(String ditNumber) {
//        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy/MM/dd HH:mm:ss");
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy");
        //如果是数字 小于0则 返回
        BigDecimal bd = new BigDecimal(ditNumber);
        int days = bd.intValue();//天数
        int mills = (int) Math.round(bd.subtract(new BigDecimal(days)).doubleValue() * 24 * 3600);
        //获取时间
        Calendar c = Calendar.getInstance();
        c.set(1900, 0, 1);
        c.add(Calendar.DATE, days - 2);
        int hour = mills / 3600;
        int minute = (mills - hour * 3600) / 60;
        int second = mills - hour * 3600 - minute * 60;
        c.set(Calendar.HOUR_OF_DAY, hour);
        c.set(Calendar.MINUTE, minute);
        c.set(Calendar.SECOND, second);
        return dateFormat.format(c.getTime());
    }


    @Override
    public void excelImport(Object workbook) {
        String sql1 = "truncate table tb_reserve_reporting";
        EruptDaoUtils.updateNoForeignKeyChecks(sql1);
    }

}
