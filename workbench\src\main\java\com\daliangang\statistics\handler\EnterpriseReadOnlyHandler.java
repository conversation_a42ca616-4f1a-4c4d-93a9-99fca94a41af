package com.daliangang.statistics.handler;


import com.daliangang.core.DaliangangContext;
import org.springframework.stereotype.Service;
import xyz.erupt.annotation.sub_field.Readonly;

@Service
public class EnterpriseReadOnlyHandler implements Readonly.ReadonlyHandler {
    @Override
    public boolean add(boolean add, String[] params) {
        //判断当前用户是否为企业用户
        if(DaliangangContext.isEnterpriseUser() || DaliangangContext.isEnterpriseEmployee()){
            return true;
        }
        return false;
    }

    @Override
    public boolean edit(boolean edit, String[] params) {
        if(DaliangangContext.isEnterpriseUser() || DaliangangContext.isEnterpriseEmployee()){
            return true;
        }
        return false;
    }
}
