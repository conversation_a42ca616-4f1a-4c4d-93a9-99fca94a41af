package com.daliangang.workbench.controller;

import com.daliangang.emergency.form.EmergencyDutyPersonForm;
import com.daliangang.workbench.form.ToDoForm;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import xyz.erupt.core.annotation.EruptRouter;
import xyz.erupt.core.module.MetaUserinfo;
import xyz.erupt.core.view.EruptApiModel;
import xyz.erupt.toolkit.remote.RemoteProxyService;
import xyz.erupt.toolkit.utils.EruptDaoUtils;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @since :2023/6/7:18:02
 */
@RestController
public class ToDoController {
    @Resource
    private RemoteProxyService remoteProxyService;

    @RequestMapping("erupt-api/get/ScheduleManagement/selectToDo")
    @EruptRouter(verifyType = EruptRouter.VerifyType.LOGIN)
    public EruptApiModel scheduleManagement(String date) {
        MetaUserinfo remoteUserInfo = remoteProxyService.getRemoteUserInfo();
        List<ToDoForm> toDoForms = EruptDaoUtils.selectOnes(" select * from e_mns_todo where owner  = '"+remoteUserInfo.getUsername()+"' and date_format(time, '%Y-%m' ) = '"+date+"'", ToDoForm.class);
        return EruptApiModel.successApi(toDoForms);
    }

}
