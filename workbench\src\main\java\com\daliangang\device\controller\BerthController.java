package com.daliangang.device.controller;

import com.daliangang.device.form.NumForms;
import com.daliangang.device.sql.BerthSql;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import xyz.erupt.core.annotation.EruptRouter;
import xyz.erupt.core.module.MetaUserinfo;
import xyz.erupt.core.view.EruptApiModel;
import xyz.erupt.toolkit.remote.RemoteProxyService;
import xyz.erupt.toolkit.utils.EruptDaoUtils;
import xyz.erupt.toolkit.utils.SqlUtils;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @since :2023/4/24:14:14
 */
@RestController
public class BerthController {
    @Resource
    private RemoteProxyService remoteProxyService;
    @Resource
    private BerthSql berthSql;

    //统计设施数
    @RequestMapping("erupt-api/get/selectBerthNum")
    @EruptRouter(verifyType = EruptRouter.VerifyType.LOGIN)
    public EruptApiModel selectBerthNum() {
        MetaUserinfo remoteUserInfo = remoteProxyService.getRemoteUserInfo();
        String orgCode = SqlUtils.wrapIn(remoteUserInfo.getAuth());
        String sql = berthSql.selectBerthNum(orgCode);
        List<NumForms> employeeCertificateForm = EruptDaoUtils.selectOnes(sql, NumForms.class);
        return EruptApiModel.successApi(employeeCertificateForm);
    }



    // 第三个统计
//    @RequestMapping("erupt-api/get/selectNumThree")
//    @EruptRouter(verifyType = EruptRouter.VerifyType.LOGIN)
//    public EruptApiModel selectNumThree() {
//        MetaUserinfo remoteUserInfo = remoteProxyService.getRemoteUserInfo();
//       // String orgCode = SqlUtils.wrapIn(remoteUserInfo.getAuth());
//        String sql = berthSql.selectNumThree(remoteUserInfo.getOrg());
//        List<NumForms> employeeCertificateForm = EruptDaoUtils.selectOnes(sql, NumForms.class);
//        return EruptApiModel.successApi(employeeCertificateForm);
//    }
}
