package com.daliangang.emergency.config;

import com.daliangang.emergency.entity.*;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.annotation.Order;
import xyz.erupt.core.constant.MenuTypeEnum;
import xyz.erupt.core.module.EruptModule;
import xyz.erupt.core.module.EruptModuleInvoke;
import xyz.erupt.core.module.MetaMenu;
import xyz.erupt.core.module.ModuleInfo;
import xyz.erupt.core.util.EruptSpringUtil;
import xyz.erupt.upms.service.EruptMenuService;
import xyz.erupt.upms.util.EruptMenuUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * @Author: zengwensi
 * @Description:
 * @Date: Created in 2023/3/17 15:18
 * @Modified By
 */
@Configuration
@Order
public class EmergencyConfiguration implements EruptModule, ApplicationRunner {
    static {
        EruptModuleInvoke.addEruptModule(EmergencyConfiguration.class);
    }

    @Override
    public ModuleInfo info() {
        return ModuleInfo.builder().name("daliangang-emergency").build();
    }

    @Override
    public List<MetaMenu> initMenus() {
        EruptMenuService eruptMenuService = EruptSpringUtil.getBean(EruptMenuService.class);
        String moduleId = "emergency";
        eruptMenuService.registerModule(moduleId, "危险货物重大突发事件智慧管理系统", 7);
        AtomicInteger rootSort = new AtomicInteger(700);
        AtomicInteger sort = new AtomicInteger();
        List<MetaMenu> menus = new ArrayList<>();

        menus.add(EruptMenuUtils.createMenu(CaseBase.class, null, sort, MenuTypeEnum.TABLE, "案例库管理", "CaseBase", moduleId, "fa fa-bookmark"));//案例库管理
//        menus.add(EruptMenuUtils.createMenu(Drill.class, null, sort, MenuTypeEnum.TABLE, "应急演练管理", "Drill", moduleId, "fa fa-bell"));//演练管理
        menus.add(EruptMenuUtils.createMenu(EmergencyDuty.class, null, sort, MenuTypeEnum.TABLE, "应急值守管理", "EmergencyDuty", moduleId, "fa fa-empire"));//应急值守管理

        //应急知识库管理
        menus.add(EruptMenuUtils.createMenu(EmergencyWiki.class, null, sort, MenuTypeEnum.TABLE, "应急知识库", "EmergencyWiki", moduleId, "fa fa-book"));

        //应急资源管理
        MetaMenu sourceMan = EruptMenuUtils.createRootMenu(null, "$dlg-sourceMan", "应急资源管理", "fa fa-plus-square", rootSort, moduleId);
        menus.add(sourceMan);
        menus.add(EruptMenuUtils.createMenu(EmergencyStatistics.class, sourceMan, sort, MenuTypeEnum.TABLE, "应急资源统计", "EmergencyStatistics", moduleId, null));//应急资源统计
        menus.add(EruptMenuUtils.createMenu(Rescueteam.class, sourceMan, sort, MenuTypeEnum.TABLE, "应急救援队伍管理", "Rescueteam", moduleId, null));//应急救援队伍管理
        menus.add(EruptMenuUtils.createMenu(Sensitivetargets.class, sourceMan, sort, MenuTypeEnum.TABLE, "敏感目标管理", "Sensitivetargets", moduleId, null)); //敏感目标管理
        menus.add(EruptMenuUtils.createMenu(EmergencyMuster.class, sourceMan, sort, MenuTypeEnum.TABLE, "应急集合点管理", "EmergencyMuster", moduleId, null));//应急集合点管理
        menus.add(EruptMenuUtils.createMenu(Hospital.class, sourceMan, sort, MenuTypeEnum.TABLE, "医院管理", "Hospital", moduleId, null));//医院管理
        menus.add(EruptMenuUtils.createMenu(EmergencyPool.class, sourceMan, sort, MenuTypeEnum.TABLE, "事故应急池管理", "EmergencyPool", moduleId, null));//事故应急池管理
        menus.add(EruptMenuUtils.createMenu(MaterialReserve.class, sourceMan, sort, MenuTypeEnum.TABLE, "应急物资储备点管理", "MaterialReserve", moduleId, null));//应急物资储备点管理
        menus.add(EruptMenuUtils.createMenu(EmergencyExpert.class, sourceMan, sort, MenuTypeEnum.TABLE, "应急专家管理", "EmergencyExpert", moduleId, null));//应急专家管理


        MetaMenu accident = EruptMenuUtils.createRootMenu(null, "$accident", "事故救援辅助", "fa fa-delicious", rootSort, moduleId);
        menus.add(accident);
        menus.add(MetaMenu.createSimpleMenu("RescueMap", "事故救援一张图", "/datav-accident/index.html", accident, rootSort.getAndIncrement(), MenuTypeEnum.NEW_WINDOW.getCode()).module(moduleId));
        menus.add(EruptMenuUtils.createMenu(AccidentInformation.class, accident, sort, MenuTypeEnum.TABLE, "事故记录", "AccidentInformation", moduleId, null));//应急救援评估
        menus.add(EruptMenuUtils.createMenu(EmergencyInformation.class, accident, sort, MenuTypeEnum.TABLE, "应急信息管理", "EmergencyInformation", moduleId, null));//评估等级管理

        menus.add(EruptMenuUtils.createMenu(Rescueassessment.class, null, sort, MenuTypeEnum.TABLE, "救援评估管理", "Rescueassessment", moduleId, null));//应急救援评估
        //应急救援能力评估
        MetaMenu abilityMan = EruptMenuUtils.createRootMenu(null, "$dlg-abilityMan", "应急能力评估", "fa fa-exclamation-circle text-blue", rootSort, moduleId);
        menus.add(abilityMan);
        //menus.add(EruptMenuUtils.createMenu(PrincipalElement.class, abilityMan, sort, MenuTypeEnum.TABLE, "主要素管理", "PrincipalElement", moduleId, null));//主要素管理
        menus.add(EruptMenuUtils.createMenu(Evaluation.class, abilityMan, sort, MenuTypeEnum.TABLE, "评估要素管理", "Evaluation", moduleId, null));//评估表管理
        menus.add(EruptMenuUtils.createMenu(EvaluationGrade.class, abilityMan, sort, MenuTypeEnum.TABLE, "评估等级管理", "EvaluationGrade", moduleId, null));//评估等级管理
        menus.add(EruptMenuUtils.createMenu(EvaluationManage.class, abilityMan, sort, MenuTypeEnum.TABLE, "评估管理", "EvaluationManage", moduleId, null));//评估管理
        menus.add(EruptMenuUtils.createMenu(EvaluationForm.class, abilityMan, sort, MenuTypeEnum.TREE, "评估表管理", "EvaluationForm", moduleId, null));//评估管理
        menus.add(MetaMenu.createSimpleMenu("9wIbxCTS", "评估统计", "9wIbxCTS", abilityMan, rootSort.getAndIncrement(), "bi").icon("fa fa-codepen").module(moduleId));

        return menus;
    }

    @Override
    public void run(ApplicationArguments args) throws Exception {
        //EruptSpringUtil.getBean(EruptExcelController.class).importExcel(Evaluation.class, "public/init/05-评估表管理.xls");
        //EruptSpringUtil.getBean(EruptExcelController.class).importExcel(EmergencyWiki.class, "public/init/06-应急知识库.xls");
    }
}
