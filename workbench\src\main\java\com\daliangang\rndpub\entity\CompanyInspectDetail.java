package com.daliangang.rndpub.entity;

import com.daliangang.rndpub.operation.InspectionResultsViewDetailHandler;
import com.daliangang.rndpub.proxy.CompanyInspectDetailDataProxy;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import xyz.erupt.annotation.Erupt;
import xyz.erupt.annotation.EruptField;
import xyz.erupt.annotation.sub_erupt.*;
import xyz.erupt.annotation.sub_field.*;
import xyz.erupt.annotation.sub_field.sub_edit.*;
import xyz.erupt.toolkit.db.Comment;
import xyz.erupt.toolkit.handler.EruptDictCodeChoiceFetchHandler;
import xyz.erupt.toolkit.handler.RemoteCallChoiceFetchHandler;
import xyz.erupt.toolkit.handler.RemoteEntityChoiceFetchHandler;
import xyz.erupt.toolkit.handler.SqlChoiceFetchHandler;
import xyz.erupt.upms.handler.GotoPageHandler;
import xyz.erupt.upms.model.auth.DataAuthHandler;
import xyz.erupt.upms.model.auth.DataAuthModel;

import javax.persistence.Entity;
import javax.persistence.Lob;
import javax.persistence.Table;
import javax.persistence.Transient;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2023/9/25
 */
@Erupt(
        name = "企业检查详情",
        power = @Power(
                add = false,
                edit = false,
                delete = false,
                viewDetails = false
        ),
        tree = @Tree(id = "id", label = "name", pid = "parent.id"),
        dataProxy = CompanyInspectDetailDataProxy.class,
        drills = {
//                @Drill(
//                        title = "整改内容", icon = "fa fa-list-alt",
//                        link = @Link(
//                                linkErupt = Rectify.class, joinColumn = "inspectionResultsId"
//                        )
//                )
        },
        rowOperation = {
                @RowOperation(
                        icon = "fa fa-search-plus",
                        title = "详情",
                        type = RowOperation.Type.TPL,
                        tplHeight = "600px",
                        tpl = @Tpl(path = "tpl/dlg-check.tpl",tplHandler = InspectionResultsViewDetailHandler.class),
//                    code = TplUtils.REMOTE_ERUPT_CODE + "export",

                        mode = RowOperation.Mode.SINGLE
                ),
                @RowOperation(title = "返回", icon = "fa fa-mail-reply",
                        operationParam="CompanyInspect",
                        confirm=false,
                        operationHandler = GotoPageHandler.class,
                        mode = RowOperation.Mode.BUTTON),
        }
)
@Table(name = "tb_inspection_results")
@Entity
@Getter
@Setter
@Comment("企业检查详情")
@ApiModel("企业检查详情")
public class CompanyInspectDetail extends DataAuthModel {

    @EruptField(
            views = @View(title = "整改状态"),
            edit = @Edit(title = "整改状态", type = EditType.CHOICE, notNull = false, show = false,search = @Search,
                    choiceType = @ChoiceType(fetchHandler = EruptDictCodeChoiceFetchHandler.class, fetchHandlerParams = "rectificationStatus")))
    @Comment("整改状态")
    @ApiModelProperty("整改状态")
    private String rectificationStatus;

    @EruptField(
            views = @View(title = "检查名称",show = false),
            edit = @Edit(title = "检查名称", type = EditType.CHOICE,
                    choiceType = @ChoiceType(anewFetch = true ,fetchHandler = RemoteCallChoiceFetchHandler.class, fetchHandlerParams ={"main","erupt-api/get/getInspectionName"})
            ))
    @Comment("检查名称")
    @ApiModelProperty("检查名称")
    private String inspectionName;

    @EruptField(
            views = @View(title = "检查对象",show = false),
            edit = @Edit(title = "检查对象", type = EditType.CHOICE, notNull = false, readonly = @Readonly(exprHandler = DataAuthHandler.class),
                    show = false,
                    search = @Search,
                    choiceType = @ChoiceType(fullSpan = true,reload = true,fetchHandler = RemoteEntityChoiceFetchHandler.class, fetchHandlerParams = {"main", "Enterprise", "org_code,name"})))
    @Transient
    private String company;

    @EruptField(
            views = @View(title = "检查对象"),
            edit = @Edit(title = "检查对象", type = EditType.REFERENCE_TREE, notNull = true,
                    referenceTreeType = @ReferenceTreeType(type = ReferenceTreeType.Type.CHOICE, dependField = "inspectionName", dependColumn = "procedure_id"),
                    choiceType = @ChoiceType(fetchHandler = SqlChoiceFetchHandler.class,
                            fetchHandlerParams = {"select org_code,enterprise_name from tb_enterprise_information", "5000", "and state=1"})))
    @Comment("检查对象")
    @ApiModelProperty("检查对象")
    private String checkObject;

    @EruptField(
            views = @View(title = "检查事项"),
            edit = @Edit(title = "检查事项", type = EditType.REFERENCE_TREE, notNull = true,
                    referenceTreeType = @ReferenceTreeType(type = ReferenceTreeType.Type.CHOICE,dependField = "",dependColumn = ""),
                    choiceType = @ChoiceType(fetchHandler = SqlChoiceFetchHandler.class,
                            fetchHandlerParams = {"select distinct inspection_items from tb_inspection_items_management", "5000", "and 1=1"})))
    @Comment("检查事项")
    @ApiModelProperty("检查事项")
    private String inspectionItems;

    @EruptField(
            views = @View(title = "问题描述"),
            edit = @Edit(title = "问题描述", type = EditType.TEXTAREA,  notNull = true))
    @Comment("问题描述")
    @ApiModelProperty("问题描述")
    private @Lob
    String problemDescription;


    @EruptField(
            views = @View(title = "检查依据"),
            edit = @Edit(title = "检查依据", type = EditType.TEXTAREA, notNull = true,
                    inputType = @InputType))
    @Comment("检查依据")
    @ApiModelProperty("检查依据")
    @Lob
    private String inspectionBasis;

    @EruptField(
            views = @View(title = "整改建议"),
            edit = @Edit(title = "整改建议", type = EditType.TEXTAREA, notNull = true,
                    inputType = @InputType))
    @Comment("整改建议")
    @ApiModelProperty("整改建议")
    @Lob
    private String proposal;

    @EruptField(
            views = @View(title = "整改截止时间（年月日）"),
            edit = @Edit(title = "整改截止时间（年月日）", type = EditType.DATE,notNull = true,
                    dateType = @DateType))
    @Comment("整改截止时间（年月日）")
    @ApiModelProperty("整改截止时间（年月日）")
    private Date deadline;

    @EruptField(
            views = @View(title = "是否逾期", show = true),
            edit = @Edit(title = "是否逾期", type = EditType.BOOLEAN, notNull = false, show = false,search = @Search(vague = true),
                    boolType = @BoolType(trueText = "未逾期", falseText = "已逾期")))
    @Comment("是否逾期")
    @ApiModelProperty("是否逾期")
    private Boolean beOverdue;


    //检查年度
    @EruptField(
            views = @View(title = "年度",show = false),
            edit = @Edit(
                    title = "年度",
                    type=EditType.DATE,
                    search = @Search(),
                    dateType = @DateType(type = DateType.Type.YEAR)
            )
    )
    private String inspectYear;
}
