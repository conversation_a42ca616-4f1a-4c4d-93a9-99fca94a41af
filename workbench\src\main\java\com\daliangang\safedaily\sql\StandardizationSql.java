package com.daliangang.safedaily.sql;

import org.springframework.stereotype.Repository;

/**
 * <AUTHOR>
 * @since :2023/4/23:16:27
 */
@Repository
public class StandardizationSql {

    public String selectStandardizationNum (String orgCode) {

        String sql = "select COUNT(*) as num,grade as type  from tb_standardization  WHERE submitted = 1 and CURRENT_DATE() <= effective_date ";
        sql += "and org_code "+orgCode+"";
        sql += "GROUP by grade";

        return sql;
    }
}
