package com.daliangang.training.entity;

import lombok.*;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Date 2023/5/19
 * @Description:
 */

@Getter
@Setter
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class PostSyncEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    //岗位id
    private Long id;

    //岗位编码
    private String code;

    //岗位名称
    private String name;

    //岗位权重
    private Integer weight;

    //保留标识
    private Boolean reserved;

    //所属组织
    private String orgCode;

    //删除标识
    private Boolean delFlag;

    //更新/新增标识
    private Boolean updateFlag;
}
