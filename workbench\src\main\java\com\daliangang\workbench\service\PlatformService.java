package com.daliangang.workbench.service;

import com.daliangang.training.models.OrganSyncEntity;
import com.daliangang.training.models.PostSyncEntity;
import com.daliangang.training.models.UserSyncEntity;
import com.daliangang.workbench.entity.EmployeeInformation;
import com.daliangang.workbench.entity.EruptRoleTemplatePost;
import com.daliangang.workbench.init.InitConst;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import xyz.erupt.jpa.dao.EruptDao;
import xyz.erupt.toolkit.redismq.RedisMQConst;
import xyz.erupt.toolkit.redismq.RedisMQService;
import xyz.erupt.toolkit.utils.AssertUtils;
import xyz.erupt.toolkit.utils.EruptDaoUtils;
import xyz.erupt.toolkit.utils.NotifyUtils;
import xyz.erupt.toolkit.utils.SqlUtils;
import xyz.erupt.upms.model.*;
import xyz.erupt.upms.model.template.*;
import xyz.erupt.upms.service.EruptPlatformService;
import xyz.erupt.upms.service.EruptRoleService;
import xyz.erupt.upms.service.EruptUserService;
import xyz.erupt.upms.util.EruptMenuUtils;
import xyz.erupt.upms.util.MetaUtil;

import javax.annotation.Resource;
import javax.transaction.Transactional;
import java.util.List;
import java.util.Objects;
import java.util.StringTokenizer;
import java.util.stream.Collectors;

/**
 * @Author：chb
 * @Package：com.daliangang.workbench.service
 * @Project：erupt
 * @name：PlatformService
 * @Date：2023/3/5 20:31
 * @Filename：PlatformService
 */
@Service
@Slf4j
public class PlatformService {

    @Resource
    private EruptRoleTemplateUserProxy eruptRoleTemplateUserProxy;

    @Resource
    private EruptRoleTemplateUserService eruptRoleTemplateUserService;

    @Resource
    private RedisMQService redisMQService;

    @Resource
    private EruptDao eruptDao;

    @Resource
    private EruptRoleService eruptRoleService;
    @Resource
    private EruptUserService eruptUserService;

    @Transactional
    public void afterAdd(String account, String name, String initOrgCode,String phone,
                         boolean createSelfOrg, EruptOrg org,
                         EruptRoleTemplate roleTemplate, String pwd, String homepage, Boolean init) {
        EruptOrg subOrg = null;
        //同步创建所在组织的一个下级组织
        if (createSelfOrg) {
            subOrg = new EruptOrg();
            subOrg.setName(name);
            subOrg.setParentOrg(org);
            subOrg.setCode(initOrgCode);
            if (StringUtils.isEmpty(initOrgCode))
                subOrg.randomOrgCode();
            EruptOrg retOrg = eruptDao.merge(subOrg);
            subOrg.setId(retOrg.getId());
        } else {
            subOrg = org;
        }

        //同步创建所在组织下的内建岗位
        List<EruptPost> posts = eruptDao.queryEntityList(EruptPost.class);
        for (EruptPost post_ : posts) {
            EruptRoleTemplatePost post = new EruptRoleTemplatePost();
            post.setCode(subOrg.getCode() + "." + post_.getCode());
            post.setName(post_.getName());
            post.setExclusive(post_.isExclusive());
            post.setReserved(true);//同步过来是系统保留记录
            post.setOrgCode(subOrg.getCode());
            post.setWeight(post_.getWeight());
            post.setType(post_.getType());
            MetaUtil.prepareMetaInfo(post, true, true);
            eruptDao.persist(post);
        }

        //创建一个基准部门
        EruptDeptTemplate dept = new EruptDeptTemplate();
        dept.setOrgCode(subOrg.getCode());
        dept.setName(name);
        dept.setSort(0);
        dept.randomDeptCode();
        dept.setParentDept(null);
        MetaUtil.prepareMetaInfo(dept, true, true);
        eruptDao.persist(dept);

        //同步创建平台用户
        roleTemplate = roleTemplate.checkSelf();
        EruptRoleTemplateUser rtUser = new EruptRoleTemplateUser();
        rtUser.setAccount(account);
        rtUser.setName(name);
        rtUser.setEruptOrg(subOrg);
        rtUser.setTemplate(roleTemplate);
        rtUser.setPassword(pwd);
        eruptRoleTemplateUserProxy.beforeAdd(rtUser);
        EruptRoleTemplateUser ret = eruptDao.merge(rtUser);
        rtUser.setId(ret.getId());
        MetaUtil.prepareMetaInfo(rtUser, true, true);
        EruptUser eruptUser = eruptRoleTemplateUserProxy.createEruptUser(rtUser, "workbench", EruptMenuUtils.getMenu(homepage));
        //更新电话号码
        this.updatePhone(eruptUser.getAccount(),phone,false);
        //仅仅在非初始化的情况下 触发生产队列  目前系统中逻辑 仅仅 允许 新增企业时增加用户 没有在新增区县 和 市的时候 增加 这里统一设置orgType为2
        if (!init) {
            //推送 组织 岗位 用户
            //todo 同步新增企业机构
            OrganSyncEntity organSync = OrganSyncEntity.builder()
                    .orgId(subOrg.getId())
                    .orgName(subOrg.getName())
                    .orgType(2) //0-1 市/区县 2 企业
                    .parentOrgCode(Objects.isNull(subOrg.getParentOrg()) ? null : subOrg.getParentOrg().getCode())
                    .orgCode(subOrg.getCode())
                    .updateFlag(false)
                    .delFlag(false)
                    .build();
            redisMQService.produce(RedisMQConst.DEFAULT_TOPIC + "_ORGAN", organSync);

            String sql = " org_code = '" + subOrg.getCode() + "'";
            List<EruptRoleTemplatePost> eruptRoleTemplatePosts = eruptDao.queryEntityList(EruptRoleTemplatePost.class, sql);
            //同步新增岗位信息
            for (EruptRoleTemplatePost post : eruptRoleTemplatePosts) {
                PostSyncEntity postSync = PostSyncEntity.builder()
                        .code(post.getCode())
                        .orgCode(post.getOrgCode())
                        .id(post.getId())
                        .name(post.getName())
                        .weight(post.getWeight())
                        .reserved(post.getReserved())
                        .delFlag(false)
                        .updateFlag(true)
                        .type(post.getType())
                        .build();
                redisMQService.produce(RedisMQConst.DEFAULT_TOPIC + "_POST", postSync);
            }
            //同步企业管理员
            UserSyncEntity userSync = UserSyncEntity.builder()
                    .id(eruptUser.getId())
                    .name(eruptUser.getName())
                    .orgCode(ret.getOrgCode())
                    .templateId(ret.getTemplate().getId())
                    .updateFlag(false)
                    .delFlag(false)
                    .build();

            redisMQService.produce(RedisMQConst.DEFAULT_TOPIC + "_USER", userSync);

        }


    }

    //给员工设置共享的角色
    //这里本意是为了给共享类角色（如移动端用户等）在创建时会自动加入进去，由于权限太大且共享类角色界定不清，目前关闭
    //功能性共享类角色注册改用接口注入
    @Transactional
    public void setShareRoles(EmployeeInformation employee, EruptUser eruptUser) {
        if (StringUtils.isEmpty(employee.getOwnerRole())) return;
        //只有非隔离角色才能共享
        String nameSql = "select * from e_upms_role where name in(";
        StringTokenizer st = new StringTokenizer(employee.getOwnerRole(), "|");
        while (st.hasMoreTokens()) {
            nameSql += SqlUtils.wrapStr(st.nextToken().trim()) + ",";
        }
        nameSql = nameSql.substring(0, nameSql.length() - 1) + ")";
        List<EruptRole> roles = EruptDaoUtils.selectOnes(nameSql, EruptRole.class);
        if (roles.isEmpty()) return;
        //如果原来有的角色，但是现在不选了，要去掉
        List<EruptRole> needRemoves = eruptUser.getRoles().stream().filter(
                role -> role.isShare() && !roles.contains(role)).collect(Collectors.toList());
        needRemoves.forEach(role -> eruptUser.getRoles().remove(role));
        //现在现的角色，原来没有，要加上
        roles.forEach(role -> {
            if (!eruptUser.getRoles().contains(role)) eruptUser.getRoles().add(role);
        });
        eruptDao.merge(eruptUser);
    }


    @Transactional
    public EruptUser afterAdd(EmployeeInformation employee, EruptRoleTemplate roleTemplate, String... shareRoles) {
        EruptOrg eruptOrg = EruptDaoUtils.selectOne("select * from e_upms_org where code = '" + employee.getOrgCode() + "'", EruptOrg.class);
        //EruptRoleTemplate roleTemplate = EruptDaoUtils.selectOne("select * from e_upms_role_template where id = 4", EruptRoleTemplate.class);
        //同步创建平台用户
        //  roleTemplate = roleTemplate.checkSelf();
        EruptRoleTemplateUser rtUser = new EruptRoleTemplateUser();
        rtUser.setAccount(employee.getPhone());
        rtUser.setName(employee.getName());
        rtUser.setEruptOrg(eruptOrg);
        rtUser.setTemplate(roleTemplate);
        rtUser.setPassword(EruptPlatformService.getInitPassWord());
        eruptRoleTemplateUserProxy.beforeAdd(rtUser);
        EruptRoleTemplateUser ret = eruptDao.merge(rtUser);
        rtUser.setId(ret.getId());
        MetaUtil.prepareMetaInfo(rtUser, true, true);
        EruptUser eruptUser = eruptRoleTemplateUserProxy.createEruptUser(rtUser, "workbench", EruptMenuUtils.getMenu(InitConst.HOMEPAGE_EMPLOYEE), shareRoles);

        //如果有共享角色，加进去,
//        if (shareRoles != null) {
//            for (String roleCode : shareRoles) {
//                eruptUser = eruptRoleService.addShareRole(eruptUser, roleCode);
//            }
//        }
//        if(null != eruptUser.getRoles()){
//            for(EruptRole role : eruptUser.getRoles()){
//                this.eruptDao.merge(role) ;
//            }
//        }
        EruptDeptTemplate eruptDeptTemplate = eruptDao.queryEntity(EruptDeptTemplate.class, " id = '" + employee.getDepartment().getId() + "'");
        UserSyncEntity userSync = UserSyncEntity.builder()
                .id(eruptUser.getId())
                .name(employee.getName())
                .orgCode(employee.getOrgCode())
                .phone(employee.getPhone())
                .sex(employee.getSex())
                .state(employee.getState())
                .posts(employee.getJobTitle())
                .manager(employee.getManager())
                .department(eruptDeptTemplate.getName())
                .code(eruptDeptTemplate.getCode())
                .templateId(4L)
                .updateFlag(false)
                .delFlag(false)
                .build();
        redisMQService.produce(RedisMQConst.DEFAULT_TOPIC + "_USER", userSync);

//        if (dispatch) {
//            UPMSUtil.getUserDispatchers().forEach(dispatcher -> {
//                dispatcher.register(EruptDaoUtils.castMap(eruptUser));
//            });
//        }

        return eruptUser;
    }

    @Transactional
    public void afterUpdate(String account, String newName,String phone, EruptOrg newOrg, EruptRoleTemplate newRoleTemplate) {
        EruptRoleTemplateUser rtUser = eruptRoleTemplateUserService.queryByAccount(account);
        AssertUtils.notNull(rtUser, "目标用户不存在[" + account + "]");
        rtUser.setName(newName);
        if (newOrg != null) rtUser.setEruptOrg(newOrg);
        if (newRoleTemplate != null) rtUser.setTemplate(newRoleTemplate);
        eruptRoleTemplateUserProxy.beforeUpdate(rtUser);
        EruptRoleTemplateUser ret = eruptDao.merge(rtUser);
        rtUser.setId(ret.getId());
        MetaUtil.prepareMetaInfo(rtUser, false, true);
        eruptRoleTemplateUserProxy.afterUpdate(rtUser);
        //修改电话号码
        this.updatePhone(account,phone,false);

    }

    @Transactional
    public void afterDelete(String account) {
        EruptRoleTemplateUser rtUser = eruptRoleTemplateUserService.queryByAccount(account);
        AssertUtils.notNull(rtUser, "目标用户不存在[" + account + "]");
        eruptRoleTemplateUserProxy.beforeDelete(rtUser);
        //eruptDao.delete(rtUser);
        String sql = "delete from e_upms_role_template_user where id=" + rtUser.getId();
        EruptDaoUtils.updateNoForeignKeyChecks(sql);
        eruptRoleTemplateUserProxy.afterDelete(rtUser);
    }

    @Transactional
    public void updatePhone(String account,String phone,boolean isChangeAccount){
        //判断该手机号码是否存在
        List<EruptUser> existPhoneUser = eruptDao.queryEntityList(EruptUser.class, " phone = '" + phone + "' and status = 1 and account !='" + account + "' ");
        if(null != existPhoneUser && existPhoneUser.size() !=0){
            //其他账号已经存在该手机号
            NotifyUtils.showWarnMsg("该手机号码已被使用，请换一个手机号");
        }
        //修改电话号码
        EruptUser empUser = eruptUserService.findEruptUserByAccount(account);
        empUser.setPhone(phone);
        if(isChangeAccount){
            empUser.setAccount(phone);
        }
        eruptDao.mergeAndFlush(empUser);
        eruptUserService.flushEruptUserCache(empUser);
    }
}
