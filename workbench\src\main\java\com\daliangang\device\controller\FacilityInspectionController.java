package com.daliangang.device.controller;

import com.daliangang.device.form.NumForms;
import com.daliangang.emergency.form.EmergencyTeamForm;
import com.daliangang.majorisk.form.CurrentJobForm;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import xyz.erupt.core.annotation.EruptRouter;
import xyz.erupt.core.module.MetaUserinfo;
import xyz.erupt.core.view.EruptApiModel;
import xyz.erupt.toolkit.remote.RemoteProxyService;
import xyz.erupt.toolkit.utils.EruptDaoUtils;

import javax.annotation.Resource;
import javax.transaction.Transactional;

/**
 * <AUTHOR>
 * @since :2023/5/8:11:24
 */
@RestController
public class FacilityInspectionController {
    @Resource
    private RemoteProxyService remoteProxyService;

    //消防设施检测
    @RequestMapping("erupt-api/get/selectFacilityInspectionInfo/{orgCode}")

    @Transactional
    public EruptApiModel selectFacilityInspection(@PathVariable("orgCode") String orgCode) {
      //  MetaUserinfo remoteUserInfo = remoteProxyService.getRemoteUserInfo();
        StringBuffer stringBuffer = new StringBuffer();

        // 消防设施检测
        NumForms currentJobForm2 = EruptDaoUtils.selectOne("SELECT COUNT(*) as num  from tb_facility_inspection  where CURRENT_DATE() > validity_period and org_code ='" + orgCode+ "'", NumForms.class);
        if (!currentJobForm2.getNum().equals("0")) {
            stringBuffer.append("消防设施已过期!");
        }
        // 防雷装置检测
        NumForms currentJobForm3 = EruptDaoUtils.selectOne("SELECT COUNT(*) as num  from tb_lightning_protection  where CURRENT_DATE() > validity_period and org_code ='" + orgCode + "'", NumForms.class);
        if (!currentJobForm3.getNum().equals("0")) {
            stringBuffer.append("防雷装置已过期!");
        }
        // 压力表检测
        NumForms currentJobForm4 = EruptDaoUtils.selectOne("SELECT COUNT(*) as num  from tb_pressure_gauge  where CURRENT_DATE() > validity_period and org_code ='" + orgCode + "'", NumForms.class);
        if (!currentJobForm4.getNum().equals("0")) {
            stringBuffer.append("压力表已过期!");
        }
        //安全阀检测
        NumForms currentJobForm5 = EruptDaoUtils.selectOne("SELECT COUNT(*) as num  from tb_safety_valve  where CURRENT_DATE() > validity_period and org_code ='" + orgCode + "'", NumForms.class);
        if (!currentJobForm5.getNum().equals("0")) {
            stringBuffer.append("安全阀已过期!");
        }
        //可燃气体报警检测
        NumForms currentJobForm6 = EruptDaoUtils.selectOne("SELECT COUNT(*) as num  from tb_combustible_gas  where CURRENT_DATE() > validity_period and org_code ='" + orgCode + "'", NumForms.class);
        if (!currentJobForm6.getNum().equals("0")) {
            stringBuffer.append("可燃气体报警已过期!");
        }
        if (stringBuffer.length() == 0) {
            stringBuffer.append("本企业港口设施维护管理系统中录入的设备设施均在检测有效期内");
        }

        return EruptApiModel.successApi(stringBuffer);
    }

}
