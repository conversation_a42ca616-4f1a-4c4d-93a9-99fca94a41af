package com.daliangang.emergency.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import xyz.erupt.annotation.Erupt;
import xyz.erupt.annotation.EruptField;
import xyz.erupt.annotation.sub_erupt.Power;
import xyz.erupt.annotation.sub_field.Edit;
import xyz.erupt.annotation.sub_field.EditType;
import xyz.erupt.annotation.sub_field.View;
import xyz.erupt.annotation.sub_field.sub_edit.InputType;
import xyz.erupt.toolkit.db.Comment;

import javax.persistence.Entity;
import javax.persistence.Table;
import java.math.BigDecimal;

@Erupt(name = "评估成绩",
        orderBy = "EvaluationManage.eval_id,EvaluationManage.id",
        power = @Power(add = false, delete = false, export = false, importable = true, viewDetails = true, edit = true)
        //, dataProxy = EvaluationDataProxy.class
        , rowOperation = {})
@Table(name = "tb_evaluation_score")
@Entity
@Getter
@Setter
@Comment("评估成绩")
@ApiModel("评估成绩")
public class EvaluationScore extends EvaluationCommon {

    @EruptField(
            views = @View(title = "得分", show = false), sort = 9999,
            edit = @Edit(title = "得分", type = EditType.NUMBER,
                    inputType = @InputType))
    @Comment("得分")
    @ApiModelProperty("得分")
    private BigDecimal score;
}
