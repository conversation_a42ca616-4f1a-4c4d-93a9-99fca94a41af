package com.daliangang.workbench.init;

import com.daliangang.workbench.entity.Department;
import com.daliangang.workbench.proxy.DepartmentDataProxy;
import com.daliangang.workbench.service.PlatformService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import xyz.erupt.excel.entity.CommonExcelObject;
import xyz.erupt.excel.template.ExcelTemplateConvertHandler;
import xyz.erupt.jpa.dao.EruptDao;
import xyz.erupt.toolkit.utils.AssertUtils;
import xyz.erupt.toolkit.utils.LogUtils;
import xyz.erupt.toolkit.utils.NotifyUtils;
import xyz.erupt.toolkit.utils.SqlUtils;
import xyz.erupt.upms.enums.DataRange;
import xyz.erupt.upms.model.EruptDictItem;
import xyz.erupt.upms.model.EruptOrg;
import xyz.erupt.upms.model.EruptPost;
import xyz.erupt.upms.model.EruptRole;
import xyz.erupt.upms.model.template.EruptRoleTemplate;
import xyz.erupt.upms.service.EruptPlatformService;
import xyz.erupt.upms.util.MetaUtil;

import javax.annotation.Resource;
import javax.transaction.Transactional;
import java.util.List;

/**
 * @Author：chb
 * @Package：com.daliangang.workbench.init
 * @Project：erupt
 * @name：InitTemplateConvertHandler
 * @Date：2023/3/5 18:00
 * @Filename：InitTemplateConvertHandler
 */
@Component
@Slf4j
public class InitTemplateConvertHandler implements ExcelTemplateConvertHandler<InitTemplate> {

    @Resource
    private EruptDao eruptDao;

    @Resource
    private PlatformService platformService;

    @Resource
    private DepartmentDataProxy departmentDataProxy;

    @Transactional
    void createInitRole(List<InitTemplate> templates) {
        for (InitTemplate currRow : templates) {
            InitRole role = (InitRole) currRow;
            EruptRole eruptRole = new EruptRole();
            eruptRole.setCode(role.getCode());
            eruptRole.setName(role.getName());
            eruptRole.setSort(role.getSort());
            eruptRole.setReserved(true);
            eruptRole.setShare(role.getShare().equals("是"));
            eruptRole.setIdent(role.getIdent());
            eruptDao.persist(eruptRole);
        }
    }


//    @Resource
//    private MenuDefaultRoleHandler menuDefaultRoleHandler;
//
//    @Resource
//    private EruptExcelController eruptExcelController;

    @Transactional
    public void createInitRoleTemplate(List<InitTemplate> templates) {
        for (InitTemplate currRow : templates) {
            InitRoleTemplate roleTemplate = (InitRoleTemplate) currRow;
            EruptRoleTemplate eruptRoleTemplate = new EruptRoleTemplate();
            eruptRoleTemplate.setName(roleTemplate.getName());
            eruptRoleTemplate.setBaseRole(this.getBaseRoleCode(roleTemplate.getRole()));
            eruptRoleTemplate.setDataRange(this.getDataRange(roleTemplate.getRange()));
            eruptRoleTemplate.setDuplicateRole(roleTemplate.getDuplicate().equalsIgnoreCase("是"));
            MetaUtil.prepareMetaInfo(eruptRoleTemplate, true, true);
            eruptDao.persist(eruptRoleTemplate);
        }

        //导菜单权限
        //eruptExcelController.importExcel(ModelMenu.class, "public/init/01-00-菜单设置.xls");
        //menuDefaultRoleHandler.exec(null, null, null);

    }

    private String getBaseRoleCode(String name) {
        EruptRole role = eruptDao.queryEntity(EruptRole.class, "name=" + SqlUtils.wrapStr(name));
        return role.getCode();
    }

    private String getDataRange(String role) {
        if (DataRange.SELF.getTitle().equalsIgnoreCase(role)) return DataRange.SELF.name();
        if (DataRange.LOWERS.getTitle().equalsIgnoreCase(role)) return DataRange.LOWERS.name();
        if (DataRange.ALL.getTitle().equalsIgnoreCase(role)) return DataRange.ALL.getTitle();
        return null;
    }

    @Transactional
    void createInitDepartment(List<InitTemplate> templates) {
        for (InitTemplate template : templates) {
            InitDepartment department = (InitDepartment) template;
            Department dept = new Department();
            dept.setName(department.getName());
            dept.setPortArea(eruptDao.queryEntity(EruptDictItem.class, "name=" + SqlUtils.wrapStr(department.getPortArea())).getCode());
            dept.setParent(this.getDepartment(department.getParent()));
            EruptRoleTemplate roleTemplate = this.getRoleTemplate(department.getRoleTemplate());
            AssertUtils.notNull(roleTemplate, "权限模板为空");
            dept.setRole(roleTemplate);
            dept.setEruptOrgId(0L);
            dept.setInitOrgCode(department.getInitOrgCode());
            departmentDataProxy.beforeAdd(dept);
            eruptDao.persist(dept);
            departmentDataProxy.afterAdd(dept);
        }

    }

    private EruptRoleTemplate getRoleTemplate(String name) {
        if (StringUtils.isEmpty(name)) return null;
        return eruptDao.queryEntity(EruptRoleTemplate.class, "name=" + SqlUtils.wrapStr(name));
    }

    private Department getDepartment(String name) {
        if (StringUtils.isEmpty(name)) return null;
        return eruptDao.queryEntity(Department.class, "name=" + SqlUtils.wrapStr(name));
    }

    public EruptOrg getEruptOrg(String name) {
        return eruptDao.queryEntity(EruptOrg.class, "name=" + SqlUtils.wrapStr(name));
    }

    private void createInitDepartmentAccount(List<InitTemplate> templates) {
        for (InitTemplate template : templates) {
            InitAccount account = (InitAccount) template;
            Department department = this.getDepartment(account.getDepartment());
            AssertUtils.notNull(department, "不存在的主管部门[" + account.getDepartment() + "]，无法创建[" + account.getAccount() + "]");
            //这里是初始化，考虑到以后不会再初始化了，所以现在先把电话号码设置为null
            platformService.afterAdd(account.getAccount(), account.getName(), department.getInitOrgCode(),null, false, this.getEruptOrg(department.getName()), department.getRole(), EruptPlatformService.getInitPassWord(), InitConst.HOMEPAGE_DEPARTMENT,true);
        }
    }

    private void createInitPost(List<InitTemplate> templates) {
        for (InitTemplate template : templates) {
            InitPost post_ = (InitPost) template;
            EruptPost post = new EruptPost();
            post.setCode(post_.getCode());
            post.setName(post_.getName());
            post.setWeight(post_.getWeight());
            post.setExclusive(post_.getTraining());
            post.setType(post_.getPostType());
            eruptDao.persist(post);
        }
    }

    /**
     * @param templates
     * @param parent_
     */
    @Override
    public void convert(List<InitTemplate> templates, CommonExcelObject parent_) {
        InitTemplate parent = (InitTemplate) parent_;
        switch (parent.getType()) {
            case ROLE:
                this.createInitRole(templates);
                break;
            case ROLE_TEMPLATE:
                this.createInitRoleTemplate(templates);
                break;
            case DEPARTMENT:
                this.createInitDepartment(templates);
                break;
            case DEPT_ACCOUNT:
                this.createInitDepartmentAccount(templates);
                break;
            case POST:
                this.createInitPost(templates);
                break;
            case OTHER:
                NotifyUtils.showErrorMsg("无效参数: " + LogUtils.toJson(parent_));
                break;
        }
    }
}
