/*
 * Copyright © 2020-2035 yundingyun All rights reserved.
 */
package com.daliangang.rndpub.operation;

import com.daliangang.rndpub.entity.EnterpriseInformation;
import com.daliangang.rndpub.entity.InspectionItems;
import com.daliangang.rndpub.entity.Procedure;
import lombok.Data;
import org.springframework.stereotype.Service;
import xyz.erupt.annotation.Erupt;
import xyz.erupt.annotation.EruptField;
import xyz.erupt.annotation.fun.OperationHandler;
import xyz.erupt.annotation.sub_field.Edit;
import xyz.erupt.annotation.sub_field.EditType;
import xyz.erupt.core.context.MetaDrill;
import xyz.erupt.jpa.dao.EruptDao;
import xyz.erupt.jpa.model.BaseModel;
import xyz.erupt.toolkit.db.EruptResultMap;
import xyz.erupt.toolkit.utils.EruptDaoUtils;
import xyz.erupt.toolkit.utils.NotifyUtils;
import xyz.erupt.toolkit.utils.SqlUtils;

import javax.annotation.Resource;
import javax.transaction.Transactional;
import java.util.List;

@Service
public class InspectionItemsSystemExtractionHandler implements OperationHandler<InspectionItems, InspectionItemsSystemExtractionHandler.DrawInspectionItemForm> {

    @Erupt(name = "自动抽取检查事项")
    @Data
    public static class DrawInspectionItemForm extends BaseModel {
        @EruptField(
                edit = @Edit(title = "抽取检查内容（一级）的个数", type = EditType.NUMBER, notNull = true))
        private int count = 10;
    }

    @Resource
    private EruptDao eruptDao;

    @Override
    @Transactional
    public String exec(List<InspectionItems> data, DrawInspectionItemForm form, String[] param) {

        //获取企业信息
        String enterpriseSql = "select enterprise_name,type from tb_enterprise_information where procedure_id=" + MetaDrill.getDrillId() + " and state=1";
        List<EnterpriseInformation> enterpriseList = EruptDaoUtils.selectOnes(enterpriseSql, EnterpriseInformation.class);
        if (enterpriseList.size() == 0) {
            NotifyUtils.showErrorMsg("请先抽取企业");
        }

        Procedure procedure = eruptDao.queryEntity(Procedure.class, "id=" + MetaDrill.getDrillId());
        //所有条目设置为未抽取
        String resetSql = "update tb_inspection_items set state=0 where procedure_id=" + MetaDrill.getDrillId();
        eruptDao.getJdbcTemplate().execute(resetSql);
        //取得所有一级条目
        String sql = "select distinct check_first from tb_inspection_items where procedure_id=" + MetaDrill.getDrillId() + " and  state=0 order by rand() limit " + form.getCount();
        List<EruptResultMap> checkFirsts = EruptDaoUtils.selectOnes(sql, EruptResultMap.class);
        for (EruptResultMap vo : checkFirsts) {
            String checkFirst = vo.getString("check_first");
            String itemSql = "select * from tb_inspection_items where procedure_id=" + MetaDrill.getDrillId() + " and check_first=" + SqlUtils.wrapStr(checkFirst);
            List<InspectionItems> items = EruptDaoUtils.selectOnes(itemSql, InspectionItems.class);
            for (InspectionItems info : items) {
                String updSql = "update tb_inspection_items set state=1  where id=" + info.getId();
                eruptDao.getJdbcTemplate().execute(updSql);
                eruptDao.flush();
            }
        }

        //更新条数
        updateCountAndName(procedure);
        return null;
    }

    public void updateCountAndName(Procedure procedure) {
        String countSql = "select count(*) as count,group_concat(inspection_items)name from tb_inspection_items where procedure_id=" + procedure.getId() + " and state=1";
        EruptResultMap eruptResultMap = EruptDaoUtils.selectMap(countSql);
        procedure.setInspectionItems(eruptResultMap.getString("name"));
        eruptDao.merge(procedure);
    }
}
