package com.daliangang.statistics.handler;

import com.daliangang.core.DaliangangContext;
import com.daliangang.workbench.entity.Department;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.stereotype.Component;
import xyz.erupt.bi.fun.EruptBiHandler;
import xyz.erupt.jpa.dao.EruptDao;
import xyz.erupt.toolkit.utils.SqlUtils;
import xyz.erupt.upms.model.EruptUser;
import xyz.erupt.upms.service.EruptUserService;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * 从业人员一体化-企业统计
 * @Author: zengwensi
 * @Description:
 * @Date: Created in 2023/6/1 14:24
 * @Modified By
 */
@Component
public class EnterpriseStatisticsHandler implements EruptBiHandler {

    @Resource
    private EruptDao eruptDao;
    @Resource
    EruptUserService eruptUserService;

    @Override
    public String exprHandler(String param, Map<String, Object> condition, String expr) {
//        岗位评估次数:企业自组织次数+政府组织次数，可在【岗位胜任力/评估统计】菜单下查询;  如果是市级，则不需要organ_id。如果是企业，organ_id 是登录企业机构id+市级的id
//        法定培训次数:市级政府添加的培训次数，可在【教务管理/培训考核】菜单下查询；  organ_id要查市级的id
//        自组织培训次数:企业添加的培训次数，可在【教务管理/培训考核】菜单下查询；   如果是企业登录，organ_id要查当前登录企业的id，如果是市级，则无需organ_id
//        总培训人次:合计参加培训的人数；  如果是市级，则不需要organ_id。如果是企业，organ_id 是登录企业机构id+政府机构id

        //当前登录机构id
        EruptUser currentEruptUser = eruptUserService.getCurrentEruptUser();
        Long orgId = 0L;
        if(null != currentEruptUser.getEruptOrg()){
            orgId = currentEruptUser.getEruptOrg().getId();
        }

        //市级id，市级只有一个
        Department department = eruptDao.queryEntity(Department.class, " parent is null");
        Long departmentId = department.getId();

        //企业名称,如果是企业，只能看自己的
        String company = "";
        if (DaliangangContext.isEnterpriseUser()) {
            company +=  " and main_id ="+  SqlUtils.wrapStr(String.valueOf(orgId)) ;
        }
        if(condition.containsKey("company")){
            String companyName = (String)condition.get("company");
            if (StringUtils.isNotEmpty( companyName)) {
                company += " and  name like '%"+companyName+"%'";
            }

        }
        expr = expr.replaceAll("#ORGSQL",company) ;


        //岗位评估次数
        String postTrain = "";
        if (DaliangangContext.isEnterpriseUser()) {
            List<Long> orgIds = Arrays.asList(orgId,departmentId);
            postTrain =  " and org_id in (select id from t_organ where main_id "+ SqlUtils.wrapIn(orgIds) +")" ;
        }
        expr = expr.replaceAll("#POSTTRAINSQL",postTrain) ;

        //法定培训次数
        String legalTrain = " and organ_id = (select id from t_organ where main_id = "+ SqlUtils.wrapStr(String.valueOf(departmentId)) +")" ;
        expr = expr.replaceAll("#LEGALTRAINSQL",legalTrain) ;

        //自组织培训次数
        String selfTrain = "";
        if (DaliangangContext.isEnterpriseUser()) {
            selfTrain =" and organ_id = (select id from t_organ where main_id = "+ SqlUtils.wrapStr(String.valueOf(orgId)) +")";
        }
        expr = expr.replaceAll("#SELFTRAINSQL",selfTrain) ;

        //总培训人次
        String userTrain = "";
        if (DaliangangContext.isEnterpriseUser()) {
            List<Long> orgIds = Arrays.asList(orgId,departmentId);
            userTrain =   " and organ_id in (select id from t_organ where main_id "+ SqlUtils.wrapIn(orgIds) +")" ;
        }
        expr = expr.replaceAll("#USERTRAINSQL",userTrain) ;

        return expr;
    }

    @Override
    public void resultHandler(String param, Map<String, Object> condition, List<Map<String, Object>> result) {

    }

    @Override
    public void exportHandler(String param, Map<String, Object> condition, Workbook workbook) {

    }
}
