package com.daliangang.workbench.handler;

import com.daliangang.workbench.entity.EmployeeInformation;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import xyz.erupt.annotation.expr.ExprBool;
import xyz.erupt.core.context.MetaContext;
import xyz.erupt.core.util.EruptSpringUtil;
import xyz.erupt.upms.model.EruptMenu;
import xyz.erupt.upms.service.EruptContextService;

@Service
public class EmployeeViewCompanyHandler implements ExprBool.ExprHandler{

    @Override
    public boolean handler(boolean expr, String[] params) {
        if (StringUtils.isEmpty(MetaContext.getToken())) return false;
        EruptContextService eruptContextService = EruptSpringUtil.getBean(EruptContextService.class);
        EruptMenu menu = eruptContextService.getCurrentEruptMenu();
        if (menu == null) {
            return true;
        } else return !menu.getValue().equals(EmployeeInformation.class.getSimpleName());
    }
}
