package com.daliangang.workbench.patch;


import cn.hutool.json.JSONUtil;
import com.daliangang.device.entity.*;
import com.daliangang.device.operation.PositionDockingHttpHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import xyz.erupt.devtools.patch.PlatformPatchHandler;
import xyz.erupt.jpa.dao.EruptDao;
import xyz.erupt.toolkit.utils.SqlUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

@Service
@Slf4j
public class DeviceUpatePatch implements PlatformPatchHandler {

    @Override
    public String getPatchId() {
        return "1.11.7.6-patch-02";
    }

    @Override
    public String getName() {
        return "需要推到风控的数据,收集经纬度后推送";
    }

    @Override
    public String getDesc() {
        return "1.查询未推送过去的数据<br>2.查询调用风控位置更新接口<br>";
    }

    @Resource
    private EruptDao eruptDao;

    @Resource
    private PositionDockingHttpHandler positionDockingHttpHandler;

    @Override
    public boolean execute() {
        //风控id
        ArrayList<String> fengkongIds = new ArrayList<>();
        fengkongIds.add("e83bb501-4808-46cf-85db-489a29fc6728");
        fengkongIds.add("fada238e-7afb-48d0-8295-0a6595f0e6aa");
        fengkongIds.add("2fadef35-404d-4f47-93e4-317971fb4064");
        fengkongIds.add("932f3cc3-4a79-4025-be4d-f31517969171");
        fengkongIds.add("b49398b7-1ad8-4aa3-b75b-228417b38135");
        fengkongIds.add("d67727b2-586e-467b-809f-b1beb55036fd");
        fengkongIds.add("833febde-509d-4b5d-bcfc-0a6e90b769b4");
        fengkongIds.add("e2e4443b-0b16-49e8-897c-130b5b8871f3");
        fengkongIds.add("145e6b08-c9b7-4dea-8aae-64f51ac1bcda");
        fengkongIds.add("18d3e6b8-caba-453e-b633-8439868670ac");
        fengkongIds.add("fe3f8803-ac6c-4fe2-b64d-4817f3671b26");
        fengkongIds.add("4126cd53-22ef-4b46-a6ae-df5653b454e7");
        fengkongIds.add("71d34d41-9390-4fa5-b9f8-6915781cd34c");
        fengkongIds.add("e75c246e-28d7-47be-a302-9c265231b841");
        String token = "";
        List<PositionDocking> positionDockings = eruptDao.queryEntityList(PositionDocking.class, " position_id " + SqlUtils.wrapIn(fengkongIds));
        for (PositionDocking positionDocking : positionDockings) {
            log.info("要推送的中间表数据为："+ JSONUtil.toJsonStr(positionDocking)) ;
            switch (positionDocking.getPositionType()){
                case "1": //储罐
                    StorageTank storageTank = eruptDao.queryEntity(StorageTank.class,"id = "+SqlUtils.wrapStr(String.valueOf(positionDocking.getBId())));
                    if(null == storageTank){
                        log.info("无该储罐数据,故没推送："+ JSONUtil.toJsonStr(positionDocking)) ;
                        continue;
                    }
//                    token = DaliangangContext.getToken();
                    positionDockingHttpHandler.AddHttpDocking(storageTank, "1", storageTank.getOrgCode(),token);
                    break ;
                case "2"://泊位
                    Berth berth = eruptDao.queryEntity(Berth.class, "id = " + SqlUtils.wrapStr(String.valueOf(positionDocking.getBId())));
                    if(null == berth){
                        log.info("无该泊位数据,故没推送："+ JSONUtil.toJsonStr(positionDocking)) ;
                        continue;
                    }
//                    token = DaliangangContext.getToken();
                    positionDockingHttpHandler.AddHttpDocking(berth, "1", berth.getOrgCode(),token);
                    break ;
                case "3"://栈台
//                    token = DaliangangContext.getToken();
                    LoadingDock loadingDock = eruptDao.queryEntity(LoadingDock.class, "id = " + SqlUtils.wrapStr(String.valueOf(positionDocking.getBId())));
                    if(null == loadingDock){
                        log.info("无该栈台数据,故没推送："+ JSONUtil.toJsonStr(positionDocking)) ;
                        continue;
                    }
                    positionDockingHttpHandler.AddHttpDocking(loadingDock, "1", loadingDock.getOrgCode(),token);
                    break ;
                case "5"://堆场
//                    token = DaliangangContext.getToken();
                    Yard yard = eruptDao.queryEntity(Yard.class, "id = " + SqlUtils.wrapStr(String.valueOf(positionDocking.getBId())));
                    if(null == yard){
                        log.info("无该堆场数据,故没推送："+ JSONUtil.toJsonStr(positionDocking)) ;
                        continue;
                    }
                    positionDockingHttpHandler.AddHttpDocking(yard, "1", yard.getOrgCode(),token);
                    break ;
                default: //仓库/棚库
//                    token = DaliangangContext.getToken();
                    Warehouse warehouse = eruptDao.queryEntity(Warehouse.class, "id = " + SqlUtils.wrapStr(String.valueOf(positionDocking.getBId())));
                    if(null == warehouse){
                        log.info("无该仓库/棚库数据,故没推送："+ JSONUtil.toJsonStr(positionDocking)) ;
                        continue;
                    }
                    positionDockingHttpHandler.AddHttpDocking(warehouse, "1", warehouse.getOrgCode(),token);
                    break ;
            }

        }
        return true;
    }
}
