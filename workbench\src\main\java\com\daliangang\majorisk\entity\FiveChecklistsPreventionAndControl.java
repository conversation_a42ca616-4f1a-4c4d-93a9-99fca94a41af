package com.daliangang.majorisk.entity;

import com.daliangang.majorisk.handler.MajorRiskTypeLink2ByHandler;
import com.daliangang.majorisk.handler.MajorRiskTypeLinkByHandler;
import com.daliangang.majorisk.proxy.FiveChecklistsPreventionAndControlDataProxy;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import xyz.erupt.annotation.Erupt;
import xyz.erupt.annotation.EruptField;
import xyz.erupt.annotation.sub_erupt.Power;
import xyz.erupt.annotation.sub_field.Edit;
import xyz.erupt.annotation.sub_field.EditType;
import xyz.erupt.annotation.sub_field.View;
import xyz.erupt.annotation.sub_field.sub_edit.*;
import xyz.erupt.toolkit.db.Comment;
import xyz.erupt.toolkit.handler.EruptDictCodeChoiceFetchHandler;
import xyz.erupt.toolkit.handler.EruptDictTagFetchHandler;
import xyz.erupt.upms.model.auth.DataAuthModel;

import javax.persistence.*;

/**
 * <AUTHOR>
 * @since :2023/4/4:9:57
 */

@Erupt(name = "五清单防控措施", power = @Power(add = true, delete = true, export = false, importable = true, viewDetails = true, edit = true)

)
@Table(name = "tb_fivechecklists_preventionAndControl")
@Entity
@Getter
@Setter
@Comment("五清单防控措施")
@ApiModel("五清单防控措施")
public class FiveChecklistsPreventionAndControl extends DataAuthModel {
//    @EruptField(
//            views = @View(title = "企业名称"),
//            edit = @Edit(title = "企业名称", readonly = @Readonly(exprHandler = DataAuthInvoker.class), type = EditType.CHOICE, notNull = true,
//                    choiceType = @ChoiceType(fetchHandler = RemoteEntityChoiceFetchHandler.class, fetchHandlerParams ={"main","Enterprise","org_code,name"})))
//    @Comment("企业名称")
//    @ApiModelProperty("企业名称")
//    private String company;

    @EruptField(
            views = @View(title = "风险类型",show = false),
            edit = @Edit(title = "风险类型", type = EditType.CHOICE,show = false,
                    choiceType = @ChoiceType(fullSpan = true,
                            fetchHandler = EruptDictCodeChoiceFetchHandler.class, fetchHandlerParams = "riskType",
                            reload = true, linkBy = @LinkBy(property = "pointsOfPrevention", handler = MajorRiskTypeLink2ByHandler.class)
                    )))
    @Comment("风险类型")
    @ApiModelProperty("风险类型")
    private String fiveRiskType;

    @Lob
    @EruptField(
            views = @View(title = "风险管控制度"),
            edit = @Edit(title = "风险管控制度", type = EditType.TEXTAREA,
                    placeHolder = "说明：列出本单位重大风险相关制度文件名称及签发号或编号或版本号，包括但不限于风险监控预警制度、风险警示告知制度、教育培训制度、档案管理制度、风险控制制度等，示例：公司重大风险相关管控制度包括：XX制度（签发号或编号或版本号）、XX制度（签发号或编号或版本号）、……",
                    inputType = @InputType(length = 10)
            ))
    @Comment("风险管控制度")
    @ApiModelProperty("风险管控制度")
    private String riskControlSystem ;

    @EruptField(
            views = @View(title = "主要防控要点"),
            edit = @Edit(title = "主要防控要点", type = EditType.TAGS,
                    tagsType = @TagsType(fetchHandler = EruptDictTagFetchHandler.class, fetchHandlerParams = "${property}")))
    @Comment("主要防控要点")
    @ApiModelProperty("主要防控要点")
    private String pointsOfPrevention ;


    @Lob
    @EruptField(
            views = @View(title = "其他备注", show = false),
            edit = @Edit(
                    title = "其他备注",
                    showBy = @ShowBy(dependField = "pointsOfPrevention", expr = "value.includes(\"其他\")"),
                    type = EditType.TEXTAREA,
                    inputType = @InputType(length = 500)
            )
    )
    private String otherRemark;

//    @Lob
//    @EruptField(
//            views = @View(title = "要点描述"),
//            edit = @Edit(title = "要点描述", type = EditType.TEXTAREA
//            ))
//    @Comment("要点描述")
//    @ApiModelProperty("要点描述")
//    private String pointDescription;

    @Lob
    @EruptField(
            views = @View(title = "风险告知措施"),
            edit = @Edit(title = "风险告知措施", type = EditType.TEXTAREA,placeHolder = "说明：列出本单位风险警示告知的情况，示例：公司在XX（区域）设置重大危险源包保责任制告知牌，在XX（区域、设施、设备等）设置安全警示标志，在XX（区域）设立安全风险分级管控四色图，建立XX岗位、XX岗位、……安全风险告知卡，……",
                    inputType = @InputType(length = 500)
            ))
    @Comment("风险告知措施")
    @ApiModelProperty("风险告知措施")
    private String riskDisclosure ;

//    @Lob
//    @EruptField(
//            views = @View(title = "备注"),
//            edit = @Edit(
//                    title = "备注",
//                    type = EditType.TEXTAREA
//            )
//    )
//    private String remark;


//    @OneToOne
//    @EruptField
//    @JoinColumn(name = "tb_fivechecklists_foundation_id")
//    private FiveChecklistsFoundation fiveChecklistsFoundation;
@OneToOne
@EruptField
@JoinColumn(name = "tb_five_detail_id")
private FiveDetail fiveDetail;

}
