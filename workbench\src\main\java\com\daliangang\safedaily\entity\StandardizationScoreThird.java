package com.daliangang.safedaily.entity;

import com.daliangang.safedaily.proxy.StandardizationScoreThirdDataProxy;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import xyz.erupt.annotation.Erupt;
import xyz.erupt.annotation.EruptField;
import xyz.erupt.annotation.config.EruptSmartSkipSerialize;
import xyz.erupt.annotation.expr.ExprBool;
import xyz.erupt.annotation.sub_erupt.Power;
import xyz.erupt.annotation.sub_field.Edit;
import xyz.erupt.annotation.sub_field.EditType;
import xyz.erupt.annotation.sub_field.Readonly;
import xyz.erupt.annotation.sub_field.View;
import xyz.erupt.annotation.sub_field.sub_edit.InputType;
import xyz.erupt.annotation.sub_field.sub_edit.NumberType;
import xyz.erupt.jpa.model.BaseModel;
import xyz.erupt.toolkit.db.Comment;

import javax.persistence.Entity;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import java.math.BigDecimal;

@Erupt(name = "标准化得分",
        power = @Power(
        add = false,
        delete = false,
        export = false,
        importable = true,
        viewDetails = true,
        edit = true)
        , dataProxy = StandardizationScoreThirdDataProxy.class
        , rowOperation = {})
@Table(name = "tb_standardization_score_third")
@Entity
@Getter
@Setter
@Comment("标准化得分")
@ApiModel("标准化得分")
public class StandardizationScoreThird extends BaseModel {

    @EruptField(views = @View(title = "序号"), edit = @Edit(title = "序号", readonly = @Readonly(exprHandler = StandardizationItem.class), type = EditType.NUMBER))
    @Comment("序号")
    @ApiModelProperty("序号")
    private Integer seriesId;

    @EruptField(views = @View(title = "评价项目"), edit = @Edit(title = "评价项目", readonly = @Readonly(exprHandler = StandardizationItem.class), type = EditType.INPUT, inputType = @InputType))
    @Comment("评价项目")
    @ApiModelProperty("评价项目")
    private String standard;

    @EruptField(views = @View(title = "标准分数"),
            edit = @Edit(title = "标准分数", readonly = @Readonly(exprHandler = StandardizationItem.class), type = EditType.NUMBER, numberType = @NumberType()))
    @Comment("标准分数")
    @ApiModelProperty("标准分数")
    private BigDecimal tandardsScore;

    @EruptField(views = @View(title = "理论分值"),
            edit = @Edit(title = "理论分值", type = EditType.NUMBER, numberType = @NumberType()))
    @Comment("理论分值")
    @ApiModelProperty("理论分值")
    private BigDecimal scoreStandard;

    @EruptField(views = @View(title = "得分占比", ifRender = @ExprBool(exprHandler = StandardizationItem.class)),
            edit = @Edit(title = "得分占比", ifRender = @ExprBool(exprHandler = StandardizationItem.class), readonly = @Readonly(exprHandler = StandardizationItem.class), type = EditType.NUMBER, numberType = @NumberType(min = 0, max = 100)))
    @Comment("得分占比")
    @ApiModelProperty("得分占比")
    private BigDecimal scoreProportion;

    @EruptField(
            views = @View(title = "实际得分"), sort = 9999,
            edit = @Edit(title = "实际得分", type = EditType.NUMBER, numberType = @NumberType()))
    @Comment("实际得分")
    @ApiModelProperty("实际得分")
    private BigDecimal score;

    @ManyToOne
    @EruptField
    @JoinColumn(name = "standard_id")
    @JsonIgnore
    @EruptSmartSkipSerialize
    private StandardizationThird standardizationThird;
}
