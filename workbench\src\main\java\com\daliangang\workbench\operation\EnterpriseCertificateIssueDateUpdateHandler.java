package com.daliangang.workbench.operation;

import com.daliangang.workbench.entity.EnterpriseCertificateView;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.stereotype.Service;
import xyz.erupt.annotation.Erupt;
import xyz.erupt.annotation.EruptField;
import xyz.erupt.annotation.fun.OperationHandler;
import xyz.erupt.annotation.sub_field.Edit;
import xyz.erupt.annotation.sub_field.EditType;
import xyz.erupt.annotation.sub_field.View;
import xyz.erupt.annotation.sub_field.sub_edit.DateType;
import xyz.erupt.jpa.dao.EruptDao;
import xyz.erupt.jpa.model.BaseModel;
import xyz.erupt.toolkit.db.Comment;

import javax.annotation.Resource;
import javax.transaction.Transactional;
import java.util.List;

@Service
public class EnterpriseCertificateIssueDateUpdateHandler implements OperationHandler<EnterpriseCertificateView, EnterpriseCertificateIssueDateUpdateHandler.IssueDateUpdate> {

    @Erupt(name = "修改发证日期")
    @Data
    public static class IssueDateUpdate extends BaseModel {
        @EruptField(
                views = @View(title = "发证日期"),
                edit = @Edit(title = "发证日期", type = EditType.DATE, notNull = true,
                        dateType = @DateType))
        @Comment("发证日期")
        @ApiModelProperty("发证日期")
        private java.util.Date issueDate;
    }

    @Resource
    private EruptDao eruptDao ;

    @Transactional
    @Override
    public String exec(List<EnterpriseCertificateView> data, EnterpriseCertificateIssueDateUpdateHandler.IssueDateUpdate issueDateUpdate, String[] param) {
        for(EnterpriseCertificateView view : data){
            view.setIssueDate(issueDateUpdate.getIssueDate());
            eruptDao.mergeAndFlush(view);
        }
        return null ;
    }
}
