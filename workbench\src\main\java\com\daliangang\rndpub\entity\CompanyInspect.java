package com.daliangang.rndpub.entity;

import com.daliangang.rndpub.proxy.CompanyInspectDataProxy;
import io.swagger.annotations.ApiModel;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import xyz.erupt.annotation.Erupt;
import xyz.erupt.annotation.EruptField;
import xyz.erupt.annotation.sub_erupt.Power;
import xyz.erupt.annotation.sub_field.Edit;
import xyz.erupt.annotation.sub_field.EditType;
import xyz.erupt.annotation.sub_field.View;
import xyz.erupt.annotation.sub_field.ViewType;
import xyz.erupt.annotation.sub_field.sub_edit.*;
import xyz.erupt.jpa.model.MetaModel;
import xyz.erupt.toolkit.db.Comment;
import xyz.erupt.toolkit.handler.RemoteEntityChoiceFetchHandler;
import xyz.erupt.upms.model.auth.OrgCodeRender;

import javax.persistence.Entity;
import javax.persistence.Table;

/**
 * <AUTHOR>
 * @date 2023/9/23
 */

@Erupt(
        name = "企业检查档案",
        power = @Power(
                add = false,
                edit = false,
                delete = false,
                viewDetails = false
        ),
        dataProxy = CompanyInspectDataProxy.class
)
@Table(name = "tb_company_inspect")
@Entity
@Getter
@Setter
@Comment("企业检查档案")
@ApiModel("企业检查档案")
@ToString
@OrgCodeRender(render = false)
public class CompanyInspect extends MetaModel {

    public static final String LINK_TARGET = "#/build/table/CompanyInspectDetail";

    public static final String LINK_SQL = "select org_code as value, name as label from tb_enterprise where `name` in (\n" +
                "select enterprise_name from tb_enterprise_information where procedure_id=(select id from tb_procedure where name='${company}')\n" +
                "and state=1)";
    @EruptField(
            views = @View(
                    title = "企业名称",
                    type = ViewType.LINK_HREF,
                    linkName="CompanyInspectDetail",
                    condition="[{'code':'company','searchCode':'company'},{'code':'inspectYear','searchCode':'inspectYear'}]"),
            edit = @Edit(
                    title = "企业名称",
                    type = EditType.CHOICE,
                    search = @Search,
                    choiceType = @ChoiceType(fullSpan = true,reload = true,fetchHandler = RemoteEntityChoiceFetchHandler.class, fetchHandlerParams = {"main", "Enterprise", "org_code,name"})))
    private String company;

    //检查次数
    @EruptField(
            views = @View(title = "检查次数",
                    type = ViewType.LINK_HREF,
                    linkName="CompanyInspectDetail",
                    condition="[{'code':'company','searchCode':'company'},{'code':'inspectYear','searchCode':'inspectYear'}]"),
            edit = @Edit(
                    title = "检查次数",
                    type=EditType.NUMBER,
                    numberType = @NumberType(min = 0)
            )
    )
    private Integer inspectCount;

    //检查问题数量
    @EruptField(
            views = @View(title = "检查问题数量", type = ViewType.LINK_HREF,
                    linkName="CompanyInspectDetail",
                    condition="[{'code':'company','searchCode':'company'},{'code':'inspectYear','searchCode':'inspectYear'}]"),
            edit = @Edit(
                    title = "检查问题数量",
                    type=EditType.NUMBER,
                    numberType = @NumberType(min = 0)
            )
    )
    private Integer inspectProblemCount;

    //已整改问题数量
    @EruptField(
            views = @View(title = "已整改问题数量", type = ViewType.LINK_HREF,
                    linkName="CompanyInspectDetail",
                    condition="[{'code':'company','searchCode':'company'},{'code':'inspectYear','searchCode':'inspectYear'}]"),
            edit = @Edit(
                    title = "已整改问题数量",
                    type=EditType.NUMBER,
                    numberType = @NumberType(min = 0)
            )
    )
    private Integer rectifyProblemCount;

    //整改率
    @EruptField(
            views = @View(title = "整改率", type = ViewType.LINK_HREF,
                    linkName="CompanyInspectDetail",
                    condition="[{'code':'company','searchCode':'company'},{'code':'inspectYear','searchCode':'inspectYear'}]"),
            edit = @Edit(
                    title = "整改率",
                    type=EditType.INPUT,
                    inputType = @InputType()
            )
    )
    private String rectifyChance;

    //检查年度
    @EruptField(
            views = @View(title = "年度", show = false,type = ViewType.LINK_HREF,
                    linkName="CompanyInspectDetail",
                    condition="[{'code':'company','searchCode':'company'},{'code':'inspectYear','searchCode':'inspectYear'}]"),
            edit = @Edit(
                    title = "年度",
                    type=EditType.DATE,
                   search = @Search(),
                   dateType = @DateType(type = DateType.Type.YEAR)
            )
    )
   private String inspectYear;


}
