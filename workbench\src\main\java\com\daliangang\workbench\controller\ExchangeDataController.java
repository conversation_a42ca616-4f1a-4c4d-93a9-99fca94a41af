package com.daliangang.workbench.controller;

import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.redis.core.ListOperations;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;
import xyz.erupt.core.annotation.EruptHandlerNaming;
import xyz.erupt.job.handler.EruptJobHandler;
import xyz.erupt.toolkit.redismq.RedisMQService;
import xyz.erupt.toolkit.service.EruptCacheRedis;
import xyz.erupt.toolkit.utils.NotifyUtils;
import xyz.erupt.upms.platform.EruptOption;
import xyz.erupt.upms.platform.EruptOptions;
import xyz.erupt.upms.service.EruptPlatformService;

import javax.annotation.Resource;
import java.util.*;

/**
 * 数据传输 接口
 */
@Slf4j
@EruptOptions({
        @EruptOption(name = ExchangeDataController.TOKEN_INPUT, value =  "{\"appkey\":\"dlyz_aq\",\"appSecret\":\"zt6Zi7TG1tB\", \"service\":\"\",\"version\":\"1.0\"}", desc = "数据传输-获取token接口的输入"),
        @EruptOption(name = ExchangeDataController.TOKEN_URL, value =  "/exchange_data/dlyzWebService/getToken.json", desc = "数据传输-获取token的接口"),
        @EruptOption(name = ExchangeDataController.TOKEN_IP, value =  "http://***********:5556", desc = "数据传输-ip"),
        @EruptOption(name = ExchangeDataController.EXCHANGE_DATA_URL, value = "/exchange_data/dlyzWebService/dataExchange.json", desc = "数据传输-数据交换的接口")
})
@RestController
@EruptHandlerNaming("推送大数据平台")
public class ExchangeDataController implements EruptJobHandler {



    //token 传参
    public static final String TOKEN_INPUT =  "EXCHANGE_DATA_TOKEN_INPUT";
    public static final String TOKEN_URL = "EXCHANGE_DATA_TOKEN_URL";
    public static final String TOKEN_IP = "EXCHANGE_DATA_TOKEN_IP";
    public static final String EXCHANGE_DATA_URL = "EXCHANGE_DATA_URL";

    public static final String EXCHANGE_DATA_TOKEN_KEY = "EXCHANGE_DATA_TOKEN_{SERVICE}:" ;

    public static final String EXCHANGE_DATA_EXCHANGE_KEY = "EXCHANGE_DATA_EXCHANGE_{SERVICE}_KEY:" ;

    public static final String EXCHANGE_DATA_ERUPT_URL = "EXCHANGE_DATA_ERUPT_URL";


    public static final Map<String,String> BUSINESS_CODE = new HashMap<String,String>(){{
        put("Release","t.web.service.aq.aqcnggxx.insert");//安全承诺公告信息
        put("Report","t.web.service.aq.aqkkxbgdxx.insert");//安全可靠性报告单信息
        put("PortRecord","t.web.service.aq.aqpjbaxx.insert");//安全评价备案信息
        put("CheckFill","t.web.service.aq.aqsczrzxx.insert");//安全生产责任制信息
        put("Standardization","t.web.service.aq.aqsczs.insert");//安全生产证书
        put("StandardizationItem","t.web.service.aq.aqsczsdfx.insert");//安全生产证书得分项
        put("Record","t.web.service.aq.aqtjscxx.insert");//安全条件审查信息
        put("Berth","t.web.service.aq.bwxx.insert");//泊位信息
        put("StorageTank","t.web.service.aq.cgxx.insert");//储罐信息
        put("Warehouse","t.web.service.aq.ckxx.insert");//仓库信息
        put("Yard","t.web.service.aq.dcxx.insert");//堆场信息
        put("EnterpriseCertificate","t.web.service.aq.fzxx.insert");//附证信息
        put("PortArea","t.web.service.aq.gkqxx.insert");//港口区信息
        put("Security","t.web.service.aq.gkssbabaxx.insert");//港口设施保安备案信息
        put("TankFarm","t.web.service.aq.gqxx.insert");//罐区信息
        put("TankGroup","t.web.service.aq.gzxx.insert");//罐组信息
        put("Sensitivetargets","t.web.service.aq.mgmb.insert");//敏感目标
        put("Wharf","t.web.service.aq.mtxx.insert");//码头信息
        put("OfflineTraining","t.web.service.aq.pxtz.insert");//培训台账 tkaq-boot工程里，在安全培训里
        put("DailyInspection","t.web.service.aq.rjcxx.insert");//日检查信息
        put("EmergencyPool","t.web.service.aq.sgyjc.insert");//事故应急池
        put("Design","t.web.service.aq.sssjscxx.insert");//设施设计审查信息
        put("MonthlyScheduling","t.web.service.aq.yddxx.insert");//月调度信息
        put("Rescueteam","t.web.service.aq.yjdw.insert");//应急队伍
        put("EmergencyMuster","t.web.service.aq.yjjhd.insert");//应急集合点
        put("MaterialReserve","t.web.service.aq.yjwzcbd.insert");//应急物资储备点
        put("PlanManagement","t.web.service.aq.yjyabaxx.insert");//应急预案备案信息
        put("Drill","t.web.service.aq.yjylxx.insert");//应急演练信息
        put("EmergencyExpert","t.web.service.aq.yjzj.insert");//应急专家
        put("EmergencyDuty","t.web.service.aq.yjzsxx.insert");//应急值守信息
        put("EmergencyDutyPerson","t.web.service.aq.yjzsxx.zsrxx.insert");//应急值守信息_值守人信息
        put("WeeklyReport","t.web.service.aq.zbgxx.insert");//周报告信息
        put("Preplan","t.web.service.aq.zdwxybaxx.insert");//重大危险源备案信息
        put("LoadingDock","t.web.service.aq.zxtxx.insert");//装卸台信息
        put("QualificationManage","t.web.service.aq.zyzzndjcxx.insert");//作业资质年度检查信息
        put("EmployeeCertificateView","t.web.service.aq.zyzzndjcxx.insert");// todo 从业人员证书

    }
    };

    @AllArgsConstructor
    @NoArgsConstructor
    @Data
    public static class ExchangeDataResponse{
        private String msg ;

        private String code ;

        private String data ;
    }

    @AllArgsConstructor
    @NoArgsConstructor
    @Data
    public static class ExchangeDataInput{
        private String clazz ;

        private String insertData ;

    }


    @Resource
    private EruptPlatformService eruptPlatformService ;

    @Resource
    private RedisMQService redisMQService;

    @Resource
    private EruptCacheRedis eruptCacheRedis;



    /**
     * 交换数据
     * @param jsonObject
     * @return
     */
    @PutMapping("/erupt-api/exchange/data/exchange")
    public Map<String,Object> exchangeData(@RequestBody JSONObject jsonObject){
        long start = System.currentTimeMillis();
        new Thread(() -> {
            ExchangeDataInput input = JSONUtil.toBean(jsonObject,ExchangeDataInput.class);
            if(input == null){
                NotifyUtils.showWarnMsg("同步的数据不能为空");
            }
            String clazz = input.getClazz();
            if(StringUtils.isEmpty(clazz)){
                NotifyUtils.showWarnMsg("clazz 不能为空");
            }
            //判断clazz存在不存在
            if(!BUSINESS_CODE.containsKey(clazz)){
                return ;
            }
            String insertData = input.getInsertData();
            if(StringUtils.isEmpty(insertData)){
                NotifyUtils.showWarnMsg("insertData 不能为空");
            }else{
                Boolean underLine = jsonObject.getBool("underLine");
                underLine = underLine == null || underLine;
                //转成数组
                if(insertData.startsWith("[")){
                    List<Map> mapList = JSONUtil.toList(insertData, Map.class);
                    for(Map parseMap : mapList){
                        //生产
                        produce(parseMap,clazz, underLine) ;
                    }
                }else{
                    //转成map
                    Map parseMap = JSONUtil.toBean(insertData, Map.class);
                    //生产
                    produce(parseMap,clazz,underLine) ;
                }

            }
        }).start();
        return  new HashMap<>() ;
    }

    /**
     * 转字段
     * @param parseMap
     * @return
     */
    private  void produce(Map parseMap,String clazz,boolean underLine){
        Map map = new HashMap();
        for(Object key : parseMap.keySet()){
            //key 转下划线和大写
            String underScoreKey = underScoreName((String)key,underLine);
            //设置下true 为1，0为false
            Object value = parseMap.get(key);
            if( value instanceof Boolean){
                if(Boolean.TRUE.equals(value)){
                    value = 1;
                }else{
                    value = 0 ;
                }
            }
            if (!(value instanceof List || value instanceof Set)){
                map.put(underScoreKey,value);
            }

        }

        String service = ExchangeDataController.BUSINESS_CODE.get(clazz);
        String insertData = JSONUtil.toJsonStr(map);
        //生产
        redisMQService.produce(EXCHANGE_DATA_EXCHANGE_KEY.replace("{SERVICE}",service),insertData);
    }

    /**
     * 驼峰转大写加下划线
     * @param name
     * @return
     */

    public static String underScoreName(String name,Boolean underLine) {
        StringBuilder result = new StringBuilder();
        if (name != null && name.length() > 0) {
            // 将第一个字符处理成大写
            result.append(name.substring(0, 1).toUpperCase());
            // 循环处理其余字符
            for (int i = 1; i < name.length(); i++) {
                String s = name.substring(i, i + 1);
                // 在大写字母前添加下划线
                if (underLine && s.equals(s.toUpperCase()) && !Character.isDigit(s.charAt(0))) {
                    result.append("_");
                }
                // 其他字符直接转成大写
                result.append(s.toUpperCase());
            }
        }
        return result.toString().trim();
    }


    @Override
    public void success(String result, String param) {

    }

    @Override
    public void error(Throwable throwable, String param) {

    }

    @Override
    public String exec(String code, String param) {

        StringBuilder result = new StringBuilder();
        String ip = eruptPlatformService.getOption(ExchangeDataController.TOKEN_IP).getAsString() ;
        String url = eruptPlatformService.getOption(ExchangeDataController.EXCHANGE_DATA_URL).getAsString() ;

        //循环去取对应的key
        for(String key : ExchangeDataController.BUSINESS_CODE.keySet()){
            String service = ExchangeDataController.BUSINESS_CODE.get(key);
            //获取token ,这里面已经有token和appSecret了
            Map exchangeData = (Map) eruptCacheRedis.get(ExchangeDataController.EXCHANGE_DATA_TOKEN_KEY.replace("{SERVICE}",service));
            //若token为空，则不执行
            if(null == exchangeData){
                continue;
            }

            //消费
            String redisKey = EXCHANGE_DATA_EXCHANGE_KEY.replace("{SERVICE}", service);
            ListOperations<String, String> listOperations = this.eruptCacheRedis.getStringRedisTemplate().opsForList();
            //取出所有的数据
            Long size = listOperations.size(redisKey);
            if(null == size || 0 == size){
                continue;
            }
            List data = new ArrayList();
            for(int i = 0 ; i < size ; i++){
                String dataStr =  redisMQService.consume(redisKey,String.class) ;
                data.add(JSONUtil.toBean(dataStr,Map.class));
            }


            exchangeData.put("service",service);
            exchangeData.put("INSERTDATA",JSONUtil.toJsonStr(data));

            try {
                HttpResponse response = HttpUtil.createPost(ip + url).form(exchangeData).execute() ;
                String body = response.body();
                JSONObject res = JSONUtil.toBean(body, JSONObject.class);
                if(0==(Integer)res.get("code")){
                    result.append("接口地址：").append(ip).append(url).append(",请求参数：").append(JSONUtil.toJsonStr(exchangeData)).append(",返回参数：").append(JSONUtil.toJsonStr(body));
                }else{
                    result.append("<span class='e-tag'><font color='red'>").append("类：").append(key).append(",接口地址：").append(ip).append(url).append(",请求参数：").append(JSONUtil.toJsonStr(exchangeData)).append(",返回参数：").append(JSONUtil.toJsonStr(body)).append("</font></span>");
                }
                log.info("接口地址：" + ip + url + ",请求参数：" + JSONUtil.toJsonStr(exchangeData) + ",返回参数：" + JSONUtil.toJsonStr(body));

            }catch (Exception e){
                result.append("<span class='e-tag'><font color='red'>").append("类：").append(key).append(",接口地址：").append(ip).append(url).append(",请求参数：").append(JSONUtil.toJsonStr(exchangeData)).append(",请求失败了").append("</font></span>") ;
                e.printStackTrace();
                log.error(e.getMessage());
            }
        }

        return result.toString() ;
    }
}
