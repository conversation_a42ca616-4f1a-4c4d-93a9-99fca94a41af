package com.daliangang.workbench.entity;

import com.daliangang.workbench.proxy.CompanyProblemListProxy;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import xyz.erupt.annotation.Erupt;
import xyz.erupt.annotation.EruptField;
import xyz.erupt.annotation.sub_erupt.Power;
import xyz.erupt.annotation.sub_erupt.RowOperation;
import xyz.erupt.annotation.sub_field.Edit;
import xyz.erupt.annotation.sub_field.EditType;
import xyz.erupt.annotation.sub_field.View;
import xyz.erupt.annotation.sub_field.ViewType;
import xyz.erupt.jpa.model.BaseModel;
import xyz.erupt.toolkit.db.Comment;
import xyz.erupt.upms.handler.GotoPageHandler;

import javax.persistence.Entity;
import javax.persistence.Table;

@Erupt(name = "企业问题清单",
        power = @Power(add = false,edit = false,delete = false),
        dataProxy = {CompanyProblemListProxy.class},
        rowOperation = {
                @RowOperation(title = "问题企业清单", icon = "fa fa-mail-reply",
                        confirm=false,
                        operationParam="CompanyProblemCount",
                        operationHandler = GotoPageHandler.class,
                        mode = RowOperation.Mode.BUTTON),

        })
@Table(name = "tb_company_problem_list")
@Entity
@Getter
@Setter
@Comment("企业问题清单")
@ApiModel("企业问题清单")

public class CompanyProblemList extends BaseModel {

    @EruptField(
            views = @View(title = "企业id",show = false),
            edit = @Edit(title = "企业id", type = EditType.INPUT, notNull = true,show = false))
    @Comment("企业id")
    @ApiModelProperty("企业id")
    private String companyId;

    @EruptField(
            views = @View(title = "企业code",show = false),
            edit = @Edit(title = "企业code", type = EditType.INPUT, notNull = true,show = false))
    @Comment("企业code")
    @ApiModelProperty("企业code")
    private String companyCode;

    @EruptField(
            views = @View(title = "企业名称",show = true),
            edit = @Edit(title = "企业名称", type = EditType.INPUT, notNull = true))
    @Comment("企业名称")
    @ApiModelProperty("企业名称")
    private String companyName;

    @EruptField(
            views = @View(title = "安全生产责任制",show = true),
            edit = @Edit(title = "安全生产责任制", type = EditType.INPUT, notNull = true))
    @Comment("安全生产责任制")
    @ApiModelProperty("安全生产责任制")
    private String checkFillSubmited;

    @EruptField(
            views = @View(title = "安全承诺公告",show = true),
            edit = @Edit(title = "安全承诺公告", type = EditType.INPUT, notNull = true))
    @Comment("安全承诺公告")
    @ApiModelProperty("安全承诺公告")
    private String releaseSubmited;

    @EruptField(
            views = @View(title = "可靠性报告单",show = true),
            edit = @Edit(title = "可靠性报告单", type = EditType.INPUT, notNull = true))
    @Comment("可靠性报告单")
    @ApiModelProperty("可靠性报告单")
    private String reportSubmited;

    @EruptField(
            views = @View(title = "日检查",show = true),
            edit = @Edit(title = "日检查", type = EditType.INPUT, notNull = true))
    @Comment("日检查")
    @ApiModelProperty("日检查")
    private String dailyInspectionSubmited;


    @EruptField(
            views = @View(title = "周报告",show = true),
            edit = @Edit(title = "周报告", type = EditType.INPUT, notNull = true))
    @Comment("周报告")
    @ApiModelProperty("周报告")
    private String weeklyReportSubmited;

    @EruptField(
            views = @View(title = "月调度",show = true),
            edit = @Edit(title = "月调度", type = EditType.INPUT, notNull = true))
    @Comment("月调度")
    @ApiModelProperty("月调度")
    private String monthlySchedulingSubmited;

    @EruptField(
            views = @View(title = "码头结构检测",show = true,
                    type = ViewType.LINK_HREF,
                    linkName="WharfStructureInspection",
                    condition="[{'code':'companyName','searchCode':'company'},{'code':'checkStatus','searchCode':'checkStatus'}]"),
            edit = @Edit(title = "码头结构检测", type = EditType.INPUT, notNull = true))
    @Comment("码头结构检测")
    @ApiModelProperty("码头结构检测")
    private String wharfStructureInspectionCount;

    @EruptField(
            views = @View(title = "消防设施检测",show = true,
                    type = ViewType.LINK_HREF,
                    linkName="FacilityInspection",
                    condition="[{'code':'companyName','searchCode':'company'},{'code':'checkStatus','searchCode':'checkStatus'}]"),
            edit = @Edit(title = "消防设施检测", type = EditType.INPUT, notNull = true))
    @Comment("消防设施检测")
    @ApiModelProperty("消防设施检测")
    private String facilityInspectionCount;

    @EruptField(
            views = @View(title = "防雷装置检测",show = true,
                    type = ViewType.LINK_HREF,
                    linkName="LightningProtection",
                    condition="[{'code':'companyName','searchCode':'company'},{'code':'checkStatus','searchCode':'checkStatus'}]"),
            edit = @Edit(title = "防雷装置检测", type = EditType.INPUT, notNull = true))
    @Comment("防雷装置检测")
    @ApiModelProperty("防雷装置检测")
    private String lightningProtectionCount;

    @EruptField(
            views = @View(title = "压力容器检测",show = true,
                    type = ViewType.LINK_HREF,
                    linkName="PressureVessel",
                    condition="[{'code':'companyName','searchCode':'company'},{'code':'checkStatus','searchCode':'checkStatus'}]"),
            edit = @Edit(title = "压力容器检测", type = EditType.INPUT, notNull = true))
    @Comment("压力容器检测")
    @ApiModelProperty("压力容器检测")
    private String pressureVesselCount;

    @EruptField(
            views = @View(title = "储罐检测",show = true,
                    type = ViewType.LINK_HREF,
                    linkName="TankInspection",
                    condition="[{'code':'companyName','searchCode':'company'},{'code':'checkStatus','searchCode':'checkStatus'}]"),
            edit = @Edit(title = "储罐检测", type = EditType.INPUT, notNull = true))
    @Comment("储罐检测")
    @ApiModelProperty("储罐检测")
    private String tankInspectionCount;

    @EruptField(
            views = @View(title = "压力管道检测",show = true,
                    type = ViewType.LINK_HREF,
                    linkName="Penstock",
                    condition="[{'code':'companyName','searchCode':'company'},{'code':'checkStatus','searchCode':'checkStatus'}]"),
            edit = @Edit(title = "压力管道检测", type = EditType.INPUT, notNull = true))
    @Comment("压力管道检测")
    @ApiModelProperty("压力管道检测")
    private String penstockCount;

    @EruptField(
            views = @View(title = "输油臂（软管）检测",show = true,
                    type = ViewType.LINK_HREF,
                    linkName="Hose",
                    condition="[{'code':'companyName','searchCode':'company'},{'code':'checkStatus','searchCode':'checkStatus'}]"),
            edit = @Edit(title = "输油臂（软管）检测", type = EditType.INPUT, notNull = true))
    @Comment("输油臂（软管）检测")
    @ApiModelProperty("输油臂（软管）检测")
    private String hoseCount;

    @EruptField(
            views = @View(title = "压力表检测",show = true,
                    type = ViewType.LINK_HREF,
                    linkName="PressureGauge",
                    condition="[{'code':'companyName','searchCode':'company'},{'code':'checkStatus','searchCode':'checkStatus'}]"),
            edit = @Edit(title = "压力表检测", type = EditType.INPUT, notNull = true))
    @Comment("压力表检测")
    @ApiModelProperty("压力表检测")
    private String pressureGaugeCount;

    @EruptField(
            views = @View(title = "安全阀检测",show = true,
                    type = ViewType.LINK_HREF,
                    linkName="SafetyValve",
                    condition="[{'code':'companyName','searchCode':'company'},{'code':'checkStatus','searchCode':'checkStatus'}]"),
            edit = @Edit(title = "安全阀检测", type = EditType.INPUT, notNull = true))
    @Comment("安全阀检测")
    @ApiModelProperty("安全阀检测")
    private String safetyValveCount;

    @EruptField(
            views = @View(title = "可燃气体报警器检测",show = true,
                    type = ViewType.LINK_HREF,
                    linkName="CombustibleGas",
                    condition="[{'code':'companyName','searchCode':'company'},{'code':'checkStatus','searchCode':'checkStatus'}]"),
            edit = @Edit(title = "可燃气体报警器检测", type = EditType.INPUT, notNull = true))
    @Comment("可燃气体报警器检测")
    @ApiModelProperty("可燃气体报警器检测")
    private String combustibleGasCount;

    @EruptField(
            views = @View(title = "起重机械检测",show = true,
                    type = ViewType.LINK_HREF,
                    linkName="HoistingMachinery",
                    condition="[{'code':'companyName','searchCode':'company'},{'code':'checkStatus','searchCode':'checkStatus'}]"),
            edit = @Edit(title = "起重机械检测", type = EditType.INPUT, notNull = true))
    @Comment("起重机械检测")
    @ApiModelProperty("起重机械检测")
    private String hoistingMachineryCount;

    @EruptField(
            views = @View(title = "其他检测",show = true,
                    type = ViewType.LINK_HREF,
                    linkName="OtherTests",
                    condition="[{'code':'companyName','searchCode':'company'},{'code':'checkStatus','searchCode':'checkStatus'}]"),
            edit = @Edit(title = "其他检测", type = EditType.INPUT, notNull = true))
    @Comment("其他检测")
    @ApiModelProperty("其他检测")
    private String otherTestsCount;

    @EruptField(
            views = @View(title = "过期状态，固定为已过期",show = false),
            edit = @Edit(title = "过期状态，固定为已过期", type = EditType.INPUT, notNull = false,show = false))
    @Comment("过期状态，固定为已过期")
    @ApiModelProperty("过期状态，固定为已过期")
    private String checkStatus ;

}
