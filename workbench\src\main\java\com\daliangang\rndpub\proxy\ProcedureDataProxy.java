/*
 * Copyright © 2020-2035 yundingyun All rights reserved.
 */
package com.daliangang.rndpub.proxy;

import com.daliangang.rndpub.RndpubConst;
import com.daliangang.rndpub.entity.*;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.stereotype.Service;
import xyz.erupt.annotation.fun.DataProxy;
import xyz.erupt.core.module.MetaUserinfo;
import xyz.erupt.core.service.EruptService;
import xyz.erupt.jpa.dao.EruptDao;
import xyz.erupt.toolkit.db.EruptResultMap;
import xyz.erupt.toolkit.remote.RemoteProxyService;
import xyz.erupt.toolkit.utils.EruptDaoUtils;
import xyz.erupt.toolkit.utils.NotifyUtils;
import xyz.erupt.toolkit.utils.SqlUtils;
import xyz.erupt.upms.model.EruptDictItem;
import xyz.erupt.upms.util.EruptDictUtils;

import javax.annotation.Resource;
import javax.transaction.Transactional;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class ProcedureDataProxy implements DataProxy<Procedure> {

    @Resource
    private RemoteProxyService remoteProxyService;

    @Resource
    private EruptDao eruptDao;

    @Resource
    private EruptService eruptService;

    @Override
    public void beforeAdd(Procedure procedure) {
        //this.produce(procedure);
        Procedure procedure1 = eruptDao.queryEntity(Procedure.class, "name=" + SqlUtils.wrapStr(procedure.getName()));
        if (ObjectUtils.isNotEmpty(procedure1)) {
            NotifyUtils.showErrorDialog("检查名称重复");
        }
        procedure.setNumberOfEnterprises(0);
        procedure.setInspectionExpert(0);
        procedure.setInspectorsNo(0);
        procedure.setCreateTime(LocalDateTime.now());
    }

    @Override
    public void addBehavior(Procedure procedure) {
//        procedure.setInspectionDate(new Date(System.currentTimeMillis()));
//        procedure.setName(new Date(System.currentTimeMillis())+"安全生产检查");
        procedure.setInspectionDate(new Date(System.currentTimeMillis()));
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
        procedure.setName(simpleDateFormat.format(new Date()) + "安全生产检查");
    }

    @Transactional
    @Override
    public void afterAdd(Procedure procedure) {
        MetaUserinfo remoteUserInfo = remoteProxyService.getRemoteUserInfo();
        String orgCode = SqlUtils.wrapIn(remoteUserInfo.getAuth());

        //查询企业上次抽取时间
        String lastCheckDateSql = " select enterprise_id ,max(current_spot_check)current_spot_check from tb_enterprise_information " +
                " group by enterprise_id";
        List<Map<String, Object>> lastCheckDates = eruptDao.getJdbcTemplate().queryForList(lastCheckDateSql);

        //同步企业数据-------------原来方法
///*//        Page page = remoteProxyService.getEruptData("Enterprise", 1, Short.MAX_VALUE, "name");
////        List<EruptResultMap> enterprises = EruptDaoUtils.convert(page.getList(), EruptResultMap.class);*/
//        List<EruptResultMap> enterprises = EruptDaoUtils.selectOnes("select * from tb_enterprise order by name", EruptResultMap.class);
//        for (EruptResultMap vo : enterprises) {
//            EnterpriseInformation info = new EnterpriseInformation();
//            info.setEnterpriseId(vo.getString("id"));
//            info.setEnterpriseName(vo.getString("name"));
//            info.setLegalRepresentative(vo.getString("representative"));
//            info.setPortArea(vo.getString("port_area"));
//            info.setType(vo.getString("type"));
//            info.setOrgCode(vo.getString("org_code"));
//            List<Map<String, Object>> collect=new ArrayList<>();
///*//            info.setCompetentDepartment(vo.getAs("imDepartment", JsonObject.class).toString());
////            List<Map<String, Object>> collect = lastCheckDates.stream().filter(item -> {
////                return item.get("enterprise_id").equals(info.getEnterpriseId());
////            }).collect(Collectors.toList());*/
//            for (Map<String, Object> lastCheckDate : lastCheckDates) {
//                if (lastCheckDate.get("enterprise_id")!=null&&lastCheckDate.get("enterprise_id").equals(info.getEnterpriseId())){
//                    collect.add(lastCheckDate);
//                }
//            }
//            Date lastSpotCheck = collect.size() > 0 ? (Date) collect.get(0).get("current_spot_check") : null;
//            info.setLastSpotCheck(lastSpotCheck);
//            info.setProcedure(procedure);
//            info.setState(false);
//            eruptDao.persist(info);
//        }
     //--------------------------------------------------------------------------新 同步企业数据  数据范围改成监管范围下的全部企业
        List<EruptResultMap> enterprises = EruptDaoUtils.selectOnes("select * from tb_enterprise where org_code "+orgCode+" order by name", EruptResultMap.class);
        for (EruptResultMap vo : enterprises) {
            EnterpriseInformation info = new EnterpriseInformation();
            info.setEnterpriseId(vo.getString("id"));
            info.setEnterpriseName(vo.getString("name"));
            info.setLegalRepresentative(vo.getString("representative"));
            info.setPortArea(vo.getString("port_area"));
            info.setType(vo.getString("type"));
            info.setOrgCode(vo.getString("org_code"));
            List<Map<String, Object>> collect=new ArrayList<>();
//            info.setCompetentDepartment(vo.getAs("imDepartment", JsonObject.class).toString());
//            List<Map<String, Object>> collect = lastCheckDates.stream().filter(item -> {
//                return item.get("enterprise_id").equals(info.getEnterpriseId());
//            }).collect(Collectors.toList());
            for (Map<String, Object> lastCheckDate : lastCheckDates) {
                if (lastCheckDate.get("enterprise_id")!=null&&lastCheckDate.get("enterprise_id").equals(info.getEnterpriseId())){
                    collect.add(lastCheckDate);
                }
            }
            Date lastSpotCheck = collect.size() > 0 ? (Date) collect.get(0).get("current_spot_check") : null;
            info.setLastSpotCheck(lastSpotCheck);
            info.setProcedure(procedure);
            info.setState(false);
            eruptDao.persist(info);
        }

        List<EruptDictItem> checkScopes = EruptDictUtils.getDictList("checkScope");
        //同步专家数据
        List<Expert> expertInformationList = eruptDao.queryEntityList(Expert.class, "expertAudidtState = '" + RndpubConst.EXPERT_STATUS_PASS + "' and useState=true");
        for (Expert expertInformation : expertInformationList) {
            ExpertInformation item = EruptDaoUtils.cast(expertInformation, ExpertInformation.class);
            item.setId(null);
            item.setProcedure(procedure);
            item.setState(false);
            List<EruptDictItem> items = checkScopes.stream().filter(c -> c.getCode().equals(item.getCheckScope())).collect(Collectors.toList());
            if (ObjectUtils.isNotEmpty(items)) {
                item.setCheckScope(items.get(0).getCode());
            }
            eruptDao.persist(item);
        }

        //同步检查人员数据
        List<CheckPerson> checkPersonList = eruptDao.queryEntityList(CheckPerson.class, "checkPerson=true and state=true");
        for (CheckPerson checkperson : checkPersonList) {
            Inspector item = EruptDaoUtils.cast(checkperson, Inspector.class);
            item.setId(null);
            item.setProcedure(procedure);
            item.setState(false);
            item.setCompany(checkperson.getBelongCompany());
            item.setInspectorId(String.valueOf(checkperson.getId()));
            item.setCertificateNo(checkperson.getCertificateNo());
            eruptDao.persist(item);
        }


        //同步检查项
        List<InspectionItemsManagement> inspectionItemsManagements = eruptDao.queryEntityList(InspectionItemsManagement.class);
        for (InspectionItemsManagement management : inspectionItemsManagements) {
            InspectionItems item = EruptDaoUtils.cast(management, InspectionItems.class);
            item.setId(null);
            item.setProcedure(procedure);
            item.setState(false);
            eruptDao.persist(item);
        }
    }

    @Override
    @Transactional
    public void beforeDelete(Procedure procedure) {
        //判断有无检查问题
        boolean check = check(procedure);
        if(check){
            NotifyUtils.showWarnMsg("该检查流程有检查问题，不可删除");
        }
        //删除关联数据
        EruptDaoUtils.updateNoForeignKeyChecks(
                "delete from tb_enterprise_information where procedure_id=" + procedure.getId(),
                "delete from tb_expert_information where procedure_id=" + procedure.getId(),
                "delete from tb_inspector where procedure_id=" + procedure.getId(),
                "delete from tb_inspection_items where procedure_id=" + procedure.getId());
    }

    @Override
    @Transactional
    public void afterFetch(Collection<Map<String, Object>> list) {
        List<Procedure> procedures = EruptDaoUtils.convert(list, Procedure.class);
        for (Procedure procedure : procedures) {
            if (ObjectUtils.isEmpty(procedure.getId())) {
                break;
            }
            this.produce(procedure);
            EruptDaoUtils.updateAfterFetch(list, procedure.getId(), "numberOfEnterprises", procedure.getNumberOfEnterprises());
        }
    }

    @Transactional
    @Override
    public void beforeUpdate(Procedure procedure) {
        this.produce(procedure);
    }

    @Transactional
    private void produce(Procedure procedure) {
        String countSql = "select count(*) as count from tb_enterprise_information where procedure_id=" + procedure.getId() + " and state=1";
        procedure.setNumberOfEnterprises(EruptDaoUtils.selectMap(countSql).getInt("count"));


        eruptDao.merge(procedure);

//        if(null != procedure.getEnterprise()){
//            procedure.setNumberOfEnterprises(procedure.getEnterprise().size());
//        }
//        if(null != procedure.getExpertInfo()){
//            procedure.setInspectionExpert(procedure.getExpertInfo().size());
//        }
//        if(null != procedure.getInspectorsInfo()){
//            procedure.setInspectorsNo(procedure.getInspectorsInfo().size());
//        }

    }


    public boolean check(Procedure procedure){
        List<InspectionResults> inspectionResults = eruptDao.queryEntityList(InspectionResults.class, "inspection_name = " + procedure.getId());
        return inspectionResults != null && inspectionResults.size() != 0;

    }
}
