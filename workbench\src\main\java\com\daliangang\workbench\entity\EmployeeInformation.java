/*
 * Copyright © 2020-2035 yundingyun All rights reserved.
 */
package com.daliangang.workbench.entity;

import com.daliangang.core.DaliangangContext;
import com.daliangang.workbench.operation.EmployeeInformationEnableHandler;
import com.daliangang.workbench.operation.EmployeeInformationResetPasswordHandler;
import com.daliangang.workbench.proxy.EmployeeInformationDataProxy;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import xyz.erupt.annotation.Erupt;
import xyz.erupt.annotation.EruptField;
import xyz.erupt.annotation.expr.ExprBool;
import xyz.erupt.annotation.sub_erupt.Power;
import xyz.erupt.annotation.sub_erupt.RowOperation;
import xyz.erupt.annotation.sub_field.Edit;
import xyz.erupt.annotation.sub_field.EditType;
import xyz.erupt.toolkit.db.Comment;

import javax.persistence.*;
import java.util.Set;
import java.util.StringTokenizer;

@Erupt(name = "员工管理", closeTreeView = true, power = @Power(importable = true),
        dataProxy = EmployeeInformationDataProxy.class
        , rowOperation = {
        @RowOperation(confirm = false, title = "启用", icon = "fa fa-toggle-off", ifExpr = "item.state=='禁用'", ifExprBehavior = RowOperation.IfExprBehavior.HIDE, operationHandler = EmployeeInformationEnableHandler.class, operationParam = "on", mode = RowOperation.Mode.MULTI),
        @RowOperation(confirm = false, title = "禁用", icon = "fa fa-toggle-on", ifExpr = "item.state=='启用'", ifExprBehavior = RowOperation.IfExprBehavior.HIDE, operationHandler = EmployeeInformationEnableHandler.class, operationParam = "off", mode = RowOperation.Mode.MULTI),
        @RowOperation(title = "重置密码", icon = "fa fa-key", operationHandler = EmployeeInformationResetPasswordHandler.class, mode = RowOperation.Mode.SINGLE),
})
@Table(name = "tb_employee_information")
@Entity
@Getter
@Setter
@Comment("员工管理")
@ApiModel("员工管理")
public class EmployeeInformation extends EmployeeView implements ExprBool.ExprHandler {

    @EruptField(
            edit = @Edit(title = "员工证书", type = EditType.TAB_TABLE_ADD,
                    show = false
                    /*showBy = @ShowBy(dependField = "checkPerson", expr = "value==false")*/))
    @OneToMany(cascade = CascadeType.ALL, orphanRemoval = true)
    @OrderBy
    @JoinColumn(name = "employee_information_id")
    @Comment("员工证书")
    @ApiModelProperty("员工证书")
    private Set<EmployeeCertificate> certificates;

    public boolean hasRole(String roleName) {
        StringTokenizer st = new StringTokenizer(this.getOwnerRole(), "|");
        while (st.hasMoreTokens()) {
            String curr = st.nextToken();
            if (curr.equals(roleName)) return true;
        }
        return false;
    }

    @Override
    public boolean handler(boolean expr, String[] params) {
        return DaliangangContext.isEnterpriseUser();
    }
}
