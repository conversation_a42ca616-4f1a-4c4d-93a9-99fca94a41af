/*
 * Copyright © 2020-2035 yundingyun All rights reserved.
 */
package com.daliangang.device.entity;

import com.daliangang.core.CompanyRenderHandler;
import com.daliangang.device.proxy.StructureInspectionDataProxy;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import xyz.erupt.annotation.Erupt;
import xyz.erupt.annotation.EruptField;
import xyz.erupt.annotation.PreDataProxy;
import xyz.erupt.annotation.expr.ExprBool;
import xyz.erupt.annotation.sub_erupt.Power;
import xyz.erupt.annotation.sub_erupt.RowOperation;
import xyz.erupt.annotation.sub_field.Edit;
import xyz.erupt.annotation.sub_field.EditType;
import xyz.erupt.annotation.sub_field.Readonly;
import xyz.erupt.annotation.sub_field.View;
import xyz.erupt.annotation.sub_field.sub_edit.*;
import xyz.erupt.mns.core.DelayMsg;
import xyz.erupt.toolkit.attachment.AttachmentDownloadHandler;
import xyz.erupt.toolkit.colorfont.ColorStateTimeFontDataProxy;
import xyz.erupt.toolkit.db.Comment;
import xyz.erupt.toolkit.handler.EruptDictCodeChoiceFetchHandler;
import xyz.erupt.toolkit.handler.RemoteCallTagFetchHandler;
import xyz.erupt.toolkit.handler.RemoteEntityChoiceFetchHandler;
import xyz.erupt.upms.dict.Dict;
import xyz.erupt.upms.dict.DictItem;
import xyz.erupt.upms.dict.Dicts;
import xyz.erupt.upms.model.auth.DataAuthHandler;
import xyz.erupt.upms.model.auth.DataAuthModel;

import javax.persistence.Entity;
import javax.persistence.Lob;
import javax.persistence.Table;

@Erupt(
        name = "码头结构检测",
        power = @Power(add = true, delete = true, export = false, importable = true, viewDetails = true, edit = true),
        rowOperation = {
                @RowOperation(title = "下载附件", icon = "fa fa-download", operationHandler = AttachmentDownloadHandler.class, mode = RowOperation.Mode.SINGLE)
        },
        dataProxy = StructureInspectionDataProxy.class
)
@Table(name = "tb_wharf_structure_inspection")
@Entity
@Getter
@Setter
@Comment("码头结构检测")
@ApiModel("码头结构检测")
@Dicts({
        @Dict(code = "technicalStatus", name = "技术状态等级", value = {
                @DictItem(code = "ClassI", value = "一类（好）"),
                @DictItem(code = "ClassII", value = "二类（较好）"),
                @DictItem(code = "ClassIII", value = "三类（较差）"),
                @DictItem(code = "ClassIV", value = "四类（差）"),
                @DictItem(code = "ClassV", value = "五类（危险）")
        }),
        @Dict(code = "detectionRangeDock", name = "检测报告检测范围码头", value = {
                @DictItem(code = "dock", value = "码头")

        })
})
@PreDataProxy(ColorStateTimeFontDataProxy.class)
@DelayMsg(timeKey = "validityPeriod")
public class WharfStructureInspection extends DataAuthModel {

    @EruptField(views = @View(title = "企业名称"),
            edit = @Edit(title = "企业名称", search = @Search(ifRender = @ExprBool(exprHandler = CompanyRenderHandler.class)),
                    readonly = @Readonly(exprHandler = DataAuthHandler.class), type = EditType.CHOICE, notNull = true, choiceType = @ChoiceType(fullSpan = true,fetchHandler = RemoteEntityChoiceFetchHandler.class, fetchHandlerParams = {"main", "Enterprise", "org_code,name"})))
    @Comment("企业名称")
    @ApiModelProperty("企业名称")
    private String company;
    @EruptField(
            views = @View(title = "检测报告名称"),
            edit = @Edit(title = "检测报告名称", type = EditType.INPUT, search = @Search(vague = true), notNull = true,
                    inputType = @InputType))
    @Comment("检测报告名称")
    @ApiModelProperty("检测报告名称")
    private String testReportName;

    @EruptField(
            views = @View(title = "技术状态等级"),
            edit = @Edit(title = "技术状态等级", type = EditType.CHOICE, notNull = true,
                    choiceType = @ChoiceType(fetchHandler = EruptDictCodeChoiceFetchHandler.class, fetchHandlerParams = "technicalStatus")))
    @Comment("技术状态等级")
    @ApiModelProperty("技术状态等级")
    private String grade;

    @EruptField(
            views = @View(title = "检测报告有效期限",width = "200px"),
            edit = @Edit(title = "检测报告有效期限", type = EditType.DATE, notNull = true,
                    dateType = @DateType))
    @Comment("检测报告有效期限")
    @ApiModelProperty("检测报告有效期限")
    private java.util.Date validityPeriod;

    @EruptField(views = @View(title = "状态"))
    @Comment("状态")
    @ApiModelProperty("状态")
    private String state;

    @EruptField(views = @View(title = "状态",show = false),
            edit = @Edit(title = "状态", type = EditType.CHOICE, search = @Search, show = false,
                    choiceType = @ChoiceType(fetchHandler = EruptDictCodeChoiceFetchHandler.class, fetchHandlerParams = "checkStatus")
            )
    )
    @Comment("状态")
    @ApiModelProperty("状态")
    private String checkStatus;

    @EruptField(
            views = @View(title = "检测报告检测范围",show = false),
            edit = @Edit(title = "检测报告检测范围",
                    type = EditType.TAGS,
                    notNull = false,
                    show = false,
//                    tagsType = @TagsType(
//                            fetchHandler = EruptDictTagFetchHandler.class,
//                            fetchHandlerParams = "detectionRange"
//                    )
                    tagsType = @TagsType(
                            fetchHandler = RemoteCallTagFetchHandler.class, allowExtension = false,
                            fetchHandlerParams = {"main", "erupt-api/get/wharfName"}
                    )
            )
    )

    @Comment("检测报告检测范围")
    @ApiModelProperty("检测报告检测范围")
    private String detectionRange;

    @Lob
    @EruptField(
            views = @View(title = "说明",show = false),
            edit = @Edit(title = "说明", type = EditType.TEXTAREA,show = false,
                    inputType = @InputType))
    @Comment("说明")
    @ApiModelProperty("说明")
    private String description;

    @EruptField(
            views = @View(title = "检测报告附件", show = false),
            edit = @Edit(title = "检测报告附件", type = EditType.ATTACHMENT, notNull = true,
                    attachmentType = @AttachmentType(type = AttachmentType.Type.BASE, maxLimit = 20, fileTypes = {".pdf", ".png", ".jpg"})))
    @Comment("检测报告附件")
    @ApiModelProperty("检测报告附件")
    private String reportAttachments;

}
