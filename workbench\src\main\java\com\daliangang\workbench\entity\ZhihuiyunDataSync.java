package com.daliangang.workbench.entity;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import xyz.erupt.annotation.Erupt;
import xyz.erupt.annotation.EruptField;
import xyz.erupt.annotation.config.Comment;
import xyz.erupt.annotation.sub_erupt.Power;
import xyz.erupt.annotation.sub_field.Edit;
import xyz.erupt.annotation.sub_field.EditType;
import xyz.erupt.annotation.sub_field.Readonly;
import xyz.erupt.annotation.sub_field.View;
import xyz.erupt.annotation.sub_field.sub_edit.ChoiceType;
import xyz.erupt.annotation.sub_field.sub_edit.InputType;
import xyz.erupt.annotation.sub_field.sub_edit.Search;
import xyz.erupt.toolkit.handler.RemoteEntityChoiceFetchHandler;
import xyz.erupt.upms.model.auth.DataAuthModelVo;

import javax.persistence.Entity;
import javax.persistence.Lob;
import javax.persistence.Table;

@Erupt(name = "智慧云对接",power = @Power(add = false,edit = false,delete = false))
@Entity
@Getter
@Setter
@Table(name = "tb_zhihuiyun")
@Comment("智慧云对接")
public class ZhihuiyunDataSync extends DataAuthModelVo {

    @EruptField(
            views = @View(title = "企业名称"),
            edit = @Edit(title = "企业名称", readonly = @Readonly(), type = EditType.CHOICE, notNull = false,search = @Search(),
                    choiceType = @ChoiceType(fullSpan = true,fetchHandler = RemoteEntityChoiceFetchHandler.class, fetchHandlerParams = {"main", "Enterprise", "org_code,name"})))
    @xyz.erupt.toolkit.db.Comment("企业名称")
    @ApiModelProperty("企业名称")
    private String company;

    @EruptField(
            views = @View(title = "操作"),
            edit = @Edit(title = "操作", readonly = @Readonly(), type = EditType.INPUT, notNull = false,search = @Search()))
    @xyz.erupt.toolkit.db.Comment("操作")
    @ApiModelProperty("操作")
    private String operate;

    @EruptField(
            views = @View(title = "erupt名称"),
            edit = @Edit(title = "erupt名称", readonly = @Readonly(), type = EditType.INPUT, notNull = false,search = @Search()))
    @xyz.erupt.toolkit.db.Comment("erupt名称")
    @ApiModelProperty("erupt名称")
    private String eruptName;

    @Lob
    @EruptField(
            views = @View(title = "原始数据"),
            edit = @Edit(title = "原始数据", type = EditType.CODE_EDITOR,
                    inputType = @InputType))
    @xyz.erupt.toolkit.db.Comment("原始数据")
    @ApiModelProperty("原始数据")
    private String beforeData;

    @Lob
    @EruptField(
            views = @View(title = "入库数据"),
            edit = @Edit(title = "入库数据", type = EditType.CODE_EDITOR,
                    inputType = @InputType))
    @xyz.erupt.toolkit.db.Comment("入库数据")
    @ApiModelProperty("入库数据")
    private String afterData;

    @Lob
    @EruptField(
            views = @View(title = "结果"),
            edit = @Edit(title = "结果", type = EditType.CODE_EDITOR,
                    inputType = @InputType))
    @xyz.erupt.toolkit.db.Comment("结果")
    @ApiModelProperty("结果")
    private String result;
}
