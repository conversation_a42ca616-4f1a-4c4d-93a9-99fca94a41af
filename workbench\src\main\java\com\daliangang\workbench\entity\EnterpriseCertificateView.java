package com.daliangang.workbench.entity;


import com.daliangang.core.CompanyRenderHandler;
import com.daliangang.workbench.operation.EnterpriseCertificateExpirationTimeUpdateHandler;
import com.daliangang.workbench.operation.EnterpriseCertificateIssueDateUpdateHandler;
import com.daliangang.workbench.proxy.EnterpriseCertificateViewDataProxy;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import xyz.erupt.annotation.Erupt;
import xyz.erupt.annotation.EruptField;
import xyz.erupt.annotation.expr.ExprBool;
import xyz.erupt.annotation.sub_erupt.Power;
import xyz.erupt.annotation.sub_erupt.RowOperation;
import xyz.erupt.annotation.sub_field.Edit;
import xyz.erupt.annotation.sub_field.EditType;
import xyz.erupt.annotation.sub_field.View;
import xyz.erupt.annotation.sub_field.sub_edit.*;
import xyz.erupt.jpa.model.MetaModel;
import xyz.erupt.toolkit.colorfont.ColorStateTimeFont;
import xyz.erupt.toolkit.colorfont.ColorStateTimeFontDataProxy;
import xyz.erupt.toolkit.db.Comment;
import xyz.erupt.toolkit.handler.EruptDictCodeChoiceFetchHandler;
import xyz.erupt.toolkit.handler.SqlChoiceFetchHandler;

import javax.persistence.Entity;
import javax.persistence.Lob;
import javax.persistence.Table;
import javax.persistence.Transient;

@Erupt(name = "企业附证查看", authVerify = true,
        orderBy = "createTime desc",
        power = @Power(add = true, delete = true, export = true, importable = true, viewDetails = true, edit = true),
        dataProxy = {EnterpriseCertificateViewDataProxy.class, ColorStateTimeFontDataProxy.class}
        , rowOperation = {
        @RowOperation(confirm = false, title = "修改发证日期",  icon = "fa fa-calendar-check-o", eruptClass = EnterpriseCertificateIssueDateUpdateHandler.IssueDateUpdate.class,operationHandler = EnterpriseCertificateIssueDateUpdateHandler.class, mode = RowOperation.Mode.MULTI),
        @RowOperation(confirm = false, title = "修改到期日期",  icon = "fa fa-calendar",eruptClass = EnterpriseCertificateExpirationTimeUpdateHandler.ExpirationTimeUpdate.class,operationHandler = EnterpriseCertificateExpirationTimeUpdateHandler.class, mode = RowOperation.Mode.MULTI),
})
@Table(name = "tb_enterprise_certificate")
@Entity
@Getter
@Setter
@Comment("企业附证")
@ApiModel("企业附证")
@ColorStateTimeFont(stateKey = "state", timeKey = "expirationTime", interval = 90)
public class EnterpriseCertificateView extends MetaModel {

    @EruptField(
            views = @View(title = "企业名称",show = true,ifRender = @ExprBool(exprHandler = CompanyRenderHandler.class)),
            edit = @Edit(title = "企业名称", search = @Search(ifRender = @ExprBool(exprHandler = CompanyRenderHandler.class) ),ifRender= @ExprBool(exprHandler = CompanyRenderHandler.class), show = true, type = EditType.CHOICE, notNull = true,
                    choiceType = @ChoiceType(fullSpan = true,fetchHandler = SqlChoiceFetchHandler.class, fetchHandlerParams = "select org_code,name from tb_enterprise")))
    @Comment("企业名称")
    @ApiModelProperty("企业名称")
    private String company;


    @EruptField(
            views = @View(title = "附证编号"),
            edit = @Edit(title = "附证编号", type = EditType.INPUT, notNull = true,
                    inputType = @InputType))
    @Comment("附证编号")
    @ApiModelProperty("附证编号")
    private String certificateNo;

    @EruptField(
            views = @View(title = "状态",width = "100px"),
            edit = @Edit(title = "状态",
//                    type = EditType.CHOICE,
                    notNull = false,
                    show = false
//                    choiceType = @ChoiceType(fullSpan = true, fetchHandler = EruptDictCodeChoiceFetchHandler.class, fetchHandlerParams = "expiredState")
            )
    )
    @Comment("状态")
    @ApiModelProperty("状态")
    private String state;

    @EruptField(
            views = @View(title = "作业范围"),
            edit = @Edit(title = "作业范围", type = EditType.INPUT, notNull = true,
                    inputType = @InputType))
    @Comment("作业范围")
    @ApiModelProperty("作业范围")
    private String workRange;

    @EruptField(
            views = @View(title = "作业方式"),
            edit = @Edit(title = "作业方式", type = EditType.INPUT, notNull = true,
                    inputType = @InputType))
    @Comment("作业方式")
    @ApiModelProperty("作业方式")
    private String operationMode;

    //	@EruptField(
//		views = @View(title = "货种名称"),
//		edit = @Edit(title = "货种名称", type = EditType.TAGS, notNull = true,
//		tagsType = @TagsType))
//	@Comment("货种名称")
//	@ApiModelProperty("货种名称")
//	private String goodsType;
    @EruptField(
            views = @View(title = "货种名称"),
            edit = @Edit(
                    title = "货种名称",
                    type = EditType.INPUT,
                    notNull = true
            )
    )
    @Comment("货种名称")
    @ApiModelProperty("货种名称")
    @Lob
    private String goodsType;

    @EruptField(
            views = @View(title = "发证日期"),
            edit = @Edit(title = "发证日期", type = EditType.DATE, notNull = true,
                    dateType = @DateType))
    @Comment("发证日期")
    @ApiModelProperty("发证日期")
    private java.util.Date issueDate;

    @EruptField(
            views = @View(title = "发证机关"),
            edit = @Edit(title = "发证机关", type = EditType.INPUT, notNull = true,
                    inputType = @InputType))
    @Comment("发证机关")
    @ApiModelProperty("发证机关")
    private String issuingAuthority;

    @EruptField(
            views = @View(title = "到期时间"),
            edit = @Edit(title = "到期时间", type = EditType.DATE, notNull = true,
                    dateType = @DateType))
    @Comment("到期时间")
    @ApiModelProperty("到期时间")
    private java.util.Date expirationTime;

    @EruptField(
            views = @View(title = "状态",show = false),
            edit = @Edit(title = "状态",
            search = @Search(), type = EditType.CHOICE,  notNull = false, show = false,
                    choiceType = @ChoiceType(fetchHandler = EruptDictCodeChoiceFetchHandler.class, fetchHandlerParams = "expiredState")
            )
    )
    @Comment("状态")
    @ApiModelProperty("状态")
    @Transient
    private String expiredState;

    @Lob
    @EruptField(
            views = @View(title = "附证扫描件", show = false),
            edit = @Edit(title = "附证扫描件", type = EditType.ATTACHMENT, notNull = false,
                    attachmentType = @AttachmentType(type = AttachmentType.Type.BASE, maxLimit = 20)))
    @Comment("附证扫描件")
    @ApiModelProperty("附证扫描件")
    private String scannedFile ;

    @Transient
    private Long enterpriseId ;
}
