/*
* Copyright © 2020-2035 yundingyun All rights reserved.
*/
package com.daliangang.device.entity;

import com.daliangang.core.CompanyRenderHandler;
import com.daliangang.device.proxy.LoadingDockDataProxy;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import xyz.erupt.annotation.Erupt;
import xyz.erupt.annotation.EruptField;
import xyz.erupt.annotation.expr.ExprBool;
import xyz.erupt.annotation.sub_erupt.Power;
import xyz.erupt.annotation.sub_field.Edit;
import xyz.erupt.annotation.sub_field.EditType;
import xyz.erupt.annotation.sub_field.Readonly;
import xyz.erupt.annotation.sub_field.View;
import xyz.erupt.annotation.sub_field.sub_edit.ChoiceType;
import xyz.erupt.annotation.sub_field.sub_edit.DateType;
import xyz.erupt.annotation.sub_field.sub_edit.InputType;
import xyz.erupt.annotation.sub_field.sub_edit.Search;
import xyz.erupt.toolkit.db.Comment;
import xyz.erupt.toolkit.handler.RemoteEntityChoiceFetchHandler;
import xyz.erupt.toolkit.handler.SqlChoiceFetchHandler;
import xyz.erupt.upms.model.auth.DataAuthHandler;
import xyz.erupt.upms.model.auth.DataAuthModel;

import javax.persistence.Entity;
import javax.persistence.Lob;
import javax.persistence.Table;

@Erupt(name = "装卸栈台管理",importTruncate = true, power = @Power(add = true, delete = true, export = false, importable = true, viewDetails = true, edit = true)
, dataProxy = LoadingDockDataProxy.class
, rowOperation = {})
@Table(name = "tb_loading_dock")
@Entity
@Getter
@Setter
@Comment("装卸栈台管理")
@ApiModel("装卸栈台管理")
public class LoadingDock extends DataAuthModel {
	@EruptField(
			views = @View(title = "企业名称"),
			edit = @Edit(title = "企业名称", search = @Search(ifRender = @ExprBool(exprHandler = CompanyRenderHandler.class)),
					readonly = @Readonly(exprHandler = DataAuthHandler.class),type = EditType.CHOICE, notNull = true,
					choiceType = @ChoiceType(fullSpan = true,reload = true,fetchHandler = RemoteEntityChoiceFetchHandler.class, fetchHandlerParams ={"main","Enterprise","org_code,name"})))
	@Comment("企业名称")
	@ApiModelProperty("企业名称")
	private String company;
	@EruptField(
		views = @View(title = "栈台名称"),
		edit = @Edit(title = "栈台名称", type = EditType.INPUT, search = @Search(vague = true), notNull = true,
		inputType = @InputType))
	@Comment("栈台名称")
	@ApiModelProperty("栈台名称")
	private String name;



//	@EruptField(
//		views = @View(title = "所属港区"),
//		edit = @Edit(title = "所属港区", type = EditType.CHOICE, search = @Search(vague = true), notNull = true,
//		choiceType = @ChoiceType(fetchHandler = RemoteCallChoiceFetchHandler.class, fetchHandlerParams ={"main","erupt-api/get/portareaName"})))
//
//	@Comment("所属港区")
//	@ApiModelProperty("所属港区")
//	private String portArea;

	@EruptField(
			views = @View(title = "所属港区"),
			edit = @Edit(title = "所属港区", type = EditType.CHOICE, notNull = true,readonly = @Readonly,search = @Search,
					choiceType = @ChoiceType(fetchHandler = SqlChoiceFetchHandler.class, fetchHandlerParams ={"select id,portarea_name from tb_port_area"})))
	@Comment("所属港区")
	@ApiModelProperty("所属港区")
	private String portArea;

	@EruptField(
		views = @View(title = "车台数"),
		edit = @Edit(title = "车台数", type = EditType.INPUT, notNull = true,
		inputType = @InputType))
	@Comment("车台数")
	@ApiModelProperty("车台数")
	private String numberOfVehicles;

	@EruptField(
		views = @View(title = "车道数"),
		edit = @Edit(title = "车道数", type = EditType.INPUT, notNull = true,
		inputType = @InputType))
	@Comment("车道数")
	@ApiModelProperty("车道数")
	private String numberOfLanes;

	@EruptField(
		views = @View(title = "投用时间"),
		edit = @Edit(title = "投用时间", type = EditType.DATE, notNull = true,
		dateType = @DateType))
	@Comment("投用时间")
	@ApiModelProperty("投用时间")
	private java.util.Date operationTime;

	@EruptField(
		views = @View(title = "联系人"),
		edit = @Edit(title = "联系人", type = EditType.INPUT, notNull = true,
		inputType = @InputType))
	@Comment("联系人")
	@ApiModelProperty("联系人")
	private String contacts;

	@EruptField(
		views = @View(title = "联系电话"),
		edit = @Edit(title = "联系电话", type = EditType.INPUT, notNull = true,
		inputType = @InputType))
	@Comment("联系电话")
	@ApiModelProperty("联系电话")
	private String contactNumber;

//	@EruptField(
//		views = @View(title = "经纬度", show = false),
//		edit = @Edit(title = "经纬度", type = EditType.INPUT, show = false, notNull = true,
//		inputType = @InputType))
//	@Comment("经纬度")
//	@ApiModelProperty("经纬度")
//	private String longitudeAndLatitude;
//
//	@EruptField(
//		views = @View(title = "经度", show = false),
//		edit = @Edit(title = "经度", type = EditType.INPUT, show = false,
//		inputType = @InputType))
//	@Comment("经度")
//	@ApiModelProperty("经度")
//	private String longitude;
//
//	@EruptField(
//		views = @View(title = "纬度", show = false),
//		edit = @Edit(title = "纬度", type = EditType.INPUT, show = false,
//		inputType = @InputType))
//	@Comment("纬度")
//	@ApiModelProperty("纬度")
//	private String latitude;

	@EruptField(
		views = @View(title = "地图", show = false),
		edit = @Edit(title = "地图", type = EditType.MAP, show = true,notNull = true))
	@Comment("地图")
	@ApiModelProperty("地图")
	@Lob
	private String map;

	@EruptField(
			views = @View(title = "三方系统位置id",show = false),
			edit = @Edit(title = "三方系统位置id", type = EditType.INPUT,show = false,
					inputType = @InputType))
	@Comment("三方系统位置id")
	@ApiModelProperty("三方系统位置id")
	private String positionId;

}
