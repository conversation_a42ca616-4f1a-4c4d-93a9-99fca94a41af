/*
* Copyright © 2020-2035 yundingyun All rights reserved.
*/
package com.daliangang.majorisk.operation;

import com.daliangang.majorisk.entity.CkReserveReporting;
import com.daliangang.majorisk.entity.ReserveReporting;
import com.daliangang.safedaily.entity.Release;
import org.springframework.stereotype.Service;
import xyz.erupt.annotation.fun.OperationHandler;
import xyz.erupt.jpa.dao.EruptDao;
import xyz.erupt.toolkit.utils.NotifyUtils;

import javax.annotation.Resource;
import javax.transaction.Transactional;
import java.time.LocalDateTime;
import java.util.List;

@Service
public class CkReserveReportingEscalationHandler implements OperationHandler<CkReserveReporting, Void> {
    @Resource
    private EruptDao eruptDao;

    @Override
    @Transactional
    public String exec(List<CkReserveReporting> data, Void unused, String[] param) {
        for (CkReserveReporting ckReserveReporting:data) {
          //  ckReserveReporting.setSubmitted(true);
            ckReserveReporting.setUpdateTime(LocalDateTime.now());
            eruptDao.merge(ckReserveReporting);
        }

        return NotifyUtils.getSuccessNotify("上报成功！");
    }
}
