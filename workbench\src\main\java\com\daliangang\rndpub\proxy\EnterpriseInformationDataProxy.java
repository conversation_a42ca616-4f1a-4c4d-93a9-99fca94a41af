/*
 * Copyright © 2020-2035 yundingyun All rights reserved.
 */
package com.daliangang.rndpub.proxy;

import cn.hutool.json.JSONUtil;
import com.daliangang.rndpub.entity.EnterpriseInformation;
import com.daliangang.rndpub.entity.Procedure;
import com.daliangang.rndpub.operation.EnterpriseInformationSystemExtractionHandler;
import org.springframework.stereotype.Service;
import xyz.erupt.annotation.fun.DataProxy;
import xyz.erupt.annotation.sub_erupt.PageModel;
import xyz.erupt.core.context.MetaDrill;
import xyz.erupt.jpa.dao.EruptDao;
import xyz.erupt.toolkit.redismq.RedisMQService;
import xyz.erupt.toolkit.service.EruptCacheRedis;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

@Service
public class EnterpriseInformationDataProxy implements DataProxy<EnterpriseInformation> , PageModel.PageModelProxy {
    @Resource
    private EruptDao eruptDao;

    @Resource
    private EruptCacheRedis eruptCacheRedis;

    @Resource
    private RedisMQService redisMQService;
    @Resource
    private EnterpriseInformationSystemExtractionHandler enterpriseInformationSystemExtractionHandler ;
    @Override
    public void searchCondition(Map<String, Object> condition) {
        //condition.put("state", true);
    }

    public String getCacheKey(){
        long drillId = MetaDrill.getDrillId();
        return Procedure.class.getSimpleName() +"-"+ EnterpriseInformation.class.getSimpleName() +"-"+ drillId + ":";
    }


    @Override
    public void open() {
//        把未抽取前的数据放入缓存
        long drillId = MetaDrill.getDrillId();
        List<EnterpriseInformation> list = eruptDao.queryEntityList(EnterpriseInformation.class, "procedure_id = " + drillId);
        String key = this.getCacheKey();
        String str = JSONUtil.toJsonStr(list);
        redisMQService.produce(key,str);
    }

    @Override
    public void close() {
        //关闭，就还原数据
        long drillId = MetaDrill.getDrillId();
        String key = this.getCacheKey();
        String json = redisMQService.consume(key, String.class);
        List<EnterpriseInformation> list = JSONUtil.toList(json, EnterpriseInformation.class);
        list.forEach(item ->{
            eruptDao.mergeAndFlush(item) ;
        });
        eruptCacheRedis.getStringRedisTemplate().delete(key);
        //还原条数
        Procedure procedure = eruptDao.queryEntity(Procedure.class, "id=" + drillId);
        enterpriseInformationSystemExtractionHandler.updateCountAndName(procedure);
    }
}
