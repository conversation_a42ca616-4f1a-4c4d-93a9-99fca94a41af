/*
* Copyright © 2020-2035 yundingyun All rights reserved.
*/
package com.daliangang.rndpub.operation;

import com.daliangang.rndpub.entity.EnterpriseInformation;
import com.daliangang.rndpub.entity.ExpertInformation;
import com.daliangang.rndpub.entity.Inspector;
import com.daliangang.rndpub.entity.Procedure;
import org.springframework.stereotype.Service;
import xyz.erupt.annotation.fun.OperationHandler;
import xyz.erupt.core.context.MetaDrill;
import xyz.erupt.jpa.dao.EruptDao;
import xyz.erupt.toolkit.utils.EruptDaoUtils;
import xyz.erupt.toolkit.utils.NotifyUtils;

import javax.annotation.Resource;
import javax.transaction.Transactional;
import java.util.List;

@Service
public class InspectorSystemExtractionHandler implements OperationHandler<Inspector, Void> {

    @Resource
    private EruptDao eruptDao;

    @Resource
    private InspectorManualSelectionHandler inspectorManualSelectionHandler;

   @Override
   @Transactional
   public String exec(List<Inspector> data, Void unused, String[] param) {

       //获取专家信息
       String expertSql = "select * from tb_expert_information where procedure_id=" + MetaDrill.getDrillId() + " and state=1";
       List<ExpertInformation> expertInformations = EruptDaoUtils.selectOnes(expertSql, ExpertInformation.class);
       if (expertInformations.size() == 0) {
           NotifyUtils.showErrorMsg("请先抽取专家");
       }

       //获取企业信息
       String enterpriseSql = "select enterprise_name,type from tb_enterprise_information where procedure_id=" + MetaDrill.getDrillId() + " and state=1";
       List<EnterpriseInformation> enterpriseList = EruptDaoUtils.selectOnes(enterpriseSql, EnterpriseInformation.class);
       if (enterpriseList.size() == 0) {
           NotifyUtils.showErrorMsg("请先抽取企业");
       }
       //抽取检查人员时，判断有无检查人员
       boolean selectedFlag = Boolean.parseBoolean(param[0]);
       if(selectedFlag){
           String sql = "select * from tb_inspector where procedure_id=" + MetaDrill.getDrillId() + " and state=0 ";
           List<Inspector> inspectorList = EruptDaoUtils.selectOnes(sql, Inspector.class);
           if(inspectorList.isEmpty()){
               NotifyUtils.showErrorDialog("暂无检查人员可选！");
           }
       }

       for (Inspector info : data) {
           info.setState(selectedFlag);
           eruptDao.merge(info);
           eruptDao.flush();
       }
       //更新条数
       Procedure procedure = eruptDao.queryEntity(Procedure.class, "id=" + MetaDrill.getDrillId());
       inspectorManualSelectionHandler.updateCountAndName(procedure);
       return null ;
	}
}
