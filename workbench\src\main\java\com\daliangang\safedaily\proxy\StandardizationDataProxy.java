/*
 * Copyright © 2020-2035 yundingyun All rights reserved.
 */
package com.daliangang.safedaily.proxy;

import cn.hutool.json.JSONObject;
import com.daliangang.core.DaliangangContext;
import com.daliangang.safedaily.entity.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import xyz.erupt.annotation.fun.DataProxy;
import xyz.erupt.annotation.query.Condition;
import xyz.erupt.common.http.EruptRemoteUtils;
import xyz.erupt.core.util.EruptSpringUtil;
import xyz.erupt.jpa.dao.EruptDao;
import xyz.erupt.toolkit.colorfont.ColorStateTimeFontDataProxy;
import xyz.erupt.toolkit.remote.RemoteProxyService;
import xyz.erupt.toolkit.utils.AssertUtils;
import xyz.erupt.toolkit.utils.EruptDaoUtils;
import xyz.erupt.toolkit.utils.NotifyUtils;

import javax.annotation.Resource;
import javax.transaction.Transactional;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Service
@Slf4j
public class StandardizationDataProxy implements DataProxy<Standardization> {

    @Resource
    private ColorStateTimeFontDataProxy colorStateTimeFontDataProxy ;
    @Resource
    private EruptDao eruptDao;
    @Resource
    private RemoteProxyService remoteProxyService;

    @Transactional
    @Override
    public void afterAdd(Standardization standardization) {
        StandardizationSenond standardizationSenond = EruptSpringUtil.getBean(StandardizationSenond.class);
        StandardizationScoreSecond standardizationScoreSecond = EruptSpringUtil.getBean(StandardizationScoreSecond.class);
        int i = Integer.parseInt(standardization.getYear());
        standardizationSenond.setYear(String.valueOf(i + 1));
        //第二年自评
        String secondYear = selfEvaluation(standardization, standardizationSenond, standardizationScoreSecond);

        //第三年自评
        standardizationSenond.setYear(String.valueOf(i + 1));
        StandardizationThird standardizationThird = EruptSpringUtil.getBean(StandardizationThird.class);
        StandardizationScoreThird standardizationScoreThird = EruptSpringUtil.getBean(StandardizationScoreThird.class);
        i = Integer.parseInt(secondYear);
        standardizationThird.setYear(String.valueOf(i + 1));
        selfThird(standardization,standardizationThird,standardizationScoreThird);

        DataProxy.super.afterAdd(standardization);
    }


    @Override
    public void addBehavior(Standardization standardization) {
        List<StandardizationScore> scores = new ArrayList<>();
        List<StandardizationItem> items = EruptDaoUtils.selectOnes("select * from tb_standardization_item order by series_id", StandardizationItem.class);
        for (StandardizationItem item : items) {
            StandardizationScore score = EruptDaoUtils.cast(item, StandardizationScore.class);
            score.setScoreStandard(new BigDecimal(0));
            score.setScore(new BigDecimal(0));
            score.setId((long) item.getSeriesId());
            scores.add(score);
        }
        standardization.setScores(scores);
    }

    @Override
    @Transactional
    public void beforeAdd(Standardization standardization) {
        validNumber(standardization);
        standardization.setSubmitted(true);
    }

    @Override
    @Transactional
    public void beforeUpdate(Standardization standardization) {
        validNumber(standardization);

        //selfEvaluation(standardization);
        /*if (standardization.getSubmitted()) {
            NotifyUtils.showErrorMsg("报告单已经上报，请勿修改！");
        }*/
    }

    @Transactional
    @Override
    public void afterUpdate(Standardization standardization) {
        StandardizationScoreSecond standardizationScoreSecond = new StandardizationScoreSecond();
        StandardizationSenond standardizationSenond = eruptDao.queryEntity(StandardizationSenond.class, "tb_standardization_id = '" + standardization.getId() + "'");
        selfEvaluation(standardization,standardizationSenond,standardizationScoreSecond);
        DataProxy.super.afterUpdate(standardization);
    }

    @Override
    public String beforeFetch(List<Condition> conditions) {

        String sql = " 1= 1 " ;
        sql = sql + colorStateTimeFontDataProxy.searchByState(Standardization.class,"state" ,conditions);
        //if (DaliangangContext.isDepartmentUser()) return sql +" and submitted=true";

        return sql;
    }

    private void selfThird(Standardization standardization, StandardizationThird standardizationThird, StandardizationScoreThird standardizationScoreThird) {
        // 同步第三年自评数据
        standardizationThird.setCompany(standardization.getCompany());
        standardizationThird.setOrgCode(standardization.getOrgCode());
        standardizationThird.setGrade(standardization.getGrade());
        standardizationThird.setIssueDate(standardization.getIssueDate());
        standardizationThird.setEffectiveDate(standardization.getEffectiveDate());
        standardizationThird.setStateStr(standardization.getStateStr());
        standardizationThird.setStandardFile(standardization.getStandardFile());
        standardizationThird.setSelfEvaluation(standardization.getSelfEvaluation());
        standardizationThird.setOtherFile(standardization.getOtherFile());
        standardizationThird.setFinalScore(standardization.getFinalScore());
        standardizationThird.setGateScore(standardization.getGateScore());
        standardizationThird.setMostTotalScore(standardization.getMostTotalScore());
        StandardizationScoreThird standardizationScoreSecond = EruptSpringUtil.getBean(StandardizationScoreThird.class);
        standardizationThird.setStandardization(standardization);
        StandardizationThird merge = eruptDao.merge(standardizationThird);
        standardization.getScores().forEach(m -> {
            standardizationScoreSecond.setSeriesId(m.getSeriesId());
            standardizationScoreSecond.setStandard(m.getStandard());
            standardizationScoreSecond.setTandardsScore(m.getTandardsScore());
            standardizationScoreSecond.setScoreStandard(m.getScoreStandard());
            standardizationScoreSecond.setScoreProportion(m.getScoreProportion());
            standardizationScoreSecond.setScore(m.getScore());
            standardizationScoreSecond.setStandardizationThird(merge);
            eruptDao.merge(standardizationScoreSecond);
            //  list.add(standardizationScoreSecond);
        });
        // standardizationSenond.setScores(list);
    }
    private String selfEvaluation(Standardization standardization, StandardizationSenond standardizationSenond, StandardizationScoreSecond standardizationScoreSecond) {
        standardizationSenond.setCompany(standardization.getCompany());
        standardizationSenond.setOrgCode(standardization.getOrgCode());
        standardizationSenond.setGrade(standardization.getGrade());
        standardizationSenond.setSubmitted(true);
        standardizationSenond.setIssueDate(standardization.getIssueDate());
        standardizationSenond.setEffectiveDate(standardization.getEffectiveDate());
        standardizationSenond.setStateStr(standardization.getStateStr());
        standardizationSenond.setStandardFile(standardization.getStandardFile());
        standardizationSenond.setSelfEvaluation(standardization.getSelfEvaluation());
        standardizationSenond.setOtherFile(standardization.getOtherFile());
        standardizationSenond.setFinalScore(standardization.getFinalScore());
        standardizationSenond.setGateScore(standardization.getGateScore());
        standardizationSenond.setMostTotalScore(standardization.getMostTotalScore());
        List<StandardizationScoreSecond> list = new ArrayList();
        standardizationSenond.setStandardization(standardization);
        StandardizationSenond merge = eruptDao.merge(standardizationSenond);
        standardization.getScores().forEach(m->{
            standardizationScoreSecond.setSeriesId(m.getSeriesId());
            standardizationScoreSecond.setStandard(m.getStandard());
            standardizationScoreSecond.setTandardsScore(m.getTandardsScore());
            standardizationScoreSecond.setScoreStandard(m.getScoreStandard());
            standardizationScoreSecond.setScoreProportion(m.getScoreProportion());
            standardizationScoreSecond.setScore(m.getScore());
            standardizationScoreSecond.setStandardizationSenond(merge);
            eruptDao.merge(standardizationScoreSecond);

        });
        JSONObject inputData = new JSONObject();
        inputData.set("clazz", "Standardization");
        inputData.set("insertData", standardization);
        EruptRemoteUtils.doPost(remoteProxyService.getMainServerUrl() + "/erupt-api/exchange/data/exchange", inputData, Map.class);
        return standardizationSenond.getYear();
    }
    @Override
    public void beforeDelete(Standardization standardization) {
        String sql = "";
        //找到second和third里的外键id
        Long standardizationId = standardization.getId();
        //找到对应的second的id和third的id
        StandardizationSenond standardizationSenond = eruptDao.queryEntity(StandardizationSenond.class, "tb_standardization_id = '" + standardizationId + "'");
        if(standardizationSenond != null){
            Long secondId = standardizationSenond.getId();
            //删除second的分数
            sql = "delete from tb_standardization_score_second where id = '" + secondId + "'";
            eruptDao.getJdbcTemplate().execute(sql);
            //删second
            eruptDao.delete(standardizationSenond);
        }
        StandardizationThird standardizationThird = eruptDao.queryEntity(StandardizationThird.class, "tb_standardization_id = '" + standardizationId + "'");
        if(standardizationThird != null){
            Long thirdId = standardizationThird.getId();
            //删除third的分数
            sql = "delete from tb_standardization_score_third where id = '" + thirdId + "'";
            eruptDao.getJdbcTemplate().execute(sql);
            //删third
            eruptDao.delete(standardizationThird);
        }

        /*if (standardization.getSubmitted()) {
            NotifyUtils.showErrorMsg("报告单已经上报，请勿删除！");
        }*/
    }

//    @Override
//    public void afterFetch(Collection<Map<String, Object>> list) {
//        Calendar calendar=Calendar.getInstance();
//        List<Standardization> standardizations = EruptDaoUtils.convert(list, Standardization.class);
//        for (Standardization standardization : standardizations) {
//            Date evaluationTime = standardization.getEffectiveDate();
//            Date now = new Date(System.currentTimeMillis());
//            calendar.setTime(evaluationTime);
//            calendar.add(Calendar.DATE,-30);
//            java.util.Date beforeMonth = calendar.getTime();
//            //预案状态:正常、即将到期、已逾期
//            if(now.after(evaluationTime)){
//                //已逾期
//                standardization.setStateStr(TplUtils.addColor("已逾期", "red"));
//            }else {
//                //到期前30天为即将逾期，否则为正常
//                if(now.before(beforeMonth)){
//                    standardization.setStateStr(TplUtils.addColor("正常", "green"));
//                }else{
//                    standardization.setStateStr(TplUtils.addColor("即将逾期", "blue"));
//                }
//            }
//            EruptDaoUtils.updateAfterFetch(list, standardization.getId(), "stateStr", standardization.getStateStr());
//        }
//
//    }


    @Transactional
    public void validNumber(Standardization target) {
        //计算总得分
        double scoreGot = 0;
        //应得总分
        double totalScore = 0;
        //判断小数点后2位的数字的正则表达式
        Pattern pattern = Pattern.compile("^(([1-9]{1}\\d*)|([0]{1}))(\\.(\\d){0,2})?$");
        //遍历每个分数
        for (StandardizationScore score : target.getScores()) {
            Matcher match = pattern.matcher(score.getScore().toString());
            AssertUtils.isTrue(match.matches(), score.getStandard() + "需为整数或小数点2位小数");
            //将得分相加
            scoreGot += score.getScore().doubleValue();
            //将选中的应得分相加
            totalScore += score.getScoreStandard().doubleValue();
        }

//        String scoreSql = "select sum(score_standard) as total from tb_standardization_item";
//        EruptDao eruptDao = EruptSpringUtil.getBean(EruptDao.class);
//        Map<String, Object> resultMap = eruptDao.getJdbcTemplate().queryForMap(scoreSql);
//        String strScore = resultMap.get("total") + "";
//        double scoreStandard = StringUtils.isEmpty(strScore) ? 0D : Double.parseDouble(String.valueOf(strScore));

        //总分计算=实际得分/应得分数*1000
        double scoreFinal = totalScore > 0 ? (scoreGot / totalScore) * 1000 : 0;
        log.info("总得分 -> " + scoreGot + ", 应得分数 -> " + totalScore + ", 实际得分 -> " + scoreFinal);
        //AssertUtils.isTrue(scoreFinal <= 200, "总得分不能超过200");
        target.setFinalScore((int)scoreGot);
        target.setGateScore((int) totalScore);
        target.setMostTotalScore(String.format("%.2f", new BigDecimal(scoreFinal)));
    }

    //重写导入方法
//    @Override
//    public void excelImport(Object workbook) {
//        Workbook wb = (Workbook) workbook;
//        Sheet sheet = wb.getSheetAt(0);
//        for (int i = sheet.getFirstRowNum() + 1; i <= sheet.getLastRowNum(); i++) {
//            //获取每行数据
//            Row row = sheet.getRow(i);
//            System.out.println(row);
//            //获取每一列数据
//            //年度
//            double year = row.getCell(0).getNumericCellValue();
//            //企业名称
//            String company = row.getCell(1).getStringCellValue();
//            String companySql="select * from tb_enterprise where name='"+company+"'";
//            Enterprise enterprise = EruptDaoUtils.selectOne(companySql, Enterprise.class);
//            //安全标准化等级
//            String grade = row.getCell(2).getStringCellValue();
//            //发证日期
//            String issueDateStr = row.getCell(3).getCellStyle().getDataFormatString();
//            Date issueDate = strToDate(issueDateStr);
//            //有效期至
//            String effectiveDateStr = row.getCell(4).getCellStyle().getDataFormatString();
//            Date effectiveDate = strToDate(effectiveDateStr);
//            //总得分
//            String finalScore = row.getCell(5).getStringCellValue();
//            //应得总分
//            String gateScoreStr = row.getCell(6).getStringCellValue();
//            Integer gateScore = Integer.valueOf(gateScoreStr);
//            String insertSql=String.format("insert into tb_standardization(year,company,grade,issue_date,effective_date,final_score,gate_score,standard_file,self_evaluation) values(%s,%s,%s,%s,%s,%s,%s,%s,%s)",
//                    year, enterprise.getOrgCode(),grade,issueDate,effectiveDate,finalScore,gateScore,"","");
//            EruptDao eruptDao = EruptDaoUtils.getEruptDao();
//            eruptDao.getJdbcTemplate().execute(insertSql);
//        }
//    }
//
//    public static java.util.Date strToDate(String strDate) {
//        String str = strDate;
//        SimpleDateFormat format = new SimpleDateFormat("yyyy/MM/dd");
//        java.util.Date d = null;
//        try {
//            d = format.parse(str);
//        } catch (Exception e) {
//            e.printStackTrace();
//        }
//        java.util.Date date = new java.util.Date(d.getTime());
//        return date;
//    }
}
