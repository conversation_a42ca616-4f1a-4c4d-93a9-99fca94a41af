/*
* Copyright © 2020-2035 yundingyun All rights reserved.
*/
package com.daliangang.rndpub.operation;

import org.springframework.stereotype.*;
import xyz.erupt.annotation.fun.*;
import java.util.*;
import com.daliangang.rndpub.entity.*;

@Service
public class ExpertDisableHandler implements OperationHandler<Expert, Void> {
   @Override
   public String exec(List<Expert> data, Void unused, String[] param) {
       return null;
	}
}