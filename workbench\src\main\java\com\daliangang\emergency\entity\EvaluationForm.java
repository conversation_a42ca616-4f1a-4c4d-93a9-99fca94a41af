package com.daliangang.emergency.entity;

import com.daliangang.emergency.proxy.EvaluationFormDataProxy;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import xyz.erupt.annotation.Erupt;
import xyz.erupt.annotation.EruptField;
import xyz.erupt.annotation.sub_erupt.Power;
import xyz.erupt.annotation.sub_field.Edit;
import xyz.erupt.annotation.sub_field.EditType;
import xyz.erupt.annotation.sub_field.View;
import xyz.erupt.annotation.sub_field.sub_edit.AttachmentType;
import xyz.erupt.toolkit.db.Comment;
import xyz.erupt.upms.model.auth.DataAuthModel;

import javax.persistence.Entity;
import javax.persistence.Table;

/**
 * <AUTHOR>
 * @since :2023/7/13:9:23
 */
@Erupt(closeTreeView = true,name = "评估表管理", power = @Power(delete = false),
        dataProxy = EvaluationFormDataProxy.class,
        rowOperation = {
        //@RowOperation(title = "TOKEN续期", operationHandler = AccessKey.class, icon = "fa fa-refresh", mode = RowOperation.Mode.SINGLE),
})
@Table(name = "tb_evaluation_form")
@Entity
@Data
@Comment("评估表管理")
public class EvaluationForm extends DataAuthModel {

    @EruptField(
            views = @View(title = "上传评估表", show = false),
            edit = @Edit(title = "上传评估表", type = EditType.ATTACHMENT, notNull = true,
                    attachmentType = @AttachmentType(type = AttachmentType.Type.BASE, maxLimit = 1, fileTypes = {".doc", ".docx"})))
    @Comment("上传评估表")
    @ApiModelProperty("上传评估表")
    private String evaluationForm;
}
