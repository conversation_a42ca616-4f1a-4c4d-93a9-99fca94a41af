package com.daliangang.majorisk.form;

import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import xyz.erupt.jpa.model.BaseModel;
import xyz.erupt.toolkit.db.Comment;

import javax.persistence.Entity;
import java.util.List;

/**
 * <AUTHOR>
 * @since :2023/4/15:13:41
 */
@Getter
@Setter
@Data
public class WeatherForm  {

  private List<Results> results;

@Data
     public class Results {
          private Now now;
     }

        @Data
        public class Now{
             private String  text;
             private String     code;
             private String     temperature;
             private String    feels_like;
             private String     pressure;
             private String     humidity;
             private String     visibility;
             private String     wind_direction;
             private String    wind_direction_degree;
             private String    wind_speed;
             private String    wind_scale;
             private String    clouds;
             private String   dew_point;
     }



}
