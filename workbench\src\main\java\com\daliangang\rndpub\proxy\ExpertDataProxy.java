/*
 * Copyright © 2020-2035 yundingyun All rights reserved.
 */
package com.daliangang.rndpub.proxy;

import com.daliangang.rndpub.RndpubConst;
import com.daliangang.rndpub.entity.Expert;
import org.springframework.stereotype.Service;
import xyz.erupt.annotation.fun.DataProxy;
import xyz.erupt.toolkit.utils.EruptDaoUtils;
import xyz.erupt.toolkit.utils.NotifyUtils;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.List;

@Service
public class ExpertDataProxy implements DataProxy<Expert> {

    @Override
    public void addBehavior(Expert expert) {
        expert.setDate("1980-01-01");
    }

    @Override
    public void beforeAdd(Expert expert) {
        if(expert.getName()!=null&&!expert.getName().equals("")){
            String querySql=String.format("select * from tb_expert where name='%s'",expert.getName());
            List<Expert> experts = EruptDaoUtils.selectOnes(querySql, Expert.class);
            if(experts.size()>0){
                NotifyUtils.showErrorDialog("当前专家【"+expert.getName()+"】已存在");

            }
        }
        expert.setExpertAudidtState(RndpubConst.EXPERT_STATUS_PASS);
//        expert.setUseState(Boolean.TRUE);
        expert.setSource(Boolean.TRUE);
        if(expert.getDate()!=null&&!expert.getDate().equals("")&&!expert.getDate().contains("-")){
            String time = getTime(expert.getDate());
            expert.setDate(time);
        }
    }

    //判断并转换时间格式
    public static String getTime(String ditNumber) {
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
        //如果是数字 小于0则 返回
        BigDecimal bd = new BigDecimal(ditNumber);
        int days = bd.intValue();//天数
        int mills = (int) Math.round(bd.subtract(new BigDecimal(days)).doubleValue() * 24 * 3600);
        //获取时间
        Calendar c = Calendar.getInstance();
        c.set(1900, 0, 1);
        c.add(Calendar.DATE, days - 2);
        int hour = mills / 3600;
        int minute = (mills - hour * 3600) / 60;
        int second = mills - hour * 3600 - minute * 60;
        c.set(Calendar.HOUR_OF_DAY, hour);
        c.set(Calendar.MINUTE, minute);
        c.set(Calendar.SECOND, second);
        return dateFormat.format(c.getTime());
    }
}
