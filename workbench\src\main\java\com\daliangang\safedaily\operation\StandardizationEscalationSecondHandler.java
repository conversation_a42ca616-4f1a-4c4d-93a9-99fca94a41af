/*
* Copyright © 2020-2035 yundingyun All rights reserved.
*/
package com.daliangang.safedaily.operation;

import com.daliangang.safedaily.entity.StandardizationScoreThird;
import com.daliangang.safedaily.entity.StandardizationSenond;
import com.daliangang.safedaily.entity.StandardizationThird;
import org.springframework.stereotype.Service;
import xyz.erupt.annotation.fun.OperationHandler;
import xyz.erupt.core.util.EruptSpringUtil;
import xyz.erupt.jpa.dao.EruptDao;
import xyz.erupt.toolkit.utils.NotifyUtils;

import javax.annotation.Resource;
import javax.transaction.Transactional;
import java.util.Calendar;
import java.util.List;

@Service
public class StandardizationEscalationSecondHandler implements OperationHandler<StandardizationSenond, Void> {
    @Resource
    EruptDao eruptDao;

   @Override
   @Transactional
   public String exec(List<StandardizationSenond> data, Void unused, String[] param) {

       data.forEach(v->{
           Calendar calendar = Calendar.getInstance();
            String year = String.valueOf(calendar.get(Calendar.YEAR));
           if (!v.getYear().equals(year)) {
               NotifyUtils.showErrorMsg("未到提交年份！");
           }
           //  v.setIsReport(true);
           v.setSubmitted(true);
         //  v.setIsReport(true);
           eruptDao.merge(v);

           // 同步第三年自评数据
           StandardizationThird standardizationSenond = EruptSpringUtil.getBean(StandardizationThird.class);
           // 年度+1
           int i = Integer.valueOf(v.getYear()).intValue();
           standardizationSenond.setYear(String.valueOf(i+1));
           standardizationSenond.setCompany(v.getCompany());
           standardizationSenond.setOrgCode(v.getOrgCode());
           standardizationSenond.setSubmitted(false);
           standardizationSenond.setGrade(v.getGrade());
           standardizationSenond.setIssueDate(v.getIssueDate());
           standardizationSenond.setEffectiveDate(v.getEffectiveDate());
           standardizationSenond.setStateStr(v.getStateStr());
           standardizationSenond.setStandardFile(v.getStandardFile());
           standardizationSenond.setSelfEvaluation(v.getSelfEvaluation());
           standardizationSenond.setOtherFile(v.getOtherFile());
         //  standardizationSenond.setIsReport(false);
           standardizationSenond.setFinalScore(v.getFinalScore());
           standardizationSenond.setGateScore(v.getGateScore());
           standardizationSenond.setMostTotalScore(v.getMostTotalScore());

           StandardizationScoreThird standardizationScoreSecond = EruptSpringUtil.getBean(StandardizationScoreThird.class);

           standardizationSenond.setStandardization(v.getStandardization());
           StandardizationThird merge = eruptDao.merge(standardizationSenond);

           v.getScores().forEach(m->{
               standardizationScoreSecond.setSeriesId(m.getSeriesId());
               standardizationScoreSecond.setStandard(m.getStandard());
               standardizationScoreSecond.setTandardsScore(m.getTandardsScore());
               standardizationScoreSecond.setScoreStandard(m.getScoreStandard());
               standardizationScoreSecond.setScoreProportion(m.getScoreProportion());
               standardizationScoreSecond.setScore(m.getScore());
               standardizationScoreSecond.setStandardizationThird(merge);
               eruptDao.merge(standardizationScoreSecond);
               //  list.add(standardizationScoreSecond);
           });
           // standardizationSenond.setScores(list);


       });
       return NotifyUtils.getSuccessNotify("上报成功！");
	}
}
