package com.daliangang.workbench.handler;

import org.springframework.stereotype.Component;
import xyz.erupt.annotation.expr.ExprBool;
import xyz.erupt.upms.service.EruptContextService;

import javax.annotation.Resource;

/**
 * @Author：chb
 * @Package：com.daliangang.workbench.handler
 * @Project：erupt
 * @name：DepartmentViewRenderHandler
 * @Date：2023/3/6 03:02
 * @Filename：DepartmentViewRenderHandler
 */
@Component
public class DepartmentViewRenderHandler implements ExprBool.ExprHandler {

    @Resource
    private EruptContextService eruptContextService;

    /**
     * @param expr   表达式
     * @param params 注解参数
     * @return 程序处理后的表达式
     */
    @Override
    public boolean handler(boolean expr, String[] params) {
        //if (eruptContextService.getCurrentEruptMenu() == null) return false;
        //String currMenu = eruptContextService.getCurrentEruptMenu().getValue();
        //if (currMenu.equalsIgnoreCase(Department.class.getSimpleName())) return false;
        return false;
    }
}
