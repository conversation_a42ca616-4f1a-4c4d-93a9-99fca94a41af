/*
* Copyright © 2020-2035 yundingyun All rights reserved.
*/
package com.daliangang.emergency.proxy;

import com.daliangang.core.DaliangangContext;
import com.daliangang.emergency.entity.Drill;
import com.daliangang.emergency.entity.EvaluationForm;
import org.springframework.stereotype.Service;
import xyz.erupt.annotation.fun.DataProxy;
import xyz.erupt.annotation.query.Condition;
import xyz.erupt.core.util.EruptSpringUtil;
import xyz.erupt.toolkit.utils.EruptDaoUtils;
import xyz.erupt.upms.model.template.EruptRoleTemplateUser;
import xyz.erupt.upms.model.template.EruptRoleTemplateUserService;

import java.util.List;

@Service
public class EvaluationFormDataProxy implements DataProxy<EvaluationForm> {
//    @Override
//    public void addBehavior(Drill drill) {
//        EruptRoleTemplateUser roleTemplateUser = EruptSpringUtil.getBean(EruptRoleTemplateUserService.class).queryCurrentAccount();
//        if (roleTemplateUser != null && roleTemplateUser.getEruptOrg() != null)
//            drill.setCompany(roleTemplateUser.getEruptOrg().getCode());
//    }


    @Override
    public void beforeAdd(EvaluationForm evaluationForm) {
        // 删除表数据
        String sql1 = "truncate table tb_evaluation_form";
        EruptDaoUtils.updateNoForeignKeyChecks(sql1);

    }


}
