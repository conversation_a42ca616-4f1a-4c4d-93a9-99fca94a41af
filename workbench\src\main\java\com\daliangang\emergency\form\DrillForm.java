package com.daliangang.emergency.form;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import xyz.erupt.annotation.Erupt;
import xyz.erupt.annotation.EruptField;
import xyz.erupt.annotation.sub_erupt.Power;
import xyz.erupt.annotation.sub_field.Edit;
import xyz.erupt.annotation.sub_field.EditType;
import xyz.erupt.annotation.sub_field.View;
import xyz.erupt.annotation.sub_field.sub_edit.InputType;
import xyz.erupt.annotation.sub_field.sub_edit.Search;
import xyz.erupt.jpa.model.BaseModel;
import xyz.erupt.toolkit.db.Comment;

import javax.persistence.Entity;

/**
 * <AUTHOR>
 * @since :2023/4/14:15:36
 */
@Erupt(name = "应急演练", power = @Power(add = false, delete = false, export = false, importable = true, viewDetails = false, edit = false)
        , rowOperation = {})

@Getter
@Setter
@Comment("应急演练")
@ApiModel("应急演练")
public class DrillForm extends BaseModel {

    @Comment("数量")
    @ApiModelProperty("数量")
    private String num;



    @Comment("名称")
    @ApiModelProperty("名称")
    private String name;

    @Comment("类型")
    @ApiModelProperty("类型")
    private String type;
}
