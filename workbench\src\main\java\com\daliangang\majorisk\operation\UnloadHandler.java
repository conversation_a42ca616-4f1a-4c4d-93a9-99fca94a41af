package com.daliangang.majorisk.operation;

import com.daliangang.majorisk.entity.Unload;
import org.springframework.stereotype.Service;
import xyz.erupt.annotation.fun.OperationHandler;
import xyz.erupt.jpa.dao.EruptDao;
import xyz.erupt.toolkit.utils.NotifyUtils;

import javax.annotation.Resource;
import javax.transaction.Transactional;
import java.util.List;

/**
 * <AUTHOR>
 * @since :2023/5/24:10:55
 */
@Service
@Transactional
public class UnloadHandler implements OperationHandler<Unload,Void> {
    @Resource
    private EruptDao eruptDao;

    @Override
    @Transactional
    public String exec(List<Unload> data, Void aVoid, String[] param) {
        data.forEach(v->{
            v.setSubmitted(true);
            eruptDao.merge(v);
        });
        return NotifyUtils.getSuccessNotify("上报成功！");
    }
}
