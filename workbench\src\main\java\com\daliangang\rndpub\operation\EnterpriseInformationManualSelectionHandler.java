/*
 * Copyright © 2020-2035 yundingyun All rights reserved.
 */
package com.daliangang.rndpub.operation;

import com.daliangang.rndpub.entity.EnterpriseInformation;
import com.daliangang.rndpub.entity.Procedure;
import org.springframework.stereotype.Service;
import xyz.erupt.annotation.fun.OperationHandler;
import xyz.erupt.core.context.MetaDrill;
import xyz.erupt.jpa.dao.EruptDao;

import javax.annotation.Resource;
import javax.transaction.Transactional;
import java.util.Date;
import java.util.List;

@Service
public class EnterpriseInformationManualSelectionHandler implements OperationHandler<EnterpriseInformation, Void> {

//    @Erupt(name = "抽取企业")
//    public static class DrawEnterpriseForm extends BaseModel {
//        @EruptField(
//                edit = @Edit(title = "企业列表", type = EditType.TAGS, notNull = true,
//                        tagsType = @TagsType(fetchHandler = RemoteEntityTagFetchHandler.class, fetchHandlerParams = {"main", "Enterprise", "administrator"})))
//        private String enterprises;
//    }

    @Resource
    private EruptDao eruptDao;

    @Resource
    private EnterpriseInformationSystemExtractionHandler enterpriseInformationSystemExtractionHandler ;

    @Override
    @Transactional
    public String exec(List<EnterpriseInformation> data, Void unused, String[] param) {
        Long produceId=0L;
        for (EnterpriseInformation info : data) {
            produceId = info.getProcedure().getId();
            info.setState(Boolean.parseBoolean(param[0]));
            //抽取后，抽取时间为当前时间
            if(Boolean.parseBoolean(param[0])){
                info.setCurrentSpotCheck(new Date(System.currentTimeMillis()));
            }else{
                //放弃抽取则抽取时间为null
                info.setCurrentSpotCheck(null);
            }
            eruptDao.merge(info);
            eruptDao.flush();
        }
        //更新条数
        Procedure procedure = eruptDao.queryEntity(Procedure.class, "id=" + produceId);
        enterpriseInformationSystemExtractionHandler.updateCountAndName(procedure);
        return null;
    }
}
