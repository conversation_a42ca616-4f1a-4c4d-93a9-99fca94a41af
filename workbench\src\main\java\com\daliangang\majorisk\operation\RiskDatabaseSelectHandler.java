/*
 * Copyright © 2020-2035 yundingyun All rights reserved.
 */

package com.daliangang.majorisk.operation;

import com.daliangang.majorisk.entity.RiskDatabaseCompany;
import com.daliangang.majorisk.entity.RiskDatabaseDepartment;
import com.daliangang.majorisk.entity.RiskDatabaseEnterprise;
import lombok.Data;
import org.springframework.stereotype.Service;
import xyz.erupt.annotation.Erupt;
import xyz.erupt.annotation.EruptField;
import xyz.erupt.annotation.fun.DataProxy;
import xyz.erupt.annotation.fun.OperationHandler;
import xyz.erupt.annotation.sub_field.Edit;
import xyz.erupt.annotation.sub_field.EditType;
import xyz.erupt.annotation.sub_field.Readonly;
import xyz.erupt.annotation.sub_field.sub_edit.ChoiceType;
import xyz.erupt.core.util.EruptSpringUtil;
import xyz.erupt.core.util.EruptUtil;
import xyz.erupt.jpa.dao.EruptDao;
import xyz.erupt.jpa.model.BaseModel;
import xyz.erupt.toolkit.handler.RemoteEntityChoiceFetchHandler;
import xyz.erupt.toolkit.utils.EruptDaoUtils;
import xyz.erupt.toolkit.utils.SqlUtils;
import xyz.erupt.upms.model.EruptUser;
import xyz.erupt.upms.model.auth.DataAuthHandler;
import xyz.erupt.upms.service.EruptUserService;

import javax.annotation.Resource;
import javax.transaction.Transactional;
import java.util.List;

@Service
public class RiskDatabaseSelectHandler implements OperationHandler<RiskDatabaseCompany, RiskDatabaseSelectHandler.EnterpriseSelectionForm> {

    @Erupt(name = "添加到企业数据库",authVerify = false, dataProxy = EnterpriseSelectionForm.class)
    @Data
    public static class EnterpriseSelectionForm extends BaseModel implements DataProxy<EnterpriseSelectionForm> {
        @EruptField(
                //views = @View(title = "企业名称",ifRender = @ExprBool(exprHandler = CompanyRenderHandler.class)),
                edit = @Edit(title = "企业名称", readonly = @Readonly(exprHandler = DataAuthHandler.class),
                        type = EditType.CHOICE, notNull = true,
                        choiceType = @ChoiceType(fullSpan = true, fetchHandler = RemoteEntityChoiceFetchHandler.class, fetchHandlerParams = {"main", "Enterprise", "org_code,name"})))
        private String company;

        @Override
        public void addBehavior(EnterpriseSelectionForm form) {
            EruptUser eruptUser = EruptSpringUtil.getBean(EruptUserService.class).getCurrentEruptUser();
            if (eruptUser != null && eruptUser.getEruptOrg() != null)
                form.setCompany(eruptUser.getEruptOrg().getCode());
            //form.setCompany("dbbf7da6");
        }
    }

    @Resource
    private EruptDao eruptDao;

    @Override
    @Transactional
    public String exec(List<RiskDatabaseCompany> data, EnterpriseSelectionForm form, String[] param) {
        for (RiskDatabaseCompany database : data) {
            String sql = "select * from " + EruptUtil.getTable(RiskDatabaseEnterprise.class)
                    + " where common_risk_id=" + database.getId() + " and org_code=" + SqlUtils.wrapStr(form.getCompany());
            RiskDatabaseEnterprise risk = EruptDaoUtils.selectOne(sql, RiskDatabaseEnterprise.class);
            if (risk == null) {
                risk = EruptDaoUtils.cast(database, RiskDatabaseEnterprise.class);
                risk.setId(null);
                risk.setCommonRiskId(database.getId());
                risk.setOrgCode(form.getCompany());
                eruptDao.persist(risk);
            }
        }
        return null;
    }
}
