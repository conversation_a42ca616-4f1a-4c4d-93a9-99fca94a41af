package com.daliangang.majorisk.entity;

import com.daliangang.majorisk.proxy.FiveChecklistsMonitorDataProxy;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import xyz.erupt.annotation.Erupt;
import xyz.erupt.annotation.EruptField;
import xyz.erupt.annotation.sub_erupt.Power;
import xyz.erupt.annotation.sub_field.Edit;
import xyz.erupt.annotation.sub_field.EditType;
import xyz.erupt.annotation.sub_field.Readonly;
import xyz.erupt.annotation.sub_field.View;
import xyz.erupt.annotation.sub_field.sub_edit.InputType;
import xyz.erupt.toolkit.db.Comment;
import xyz.erupt.upms.model.auth.DataAuthModel;

import javax.persistence.*;

/**
 * <AUTHOR>
 * @since :2023/4/4:9:57
 */

@Erupt(name = "五清单监测监控", power = @Power(add = true, delete = true, export = false, importable = true, viewDetails = true, edit = true)
        , dataProxy = FiveChecklistsMonitorDataProxy.class
)
@Table(name = "tb_fivechecklists_monitor")
@Entity
@Getter
@Setter
@Comment("五清单监测监控")
@ApiModel("五清单监测监控")
public class FiveChecklistsMonitor extends DataAuthModel {
//    @EruptField(
//            views = @View(title = "企业名称"),
//            edit = @Edit(title = "企业名称", readonly = @Readonly(exprHandler = DataAuthInvoker.class), type = EditType.CHOICE, notNull = true,
//                    choiceType = @ChoiceType(fetchHandler = RemoteEntityChoiceFetchHandler.class, fetchHandlerParams ={"main","Enterprise","org_code,name"})))
//    @Comment("企业名称")
//    @ApiModelProperty("企业名称")
//    private String company;

    @Lob
    @EruptField(
            views = @View(title = "监测监控计划"),
            edit = @Edit(title = "监测监控计划", type = EditType.TEXTAREA,placeHolder = "说明：本单位针对重大风险制定的监测监控计划，监控项目包括但不限于视频监控、工艺参数（压力、温度、流量、液位等）、可燃/有毒气体报警、火灾报警、风速、设备设施的定期检验等。示例：公司按照隐患排查表/清单对XX（排查项目）定期开展隐患排查，主要内容包括：XX（排查内容）、XX、XX……，每XX（周期）进行1次；对储罐及安全附件、防爆电气、消防设施……等相关设备设施每XX（周期）开展定期检测；高风险作业可通过视频监控实时查看；工艺参数（压力、温度、流量、液位等）可通过XX控制系统实时监测监控……",
                    inputType = @InputType(length = 500)))
    @Comment("监测监控计划")
    @ApiModelProperty("监测监控计划")
    private String monitoring;

    @EruptField(
            views = @View(title = "监测监控数据及预警信息", show = false),
            edit = @Edit(title = "监测监控数据及预警信息", type = EditType.DIVIDE))
    @Transient
    @Comment("监测监控数据及预警信息")
    @ApiModelProperty("监测监控数据及预警信息")
    private String runawayConsequences;

    @EruptField(
            views = @View(title = "装卸船作业数量", show = false),
            edit = @Edit(title = "装卸船作业数量", type = EditType.INPUT, readonly = @Readonly,
                    inputType = @InputType))
    @Comment("装卸船作业数量")
    @ApiModelProperty("装卸船作业数量")
    private String shipWorkNum;

    @EruptField(
            views = @View(title = "装卸船作业位置", show = false),
            edit = @Edit(title = "装卸船作业位置", type = EditType.INPUT, readonly = @Readonly,
                    inputType = @InputType))
    @Comment("装卸船作业位置")
    @ApiModelProperty("装卸船作业位置")
    private String shipWorkAddress;

    @EruptField(
            views = @View(title = "填充", show = false),
            edit = @Edit(title = "填充", type = EditType.EMPTY,
                    inputType = @InputType))
    @Comment("填充")
    @Transient
    @ApiModelProperty("填充")
    private String tc;

    @EruptField(
            views = @View(title = "装卸车作业数量", show = false),
            edit = @Edit(title = "装卸车作业数量", type = EditType.INPUT, readonly = @Readonly,
                    inputType = @InputType))
    @Comment("装卸车作业数量")
    @ApiModelProperty("装卸车作业数量")
    private String carWorkNum;

    @EruptField(
            views = @View(title = "装卸车作业位置", show = false),
            edit = @Edit(title = "装卸车作业位置", type = EditType.INPUT, readonly = @Readonly,
                    inputType = @InputType))
    @Comment("装卸车作业位置")
    @ApiModelProperty("装卸车作业位置")
    private String carWorkAddress;

    @EruptField(
            views = @View(title = "填充", show = false),
            edit = @Edit(title = "填充", type = EditType.EMPTY,
                    inputType = @InputType))
    @Comment("填充")
    @Transient
    @ApiModelProperty("填充")
    private String tc1;

    @EruptField(
            views = @View(title = "装卸火车作业数量", show = false),
            edit = @Edit(title = "装卸火车作业数量", type = EditType.INPUT, readonly = @Readonly,
                    inputType = @InputType))
    @Comment("装卸火车作业数量")
    @ApiModelProperty("装卸火车作业数量")
    private String trainWorkNum;

    @EruptField(
            views = @View(title = "装卸火车作业位置", show = false),
            edit = @Edit(title = "装卸火车作业位置", type = EditType.INPUT, readonly = @Readonly,
                    inputType = @InputType))
    @Comment("装卸火车作业位置")
    @ApiModelProperty("装卸火车作业位置")
    private String trainWorkAddress;

    @EruptField(
            views = @View(title = "填充", show = false),
            edit = @Edit(title = "填充", type = EditType.EMPTY,
                    inputType = @InputType))
    @Comment("填充")
    @Transient
    @ApiModelProperty("填充")
    private String tc2;

    @EruptField(
            views = @View(title = "动火作业数量", show = false),
            edit = @Edit(title = "动火作业数量", type = EditType.INPUT, readonly = @Readonly,
                    inputType = @InputType))
    @Comment("动火作业数量")
    @ApiModelProperty("动火作业数量")
    private String fireWorkNum;

    @EruptField(
            views = @View(title = "受限空间作业数量", show = false),
            edit = @Edit(title = "受限空间作业数量", type = EditType.INPUT, readonly = @Readonly,
                    inputType = @InputType))
    @Comment("受限空间作业数量")
    @ApiModelProperty("受限空间作业数量")
    private String restrictedWorkNum;

    @EruptField(
            views = @View(title = "实时天气", show = false),
            edit = @Edit(title = "实时天气", type = EditType.DIVIDE))
    @Transient
    @Comment("实时天气")
    @ApiModelProperty("实时天气")
    private String sstq;

    @EruptField(
            views = @View(title = "天气情况", show = false),
            edit = @Edit(title = "天气情况", type = EditType.INPUT, readonly = @Readonly,
                    inputType = @InputType(fullSpan = true)))
    @Comment("天气情况")
    @ApiModelProperty("天气情况")
    private String weatherConditions;

    @EruptField(
            views = @View(title = "天气预警", show = false),
            edit = @Edit(title = "天气预警", type = EditType.INPUT, readonly = @Readonly,
                    inputType = @InputType(fullSpan = true)))
    @Comment("天气预警")
    @ApiModelProperty("天气预警")
    private String weatherWarning;

    @EruptField(
            views = @View(title = "设备设施检验检测状态", show = false),
            edit = @Edit(title = "设备设施检验检测状态", type = EditType.DIVIDE))
    @Transient
    @Comment("设备设施检验检测状态")
    @ApiModelProperty("设备设施检验检测状态")
    private String sbss;

    @EruptField(
            views = @View(title = "设备状态", show = false),
            edit = @Edit(title = "设备状态", type = EditType.INPUT, readonly = @Readonly,
                    inputType = @InputType(fullSpan = true)))
    @Comment("设备状态")
    @ApiModelProperty("设备状态")
    private String deviceStatus;


//    @OneToOne
//    @EruptField
//    @JoinColumn(name = "tb_fivechecklists_foundation_id")
//    private FiveChecklistsFoundation fiveChecklistsFoundation;

    @OneToOne
    @EruptField
    @JoinColumn(name = "tb_five_detail_id")
    private FiveDetail fiveDetail;

}
