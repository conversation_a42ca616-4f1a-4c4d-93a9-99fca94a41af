/*
 * Copyright © 2020-2035 yundingyun All rights reserved.
 */
package com.daliangang.rndpub.entity;

import com.daliangang.rndpub.operation.InspectionItemsManualSelectionHandler;
import com.daliangang.rndpub.operation.InspectionItemsSystemExtractionHandler;
import com.daliangang.rndpub.operation.ProcedureExportCheckItemsHandler;
import com.daliangang.rndpub.proxy.InspectionItemsDataProxy;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import xyz.erupt.annotation.Erupt;
import xyz.erupt.annotation.EruptField;
import xyz.erupt.annotation.expr.ExprBool;
import xyz.erupt.annotation.sub_erupt.PageModel;
import xyz.erupt.annotation.sub_erupt.Power;
import xyz.erupt.annotation.sub_erupt.RowOperation;
import xyz.erupt.annotation.sub_erupt.Tpl;
import xyz.erupt.annotation.sub_field.Edit;
import xyz.erupt.annotation.sub_field.EditType;
import xyz.erupt.annotation.sub_field.View;
import xyz.erupt.annotation.sub_field.sub_edit.BoolType;
import xyz.erupt.annotation.sub_field.sub_edit.InputType;
import xyz.erupt.annotation.sub_field.sub_edit.Search;
import xyz.erupt.core.context.MetaDrill;
import xyz.erupt.jpa.model.MetaModel;
import xyz.erupt.toolkit.db.Comment;
import xyz.erupt.toolkit.utils.EruptDaoUtils;

import javax.persistence.*;
import java.util.List;

@Erupt(name = "检查事项", power = @Power(add = false, delete = false, export = true, importable = true, viewDetails = true, edit = false),selectionVisible = false
        , dataProxy = InspectionItemsDataProxy.class//, orderBy = "state desc, series_id asc"
        , pageModel = @PageModel(flag = true,pageProxy = InspectionItemsDataProxy.class)
        , rowOperation = {
        @RowOperation(title = "自动抽取", icon = "fa fa-chain-broken", eruptClass = InspectionItemsSystemExtractionHandler.DrawInspectionItemForm.class, operationHandler = InspectionItemsSystemExtractionHandler.class, mode = RowOperation.Mode.BUTTON),
        @RowOperation(title = "手动抽取", icon = "fa fa-hand-o-up",
                show = @ExprBool(exprHandler = InspectionItems.class),
                mode = RowOperation.Mode.BUTTON, type = RowOperation.Type.TPL, tplHeight = "88vh", tpl = @Tpl(path = "/tpl/AvueForm.ftl", tplHandler = InspectionItemsManualSelectionHandler.class)),
        @RowOperation(title = "导出检查事项", icon = "fa fa-arrow-down", operationHandler = ProcedureExportCheckItemsHandler.class, mode = RowOperation.Mode.BUTTON),
        @RowOperation(confirm = false,type = RowOperation.Type.CLOSE,title = "确认", mode = RowOperation.Mode.BUTTON)
})
@Table(name = "tb_inspection_items")
@Entity
@Getter
@Setter
@Comment("检查事项")
@ApiModel("检查事项")
public class InspectionItems extends MetaModel implements ExprBool.ExprHandler{

    @ManyToOne
    @EruptField( sort = 1,
            views = @View(title = "所属流程", column = "name"),
            edit = @Edit(title = "所属流程", type = EditType.REFERENCE_TREE, show = false)
    )
    private Procedure procedure;

    @EruptField(
            views = @View(title = "状态", show = true, sortable = true, export = false),
            edit = @Edit(title = "状态", type = EditType.BOOLEAN, notNull = true, show = false, search = @Search(vague = true),
                    boolType = @BoolType(trueText = "已抽取", falseText = "未抽取")))
    @Comment("状态")
    @ApiModelProperty("状态")
    private Boolean state;

    /**
     * 序号
     */
    @Transient
    private Integer num;

    @EruptField(
            views = @View(title = "检查事项ID", export = false,show = false),
            edit = @Edit(title = "检查事项ID", type = EditType.INPUT, show = true,
                    inputType = @InputType))
    @Comment("检查事项ID")
    @ApiModelProperty("检查事项ID")
    private Long seriesId;

    @EruptField(
            views = @View(title = "检查事项", show = true),
            edit = @Edit(title = "检查事项", type = EditType.INPUT, notNull = true
            ))
    @Comment("检查事项")
    @ApiModelProperty("检查事项")
    private String inspectionItems;

    @EruptField(
            views = @View(title = "检查内容（一级）", show = true),
            edit = @Edit(title = "检查内容（一级）", type = EditType.TEXTAREA, notNull = true))
    @Comment("检查内容（一级）")
    @ApiModelProperty("检查内容（一级）")
    private String checkFirst;

    @EruptField(
            views = @View(title = "检查内容（二级）", show = true),
            edit = @Edit(title = "检查内容（二级）", type = EditType.TEXTAREA, notNull = true))
    @Comment("检查内容（二级）")
    @ApiModelProperty("检查内容（二级）")
    private String checkSecond;

    @EruptField(
            views = @View(title = "检查依据", show = true),
            edit = @Edit(title = "检查依据", type = EditType.TEXTAREA, notNull = true))
    @Comment("检查依据")
    @ApiModelProperty("检查依据")
    private @Lob String basis;

    @EruptField(
            views = @View(title = "检查方法"),
            edit = @Edit(title = "检查方法", type = EditType.TEXTAREA, notNull = true))
    @Comment("检查方法")
    @ApiModelProperty("检查方法")
    private @Lob String method;

    @EruptField(
            views = @View(title = "检查标准", show = true, export = true),
            edit = @Edit(title = "检查标准", type = EditType.TEXTAREA, notNull = true))
    @Comment("检查标准")
    @ApiModelProperty("检查标准")
    private @Lob String standard;

    @EruptField(
            views = @View(title = "适用企业类型"),
            edit = @Edit(title = "适用企业类型", type = EditType.INPUT, notNull = true))
    @Comment("适用企业类型")
    @ApiModelProperty("适用企业类型")
    private String type;


    @Override
    public boolean handler(boolean expr, String[] params) {
        //获取企业信息
        String enterpriseSql = "select enterprise_name,type from tb_enterprise_information where procedure_id=" + MetaDrill.getDrillId() + " and state=1";
        List<EnterpriseInformation> enterpriseList = EruptDaoUtils.selectOnes(enterpriseSql, EnterpriseInformation.class);
//        if (enterpriseList.size() == 0) {
//            NotifyUtils.showErrorMsg("请先抽取企业");
//        }
        return enterpriseList.size() != 0;
    }
}
