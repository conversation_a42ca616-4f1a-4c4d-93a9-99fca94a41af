/*
 * Copyright © 2020-2035 yundingyun All rights reserved.
 */
package com.daliangang.device.entity;

import com.daliangang.core.CompanyRenderHandler;
import com.daliangang.device.proxy.CombustibleGasDataProxy;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import xyz.erupt.annotation.Erupt;
import xyz.erupt.annotation.EruptField;
import xyz.erupt.annotation.PreDataProxy;
import xyz.erupt.annotation.expr.ExprBool;
import xyz.erupt.annotation.sub_erupt.Power;
import xyz.erupt.annotation.sub_erupt.RowOperation;
import xyz.erupt.annotation.sub_field.Edit;
import xyz.erupt.annotation.sub_field.EditType;
import xyz.erupt.annotation.sub_field.Readonly;
import xyz.erupt.annotation.sub_field.View;
import xyz.erupt.annotation.sub_field.sub_edit.*;
import xyz.erupt.mns.core.DelayMsg;
import xyz.erupt.toolkit.attachment.AttachmentDownloadHandler;
import xyz.erupt.toolkit.colorfont.ColorStateTimeFontDataProxy;
import xyz.erupt.toolkit.db.Comment;
import xyz.erupt.toolkit.handler.EruptDictCodeChoiceFetchHandler;
import xyz.erupt.toolkit.handler.EruptDictTagFetchHandler;
import xyz.erupt.toolkit.handler.RemoteEntityChoiceFetchHandler;
import xyz.erupt.upms.model.auth.DataAuthHandler;
import xyz.erupt.upms.model.auth.DataAuthModel;

import javax.persistence.Entity;
import javax.persistence.Lob;
import javax.persistence.Table;

@Erupt(name = "可燃气体报警器检测", power = @Power(add = true, delete = true, export = false, importable = true, viewDetails = true, edit = true),
        dataProxy = CombustibleGasDataProxy.class,
        rowOperation = {
                @RowOperation(title = "下载附件", icon = "fa fa-download", operationHandler = AttachmentDownloadHandler.class, mode = RowOperation.Mode.SINGLE)
        })
@Table(name = "tb_combustible_gas")
@Entity
@Getter
@Setter
@Comment("可燃气体报警器检测")
@ApiModel("可燃气体报警器检测")
@PreDataProxy(ColorStateTimeFontDataProxy.class)
@DelayMsg(timeKey = "validityPeriod")
public class CombustibleGas extends DataAuthModel {
    @EruptField(
            views = @View(title = "企业名称", width = "250px"),
            edit = @Edit(title = "企业名称", search = @Search(ifRender = @ExprBool(exprHandler = CompanyRenderHandler.class)),
                    readonly = @Readonly(exprHandler = DataAuthHandler.class), type = EditType.CHOICE, notNull = true,
                    choiceType = @ChoiceType(fullSpan = true,fetchHandler = RemoteEntityChoiceFetchHandler.class, fetchHandlerParams = {"main", "Enterprise", "org_code,name"})))
    @Comment("企业名称")
    @ApiModelProperty("企业名称")
    private String company;

    @EruptField(
            views = @View(title = "检测报告名称",width = "200px"),
            edit = @Edit(title = "检测报告名称", type = EditType.INPUT, search = @Search(vague = true), notNull = true,
                    inputType = @InputType))
    @Comment("检测报告名称")
    @ApiModelProperty("检测报告名称")
    private String testReportName;

    @EruptField(
            views = @View(title = "检测报告有效期限", width = "100px"),
            edit = @Edit(title = "检测报告有效期限", type = EditType.DATE, notNull = true,
                    dateType = @DateType))
    @Comment("检测报告有效期限")
    @ApiModelProperty("检测报告有效期限")
    private java.util.Date validityPeriod;

    @EruptField(views = @View(title = "状态",width = "100px"))
    @Comment("状态")
    @ApiModelProperty("状态")
    private String state;

    @EruptField(views = @View(title = "状态",show = false),
            edit = @Edit(title = "状态", type = EditType.CHOICE, search = @Search, show = false,
                    choiceType = @ChoiceType(fetchHandler = EruptDictCodeChoiceFetchHandler.class, fetchHandlerParams = "checkStatus")
            )
    )
    @Comment("状态")
    @ApiModelProperty("状态")
    private String checkStatus;

    @EruptField(views = @View(title = "检测报告检测范围", show = false), edit = @Edit(title = "检测报告检测范围", type = EditType.TAGS, notNull = false,
            show = false,
            tagsType = @TagsType(allowExtension = false, fetchHandler = EruptDictTagFetchHandler.class, fetchHandlerParams = "detectionRange")))
    @Comment("检测报告检测范围")
    @ApiModelProperty("检测报告检测范围")
    private String detectionRange;

    @Lob
    @EruptField(
            views = @View(title = "说明", show = false),
            edit = @Edit(title = "说明", type = EditType.TEXTAREA, show = false,
                    inputType = @InputType))
    @Comment("说明")
    @ApiModelProperty("说明")
    private String description;

    @EruptField(
            views = @View(title = "检测报告附件", show = false),
            edit = @Edit(title = "检测报告附件", type = EditType.ATTACHMENT, notNull = true,
                    attachmentType = @AttachmentType(type = AttachmentType.Type.BASE, maxLimit = 20, fileTypes = {".pdf", ".doc", ".docx", ".xlsx", ".png", ".jpg", ".xls", ".ppt", ".pptx", ".zip", ".rar"})))
    @Comment("检测报告附件")
    @ApiModelProperty("检测报告附件")
    private String reportAttachments;



}
