package com.daliangang.rndpub.proxy;

import com.daliangang.rndpub.entity.CompanyInspectDetail;
import com.daliangang.rndpub.entity.InspectionResults;
import com.daliangang.rndpub.entity.Procedure;
import com.daliangang.workbench.entity.Enterprise;
import org.springframework.stereotype.Service;
import xyz.erupt.annotation.fun.DataProxy;
import xyz.erupt.annotation.query.Condition;
import xyz.erupt.jpa.dao.EruptDao;
import xyz.erupt.toolkit.utils.EruptDaoUtils;
import xyz.erupt.toolkit.utils.SqlUtils;

import javax.annotation.Resource;
import javax.transaction.Transactional;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.ZoneId;
import java.util.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 * @date 2023/9/25
 */
@Service
public class CompanyInspectDetailDataProxy implements DataProxy<CompanyInspectDetail> {

    @Resource
    private EruptDao eruptDao;

    @Override
    public void addBehavior(CompanyInspectDetail companyInspectDetail) {
//       companyInspectDetail.setInspectionResult("TO_BE_RECTIFIED");
    }


    @Override
    public String beforeFetch(List<Condition> conditions) {
        //只能查看已发布的数据
        AtomicReference<String> returnStr = new AtomicReference<>(" 1 = 1 and publish_status = 1 ") ;
        //检查对象查询条件
        Optional<Condition> checkObjectFirst = conditions.stream().filter(condition ->
                "company".equals(condition.getKey())
        ).findFirst();
        checkObjectFirst.ifPresent( f ->{
            returnStr.set(returnStr.get()+" and check_object like CONCAT('%'," + SqlUtils.wrapStr((String) f.getValue())+",'%')");
            conditions.remove(f) ;
        });
        //是否逾期查询条件
        Optional<Condition> beOverdueFirst = conditions.stream().filter(condition ->
                "beOverdue".equals(condition.getKey())
        ).findFirst();
        beOverdueFirst.ifPresent( f ->{
            returnStr.set((Boolean)f.getValue() ? returnStr.get()+" and date(now()) <=date(deadline)":returnStr.get()+" and date(now()) >date(deadline)");
            conditions.remove(f) ;
        });
        //年度查询条件
        Optional<Condition> inspectYearCondition = conditions.stream().filter(condition ->
                "inspectYear".equals(condition.getKey())
        ).findFirst();
        inspectYearCondition.ifPresent( f ->{
            returnStr.set(returnStr.get() + " and inspection_name in (select id from Procedure where year(inspection_date)='"+f.getValue()+"')");
            conditions.remove(f) ;
        });

        return returnStr.get();
    }


    @Override
    @Transactional
    public void afterFetch(Collection<Map<String, Object>> list) {
        LocalDate localDateTime = LocalDate.now();
        ZoneId zoneId = ZoneId.systemDefault();
        Date now = Date.from(localDateTime.atStartOfDay().atZone(zoneId).toInstant());
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy");
        List<InspectionResults> fields = EruptDaoUtils.convert(list, InspectionResults.class);
        for (InspectionResults field : fields) {
            if(null != field.getDeadline()){
                EruptDaoUtils.updateAfterFetch(list, field.getId(), "beOverdue", now.compareTo(field.getDeadline()) <= 0);
            }
            //设置年度值
            String yearSql=String.format("select * from tb_procedure where id='%s'",field.getInspectionName());
            Procedure procedure = EruptDaoUtils.selectOne(yearSql, Procedure.class);
            if(null != procedure){
                Date inspectionDate = procedure.getInspectionDate();
                String updateSql=String.format("update tb_inspection_results set inspect_year='%s'",simpleDateFormat.format(inspectionDate));
                eruptDao.getJdbcTemplate().execute(updateSql);
            }

        }

    }

    @Override
    public void beforeUpdate(CompanyInspectDetail companyInspectDetail) {
        String checkObject = companyInspectDetail.getCheckObject();
        if(checkObject!=null){
            Pattern p = Pattern.compile("[\u4e00-\u9fa5]");
            Matcher m = p.matcher(checkObject);
            if(m.find()){
                String selectSql="select * from tb_enterprise where name='"+checkObject+"'";
                List<Enterprise> enterpriseList = EruptDaoUtils.selectOnes(selectSql, Enterprise.class);
                if(!enterpriseList.isEmpty()){
                    companyInspectDetail.setCheckObject(enterpriseList.get(0).getOrgCode());
                }

            }
        }

    }
}
