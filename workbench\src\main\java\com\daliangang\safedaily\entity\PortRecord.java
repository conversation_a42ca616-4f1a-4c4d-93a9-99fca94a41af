/*
 * Copyright © 2020-2035 yundingyun All rights reserved.
 */
package com.daliangang.safedaily.entity;

import com.daliangang.core.CompanyRenderHandler;
import com.daliangang.safedaily.proxy.PortRecordDataProxy;
import com.daliangang.workbench.handler.EmployeeExternalRenderHandler;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import xyz.erupt.annotation.Erupt;
import xyz.erupt.annotation.EruptField;
import xyz.erupt.annotation.PreDataProxy;
import xyz.erupt.annotation.expr.ExprBool;
import xyz.erupt.annotation.sub_erupt.Power;
import xyz.erupt.annotation.sub_erupt.RowOperation;
import xyz.erupt.annotation.sub_field.Edit;
import xyz.erupt.annotation.sub_field.EditType;
import xyz.erupt.annotation.sub_field.Readonly;
import xyz.erupt.annotation.sub_field.View;
import xyz.erupt.annotation.sub_field.sub_edit.*;
import xyz.erupt.toolkit.attachment.AttachmentDownloadHandler;
import xyz.erupt.toolkit.colorfont.ColorStateTimeFontDataProxy;
import xyz.erupt.toolkit.db.Comment;
import xyz.erupt.toolkit.handler.DelOperationHandler;
import xyz.erupt.toolkit.handler.EditOperationHandler;
import xyz.erupt.toolkit.handler.RemoteEntityChoiceFetchHandler;
import xyz.erupt.toolkit.utils.TplUtils;
import xyz.erupt.upms.model.auth.DataAuthHandler;
import xyz.erupt.upms.model.auth.DataAuthModel;

import javax.persistence.Entity;
import javax.persistence.Lob;
import javax.persistence.Table;
import javax.persistence.Transient;

@Erupt(name = "港口安全评价备案管理", power = @Power(add = true, delete = false, export = false, importable = true, viewDetails = true, edit = false)
        , dataProxy = PortRecordDataProxy.class
        , rowOperation = {
        @RowOperation(title = "编辑", icon = "fa fa-edit", code = TplUtils.EDIT_OPER_CODE, eruptClass = PortRecord.class,show = @ExprBool( exprHandler = EmployeeExternalRenderHandler.class),
                operationHandler = EditOperationHandler.class,  ifExprBehavior = RowOperation.IfExprBehavior.HIDE, mode = RowOperation.Mode.SINGLE),
        @RowOperation(title = "删除", icon = "fa fa-trash-o", show = @ExprBool( exprHandler = EmployeeExternalRenderHandler.class),operationHandler = DelOperationHandler.class, ifExprBehavior = RowOperation.IfExprBehavior.HIDE, mode = RowOperation.Mode.SINGLE),
//        @RowOperation(title = "上报", icon = "fa fa-arrow-circle-o-up", operationHandler = PortRecordEscalationHandler.class, ifExpr = "item.submitted=='未上报'", mode = RowOperation.Mode.SINGLE),
        @RowOperation(title = "下载附件", icon = "fa fa-download", operationHandler = AttachmentDownloadHandler.class, mode = RowOperation.Mode.SINGLE)
})
@Table(name = "tb_port_record")
@Entity
@Getter
@Setter
@Comment("港口安全评价备案管理")
@ApiModel("港口安全评价备案管理")
@PreDataProxy(ColorStateTimeFontDataProxy.class)
public class PortRecord extends DataAuthModel {
    @EruptField(
            views = @View(title = "企业名称",width = "150px"),
            edit = @Edit(title = "企业名称", search = @Search(ifRender = @ExprBool(exprHandler = CompanyRenderHandler.class) ), readonly = @Readonly(exprHandler = DataAuthHandler.class), type = EditType.CHOICE, notNull = true,
                    choiceType = @ChoiceType(fullSpan = true,fetchHandler = RemoteEntityChoiceFetchHandler.class, fetchHandlerParams = {"main", "Enterprise", "org_code,name"})))
    @Comment("企业名称")
    @ApiModelProperty("企业名称")
    private String company;

    @EruptField(
            views = @View(title = "上报状态",width = "100px",show = false),
            edit = @Edit(title = "上报状态", type = EditType.BOOLEAN, show = false, /*notNull = true, */
                    boolType = @BoolType(trueText = "已上报", falseText = "未上报")))
    @Comment("上报状态")
    @ApiModelProperty("上报状态")
    private Boolean submitted;

    @EruptField(
            views = @View(title = "项目名称",width = "150px"),
            edit = @Edit(title = "项目名称", type = EditType.INPUT, notNull = true,
                    search = @Search(vague = true),
                    inputType = @InputType))
    @Comment("项目名称")
    @ApiModelProperty("项目名称")
    private String grade;

    @Lob
    @EruptField(
            views = @View(title = "项目概况",width = "220px"),
            edit = @Edit(title = "项目概况", type = EditType.TEXTAREA, notNull = true,
                    inputType = @InputType))
    @Comment("项目概况")
    @ApiModelProperty("项目概况")
    private String issueDate;

    @EruptField(
            views = @View(title = "备案编号",width = "60px"),
            edit = @Edit(title = "备案编号", type = EditType.INPUT,
                    search = @Search(vague = true),
                    notNull = true,
                    inputType = @InputType))
    @Comment("备案编号")
    @ApiModelProperty("备案编号")
    private String effectiveDate;

    @EruptField(
            views = @View(title = "备案有效期至",width = "40px"),
            edit = @Edit(title = "备案有效期至", type = EditType.DATE,
                    dateType = @DateType, notNull = true))
    @Comment("备案有效期至")
    @ApiModelProperty("备案有效期至")
    private java.util.Date validityPeriod;

    @EruptField(
            edit = @Edit(title = "状态",show = false, search = @Search),
            views = @View(title = "状态",width = "100px")
    )
    @Comment("状态")
    @ApiModelProperty("状态")
    private String state;

    @EruptField(
            views = @View(title = "上传附件", show = false),
            edit = @Edit(title = "上传附件", type = EditType.DIVIDE))
    @Transient
    @Comment("上传附件")
    @ApiModelProperty("上传附件")
    private String fileDivide;

    @EruptField(
            views = @View(title = "评价报告", show = false),
            edit = @Edit(title = "评价报告", type = EditType.ATTACHMENT, notNull = true,
                    attachmentType = @AttachmentType(type = AttachmentType.Type.BASE, maxLimit = 20, fileTypes = {".pdf", ".doc", ".docx", ".xlsx", ".png", ".jpg", ".xls", ".ppt", ".pptx", ".zip", ".rar"})))
    @Comment("评价报告")
    @ApiModelProperty("评价报告")
    private String selfEvaluation;

    @EruptField(
            views = @View(title = "其他文件、资料", show = false),
            edit = @Edit(title = "其他文件、资料", type = EditType.ATTACHMENT, notNull = false,
                    attachmentType = @AttachmentType(type = AttachmentType.Type.BASE, maxLimit = 20, fileTypes = {".pdf", ".doc", ".docx", ".xlsx", ".png", ".jpg", ".xls", ".ppt", ".pptx", ".zip", ".rar"})))
    @Comment("其他文件、资料")
    @ApiModelProperty("其他文件、资料")
    private String otherFile;

    @EruptField(
            views = @View(title = "是否上报", show = false),
            edit = @Edit(title = "是否上报", type = EditType.BOOLEAN, show = false,
                    boolType = @BoolType(trueText = "是", falseText = "否")))
    @Comment("是否上报")
    @ApiModelProperty("是否上报")
    private Boolean isReport;


}
