package com.daliangang.datascreen.regulatoryboard.response;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @Author: chong<PERSON>glin
 * @Date: 2024/11/26 13:53
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class UnreportedCompanyResponse {
    private String title;
    private List<CompanyDetail> company;
    private String code;

    @Data
    @Builder(toBuilder = true)
    @NoArgsConstructor
    @AllArgsConstructor
    public static class CompanyDetail {
        private String orgCode;
        private String companyName;
    }
}
