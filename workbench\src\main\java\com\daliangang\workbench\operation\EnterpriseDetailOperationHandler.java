package com.daliangang.workbench.operation;

import com.daliangang.workbench.entity.EnterpriseDetail;
import org.springframework.stereotype.Service;
import xyz.erupt.annotation.fun.OperationHandler;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/7/25
 */
@Service
public class EnterpriseDetailOperationHandler implements OperationHandler<EnterpriseDetail,Void> {
    @Override
    public String exec(List<EnterpriseDetail> data, Void unused, String[] param) {

        return "location.href='/#/build/tree/EnterpriseInfo'";
    }
}
