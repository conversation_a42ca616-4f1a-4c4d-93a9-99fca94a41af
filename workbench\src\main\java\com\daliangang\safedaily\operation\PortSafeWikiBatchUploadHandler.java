package com.daliangang.safedaily.operation;

import com.daliangang.safedaily.entity.PortSafeWikiDir;
import com.daliangang.safedaily.entity.PortSafeWikiEntry;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import xyz.erupt.annotation.Erupt;
import xyz.erupt.annotation.EruptField;
import xyz.erupt.annotation.fun.OperationHandler;
import xyz.erupt.annotation.sub_field.Edit;
import xyz.erupt.annotation.sub_field.EditType;
import xyz.erupt.annotation.sub_field.sub_edit.AttachmentType;
import xyz.erupt.jpa.dao.EruptDao;
import xyz.erupt.jpa.model.BaseModel;
import xyz.erupt.toolkit.utils.FileUtils;
import xyz.erupt.toolkit.utils.NotifyUtils;
import xyz.erupt.toolkit.utils.SqlUtils;
import xyz.erupt.upms.util.MetaUtil;

import javax.annotation.Resource;
import javax.transaction.Transactional;
import java.util.List;
import java.util.StringTokenizer;

@Service
@Slf4j
public class PortSafeWikiBatchUploadHandler implements OperationHandler<PortSafeWikiDir, PortSafeWikiBatchUploadHandler.PortSafeWikiBatchUploadForm> {

    @Resource
    private EruptDao eruptDao;

    @Override
    @Transactional
    public String exec(List<PortSafeWikiDir> data, PortSafeWikiBatchUploadForm uploadForm, String[] param) {
        StringTokenizer st = new StringTokenizer(uploadForm.getFile(), "|");
        while (st.hasMoreTokens()) {
            String path = st.nextToken().trim();
            if (!path.contains(".") || path.endsWith(".zip") || path.contains("MAC") || path.endsWith(".DS_Store"))
                continue;
            //log.error(path);

            String[] nameArr = path.split("/");
            if (nameArr == null || nameArr.length < 2) NotifyUtils.showErrorNotify("压缩包内容不对");
            String category = nameArr[nameArr.length - 2];
            String filename = nameArr[nameArr.length - 1];
            log.info("category=" + category + ", filename=" + filename);

            PortSafeWikiDir dir = eruptDao.queryEntity(PortSafeWikiDir.class, "name=" + SqlUtils.wrapStr(category));
            if (dir == null) {
                dir = new PortSafeWikiDir();
                dir.setName(category);
                MetaUtil.prepareMetaInfo(dir, true, true);
                eruptDao.persist(dir);
            }

            //插入记录
            String entryName = FileUtils.getFilename(path);
            PortSafeWikiEntry entry = eruptDao.queryEntity(PortSafeWikiEntry.class, "name=" + SqlUtils.wrapStr(entryName));
            if (entry == null) {
                entry = new PortSafeWikiEntry();
                entry.setWikiDir(dir);
                entry.setName(entryName);
                entry.setFile(path);
                MetaUtil.prepareMetaInfo(entry, true, true);
                eruptDao.persist(entry);
            } else {
                entry.setFile(path);
                MetaUtil.prepareMetaInfo(entry, false, true);
                eruptDao.merge(entry);
            }

        }
        return null;
    }

    @Erupt(name = "港口安全知识库批量上传", authVerify = false)
    @Data
    public static class PortSafeWikiBatchUploadForm extends BaseModel {
        @EruptField(
                edit = @Edit(title = "知识库文件（压缩包）", type = EditType.ATTACHMENT, notNull = true,
                        attachmentType = @AttachmentType(type = AttachmentType.Type.BASE, maxLimit = 1, fileTypes = {".zip"}, unzip = true)))
        private String file;
    }
}
