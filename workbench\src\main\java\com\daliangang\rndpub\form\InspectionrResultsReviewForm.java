package com.daliangang.rndpub.form;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import xyz.erupt.annotation.Erupt;
import xyz.erupt.annotation.EruptField;
import xyz.erupt.annotation.sub_field.Edit;
import xyz.erupt.annotation.sub_field.EditType;
import xyz.erupt.annotation.sub_field.Readonly;
import xyz.erupt.annotation.sub_field.View;
import xyz.erupt.annotation.sub_field.sub_edit.*;
import xyz.erupt.toolkit.db.Comment;
import xyz.erupt.toolkit.handler.SqlChoiceFetchHandler;

import javax.persistence.Id;
import javax.persistence.Lob;
import javax.persistence.Transient;

@Data
@Erupt(name = "复查", authVerify = false)
public class InspectionrResultsReviewForm {
    @Id
    @EruptField
    @Transient
    private String id;

    @EruptField(
            views = @View(title = "整改id"),
            edit = @Edit(title = "整改id", type = EditType.INPUT, notNull = false,show = false))
    @Comment("整改id")
    @ApiModelProperty("整改id")
    private Long inspectionResultsId ;

    @Transient
    @EruptField(
            edit = @Edit(title = "检查名称", type = EditType.CHOICE, notNull = false,readonly = @Readonly,
                    choiceType = @ChoiceType(fetchHandler = SqlChoiceFetchHandler.class, fetchHandlerParams = "select id,name from tb_procedure")
            ))
    private String inspectionName;


    @EruptField(
            edit = @Edit(title = "检查对象", type = EditType.CHOICE, notNull = true,readonly = @Readonly,
                    choiceType = @ChoiceType(fetchHandler = SqlChoiceFetchHandler.class,
                            fetchHandlerParams = {"select org_code,enterprise_name from tb_enterprise_information", "5000", "and state=1"})))
    @Transient
    private String checkObject;

    @EruptField(
            edit = @Edit(title = "检查事项", type = EditType.CHOICE, notNull = false,readonly = @Readonly,
                    choiceType = @ChoiceType(fetchHandler = SqlChoiceFetchHandler.class,
                            fetchHandlerParams = {"select distinct inspection_items from tb_inspection_items", "5000", "and state=1"})))
    @Transient
    private String inspectionItems;

//    @EruptField(
//            edit = @Edit(title = "检查内容（一级）", type = EditType.INPUT, readonly = @Readonly))
//    @Transient
//    private String inspectionContent;

    @EruptField(
            edit = @Edit(title = "问题描述", type = EditType.TEXTAREA, readonly = @Readonly))
    @Transient
    private @Lob
    String problemDescription;


    @EruptField(
            edit = @Edit(title = "检查依据", type = EditType.INPUT, readonly = @Readonly))
    @Transient
    private String inspectionBasis;

    @EruptField(
            edit = @Edit(title = "现场照片", type = EditType.ATTACHMENT, notNull = false, readonly = @Readonly,
                    attachmentType = @AttachmentType(type = AttachmentType.Type.BASE, maxLimit = 1, fileTypes = {".pdf", ".doc", ".docx", ".xlsx", ".png", ".jpg", ".xls", ".ppt", ".pptx", ".zip", ".rar"})))
    @Transient
    private String inspectionBasisFile;

    @EruptField(
            edit = @Edit(title = "整改建议", type = EditType.TEXTAREA, readonly = @Readonly))
    @Transient
    private String proposal;
//    @EruptField(
//            edit = @Edit(title = "检查依据附件名称", type = EditType.ATTACHMENT, notNull = false, readonly = @Readonly,
//                    attachmentType = @AttachmentType(type = AttachmentType.Type.BASE, maxLimit = 1, fileTypes = {".pdf", ".doc", ".docx", ".xlsx", ".png", ".jpg", ".xls", ".ppt", ".pptx", ".zip", ".rar"})))
//    @Transient
//    private String proposalFile;

//    @EruptField(
//            edit = @Edit(title = "整改结果", type = EditType.CHOICE, notNull = false, readonly = @Readonly,
//                    choiceType = @ChoiceType(fetchHandler = EruptDictCodeChoiceFetchHandler.class, fetchHandlerParams = "result")))
//    @Transient
//    private String inspectionResult;

    @EruptField(
            edit = @Edit(title = "整改截止时间（年月日）", type = EditType.DATE, notNull = false, readonly = @Readonly,
                    dateType = @DateType))
    @Transient
    private java.util.Date deadline;

    @Transient
    @EruptField(
            edit = @Edit(title = "整改内容", type = EditType.DIVIDE)
    )
    private String divide;

    @Transient
    @EruptField(edit = @Edit(title = "整改说明", type = EditType.TEXTAREA, readonly = @Readonly))
    private @Lob String description;

    @EruptField(
            edit = @Edit(title = "整改证明材料", type = EditType.ATTACHMENT, notNull = false, readonly = @Readonly,
                    attachmentType = @AttachmentType(type = AttachmentType.Type.BASE, maxLimit = 20, fileTypes = {".pdf", ".doc", ".docx", ".xlsx", ".png", ".jpg", ".word", ".ppt", ".zip", ".rar"})))
    @Transient
    private String supportingMaterials;


    @EruptField(
            edit = @Edit(title = "整改时间", type = EditType.DATE, notNull = false, readonly = @Readonly,
                    dateType = @DateType))
    @Transient
    private java.sql.Date rectificationTime;

    @Transient
    @EruptField(
            edit = @Edit(title = "复查结果", type = EditType.DIVIDE)
    )
    private String divide1;

    @EruptField(
            edit = @Edit(title = "整改结果", type = EditType.BOOLEAN, notNull = true,
                    boolType = @BoolType(trueText = "通过", falseText = "不通过")))
    @Comment("整改结果")
    @ApiModelProperty("整改结果")
    private Boolean rectificationResults;

    @EruptField(
            edit = @Edit(title = "重置整改时间", type = EditType.DATE, notNull = false,showBy=@ShowBy(dependField="rectificationResults",expr="value==0"),
                    dateType = @DateType))
    @Transient
    private java.sql.Date rectificationTime1;

    @Transient
    @EruptField(edit = @Edit(title = "备注", type = EditType.TEXTAREA))
    private @Lob String remark;

}
