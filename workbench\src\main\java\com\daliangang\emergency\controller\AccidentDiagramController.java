package com.daliangang.emergency.controller;


import com.daliangang.core.DaliangangContext;
import com.daliangang.emergency.entity.AccidentInformation;
import com.daliangang.emergency.entity.EmergencyInformation;
import com.daliangang.emergency.entity.Sensitivetargets;
import com.daliangang.emergency.form.MapForm;
import com.daliangang.emergency.proxy.AccidentInformationDataProxy;
import com.daliangang.emergency.sql.AccidentInformationSql;
import com.google.gson.Gson;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.web.bind.annotation.*;
import xyz.erupt.core.annotation.EruptRouter;
import xyz.erupt.core.config.GsonFactory;
import xyz.erupt.core.view.EruptApiModel;
import xyz.erupt.jpa.dao.EruptDao;
import xyz.erupt.toolkit.utils.EruptDaoUtils;

import javax.annotation.Resource;
import javax.transaction.Transactional;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since :2023/4/27:9:41
 */
@RestController
public class AccidentDiagramController {
    @Resource
    private AccidentInformationSql accidentInformationSql;
    @Resource
    private EruptDao eruptDao;

    @Resource
    private AccidentInformationDataProxy accidentInformationDataProxy;



    // 添加事故信息
    @PostMapping("erupt-api/get/addAccidentInformationInfo")
    @Transactional

    public EruptApiModel addAccidentInformationInfo(@RequestBody AccidentInformation accidentInformation) {
        accidentInformation.setOrgCode(accidentInformation.getCompany());
        accidentInformation.setCreateTime(LocalDateTime.now());
           eruptDao.merge(accidentInformation);
        //  accidentInformationDataProxy.beforeAdd(accidentInformation);
        return EruptApiModel.successApi("保存成功！");
    }

    // 事故信息列表
    @RequestMapping("erupt-api/get/AccidentInformationInfo")
    @EruptRouter(verifyType = EruptRouter.VerifyType.LOGIN)
    public EruptApiModel selectAccidentInformationInfo() {
   //     MetaUserinfo remoteUserInfo = remoteProxyService.getRemoteUserInfo();
//        List<LinkedTreeMap> linkedTreeMaps = remoteProxyService.queryEntity("Enterprise", RemoteQuery.builder().condition("orgCode", remoteUserInfo.getOrg()));
//        String isEnterprise = "1";
//        if (ObjectUtils.isEmpty(linkedTreeMaps)) {  // 不存在则是政府账号
//            isEnterprise = "0";
//        }
//      String orgCode = SqlUtils.wrapIn(remoteUserInfo.getAuth());
        String sql = accidentInformationSql.selectAccidentInformation();
        List<AccidentInformation> accidentInformations = EruptDaoUtils.selectOnes(sql, AccidentInformation.class);
        if (ObjectUtils.isNotEmpty(accidentInformations)) {
            accidentInformations.forEach(v->{
                v.setCompanyName(DaliangangContext.getEnterpriseName(v.getCompany()));
            });
        }
        return EruptApiModel.successApi(accidentInformations);
    }

    // 查询事故详情
    @RequestMapping("erupt-api/get/selectAccidentInformationInfo/{id}")

    public EruptApiModel selectAccidentInformationInfo(@PathVariable("id") Long id) {
        AccidentInformation accidentInformation = EruptDaoUtils.selectOne("select * from tb_accident_information where id ="+id, AccidentInformation.class);

        return EruptApiModel.successApi(accidentInformation);
    }

    // 修改事故半径范围
    @RequestMapping("erupt-api/get/updateAccidentInformationInfo/{id}/{influenceScope}")
    @Transactional

    public EruptApiModel updateAccidentInformationInfo(@PathVariable("id") Long id, @PathVariable("influenceScope") Double influenceScope) {
        AccidentInformation accidentInformation = EruptDaoUtils.selectOne("select * from tb_accident_information where id ="+id, AccidentInformation.class);
        // 地图数据处理
//        Gson gson = GsonFactory.getGson();
//        JSONObject jsonObject = JSON.parseObject(accidentInformation.getMap());
//        MapForm mapForm;
//        if (ObjectUtils.isNotEmpty(jsonObject.get("point"))) {
//            mapForm = gson.fromJson( jsonObject.get("point").toString(), MapForm.class);
//            mapForm.setType("circle");
//        } else {
//            mapForm = gson.fromJson(accidentInformation.getMap(), MapForm.class);
//
//        }
//
//        mapForm.setRadius(influenceScope);
//        accidentInformation.setMap(gson.toJson(mapForm));
        accidentInformation.setInfluenceScope(influenceScope);
        eruptDao.merge(accidentInformation);
        return EruptApiModel.successApi("更新成功");
    }


    // 敏感目标范围查询
    @RequestMapping("erupt-api/get/selectSensitivetargetsInfo")
    @EruptRouter(verifyType = EruptRouter.VerifyType.LOGIN)

    public EruptApiModel selectSensitivetargetsInfo() {
        List<Sensitivetargets> sensitivetargets = EruptDaoUtils.selectOnes("select * from tb_sensitivetargets", Sensitivetargets.class);
        if (ObjectUtils.isEmpty(sensitivetargets)) {
            return EruptApiModel.successApi();
        }
  //      List<Sensitivetargets> list = new ArrayList();
//        Gson gson = GsonFactory.getGson();
//        // 处理圆形数据
//        JSONObject jsonObject = JSON.parseObject(accidentInformation.getMap());
//        MapForm mapForm;
//        if (ObjectUtils.isNotEmpty(jsonObject.get("point"))) {
//            mapForm = gson.fromJson( jsonObject.get("point").toString(), MapForm.class);
//            mapForm.setType("circle");
//            mapForm.setRadius(Double.valueOf(String.valueOf(jsonObject.get("radius"))));
//        } else {
//            mapForm = gson.fromJson(accidentInformation.getMap(), MapForm.class);
//
//        }
        // 处理是否在园内并计算距离
//        sensitivetargets.forEach(v->{
//            JSONObject jsonObject1 = JSON.parseObject(v.getMap());
//            MapForm mapForm1;
//            if (ObjectUtils.isNotEmpty(jsonObject1.get("point"))) {
//                mapForm1 = gson.fromJson( jsonObject1.get("point").toString(), MapForm.class);
//                mapForm1.setType("circle");
//                mapForm1.setRadius(Double.valueOf(String.valueOf(jsonObject.get("radius"))));
//            } else {
//                mapForm1 = gson.fromJson(v.getMap(), MapForm.class);
//
//            }
//
//            boolean inCircle = GisPointUtil.isInCircle(mapForm.getLng(), mapForm.getLat(), mapForm1.getLng(), mapForm1.getLat(), mapForm.getRadius());
//            if (inCircle) {
//                double distance = GisPointUtil.getDistance(mapForm.getLat(), mapForm.getLng(), mapForm1.getLat(), mapForm1.getLng());
//                v.setDistance(distance);
//                list.add(v);
//            }
//        });

//        List<Sensitivetargets> newList = list.stream().sorted(Comparator.comparing(Sensitivetargets::getDistance))
//                .collect(Collectors.toList());
        return EruptApiModel.successApi(sensitivetargets);
    }


    // 应急管理部门
    @RequestMapping("erupt-api/get/selectEmergencyInformationInfo")
    @EruptRouter(verifyType = EruptRouter.VerifyType.LOGIN)
    public EruptApiModel selectEmergencyInformationInfo() {
        List<EmergencyInformation> emergencyInformations = EruptDaoUtils.selectOnes("select * from tb_emergency_information", EmergencyInformation.class);

        return EruptApiModel.successApi(emergencyInformations);
    }

}
    