package com.daliangang.emergency.entity;

import com.daliangang.emergency.operation.EmergencyFlowChartHandler;
import com.daliangang.emergency.proxy.EmergencyInformationDataProxy;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import xyz.erupt.annotation.Erupt;
import xyz.erupt.annotation.EruptField;
import xyz.erupt.annotation.sub_erupt.Power;
import xyz.erupt.annotation.sub_erupt.RowOperation;
import xyz.erupt.annotation.sub_field.Edit;
import xyz.erupt.annotation.sub_field.EditType;
import xyz.erupt.annotation.sub_field.View;
import xyz.erupt.annotation.sub_field.sub_edit.InputType;
import xyz.erupt.toolkit.db.Comment;
import xyz.erupt.upms.model.auth.DataAuthModel;

import javax.persistence.Entity;
import javax.persistence.Table;

/**
 * <AUTHOR>
 * @since :2023/4/26:10:56
 */
@Erupt(name = "应急信息", power = @Power(add = true, delete = true, export = false, importable = true, viewDetails = true, edit = true)
        , dataProxy = EmergencyInformationDataProxy.class
        , rowOperation = {
//        @RowOperation(title = "上传应急流程图", icon = "fa fa-upload", operationHandler = EmergencyFlowChartHandler.class,
//                eruptClass = EmergencyFlowChart.class, mode = RowOperation.Mode.BUTTON)

})
@Table(name = "tb_emergency_information")
@Entity
@Getter
@Setter
@Comment("应急信息")
@ApiModel("应急信息")
public class EmergencyInformation extends DataAuthModel {

    @EruptField(
            views = @View(title = "单位"),
            edit = @Edit(title = "单位", type = EditType.INPUT, notNull = true,
                    inputType = @InputType))
    @Comment("单位")
    @ApiModelProperty("单位")
    private String unit;

    @EruptField(
            views = @View(title = "电话"),
            edit = @Edit(title = "电话", type = EditType.INPUT, notNull = true,
                    inputType = @InputType))
    @Comment("电话")
    @ApiModelProperty("电话")
    private String phone;


}
