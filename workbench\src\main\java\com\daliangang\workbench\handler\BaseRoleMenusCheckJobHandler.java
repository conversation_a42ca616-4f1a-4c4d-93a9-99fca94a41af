package com.daliangang.workbench.handler;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import xyz.erupt.designer.entity.handler.MenuDefaultRoleHandler;
import xyz.erupt.job.handler.EruptJobHandler;
import xyz.erupt.jpa.dao.EruptDao;
import xyz.erupt.upms.model.EruptRole;

import javax.annotation.Resource;
import javax.transaction.Transactional;

//基准角色的菜单状态检查定时器
@Service
@Slf4j
public class BaseRoleMenusCheckJobHandler implements EruptJobHandler {


    @Resource
    private EruptDao eruptDao;

    @Resource
    private MenuDefaultRoleHandler menuDefaultRoleHandler;

    @Transactional
    @Override
    public String exec(String code, String param) {
        int startRoleId = 101, endRoleId = 109;
        for (int i = startRoleId; i <= endRoleId; i++) {
            int roleId = i;
            EruptRole role = eruptDao.queryEntity(EruptRole.class, "code='" + roleId + "'");
            if (role.getMenus().isEmpty()) {
                log.error("发现基准角色[" + role.getName() + "]的菜单丢失，正在进行补正..");
                menuDefaultRoleHandler.refreshRoleMenus(role);
            }
        }
        log.error("基准角色菜单状态检查结束...");
        return null;
    }
}
