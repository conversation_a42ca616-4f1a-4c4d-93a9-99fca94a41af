/*
* Copyright © 2020-2035 yundingyun All rights reserved.
*/
package com.daliangang.safedaily.entity;

import javax.persistence.*;
import xyz.erupt.annotation.*;
import io.swagger.annotations.*;
import xyz.erupt.annotation.sub_erupt.*;
import xyz.erupt.annotation.sub_field.*;
import xyz.erupt.annotation.sub_field.sub_edit.*;
import xyz.erupt.upms.model.auth.*;
import xyz.erupt.toolkit.handler.*;
import java.util.*;
import java.sql.*;
import xyz.erupt.toolkit.db.Comment;
import xyz.erupt.toolkit.submit.*;
import com.daliangang.safedaily.proxy.*;
import lombok.*;

@Erupt(name = "评价表信息", power = @Power(add = true, delete = true, export = false, importable = true, viewDetails = true, edit = true)
, dataProxy = EvaluationInformationDataProxy.class
, rowOperation = {})
@Table(name = "tb_evaluation_information")
@Entity
@Getter
@Setter
@Comment("评价表信息")
@ApiModel("评价表信息")
public class EvaluationInformation extends DataAuthModel {
	@EruptField(
		views = @View(title = "评价类目", show = false),
		edit = @Edit(title = "评价类目", type = EditType.INPUT, notNull = true, 
		inputType = @InputType))
	@Comment("评价类目")
	@ApiModelProperty("评价类目")
	private String category;

	@EruptField(
		views = @View(title = "评价项目", show = false),
		edit = @Edit(title = "评价项目", type = EditType.INPUT, notNull = true, 
		inputType = @InputType))
	@Comment("评价项目")
	@ApiModelProperty("评价项目")
	private String project;

	@EruptField(
		views = @View(title = "释义", show = false),
		edit = @Edit(title = "释义", type = EditType.INPUT, notNull = true, 
		inputType = @InputType))
	@Comment("释义")
	@ApiModelProperty("释义")
	private String interpretation;

	@EruptField(
		views = @View(title = "评价方法", show = false),
		edit = @Edit(title = "评价方法", type = EditType.INPUT, notNull = true, 
		inputType = @InputType))
	@Comment("评价方法")
	@ApiModelProperty("评价方法")
	private String method;

	@EruptField(
		views = @View(title = "标准分值", show = false),
		edit = @Edit(title = "标准分值", type = EditType.INPUT, notNull = true, 
		inputType = @InputType))
	@Comment("标准分值")
	@ApiModelProperty("标准分值")
	private String score;

	@EruptField(
		views = @View(title = "评价标准", show = false),
		edit = @Edit(title = "评价标准", type = EditType.INPUT, notNull = true, 
		inputType = @InputType))
	@Comment("评价标准")
	@ApiModelProperty("评价标准")
	private String standard;

	@EruptField(
		views = @View(title = "是否涉及", show = false),
		edit = @Edit(title = "是否涉及", type = EditType.BOOLEAN, notNull = true, 
		boolType = @BoolType(trueText = "是", falseText = "否")))
	@Comment("是否涉及")
	@ApiModelProperty("是否涉及")
	private Boolean isInvolve;

	@EruptField(
		views = @View(title = "得分", show = false),
		edit = @Edit(title = "得分", type = EditType.INPUT, notNull = true, 
		inputType = @InputType))
	@Comment("得分")
	@ApiModelProperty("得分")
	private String myScore;

}