/*
* Copyright © 2020-2035 yundingyun All rights reserved.
*/
package com.daliangang.majorisk.proxy;

import com.daliangang.core.DaliangangContext;
import com.daliangang.majorisk.entity.ReserveReporting;
import org.springframework.stereotype.Service;
import xyz.erupt.annotation.fun.DataProxy;
import xyz.erupt.upms.model.EruptUser;
import xyz.erupt.upms.service.EruptUserService;

import javax.annotation.Resource;
import java.util.Date;
import java.util.Map;

@Service
public class ReserveReportingDataProxy implements DataProxy<ReserveReporting> {

    @Resource
    private EruptUserService eruptUserService;
    //    @Override
//    public String beforeFetch(List<Condition> conditions) {
//        if (DaliangangContext.isDepartmentUser()) return "submitted=true";
//        return null;
//    }
//


    @Override
    public void beforeAdd(ReserveReporting reserveReporting) {
        reserveReporting.setModifyTime(new Date());
    }

    @Override
    public void beforeUpdate(ReserveReporting reserveReporting) {
        reserveReporting.setModifyTime(new Date());
    }


//    @Override
//    public void searchCondition(Map<String, Object> condition) {
//        if(!DaliangangContext.isDepartmentUser()){
//            EruptUser user = eruptUserService.getCurrentEruptUser();
//            if (user != null && user.getEruptOrg() != null)
//                condition.put("company", user.getEruptOrg().getCode());
//        }
//
//    }
}
