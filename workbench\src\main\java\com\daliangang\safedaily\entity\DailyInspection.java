/*
 * Copyright © 2020-2035 yundingyun All rights reserved.
 */
package com.daliangang.safedaily.entity;

import com.daliangang.core.CompanyRenderHandler;
import com.daliangang.safedaily.operation.DailyInspectionSubmitHandler;
import com.daliangang.safedaily.proxy.DailyInspectionDataProxy;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import xyz.erupt.annotation.Erupt;
import xyz.erupt.annotation.EruptField;
import xyz.erupt.annotation.expr.ExprBool;
import xyz.erupt.annotation.sub_erupt.Power;
import xyz.erupt.annotation.sub_erupt.RowOperation;
import xyz.erupt.annotation.sub_field.Edit;
import xyz.erupt.annotation.sub_field.EditType;
import xyz.erupt.annotation.sub_field.Readonly;
import xyz.erupt.annotation.sub_field.View;
import xyz.erupt.annotation.sub_field.sub_edit.*;
import xyz.erupt.toolkit.attachment.AttachmentDownloadHandler;
import xyz.erupt.toolkit.db.Comment;
import xyz.erupt.toolkit.handler.DelOperationHandler;
import xyz.erupt.toolkit.handler.EditOperationHandler;
import xyz.erupt.toolkit.handler.RemoteEntityChoiceFetchHandler;
import xyz.erupt.toolkit.submit.CheckSubmit;
import xyz.erupt.toolkit.utils.TplUtils;
import xyz.erupt.upms.model.auth.DataAuthHandler;
import xyz.erupt.upms.model.auth.DataAuthModel;

import javax.persistence.Entity;
import javax.persistence.Table;

@Erupt(name = "日检查", power = @Power(delete = false, edit = false)
        , dataProxy = DailyInspectionDataProxy.class
        ,orderBy = "DailyInspection.inspectionTime desc",
        rowOperation = {
        @RowOperation(title = "编辑", icon = "fa fa-edit", code = TplUtils.EDIT_OPER_CODE,
                eruptClass = DailyInspection.class, operationHandler = EditOperationHandler.class,
                ifExpr = "item.submitted=='未上报'", ifExprBehavior = RowOperation.IfExprBehavior.HIDE,
                mode = RowOperation.Mode.SINGLE),
        @RowOperation(title = "删除", icon = "fa fa-trash-o",
                operationHandler = DelOperationHandler.class, ifExpr = "item.submitted=='未上报'", ifExprBehavior = RowOperation.IfExprBehavior.HIDE, mode = RowOperation.Mode.SINGLE),
        @RowOperation(title = "上报", icon = "fa fa-arrow-circle-o-up", operationHandler = DailyInspectionSubmitHandler.class, ifExpr = "item.submitted=='未上报'", ifExprBehavior = RowOperation.IfExprBehavior.HIDE, mode = RowOperation.Mode.SINGLE),
        @RowOperation(title = "下载附件", icon = "fa fa-download", operationHandler = AttachmentDownloadHandler.class, mode = RowOperation.Mode.SINGLE)
})
@Table(name = "tb_daily_inspection")
@Entity
@Getter
@Setter
@Comment("日检查")
@ApiModel("日检查")
public class DailyInspection extends DataAuthModel  {
    @EruptField(
            views = @View(title = "企业名称"),
            edit = @Edit(title = "企业名称", search = @Search(ifRender = @ExprBool(exprHandler = CompanyRenderHandler.class) ), readonly = @Readonly(exprHandler = DataAuthHandler.class), type = EditType.CHOICE, notNull = true,
                    choiceType = @ChoiceType(fullSpan = true,fetchHandler = RemoteEntityChoiceFetchHandler.class, fetchHandlerParams = {"main", "Enterprise", "org_code,name"})))
    @Comment("企业名称")
    @ApiModelProperty("企业名称")
    private String company;

    @EruptField(
            views = @View(title = "检查人员"),
            edit = @Edit(title = "检查人员", type = EditType.INPUT, notNull = true,search = @Search(vague = true),
                    inputType = @InputType))
    @Comment("检查人员")
    @ApiModelProperty("检查人员")
    private String inspector;

    @EruptField(
            views = @View(title = "岗位"),
            edit = @Edit(title = "岗位", type = EditType.INPUT, notNull = true,
                    inputType = @InputType))
    @Comment("岗位")
    @ApiModelProperty("岗位")
    private String jobTitle;

    @EruptField(
            views = @View(title = "上报状态", show = true),
            edit = @Edit(title = "上报状态", type = EditType.BOOLEAN, show = false, /*notNull = true, */
                    boolType = @BoolType(trueText = "已上报", falseText = "未上报")))
    @CheckSubmit(linkProperty = "inspectionTime", checkTimeDiffPeriod = 1)
    @Comment("上报状态")
    @ApiModelProperty("上报状态")
    private Boolean submitted;

    @EruptField(
            views = @View(title = "检查时间"),
            edit = @Edit(title = "检查时间", type = EditType.DATE, notNull = true,
                    search = @Search,
                    dateType = @DateType))
    @Comment("检查时间")
    @ApiModelProperty("检查时间")
    private java.util.Date inspectionTime;

    @EruptField(
            views = @View(title = "检查结果"),
            edit = @Edit(title = "检查结果",
                    type = EditType.INPUT,
                    notNull = true,
                    search = @Search(vague = true),
                    inputType = @InputType))
    @Comment("检查结果")
    @ApiModelProperty("检查结果")
    private String inspectionResult;

    @EruptField(
            views = @View(title = "上传附件", show = false),
            edit = @Edit(title = "上传附件", type = EditType.ATTACHMENT, notNull = true,
                    attachmentType = @AttachmentType(type = AttachmentType.Type.BASE, maxLimit = 20, fileTypes = {".pdf", ".doc", ".docx", ".xlsx", ".png", ".jpg", ".xls", ".ppt", ".pptx", ".zip", ".rar"})))
    @Comment("上传附件")
    @ApiModelProperty("上传附件")
    private String file;

}
