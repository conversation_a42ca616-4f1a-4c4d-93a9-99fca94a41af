package com.daliangang.training.entity;

import lombok.*;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Date 2023/5/19
 * @Description:
 */

@Getter
@Setter
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class PostTemplateSyncEntity  implements Serializable {

    //岗位id
    private Integer id;
    //岗位code
    private String code;
    //岗位名称
    private String name;
    //岗位权重
    private Integer weight;
    //排它
    private boolean exclusive;
    //删除标识
    private Boolean delFlag;

    //更新/新增标识
    private Boolean updateFlag;
}
