#!/bin/bash

# 大连港工作台 Docker 启动脚本

set -e

echo "🚀 启动大连港工作台 Docker 环境..."

# 检查必要文件
if [ ! -f ".env" ]; then
    echo "❌ 错误：.env 文件不存在！"
    echo "请复制 .env.example 为 .env 并配置相应的环境变量"
    exit 1
fi

if [ ! -f "docker-compose.yml" ]; then
    echo "❌ 错误：docker-compose.yml 文件不存在！"
    exit 1
fi

# 创建必要的目录
echo "📁 创建必要的目录..."
mkdir -p docker/mysql/conf.d
mkdir -p docker/mysql/init
mkdir -p docker/redis
mkdir -p logs
mkdir -p uploads

# 检查Docker是否运行
if ! docker info > /dev/null 2>&1; then
    echo "❌ 错误：Docker 未运行或无法访问！"
    echo "请确保 Docker 已安装并正在运行"
    exit 1
fi

# 检查Docker Compose是否可用
if ! command -v docker-compose > /dev/null 2>&1; then
    echo "❌ 错误：docker-compose 命令不可用！"
    echo "请安装 Docker Compose"
    exit 1
fi

# 停止现有容器（如果存在）
echo "🛑 停止现有容器..."
docker-compose down --remove-orphans

# 构建并启动服务
echo "🔨 构建并启动服务..."
docker-compose up --build -d

# 等待服务启动
echo "⏳ 等待服务启动..."
sleep 10

# 检查服务状态
echo "🔍 检查服务状态..."
docker-compose ps

# 显示服务信息
echo ""
echo "✅ 服务启动完成！"
echo ""
echo "📋 服务访问信息："
echo "  🌐 大连港工作台: http://localhost:$(grep APP_PORT .env | cut -d'=' -f2)"
echo "  🗄️  MySQL: localhost:$(grep MYSQL_PORT .env | cut -d'=' -f2)"
echo "  🔴 Redis: localhost:$(grep REDIS_PORT .env | cut -d'=' -f2)"
echo "  📦 MinIO: http://localhost:$(grep MINIO_PORT .env | cut -d'=' -f2)"
echo "  🎛️  MinIO Console: http://localhost:$(grep MINIO_CONSOLE_PORT .env | cut -d'=' -f2)"
echo ""
echo "📝 查看日志："
echo "  docker-compose logs -f daliangang-app"
echo ""
echo "🛑 停止服务："
echo "  docker-compose down"
echo ""
echo "🔄 重启服务："
echo "  docker-compose restart"
echo ""

# 检查应用健康状态
echo "🏥 检查应用健康状态..."
APP_PORT_VALUE=$(grep APP_PORT .env | cut -d'=' -f2)
for i in {1..30}; do
    if curl -f http://localhost:${APP_PORT_VALUE}/actuator/health > /dev/null 2>&1; then
        echo "✅ 应用健康检查通过！"
        echo "🌐 应用访问地址: http://localhost:${APP_PORT_VALUE}"
        break
    else
        echo "⏳ 等待应用启动... ($i/30)"
        sleep 5
    fi

    if [ $i -eq 30 ]; then
        echo "⚠️  应用可能启动失败，请检查日志："
        echo "  docker-compose logs daliangang-app"
        echo "🔍 手动检查健康状态："
        echo "  curl http://localhost:${APP_PORT_VALUE}/actuator/health"
    fi
done

echo ""
echo "🎉 大连港工作台 Docker 环境启动完成！"
