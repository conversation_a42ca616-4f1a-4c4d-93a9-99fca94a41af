package com.daliangang.workbench.init;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import xyz.erupt.excel.model.ExcelHeader;

/**
 * @Author：chb
 * @Package：com.daliangang.workbench.init
 * @Project：erupt
 * @name：InitParams
 * @Date：2023/3/5 22:03
 * @Filename：InitParams
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class InitParams {

    @ExcelHeader(col = 0, name = "参数1")
    private String param1;

    @ExcelHeader(col = 1, name = "参数2")
    private String param2;

    @ExcelHeader(col = 2, name = "参数3")
    private String param3;

    @ExcelHeader(col = 3, name = "参数4")
    private String param4;

    public static InitParams valueOf(InitTemplate template) {
        return InitParams.builder().param1(template.getParam1())
                .param2(template.getParam2())
                .param3(template.getParam3())
                .param4(template.getParam4()).build();
    }
}
