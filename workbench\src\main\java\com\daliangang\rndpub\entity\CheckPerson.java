/*
 * Copyright © 2020-2035 yundingyun All rights reserved.
 */
package com.daliangang.rndpub.entity;

import com.daliangang.core.CompanyRenderHandler;
import com.daliangang.rndpub.operation.CheckpersonDisableHandler;
import com.daliangang.rndpub.operation.CheckpersonEnableHandler;
import com.daliangang.rndpub.proxy.CheckpersonDataProxy;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import xyz.erupt.annotation.Erupt;
import xyz.erupt.annotation.EruptField;
import xyz.erupt.annotation.expr.ExprBool;
import xyz.erupt.annotation.sub_erupt.Power;
import xyz.erupt.annotation.sub_erupt.RowOperation;
import xyz.erupt.annotation.sub_field.Edit;
import xyz.erupt.annotation.sub_field.EditType;
import xyz.erupt.annotation.sub_field.Readonly;
import xyz.erupt.annotation.sub_field.View;
import xyz.erupt.annotation.sub_field.sub_edit.BoolType;
import xyz.erupt.annotation.sub_field.sub_edit.InputType;
import xyz.erupt.annotation.sub_field.sub_edit.Search;
import xyz.erupt.annotation.sub_field.sub_edit.TagsType;
import xyz.erupt.toolkit.db.Comment;
import xyz.erupt.toolkit.handler.RemoteEntityTagFetchHandler;
import xyz.erupt.upms.model.auth.DataAuthModel;

import javax.persistence.Entity;
import javax.persistence.Table;

@Erupt(name = "检查人员管理", power = @Power(add = false, delete = false, export = false, importable = true, viewDetails = true, edit = false)
        , dataProxy = CheckpersonDataProxy.class
        , rowOperation = {
        //@RowOperation(title = "添加检查人员", icon = "fa fa-male", eruptClass = CheckpersonAddHandler.CheckPersionAddForm.class, operationHandler = CheckpersonAddHandler.class, mode = RowOperation.Mode.BUTTON),
        @RowOperation(confirm = false, title = "启用", icon = "fa fa-toggle-off", ifExpr = "item.state=='禁用'", ifExprBehavior = RowOperation.IfExprBehavior.HIDE, operationHandler = CheckpersonEnableHandler.class, mode = RowOperation.Mode.SINGLE),
        @RowOperation(confirm = false, title = "禁用", icon = "fa fa-toggle-on", ifExpr = "item.state=='启用'", ifExprBehavior = RowOperation.IfExprBehavior.HIDE, operationHandler = CheckpersonDisableHandler.class, mode = RowOperation.Mode.SINGLE)
})
@Table(name = "tb_checkperson")
@Entity
@Getter
@Setter
@Comment("检查人员管理")
@ApiModel("检查人员管理")
public class CheckPerson extends DataAuthModel {

    @EruptField(
            views = @View(title = "企业名称"),
            edit = @Edit(title = "企业名称", search = @Search(ifRender = @ExprBool(exprHandler = CompanyRenderHandler.class)),
                    readonly = @Readonly, type = EditType.INPUT))
    @Comment("企业名称")
    @ApiModelProperty("企业名称")
    private String belongCompany;

    @EruptField(
            views = @View(title = "姓名"),
            edit = @Edit(title = "姓名", readonly = @Readonly,search = @Search))
    @Comment("姓名")
    @ApiModelProperty("姓名")
    private String name;

    @EruptField(
            views = @View(title = "状态", show = true),
            edit = @Edit(title = "状态", type = EditType.BOOLEAN, notNull = true, show = false,search = @Search,
                    boolType = @BoolType(trueText = "启用", falseText = "禁用")))
    @Comment("状态")
    @ApiModelProperty("状态")
    private Boolean state;

    @EruptField(
            views = @View(title = "性别"),
            edit = @Edit(title = "性别", readonly = @Readonly, type = EditType.BOOLEAN,
                    boolType = @BoolType(trueText = "男", falseText = "女")))
    @Comment("性别")
    @ApiModelProperty("性别")
    private Boolean sex;

    @EruptField(
            views = @View(title = "手机号"),
            edit = @Edit(title = "手机号", readonly = @Readonly, type = EditType.INPUT,
                    inputType = @InputType))
    @Comment("手机号")
    @ApiModelProperty("手机号")
    private String phone;



    @EruptField(
            views = @View(title = "检查范围"),
            edit = @Edit(title = "检查范围", readonly = @Readonly,type = EditType.INPUT))
    @Comment("检查范围")
    @ApiModelProperty("检查范围")
    private String checkScope;

    @EruptField(
            views = @View(title = "执法证号"),
            edit = @Edit(title = "执法证号", type = EditType.INPUT,
                    inputType = @InputType))
    @Comment("执法证号")
    @ApiModelProperty("执法证号")
    private String certificateNo;

    @EruptField(
            views = @View(title = "规避企业"),
            edit = @Edit(title = "规避企业", type = EditType.TAGS,
                    tagsType = @TagsType(allowExtension = false, fetchHandler = RemoteEntityTagFetchHandler.class, fetchHandlerParams = {"main", "Enterprise", "administrator"})))
    @Comment("规避企业")
    @ApiModelProperty("规避企业")
    private String EvadeEnterprise;

    @EruptField(
            views = @View(title = "是否检查人员"),
            edit = @Edit(
                    title = "是否检查人员",
                    type = EditType.BOOLEAN,
                    boolType = @BoolType))
    @Comment("是否检查人员")
    @ApiModelProperty("是否检查人员")
    private Boolean checkPerson=false;
}
