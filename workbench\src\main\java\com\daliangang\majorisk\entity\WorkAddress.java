package com.daliangang.majorisk.entity;

import com.daliangang.majorisk.enums.WorkAddressType;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import xyz.erupt.annotation.Erupt;
import xyz.erupt.annotation.EruptField;
import xyz.erupt.annotation.fun.DataProxy;
import xyz.erupt.annotation.fun.FilterHandler;
import xyz.erupt.annotation.sub_field.Edit;
import xyz.erupt.annotation.sub_field.EditType;
import xyz.erupt.annotation.sub_field.View;
import xyz.erupt.annotation.sub_field.sub_edit.InputType;
import xyz.erupt.core.context.MetaContext;
import xyz.erupt.core.util.EruptSpringUtil;
import xyz.erupt.jpa.model.BaseModel;
import xyz.erupt.toolkit.db.Comment;
import xyz.erupt.upms.model.EruptUser;
import xyz.erupt.upms.service.EruptUserService;

import javax.persistence.Entity;
import javax.persistence.Table;

@Erupt(name = "作业地点", dataProxy = WorkAddress.class)
@Table(name = "tb_work_address")
@Entity
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Comment("作业地点")
@ApiModel("作业地点")
@Component
@Slf4j
public class WorkAddress extends BaseModel implements DataProxy<WorkAddress>, FilterHandler {

    @EruptField
    private String orgCode;

    @EruptField(
            views = @View(title = "类型"),
            edit = @Edit(title = "类型", type = EditType.INPUT, inputType = @InputType))
    @Comment("类型")
    @ApiModelProperty("类型")
    private WorkAddressType type;

    @EruptField(
            views = @View(title = "ID"),
            edit = @Edit(title = "ID", type = EditType.INPUT, inputType = @InputType))
    @Comment("ID")
    @ApiModelProperty("ID")
    private Long addrId;


    @EruptField(
            views = @View(title = "名称"),
            edit = @Edit(title = "名称", type = EditType.INPUT, notNull = true,
                    inputType = @InputType))
    @Comment("名称")
    @ApiModelProperty("名称")
    private String name;

    @Override
    public String filter(String condition, String[] params) {
        String orgCode = null;
        EruptUserService eruptUserService = EruptSpringUtil.getBean(EruptUserService.class);
        if (MetaContext.getUser() != null) {
            EruptUser eruptUser = eruptUserService.getEruptUser(Long.parseLong(MetaContext.getUser().getUid()));
            orgCode = eruptUser != null && eruptUser.getEruptOrg() != null ? eruptUser.getEruptOrg().getCode() : null;
        }
        WorkAddressType type = WorkAddressType.valueOf(params[0]);
        return "WorkAddress.type=" + type.ordinal() + (StringUtils.isNotEmpty(orgCode) ? " and org_code='" + orgCode + "'" : "");
    }
}
