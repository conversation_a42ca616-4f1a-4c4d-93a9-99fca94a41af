/*
 * Copyright © 2020-2035 yundingyun All rights reserved.
 */
package com.daliangang.rndpub.proxy;

import com.daliangang.rndpub.entity.InspectionItemsManagement;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import xyz.erupt.annotation.fun.DataProxy;

@Service
@Slf4j
public class InspectionItemsManagementDataProxy implements DataProxy<InspectionItemsManagement> {
    @Override
    public void beforeAdd(InspectionItemsManagement item) {
        item.setInspectionItemsSearch(item.getInspectionItems());
        item.setCheckFirstSearch(item.getCheckFirst());
    }

    @Override
    public void excelImport(Object workbook_) {
//        EruptDaoUtils.truncate(InspectionItemsManagement.class);
    }
}
