package com.daliangang.markdown;

import lombok.Data;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.stereotype.Service;
import xyz.erupt.core.service.EruptCoreService;
import xyz.erupt.core.view.EruptModel;

import java.io.File;
import java.io.FileWriter;
import java.io.PrintWriter;
import java.util.StringTokenizer;

@Service
@Data
@Slf4j
public class EruptEntityMarkdownWriter implements ApplicationRunner {

    @Value("${markdown.erupt:}")
    private String exportEruptValues;

    @SneakyThrows
    private void generateMarkdown(EruptModel eruptModel) {
        File targetFile = new File("D:\\workspace\\daliangang-workbench\\workbench\\src\\main\\resources\\markdown\\"+ eruptModel.getErupt().name() +".md");

        if (targetFile.exists()) targetFile.delete();
        targetFile.createNewFile();
        PrintWriter out = new PrintWriter(new FileWriter(targetFile));
        out.println("# " + eruptModel.getEruptName());
        out.println();
        out.println("本文档用于说明 " + eruptModel.getErupt().name() + " 的创建与更新。");
        out.println();
        out.println("# 1 创建 " + eruptModel.getErupt().name() + " 对象");
        out.println();
        out.println("请求方式：POST<br>");
        out.println("请求地址：/erupt-api/open-api/data/" + eruptModel.getClazz().getSimpleName());
        out.println();
        out.println("## 1.1 Body参数");
        out.println("参数说明：以JSON格式在Body体内提交");
        out.println();
        out.println("|  参数  |  类型  |  是否必填  |  说明  |  备注  |");
        out.println("| --- | --- | --- | --- | --- |");
        eruptModel.getEruptFieldModels().forEach(eruptFieldModel -> {
            out.print("|  ");
            out.print(eruptFieldModel.getFieldName());
            out.print("  |  ");
            out.print(eruptFieldModel.getField().getType().getSimpleName());
            out.print("  |  ");
            out.print(eruptFieldModel.getEruptField().edit().notNull() ? "是" : "否");
            out.print("  |  ");
            out.print(eruptFieldModel.getEruptField().edit().title());
            out.println("  |       |");
        });

        out.println("## 1.2 返回值");
        out.println("返回值说明：以JSON格式返回");
        out.println();
        out.println("|  参数  |  类型  |  说明  |  备注  |");
        out.println("| --- | --- | --- | --- |");
        out.println("| status  | 状态   |  SUCCESS/ERROR |  |");
        out.println("| promptWay  | 类型   |  MESSAGE，表示返回类型为消息 |   |");
        out.println("| message  | 消息内容   |  报错信息（如果发生错误） |   |");
        out.println("| data  | 携带数据   |  请求返回的数据（如果有数据）|   |");
        out.println("| errorIntercept  | 保留字段   |    |   |");

        out.flush();
        out.close();
    }

    @Override
    @SneakyThrows
    public void run(ApplicationArguments args) throws Exception {
        log.info("正在检查Erupt文档生成配置：{}", exportEruptValues);
        StringTokenizer st = new StringTokenizer(exportEruptValues, ",");
        while (st.hasMoreTokens()) {
            String eruptName = st.nextToken().trim();
            EruptModel eruptModel = EruptCoreService.getErupt(eruptName);
            if (eruptModel == null) {
                log.error("找不到这个Erupt对象 {}，无法生成文档.", eruptName);
            } else {
                this.generateMarkdown(eruptModel);
            }
        }
    }
}
