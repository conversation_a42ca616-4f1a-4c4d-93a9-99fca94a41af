/*
 * Copyright © 2020-2035 yundingyun All rights reserved.
 */
package com.daliangang.safedaily.entity;

import com.daliangang.core.CompanyRenderHandler;
import com.daliangang.safedaily.operation.ReleaseEscalationHandler;
import com.daliangang.safedaily.proxy.ReleaseDataProxy;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import xyz.erupt.annotation.Erupt;
import xyz.erupt.annotation.EruptField;
import xyz.erupt.annotation.expr.ExprBool;
import xyz.erupt.annotation.sub_erupt.Power;
import xyz.erupt.annotation.sub_erupt.RowOperation;
import xyz.erupt.annotation.sub_field.Edit;
import xyz.erupt.annotation.sub_field.EditType;
import xyz.erupt.annotation.sub_field.Readonly;
import xyz.erupt.annotation.sub_field.View;
import xyz.erupt.annotation.sub_field.sub_edit.*;
import xyz.erupt.toolkit.db.Comment;
import xyz.erupt.toolkit.handler.DelOperationHandler;
import xyz.erupt.toolkit.handler.EditOperationHandler;
import xyz.erupt.toolkit.handler.EruptDictCodeChoiceFetchHandler;
import xyz.erupt.toolkit.handler.RemoteEntityChoiceFetchHandler;
import xyz.erupt.toolkit.submit.CheckSubmit;
import xyz.erupt.toolkit.utils.TplUtils;
import xyz.erupt.upms.model.auth.DataAuthHandler;
import xyz.erupt.upms.model.auth.DataAuthModel;

import javax.persistence.Entity;
import javax.persistence.Lob;
import javax.persistence.Table;
import java.time.LocalDateTime;

@Erupt(name = "安全承诺公告",
        orderBy = "Release.fillingDate desc",
        power = @Power(add = true, delete = false, export = false, importable = true, viewDetails = true, edit = false)
        , dataProxy = ReleaseDataProxy.class,
        rowOperation = {
        @RowOperation(title = "上报", icon = "fa fa-arrow-circle-o-up", operationHandler = ReleaseEscalationHandler.class, ifExpr = "item.submitted=='未上报'", mode = RowOperation.Mode.SINGLE),
        @RowOperation(title = "编辑", icon = "fa fa-edit", code = TplUtils.EDIT_OPER_CODE,
                eruptClass = Release.class, operationHandler = EditOperationHandler.class,
                ifExpr = "item.submitted=='未上报'", ifExprBehavior = RowOperation.IfExprBehavior.HIDE,
                mode = RowOperation.Mode.SINGLE),
        @RowOperation(title = "删除", icon = "fa fa-trash-o",
                operationHandler = DelOperationHandler.class, ifExpr = "item.submitted=='未上报'", ifExprBehavior = RowOperation.IfExprBehavior.HIDE, mode = RowOperation.Mode.SINGLE)
})
@Table(name = "tb_release")
@Entity
@Getter
@Setter
@Comment("安全承诺公告")
@ApiModel("安全承诺公告")
public class Release extends DataAuthModel {

    public static final String DEFAULT_CONTENT =
            "   公司今日在岗职工 X 人，有高处作业 X 项，动火作业 X 项，临时用电作业 X 项，有限空间作业 X" +
            "项，吊装作业 X 项，动土作业 X 项，断路作业 X 项，盲板作业 X 项，重点管控的生产作业 X 项，重点管" +
            "控的检维修作业 X 项。\n" +
            "   存在红色风险 X 项，橙色风险 X 项，黄色风险 X 项，蓝色风险 X 项，风险主要分布于 XXX。经公司" +
            "全面安全风险研判，设备设施及工艺处于安全运行状态，在岗职工全部安全培训教育合格，特种作业人员" +
            "均持证上岗，职业病危害因素控制符合国家标准，各项风险防范措施可靠。\n" +
            "     企业风险等级为 X 色。\n" ;



    @EruptField(
            views = @View(title = "企业名称",width = "260px"),
            edit = @Edit(title = "企业名称",search = @Search(ifRender = @ExprBool(exprHandler = CompanyRenderHandler.class)), readonly = @Readonly(exprHandler = DataAuthHandler.class), type = EditType.CHOICE, notNull = true,
                    choiceType = @ChoiceType(fullSpan = true,fetchHandler = RemoteEntityChoiceFetchHandler.class, fetchHandlerParams = {"main", "Enterprise", "org_code,name"}, reload = true)))
    @Comment("企业名称")
    @ApiModelProperty("企业名称")
    private String company;

    @EruptField(
            views = @View(title = "填报日期",width = "100px"),
            edit = @Edit(title = "填报日期", type = EditType.DATE, search = @Search, notNull = true,
                    readonly = @Readonly,
                    dateType = @DateType))
    @Comment("填报日期")
    @ApiModelProperty("填报日期")
    private java.util.Date fillingDate;

    @EruptField(
            views = @View(title = "创建时间",width = "100px"),
            edit = @Edit(title = "创建时间", search = @Search, notNull = true,
                    type = EditType.DATE,dateType = @DateType(type= DateType.Type.DATE_TIME),
                    readonly = @Readonly))
    @Comment("创建时间")
    @ApiModelProperty("创建时间")
    private LocalDateTime fillingTime;


//    @EruptField(
//
//            views = @View(title = "岗位"),
//            edit = @Edit(title = "岗位", type = EditType.TAGS, notNull = true, readonly = @Readonly(exprHandler = EmployeeView.class),
//                    tagsType = @TagsType(allowExtension = false, fetchHandler = EmployeeInfoJobTitleDataProxy.class)))
//    @Comment("岗位")
//    @ApiModelProperty("岗位")
//    private String jobTitle;

//    @EruptField(
//            views = @View(title = "岗位"),
//            edit = @Edit(title = "岗位", type = EditType.INPUT, notNull = true,
//                    inputType = @InputType))
//    @Comment("岗位")
//    @ApiModelProperty("岗位")
//    private String jobTitle;

    @EruptField(
            views = @View(title = "企业类型",width = "100px"),
            edit = @Edit(title = "企业类型", type = EditType.CHOICE,
                    readonly = @Readonly,
                    choiceType = @ChoiceType(fetchHandler = EruptDictCodeChoiceFetchHandler.class, fetchHandlerParams = "type")))
    @Comment("企业类型")
    @ApiModelProperty("企业类型")
    private String type;

    @Lob
    @EruptField(
            views = @View(title = "内容",show = false),
            edit = @Edit(title = "内容", type = EditType.TEXTAREA,show = true,notNull = true,
                    inputType = @InputType(length = 1000)))
    @Comment("内容")
    @ApiModelProperty("内容")
    private String content ;

//    @EruptField(
//            views = @View(title = "危险货物泊位数量", show = false),
//            edit = @Edit(title = "危险货物泊位数量", type = EditType.INPUT, notNull = false,
//                    inputType = @InputType))
//    @Comment("危险货物泊位数量")
//    @ApiModelProperty("危险货物泊位数量")
//    private String berthNo;
//
//    @EruptField(
//            views = @View(title = "处于安全运行状态储罐数量", show = false),
//            edit = @Edit(title = "处于安全运行状态储罐数量", type = EditType.INPUT, notNull = false,
//                    showBy = @ShowBy(dependField = "type", expr = "value=='OIL'"),
//                    inputType = @InputType))
//    @Comment("处于安全运行状态储罐数量")
//    @ApiModelProperty("处于安全运行状态储罐数量")
//    private String controTanknum;
//
//    @EruptField(
//            views = @View(title = "处于安全可控状态动火作业、受限空间作业风险数量", show = false),
//            edit = @Edit(title = "处于安全可控状态动火作业、受限空间作业风险数量",
//                    type = EditType.INPUT,
//                    showBy = @ShowBy(dependField = "type", expr = "value=='OIL'"),
//                    inputType = @InputType(fullSpan = true)))
//    @Comment("处于安全可控状态动火作业、受限空间作业风险数量")
//    @ApiModelProperty("处于安全可控状态动火作业、受限空间作业风险数量")
//    private String controRisknum;
//
//    @EruptField(
//            views = @View(title = "处于安全运行状态危险货物堆场（库房）", show = false),
//            edit = @Edit(title = "处于安全运行状态危险货物堆场（库房）", type = EditType.INPUT, notNull = false,
//                    showBy = @ShowBy(dependField = "type", expr = "value=='OTHER'"),
//                    inputType = @InputType))
//    @Comment("处于安全运行状态危险货物堆场（库房）")
//    @ApiModelProperty("处于安全运行状态危险货物堆场（库房）")
//    private String wareHouseNoKu;
//
//    @EruptField(
//            views = @View(title = "处于安全运行状态危险货物堆场", show = false),
//            edit = @Edit(title = "处于安全运行状态危险货物堆场", type = EditType.INPUT, notNull = false,
//                    showBy = @ShowBy(dependField = "type", expr = " value=='CONTAINER'"),
//                    inputType = @InputType))
//    @Comment("处于安全运行状态危险货物堆场")
//    @ApiModelProperty("处于安全运行状态危险货物堆场")
//    private String wareHouseNo;
//
//    @EruptField(
//            views = @View(title = "处于安全可控状态动火作业风险数量", show = false),
//            edit = @Edit(title = "处于安全可控状态动火作业风险数量", type = EditType.INPUT, notNull = false,
//                    showBy = @ShowBy(dependField = "type", expr = "value=='OTHER' || value=='CONTAINER'"),
//                    inputType = @InputType))
//    @Comment("处于安全可控状态动火作业风险数量")
//    @ApiModelProperty("处于安全可控状态动火作业风险数量")
//    private String riskNo;

    @EruptField(
            views = @View(title = "董事长或总经理",width = "150px"),
            edit = @Edit(title = "董事长或总经理", type = EditType.INPUT, notNull = true,
                    inputType = @InputType))
    @Comment("董事长或总经理")
    @ApiModelProperty("董事长或总经理")
    private String resPerson;

    @EruptField(
            views = @View(title = "附件", show = false),
            edit = @Edit(title = "附件", type = EditType.ATTACHMENT, notNull = false,
                    attachmentType = @AttachmentType(type = AttachmentType.Type.BASE, maxLimit = 20, fileTypes = {".pdf", ".doc", ".docx", ".xlsx", ".png", ".jpg", ".xls", ".ppt", ".pptx", ".zip", ".rar"})))
    @Comment("附件")
    @ApiModelProperty("附件")
    private String reportAttachments;


    @EruptField(
            views = @View(title = "上报状态",width = "100px"),
            edit = @Edit(title = "上报状态", type = EditType.BOOLEAN, show = false,/*notNull = true, */
                    boolType = @BoolType(trueText = "已上报", falseText = "未上报")))
    @CheckSubmit(linkProperty = "createTime", checkTimeDiffPeriod = 1)
    @Comment("上报状态")
    @ApiModelProperty("上报状态")
    private Boolean submitted;


}
