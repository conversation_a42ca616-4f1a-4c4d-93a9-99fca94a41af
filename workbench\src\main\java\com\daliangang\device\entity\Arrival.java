/*
* Copyright © 2020-2035 yundingyun All rights reserved.
*/
package com.daliangang.device.entity;

import com.daliangang.device.proxy.ArrivalDataProxy;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import xyz.erupt.annotation.Erupt;
import xyz.erupt.annotation.EruptField;
import xyz.erupt.annotation.sub_erupt.Power;
import xyz.erupt.annotation.sub_field.Edit;
import xyz.erupt.annotation.sub_field.EditType;
import xyz.erupt.annotation.sub_field.View;
import xyz.erupt.annotation.sub_field.sub_edit.InputType;
import xyz.erupt.annotation.sub_field.sub_edit.Search;
import xyz.erupt.jpa.model.MetaModel;
import xyz.erupt.toolkit.db.Comment;

import javax.persistence.Entity;
import javax.persistence.Lob;
import javax.persistence.Table;

@Erupt(name = "进港航道管理", power = @Power(add = true, delete = true, export = false, importable = true, viewDetails = true, edit = true)
, dataProxy = ArrivalDataProxy.class
, rowOperation = {})
@Table(name = "tb_arrival")
@Entity
@Getter
@Setter
@Comment("进港航道管理")
@ApiModel("进港航道管理")
public class Arrival extends MetaModel {
	@EruptField(
		views = @View(title = "进港航道名称"),
		edit = @Edit(title = "进港航道名称", type = EditType.INPUT, search = @Search(vague = true), notNull = true,
		inputType = @InputType))
	@Comment("进港航道名称")
	@ApiModelProperty("进港航道名称")
	private String approachChannel;

//	@EruptField(
//		views = @View(title = "经纬度", show = false),
//		edit = @Edit(title = "经纬度", type = EditType.INPUT, show = false, notNull = true,
//		inputType = @InputType))
//	@Comment("经纬度")
//	@ApiModelProperty("经纬度")
//	private String longitudeAndLatitude;

//	@EruptField(
//		views = @View(title = "经度", show = false),
//		edit = @Edit(title = "经度", type = EditType.INPUT, show = false,
//		inputType = @InputType))
//	@Comment("经度")
//	@ApiModelProperty("经度")
//	private String longitude;
//
//	@EruptField(
//		views = @View(title = "纬度", show = false),
//		edit = @Edit(title = "纬度", type = EditType.INPUT, show = false,
//		inputType = @InputType))
//	@Comment("纬度")
//	@ApiModelProperty("纬度")
//	private String latitude;

	@EruptField(
		views = @View(title = "地图", show = false),
		edit = @Edit(title = "地图", type = EditType.MAP, show = true))
	@Comment("地图")
	@ApiModelProperty("地图")
	@Lob
	private String map;

}
