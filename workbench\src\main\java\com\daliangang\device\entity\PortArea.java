/*
 * Copyright © 2020-2035 yundingyun All rights reserved.
 */
package com.daliangang.device.entity;

import com.daliangang.device.proxy.PortAreaDataProxy;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import xyz.erupt.annotation.Erupt;
import xyz.erupt.annotation.EruptField;
import xyz.erupt.annotation.sub_erupt.Power;
import xyz.erupt.annotation.sub_field.Edit;
import xyz.erupt.annotation.sub_field.EditType;
import xyz.erupt.annotation.sub_field.View;
import xyz.erupt.annotation.sub_field.sub_edit.InputType;
import xyz.erupt.annotation.sub_field.sub_edit.Search;
import xyz.erupt.jpa.model.MetaModel;
import xyz.erupt.toolkit.db.Comment;

import javax.persistence.Entity;
import javax.persistence.Lob;
import javax.persistence.Table;

@Erupt(name = "港区管理", importTruncate = true,
        power = @Power(add = true, delete = true, export = false, importable = true, viewDetails = true, edit = true)
        , dataProxy = PortAreaDataProxy.class
        , rowOperation = {})
@Table(name = "tb_port_area")
@Entity
@Getter
@Setter
@Comment("港区管理")
@ApiModel("港区管理")
public class PortArea extends MetaModel {
    @EruptField(
            views = @View(title = "港区名称"),
            edit = @Edit(title = "港区名称", type = EditType.INPUT, search = @Search(vague = true), notNull = true,
                    inputType = @InputType))
    @Comment("港区名称")
    @ApiModelProperty("港区名称")
    private String portareaName;

    @EruptField(
            views = @View(title = "合计面积（万平方米）"),
            edit = @Edit(title = "合计面积（万平方米）", type = EditType.INPUT, notNull = true,
                    inputType = @InputType))
    @Comment("合计面积（万平方米）")
    @ApiModelProperty("合计面积（万平方米）")
    private String totalArea;

    @EruptField(
            views = @View(title = "陆域面积（万平方米）"),
            edit = @Edit(title = "陆域面积（万平方米）", type = EditType.INPUT, notNull = true,
                    inputType = @InputType))
    @Comment("陆域面积（万平方米）")
    @ApiModelProperty("陆域面积（万平方米）")
    private String landArea;

    @EruptField(
            views = @View(title = "水域面积（万平方米）"),
            edit = @Edit(title = "水域面积（万平方米）", type = EditType.INPUT, notNull = true,
                    inputType = @InputType))
    @Comment("水域面积（万平方米）")
    @ApiModelProperty("水域面积（万平方米）")
    private String waterArea;

    @EruptField(
            views = @View(title = "港口生产已使用自然岸线长度（米）"),
            edit = @Edit(title = "港口生产已使用自然岸线长度（米）", type = EditType.INPUT, notNull = true,
                    inputType = @InputType))
    @Comment("港口生产已使用自然岸线长度（米）")
    @ApiModelProperty("港口生产已使用自然岸线长度（米）")
    private String shorelineLength;


    @EruptField(
            views = @View(title = "地图", show = false),
            edit = @Edit(title = "地图", type = EditType.MAP, show = true))
    @Comment("地图")
    @ApiModelProperty("地图")
    @Lob
    private String map;

}
