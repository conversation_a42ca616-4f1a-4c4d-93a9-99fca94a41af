/*
* Copyright © 2020-2035 yundingyun All rights reserved.
*/
package com.daliangang.workbench.proxy;

import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.daliangang.workbench.entity.EmployeeCertificateView;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import xyz.erupt.annotation.fun.DataProxy;
import xyz.erupt.annotation.query.Condition;
import xyz.erupt.common.http.EruptRemoteUtils;
import xyz.erupt.jpa.dao.EruptDao;
import xyz.erupt.toolkit.colorfont.ColorStateTimeFontDataProxy;
import xyz.erupt.toolkit.remote.RemoteProxyService;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

@Service
public class EmployeeCertificateViewProxy implements DataProxy<EmployeeCertificateView> {

    @Resource
    private ColorStateTimeFontDataProxy colorStateTimeFontDataProxy ;
    @Resource
    private EruptDao eruptDao;
    @Resource
    private RemoteProxyService remoteProxyService;

    @Override
    public String beforeFetch(List<Condition> conditions) {

        String sql = " 1= 1 " ;
        sql = sql + colorStateTimeFontDataProxy.searchByState(EmployeeCertificateView.class,"state" ,conditions);

        return sql;
    }

    @Override
    @Transactional
    public void afterAdd(EmployeeCertificateView employeeCertificateView) {
        this.resetOrgCode(employeeCertificateView);
        this.syncEmployeeCertificateViewData(employeeCertificateView);
    }

    @Override
    @Transactional
    public void afterUpdate(EmployeeCertificateView employeeCertificateView) {
        this.resetOrgCode(employeeCertificateView);
        this.syncEmployeeCertificateViewData(employeeCertificateView);
    }


    /**
     * 同步到一体化平台
     * @param employeeCertificateView1
     */
    public void syncEmployeeCertificateViewData(EmployeeCertificateView employeeCertificateView1){
        EmployeeCertificateView employeeCertificateView = JSONUtil.toBean(JSONUtil.toJsonStr(employeeCertificateView1), EmployeeCertificateView.class);
//        Enterprise enterprise = eruptDao.queryEntity(Enterprise.class, "org_code =" + SqlUtils.wrapStr(employeeCertificateView.getCompany1()));
        employeeCertificateView.setCompany(employeeCertificateView.getCompany1());
        JSONObject inputData = new JSONObject();
        inputData.set("clazz", "EmployeeCertificateView");
        inputData.set("insertData",employeeCertificateView);
        EruptRemoteUtils.doPost(remoteProxyService.getMainServerUrl() + "/erupt-api/exchange/data/exchange", inputData, Map.class);
    }

    @Override
    public EmployeeCertificateView queryDataById(long id) {
        return DataProxy.super.queryDataById(id);
    }

    /**
     * 将从业人员证书表中的orgCode赋值为所属企业的Orgcode
     * @param employeeCertificateView
     */
    public void resetOrgCode(EmployeeCertificateView employeeCertificateView){
        employeeCertificateView.setOrgCode(employeeCertificateView.getCompany());
        eruptDao.merge(employeeCertificateView);
    }
}
