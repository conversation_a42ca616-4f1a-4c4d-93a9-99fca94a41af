/*
* Copyright © 2020-2035 yundingyun All rights reserved.
*/
package com.daliangang.rndpub.operation;

import com.daliangang.rndpub.entity.EnterpriseInformation;
import com.daliangang.rndpub.entity.ExpertInformation;
import com.daliangang.rndpub.entity.Inspector;
import com.daliangang.rndpub.entity.Procedure;
import lombok.Data;
import org.springframework.stereotype.Service;
import xyz.erupt.annotation.Erupt;
import xyz.erupt.annotation.EruptField;
import xyz.erupt.annotation.fun.OperationHandler;
import xyz.erupt.annotation.sub_field.Edit;
import xyz.erupt.annotation.sub_field.EditType;
import xyz.erupt.annotation.sub_field.sub_edit.NumberType;
import xyz.erupt.core.context.MetaDrill;
import xyz.erupt.jpa.dao.EruptDao;
import xyz.erupt.jpa.model.BaseModel;
import xyz.erupt.toolkit.db.EruptResultMap;
import xyz.erupt.toolkit.utils.EruptDaoUtils;
import xyz.erupt.toolkit.utils.NotifyUtils;

import javax.annotation.Resource;
import javax.transaction.Transactional;
import java.util.List;

@Service
public class InspectorManualSelectionHandler implements OperationHandler<Inspector, InspectorManualSelectionHandler.DrawForm> {

    @Erupt(name = "自动抽取检查人员")
    @Data
    public static class DrawForm extends BaseModel {
        @EruptField(
                edit = @Edit(title = "抽取检查人员个数",
                        type = EditType.NUMBER,
                        numberType = @NumberType(min = 2),
                        desc = "人数最少2名",
                        notNull = true))
        private int count = 10;
    }

    @Resource
    private EruptDao eruptDao;

   @Override
   @Transactional
   public String exec(List<Inspector> data, DrawForm form, String[] param) {
       Procedure procedure = eruptDao.queryEntity(Procedure.class, "id=" + MetaDrill.getDrillId());
       //获取专家信息
       String expertSql = "select * from tb_expert_information where procedure_id=" + MetaDrill.getDrillId() + " and state=1";
       List<ExpertInformation> expertInformations = EruptDaoUtils.selectOnes(expertSql, ExpertInformation.class);
       if (expertInformations.size() == 0) {
           NotifyUtils.showErrorMsg("请先抽取专家");
       }
       //获取企业信息
       String enterpriseSql = "select enterprise_name,type from tb_enterprise_information where procedure_id=" + MetaDrill.getDrillId() + " and state=1";
       List<EnterpriseInformation> enterpriseList = EruptDaoUtils.selectOnes(enterpriseSql, EnterpriseInformation.class);
       if (enterpriseList.size() == 0) {
           NotifyUtils.showErrorMsg("请先抽取企业");
       }
       String sql = "select * from tb_inspector where procedure_id=" + MetaDrill.getDrillId() + " and state=0 order by rand() limit " + form.count;
       List<Inspector> inspectorList = EruptDaoUtils.selectOnes(sql, Inspector.class);
       if(inspectorList.isEmpty()){
           NotifyUtils.showErrorDialog("暂无检查人员可选！");
       }
       for (Inspector info : inspectorList) {
           String updSql = "update tb_inspector set state=1 where id=" + info.getId();
           eruptDao.getJdbcTemplate().execute(updSql);
       }
       updateCountAndName(procedure);
       return null;
	}


    public void updateCountAndName(Procedure procedure){
        String countSql = "select count(*) as count,group_concat(name)name from tb_inspector where procedure_id=" + procedure.getId() + " and state=1";
        EruptResultMap eruptResultMap = EruptDaoUtils.selectMap(countSql);
        procedure.setInspectorsNo(eruptResultMap.getInt("count"));
        procedure.setInspectors(eruptResultMap.getString("name"));
        eruptDao.merge(procedure);
    }
}
