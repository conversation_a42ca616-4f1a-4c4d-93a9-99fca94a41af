package com.daliangang.workbench.form;

import lombok.Data;
import xyz.erupt.annotation.EruptField;
import xyz.erupt.annotation.config.Comment;
import xyz.erupt.annotation.sub_field.Edit;
import xyz.erupt.annotation.sub_field.EditType;
import xyz.erupt.annotation.sub_field.Readonly;
import xyz.erupt.annotation.sub_field.View;
import xyz.erupt.annotation.sub_field.sub_edit.DateType;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @since :2023/6/7:18:02
 */
@Data
public class ToDoForm {

    @Comment("待办事项")
    private String item;


    @Comment("待办内容")
    private String message;


    @Comment("时间")
    protected LocalDateTime time ;


    private String erupt;


    private String owner;

}
