package com.daliangang.safedaily.operation;

import com.daliangang.safedaily.entity.CheckFill;
import com.daliangang.safedaily.proxy.CheckFillDataProxy;
import com.google.gson.JsonObject;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;
import xyz.erupt.annotation.fun.OperationHandler;
import xyz.erupt.core.annotation.EruptRouter;
import xyz.erupt.core.controller.EruptModifyController;
import xyz.erupt.core.view.EruptApiModel;
import xyz.erupt.jpa.dao.EruptDao;
import xyz.erupt.toolkit.utils.NotifyUtils;

import javax.annotation.Resource;
import javax.transaction.Transactional;
import java.util.List;

/**
 * @Author: zengwensi
 * @Description:
 * @Date: Created in 2023/3/15 9:59
 * @Modified By
 */
@Service
public class CheckFillEditHandler implements OperationHandler<CheckFill, Void> {

    @Resource
    private CheckFillDataProxy checkFillDataProxy;

    @Resource
    private EruptDao eruptDao;

    @Override
    @Transactional
    public String exec(List<CheckFill> data, Void unused, String[] param) {
        CheckFill checkFill = data.get(0);
        checkFillDataProxy.beforeUpdate(checkFill);
        eruptDao.merge(checkFill);
        checkFillDataProxy.afterUpdate(checkFill);
        return NotifyUtils.getSuccessNotify("修改成功");
    }

    @RestController
    public static class CheckFillEditController {
        @Resource
        private EruptModifyController modifyController;

        @PostMapping("/erupt-api/data/CheckFill/operator/checkFill")
        @EruptRouter(verifyType = EruptRouter.VerifyType.LOGIN)
        public EruptApiModel putUpdateEruptData(@RequestBody JsonObject data) throws IllegalAccessException {
            return modifyController.putUpdateEruptDataImpl("CheckFill", data.getAsJsonObject("param"));
        }
    }
}
