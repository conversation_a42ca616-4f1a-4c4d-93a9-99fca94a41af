<!DOCTYPE html>
<html lang="en">
<head>
	<meta charset="UTF-8">
	<meta http-equiv="X-UA-Compatible" content="IE=edge">
	<meta name="viewport" content="width=device-width, initial-scale=1.0">
	<title>首页</title>
	<script src="./js/vue.min.js"></script>
	<script src="./js/common.js"></script>
	<link rel="stylesheet" href="./css/common.css"/>
<link rel="stylesheet" href="./css/element-ui.css"/>
	<script src="./js/vue-resource.min.js"></script>
<script src="./js/element-ui.js"></script>

</head>
<body>
	<div class="wrap"id="app">
		<div class="wrapMain">
			<div class="title">{{title}}</div>
			<ul>
				<li v-for="(item,index) in list" :key="index"  @click="goTo(item)" class="item" :style="{'background':`url(./img/${item.img}) no-repeat`,'background-size':'100% 100%'}">
					<div class="info">
					<div class="name">{{item.name}}</div>
					<div class="link">点击进入>></div>
				</div>
				</li>
			</ul>
		</div>
	</div>
</body>
</html>
<script>
	var app = new Vue({
		el: '#app',
		mixins:[common],
		data: {
		// 	title:'港口设施维护管理系统',
		//   list:[
		// 	{name:'储罐管理',url:'/#/build/table/StorageTank',img:'bg1.png'},
		// 	{name:'堆场管理',url:'/#/build/table/Yard',img:'bg2.png'},
		// 	{name:'起重机械检测',url:'/#/build/table/HoistingMachinery',img:'bg3.png'},
		// 	{name:'压力管道检测',url:'/#/build/table/Penstock',img:'bg1.png'},
		// 	{name:'储罐检测',url:'/#/build/table/TankInspection',img:'bg3.png'},
		// 	{name:'防雷装置检测',url:'/#/build/table/LightningProtection',img:'bg2.png'},
		//   ]
		//   title:'港口危险货物安全日常业务监管系统',
		//   list:[
		// 	{name:'安全承诺公告',url:'/#/build/table/Release',img:'bg1.png'},
		// 	{name:'标准化管理',url:'/#/build/table/Standardization',img:'bg2.png'},
		// 	{name:'应急预案管理',url:'/#/build/table/PlanManagement',img:'bg3.png'},
		// 	{name:'重大危险源管理',url:'/#/build/table/Preplan',img:'bg1.png'},
		// 	{name:'MSDS',url:'/#/build/table/MSDS',img:'bg3.png'},
		// 	{name:'安全法规',url:'/#/build/table/PortSafeWikiEntry',img:'bg2.png'},
		//   ]

		//   title:'港区重大风险监测与管控系统',
		//   list:[
		// 	{name:'装卸船作业',url:'/#/build/table/UnloadShip',img:'bg1.png'},
		// 	{name:'装卸车作业',url:'/#/build/table/Unload',img:'bg2.png'},
		// 	{name:'检维修作业',url:'/#/build/table/Maintenance',img:'bg3.png'},
		// 	{name:'储罐每日储量上报',url:'/#/build/table/ReserveReporting',img:'bg1.png'},
		// 	{name:'重大风险分布图',url:'/major-risk/index.html',img:'bg3.png'},
		// 	{name:'风险数据库',url:'/#/build/table/RiskDatabaseDepartment',img:'bg2.png'},
		//   ]

		  title:'港区危险货物企业"双随机、一公开"系统',
		  list:[
			{name:'整改管理',url:'/#/build/table/Rectification',code:'Rectification',img:'bg1.png'},
			{name:'检查流程管理',url:'/#/build/table/Procedure',code:'Procedure',img:'bg2.png'},
			{name:'检查结果录入',url:'/#/build/table/InspectionResults',code:'Procedure',img:'bg3.png'},
			{name:'检查结果管理',url:'/#/build/table/InspectionResultsView',code:'InspectionResultsView',img:'bg1.png'},
			{name:'专家列表',url:'/#/build/table/Expert',code:'Expert',img:'bg3.png'},
			{name:'检查人员',url:'/#/build/table/CheckPerson',code:'CheckPerson',img:'bg2.png'},
		  ]
		
// 		title:'从业人员一体化精准管理系统',
// list:[
// {name:'我的课程',url:'/post/index.html#/my/course',img:'bg1.png'},
// {name:'我的培训',url:'/post/index.html#/my/train',img:'bg2.png'},
// {name:'培训记录（转到线下培训）',url:'/post/index.html#/educational/offlineTrain',img:'bg3.png'},
// {name:'课程管理',url:'/post/index.html#/educational/classStart',img:'bg1.png'},
// {name:'试卷管理',url:'/post/index.html#/educational/paperManage',img:'bg3.png'},
// {name:'培训管理',url:'/post/index.html#/educational/train',img:'bg2.png'},
// ]
		},
		created:{

		},
		methods:{
		}
	  })
</script>

<style>

</style>