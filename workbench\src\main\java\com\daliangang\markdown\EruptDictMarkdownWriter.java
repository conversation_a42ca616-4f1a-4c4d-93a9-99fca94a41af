package com.daliangang.markdown;

import lombok.Data;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.core.type.filter.AnnotationTypeFilter;
import org.springframework.core.type.filter.TypeFilter;
import org.springframework.stereotype.Service;
import xyz.erupt.core.service.EruptApplication;
import xyz.erupt.core.util.EruptSpringUtil;
import xyz.erupt.upms.dict.Dict;
import xyz.erupt.upms.dict.DictItem;
import xyz.erupt.upms.dict.Dicts;

import java.io.File;
import java.io.FileWriter;
import java.io.PrintWriter;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;
import java.util.StringTokenizer;

@Service
@Data
@Slf4j
public class EruptDictMarkdownWriter implements ApplicationRunner {

    @Value("${markdown.dict:}")
    private String exportDictValues;
    private Map<String, Dict> dictMap = new HashMap<>();

    private void generateMarkdown(PrintWriter out, Dict dict) {
        out.println("# " + dict.name());
        out.println();
        out.println("#### 字典编码：" + dict.code());
        out.println();
        out.println("|  字典项编码  |  字典项含义  |  备注  |");
        out.println("| --- | --- | --- |");
        for (DictItem item : dict.value()) {
            out.print("|  ");
            out.print(item.code());
            out.print("  |  ");
            out.print(item.value());
            out.println("  |       |");
        }
    }
    @Override
    @SneakyThrows
    public void run(ApplicationArguments args) throws Exception {
        File targetFile = new File("D:\\workspace\\daliangang-workbench\\workbench\\src\\main\\resources\\markdown\\字典说明.md");

        if (targetFile.exists()) targetFile.delete();
        targetFile.createNewFile();
        PrintWriter writer = new PrintWriter(new FileWriter(targetFile));

        //writer.println("# 字典说明");
        writer.println();
        writer.println();

        log.info("正在检查字典文档生成配置：{}", exportDictValues);

        EruptSpringUtil.scannerPackage(EruptApplication.getScanPackage(), new TypeFilter[]{
                new AnnotationTypeFilter(Dicts.class)
        }, clazz -> {
            Dicts dicts = clazz.getAnnotation(Dicts.class);
            Arrays.stream(dicts.value()).forEach(dict -> {
                dictMap.put(dict.code(), dict);
            });
        });
        EruptSpringUtil.scannerPackage(EruptApplication.getScanPackage(), new TypeFilter[]{
                new AnnotationTypeFilter(Dict.class)
        }, clazz -> {
            Dict dict = clazz.getAnnotation(Dict.class);
            dictMap.put(dict.code(), dict);
        });

        if (StringUtils.isEmpty(exportDictValues)) {
            dictMap.values().forEach(dict -> generateMarkdown(writer, dict));
        } else {
            StringTokenizer st = new StringTokenizer(exportDictValues, ",");
            while (st.hasMoreTokens()) {
                String dictName = st.nextToken().trim();
                if (dictMap.containsKey(dictName)) {
                    this.generateMarkdown(writer, dictMap.get(dictName));
                }
            }
        }

        writer.flush();
        writer.close();
    }
}
