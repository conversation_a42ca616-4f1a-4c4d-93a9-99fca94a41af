package com.daliangang.workbench.controller;

import com.daliangang.datascreen.regulatoryboard.service.RegulatoryService;
import com.daliangang.datascreen.response.StatisticsGroup;
import com.daliangang.datascreen.utils.DateUtil;
import com.daliangang.datascreen.utils.OrgUtils;
import com.daliangang.safedaily.entity.PlanManagement;
import com.daliangang.safedaily.entity.PortRecord;
import com.daliangang.safedaily.entity.PrePlanDangerSource;
import com.daliangang.safedaily.entity.Security;
import com.daliangang.workbench.entity.EmployeeCertificateView;
import com.daliangang.workbench.entity.EmployeeInformation;
import com.daliangang.workbench.form.EmployeeCertificateForm;
import com.daliangang.workbench.form.RiskProfileFrom;
import com.daliangang.workbench.sql.EmployeeInformationSql;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import xyz.erupt.core.annotation.EruptRouter;
import xyz.erupt.core.module.MetaUserinfo;
import xyz.erupt.core.view.EruptApiModel;
import xyz.erupt.jpa.dao.EruptDao;
import xyz.erupt.toolkit.remote.RemoteProxyService;
import xyz.erupt.toolkit.utils.EruptDaoUtils;
import xyz.erupt.toolkit.utils.SqlUtils;

import javax.annotation.Resource;
import javax.transaction.Transactional;
import java.util.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.daliangang.datascreen.utils.OrgUtils.getCompanyNamesByBatchQuery;

/**
 * @Author: zengwensi
 * @Description:
 * @Date: Created in 2023/4/12 17:57
 * @Modified By
 */
@RestController
public class EmployeeInformationController {
    @Resource
    private EruptDao eruptDao;

    @Resource
    private EmployeeInformationSql employeeInformationSql;

    @Resource
    private RemoteProxyService remoteProxyService;

    @Resource
    RegulatoryService regulatoryService;

    /**
     * 修改员工管理
     * @return
     */
    @RequestMapping("erupt-api/employeeInformation/updateEmployeeInformation")
    @EruptRouter(verifyType = EruptRouter.VerifyType.LOGIN)
    @Transactional
    public EruptApiModel updateEmployeeInformation(@RequestBody EmployeeInformation emp) {
        String sql = " update tb_employee_information set check_person ="+emp.getCheckPerson();
        if(emp.getCheckPerson()){
            if(StringUtils.isEmpty(emp.getCompany())){
                sql += ",company = null," ;
            }else {
                sql += ",company = '"+emp.getCompany()+"'," ;
            }

            if(StringUtils.isEmpty(emp.getCheckScope())){
                sql += "check_scope = null," ;
            }else {
                sql += " check_scope = '"+emp.getCheckScope()+"'," ;
            }

            if(StringUtils.isEmpty(emp.getCertificateNo())){
                sql += "certificate_no = null," ;
            }else {
                sql += " certificate_no ='"+emp.getCertificateNo() +"'," ;
            }

            if(StringUtils.isEmpty(emp.getCertificateNo())){
                sql += "certificate_no = null," ;
            }else {
                sql += " certificate_no ='"+emp.getCertificateNo() +"'," ;
            }

            if(StringUtils.isEmpty(emp.getEvadeEnterprise())){
                sql += "evade_enterprise = null" ;
            }else {
                sql += " evade_enterprise ='"+emp.getEvadeEnterprise() +"'" ;
            }
        }

        String where = " where id = "+emp.getId();
        int count = eruptDao.getJdbcTemplate().update(sql + where);
        return EruptApiModel.successApi(count);
    }


    // 统计人员证书
    @RequestMapping("erupt-api/get/selectEmployeeCertificateNum")
    @EruptRouter(verifyType = EruptRouter.VerifyType.LOGIN)
    public EruptApiModel selectEmployeeCertificateNum() {
        MetaUserinfo remoteUserInfo = remoteProxyService.getRemoteUserInfo();
//        List<LinkedTreeMap> linkedTreeMaps = remoteProxyService.queryEntity("Enterprise", RemoteQuery.builder().condition("orgCode", remoteUserInfo.getOrg()));
//        String isEnterprise = "1";
//        if (ObjectUtils.isEmpty(linkedTreeMaps)) {  // 不存在则是政府账号
//            isEnterprise = "0";
//        }
        String orgCode = SqlUtils.wrapIn(remoteUserInfo.getAuth());

        String sql = employeeInformationSql.selectEmployeeCertificate(orgCode);
        List<EmployeeCertificateForm> employeeCertificateForm = EruptDaoUtils.selectOnes(sql, EmployeeCertificateForm.class);
        return EruptApiModel.successApi(employeeCertificateForm);
    }

    //统计员工人数
    @RequestMapping("erupt-api/get/selectEmployeeInformationNum")
    @EruptRouter(verifyType = EruptRouter.VerifyType.LOGIN)
    public EruptApiModel selectEmployeeInformationNum() {
        MetaUserinfo remoteUserInfo = remoteProxyService.getRemoteUserInfo();
       // String orgCode = SqlUtils.wrapIn(remoteUserInfo.getAuth());
        String sql = employeeInformationSql.selectEmployeeInformationNum(remoteUserInfo.getOrg());
        EmployeeCertificateForm employeeCertificateForm = EruptDaoUtils.selectOne(sql, EmployeeCertificateForm.class);
        return EruptApiModel.successApi(employeeCertificateForm);
    }
    //统计检查员工人数
    @RequestMapping("erupt-api/get/selectJcEmployeeInformationNum")
    @EruptRouter(verifyType = EruptRouter.VerifyType.LOGIN)
    public EruptApiModel selectJcEmployeeInformationNum() {
        MetaUserinfo remoteUserInfo = remoteProxyService.getRemoteUserInfo();
        // String orgCode = SqlUtils.wrapIn(remoteUserInfo.getAuth());
        String sql = employeeInformationSql.selectJcEmployeInformationNum(remoteUserInfo.getOrg());
        EmployeeCertificateForm employeeCertificateForm = EruptDaoUtils.selectOne(sql, EmployeeCertificateForm.class);
        return EruptApiModel.successApi(employeeCertificateForm);
    }


    // 统计企业员工
    @RequestMapping("erupt-api/get/selectQyEmployeeInformationNum/{jobTitle}")
    @EruptRouter(verifyType = EruptRouter.VerifyType.LOGIN)
    public EruptApiModel selectQyEmployeeInformationNum(@PathVariable("jobTitle") String jobTitle) {
        MetaUserinfo remoteUserInfo = remoteProxyService.getRemoteUserInfo();
       // String orgCode = SqlUtils.wrapIn(remoteUserInfo.getAuth());

        String sql = employeeInformationSql.selectQyEmployeeInformationNum(remoteUserInfo.getOrg()+"."+jobTitle,remoteUserInfo.getOrg());
        EmployeeCertificateForm employeeCertificateForm = EruptDaoUtils.selectOne(sql, EmployeeCertificateForm.class);
        return EruptApiModel.successApi(employeeCertificateForm);
    }


    //第二大屏统计员工证书
    @RequestMapping("erupt-api/get/selectEmployeeInformationNumTwo/{orgCode}")
    @EruptRouter(verifyType = EruptRouter.VerifyType.LOGIN)
    public EruptApiModel selectEmployeeInformationNumTwo(@PathVariable("orgCode") String orgCode) {
        List<RiskProfileFrom> employeeCertificateForm = new ArrayList<>();
        List<String> orgCodes = OrgUtils.getDeptCodes(orgCode);
        //1.企业附证
        enterpriseCertificate(orgCode, employeeCertificateForm);
        //2.人员资质
        employeeCertificate(orgCodes, employeeCertificateForm);
        //3.应急预案
        planManagements(orgCodes,  employeeCertificateForm);
        //4.重大危险源
        prePlanDangerSources(orgCodes,  employeeCertificateForm);
        //5.安全评价
        portRecords(orgCodes, employeeCertificateForm);
        //6.港口保安
        securities(orgCodes,  employeeCertificateForm);
        //  MetaUserinfo remoteUserInfo = remoteProxyService.getRemoteUserInfo();
        // String orgCode = SqlUtils.wrapIn(remoteUserInfo.getAuth());
//        String sql = employeeInformationSql.selectEmployeeInformationNumTwo(orgCode);
//        List<EmployeeCertificateForm> employeeCertificateForm = EruptDaoUtils.selectOnes(sql, EmployeeCertificateForm.class);
        return EruptApiModel.successApi(employeeCertificateForm);
    }

    //港口保安
    private void securities(List<String> orgCodes, List<RiskProfileFrom> employeeCertificateForm) {
        RiskProfileFrom form = new RiskProfileFrom();
        List<Security> securities = eruptDao.queryEntityList(Security.class, "orgCode IN :orgCodes", Collections.singletonMap("orgCodes", orgCodes));
        Date currentDate = DateUtil.getCurrentDayDate();
        // 计算已逾期的数量
        long expiredCount = regulatoryService.calculateExpiredCount(securities, currentDate,Security::getEffectiveDate);
        form.setTotal(String.valueOf(securities.size()));
        form.setExceed(String.valueOf(expiredCount));
        form.setCode("Security");
        employeeCertificateForm.add(form);
    }

    //安全评价
    private void portRecords(List<String> orgCodes, List<RiskProfileFrom> employeeCertificateForm) {
        RiskProfileFrom form = new RiskProfileFrom();
        List<PortRecord> portRecords = eruptDao.queryEntityList(PortRecord.class, "orgCode IN :orgCodes", Collections.singletonMap("orgCodes", orgCodes));
        Date currentDate = DateUtil.getCurrentDayDate();
        // 计算已逾期的数量
        long expiredCount = regulatoryService.calculateExpiredCount(portRecords, currentDate,PortRecord::getValidityPeriod);
        form.setTotal(String.valueOf(portRecords.size()));
        form.setExceed(String.valueOf(expiredCount));
        form.setCode("PortRecord");
        employeeCertificateForm.add(form);
    }

    //重大危险源
    private void prePlanDangerSources(List<String> orgCodes, List<RiskProfileFrom> employeeCertificateForm) {
        RiskProfileFrom form = new RiskProfileFrom();
        List<PrePlanDangerSource> prePlanDangerSources = eruptDao.queryEntityList(PrePlanDangerSource.class, "orgCode IN :orgCodes", Collections.singletonMap("orgCodes", orgCodes));
        // 获取当前日期
        Date currentDate = DateUtil.getCurrentDayDate();
        // 计算到期时间：根据备案时间延后 3 年
        Function<PrePlanDangerSource, Date> calculateExpiryDate = source -> {
            Date recordDate = source.getRecordTime();
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(recordDate);
            calendar.add(Calendar.YEAR, 3); // 延后3年得到到期时间
            return calendar.getTime();
        };
        // 计算已逾期的数量
        long expiredCount = regulatoryService.calculateExpiredCount(prePlanDangerSources, currentDate,calculateExpiryDate);
        form.setTotal(String.valueOf(prePlanDangerSources.size()));
        form.setExceed(String.valueOf(expiredCount));
        form.setCode("PrePlanDangerSource");
        employeeCertificateForm.add(form);
    }

    //应急预案统计
    private void planManagements(List<String> orgCodes, List<RiskProfileFrom> employeeCertificateForm) {
        RiskProfileFrom form = new RiskProfileFrom();
        List<PlanManagement> planManagements = eruptDao.queryEntityList(PlanManagement.class, "orgCode IN :orgCodes", Collections.singletonMap("orgCodes", orgCodes));
        //3.1 应急预案总数
        form.setTotal(String.valueOf(planManagements.size()));
        //3.2 应急预案逾期数
        // 获取当前日期
        Date currentDate = DateUtil.getCurrentDayDate();
        long expiredCount = regulatoryService.calculateExpiredCount(planManagements, currentDate,PlanManagement::getEvaluationTime);
        form.setExceed(String.valueOf(expiredCount));
        form.setCode("PlanManagement");
        employeeCertificateForm.add(form);
    }

    //人员资质
    private void employeeCertificate(List<String> orgCodes, List<RiskProfileFrom> employeeCertificateForm) {
        RiskProfileFrom form = new RiskProfileFrom();
        // 获取所有的 orgCodes
        List<String> companyNames = getCompanyNamesByBatchQuery(orgCodes);
        // 替换公司名称中的括号，确保匹配中文括号与英文括号
        // 标准化输入的公司名称列表，将括号统一为中文括号
        List<String> companyName = companyNames.stream()
                .map(name -> name.replace("(", "（").replace(")", "）"))
                .collect(Collectors.toList());

        // 在 SQL 中将数据库字段的括号也统一为中文括号
        List<EmployeeCertificateView> views = eruptDao.queryEntityList(
                EmployeeCertificateView.class,
                "REPLACE(REPLACE(company1, '(', '（'), ')', '）') IN :companyNames",
                Collections.singletonMap("companyNames", companyName)
        );
        //2.1 人员资质总数
        form.setTotal(String.valueOf(views.size()));
        //2.2 计算逾期数量
        int expiredCount = 0;
        for (EmployeeCertificateView view : views) {
            if (view.getValidityPeriod() != null && view.getValidityPeriod().before(new Date())) {
                expiredCount++;
            }
        }
        form.setExceed(String.valueOf(expiredCount));
        //2.3 统计类型人员资质code
        form.setCode("EmployeeCertificateView");
        employeeCertificateForm.add(form);
    }

    //企业附证
    private void enterpriseCertificate(String orgCode, List<RiskProfileFrom> employeeCertificateForm) {
        RiskProfileFrom form = new RiskProfileFrom();
        StatisticsGroup enterpriseCertificate = regulatoryService.getEnterpriseCertificate(orgCode);
        //1.1 企业附征总数
        StatisticsGroup.StatisticsItem certificateItem = enterpriseCertificate.getItems().get(0);
        Integer certificateTotal = certificateItem.getCount();
        AtomicReference<Integer> certificateExceed = new AtomicReference<>(0);
        certificateItem.getSubStats().forEach(subItem -> {
            if (subItem.getSubTitle().equals("已逾期")) {
                certificateExceed.updateAndGet(v -> v + subItem.getCount());
            }
        });
        form.setTotal(String.valueOf(certificateTotal));
        form.setExceed(String.valueOf(certificateExceed.get()));
        form.setCode("EnterpriseCertificateView");
        employeeCertificateForm.add(form);
    }

//    @RequestMapping("erupt-api/get/getOwnerRole")
//    @EruptRouter(verifyType = EruptRouter.VerifyType.LOGIN)
//    public EruptApiModel getOwnerRole() {
//        //查看身份
////        String role="ENTERPRISE";
//        //是否为政府用户
////        boolean departmentUser = DaliangangContext.isDepartmentUser();
//        //获取当前用户
//        EruptUser currentEruptUser = EruptSpringUtil.getBean(EruptUserService.class).getCurrentEruptUser();
//        //获取当前用户的账户
//        String account = currentEruptUser.getName();
//        //根据账户查询类型
//        String selectSql1="select * from e_upms_role where name like '"+account+"%'";
//        EruptRole selectOne = EruptDaoUtils.selectOne(selectSql1, EruptRole.class);
//        //如果是政府用户
//        String role="";
//        if(selectOne!=null){
//            //role=selectOne.getRoleType();
//        }
//        String selectSql="select * from e_upms_role where role_type='"+role+"'";
//        List<EruptRole> eruptRoles = EruptDaoUtils.selectOnes(selectSql, EruptRole.class);
//        if (ObjectUtils.isNotEmpty(eruptRoles)) {
//            List<LinkedTreeMap> list1 = new ArrayList<>();
//            eruptRoles.forEach(e->{
//                LinkedTreeMap map = new LinkedTreeMap();
//                map.put("code",e.getCode());
//                map.put("name",e.getName());
//                list1.add(map);
//            });
//            return EruptApiModel.successApi(list1);
//        }
//        return EruptApiModel.successApi();
//    }

}
