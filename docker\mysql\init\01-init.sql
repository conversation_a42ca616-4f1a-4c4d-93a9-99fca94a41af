-- 大连港工作台数据库初始化脚本

-- 设置字符集
SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- 创建数据库（如果不存在）
CREATE DATABASE IF NOT EXISTS `daliangang` 
DEFAULT CHARACTER SET utf8mb4 
COLLATE utf8mb4_unicode_ci;

-- 使用数据库
USE `daliangang`;

-- 创建用户并授权（如果不存在）
CREATE USER IF NOT EXISTS 'daliangang_user'@'%' IDENTIFIED BY 'DLG!User@2024#Safe';
GRANT ALL PRIVILEGES ON `daliangang`.* TO 'daliangang_user'@'%';
FLUSH PRIVILEGES;

-- 设置时区
SET time_zone = '+08:00';

-- 记录初始化完成
INSERT INTO information_schema.PROCESSLIST (ID, USER, HOST, DB, COMMAND, TIME, STATE, INFO) 
VALUES (0, 'system', 'localhost', 'daliangang', 'Init', 0, 'Completed', 'Database initialization completed') 
ON DUPLICATE KEY UPDATE INFO = 'Database initialization completed';

SET FOREIGN_KEY_CHECKS = 1;
