package com.daliangang.safedaily.sql;

import org.springframework.stereotype.Repository;

/**
 * <AUTHOR>
 * @since :2023/4/23:14:02
 */
@Repository
public class MonthlySchedulingSql {

    public String selectMonthlySchedulingNum (String orgCode) {

        String sql = "select COUNT(DISTINCT org_code) as num  from tb_monthly_scheduling where submitted = 1 and  date_format(NOW(), '%Y-%m' ) = report_month and ";
        sql += " org_code "+orgCode+"";
        return sql;
    }
}
