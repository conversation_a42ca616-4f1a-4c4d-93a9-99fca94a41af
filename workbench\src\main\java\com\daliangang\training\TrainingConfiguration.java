package com.daliangang.training;

import com.daliangang.training.entity.FileUpload;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.annotation.Order;
import xyz.erupt.core.constant.MenuStatus;
import xyz.erupt.core.constant.MenuTypeEnum;
import xyz.erupt.core.module.EruptModule;
import xyz.erupt.core.module.EruptModuleInvoke;
import xyz.erupt.core.module.MetaMenu;
import xyz.erupt.core.module.ModuleInfo;
import xyz.erupt.core.util.EruptSpringUtil;
import xyz.erupt.upms.service.EruptMenuService;
import xyz.erupt.upms.util.EruptMenuUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * <AUTHOR>
 * date 2021/3/28 18:51
 */
@Configuration
@Slf4j
@Order(Integer.MAX_VALUE)
public class TrainingConfiguration implements EruptModule {

    static {
        EruptModuleInvoke.addEruptModule(TrainingConfiguration.class);
    }

    @Override
    public ModuleInfo info() {
        return ModuleInfo.builder().name("daliangang-training").build();
    }

    @Override
    public List<MetaMenu> initMenus() {
        EruptMenuService eruptMenuService = EruptSpringUtil.getBean(EruptMenuService.class);
        String moduleId = "training";
        eruptMenuService.registerModule(moduleId, "从业人员一体化精准管理系统", 6);
        AtomicInteger sort = new AtomicInteger();
        AtomicInteger rootSort = new AtomicInteger(600);

        List<MetaMenu> menus = new ArrayList<>();

        menus.add(MetaMenu.createSimpleMenu("MyCourse", "我的课程", "/post/index.html#/my/course", null, rootSort.getAndIncrement(),"fa fa-hospital-o",MenuTypeEnum.LINK.getCode(), MenuStatus.HIDE).module(moduleId).hide());
        menus.add(MetaMenu.createSimpleMenu("MyTrain", "我的培训", "/post/index.html#/my/train", null, rootSort.getAndIncrement(), "fa fa-book",MenuTypeEnum.LINK.getCode(),MenuStatus.HIDE).module(moduleId).hide());
        menus.add(MetaMenu.createSimpleMenu("MyExam", "我的考试", "/post/index.html#/my/examine", null, rootSort.getAndIncrement(), "fa fa-wpforms",MenuTypeEnum.LINK.getCode(),MenuStatus.HIDE).module(moduleId).hide());

        //知识库分类
        MetaMenu knowledge = EruptMenuUtils.createRootMenu(null, "$trainingKnowledgeBase", "知识库管理", "fa fa-balance-scale", rootSort, moduleId);

        menus.add(knowledge);
        menus.add(MetaMenu.createSimpleMenu("trainingKnowledgeBase", "分类管理", "/post/index.html#/knowledge/category", knowledge, rootSort.getAndIncrement(), MenuTypeEnum.LINK.getCode()).module(moduleId));
        menus.add(MetaMenu.createSimpleMenu("Question", "试题管理", "/post/index.html#/knowledge/questionstart", knowledge, rootSort.getAndIncrement(), MenuTypeEnum.LINK.getCode()).module(moduleId));
        menus.add(MetaMenu.createSimpleMenu("courseware", "课件管理", "/post/index.html#/knowledge/course", knowledge, rootSort.getAndIncrement(), MenuTypeEnum.LINK.getCode()).module(moduleId));


        MetaMenu post = EruptMenuUtils.createRootMenu(null, "$postCompetence", "岗位评估", "fa fa-flag-checkered", rootSort, moduleId);
        menus.add(post);
        menus.add(MetaMenu.createSimpleMenu("PostManage", "指标体系", "/post/index.html#/jobCompetence/personnel-management", post, rootSort.getAndIncrement(), MenuTypeEnum.LINK.getCode()).module(moduleId));
        menus.add(MetaMenu.createSimpleMenu("StandardManage", "指标体系下钻", "/post/index.html#", post, rootSort.getAndIncrement(), MenuTypeEnum.LINK.getCode()).module(moduleId).hide());
        menus.add(MetaMenu.createSimpleMenu("EvaluationPaperInfo", "问卷管理", "/post/index.html#/jobCompetence/evaluatio-questionnaire", post, rootSort.getAndIncrement(), MenuTypeEnum.LINK.getCode()).module(moduleId));
        menus.add(MetaMenu.createSimpleMenu("TrainingEvaluationManage", "评估管理", "/post/index.html#/jobCompetence/evaluation-management", post, rootSort.getAndIncrement(), MenuTypeEnum.LINK.getCode()).module(moduleId));
        menus.add(MetaMenu.createSimpleMenu("MineEvaluation", "我的自评", "/post/index.html#/jobCompetence/questionnaire-self-evaluation", post, rootSort.getAndIncrement(), MenuTypeEnum.LINK.getCode()).module(moduleId));
        menus.add(MetaMenu.createSimpleMenu("OtherEvaluation", "他评问卷", "/post/index.html#/jobCompetence/separate-evaluation", post, rootSort.getAndIncrement(), MenuTypeEnum.LINK.getCode()).module(moduleId));
        menus.add(MetaMenu.createSimpleMenu("EvaluationStatistic", "评估统计", "/post/index.html#/jobCompetence/evaluation-statistics", post, rootSort.getAndIncrement(), MenuTypeEnum.LINK.getCode()).module(moduleId));


        //教务
        MetaMenu trainStuff = EruptMenuUtils.createRootMenu(null, "$trainStuff", "培训管理", "fa fa-dashboard", rootSort, moduleId);
        menus.add(trainStuff);
        menus.add(MetaMenu.createSimpleMenu("Course", "课程管理", "/post/index.html#/educational/classStart", trainStuff, rootSort.getAndIncrement(), MenuTypeEnum.LINK.getCode()).module(moduleId));
        menus.add(MetaMenu.createSimpleMenu("Paper", "试卷管理", "/post/index.html#/educational/paperManage", trainStuff, rootSort.getAndIncrement(), MenuTypeEnum.LINK.getCode()).module(moduleId));
        menus.add(MetaMenu.createSimpleMenu("Train", "培训管理", "/post/index.html#/educational/train", trainStuff, rootSort.getAndIncrement(), MenuTypeEnum.LINK.getCode()).module(moduleId));
        menus.add(MetaMenu.createSimpleMenu("Grade", "成绩管理", "/post/index.html#/educational/scoreManage", trainStuff, rootSort.getAndIncrement(), MenuTypeEnum.LINK.getCode()).module(moduleId));
        menus.add(MetaMenu.createSimpleMenu("GradeStatistical", "考核统计", "/post/index.html#/educational/assess", trainStuff, rootSort.getAndIncrement(), MenuTypeEnum.LINK.getCode()).module(moduleId));
        menus.add(MetaMenu.createSimpleMenu("OfflineTrain", "线下培训", "/post/index.html#/educational/offlineTrain", trainStuff, rootSort.getAndIncrement(), MenuTypeEnum.LINK.getCode()).module(moduleId));

//        MetaMenu kaoHe = EruptMenuUtils.createRootMenu(trainStuff, "GradeStatisticalParent", "考核统计", "", rootSort, moduleId);
//        menus.add(kaoHe);
//        menus.add(MetaMenu.createSimpleMenu("GradeStatistical", "考核统计", "/post/index.html#/educational/assess", trainStuff, rootSort.getAndIncrement(), MenuTypeEnum.LINK.getCode()).module(moduleId));
//        menus.add(MetaMenu.createSimpleMenu("TrainStatistics", "培训统计", "/post/index.html#/educational/assess/train/:id", kaoHe, rootSort.getAndIncrement(), MenuTypeEnum.LINK.getCode()).module(moduleId).hide());
//        menus.add(MetaMenu.createSimpleMenu("UserStatistics", "考生统计", "/post/index.html#/educational/assess/user/:id", kaoHe, rootSort.getAndIncrement(), MenuTypeEnum.LINK.getCode()).module(moduleId).hide());

        menus.add(MetaMenu.createSimpleMenu("OWXfcRW8", "企业统计", "OWXfcRW8", trainStuff, rootSort.getAndIncrement(), "bi").module(moduleId));
        menus.add(MetaMenu.createSimpleMenu("EA2LbAQx", "考生统计", "EA2LbAQx", trainStuff, rootSort.getAndIncrement(), "bi").module(moduleId));


        MetaMenu userManager = EruptMenuUtils.createRootMenu(null, "$userManager", "人员管理", "fa fa-address-book-o", rootSort, moduleId).hide();
        menus.add(userManager);
        menus.add(MetaMenu.createSimpleMenu("StuManage", "学员管理", "/post/index.html#/account/student", userManager, rootSort.getAndIncrement(), MenuTypeEnum.LINK.getCode()).module(moduleId));
        menus.add(MetaMenu.createSimpleMenu("UserManage", "用户管理", "/post/index.html#/account/account", userManager, rootSort.getAndIncrement(), MenuTypeEnum.LINK.getCode()).module(moduleId));



        menus.add(EruptMenuUtils.createMenu(FileUpload.class, menus.get(0), rootSort, MenuTypeEnum.TABLE, "专门上传文件的菜单", "FileUpload", moduleId, null).hide());


        return menus;
    }
}
