/*
* Copyright © 2020-2035 yundingyun All rights reserved.
*/
package com.daliangang.rndpub.proxy;

import cn.hutool.core.util.ObjectUtil;
import com.daliangang.emergency.entity.EmergencyDuty;
import lombok.*;
import org.springframework.stereotype.*;
import xyz.erupt.annotation.fun.*;
import com.daliangang.rndpub.entity.*;
import xyz.erupt.core.util.EruptSpringUtil;
import xyz.erupt.jpa.dao.EruptDao;
import xyz.erupt.toolkit.utils.EruptDaoUtils;

import javax.annotation.*;
import javax.transaction.Transactional;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Map;

@Service
public class ExpertauditDataProxy implements DataProxy<Expertaudit> {

    @Resource
    private EruptDao eruptDao;

    @Transactional
    @Override
    public void afterFetch(Collection<Map<String, Object>> list) {
        List<Expertaudit> expertAudits = EruptDaoUtils.convert(list, Expertaudit.class);
        ArrayList<Expertaudit> updateExperts = new ArrayList<>();
        expertAudits.forEach(expert -> {
            if(expert.getUseState()==null){
                expert.setUseState(false);
                updateExperts.add(expert);
            }
        });
        String updateStateSql="update "+EruptDaoUtils.getTableName(Expertaudit.class)+" set use_state=0 where id=";
        updateExperts.forEach(expert->{
         eruptDao.getJdbcTemplate().execute(updateStateSql+expert.getId());
        });
    }
}
