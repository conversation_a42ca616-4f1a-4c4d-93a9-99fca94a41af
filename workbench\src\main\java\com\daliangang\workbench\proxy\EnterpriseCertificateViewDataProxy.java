/*
 * Copyright © 2020-2035 yundingyun All rights reserved.
 */
package com.daliangang.workbench.proxy;


import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.daliangang.core.DaliangangContext;
import com.daliangang.workbench.entity.Enterprise;
import com.daliangang.workbench.entity.EnterpriseCertificate;
import com.daliangang.workbench.entity.EnterpriseCertificateView;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.stereotype.Service;
import xyz.erupt.annotation.fun.DataProxy;
import xyz.erupt.annotation.query.Condition;
import xyz.erupt.common.http.EruptRemoteUtils;
import xyz.erupt.jpa.dao.EruptDao;
import xyz.erupt.toolkit.remote.RemoteProxyService;
import xyz.erupt.toolkit.utils.AssertUtils;
import xyz.erupt.toolkit.utils.EruptDaoUtils;
import xyz.erupt.toolkit.utils.NotifyUtils;
import xyz.erupt.toolkit.utils.SqlUtils;
import xyz.erupt.upms.service.EruptUserService;

import javax.annotation.Resource;
import javax.transaction.Transactional;
import java.time.LocalDateTime;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.atomic.AtomicReference;

@Service
public class EnterpriseCertificateViewDataProxy implements DataProxy<EnterpriseCertificateView> {

    @Resource
    private EruptUserService eruptUserService;

    @Resource
    private EruptDao eruptDao;

    @Resource
    private RemoteProxyService remoteProxyService;

    @Override
    public String beforeFetch(List<Condition> conditions) {
        //只能查看自己的数据
        AtomicReference<String> returnStr = new AtomicReference<>(" 1 = 1 and company "+SqlUtils.wrapIn(eruptUserService.getUserAuth())) ;
        //状态查询条件
        Optional<Condition> stateCondition = conditions.stream().filter(condition ->
                "expiredState".equals(condition.getKey())
        ).findFirst();
        stateCondition.ifPresent( f ->{
            LocalDateTime now = LocalDateTime.now();
            String expiredState = (String)f.getValue();
            if("EXPIRED".equals(expiredState)){
                //已逾期
                returnStr.set(returnStr.get()+" and CURRENT_DATE() > expiration_time");
            }else if("NORMAL".equals(expiredState)){
                //正常
                returnStr.set(returnStr.get()+" and  datediff(expiration_time,CURRENT_DATE()) > 90");
            }else {
                //即将逾期
                returnStr.set(returnStr.get() + "and  CURRENT_DATE() <= expiration_time and datediff(expiration_time,CURRENT_DATE()) < 90") ;
            }
            conditions.remove(f) ;
        });
        Optional<Condition> searchCompany = conditions.stream().filter(condition ->
                "searchCompany".equals(condition.getKey())
        ).findFirst();
        searchCompany.ifPresent( f ->{
            f.setKey("company");
        });
        return returnStr.get();
    }

    @Override
    public void addBehavior(EnterpriseCertificateView certificate) {
        if(!DaliangangContext.isDepartmentUser()){

            certificate.setCompany(eruptUserService.getCurrentEruptUser().getEruptOrg().getCode());
        }

    }

    @Override
    public void beforeAdd(EnterpriseCertificateView certificate) {
        this.fillCertificateData(certificate);
    }

    @Override
    public void beforeUpdate(EnterpriseCertificateView certificate) {
        this.fillCertificateData(certificate);
    }

    /**
     * 补充信息
     * @param certificate
     */
    private void fillCertificateData(EnterpriseCertificateView certificate){

        //获取企业信息
        if(!DaliangangContext.isDepartmentUser()){
            certificate.setCompany(eruptUserService.getCurrentEruptUser().getEruptOrg().getCode());
        }
        String company = certificate.getCompany();
        Enterprise enterprise = EruptDaoUtils.selectOne("select * from tb_enterprise where org_code = "+ SqlUtils.wrapStr(company),Enterprise.class) ;
        certificate.setEnterpriseId(enterprise.getId());
    }

    @Override
    public void afterAdd(EnterpriseCertificateView enterpriseCertificate) {
        //同步到一体化平台
        this.syncEnterpriseCertificateData(enterpriseCertificate);
    }

    @Override
    public void afterUpdate(EnterpriseCertificateView enterpriseCertificate) {
        //同步到一体化平台
        this.syncEnterpriseCertificateData(enterpriseCertificate);
    }

    /**
     * 同步到一体化平台
     * @param enterpriseCertificate
     */
    public void syncEnterpriseCertificateData(EnterpriseCertificateView enterpriseCertificate){
        EnterpriseCertificateView view = JSONUtil.toBean(JSONUtil.toJsonStr(enterpriseCertificate), EnterpriseCertificateView.class);
        Enterprise enterprise = EruptDaoUtils.selectOne("select * from tb_enterprise where org_code = "+ SqlUtils.wrapStr(enterpriseCertificate.getCompany()),Enterprise.class) ;
        view.setCompany(enterprise.getName());
        JSONObject inputData = new JSONObject();
        inputData.set("clazz", "EnterpriseCertificate");
        inputData.set("insertData",view);
        EruptRemoteUtils.doPost(remoteProxyService.getMainServerUrl() + "/erupt-api/exchange/data/exchange", inputData, Map.class);
    }

    @Override
    @Transactional
    public void afterFetch(Collection<Map<String, Object>> list) {
        list.forEach(vo -> {
            vo.put("erupt", "EnterpriseCertificate");
//            String sql = "update tb_enterprise_certificate set state = \"%s\" where id = %s" ;
//            eruptDao.getJdbcTemplate().update(String.format(sql,vo.get("state"),vo.get("id")));
        });
    }

    @Override
    public void excelImport(Object workbook) {
        Workbook wb = (Workbook) workbook;
        Sheet sheet = wb.getSheetAt(0);
        for (int i = sheet.getFirstRowNum(); i <= sheet.getLastRowNum(); i++) {
            Row row = sheet.getRow(i);
            Cell cell = row.getCell(1);
            AssertUtils.notNull(cell, "附证编号不可为空");
            String certNo = "";
            switch (cell.getCellType()) {
                case STRING:
                    certNo = cell.getStringCellValue();
                    break;
                case NUMERIC:
                    certNo = cell.getNumericCellValue() + "";
                    break;
            }
            if (StringUtils.isEmpty(certNo)) NotifyUtils.showErrorMsg("附证编号不可为空");
            EnterpriseCertificate certificate = eruptDao.queryEntity(EnterpriseCertificate.class, "certificate_no=" + SqlUtils.wrapStr(certNo));
            AssertUtils.isNull(certificate, "附证编号已存在 -> " + certNo);
        }
    }

//    @Override
//    public void afterExcelImport(Object workbook, List list) {
//        list.forEach(obj -> {
//            JsonObject vo = (JsonObject) obj;
//            String orgCode = vo.get("company").getAsString();
//            Enterprise enterprise = eruptDao.queryEntity(Enterprise.class, "org_code=" + SqlUtils.wrapStr(orgCode));
//            AssertUtils.notNull(enterprise, "没有这个企业 -> " + orgCode);
//            String updateSql = "update tb_enterprise_certificate set enterprise_id=" + enterprise.getId() + " where certificate_no=" + SqlUtils.wrapStr(vo.get("certificateNo").getAsString());
//            EruptDaoUtils.updateNoForeignKeyChecks(updateSql);
//        });
//    }

//    @Override
//    public void excelExport(Object workbook) {
//        Workbook wb = (Workbook) workbook;
//        Sheet sheet = wb.getSheetAt(0);
//        Row title = sheet.getRow(0);
//        int index = -1;
//        for (int i = title.getFirstCellNum(); i <= title.getLastCellNum(); i++) {
//            Cell cell = title.getCell(i);
//            if (cell.getStringCellValue().equals("状态")) {
//                index = i;
//                break;
//            }
//        }
//        if (index < 0) return;
//        for (int i = 1; i <= sheet.getLastRowNum(); i++) {
//            Row row = sheet.getRow(i);
//            Cell cell = row.getCell(index);
//            String value = cell.getStringCellValue();
//            value = value.replace("<span class='e-tag'>", "");
//            value = value.replace("</font></span>", "");
//            StringTokenizer st = new StringTokenizer(value, "'>");
//            st.nextToken();
//            st.nextToken();
//            value = st.nextToken();
//            cell.setCellValue(value);
//        }
//    }

}
