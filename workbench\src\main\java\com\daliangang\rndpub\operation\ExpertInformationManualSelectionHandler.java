/*
* Copyright © 2020-2035 yundingyun All rights reserved.
*/
package com.daliangang.rndpub.operation;

import com.daliangang.rndpub.entity.EnterpriseInformation;
import com.daliangang.rndpub.entity.ExpertInformation;
import com.daliangang.rndpub.entity.Procedure;
import org.springframework.stereotype.Service;
import xyz.erupt.annotation.fun.OperationHandler;
import xyz.erupt.core.context.MetaDrill;
import xyz.erupt.jpa.dao.EruptDao;
import xyz.erupt.toolkit.utils.EruptDaoUtils;
import xyz.erupt.toolkit.utils.NotifyUtils;

import javax.annotation.Resource;
import javax.transaction.Transactional;
import java.util.List;

@Service
public class ExpertInformationManualSelectionHandler implements OperationHandler<ExpertInformation, Void> {

    @Resource
    private EruptDao eruptDao;

    @Resource
    private ExpertInformationSystemExtractionHandler expertInformationSystemExtractionHandler;

    @Override
    @Transactional
    public String exec(List<ExpertInformation> data, Void unused, String[] param) {

        //获取企业信息
        String enterpriseSql = "select enterprise_name,type from tb_enterprise_information where procedure_id=" + MetaDrill.getDrillId() + " and state=1";
        List<EnterpriseInformation> enterpriseList = EruptDaoUtils.selectOnes(enterpriseSql, EnterpriseInformation.class);
        if (enterpriseList.size() == 0) {
            NotifyUtils.showErrorMsg("请先抽取企业");
        }
        boolean selectedFlag = Boolean.parseBoolean(param[0]);
        if(selectedFlag) {
            String expertInformation = "select * from tb_expert_information where state=0";
            List<ExpertInformation> expertInformationList = EruptDaoUtils.selectOnes(expertInformation, ExpertInformation.class);
            if (expertInformationList.size() == 0) {
                NotifyUtils.showErrorMsg("暂无专家可选！");
            }
        }
        for (ExpertInformation info : data) {
            info.setState(selectedFlag);
            eruptDao.merge(info);
            eruptDao.flush();
        }
        //更新条数
        Procedure procedure = eruptDao.queryEntity(Procedure.class, "id=" + MetaDrill.getDrillId());
        this.expertInformationSystemExtractionHandler.updateCountAndName(procedure);
        return null;
    }
}
