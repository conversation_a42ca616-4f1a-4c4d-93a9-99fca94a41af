package com.daliangang.datascreen.riskboard.entity;

import lombok.Data;
import xyz.erupt.annotation.Erupt;
import xyz.erupt.annotation.EruptField;
import xyz.erupt.annotation.sub_field.Edit;
import xyz.erupt.annotation.sub_field.View;
import xyz.erupt.upms.model.auth.DataAuthModel;

import javax.persistence.Entity;
import javax.persistence.Table;

/**
 * @Author: ch<PERSON><PERSON><PERSON>n
 * @Date: 2024/11/13 16:14
 */
@Erupt(name = "风险类型数量")
@Entity
@Data
@Table(name = "tb_data_risk_board_num")
public class RiskBoardNum extends DataAuthModel {
    @EruptField(
            views = @View(title = "账号"),
            edit = @Edit(title = "账号")
    )
    private String account;

    @EruptField(
            views = @View(title = "用户名"),
            edit = @Edit(title = "用户名")
    )
    private String name;

    @EruptField(
            views = @View(title = "高风险数量"),
            edit = @Edit(title = "高风险数量")
    )
    private Integer highRisk;

    @EruptField(
            views = @View(title = "中风险数量"),
            edit = @Edit(title = "中风险数量")
    )
    private Integer middleRisk;

    @EruptField(
            views = @View(title = "低风险数量"),
            edit = @Edit(title = "低风险数量")
    )
    private Integer lowRisk;

    @EruptField(
            views = @View(title = "企业总数"),
            edit = @Edit(title = "企业总数")
    )
    private Integer total;

    @EruptField(
            views = @View(title = "企业名单"),
            edit = @Edit(title = "企业名单")
    )
    private String riskForm;
}
