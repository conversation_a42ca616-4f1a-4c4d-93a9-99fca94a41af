/*
 * Copyright © 2020-2035 yundingyun All rights reserved.
 */
package com.daliangang.emergency.entity;

import com.daliangang.core.CompanyRenderHandler;
import com.daliangang.emergency.proxy.RescueteamDataProxy;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import org.springframework.stereotype.Component;
import xyz.erupt.annotation.Erupt;
import xyz.erupt.annotation.EruptField;
import xyz.erupt.annotation.config.EruptSmartSkipSerialize;
import xyz.erupt.annotation.expr.ExprBool;
import xyz.erupt.annotation.sub_erupt.Power;
import xyz.erupt.annotation.sub_field.Edit;
import xyz.erupt.annotation.sub_field.EditType;
import xyz.erupt.annotation.sub_field.Readonly;
import xyz.erupt.annotation.sub_field.View;
import xyz.erupt.annotation.sub_field.sub_edit.ChoiceType;
import xyz.erupt.annotation.sub_field.sub_edit.InputType;
import xyz.erupt.annotation.sub_field.sub_edit.Search;
import xyz.erupt.toolkit.db.Comment;
import xyz.erupt.toolkit.handler.EruptDictCodeChoiceFetchHandler;
import xyz.erupt.toolkit.handler.RemoteEntityChoiceFetchHandler;
import xyz.erupt.toolkit.handler.SqlChoiceFetchHandler;
import xyz.erupt.upms.model.auth.DataAuthHandler;
import xyz.erupt.upms.model.auth.DataAuthModel;

import javax.persistence.Entity;
import javax.persistence.Lob;
import javax.persistence.Table;

@Erupt(name = "应急救援队伍管理", importTruncate = true, power = @Power(add = true, delete = true, export = false, importable = true, viewDetails = true, edit = true)
		, dataProxy = RescueteamDataProxy.class
		, rowOperation = {})
@Table(name = "tb_rescueteam")
@Entity
@Getter
@Setter
@Comment("应急救援队伍管理")
@ApiModel("应急救援队伍管理")
public class Rescueteam extends DataAuthModel {
	@EruptField(
			views = @View(title = "所属单位"),
			edit = @Edit(title = "所属单位",
					type = EditType.INPUT,
					notNull = true,search = @Search(vague = true),
					inputType = @InputType
			)
	)
	@Comment("所属单位")
	@ApiModelProperty("所属单位")
	private String ownerDepartment;

	@EruptField(
			views = @View(title = "队伍名称"),
			edit = @Edit(title = "队伍名称", type = EditType.INPUT, search = @Search(vague = true), notNull = true,
					inputType = @InputType))
	@Comment("队伍名称")
	@ApiModelProperty("队伍名称")
	private String teamName;

	@EruptField(
			views = {@View(
					title = "组织编码",
					show = false,
					ifRender = @ExprBool(
							exprHandler = DataAuthHandler.class
					)
			)},
			edit = @Edit(
					title = "组织编码",
					show = false
			)
	)
	@EruptSmartSkipSerialize
	private String orgCode;



//	@EruptField(
//			views = @View(title = "企业名称"),
//			edit = @Edit(title = "企业名称",
//					readonly = @Readonly(exprHandler = DataAuthHandler.class),
//					type = EditType.CHOICE, notNull = true,
//					search = @Search,
//					choiceType = @ChoiceType(fetchHandler = RemoteEntityChoiceFetchHandler.class, fetchHandlerParams ={"main","Enterprise","org_code,name"})))
//	@Comment("企业名称")
//	@ApiModelProperty("企业名称")
//	private String company;
//	@EruptField(
//			views = @View(title = "填报单位"),
//			edit = @Edit(title = "填报单位",
//					search = @Search(vague = true),
//					readonly = @Readonly(),
//					type = EditType.INPUT, notNull = true
//			))
//	@Comment("填报单位")
//	@ApiModelProperty("填报单位")
//	private String company;

	@EruptField(
			views = @View(title = "填报单位"),
			edit = @Edit(title = "填报单位",
					search = @Search(ifRender = @ExprBool(exprHandler = CompanyRenderHandler.class)),
					type = EditType.CHOICE, notNull = true, readonly = @Readonly(exprHandler = ReceiverReadOnlyHandler.class),
					choiceType = @ChoiceType(fetchHandler = RemoteEntityChoiceFetchHandler.class, fetchHandlerParams = {"main", "EruptOrg", "code,name"})))
	@Comment("填报单位")
	@ApiModelProperty("填报单位")
	private String company;

//	@EruptField(
//			views = @View(title = "所属港区", ifRender = @ExprBool(exprHandler = DepartmentViewRenderHandler.class)),
//			edit = @Edit(title = "所属港区", type = EditType.CHOICE, search = @Search(vague = true), notNull = true,
//					readonly = @Readonly(exprHandler = DataAuthHandler.class),
//					choiceType = @ChoiceType(fetchHandler = RemoteEntityChoiceFetchHandler.class, fetchHandlerParams ={"device","PortArea","id,portarea_name"})))
//	@Comment("所属港区")
//	@ApiModelProperty("所属港区")
//	private String portArea;

	@EruptField(
			views = @View(title = "所属港区"),
			edit = @Edit(title = "所属港区", type = EditType.CHOICE,

					notNull = false,readonly = @Readonly(exprHandler = ReceiverReadOnlyHandler.class),
					search = @Search,
					choiceType = @ChoiceType(fetchHandler = SqlChoiceFetchHandler.class, fetchHandlerParams ={"select id,portarea_name from tb_port_area"})))
	@Comment("所属港区")
	@ApiModelProperty("所属港区")
	private String portArea;

	@EruptField(
			views = @View(title = "地址"),
			edit = @Edit(title = "地址", type = EditType.INPUT, notNull = true,
					inputType = @InputType))
	@Comment("地址")
	@ApiModelProperty("地址")
	private String address;

	@EruptField(
			views = @View(title = "队伍级别"),
			edit = @Edit(title = "队伍级别", type = EditType.CHOICE,search = @Search,
	                choiceType = @ChoiceType(fetchHandler = EruptDictCodeChoiceFetchHandler.class, fetchHandlerParams = "teamLevel")))
	@Comment("队伍级别")
	@ApiModelProperty("队伍级别")
	private String teamLevel;

	@EruptField(
			views = @View(title = "人员规模"),
			edit = @Edit(title = "人员规模", type = EditType.INPUT, notNull = true,
					inputType = @InputType))
	@Comment("人员规模")
	@ApiModelProperty("人员规模")
	private String personnelSize;

	@EruptField(
			views = @View(title = "装备情况"),
			edit = @Edit(title = "装备情况", type = EditType.INPUT, notNull = true,
					inputType = @InputType))
	@Comment("装备情况")
	@ApiModelProperty("装备情况")
	private String equipmentCondition;

	@EruptField(
			views = @View(title = "负责人"),
			edit = @Edit(title = "负责人", type = EditType.INPUT, notNull = true,
					inputType = @InputType))
	@Comment("负责人")
	@ApiModelProperty("负责人")
	private String personInCharge;

	@EruptField(
			views = @View(title = "值守电话"),
			edit = @Edit(title = "值守电话", type = EditType.INPUT, notNull = true,
					inputType = @InputType))
	@Comment("值守电话")
	@ApiModelProperty("值守电话")
	private String contactNumber;

	@EruptField(
			views = @View(title = "技术专长描述", show = false),
			edit = @Edit(title = "技术专长描述", type = EditType.TEXTAREA, notNull = true))
	@Comment("技术专长描述")
	@ApiModelProperty("技术专长描述")
	private @Lob String expertiseDescription;


	@EruptField(
			views = @View(title = "地图", show = false),
			edit = @Edit(title = "地图", type = EditType.MAP, show = true))
	@Comment("地图")
	@ApiModelProperty("地图")
	@Lob private String map;



	@Component
	public static class ReceiverReadOnlyHandler implements Readonly.ReadonlyHandler {

		@Override
		public boolean add(boolean add, String[] params) {
			return true;
//		 return !DaliangangContext.isDepartmentUser();
		}

		@Override
		public boolean edit(boolean edit, String[] params) {
			return add(edit, params);
		}
	}

}
