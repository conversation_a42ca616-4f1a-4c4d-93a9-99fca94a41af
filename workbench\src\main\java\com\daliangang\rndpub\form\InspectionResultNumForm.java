package com.daliangang.rndpub.form;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import xyz.erupt.annotation.Erupt;
import xyz.erupt.annotation.EruptField;
import xyz.erupt.annotation.sub_erupt.Power;
import xyz.erupt.annotation.sub_field.Edit;
import xyz.erupt.annotation.sub_field.EditType;
import xyz.erupt.annotation.sub_field.Readonly;
import xyz.erupt.annotation.sub_field.View;
import xyz.erupt.annotation.sub_field.sub_edit.InputType;
import xyz.erupt.jpa.model.BaseModel;
import xyz.erupt.toolkit.db.Comment;

import javax.persistence.Entity;

/**
 * <AUTHOR>
 * @since :2023/4/25:9:58
 */
@Erupt(name = "统计", power = @Power(add = false, delete = false, export = false, importable = true, viewDetails = false, edit = false)
        , rowOperation = {})

@Getter
@Setter
@Comment("统计")
@ApiModel("统计")
public class InspectionResultNumForm extends BaseModel {
    @EruptField(
            views = @View(title = "数量",show = false),
            edit = @Edit(title = "数量", type = EditType.INPUT,readonly = @Readonly,
                    inputType = @InputType))
    @Comment("数量")
    @ApiModelProperty("数量")
    private String num;



    @EruptField(
            views = @View(title = "类型",show = false),
            edit = @Edit(title = "类型", type = EditType.INPUT,readonly = @Readonly,
                    inputType = @InputType))
    @Comment("类型")
    @ApiModelProperty("类型")
    private String type;

}
