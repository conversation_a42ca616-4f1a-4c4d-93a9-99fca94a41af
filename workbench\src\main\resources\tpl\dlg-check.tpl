<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <base href="/">
    <title>erupt</title>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
<link rel="stylesheet" href="./css/element-ui.css"/>
  <style>
	  .right{
		  float:right
	  }
	  .left{
		  float:left
	  }
	  .red{
		color:#f00 !important;
	  }
	  .green{
		color:green !important;
	  }
	  body{
		  background:#fff;
		  padding:10px;
	  }
	  .title{
		width:100%;
		font-size:20px;
		line-height:40px;
		font-weight:bold;
		margin-bottom:10px;
	  }
	  .detailFormPreview.horizontal .item {
	 width: 50%;
}
.detailFormPreview{
	width:100%;
	overflow:hidden;
}
 .detailFormPreview .content {
	 box-sizing: border-box;
}
 .detailFormPreview .item {
	 /* margin-bottom: 18px; */
	 display: inline-flex;
	 width: 100%;
	 line-height: 30px;
}
 .detailFormPreview .item.inline {
	 display: inline-block;
	 width: auto;
	 line-height: 20px;
}
 .detailFormPreview .item .label, .detailFormPreview .item .value {
	 display: inline-block;
	 font-size: 14px;
	 vertical-align: top;
	 box-sizing: border-box;
}
 .detailFormPreview .item .label {
	 color: #66635d;
	 text-align: right;
	 vertical-align: top;
}
 .detailFormPreview .item .value {
	 display: inline-block;
	 max-width: 60%;
	 color: #9e9e9e;
	 word-break: break-all;
}
 .detailFormPreview .item .value span i {
	 font-weight: bold;
	 font-size: 16px;
}
 .detailFormPreview .hidden {
	 display: none;
}

  </style>
</head>
<body>
<div id="app" v-loading.fullscreen.lock="fullscreenLoading">
	<div class="paperHeader" v-if="list.length>0">
		<div class="detailFormPreview clearfix"
		class="detailClass horizontal"  v-for="child in list">
			<div class="title">{{child.title}}</div>
			<div class="content">
				<div v-for="(item, index) in child.data"
					:key="index"
					:class="{
						inline: item.inline,
						item: !item.hide,
						hidden: item.hide,
					}"
					:style="{
						width: item.width||itemWidth ,
						marginRight: item.marginRight ,
					}">
					<i v-if="item.icon"
						:class="['iconfont', item.icon]"></i>
					<div v-if="!item.hide">
						{{ item.label }}
					</div>
					<div class="value"
						:class="[{ difference: item.difference },item.class]"
						:style="[
							{ width: item.valueWidth },
							{ 'max-width': item.maxWidth },
						]"
						v-if="!item.hide">
						<template v-if="item.render">
							<!-- <Render :render="item.render"></Render> -->
							{{getRender(item)}}
						</template>
						<template v-else-if="item.html">
							<div v-html="item.html"></div>
						</template>
						<template v-else-if="item.type=='file'">
							<el-link type="primary" @click="downFile(item)">{{getFileName(item.value)}}</el-link>
						</template>
						<template v-else>{{ item.value }}</template>
					</div>
				</div>
			</div>
			<el-divider v-if="child.divider"></el-divider>
	</div>
</div>
<script src="./js/vue.min.js"></script>
<script src="./js/element-ui.js"></script>
<script src="./js/axios.min.js"></script>
<script>
	let baseUrl=window.location.origin+'/erupt-api/';
	// let baseUrl="http://dlg-ali2.yundingyun.net:31080/erupt-api/"
	//let fileDomain="http://dlg-ali2.yundingyun.net:30900/daliangang"
	let fileDomain="https://**************:30057/daliangang"
    var vue = new Vue({
        el: '#app',
        data: function () {
            return {
				token:'',
                fullscreenLoading: true,
				itemWidth:'50%',
				list:[],
				data:{},
				id:null,

			}
        },
        created(){
			this.token=JSON.parse(window.localStorage.getItem('_token')).token||''
			const params = new URLSearchParams(window.location.search);
		this.id=params.get('ids')
            this.getList()
        },
        mounted: function () {
        },
        methods: {
			downFile(item){
				let name=this.getFileName(item.value)
				const link = document.createElement('a')
				link.href =fileDomain+  item.value
				link.target = '_blank'
				link.download = name
				link.click()
			},
			getFileName(path){
				if(path){
				let dirIdx = path.lastIndexOf("/") + 1;
				let codeIdx = path.lastIndexOf("-");
				let extIdx = path.lastIndexOf(".");
				let file = path.substring(codeIdx, path.length);
				let isDir = file.indexOf("/") > 0;
				if (isDir) {
					return path.substring(dirIdx, path.length);
				} else {
					let filename = path.substring(dirIdx, extIdx);
					let ext = path.substring(extIdx, path.length);
					return filename + ext;
				}
			}
			return ''
			},
			getRender(item){
				return item.render(item)
			},
			getList(page = 1) {
				this.list=[]
				// 检查结果
				axios({
					method:'get',
					url:baseUrl+'data/InspectionResults/'+this.id,
				headers:{
					Token:this.token,
					erupt:'InspectionResults'

				}
				})
					.then(response => {
						if (response.data) {
							this.fullscreenLoading = false;
							let data=response.data
							this.list.push( 
								{
									title:'检查结果',
									divider:true,
									data:[
										{
											label: '检查事项：',
											value: data.inspectionItems
										},
										{
											label: '问题描述：',
											value: data.problemDescription
										},
										{
											label: '检查依据：',
											value: data.inspectionBasis
										},
										{
											label: '现场照片：',
											value: data.inspectionBasisFile,
											type:'file',
										},
										{
											label: '整改意见：',
											value: data.proposal
										},
										{
											label: '整改截止时间：',
											value: data.deadline,
											render:(h,scope)=>{
												return h.value.split(" ")[0]
											}
										},
									]
								}
							)
							this.getGroupList()
						} 
					}).catch((err) => {
                })
			},
			getGroupList(){
				// 整改情况
				axios({
					method:'post',
					url:baseUrl+`data/table/Rectify`, 
					headers: {
						Token: this.token,
						erupt:'Rectify'
					},
					data:{
						pageIndex:1,
						pageSize:10000,
						condition:[
						{key:'inspectionResultsId',value:this.id}
						]
					}
					}).then((res) => {
						let data=res.data.list
						data.map(item=>{
							this.list.push({
								title:'整改情况',
								data:[
									{
										label:'整改时间：',
										value:item.rectificationTime,
										render:(h,scope)=>{
												return h.value.split(" ")[0]
											}
									},
									{
										label:'整改说明：',
										value:item.description
									},
									{
										label:'整改证明材料：',
										value:item.supportingMaterials,
										type:'file',
									},
								]
							})
							this.list.push({
								title:'复查情况',
								divider:true,
								data:[
									{
										label:'复查时间：',
										value:item.reviewDate,
										render:(h,scope)=>{
												return h.value.split(" ")[0]
											}
									},
									{
										label:'复查结果：',
										value:item.reviewResult,
										class:item.reviewResult=='通过'?'green':'red'
									},
									{
										label:'整改截止时间：',
										value:item.deadline,
										hide:item.reviewResult=='通过',
										render:(h,scope)=>{
												return h.value.split(" ")[0]
											}
									},
									{
										label:'意见：',
										value:item.remark
									},
								]
							})
						})
					})
			},
        }
	})
</script>

</body>
</html>