window.eruptSiteConfig = {
    //erupt接口地址，在前后端分离时指定
    domain: "",
    // domain: "http://dlg-hw.yundingyun.net:31080",
    //附件地址，一般情况下不需要指定，如果自定义对象存储空间，则需在此指定附件资源访问地址
    fileDomain: "http://116.204.97.7:30900/test",
    //回源地址，用于解决跨域问题
    sourceDomain: "",
    //后端服务转发地址
    routeDomains: {},
    //标题
    title: "大连港",
    //描述
    desc: "危险货物港区重大安全风险管控平台门户",
    //是否展示版权信息
    copyright: false,
    //高德地图api key,使用地图组件须指定此属性，amapKey获取地址：https://lbs.amap.com (服务平台为：Web端(JS API))
    amapKey: "f8fbb703b56992cc847a9fe3f5e24618",
    //logo路径
    logoPath: "",
    //logo文字
    logoText: "危险货物港区重大安全风险管控平台",
    //右侧文字
    rightTopText: "",
    //登录前主页
    homePage: "",
    //注册页地址(仅是一个链接，需要自定义实际样式)
    registerPage: "",
    //忘记密码地址(仅是一个链接，需要自定义实际样式)
    forgotPwdPage: "",
    //自定义导航栏按钮，配置后将会出现在页面右上角
    r_tools: [],
    tableLink: false, //表格字段查看详情
    //登录成功事件
    login: function (user) {

    },
    //注销事件
    logout: function (user) {

    }
};

//路由回调函数
window.eruptRouterEvent = {
    //key表示要监听的路由切换地址，为url hash地址最后一段
    //例如：http://www.erupt.xyz:9999/#/build/table/demo中demo为回调key
    demo: {
        //路由载入事件
        load: function (e) {

        },
        //路由退出事件
        unload: function (e) {

        }
    },
    //$ 为全路径通配符，在任何路由切换时都会执行load与unload事件
    $: {
        load: function (e) {

        },
        unload: function (e) {
        }
    }
};

//erupt生命周期函数
window.eruptEvent = {
    //页面加载完成后回调
    startup: function () {

    }
}
