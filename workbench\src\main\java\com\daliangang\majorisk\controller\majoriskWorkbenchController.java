package com.daliangang.majorisk.controller;

import com.daliangang.core.DaliangangContext;
import com.daliangang.majorisk.form.CurrentJobForm;
import com.daliangang.majorisk.form.WorkYjForm;
import com.daliangang.majorisk.form.WorksNumForm;
import com.daliangang.majorisk.sql.MajoriskWorkbenchSql;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import xyz.erupt.core.annotation.EruptRouter;
import xyz.erupt.core.module.MetaUserinfo;
import xyz.erupt.core.util.EruptSpringUtil;
import xyz.erupt.core.view.EruptApiModel;
import xyz.erupt.jpa.dao.EruptDao;
import xyz.erupt.toolkit.remote.RemoteProxyService;
import xyz.erupt.toolkit.utils.EruptDaoUtils;
import xyz.erupt.toolkit.utils.SqlUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since :2023/4/21:16:22
 */
@RestController
public class majoriskWorkbenchController {

    @Resource
    private EruptDao eruptDao;
    @Resource
    private RemoteProxyService remoteProxyService;

    @Resource
    private MajoriskWorkbenchSql majoriskWorkbenchSql;

    // 统计装卸船作业、装卸汽车/火车作业之和
    @RequestMapping("erupt-api/get/selectUnloadWork")
    @EruptRouter(verifyType = EruptRouter.VerifyType.LOGIN)
    public EruptApiModel selectUnloadWork() {
        MetaUserinfo remoteUserInfo = remoteProxyService.getRemoteUserInfo();
//        List<LinkedTreeMap> linkedTreeMaps = remoteProxyService.queryEntity("Enterprise", RemoteQuery.builder().condition("orgCode", remoteUserInfo.getOrg()));
//        String isEnterprise = "1";
//        if (ObjectUtils.isEmpty(linkedTreeMaps)) {  // 不存在则是政府账号
//            isEnterprise = "0";
//        }
        String orgCode = SqlUtils.wrapIn(remoteUserInfo.getAuth());
        String sql = majoriskWorkbenchSql.selectWork(orgCode);
        CurrentJobForm currentJobForm = EruptDaoUtils.selectOne(sql, CurrentJobForm.class);
        return EruptApiModel.successApi(currentJobForm);
    }

    // 统计检维修作业之和
    @RequestMapping("erupt-api/get/selectMaintenanceWork")
    @EruptRouter(verifyType = EruptRouter.VerifyType.LOGIN)
    public EruptApiModel selectMaintenanceWork() {
        MetaUserinfo remoteUserInfo = remoteProxyService.getRemoteUserInfo();
//        List<LinkedTreeMap> linkedTreeMaps = remoteProxyService.queryEntity("Enterprise", RemoteQuery.builder().condition("orgCode", remoteUserInfo.getOrg()));
//        String isEnterprise = "1";
//        if (ObjectUtils.isEmpty(linkedTreeMaps)) {  // 不存在则是政府账号
//            isEnterprise = "0";
//        }
        String orgCode = SqlUtils.wrapIn(remoteUserInfo.getAuth());

        String sql = majoriskWorkbenchSql.selectMaintenanceWork(orgCode);
        CurrentJobForm currentJobForm = EruptDaoUtils.selectOne(sql, CurrentJobForm.class);
        return EruptApiModel.successApi(currentJobForm);
    }

    // 应急大屏作业数据显示
    @RequestMapping("erupt-api/get/selectYjWorks/{orgCode}")
    @EruptRouter(verifyType = EruptRouter.VerifyType.LOGIN)
    public EruptApiModel selectYjWorks(@PathVariable("orgCode") String orgCode) {
        List<WorkYjForm> listAddress = new ArrayList();
        //装卸车
        // 根据code查询作业关联的地点id集合
        List<WorkYjForm> workYjForm = EruptDaoUtils.selectOnes("select tu.id,tu.name , (CASE when tu.work_type= 'CAR' then '装卸汽车作业' when tu.work_type= 'TRAIN' then '装卸火车作业' end) as type,tu.company from tb_unload tu where (NOW() between ab_time and ae_time) or (now() < ab_time ) and tu.org_code ='"+orgCode+"'", WorkYjForm.class);
        if(ObjectUtils.isNotEmpty(workYjForm)) {
            workYjForm.forEach(v -> {
                List listAddress2 = new ArrayList();
                // 泊位
                List<WorkYjForm.AddressInfo> workYjFormList1 = EruptDaoUtils.selectOnes("select map,'BW' as addressType from tb_unload_berths tub left join tb_berth tb on tub.berths_id = tb.id where tub.unload_id =" + v.getId(), WorkYjForm.AddressInfo.class);
                if (ObjectUtils.isNotEmpty(workYjFormList1)) {
                    List list = new ArrayList();
                    workYjFormList1.forEach(m -> {
                        list.add(m);
                    });
                    listAddress2.addAll(list); // 保存泊位作业地点
                }

                // 堆场
                List<WorkYjForm.AddressInfo> workYjFormList2 = EruptDaoUtils.selectOnes("select map,'DC' as addressType from tb_unload_yards tub left join tb_yard tb on tub.yards_id = tb.id where tub.unload_id =" + v.getId(), WorkYjForm.AddressInfo.class);
                if (ObjectUtils.isNotEmpty(workYjFormList2)) {
                    List list = new ArrayList();
                    workYjFormList2.forEach(m -> {
                        list.add(m);
                    });
                    listAddress2.addAll(list); // 保存泊位作业地点
                }

                // 仓库
                List<WorkYjForm.AddressInfo> workYjFormList3 = EruptDaoUtils.selectOnes("select map,'CK' as addressType from tb_unload_wares tub left join tb_warehouse tb on tub.wares_id = tb.id where tub.unload_id =" + v.getId(), WorkYjForm.AddressInfo.class);
                if (ObjectUtils.isNotEmpty(workYjFormList3)) {
                    List list = new ArrayList();
                    workYjFormList3.forEach(m -> {
                        list.add(m);
                    });
                    listAddress2.addAll(list); // 保存泊位作业地点
                }

                // 栈台
                List<WorkYjForm.AddressInfo> workYjFormList4 = EruptDaoUtils.selectOnes(" select map,'ZT' as addressType from tb_unload_wharfs tub left join tb_loading_dock tb on tub.wharfs_id = tb.id where tub.unload_id =" + v.getId(), WorkYjForm.AddressInfo.class);
                if (ObjectUtils.isNotEmpty(workYjFormList4)) {
                    List list = new ArrayList();
                    workYjFormList4.forEach(m -> {
                        list.add(m);
                    });
                    listAddress2.addAll(list); // 保存泊位作业地点
                }

                // 储罐
                List<WorkYjForm.AddressInfo> workYjFormList5 = EruptDaoUtils.selectOnes("select map,'CG' as addressType from tb_unload_tanks tub left join tb_storage_tank tb on tub.tanks_id = tb.id where tub.unload_id = " + v.getId(), WorkYjForm.AddressInfo.class);
                if (ObjectUtils.isNotEmpty(workYjFormList5)) {
                    List list = new ArrayList();
                    workYjFormList5.forEach(m -> {
                        list.add(m);
                    });
                    listAddress2.addAll(list); // 保存泊位作业地点
                }
                v.setAddress(listAddress2);
            });
            listAddress.addAll(workYjForm);  // 保存作业数据
        }
        // 装卸船
            List<WorkYjForm> workYjForm1 = EruptDaoUtils.selectOnes("select  id, name , (CASE when work_type= 'ship' then '装卸船作业'  end) as type,company  from tb_unload_ship tus where (NOW() between ab_time and ae_time) or (now() < ab_time ) and tus.org_code ='"+orgCode+"'", WorkYjForm.class);
            if(ObjectUtils.isNotEmpty(workYjForm1)) {
                workYjForm1.forEach(v->{
                    List listAddress2 = new ArrayList();
                    // 泊位
                    List<WorkYjForm.AddressInfo> workYjFormListShip1 = EruptDaoUtils.selectOnes("select map,'BW' as addressType from tb_unload_ship_berths tub left join tb_berth tb on tub.berths_id = tb.id where tub.unload_id ="+v.getId(), WorkYjForm.AddressInfo.class);
                    if (ObjectUtils.isNotEmpty(workYjFormListShip1)) {
                        List list = new ArrayList();
                        workYjFormListShip1.forEach(m->{
                            list.add(m);
                        });
                        listAddress2.addAll(list); // 保存泊位作业地点
                    }

                    // 堆场
                    List<WorkYjForm.AddressInfo> workYjFormListShip2 = EruptDaoUtils.selectOnes("select map,'DC' as addressType from tb_unload_ship_yards tub left join tb_yard tb on tub.yards_id = tb.id where tub.unload_id ="+v.getId(), WorkYjForm.AddressInfo.class);
                    if (ObjectUtils.isNotEmpty(workYjFormListShip2)) {
                        List list = new ArrayList();
                        workYjFormListShip2.forEach(m->{
                            list.add(m);
                        });
                        listAddress2.addAll(list); // 保存泊位作业地点
                    }

                    // 仓库
                    List<WorkYjForm.AddressInfo> workYjFormListShip3 = EruptDaoUtils.selectOnes("select map,'CK' as addressType from tb_unload_ship_wares tub left join tb_warehouse tb on tub.wares_id = tb.id where tub.unload_id ="+v.getId(), WorkYjForm.AddressInfo.class);
                    if (ObjectUtils.isNotEmpty(workYjFormListShip3)) {
                        List list = new ArrayList();
                        workYjFormListShip3.forEach(m->{
                            list.add(m);
                        });
                        listAddress2.addAll(list); // 保存泊位作业地点
                    }

                    // 栈台
                    List<WorkYjForm.AddressInfo> workYjFormListShip4 = EruptDaoUtils.selectOnes(" select map,'ZT' as addressType from tb_unload_ship_wharfs tub left join tb_loading_dock tb on tub.wharfs_id = tb.id where tub.unload_id ="+v.getId(), WorkYjForm.AddressInfo.class);
                    if (ObjectUtils.isNotEmpty(workYjFormListShip4)) {
                        List list = new ArrayList();
                        workYjFormListShip4.forEach(m->{
                            list.add(m);
                        });
                        listAddress2.addAll(list); // 保存泊位作业地点
                    }

                    // 储罐
                    List<WorkYjForm.AddressInfo> workYjFormListShip5 = EruptDaoUtils.selectOnes("select map,'CG' as addressType from tb_unload_ship_tanks tub left join tb_storage_tank tb on tub.tanks_id = tb.id where tub.unload_id = "+v.getId(), WorkYjForm.AddressInfo.class);
                    if (ObjectUtils.isNotEmpty(workYjFormListShip5)) {
                        List list = new ArrayList();
                        workYjFormListShip5.forEach(m->{
                            list.add(m);
                        });
                        listAddress2.addAll(list); // 保存泊位作业地点
                    }
                    v.setAddress(listAddress2);
                });
                listAddress.addAll(workYjForm1);  // 保存作业数据
            }

            // 检维修
        List<WorkYjForm> workYjForm2 = EruptDaoUtils.selectOnes("select  id, work_name as name , (CASE when type= 'DH' then '动火作业' when type= 'SX' then '受限空间作业'  end) as type,company,address as map from tb_maintenance tm where state in ('NOT_STARTED','STARTED') and tm.org_code ='"+orgCode+"'", WorkYjForm.class);
         if (ObjectUtils.isNotEmpty(workYjForm2)) {
             listAddress.addAll(workYjForm2);
         }

         if (ObjectUtils.isNotEmpty(listAddress)) {
             listAddress.forEach(v->{
                 v.setCompanyName(DaliangangContext.getEnterpriseName(v.getCompany()));
             });
         }

        return EruptApiModel.successApi(listAddress);
    }


    // 第二大屏 作业数据
    @RequestMapping("erupt-api/get/selectWorksNum/{workType}")
    @EruptRouter(verifyType = EruptRouter.VerifyType.LOGIN)
    public EruptApiModel selectWorksNum(@PathVariable("workType") String workType) {
        MetaUserinfo remoteUserInfo = remoteProxyService.getRemoteUserInfo();
        String orgCode = SqlUtils.wrapIn(remoteUserInfo.getAuth());
        String sql = majoriskWorkbenchSql.selectWorksNum(orgCode);
        List<WorksNumForm> currentJobForm = EruptDaoUtils.selectOnes(sql, WorksNumForm.class);
        if (ObjectUtils.isNotEmpty(currentJobForm)) {
            currentJobForm.forEach(v->{
                v.setCompanyName(DaliangangContext.getEnterpriseName(v.getCompany()));
            });
        }

        List<WorksNumForm> list = new ArrayList<>();
        if (!workType.equals("all")) {
            List<WorksNumForm> collect = currentJobForm.stream().filter(it -> workType.contains(it.getType())).collect(Collectors.toList());
            list.addAll(collect);
            return EruptApiModel.successApi(list);
        }

        return EruptApiModel.successApi(currentJobForm);
    }


    // 第二大屏作业详情数据
    @RequestMapping("erupt-api/get/selectWorksNumTwoInfo/{id}/{type}")
    @EruptRouter(verifyType = EruptRouter.VerifyType.LOGIN)
    public EruptApiModel selectWorksNumTwoInfo(@PathVariable("id") Long id,@PathVariable("type") String type) {
       // 判断作业类型
        if (type.equals("装卸汽车") || type.equals("装卸火车")) {
            WorkYjForm workYjForm = EruptDaoUtils.selectOne("select tu.id, tu.name , (CASE when tu.work_type= 'CAR' then '装卸汽车' when tu.work_type= 'TRAIN' then '装卸火车' end) as type,tu.company ,tu.ab_time as starTime,tu.ae_time as endTime,(CASE when (now() < ab_time ) then '未开始' when (NOW() between ab_time and ae_time) then '进行中' end) as state,tu.goods_name as goodsName,trde.risk_name as riskName from tb_unload tu left join tb_risk_database_enterprise trde on tu.risk_database = trde.id where tu.id ="+id, WorkYjForm.class);
            if(ObjectUtils.isNotEmpty(workYjForm)) {
                    List listAddress2 = new ArrayList();
                    // 泊位
                    List<WorkYjForm.AddressInfo> workYjFormList1 = EruptDaoUtils.selectOnes("select map,'BW' as addressType from tb_unload_berths tub left join tb_berth tb on tub.berths_id = tb.id where tub.unload_id =" + workYjForm.getId(), WorkYjForm.AddressInfo.class);
                    if (ObjectUtils.isNotEmpty(workYjFormList1)) {
                        List list = new ArrayList();
                        workYjFormList1.forEach(m -> {
                            list.add(m);
                        });
                        listAddress2.addAll(list); // 保存泊位作业地点
                    }

                    // 堆场
                    List<WorkYjForm.AddressInfo> workYjFormList2 = EruptDaoUtils.selectOnes("select map,'DC' as addressType from tb_unload_yards tub left join tb_yard tb on tub.yards_id = tb.id where tub.unload_id =" + workYjForm.getId(), WorkYjForm.AddressInfo.class);
                    if (ObjectUtils.isNotEmpty(workYjFormList2)) {
                        List list = new ArrayList();
                        workYjFormList2.forEach(m -> {
                            list.add(m);
                        });
                        listAddress2.addAll(list); // 保存泊位作业地点
                    }

                    // 仓库
                    List<WorkYjForm.AddressInfo> workYjFormList3 = EruptDaoUtils.selectOnes("select map,'CK' as addressType from tb_unload_wares tub left join tb_warehouse tb on tub.wares_id = tb.id where tub.unload_id =" + workYjForm.getId(), WorkYjForm.AddressInfo.class);
                    if (ObjectUtils.isNotEmpty(workYjFormList3)) {
                        List list = new ArrayList();
                        workYjFormList3.forEach(m -> {
                            list.add(m);
                        });
                        listAddress2.addAll(list); // 保存泊位作业地点
                    }

                    // 栈台
                    List<WorkYjForm.AddressInfo> workYjFormList4 = EruptDaoUtils.selectOnes(" select map,'ZT' as addressType from tb_unload_wharfs tub left join tb_loading_dock tb on tub.wharfs_id = tb.id where tub.unload_id =" + workYjForm.getId(), WorkYjForm.AddressInfo.class);
                    if (ObjectUtils.isNotEmpty(workYjFormList4)) {
                        List list = new ArrayList();
                        workYjFormList4.forEach(m -> {
                            list.add(m);
                        });
                        listAddress2.addAll(list); // 保存泊位作业地点
                    }

                    // 储罐
                    List<WorkYjForm.AddressInfo> workYjFormList5 = EruptDaoUtils.selectOnes("select map,'CG' as addressType from tb_unload_tanks tub left join tb_storage_tank tb on tub.tanks_id = tb.id where tub.unload_id = " + workYjForm.getId(), WorkYjForm.AddressInfo.class);
                    if (ObjectUtils.isNotEmpty(workYjFormList5)) {
                        List list = new ArrayList();
                        workYjFormList5.forEach(m -> {
                            list.add(m);
                        });
                        listAddress2.addAll(list); // 保存泊位作业地点
                    }

                workYjForm.setAddress(listAddress2);// 保存作业数据
                workYjForm.setCompanyName(DaliangangContext.getEnterpriseName(workYjForm.getCompany()));
            }
            return EruptApiModel.successApi(workYjForm);

        } else if (type.equals("装卸船")) {
            WorkYjForm workYjForm = EruptDaoUtils.selectOne("select tu.id, tu.name , (CASE when tu.work_type= 'ship' then '装卸船'  end) as type,tu.company ,tu.ab_time as starTime,tu.ae_time as endTime,(CASE when (now() < ab_time ) then '未开始' when (NOW() between ab_time and ae_time) then '进行中' end) as state,tu.goods_name as goodsName,trde.risk_name as riskName from tb_unload_ship tu left join tb_risk_database_enterprise trde on tu.risk_database = trde.id where tu.id ="+id, WorkYjForm.class);
            if(ObjectUtils.isNotEmpty(workYjForm)) {
                List listAddress2 = new ArrayList();
                // 泊位
                List<WorkYjForm.AddressInfo> workYjFormList1 = EruptDaoUtils.selectOnes("select map,'BW' as addressType from tb_unload_berths tub left join tb_berth tb on tub.berths_id = tb.id where tub.unload_id =" + workYjForm.getId(), WorkYjForm.AddressInfo.class);
                if (ObjectUtils.isNotEmpty(workYjFormList1)) {
                    List list = new ArrayList();
                    workYjFormList1.forEach(m -> {
                        list.add(m);
                    });
                    listAddress2.addAll(list); // 保存泊位作业地点
                }

                // 堆场
                List<WorkYjForm.AddressInfo> workYjFormList2 = EruptDaoUtils.selectOnes("select map,'DC' as addressType from tb_unload_yards tub left join tb_yard tb on tub.yards_id = tb.id where tub.unload_id =" + workYjForm.getId(), WorkYjForm.AddressInfo.class);
                if (ObjectUtils.isNotEmpty(workYjFormList2)) {
                    List list = new ArrayList();
                    workYjFormList2.forEach(m -> {
                        list.add(m);
                    });
                    listAddress2.addAll(list); // 保存泊位作业地点
                }

                // 仓库
                List<WorkYjForm.AddressInfo> workYjFormList3 = EruptDaoUtils.selectOnes("select map,'CK' as addressType from tb_unload_wares tub left join tb_warehouse tb on tub.wares_id = tb.id where tub.unload_id =" + workYjForm.getId(), WorkYjForm.AddressInfo.class);
                if (ObjectUtils.isNotEmpty(workYjFormList3)) {
                    List list = new ArrayList();
                    workYjFormList3.forEach(m -> {
                        list.add(m);
                    });
                    listAddress2.addAll(list); // 保存泊位作业地点
                }

                // 栈台
                List<WorkYjForm.AddressInfo> workYjFormList4 = EruptDaoUtils.selectOnes(" select map,'ZT' as addressType from tb_unload_wharfs tub left join tb_loading_dock tb on tub.wharfs_id = tb.id where tub.unload_id =" + workYjForm.getId(), WorkYjForm.AddressInfo.class);
                if (ObjectUtils.isNotEmpty(workYjFormList4)) {
                    List list = new ArrayList();
                    workYjFormList4.forEach(m -> {
                        list.add(m);
                    });
                    listAddress2.addAll(list); // 保存泊位作业地点
                }

                // 储罐
                List<WorkYjForm.AddressInfo> workYjFormList5 = EruptDaoUtils.selectOnes("select map,'CG' as addressType from tb_unload_tanks tub left join tb_storage_tank tb on tub.tanks_id = tb.id where tub.unload_id = " + workYjForm.getId(), WorkYjForm.AddressInfo.class);
                if (ObjectUtils.isNotEmpty(workYjFormList5)) {
                    List list = new ArrayList();
                    workYjFormList5.forEach(m -> {
                        list.add(m);
                    });
                    listAddress2.addAll(list); // 保存泊位作业地点
                }
                workYjForm.setAddress(listAddress2);// 保存作业数据
                workYjForm.setCompanyName(DaliangangContext.getEnterpriseName(workYjForm.getCompany()));

            }
            return EruptApiModel.successApi(workYjForm);

        } else {
            WorkYjForm workYjForm = EruptDaoUtils.selectOne("select  id, work_name as name , (CASE when type= 'DH' then '动火作业' when type= 'SX' then '受限空间作业'  end) as type,company ,star_date as starTime,end_date as endTime,(CASE when state= 'NOT_STARTED' then '未开始' when state= 'STARTED' then '进行中' end) as state,address as map from tb_maintenance tm where tm.id ="+id, WorkYjForm.class);
            if (ObjectUtils.isNotEmpty(workYjForm)) {
                List list = new ArrayList();
                WorkYjForm.AddressInfo addressInfo = EruptSpringUtil.getBean(WorkYjForm.AddressInfo.class);
                addressInfo.setAddressType("JWX");
                addressInfo.setMap(workYjForm.getMap());
                list.add(addressInfo);
                workYjForm.setAddress(list);
                workYjForm.setCompanyName(DaliangangContext.getEnterpriseName(workYjForm.getCompany()));
            }
            return EruptApiModel.successApi(workYjForm);
        }

    }

    // 获取当前人orgcode
    @RequestMapping("erupt-api/get/selectOrgCodeInfo")
    @EruptRouter(verifyType = EruptRouter.VerifyType.LOGIN)
    public EruptApiModel selectOrgCodeInfo() {
        MetaUserinfo remoteUserInfo = remoteProxyService.getRemoteUserInfo();

        return EruptApiModel.successApi(remoteUserInfo.getOrg());
    }


    // 判断当前登录用户是否是企业用户
    @GetMapping("erupt-api/get/isDepartmentUser")
    @EruptRouter(verifyType = EruptRouter.VerifyType.LOGIN)
    public EruptApiModel isDepartmentUser() {
        boolean departmentUser = DaliangangContext.isDepartmentUser();
        return EruptApiModel.successApi(departmentUser);
    }

}

