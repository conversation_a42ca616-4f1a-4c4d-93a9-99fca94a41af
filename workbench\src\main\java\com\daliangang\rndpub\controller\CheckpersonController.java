package com.daliangang.rndpub.controller;

import org.springframework.web.bind.annotation.RestController;

/**
 * @Author: zeng<PERSON><PERSON>
 * @Description:
 * @Date: Created in 2023/4/6 13:45
 * @Modified By
 */
@RestController
public class CheckpersonController {

//    @Resource
//    private RemoteProxyService remoteProxyService;
//
//    @Resource
//    private EruptDao eruptDao;
//
//    /**
//     * 查询检查人员
//     * @param
//     * @return
//     */
//    @PostMapping("/erupt-api/data/table/Checkperson")
//    @EruptRouter(verifyType = EruptRouter.VerifyType.LOGIN)
//    public Page getCheckperson(@RequestBody TableQueryVo tableQueryVo){
//        List<Condition> conditions = tableQueryVo.getCondition();
//        if(null == conditions){
//            conditions = new ArrayList<>();
//        }
//        Condition condition = new Condition("checkPerson",true);
//        conditions.add(condition);
//        return remoteProxyService.getEruptData("main","EmployeeInformation",
//                tableQueryVo.getPageIndex(),
//                tableQueryVo.getPageSize(),
//                tableQueryVo.getSort(),
//                conditions.toArray(new Condition[conditions.size()]));
//
//
//    }
//
//    @RequestMapping("/erupt-api/data/Checkperson/{id}")
//    @EruptRouter(verifyType = EruptRouter.VerifyType.LOGIN)
//    @SneakyThrows
//    public JsonObject getCheckpersonById(@PathVariable(name = "id") Long id) {
//        JsonObject checkperson = remoteProxyService.getEruptDataById("main", "EmployeeInformation", id, JsonObject.class);
//        checkperson.add("name",checkperson.get("id"));
//        checkperson.addProperty("isEdit",true);
//        return checkperson ;
//    }
//
//
//    /**
//     * 新增检查人员--实际上是同步到人员管理模块中，是修改
//     * @param data
//     * @return
//     */
//    @PostMapping("erupt-api/data/modify/Checkperson")
//    @EruptRouter(verifyType = EruptRouter.VerifyType.LOGIN)
//    public EruptApiModel addCheckperson( HttpServletRequest request,@RequestBody JsonObject data){
//        return modifyCheckperson(request,data);
//    }
//
//    /**
//     * 修改检查人员--实际上是同步到人员管理模块中，是修改
//     * @param data
//     * @return
//     */
//    @PutMapping("erupt-api/data/modify/Checkperson")
//    @EruptRouter(verifyType = EruptRouter.VerifyType.LOGIN)
//    public EruptApiModel updateCheckperson( HttpServletRequest request,@RequestBody JsonObject data){
//        return modifyCheckperson(request,data);
//    }
//
//
//    private EruptApiModel modifyCheckperson(HttpServletRequest request, JsonObject data){
//        data.addProperty("id",data.get("name").getAsNumber().longValue());
//        data.remove("name");
//        data.addProperty("checkPerson",true);
//        //todo
//        return EruptRemoteUtils.doPost(remoteProxyService.getMainServerUrl()+"/erupt-api/employeeInformation/updateEmployeeInformation",data,EruptApiModel.class);
//    }
//
//    /**
//     * 删除检查人员--实际上是同步到人员管理模块中，是修改
//     * @param id
//     * @return
//     */
//    @DeleteMapping("erupt-api/data/modify/Checkperson/{id}")
//    @EruptRouter(verifyType = EruptRouter.VerifyType.LOGIN)
//    public EruptApiModel delCheckperson( @PathVariable("id") Long id){
//        JsonObject data = new JsonObject();
//        data.addProperty("id",id);
//        data.addProperty("checkPerson",false);
//
//        //todo
//        return EruptRemoteUtils.doPost(remoteProxyService.getMainServerUrl()+"/erupt-api/employeeInformation/updateEmployeeInformation",data,EruptApiModel.class);
//    }


}
