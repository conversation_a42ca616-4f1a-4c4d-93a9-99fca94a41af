/*
 * Copyright © 2020-2035 yundingyun All rights reserved.
 */
package com.daliangang.device.proxy;

import cn.hutool.json.JSONObject;
import com.daliangang.device.entity.Wharf;
import com.google.gson.internal.LinkedTreeMap;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.stereotype.Service;
import xyz.erupt.annotation.fun.DataProxy;
import xyz.erupt.common.http.EruptRemoteUtils;
import xyz.erupt.toolkit.remote.RemoteProxyService;
import xyz.erupt.toolkit.remote.RemoteQuery;
import xyz.erupt.toolkit.utils.EruptDaoUtils;
import xyz.erupt.toolkit.utils.NotifyUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

@Service
public class WharfDataProxy implements DataProxy<Wharf> {
    @Resource
    private RemoteProxyService remoteProxyService;

    @Override
    public void addBehavior(Wharf wharf) {
        if (ObjectUtils.isNotEmpty(wharf.getCompany())) {
            List<LinkedTreeMap> enterprise = remoteProxyService.queryEntity("Enterprise", RemoteQuery.builder().condition("orgCode", wharf.getCompany()));
            if (ObjectUtils.isNotEmpty(enterprise)) {
                //  Integer portArea1 = (int) Float.parseFloat((String) enterprise.get(0).get("portArea"));
                //   String portArea = String.valueOf(enterprise.get(0).get("portArea"));
                String portArea=new BigDecimal(enterprise.get(0).get("portArea").toString()).stripTrailingZeros().toPlainString();
                wharf.setPortArea(portArea);
            }
        }
    }

    @Override
    public void excelImport(Object workbook) {
//        EruptDaoUtils.truncate(Wharf.class);
    }

    @Override
    public void beforeDelete(Wharf wharf) {
        String selectSql="select * from tb_berth where wharf="+wharf.getId();
        List<Wharf> wharves = EruptDaoUtils.selectOnes(selectSql, Wharf.class);
        if(wharves.size()>0){
            NotifyUtils.showErrorMsg("当前码头已有下级泊位信息，不可删除!");
        }

    }


    @Override
    public void afterAdd(Wharf wharf) {
        JSONObject inputData = new JSONObject();
        inputData.set("clazz", "Wharf");
        inputData.set("insertData",wharf);
        EruptRemoteUtils.doPost(remoteProxyService.getMainServerUrl() + "/erupt-api/exchange/data/exchange", inputData, Map.class);
    }

    @Override
    public void afterUpdate(Wharf wharf) {
        JSONObject inputData = new JSONObject();
        inputData.set("clazz", "Wharf");
        inputData.set("insertData",wharf);
        EruptRemoteUtils.doPost(remoteProxyService.getMainServerUrl() + "/erupt-api/exchange/data/exchange", inputData, Map.class);
    }
}
