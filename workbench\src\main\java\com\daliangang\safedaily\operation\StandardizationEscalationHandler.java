/*
* Copyright © 2020-2035 yundingyun All rights reserved.
*/
package com.daliangang.safedaily.operation;

import cn.hutool.json.JSONObject;
import com.daliangang.safedaily.entity.Standardization;
import com.daliangang.safedaily.entity.StandardizationScoreSecond;
import com.daliangang.safedaily.entity.StandardizationSenond;
import com.daliangang.safedaily.proxy.StandardizationSecondDataProxy;
import org.springframework.stereotype.Service;
import xyz.erupt.annotation.fun.OperationHandler;
import xyz.erupt.common.http.EruptRemoteUtils;
import xyz.erupt.core.util.EruptSpringUtil;
import xyz.erupt.jpa.dao.EruptDao;
import xyz.erupt.toolkit.remote.RemoteProxyService;
import xyz.erupt.toolkit.utils.NotifyUtils;

import javax.annotation.Resource;
import javax.transaction.Transactional;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Service
public class StandardizationEscalationHandler implements OperationHandler<Standardization, Void> {
    @Resource
    EruptDao eruptDao;
    @Resource
    private RemoteProxyService remoteProxyService;

    @Resource
    private StandardizationSecondDataProxy standardizationSecondDataProxy;

   @Override
   @Transactional
   public String exec(List<Standardization> data, Void unused, String[] param) {
       data.forEach(v->{
           //  v.setIsReport(true);
           v.setSubmitted(true);
         //  v.setIsReport(true);
           eruptDao.merge(v);

           // 同步第二年自评数据

           StandardizationSenond standardizationSenond = EruptSpringUtil.getBean(StandardizationSenond.class);

           int i = Integer.valueOf(v.getYear()).intValue();
           standardizationSenond.setYear(String.valueOf(i+1));
           standardizationSenond.setCompany(v.getCompany());
           standardizationSenond.setOrgCode(v.getOrgCode());
           standardizationSenond.setSubmitted(false);
           standardizationSenond.setGrade(v.getGrade());
           standardizationSenond.setIssueDate(v.getIssueDate());
           standardizationSenond.setEffectiveDate(v.getEffectiveDate());
           standardizationSenond.setStateStr(v.getStateStr());
           standardizationSenond.setStandardFile(v.getStandardFile());
           standardizationSenond.setSelfEvaluation(v.getSelfEvaluation());
           standardizationSenond.setOtherFile(v.getOtherFile());
         //  standardizationSenond.setIsReport(false);
           standardizationSenond.setFinalScore(v.getFinalScore());
           standardizationSenond.setGateScore(v.getGateScore());
           standardizationSenond.setMostTotalScore(v.getMostTotalScore());
           List<StandardizationScoreSecond> list = new ArrayList();
           StandardizationScoreSecond standardizationScoreSecond = EruptSpringUtil.getBean(StandardizationScoreSecond.class);
           standardizationSenond.setStandardization(v);
        StandardizationSenond merge = eruptDao.merge(standardizationSenond);
           v.getScores().forEach(m->{
               standardizationScoreSecond.setSeriesId(m.getSeriesId());
               standardizationScoreSecond.setStandard(m.getStandard());
               standardizationScoreSecond.setTandardsScore(m.getTandardsScore());
               standardizationScoreSecond.setScoreStandard(m.getScoreStandard());
               standardizationScoreSecond.setScoreProportion(m.getScoreProportion());
               standardizationScoreSecond.setScore(m.getScore());
               standardizationScoreSecond.setStandardizationSenond(merge);
               eruptDao.merge(standardizationScoreSecond);
            //   list.add(standardizationScoreSecond);
           });
          // standardizationSenond.setScores(list);
         //  standardizationSecondDataProxy.beforeAdd(standardizationSenond);
           //StandardizationSenond merge = eruptDao.merge(standardizationSenond);

           JSONObject inputData = new JSONObject();
           inputData.set("clazz", "Standardization");
           inputData.set("insertData",v);
           EruptRemoteUtils.doPost(remoteProxyService.getMainServerUrl() + "/erupt-api/exchange/data/exchange", inputData, Map.class);

       });
       return NotifyUtils.getSuccessNotify("上报成功！");
	}
}
