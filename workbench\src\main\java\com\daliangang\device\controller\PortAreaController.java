package com.daliangang.device.controller;

import com.daliangang.device.entity.PortArea;
import com.google.gson.internal.LinkedTreeMap;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import xyz.erupt.core.annotation.EruptRouter;
import xyz.erupt.core.module.MetaUserinfo;
import xyz.erupt.core.view.EruptApiModel;
import xyz.erupt.jpa.dao.EruptDao;
import xyz.erupt.toolkit.remote.RemoteProxyService;
import xyz.erupt.toolkit.utils.EruptDaoUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @since :2023/4/18:13:21
 */
@RestController
public class PortAreaController {
    @Resource
    private EruptDao eruptDao;
    @Resource
    private RemoteProxyService remoteProxyService;

    /**
     * 获取企业港区管理
     *
     * @param
     * @return
     */
    @RequestMapping("erupt-api/get/portareaName")
    @EruptRouter(verifyType = EruptRouter.VerifyType.LOGIN)
    public EruptApiModel getPortareaName() {
        MetaUserinfo remoteUserInfo = remoteProxyService.getRemoteUserInfo();
        List<PortArea> portAreas = this.queryPortArea(remoteUserInfo.getOrg());
        if (ObjectUtils.isNotEmpty(portAreas)) {
            List<LinkedTreeMap> list1 = new ArrayList<>();
            portAreas.forEach(v -> {
                LinkedTreeMap map = new LinkedTreeMap();
                map.put("code", v.getId());
                map.put("name", v.getPortareaName());
                list1.add(map);
            });

            return EruptApiModel.successApi(list1);
        }

        return EruptApiModel.successApi();
    }

    private List<PortArea> queryPortArea(String orgCode) {
        List<PortArea> portAreas = EruptDaoUtils.selectOnes("select * from tb_port_area where org_code='" + orgCode + "'", PortArea.class);
        if (portAreas.isEmpty()) {
            portAreas = EruptDaoUtils.selectOnes("select * from tb_port_area", PortArea.class);
        }
        return portAreas;
    }
}
