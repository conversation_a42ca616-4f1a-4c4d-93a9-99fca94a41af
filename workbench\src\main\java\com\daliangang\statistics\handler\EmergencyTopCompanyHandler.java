package com.daliangang.statistics.handler;

import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.stereotype.Component;
import xyz.erupt.bi.fun.EruptBiHandler;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * top10企业名单
 * @Author: zengwensi
 * @Description:
 * @Date: Created in 2023/5/10 17:25
 * @Modified By
 */
@Component
public class EmergencyTopCompanyHandler implements EruptBiHandler {
    @Resource
    private CommonHandler commonHandler;
    @Override
    public String exprHandler(String param, Map<String, Object> condition, String expr) {
        //权限
        String s = commonHandler.exprHandlerAuthoritySql(param, condition, expr);
        if(StringUtils.isNotEmpty(s)){
            String replaceSql =  " and enterprise.org_code "+ s;
            expr = expr.replaceAll("#REPLACESQL",replaceSql) ;
        }
        return expr;
//        expr = commonHandler.exprHandlerAuthority(param,condition,expr);
//        return expr ;
    }

    @Override
    public void resultHandler(String param, Map<String, Object> condition, List<Map<String, Object>> result) {

    }

    @Override
    public void exportHandler(String param, Map<String, Object> condition, Workbook workbook) {

    }
}
