/*
* Copyright © 2020-2035 yundingyun All rights reserved.
*/
package com.daliangang.majorisk.operation;

import org.springframework.stereotype.*;
import xyz.erupt.annotation.fun.*;

import java.time.LocalDateTime;
import java.util.*;
import com.daliangang.majorisk.entity.*;
import xyz.erupt.jpa.dao.EruptDao;
import xyz.erupt.toolkit.utils.NotifyUtils;

import javax.annotation.Resource;
import javax.transaction.Transactional;

@Service
public class ReserveReportingEscalationHandler implements OperationHandler<ReserveReporting, Void> {
    @Resource
    private EruptDao eruptDao;

    @Override
    @Transactional
   public String exec(List<ReserveReporting> data, Void unused, String[] param) {
        for (ReserveReporting reserveReporting:data) {
            //reserveReporting.setSubmitted(true);
            reserveReporting.setUpdateTime(LocalDateTime.now());
            eruptDao.merge(reserveReporting);
        }

        return NotifyUtils.getSuccessNotify("上报成功！");
	}
}
