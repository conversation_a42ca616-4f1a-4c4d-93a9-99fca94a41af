/*
 * Copyright © 2020-2035 yundingyun All rights reserved.
 */
package com.daliangang.safedaily.proxy;

import com.daliangang.core.DaliangangContext;
import com.daliangang.safedaily.entity.PortRecord;
import org.springframework.stereotype.Service;
import xyz.erupt.annotation.fun.DataProxy;
import xyz.erupt.annotation.query.Condition;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;

@Service
public class PortRecordDataProxy implements DataProxy<PortRecord> {
    @Override
    public void beforeAdd(PortRecord portRecord) {
        portRecord.setSubmitted(true);
    }

    @Override
    public void beforeUpdate(PortRecord portRecord) {
//        if (portRecord.getSubmitted()) {
//            NotifyUtils.showErrorMsg("报告单已经上报，请勿修改！");
//        }
    }

    @Override
    public String beforeFetch(List<Condition> conditions) {
        StringBuilder str = new StringBuilder();
        // 当前时间设置为当天 0 点 0 分 0 秒
        LocalDateTime nowStartOfDay = LocalDateTime.now().toLocalDate().atStartOfDay();
        // 当前时间加上 30 天
        LocalDateTime thirtyDaysLaterStartOfDay = nowStartOfDay.plusDays(30);

        // 定义日期格式化器
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        // 格式化时间
        String nowFormatted = nowStartOfDay.format(formatter);
        String thirtyDaysLaterFormatted = thirtyDaysLaterStartOfDay.format(formatter);

        // 遍历 conditions 参数，构造查询逻辑
        for (Condition condition : conditions) {
            if ("state".equals(condition.getKey())) {
                conditions.remove(condition);
                Object value = condition.getValue();
                if ("正常".equals(value.toString())) {
                    // 当前时间晚于 validity_period + 30 天
                    str.append(" AND validity_period > '").append(thirtyDaysLaterFormatted).append("'");
                } else if ("已逾期".equals(value.toString())) {
                    // 当前时间晚于 validity_period
                    str.append(" AND validity_period < '").append(nowFormatted).append("'");
                } else if ("即将逾期".equals(value.toString())) {
                    // 当前时间在 validity_period 和 validity_period + 30 天之间
                    str.append(" AND validity_period BETWEEN '")
                            .append(nowFormatted).append("' AND '").append(thirtyDaysLaterFormatted).append("'");
                }
            }
        }

        // 根据是否是部门用户决定是否添加 `submitted=true`
        if (DaliangangContext.isDepartmentUser()) {
            str.append(" AND submitted=true");
        }

        // 如果没有生成任何条件，返回空字符串
        if (str.length() > 0) {
            return str.substring(5); // 移除开头的 " AND"
        } else {
            return "";
        }
    }

    @Override
    public void beforeDelete(PortRecord portRecord) {
//        if (portRecord.getSubmitted()) {
//            NotifyUtils.showErrorMsg("报告单已经上报，请勿删除！");
//        }
    }
}
