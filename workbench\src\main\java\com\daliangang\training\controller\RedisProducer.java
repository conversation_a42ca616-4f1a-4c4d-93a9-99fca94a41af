package com.daliangang.training.controller;

import com.daliangang.training.models.CleanInitEntity;
import com.daliangang.training.models.OrganSyncEntity;
import com.daliangang.training.models.PostSyncEntity;
import com.daliangang.training.models.UserSyncEntity;
import com.daliangang.workbench.entity.Department;
import com.daliangang.workbench.entity.EmployeeInformation;
import com.daliangang.workbench.entity.Enterprise;
import com.daliangang.workbench.entity.EruptRoleTemplatePost;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RestController;
import xyz.erupt.core.view.EruptApiModel;
import xyz.erupt.jpa.dao.EruptDao;
import xyz.erupt.toolkit.redismq.RedisMQConst;
import xyz.erupt.toolkit.redismq.RedisMQService;
import xyz.erupt.toolkit.utils.SqlUtils;
import xyz.erupt.upms.model.EruptOrg;
import xyz.erupt.upms.model.EruptUser;
import xyz.erupt.upms.model.template.EruptDeptTemplate;
import xyz.erupt.upms.model.template.EruptRoleTemplateUser;
import xyz.erupt.upms.service.EruptUserService;

import javax.annotation.Resource;
import javax.transaction.Transactional;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @Date 2023/5/18
 * @Description:
 */

@RestController
public class RedisProducer {

    @Resource
    private RedisMQService redisMQService;

    @Resource
    private EruptDao eruptDao;

    @Resource
    private EruptUserService eruptUserService;

    // 添加事故信息
//    @PostMapping("/produce/info/init")
    @Transactional
    public EruptApiModel addRedisProduce() {


        List<Department> departments = eruptDao.queryEntityList(Department.class);
        for (Department department : departments) {

            String parentOrgCode = null; //默认当前部门无父级
            boolean highestFlag = true; //默认当前部门为市
            EruptOrg org =   this.getEruptOrg(department.getName());
            if (department.getParent() != null) {
                highestFlag = false;
                EruptOrg parent = this.getEruptOrg(department.getParent().getName());
                org.setParentOrg(parent);
                parentOrgCode = parent.getCode();
            }

            OrganSyncEntity organSync = OrganSyncEntity.builder()
                    .orgId(org.getId())
                    .orgName(department.getName())
                    .orgType(highestFlag ? 0 : 1) //若无组织父级 则为市 否则为区县
                    .parentOrgCode(Objects.isNull(parentOrgCode)? null : parentOrgCode)
                    .orgCode(org.getCode())
                    .updateFlag(false)
                    .delFlag(false)
                    .build();
            redisMQService.produce(RedisMQConst.DEFAULT_TOPIC + "_ORGAN", organSync);

        }

        List<Enterprise> enterprises = eruptDao.queryEntityList(Enterprise.class);
        for (Enterprise enterprise : enterprises) {
            EruptOrg org = eruptDao.queryEntity(EruptOrg.class, "name=" + SqlUtils.wrapStr(enterprise.getName()));
            enterprise.setOrgCode(org.getCode());

            OrganSyncEntity organSync = OrganSyncEntity.builder()
                    .orgId(org.getId())
                    .orgName(org.getName())
                    .orgType(2) //若无组织父级 则为市 否则为区县
                    .parentOrgCode(Objects.isNull(org.getParentOrg())? null : org.getParentOrg().getCode())
                    .orgCode(org.getCode())
                    .updateFlag(false)
                    .delFlag(false)
                    .build();
            redisMQService.produce(RedisMQConst.DEFAULT_TOPIC + "_ORGAN", organSync);
        }
        try {
            Thread.sleep(6000);
        } catch (InterruptedException e) {
            throw new RuntimeException(e);
        }


        List<EruptRoleTemplatePost> posts = eruptDao.queryEntityList(EruptRoleTemplatePost.class," exclusive = 1 ");
        for (EruptRoleTemplatePost eruptRoleTemplatePost : posts) {
            PostSyncEntity postSync = PostSyncEntity.builder()
                    .code(eruptRoleTemplatePost.getCode())
                    .orgCode(eruptRoleTemplatePost.getOrgCode())
                    .id(eruptRoleTemplatePost.getId())
                    .name(eruptRoleTemplatePost.getName())
                    .weight(eruptRoleTemplatePost.getWeight())
                    .reserved(eruptRoleTemplatePost.getReserved())
                    .delFlag(false)
                    .updateFlag(false)
                    .type(eruptRoleTemplatePost.getType())
                    .build();
            redisMQService.produce(RedisMQConst.DEFAULT_TOPIC+ "_POST",postSync);

        }
        try {
            Thread.sleep(6000);
        } catch (InterruptedException e) {
            throw new RuntimeException(e);
        }


        EruptUser eruptUser = eruptUserService.findEruptUserByAccount("erupt");
        String sql = " name = '" + "大连市交通运输局" + "'";
        EruptRoleTemplateUser admin = eruptDao.queryEntity(EruptRoleTemplateUser.class,sql);
        String departmentCode = admin.getDepartment().getCode();
        String departmentName = admin.getDepartment().getName();
        UserSyncEntity userSyncErupt = UserSyncEntity.builder()
                .id(eruptUser.getId())
                .name(eruptUser.getName())
                .orgCode(admin.getOrgCode())
                .templateId(0L)
                .department(departmentName)
                .code(departmentCode)
                .updateFlag(false)
                .delFlag(false)
                .build();
        redisMQService.produce("REDIS_MQ_USER",userSyncErupt);

        //模板用户同步
        List<EruptRoleTemplateUser> templateUsers = eruptDao.queryEntityList(EruptRoleTemplateUser.class);
        for (EruptRoleTemplateUser templateUser : templateUsers) {
            if (templateUser.getTemplate().getId()<4L) { //非企业员工 同步无岗位管理员角色
                String userSql = " account = '" + templateUser.getAccount() + "'";
                EruptUser user = eruptDao.queryEntity(EruptUser.class,userSql);
                UserSyncEntity userSync = UserSyncEntity.builder()
                        .id(user.getId())
                        .name(templateUser.getName())
                        .orgCode(templateUser.getOrgCode())
                        .templateId(templateUser.getTemplate().getId())
                        .updateFlag(false)
                        .delFlag(false)
                        .build();
                redisMQService.produce("REDIS_MQ_USER",userSync);
            }

        }
        //同步全部的员工 带岗位
        List<EmployeeInformation> users = eruptDao.queryEntityList(EmployeeInformation.class);
        for (EmployeeInformation user : users) {
            String sqlEmployee = " account = '" + user.getPhone() + "'";


            EruptRoleTemplateUser employee = eruptDao.queryEntity(EruptRoleTemplateUser.class,sqlEmployee);

            String userSql = " account = '" + user.getPhone() + "'";
            EruptUser userEmployee = eruptDao.queryEntity(EruptUser.class,userSql);

            UserSyncEntity userSync = UserSyncEntity.builder()
                    .id(userEmployee.getId())
                    .name(user.getName())
                    .orgCode(user.getOrgCode())
                    .phone(user.getPhone())
                    .sex(user.getSex())
                    .state(user.getState())
                    .posts(user.getJobTitle())
                    .manager(user.getManager())
                    .templateId(4L)
                    .posts(user.getJobTitle())
                    .code(user.getDepartment().getCode())
                    .department(user.getDepartment().getName())
                    .updateFlag(false)
                    .delFlag(false)
                    .build();
            redisMQService.produce("REDIS_MQ_USER",userSync);
        }






        return EruptApiModel.successApi();
    }

    // 添加事故信息
//    @PostMapping("/produce/info/clean")
    @Transactional
    public EruptApiModel cleanRedisProduce() {

        CleanInitEntity cleanInitEntity = CleanInitEntity.builder()
                .initTime(new Date())
                .build();
        redisMQService.produce(RedisMQConst.DEFAULT_TOPIC + "_CLEAN", cleanInitEntity);
        return EruptApiModel.successApi();
    }

    public EruptOrg getEruptOrg(String name) {
        return eruptDao.queryEntity(EruptOrg.class, "name=" + SqlUtils.wrapStr(name));
    }

    @PostMapping("/produce/info/init")
    @Transactional
    public EruptApiModel RedisProduce() {
        //同步全部的员工 带岗位
        List<EmployeeInformation> users = eruptDao.queryEntityList(EmployeeInformation.class);
        for (EmployeeInformation user : users) {
            String sqlEmployee = " account = '" + user.getPhone() + "'";


            EruptRoleTemplateUser employee = eruptDao.queryEntity(EruptRoleTemplateUser.class, sqlEmployee);

            String userSql = " account = '" + user.getPhone() + "'";
            EruptUser userEmployee = eruptDao.queryEntity(EruptUser.class, userSql);
            if(userEmployee == null)
                continue;
            UserSyncEntity userSync = UserSyncEntity.builder()
                    .id(userEmployee.getId())
                    .name(user.getName())
                    .orgCode(user.getOrgCode())
                    .phone(user.getPhone())
                    .sex(user.getSex())
                    .state(user.getState())
                    .posts(user.getJobTitle())
                    .manager(user.getManager())
                    .templateId(4L)
                    .posts(user.getJobTitle())
                    .code(user.getDepartment().getCode())
                    .department(user.getDepartment().getName())
                    .updateFlag(true)
                    .delFlag(false)
                    .build();
            redisMQService.produce("REDIS_MQ_USER_DEPT", userSync);
        }
        return EruptApiModel.successApi();
    }
}
