/*
 * Copyright © 2020-2035 yundingyun All rights reserved.
 */
package com.daliangang.emergency.proxy;

import cn.hutool.core.util.RandomUtil;
import com.daliangang.core.DaliangangContext;
import com.daliangang.emergency.entity.Evaluation;
import com.daliangang.emergency.entity.EvaluationGrade;
import com.daliangang.emergency.entity.EvaluationManage;
import com.daliangang.emergency.entity.EvaluationScore;
import org.springframework.stereotype.Service;
import xyz.erupt.annotation.fun.DataProxy;
import xyz.erupt.annotation.query.Condition;
import xyz.erupt.core.util.EruptUtil;
import xyz.erupt.jpa.dao.EruptDao;
import xyz.erupt.toolkit.utils.AssertUtils;
import xyz.erupt.toolkit.utils.EruptDaoUtils;
import xyz.erupt.toolkit.utils.NotifyUtils;
import xyz.erupt.upms.service.EruptUserService;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Service
public class EvaluationManageDataProxy implements DataProxy<EvaluationManage> {

    @Resource
    private EruptDao eruptDao;

    @Resource
    private EruptUserService eruptUserService;

    @Override
    public void addBehavior(EvaluationManage eval) {
        List<EvaluationScore> scores = new ArrayList<>();
        String sql = "select * from " + EruptUtil.getTable(Evaluation.class) + " order by id";
        List<Evaluation> evaluations = EruptDaoUtils.selectOnes(sql, Evaluation.class);
        for (Evaluation evaluation : evaluations) {
            EvaluationScore score = EruptDaoUtils.cast(evaluation, EvaluationScore.class);
            score.setId(RandomUtil.randomLong());
            score.setScore(new BigDecimal(0));
            scores.add(score);
        }
        eval.setScores(scores);
    }


    @Override
    public void beforeUpdate(EvaluationManage evaluationManage) {
        this.beforeAdd(evaluationManage);
    }

    @Override
    public void beforeAdd(EvaluationManage target) {
        //计算总得分
        double scoreGot = 0;
        Pattern pattern = Pattern.compile("^(([1-9]{1}\\d*)|([0]{1}))(\\.(\\d){0,2})?$"); // 判断小数点后2位的数字的正则表达式
        for (EvaluationScore score : target.getScores()) {
            Matcher match = pattern.matcher(score.getScore().toString());
            AssertUtils.isTrue(match.matches(), score.getName() + "需为整数或小数点2位小数");
            double currScore = score.getScore().doubleValue() * score.getWeight();//子要素得分
            scoreGot += currScore;
        }
        AssertUtils.isTrue(scoreGot <= 100, "总得分不能超过100");
        target.setScore(String.format("%.2f", new BigDecimal(scoreGot)));//总得分

        //计算评估等级
        List<EvaluationGrade> grades = EruptDaoUtils.selectOnes("select * from tb_evaluation_grade", EvaluationGrade.class);
//        List<EvaluationGrade> grades = eruptDao.queryEntityList(EvaluationGrade.class);
        for (EvaluationGrade grade : grades) {
            if (grade.eval(scoreGot)) {
                target.setGrade(grade);
                break;
            }
        }
    }

//    @Override
//    public String beforeFetch(List<Condition> conditions) {
//        EruptRoleTemplateUser roleTemplateUser = EruptSpringUtil.getBean(EruptRoleTemplateUserService.class).queryCurrentAccount();
//        if (DaliangangContext.isEnterpriseUser() && roleTemplateUser != null) {
//            return "company='" + roleTemplateUser.getOrgCode() + "'";
//        } else if (DaliangangContext.isDepartmentUser() && roleTemplateUser != null) {
//            return "org_code " + SqlUtils.wrapIn(eruptUserService.getUserAuth());
//        }
//        return null;
//    }

    @Override
    public void afterDelete(EvaluationManage evaluationManage) {
        if (evaluationManage.getPublicState()) {
            NotifyUtils.showErrorDialog("已发布的不能删除!");
        }
    }


    @Override
    public void searchCondition(Map<String, Object> condition) {
        // 把本年的开始时间和结束时间放到object里
        LocalDateTime now = LocalDateTime.now();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        // 创建当年的起止时间数组
        String[] dateRange = new String[]{
                now.withDayOfYear(1).withHour(0).withMinute(0).withSecond(0).format(formatter),  // 1月1日 00:00:00
                now.withDayOfYear(now.toLocalDate().lengthOfYear()).withHour(23).withMinute(59).withSecond(59).format(formatter)  // 12月31日 23:59:59
        };
        condition.put("evaDate",dateRange);
        DataProxy.super.searchCondition(condition);
    }

    @Override
    public String beforeFetch(List<Condition> conditions) {
        //政府部门看不到未发布的数据
        if(DaliangangContext.isDepartmentUser()){
            return "  public_state=1";

        }
        return null ;

    }
}
