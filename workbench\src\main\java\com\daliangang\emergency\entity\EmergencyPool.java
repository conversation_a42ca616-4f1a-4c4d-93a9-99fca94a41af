/*
 * Copyright © 2020-2035 yundingyun All rights reserved.
 */
package com.daliangang.emergency.entity;

import com.daliangang.core.CompanyRenderHandler;
import com.daliangang.emergency.operation.EmergencyPoolPowerHandler;
import com.daliangang.emergency.proxy.EmergencyPoolDataProxy;
import com.daliangang.workbench.entity.Enterprise;
import com.daliangang.workbench.handler.DepartmentViewRenderHandler;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import xyz.erupt.annotation.Erupt;
import xyz.erupt.annotation.EruptField;
import xyz.erupt.annotation.expr.ExprBool;
import xyz.erupt.annotation.sub_erupt.Power;
import xyz.erupt.annotation.sub_field.Edit;
import xyz.erupt.annotation.sub_field.EditType;
import xyz.erupt.annotation.sub_field.Readonly;
import xyz.erupt.annotation.sub_field.View;
import xyz.erupt.annotation.sub_field.sub_edit.ChoiceType;
import xyz.erupt.annotation.sub_field.sub_edit.InputType;
import xyz.erupt.annotation.sub_field.sub_edit.NumberType;
import xyz.erupt.annotation.sub_field.sub_edit.Search;
import xyz.erupt.toolkit.db.Comment;
import xyz.erupt.toolkit.handler.RemoteEntityChoiceFetchHandler;
import xyz.erupt.toolkit.handler.SqlChoiceFetchHandler;
import xyz.erupt.upms.model.auth.DataAuthHandler;
import xyz.erupt.upms.model.auth.DataAuthModel;

import javax.persistence.Entity;
import javax.persistence.Lob;
import javax.persistence.Table;

@Erupt(name = "事故应急池管理",
		power = @Power(
//				add = true,
//				delete = true,
//				export = false,
//				importable = true,
//				viewDetails = true,
//				edit = true,
				powerHandler = EmergencyPoolPowerHandler.class
		), dataProxy = EmergencyPoolDataProxy.class
		, rowOperation = {})
@Table(name = "tb_emergency_pool")
@Entity
@Getter
@Setter
@Comment("事故应急池管理")
@ApiModel("事故应急池管理")
public class EmergencyPool extends DataAuthModel {
	@EruptField(
			views = @View(title = "企业名称"),
			edit = @Edit(title = "企业名称", type = EditType.CHOICE, notNull = true,
					search = @Search(ifRender = @ExprBool(exprHandler = CompanyRenderHandler.class)),
					readonly = @Readonly(exprHandler = DataAuthHandler.class),
					choiceType = @ChoiceType(fullSpan = true,fetchHandler = RemoteEntityChoiceFetchHandler.class, fetchHandlerParams ={"main","Enterprise","org_code,name"})))
	@Comment("企业名称")
	@ApiModelProperty("企业名称")
	private String company;

	@EruptField(
			views = @View(title = "事故池名称或编号"),
			edit = @Edit(title = "事故池名称或编号",
					type = EditType.INPUT,
					desc = "事故池名称或编号",
					search = @Search(vague = true), notNull = true,
					inputType = @InputType))
	@Comment("事故池名称或编号")
	@ApiModelProperty("事故池名称或编号")
	private String accidentPool;



//	@EruptField(
//			views = @View(title = "所属港区", ifRender = @ExprBool(exprHandler = DepartmentViewRenderHandler.class)),
//			edit = @Edit(title = "所属港区", type = EditType.CHOICE, search = @Search(vague = true), notNull = true,
//					choiceType = @ChoiceType(fetchHandler = RemoteEntityChoiceFetchHandler.class, fetchHandlerParams ={"device","PortArea","id,portarea_name"})))
////                    choiceType = @ChoiceType(fetchHandler = EruptDictCodeChoiceFetchHandler.class, fetchHandlerParams = "portArea")))
//	@Comment("所属港区")
//	@ApiModelProperty("所属港区")
//	private String portArea;
	@EruptField(
			views = @View(title = "所属港区"),
			edit = @Edit(title = "所属港区", type = EditType.CHOICE,
					notNull = true,readonly = @Readonly,
					search = @Search,
					choiceType = @ChoiceType(fetchHandler = SqlChoiceFetchHandler.class, fetchHandlerParams ={"select id,portarea_name from tb_port_area"})))
	@Comment("所属港区")
	@ApiModelProperty("所属港区")
	private String portArea;

	@EruptField(
			views = @View(title = "容积（m3)"),
			edit = @Edit(title = "容积（m3)", type = EditType.NUMBER, notNull = true,
					numberType = @NumberType))
	@Comment("容积（m3)")
	@ApiModelProperty("容积（m3)")
	private Integer area;

	@EruptField(
			views = @View(title = "备注"),
			edit = @Edit(title = "备注", type = EditType.TEXTAREA, notNull = true))
	@Comment("备注")
	@ApiModelProperty("备注")
	private @Lob String remarks;

	@EruptField(
			views = @View(title = "地图", show = false),
			edit = @Edit(title = "地图", type = EditType.MAP, show = true))
	@Comment("地图")
	@ApiModelProperty("地图")
	@Lob private String map;

}
