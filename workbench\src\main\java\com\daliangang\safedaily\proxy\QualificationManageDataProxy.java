/*
* Copyright © 2020-2035 yundingyun All rights reserved.
*/
package com.daliangang.safedaily.proxy;

import com.daliangang.core.DaliangangContext;
import com.daliangang.safedaily.entity.QualificationManage;
import org.springframework.stereotype.Service;
import xyz.erupt.annotation.fun.DataProxy;
import xyz.erupt.annotation.query.Condition;
import xyz.erupt.toolkit.colorfont.ColorStateTimeFontDataProxy;

import javax.annotation.Resource;
import java.util.List;

@Service
public class QualificationManageDataProxy implements DataProxy<QualificationManage> {
    @Override
    public void beforeAdd(QualificationManage qualificationManage) {
        qualificationManage.setSubmitted(true);
    }

    @Override
    public void beforeUpdate(QualificationManage qualificationManage) {
//        if (qualificationManage.getSubmitted()) {
//            NotifyUtils.showErrorMsg("报告单已经上报，请勿修改！");
//        }
    }

    @Override
    public void beforeDelete(QualificationManage qualificationManage) {
//        if (qualificationManage.getSubmitted()) {
//            NotifyUtils.showErrorMsg("报告单已经上报，请勿删除！");
//        }
    }

//    @Override
//    public void afterFetch(Collection<Map<String, Object>> list)  {
//        List<QualificationManage> qualificationManageList = EruptDaoUtils.convert(list, QualificationManage.class);
//
//
//        for (QualificationManage qualificationManage : qualificationManageList) {
//            //若当前时间大于【有效期至】 ，则已逾期；若当前时间距离评估时间还有一个月，则即将逾期；其他为正常状态
//            Date effectiveDate = qualificationManage.getCheckDate();
//            java.util.Date now = Date.from(LocalDate.now().atStartOfDay().atZone(ZoneId.systemDefault()).toInstant());
//
//
//            long month = DateUtil.betweenMonth(now, effectiveDate, false);
//
//            if (now.after(effectiveDate)) {
//                qualificationManage.setStateStr(TplUtils.addColor("已逾期", "#f5222d"));
//            } else if (month >= 1) {
//                qualificationManage.setStateStr(TplUtils.addColor("正常", "#52c41a"));
//            }   else {
//                qualificationManage.setStateStr(TplUtils.addColor("即将逾期", "#f5bd00"));
//
//            }
//
//            EruptDaoUtils.updateAfterFetch(list, qualificationManage.getId(), "stateStr", qualificationManage.getStateStr());
//        }
//
//    }
    @Resource
    private ColorStateTimeFontDataProxy colorStateTimeFontDataProxy ;

    @Override
    public String beforeFetch(List<Condition> conditions) {
        String sql = " 1= 1 " ;
        sql = sql + colorStateTimeFontDataProxy.searchByState(QualificationManage.class,"state" ,conditions);
        if (DaliangangContext.isDepartmentUser()) return sql +" and submitted=true";

        return sql;
    }
}
