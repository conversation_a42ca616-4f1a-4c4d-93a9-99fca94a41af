/*
 * Copyright © 2020-2035 yundingyun All rights reserved.
 */
package com.daliangang.workbench.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import xyz.erupt.annotation.Erupt;
import xyz.erupt.annotation.EruptField;
import xyz.erupt.annotation.sub_erupt.Power;
import xyz.erupt.annotation.sub_field.Edit;
import xyz.erupt.annotation.sub_field.EditType;
import xyz.erupt.annotation.sub_field.View;
import xyz.erupt.annotation.sub_field.sub_edit.InputType;
import xyz.erupt.jpa.model.MetaModel;
import xyz.erupt.toolkit.db.Comment;

import javax.persistence.Entity;
import javax.persistence.Table;

@Erupt(name = "第三方企业数据关联", importTruncate = true, power = @Power(add = true, delete = true, export = true, importable = true, viewDetails = true, edit = true))
@Table(name = "tb_enterprise_relevancy")
@Entity
@Getter
@Setter
@Comment("第三方企业数据关联")
@ApiModel("第三方企业数据关联")
public class EnterpriseRelevancy extends MetaModel {

    @EruptField(
            views = @View(title = "企业名称"),
            edit = @Edit(title = "企业名称", type = EditType.INPUT,
                    inputType = @InputType))
    @Comment("企业名称")
    @ApiModelProperty("企业名称")
    private String company;

    @EruptField(
            views = @View(title = "本系统id"),
            edit = @Edit(title = "本系统id", type = EditType.INPUT,
                    inputType = @InputType))
    @Comment("本系统id")
    @ApiModelProperty("本系统id")
    private String orgCode;

    @EruptField(
            views = @View(title = "赛名系统id"),
            edit = @Edit(title = "赛名系统id", type = EditType.INPUT,
                    inputType = @InputType))
    @Comment("赛名系统id")
    @ApiModelProperty("赛名系统id")
    private String smId;

}
