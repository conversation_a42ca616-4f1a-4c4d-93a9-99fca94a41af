package com.daliangang.majorisk.entity;

import com.daliangang.majorisk.proxy.FiveChecklistsEmergencyDisposalDataProxy;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import xyz.erupt.annotation.Erupt;
import xyz.erupt.annotation.EruptField;
import xyz.erupt.annotation.sub_erupt.Power;
import xyz.erupt.annotation.sub_field.Edit;
import xyz.erupt.annotation.sub_field.EditType;
import xyz.erupt.annotation.sub_field.Readonly;
import xyz.erupt.annotation.sub_field.View;
import xyz.erupt.annotation.sub_field.sub_edit.AttachmentType;
import xyz.erupt.annotation.sub_field.sub_edit.InputType;
import xyz.erupt.toolkit.db.Comment;
import xyz.erupt.upms.model.auth.DataAuthModel;

import javax.persistence.*;

/**
 * <AUTHOR>
 * @since :2023/4/4:9:57
 */

@Erupt(name = "五清单应急处置清单", power = @Power(add = true, delete = true, export = false, importable = true, viewDetails = true, edit = true)
        , dataProxy = FiveChecklistsEmergencyDisposalDataProxy.class
)
@Table(name = "tb_fivechecklists_mergencydisposal")
@Entity
@Getter
@Setter
@Comment("五清单应急处置清单")
@ApiModel("五清单应急处置清单")
public class FiveChecklistsEmergencyDisposal extends DataAuthModel {
//    @EruptField(
//            views = @View(title = "企业名称"),
//            edit = @Edit(title = "企业名称", readonly = @Readonly(exprHandler = DataAuthInvoker.class), type = EditType.CHOICE, notNull = true,
//                    choiceType = @ChoiceType(fetchHandler = RemoteEntityChoiceFetchHandler.class, fetchHandlerParams ={"main","Enterprise","org_code,name"})))
//    @Comment("企业名称")
//    @ApiModelProperty("企业名称")
//    private String company;

    @Lob
    @EruptField(
            views = @View(title = "应急预案"),
            edit = @Edit(title = "应急预案", type = EditType.TEXTAREA,placeHolder = "说明：列出本单位重大风险相关应急预案名称",
                    inputType = @InputType(length = 300)))
    @Comment("应急预案")
    @ApiModelProperty("应急预案")
    private String emergencyPlan;

    @Lob
    @EruptField(
            views = @View(title = "应急管理机制"),
            edit = @Edit(title = "应急管理机制", type = EditType.TEXTAREA,placeHolder = "说明：添加应急组织机构图和对应文字说明，文字说明示例：公司应急组织机构由应急管理委员会、应急管理办公室、应急救援指挥部、各应急行动组共同构成，应急组织机构图如图所示",
                    inputType = @InputType(length = 500)))
    @Comment("应急管理机制")
    @ApiModelProperty("应急管理机制")
    private String emergencyManagement;

    @EruptField(
            views = @View(title = "附件"),
            edit = @Edit(title = "附件", type = EditType.ATTACHMENT,
                    attachmentType = @AttachmentType(type = AttachmentType.Type.IMAGE, maxLimit = 50, fileTypes = {".jpg", ".bmp", ".png"})))
    @Comment("附件")
    @ApiModelProperty("附件")
    private String file;


    @Lob
    @EruptField(
            views = @View(title = "应急措施"),
            edit = @Edit(title = "应急措施", type = EditType.TEXTAREA,placeHolder = "说明：应急处置要点，示例：1、发现储罐着火，呼救并按下现场火灾报警按钮；2、立即报告值班领导；停止作业、关闭相关阀门；3、向119、主管部门报警，向相邻单位通报有关情况；4、利用现场消防器材扑救；启动消防泵，开启着火罐泡沫灭火系统，……",
                    inputType = @InputType(length = 1000)))
    @Comment("应急措施")
    @ApiModelProperty("应急措施")
    private String emergencyMeasure;


    @Lob
    @EruptField(
            views = @View(title = "本年度事件（故）处置信息"),
            edit = @Edit(title = "本年度事件（故）处置信息", type = EditType.TEXTAREA,placeHolder = "说明：本年度事件（故）时间、地点、人物、事故后果、事故处置情况、事故原因分析等，示例：1、XX年XX月XX日，XX泊位卸XX（货物名称），出现压力骤升现象，导致XX（地点）多处发生渗漏。公司XX部门及时对渗漏点进行维修和打压检测，……。通过视频监控、人员询问等方式，调查分析原因为：……",
                    inputType = @InputType(length =  300)))
    @Comment("本年度事件（故）处置信息")
    @ApiModelProperty("本年度事件（故）处置信息")
    private String eventDisposal;

    @Lob
    @EruptField(
            views = @View(title = "专（兼）职应急队伍"),
            edit = @Edit(title = "专（兼）职应急队伍", type = EditType.TEXTAREA,readonly = @Readonly,
                    inputType = @InputType))
    @Comment("专（兼）职应急队伍")
    @ApiModelProperty("专（兼）职应急队伍")
    private String emergencyTeam;

    @Lob
    @EruptField(
            views = @View(title = "应急物资"),
            edit = @Edit(title = "应急物资", type = EditType.TEXTAREA,readonly = @Readonly,
                    inputType = @InputType))
    @Comment("应急物资")
    @ApiModelProperty("应急物资")
    private String emergencySupplies;


    @EruptField(
            views = @View(title = "应急演练总数"),
            edit = @Edit(title = "应急演练总数", type = EditType.INPUT,readonly = @Readonly,
                    inputType = @InputType))
    @Comment("应急演练总数")
    @ApiModelProperty("应急演练总数")
    private String emergencyDrillNum;


    @EruptField(
            views = @View(title = "应急演练名称"),
            edit = @Edit(title = "应急演练名称", type = EditType.INPUT,readonly = @Readonly,
                    inputType = @InputType(fullSpan = true)))
    @Comment("应急演练名称")
    @ApiModelProperty("应急演练名称")
    private String emergencyDrillName;


//    @OneToOne
//    @EruptField
//    @JoinColumn(name = "tb_fivechecklists_foundation_id")
//    private FiveChecklistsFoundation fiveChecklistsFoundation;

    @OneToOne
    @EruptField
    @JoinColumn(name = "tb_five_detail_id")
    private FiveDetail fiveDetail;

}
