package com.daliangang.rndpub.proxy;

import com.daliangang.rndpub.entity.InspectionProblemManage;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import xyz.erupt.annotation.fun.DataProxy;
import xyz.erupt.annotation.query.Condition;
import xyz.erupt.jpa.dao.EruptDao;
import xyz.erupt.toolkit.utils.EruptDaoUtils;
import xyz.erupt.toolkit.utils.SqlUtils;
import xyz.erupt.upms.model.EruptUser;
import xyz.erupt.upms.service.EruptUserService;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.List;
import java.util.Map;

@Service
@Slf4j
public class InspectionProblemManageProxy implements DataProxy<InspectionProblemManage> {
    @Resource
    private EruptDao eruptDao ;
    @Resource
    private EruptUserService eruptUserService ;

    @Transactional
    @Override
    public void searchCondition(Map<String, Object> condition) {
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        //清空内容
        String truncateSql="truncate table tb_inspection_problem_self";
        eruptDao.getJdbcTemplate().execute(truncateSql);
        String sql = "SELECT" +
                "      *," +
                "      CONCAT(IFNULL(ROUND( publishQuestionNum * 100 / totalQuestion, 2 ),0),'%') publishRatio," +//发布率
                "      CONCAT(IFNULL(ROUND( passResult * 100 / totalQuestion, 2 ),0),'%') rectifiedRatio " +//整改率
                "     FROM" +
                "     (" +
                "       SELECT " +
                "          p.id procedureId ,name," +//检查名称
                "          CONCAT(count(distinct r.check_object),'/',number_of_enterprises) enterprisesNum," +//检查企业数量
                "          inspection_date inspectionDate,"+//检查开始日期
                "          count(r.id) totalQuestion," +//检查问题数量
                "          count(IF( publish_status = 1, TRUE, NULL )) publishQuestionNum, " +//已发布问题数量
                "          count(IF( inspection_result = 'PASS' and publish_status = 1, TRUE, NULL )) passResult," +//整改通过问题数量
                "          count(IF( rectification_status = 'TO_BE_RECTIFIED' and publish_status = 1, TRUE, NULL )) rectifiedResult," +//待整改问题数量
                "          count(IF( DATEDIFF(deadline,now())<0 and publish_status = 1, TRUE, NULL )) overDue, "+//已逾期问题数量
                "          count(IF( DATEDIFF(deadline,now())<0 and rectification_status != 'TO_BE_RECTIFIED' and publish_status = 1, TRUE, NULL )) overDueNotPass, "+//逾期未整改问题数量
                "          r.create_by createBy "+
                "        FROM" +
                "           tb_procedure p" +
                "        LEFT JOIN tb_inspection_results r ON p.id = r.inspection_name  " +
                "        GROUP BY name,number_of_enterprises,inspection_date,p.id,r.create_by" +
                ") t" ;
        List<InspectionProblemManage> list = EruptDaoUtils.selectOnes(sql, InspectionProblemManage.class);
        list.forEach(item->{

            eruptDao.merge(item);
//            String insertSql=String.format("insert into tb_inspection_problem_self(id,enterprises_num,name,over_due,pass_result,publish_question_num,publish_ratio,rectified_ratio,rectified_result,total_question,inspection_date,over_due_not_pass,create_by) values(%s,'%s','%s','%s','%s','%s','%s','%s','%s','%s','%s','%s','%s')",
//                    item.getId(),
//                    item.getEnterprisesNum(),
//                    item.getName(),
//                    item.getOverDue(),
//                    item.getPassResult(),
//                    item.getPublishQuestionNum(),
//                    item.getPublishRatio(),
//                    item.getRectifiedRatio(),
//                    item.getRectifiedResult(),
//                    item.getTotalQuestion(),
//                    simpleDateFormat.format(item.getInspectionDate()),
//                    item.getOverDueNotPass(),
//                    item.getCreateBy()
//                    );
//            eruptDao.getJdbcTemplate().execute(insertSql);

        });

    }

    @Override
    public String beforeFetch(List<Condition> conditions) {
        String sql = " create_by is null " ;
        EruptUser user = eruptUserService.getCurrentEruptUser();
        if( null != user){
            return  " (create_by = "+ SqlUtils.wrapStr(user.getName()) + " or "+sql +")";
        }
        return sql ;
    }
}
