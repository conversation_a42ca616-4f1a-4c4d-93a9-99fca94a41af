package com.daliangang.datascreen.regulatoryboard.response;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * @Author: chong<PERSON><PERSON>n
 * @Date: 2024/11/13 16:57
 */

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class SecurityDetail {
    private String companyName;
    private String status;
    private String fillingTime;
}
