/*
 * Copyright © 2020-2035 yundingyun All rights reserved.
 */
package com.daliangang.emergency.entity;

import com.daliangang.core.CompanyRenderHandler;
import com.daliangang.emergency.proxy.MaterialReserveDataProxy;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import xyz.erupt.annotation.Erupt;
import xyz.erupt.annotation.EruptField;
import xyz.erupt.annotation.expr.ExprBool;
import xyz.erupt.annotation.sub_erupt.Power;
import xyz.erupt.annotation.sub_field.Edit;
import xyz.erupt.annotation.sub_field.EditType;
import xyz.erupt.annotation.sub_field.Readonly;
import xyz.erupt.annotation.sub_field.View;
import xyz.erupt.annotation.sub_field.sub_edit.ChoiceType;
import xyz.erupt.annotation.sub_field.sub_edit.InputType;
import xyz.erupt.annotation.sub_field.sub_edit.Search;
import xyz.erupt.annotation.sub_field.sub_edit.TagsType;
import xyz.erupt.toolkit.db.Comment;
import xyz.erupt.toolkit.handler.EruptDictTagFetchHandler;
import xyz.erupt.toolkit.handler.RemoteEntityChoiceFetchHandler;
import xyz.erupt.toolkit.handler.SqlChoiceFetchHandler;
import xyz.erupt.upms.model.auth.DataAuthHandler;
import xyz.erupt.upms.model.auth.DataAuthModel;

import javax.persistence.Entity;
import javax.persistence.Lob;
import javax.persistence.Table;

@Erupt(name = "应急物资储备点管理",importTruncate = true, power = @Power(add = true, delete = true, export = false, importable = true, viewDetails = true, edit = true)
		, dataProxy = MaterialReserveDataProxy.class
		, rowOperation = {})
@Table(name = "tb_material_reserve")
@Entity
@Getter
@Setter
@Comment("应急物资储备点管理")
@ApiModel("应急物资储备点管理")
public class MaterialReserve extends DataAuthModel {

	@EruptField(
			views = @View(title = "所属单位"),
			edit = @Edit(title = "所属单位",
					type = EditType.INPUT,
					notNull = true,search = @Search(vague = true),
					inputType = @InputType
			)
	)
	@Comment("所属单位")
	@ApiModelProperty("所属单位")
	private String ownerDepartment;

	@EruptField(
			views = @View(title = "应急物资储备点名称"),
			edit = @Edit(title = "应急物资储备点名称",
					desc = "应急物资储备点名称",
					type = EditType.INPUT, search = @Search(vague = true), notNull = true,
					inputType = @InputType))
	@Comment("应急物资储备点名称")
	@ApiModelProperty("应急物资储备点名称")
	private String materialReserve;

//	@EruptField(
//			views = {@View(
//					title = "组织编码",
//					show = false,
//					ifRender = @ExprBool(
//							exprHandler = DataAuthHandler.class
//					)
//			)},
//			edit = @Edit(
//					title = "组织编码",
//					show = false
//			)
//	)
//	@EruptSmartSkipSerialize
//	private String orgCode;



//	@EruptField(
//			views = @View(title = "企业名称"),
//			edit = @Edit(title = "企业名称", type = EditType.CHOICE, notNull = true,
//					search = @Search,
//					readonly = @Readonly(exprHandler = DataAuthHandler.class),
//					choiceType = @ChoiceType(fetchHandler = RemoteEntityChoiceFetchHandler.class, fetchHandlerParams ={"main","Enterprise","org_code,name"})))
//	@Comment("企业名称")
//	@ApiModelProperty("企业名称")
//	private String company;
//		@EruptField(
//				views = @View(title = "填报单位"),
//				edit = @Edit(title = "填报单位",
//						readonly = @Readonly(),
//						type = EditType.INPUT, notNull = true,
//						search = @Search(vague = true)
//				))
//		@Comment("填报单位")
//		@ApiModelProperty("填报单位")
//		private String company;
	@EruptField(
			views = @View(title = "填报单位"),
			edit = @Edit(title = "填报单位",
					search = @Search(ifRender = @ExprBool(exprHandler = CompanyRenderHandler.class)),
					type = EditType.CHOICE, notNull = true, readonly = @Readonly(exprHandler = DataAuthHandler.class),
					choiceType = @ChoiceType(fetchHandler = RemoteEntityChoiceFetchHandler.class, fetchHandlerParams = {"main", "EruptOrg", "code,name"})))
	@Comment("填报单位")
	@ApiModelProperty("填报单位")
	private String company;

//	@EruptField(
//			views = @View(title = "所属港区", ifRender = @ExprBool(exprHandler = DepartmentViewRenderHandler.class)),
//			edit = @Edit(title = "所属港区", type = EditType.CHOICE, search = @Search(vague = true), notNull = true,
//					choiceType = @ChoiceType(fetchHandler = RemoteEntityChoiceFetchHandler.class, fetchHandlerParams ={"device","PortArea","id,portarea_name"})))
////                    choiceType = @ChoiceType(fetchHandler = EruptDictCodeChoiceFetchHandler.class, fetchHandlerParams = "portArea")))
//	@Comment("所属港区")
//	@ApiModelProperty("所属港区")
//	private String portArea;
	@EruptField(
			views = @View(title = "所属港区"),
			edit = @Edit(title = "所属港区", type = EditType.CHOICE,
					notNull = false,readonly = @Readonly,
					search = @Search,
					choiceType = @ChoiceType(fetchHandler = SqlChoiceFetchHandler.class, fetchHandlerParams ={"select id,portarea_name from tb_port_area"})))
	@Comment("所属港区")
	@ApiModelProperty("所属港区")
	private String portArea;

	@EruptField(
			views = @View(title = "物资类型"),
			edit = @Edit(title = "物资类型", type = EditType.TAGS, notNull = true,search = @Search,
					tagsType = @TagsType(allowExtension = false, fetchHandler = EruptDictTagFetchHandler.class, fetchHandlerParams = "materialType")))
	@Comment("物资类型")
	@ApiModelProperty("物资类型")
	private String materialType;

	@EruptField(
			views = @View(title = "联系人"),
			edit = @Edit(title = "联系人", type = EditType.INPUT, notNull = true,
					inputType = @InputType))
	@Comment("联系人")
	@ApiModelProperty("联系人")
	private String contacts;

	@EruptField(
			views = @View(title = "联系电话"),
			edit = @Edit(title = "联系电话", type = EditType.INPUT, notNull = true,
					inputType = @InputType))
	@Comment("联系电话")
	@ApiModelProperty("联系电话")
	private String contactNumber;


	@EruptField(
			views = @View(title = "地图", show = false),
			edit = @Edit(title = "地图", type = EditType.MAP, show = true))
	@Comment("地图")
	@ApiModelProperty("地图")
	@Lob
	private String map;

}
