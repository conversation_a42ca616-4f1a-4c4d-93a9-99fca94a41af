package com.daliangang.rndpub.proxy;

import com.daliangang.rndpub.entity.EnterpriseInformation;
import com.daliangang.rndpub.entity.InspectionResultsView;
import com.daliangang.rndpub.entity.Procedure;
import com.daliangang.rndpub.form.InspectionResultNumForm;
import com.daliangang.workbench.entity.Enterprise;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import xyz.erupt.annotation.fun.DataProxy;
import xyz.erupt.annotation.query.Condition;
import xyz.erupt.jpa.dao.EruptDao;
import xyz.erupt.toolkit.utils.*;

import javax.transaction.Transactional;
import java.time.LocalDate;
import java.time.ZoneId;
import java.util.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

public class InspectionrResultsViewDataProxy implements DataProxy<InspectionResultsView> {
    @Override
    public void addBehavior(InspectionResultsView inspectionrResults) {
        //待整改
        inspectionrResults.setInspectionResult("TO_BE_RECTIFIED");
    }

    @Override
    public String beforeFetch(List<Condition> conditions) {
        //只能查看已发布的数据
        AtomicReference<String> returnStr = new AtomicReference<>(" 1 = 1 and publish_status = 1") ;
        //检查对象查询条件
        Optional<Condition> checkObjectFirst = conditions.stream().filter(condition ->
                "company".equals(condition.getKey())
        ).findFirst();
        checkObjectFirst.ifPresent( f ->{
            returnStr.set(returnStr.get()+"and check_object like CONCAT('%'," + SqlUtils.wrapStr((String) f.getValue())+",'%')");
            conditions.remove(f) ;
        });
        //是否逾期查询条件
        Optional<Condition> beOverdueFirst = conditions.stream().filter(condition ->
                "beOverdue".equals(condition.getKey())
        ).findFirst();
        beOverdueFirst.ifPresent(f -> {
            Object value = f.getValue();
            if (value instanceof List) {
                List<?> list = (List<?>) value;
                Set<String> statusSet = new HashSet<>(list.size());
                for (Object item : list) {
                    if (item instanceof String) {
                        statusSet.add((String) item);
                    } else {
                        throw new IllegalArgumentException("Condition 'beOverdue' list contains non-string elements");
                    }
                }

                if (statusSet.contains("0") && statusSet.contains("1") && statusSet.contains("2")) {
                    // 如果是全选或者包含所有状态，不添加额外条件
                } else {
                    List<String> conditionsList = new ArrayList<>();
                    if (statusSet.contains("0")) {
                        conditionsList.add("date(rectification_time) <= date(deadline)");
                    }
                    if (statusSet.contains("1")) {
                        conditionsList.add("date(rectification_time) > date(deadline) and inspection_result != 'PASS'");
                    }
                    if (statusSet.contains("2")) {
                        conditionsList.add("date(rectification_time) > date(deadline) and inspection_result = 'PASS'");
                    }
                    if (!conditionsList.isEmpty()) {
                        returnStr.set(returnStr.get() + " and (" + String.join(" or ", conditionsList) + ")");
                    }
                }

                conditions.remove(f);
            } else if (value instanceof String) {
                String overdueStatus = (String) value;
                switch (overdueStatus) {
                    case "0":
                        returnStr.set(returnStr.get() + " and date(rectification_time) <= date(deadline)");
                        break;
                    case "1":
                        returnStr.set(returnStr.get() + " and date(rectification_time) > date(deadline) and inspection_result != 'PASS'");
                        break;
                    case "2":
                        returnStr.set(returnStr.get() + " and date(rectification_time) > date(deadline) and inspection_result = 'PASS'");
                        break;
                    default:
                        break;
                }
                conditions.remove(f);
            } else {
                throw new IllegalArgumentException("Condition 'beOverdue' should be a String or a List containing Strings");
            }
        });
        /*beOverdueFirst.ifPresent( f ->{
            String value = (String) f.getValue();
            switch (value) {
                case "0":
                    returnStr.set(returnStr.get() + " and date(now()) > date(deadline)");
                    break;
                case "1":
                    returnStr.set(returnStr.get() + " and date(now()) <= date(deadline)");
                    break;
                case "all":
                    // 如果状态是"all"，则不添加额外条件
                    break;
                default:
                    throw new IllegalArgumentException("Unknown beOverdue status: " + value);
            }
            //returnStr.set((Boolean)f.getValue() ? returnStr.get()+" and date(now()) <=date(deadline)":returnStr.get()+" and date(now()) >date(deadline)");
            conditions.remove(f) ;
        });*/
        return returnStr.get() +
                "ORDER BY "+
                "("+
                " CASE WHEN inspection_result='BY' THEN 0 " +
                " ELSE "+
                " CASE "+
                "WHEN DATEDIFF( now(), deadline )< 0 THEN "+
                " CASE "+
                " WHEN inspection_result = 'BY' THEN 0"+
                " WHEN inspection_result = 'TO_BE_RECTIFIED' THEN 1"+
                " WHEN inspection_result = 'TO_BE_REVIEWED' THEN 2"+
                " WHEN inspection_result = 'NOT_THROUGH' THEN 3 "+
                " END ELSE "+
                "       CASE"+
                "      WHEN inspection_result = 'BY' THEN 4"+
                "       WHEN inspection_result = 'TO_BE_RECTIFIED' THEN 5"+
                "        WHEN inspection_result = 'TO_BE_REVIEWED' THEN 6 "+
                "       WHEN inspection_result = 'NOT_THROUGH' THEN 7"+
                "       END"+
                "       END"+
                "       END"+
                ") DESC,"+
                "check_object,"+
                "deadline DESC";
    }
    @Override
    public void afterFetch(Collection<Map<String, Object>> list) {
//        Date now = new Date(System.currentTimeMillis());
        LocalDate localDateTime = LocalDate.now();
        ZoneId zoneId = ZoneId.systemDefault();
        Date now = Date.from(localDateTime.atStartOfDay().atZone(zoneId).toInstant());
        List<InspectionResultsView> fields = EruptDaoUtils.convert(list, InspectionResultsView.class);
        for (InspectionResultsView field : fields) {
//            String checkObject = field.getCheckObject();
//            System.out.println("检查对象为:"+checkObject);
            if(null != field.getDeadline()){
                //拿到当前日期，判断是否逾期
                //如果当前日期小于等于截止日期
                if ((field.getRectificationTime()==null?now:field.getRectificationTime()).compareTo(field.getDeadline()) <= 0) {
                    // 未逾期
                    EruptDaoUtils.updateAfterFetch(list, field.getId(), "beOverdue", "0");
                } else {
                    // 逾期
                    if (field.getInspectionResult() == null || !field.getInspectionResult().equals("PASS")) {
                        // 审查结果为空或未通过
                        EruptDaoUtils.updateAfterFetch(list, field.getId(), "beOverdue", "1");
                    } else {
                        // 逾期已整改
                        EruptDaoUtils.updateAfterFetch(list, field.getId(), "beOverdue", "2");
                    }
                }
              //  EruptDaoUtils.updateAfterFetch(list, field.getId(), "beOverdue", now.compareTo(field.getDeadline()) <= 0);
//                EruptDaoUtils.updateAfterFetch(list, field.getId(), "checkObject", field.getCheckObject());
            }
            String inspectionResultView = null ;
//            “不通过”三个字要红色显示
//            “待审核”三个字要黄色
            if("NOT_THROUGH".equals(field.getInspectionResult())){
                inspectionResultView = "<font color='#f5222d'>不通过</font>" ; ;
            }
            else if("TO_BE_REVIEWED".equals(field.getInspectionResult())){
                inspectionResultView = "<font color='#f5bd00'>待审核</font>" ;

            } else if("TO_BE_RECTIFIED".equals(field.getInspectionResult())){
                inspectionResultView = "待整改";
            }else {
                inspectionResultView = "通过" ;
            }
            EruptDaoUtils.updateAfterFetch(list,field.getId(),"inspectionResultView",inspectionResultView);
        }

    }

    @Override
    @Transactional
    public void excelImport(Object workbook) {
        Workbook wb = (Workbook) workbook;
        Sheet sheet = wb.getSheetAt(0);
        EruptDao eruptDao = EruptDaoUtils.getEruptDao();
        String entSql = "select distinct org_code,enterprise_name from tb_enterprise_information ";
        List<EnterpriseInformation> enterprises = EruptDaoUtils.selectOnes(entSql, EnterpriseInformation.class);
        Map<String, String> enterpriseMap = enterprises.stream().collect(Collectors.toMap(EnterpriseInformation::getEnterpriseName, EnterpriseInformation::getOrgCode));
        for (int i = sheet.getFirstRowNum() + 1; i <= sheet.getLastRowNum(); i++) {
            Row row = sheet.getRow(i);
            String inspName = row.getCell(0).getStringCellValue();
            String inspectionItems = row.getCell(2).getStringCellValue();  //检查事项
            String checkFirst = row.getCell(3).getStringCellValue();  // 检查内容
            Procedure procedure = eruptDao.queryEntity(Procedure.class, "name=" + SqlUtils.wrapStr(inspName));
            AssertUtils.notNull(procedure, "无效的检查名称");
            InspectionResultNumForm o = EruptDaoUtils.selectOne("select count(*) as num from tb_inspection_items where inspection_items= '" + inspectionItems + "'", InspectionResultNumForm.class);
            if (o.getNum().equals("0")) {
                NotifyUtils.showErrorDialog("无效的检查事项");
            }
            InspectionResultNumForm o1 = EruptDaoUtils.selectOne("select count(*) as num from tb_inspection_items where check_first= '" + checkFirst + "'", InspectionResultNumForm.class);
            if (o1.getNum().equals("0")) {
                NotifyUtils.showErrorDialog("无效的检查内容");
            }
            String orgCode = enterpriseMap.get(row.getCell(1).getStringCellValue());
            String sql = "delete from tb_inspection_results where check_object=" + SqlUtils.wrapStr(orgCode)
                    + " and inspection_name=" + procedure.getId()
                    + " and inspection_items=" + SqlUtils.wrapStr(row.getCell(2).getStringCellValue());
            eruptDao.getJdbcTemplate().execute(sql);
        }
    }

    @Override
    public void beforeUpdate(InspectionResultsView inspectionResults) {
        String checkObject = inspectionResults.getCheckObject();
        if(checkObject!=null){
            Pattern p = Pattern.compile("[\u4e00-\u9fa5]");
            Matcher m = p.matcher(checkObject);
            if(m.find()){
                String selectSql="select * from tb_enterprise where name='"+checkObject+"'";
                List<Enterprise> enterpriseList = EruptDaoUtils.selectOnes(selectSql, Enterprise.class);
                if(!enterpriseList.isEmpty()){
                    inspectionResults.setCheckObject(enterpriseList.get(0).getOrgCode());
                }

            }
        }

    }
}
