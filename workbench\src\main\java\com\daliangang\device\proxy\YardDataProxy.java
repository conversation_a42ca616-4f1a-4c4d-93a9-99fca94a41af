/*
 * Copyright © 2020-2035 yundingyun All rights reserved.
 */
package com.daliangang.device.proxy;

import cn.hutool.json.JSONObject;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.daliangang.core.DaliangangContext;
import com.daliangang.device.entity.PositionDocking;
import com.daliangang.device.entity.Yard;
import com.daliangang.device.form.DockingForm;
import com.daliangang.device.operation.PositionDockingHttpHandler;
import com.daliangang.majorisk.entity.DcReserveReporting;
import com.google.gson.Gson;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import xyz.erupt.annotation.fun.DataProxy;
import xyz.erupt.common.http.EruptRemoteUtils;
import xyz.erupt.core.config.GsonFactory;
import xyz.erupt.core.util.EruptSpringUtil;
import xyz.erupt.jpa.dao.EruptDao;
import xyz.erupt.toolkit.remote.RemoteProxyService;
import xyz.erupt.toolkit.utils.EruptDaoUtils;
import xyz.erupt.upms.service.EruptPlatformService;

import javax.annotation.Resource;
import javax.transaction.Transactional;
import java.util.HashMap;
import java.util.Map;

@Service
@Slf4j
public class YardDataProxy implements DataProxy<Yard> {
    @Resource
    private PositionDockingHttpHandler positionDockingHttpHandler;
    @Resource
    private EruptDao eruptDao;

    @Resource
    private RemoteProxyService remoteProxyService;

    @Resource
    private EruptPlatformService eruptPlatformService ;

    public static final String IS_PUSH = "IS_PUSH" ;

    @Override
    @Transactional
    public void afterAdd(Yard yard) {
        // 同步保存每日储量上报

        DcReserveReporting dcReserveReporting = EruptSpringUtil.getBean(DcReserveReporting.class);
        dcReserveReporting.setCompany(yard.getCompany());
        dcReserveReporting.setOrgCode(yard.getCompany());
        dcReserveReporting.setYardNum(String.valueOf(yard.getId()));
        eruptDao.merge(dcReserveReporting);

        String isPush = eruptPlatformService.getOption(YardDataProxy.IS_PUSH).getAsString() ;
        if (isPush.equals("true")) {
            if (StringUtils.isNotEmpty(yard.getOrgCode()) && ObjectUtils.isEmpty(yard.getPositionId())) {

                String token = DaliangangContext.getToken();
                positionDockingHttpHandler.AddHttpDocking(yard, "5", yard.getOrgCode(), token);
                log.info("推送堆场数据成功 ->  token=" + token);
            }
        }
        // 保存导入的三方地点关系
        if (ObjectUtils.isNotEmpty(yard.getPositionId())) {
            // 删除中间表数据
            String sql1 = "delete from tb_position_docking where position_type = '5' and b_id = "+yard.getId();
            EruptDaoUtils.updateNoForeignKeyChecks(sql1);

            PositionDocking positionDocking = EruptSpringUtil.getBean(PositionDocking.class);
            positionDocking.setBId(yard.getId());
            positionDocking.setPositionId(yard.getPositionId());
            positionDocking.setPositionType("5");
            eruptDao.merge(positionDocking);
        }

        JSONObject inputData = new JSONObject();
        inputData.set("clazz", "Yard");
        inputData.set("insertData",yard);
        EruptRemoteUtils.doPost(remoteProxyService.getMainServerUrl() + "/erupt-api/exchange/data/exchange", inputData, Map.class);

    }

    @Override
    public void afterUpdate(Yard yard) {
        String isPush = eruptPlatformService.getOption(YardDataProxy.IS_PUSH).getAsString() ;
        if (isPush.equals("true")) {
            if (StringUtils.isNotEmpty(yard.getOrgCode())) {
                String token = DaliangangContext.getToken();
                positionDockingHttpHandler.updateHttpDocking(yard, "5", yard.getOrgCode(), yard.getId(), token);
                log.info("推送修改堆场数据成功 ->  token=" + token);
            }
        }
        JSONObject inputData = new JSONObject();
        inputData.set("clazz", "Yard");
        inputData.set("insertData",yard);
        EruptRemoteUtils.doPost(remoteProxyService.getMainServerUrl() + "/erupt-api/exchange/data/exchange", inputData, Map.class);
    }

    @Override
    public void beforeAdd(Yard yard) {
        if (ObjectUtils.isNotEmpty(yard.getPositionId())) {
            // 数据转换
            Gson gson = GsonFactory.getGson();
            Map map =new HashMap();
            JSONArray objects = JSON.parseArray(yard.getMap());
            // JSONObject jsonObject = JSON.parseObject(warehouse.getMap());
            map.put("map", objects);
            String json = JSON.toJSONString(map);
            DockingForm positionDockingForm = gson.fromJson(json, DockingForm.class);
            String listToJsonString = "";
            // 处理风控数据中经纬度数据
            // 判断是否为圆
            if (positionDockingForm.getMap().get(0).getRy() != 0.0) {
                Map mapY = new HashMap();
                mapY.put("type","circle");
                mapY.put("lng",positionDockingForm.getMap().get(0).getHt().get(0).getLng());
                mapY.put("lat",positionDockingForm.getMap().get(0).getHt().get(0).getLat());
                mapY.put("radius",positionDockingForm.getMap().get(0).getRy());
                listToJsonString = gson.toJson(mapY);
            } else {
                Map mapD = new HashMap();
                mapD.put("type","polygon");
                mapD.put("points",positionDockingForm.getMap().get(0).getHt());
                listToJsonString = gson.toJson(mapD);
            }
            yard.setMap(listToJsonString);
        }
    }

    @Override
    public void afterDelete(Yard yard) {
        // 同步保存每日储量上报
    //    DcReserveReporting positionDocking = EruptSpringUtil.getBean(DcReserveReporting.class);
        DcReserveReporting reserveReporting = EruptDaoUtils.selectOne("select * from tb_dc_reserve_reporting where yard_num = " + yard.getId(), DcReserveReporting.class);
        if(reserveReporting != null){
            eruptDao.getEntityManager().remove(eruptDao.getEntityManager().merge(reserveReporting));
        }
        String token = DaliangangContext.getToken();
        positionDockingHttpHandler.deleteHttpDocking(yard, "5", yard.getOrgCode(), yard.getId(), token);

    }

    @Override
    public void excelImport(Object workbook) {
            // 删除表数据
            String sql1 = "truncate table tb_dc_reserve_reporting";
            EruptDaoUtils.updateNoForeignKeyChecks(sql1);

    }
}
