# 端口配置指南

## 🔧 端口配置说明

### 端口变量说明

| 变量名 | 说明 | 默认值 | 示例 |
|--------|------|--------|------|
| `APP_PORT` | 外部访问端口（宿主机端口） | 8080 | 19999 |
| `SERVER_PORT` | 应用内部端口（容器内端口） | 8080 | 8080 |

### 配置方式

#### 方式一：只修改外部端口（推荐）
```bash
# .env 文件配置
APP_PORT=19999      # 外部访问端口
SERVER_PORT=8080    # 内部端口保持8080

# 访问地址
http://localhost:19999
```

#### 方式二：同时修改内外部端口
```bash
# .env 文件配置
APP_PORT=19999      # 外部访问端口
SERVER_PORT=19999   # 内部端口也改为19999

# 访问地址
http://localhost:19999
```

## 🚀 重启服务

修改端口配置后，需要重启Docker服务：

### 方法一：使用脚本重启
```bash
# Windows
docker-start.bat

# Linux/Mac
./docker-start.sh
```

### 方法二：手动重启
```bash
# 停止服务
docker-compose down

# 重新启动
docker-compose up -d

# 查看服务状态
docker-compose ps
```

## 🔍 故障排除

### 1. 端口被占用
```bash
# 检查端口占用
netstat -tulpn | grep :19999

# Windows检查端口
netstat -ano | findstr :19999

# 如果端口被占用，可以：
# - 停止占用端口的程序
# - 或者修改为其他端口
```

### 2. 服务无法访问
```bash
# 检查容器状态
docker-compose ps

# 查看应用日志
docker-compose logs daliangang-app

# 检查端口映射
docker port daliangang-workbench
```

### 3. 健康检查失败
```bash
# 检查健康检查配置
# 确保健康检查URL使用正确的端口
curl -f http://localhost:19999/actuator/health
```

## 📝 配置示例

### 示例1：使用19999端口
```bash
# .env 文件
APP_PORT=19999
SERVER_PORT=8080

# 访问地址
http://localhost:19999
```

### 示例2：使用自定义端口
```bash
# .env 文件
APP_PORT=9090
SERVER_PORT=8080

# 访问地址
http://localhost:9090
```

### 示例3：内外部端口一致
```bash
# .env 文件
APP_PORT=19999
SERVER_PORT=19999

# 访问地址
http://localhost:19999
```

## ⚠️ 注意事项

1. **防火墙设置**：确保防火墙允许新端口的访问
2. **代理配置**：如果使用反向代理，需要更新代理配置
3. **健康检查**：确保健康检查URL使用正确的端口
4. **文档更新**：更新相关文档中的端口信息

## 🔗 相关文件

- `docker-compose.yml` - Docker Compose配置
- `.env` - 环境变量配置
- `workbench/src/main/resources/application.yml` - Spring Boot配置
