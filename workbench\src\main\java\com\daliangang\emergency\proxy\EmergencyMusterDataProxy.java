/*
* Copyright © 2020-2035 yundingyun All rights reserved.
*/
package com.daliangang.emergency.proxy;

import cn.hutool.json.JSONObject;
import com.daliangang.core.DaliangangContext;
import com.google.gson.internal.LinkedTreeMap;
import lombok.*;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.stereotype.*;
import xyz.erupt.annotation.fun.*;
import com.daliangang.emergency.entity.*;
import xyz.erupt.common.http.EruptRemoteUtils;
import xyz.erupt.toolkit.remote.RemoteProxyService;
import xyz.erupt.toolkit.remote.RemoteQuery;
import xyz.erupt.upms.model.EruptUser;
import xyz.erupt.upms.service.EruptUserService;

import javax.annotation.*;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

@Service
public class EmergencyMusterDataProxy implements DataProxy<EmergencyMuster> {

    @Resource
    private RemoteProxyService remoteProxyService;
    @Resource
    private EruptUserService eruptUserService;

    @Override
    public void addBehavior(EmergencyMuster emergencyMuster) {

        if (ObjectUtils.isNotEmpty(emergencyMuster.getCompany())) {
            List<LinkedTreeMap> enterprise = remoteProxyService.queryEntity("Enterprise", RemoteQuery.builder().condition("orgCode", emergencyMuster.getCompany()));
            if (ObjectUtils.isNotEmpty(enterprise)) {
                //  Integer portArea1 = (int) Float.parseFloat((String) enterprise.get(0).get("portArea"));
                //   String portArea = String.valueOf(enterprise.get(0).get("portArea"));
                String portArea=new BigDecimal(enterprise.get(0).get("portArea").toString()).stripTrailingZeros().toPlainString();
                emergencyMuster.setPortArea(portArea);
            }
        }

    }

    @Override
    public void afterAdd(EmergencyMuster emergencyMuster) {
        //推送数据
        JSONObject inputData = new JSONObject();
        inputData.set("clazz", "EmergencyMuster");
        inputData.set("insertData",emergencyMuster);
        EruptRemoteUtils.doPost(remoteProxyService.getMainServerUrl() + "/erupt-api/exchange/data/exchange", inputData, Map.class);
    }

    @Override
    public void afterUpdate(EmergencyMuster emergencyMuster) {
        //推送数据
        JSONObject inputData = new JSONObject();
        inputData.set("clazz", "EmergencyMuster");
        inputData.set("insertData",emergencyMuster);
        EruptRemoteUtils.doPost(remoteProxyService.getMainServerUrl() + "/erupt-api/exchange/data/exchange", inputData, Map.class);
    }

    @Override
    public void searchCondition(Map<String, Object> condition) {
        if(!DaliangangContext.isDepartmentUser()){
            EruptUser user = eruptUserService.getCurrentEruptUser();
            if (user != null && user.getEruptOrg() != null)
                condition.put("company", user.getEruptOrg().getCode());
        }
    }
}
