/*
 * Copyright © 2020-2035 yundingyun All rights reserved.
 */
package com.daliangang.device.proxy;

import cn.hutool.json.JSONObject;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.daliangang.core.DaliangangContext;
import com.daliangang.device.entity.LoadingDock;
import com.daliangang.device.entity.PositionDocking;
import com.daliangang.device.form.DockingForm;
import com.daliangang.device.operation.PositionDockingHttpHandler;
import com.google.gson.Gson;
import com.google.gson.internal.LinkedTreeMap;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import xyz.erupt.annotation.fun.DataProxy;
import xyz.erupt.common.http.EruptRemoteUtils;
import xyz.erupt.core.config.GsonFactory;
import xyz.erupt.core.util.EruptSpringUtil;
import xyz.erupt.jpa.dao.EruptDao;
import xyz.erupt.toolkit.remote.RemoteProxyService;
import xyz.erupt.toolkit.remote.RemoteQuery;
import xyz.erupt.toolkit.utils.EruptDaoUtils;
import xyz.erupt.upms.service.EruptPlatformService;

import javax.annotation.Resource;
import javax.transaction.Transactional;
import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
@Slf4j
public class LoadingDockDataProxy implements DataProxy<LoadingDock> {
    @Resource
    private RemoteProxyService remoteProxyService;
    @Resource
    private PositionDockingHttpHandler positionDockingHttpHandler;
    @Resource
    private EruptDao eruptDao;

    @Resource
    private EruptPlatformService eruptPlatformService ;

    public static final String IS_PUSH = "IS_PUSH" ;

    @Override
    public void addBehavior(LoadingDock loadingDock) {
        if (ObjectUtils.isNotEmpty(loadingDock.getCompany())) {
            List<LinkedTreeMap> enterprise = remoteProxyService.queryEntity("Enterprise", RemoteQuery.builder().condition("orgCode", loadingDock.getCompany()));
            if (ObjectUtils.isNotEmpty(enterprise)) {
                //  Integer portArea1 = (int) Float.parseFloat((String) enterprise.get(0).get("portArea"));
                //   String portArea = String.valueOf(enterprise.get(0).get("portArea"));
                String portArea=new BigDecimal(enterprise.get(0).get("portArea").toString()).stripTrailingZeros().toPlainString();
                loadingDock.setPortArea(portArea);
            }
        }
    }

    @Transactional
    @Override
    public void afterAdd(LoadingDock loadingDock) {
        String isPush = eruptPlatformService.getOption(LoadingDockDataProxy.IS_PUSH).getAsString() ;
        if (isPush.equals("true")) {
            if (StringUtils.isNotEmpty(loadingDock.getOrgCode()) && ObjectUtils.isEmpty(loadingDock.getPositionId())) {
                String token = DaliangangContext.getToken();
                positionDockingHttpHandler.AddHttpDocking(loadingDock, "3", loadingDock.getOrgCode(), token);
                log.info("推送装卸台数据成功 ->  token=" + token);

            }
        }
        // 保存导入的三方地点关系
        if (ObjectUtils.isNotEmpty(loadingDock.getPositionId())) {
            // 删除中间表数据
            String sql1 = "delete from tb_position_docking where position_type = '3' and b_id = "+loadingDock.getId();
            EruptDaoUtils.updateNoForeignKeyChecks(sql1);

            PositionDocking positionDocking = EruptSpringUtil.getBean(PositionDocking.class);
            positionDocking.setBId(loadingDock.getId());
            positionDocking.setPositionId(loadingDock.getPositionId());
            positionDocking.setPositionType("3");
            eruptDao.merge(positionDocking);
        }

        //推送数据
        JSONObject inputData = new JSONObject();
        inputData.set("clazz", "LoadingDock");
        inputData.set("insertData",loadingDock);
        EruptRemoteUtils.doPost(remoteProxyService.getMainServerUrl() + "/erupt-api/exchange/data/exchange", inputData, Map.class);
    }

    @Override
    public void afterUpdate(LoadingDock loadingDock) {
        String isPush = eruptPlatformService.getOption(LoadingDockDataProxy.IS_PUSH).getAsString() ;
        if (isPush.equals("true")) {
            if (StringUtils.isNotEmpty(loadingDock.getOrgCode())) {
                String token = DaliangangContext.getToken();
                positionDockingHttpHandler.updateHttpDocking(loadingDock, "3", loadingDock.getOrgCode(), loadingDock.getId(), token);
                log.info("推送修改装卸台数据成功 ->  token=" + token);

            }
        }
        //推送数据
        JSONObject inputData = new JSONObject();
        inputData.set("clazz", "LoadingDock");
        inputData.set("insertData",loadingDock);
        EruptRemoteUtils.doPost(remoteProxyService.getMainServerUrl() + "/erupt-api/exchange/data/exchange", inputData, Map.class);
    }

    @Override
    public void beforeAdd(LoadingDock loadingDock) {
        if (ObjectUtils.isNotEmpty(loadingDock.getPositionId())) {
            // 数据转换
            Gson gson = GsonFactory.getGson();
            Map map =new HashMap();
            JSONArray objects = JSON.parseArray(loadingDock.getMap());
            // JSONObject jsonObject = JSON.parseObject(warehouse.getMap());
            map.put("map", objects);
            String json = JSON.toJSONString(map);
            DockingForm positionDockingForm = gson.fromJson(json, DockingForm.class);
            String listToJsonString = "";
            // 处理风控数据中经纬度数据
            // 判断是否为圆
            if (positionDockingForm.getMap().get(0).getRy() != 0.0) {
                Map mapY = new HashMap();
                mapY.put("type","circle");
                mapY.put("lng",positionDockingForm.getMap().get(0).getHt().get(0).getLng());
                mapY.put("lat",positionDockingForm.getMap().get(0).getHt().get(0).getLat());
                mapY.put("radius",positionDockingForm.getMap().get(0).getRy());
                listToJsonString = gson.toJson(mapY);
            } else {
                Map mapD = new HashMap();
                mapD.put("type","polygon");
                mapD.put("points",positionDockingForm.getMap().get(0).getHt());
                listToJsonString = gson.toJson(mapD);
            }
            loadingDock.setMap(listToJsonString);
        }
    }

    @Override
    public void afterDelete(LoadingDock loadingDock) {
        String token = DaliangangContext.getToken();
        positionDockingHttpHandler.deleteHttpDocking(loadingDock, "3", loadingDock.getOrgCode(), loadingDock.getId(), token);
    }
}
