/*
 * Copyright © 2020-2035 yundingyun All rights reserved.
 */
package com.daliangang.workbench.operation;

import com.daliangang.workbench.entity.EmployeeInformation;
import com.daliangang.workbench.entity.Enterprise;
import org.springframework.stereotype.Service;
import xyz.erupt.annotation.fun.OperationHandler;
import xyz.erupt.jpa.dao.EruptDao;
import xyz.erupt.toolkit.utils.AssertUtils;
import xyz.erupt.toolkit.utils.SqlUtils;
import xyz.erupt.upms.model.EruptUser;
import xyz.erupt.upms.service.EruptUserService;

import javax.annotation.Resource;
import javax.transaction.Transactional;
import java.util.List;

@Service
public class EnterpriseEnabledHandler implements OperationHandler<Enterprise, Void> {

    @Resource
    private EruptDao eruptDao;

    @Resource
    private EruptUserService eruptUserService;

    @Override
    @Transactional
    public String exec(List<Enterprise> data, Void unused, String[] param) {
        for (Enterprise enterprise : data) {
            boolean state = param[0].equals("on");
            enterprise.setState(state);
            eruptDao.merge(enterprise);

            EruptUser adminUser = eruptDao.queryEntity(EruptUser.class, "account=" + SqlUtils.wrapStr(enterprise.getAdministrator()));
            AssertUtils.notNull(adminUser, "没有这个用户");
            adminUser.setStatus(state);
            eruptDao.merge(adminUser);

            //重置一下缓存
            eruptUserService.flushEruptUserCache(adminUser);

            //找到该企业下的所有员工
            List<EmployeeInformation> employees = eruptDao.queryEntityList(EmployeeInformation.class, "org_code=" + SqlUtils.wrapStr(enterprise.getOrgCode()));
            for (EmployeeInformation employee : employees) {
                EruptUser empUser = eruptDao.queryEntity(EruptUser.class, "account=" + SqlUtils.wrapStr(employee.getPhone()));
                AssertUtils.notNull(empUser, "没有这个用户");
                empUser.setStatus(state);
                eruptDao.merge(empUser);

                //重置一下缓存
                eruptUserService.flushEruptUserCache(empUser);
            }


        }
        return null;
    }
}
