# 大连港工作台 Docker 部署指南

## 📋 概述

本项目使用 Docker Compose 来部署大连港工作台应用及其依赖服务，包括：
- **MySQL 8.0** - 主数据库
- **Redis 7** - 缓存和会话存储
- **MinIO** - 对象存储服务
- **大连港工作台** - Spring Boot 应用

## 🚀 快速开始

### 1. 环境准备

确保已安装以下软件：
- Docker (版本 20.10+)
- Docker Compose (版本 2.0+)

### 2. 配置环境变量

1. 复制环境变量模板：
```bash
cp .env.example .env
```

2. 编辑 `.env` 文件，修改以下重要配置：
```bash
# 修改数据库密码
MYSQL_ROOT_PASSWORD=your_strong_password
MYSQL_PASSWORD=your_user_password

# 修改Redis密码
REDIS_PASSWORD=your_redis_password

# 修改管理员密码
SPRING_SECURITY_USER_PASSWORD=your_admin_password

# 修改MinIO密钥
MINIO_ACCESS_KEY=your_minio_access_key
MINIO_SECRET_KEY=your_minio_secret_key
```

### 3. 启动服务

#### 方式一：使用启动脚本（推荐）
```bash
chmod +x docker-start.sh
./docker-start.sh
```

#### 方式二：手动启动
```bash
# 启动所有服务
docker-compose up -d

# 查看服务状态
docker-compose ps

# 查看日志
docker-compose logs -f
```

### 4. 访问服务

启动成功后，可以通过以下地址访问：

- **大连港工作台**: http://localhost:8080
- **MinIO控制台**: http://localhost:9001
- **MySQL**: localhost:3306
- **Redis**: localhost:6379

## 🛠️ 常用命令

### 服务管理
```bash
# 启动服务
docker-compose up -d

# 停止服务
docker-compose down

# 重启服务
docker-compose restart

# 重新构建并启动
docker-compose up --build -d

# 查看服务状态
docker-compose ps

# 查看服务日志
docker-compose logs -f [service_name]
```

### 数据管理
```bash
# 备份MySQL数据
docker-compose exec mysql mysqldump -u root -p daliangang > backup.sql

# 恢复MySQL数据
docker-compose exec -T mysql mysql -u root -p daliangang < backup.sql

# 进入MySQL容器
docker-compose exec mysql mysql -u root -p

# 进入Redis容器
docker-compose exec redis redis-cli -a your_redis_password
```

### 应用管理
```bash
# 查看应用日志
docker-compose logs -f daliangang-app

# 进入应用容器
docker-compose exec daliangang-app bash

# 重启应用
docker-compose restart daliangang-app
```

## 📁 目录结构

```
.
├── docker-compose.yml          # Docker Compose 配置文件
├── .env                        # 环境变量配置
├── docker-start.sh            # 启动脚本
├── docker/
│   ├── mysql/
│   │   ├── conf.d/
│   │   │   └── my.cnf         # MySQL 配置文件
│   │   └── init/
│   │       └── 01-init.sql    # 数据库初始化脚本
│   └── redis/
│       └── redis.conf         # Redis 配置文件
└── workbench/
    └── Dockerfile             # 应用 Dockerfile
```

## 🔧 配置说明

### 环境变量说明

| 变量名 | 说明 | 默认值 |
|--------|------|--------|
| `APP_PORT` | 应用端口 | 8080 |
| `MYSQL_PORT` | MySQL端口 | 3306 |
| `REDIS_PORT` | Redis端口 | 6379 |
| `MINIO_PORT` | MinIO端口 | 9000 |
| `MYSQL_ROOT_PASSWORD` | MySQL root密码 | - |
| `MYSQL_PASSWORD` | MySQL用户密码 | - |
| `REDIS_PASSWORD` | Redis密码 | - |
| `SPRING_SECURITY_USER_PASSWORD` | 管理员密码 | - |

### 数据持久化

以下数据会持久化存储：
- MySQL数据：`mysql_data` 卷
- Redis数据：`redis_data` 卷
- MinIO数据：`minio_data` 卷
- 应用日志：`app_logs` 卷
- 上传文件：`app_uploads` 卷

## 🔒 安全建议

1. **修改默认密码**：确保所有服务使用强密码
2. **网络隔离**：生产环境建议使用自定义网络
3. **端口限制**：仅暴露必要的端口
4. **定期备份**：定期备份数据库和重要文件
5. **监控日志**：定期检查应用和服务日志

## 🐛 故障排除

### 常见问题

1. **端口冲突**
   ```bash
   # 检查端口占用
   netstat -tulpn | grep :8080
   
   # 修改 .env 文件中的端口配置
   ```

2. **内存不足**
   ```bash
   # 检查Docker内存使用
   docker stats
   
   # 调整应用内存配置
   JAVA_OPTS="-Xms256m -Xmx512m"
   ```

3. **数据库连接失败**
   ```bash
   # 检查MySQL服务状态
   docker-compose logs mysql
   
   # 检查网络连接
   docker-compose exec daliangang-app ping mysql
   ```

4. **应用启动失败**
   ```bash
   # 查看详细日志
   docker-compose logs daliangang-app
   
   # 检查配置文件
   docker-compose exec daliangang-app cat /app/application.yml
   ```

### 日志查看

```bash
# 查看所有服务日志
docker-compose logs

# 查看特定服务日志
docker-compose logs mysql
docker-compose logs redis
docker-compose logs minio
docker-compose logs daliangang-app

# 实时查看日志
docker-compose logs -f daliangang-app
```

## 📞 技术支持

如遇到问题，请：
1. 查看相关服务日志
2. 检查配置文件
3. 参考故障排除部分
4. 联系技术支持团队
