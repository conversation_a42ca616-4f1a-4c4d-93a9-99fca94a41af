package com.daliangang.datascreen.riskboard.controller;

import com.daliangang.datascreen.riskboard.entity.response.*;
import com.daliangang.datascreen.riskboard.service.RiskBoardService;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import xyz.erupt.core.annotation.EruptRouter;

import javax.annotation.Resource;
import java.util.List;

/**
 * @Author: chongmenglin
 * @Date: 2024/11/7 16:21
 */
@RestController
public class RiskBoardController {
    @Resource
    private RiskBoardService riskBoardService;

    /**
     * 获取风险类型
     * @return
     */
    @RequestMapping("/risk/board/riskType")
    @EruptRouter(verifyType = EruptRouter.VerifyType.LOGIN)
    public RiskTypeResponse getRiskTypeNum(@RequestParam(required = false) String orgCode){
        return riskBoardService.getRiskTypeNum();
    }

    /**
     * 获取企业风险表格
     * @return
     */
    @RequestMapping("/risk/board/riskForm")
    @EruptRouter(verifyType = EruptRouter.VerifyType.LOGIN)
    public List<RiskTypeForm> getRiskTypeForm(@RequestParam(required = false) String orgCode){
        return riskBoardService.getRiskTypeForm();
    }

    /**
     * 当日作业数量
     * @param orgCode
     * @return
     */
    @RequestMapping("/risk/board/workStatistics")
    @EruptRouter(verifyType = EruptRouter.VerifyType.LOGIN)
    public WorkTypeNum getWorkStatistics(@RequestParam(required = false) String orgCode){
        return riskBoardService.getWorkStatistics(orgCode);
    }

    /**
     * 当日作业详情
     * @return
     */
    @RequestMapping("/risk/board/workDetails")
    @EruptRouter(verifyType = EruptRouter.VerifyType.LOGIN)
    public List<WorkDetailsResponse> getDailyWorkDetails(){
        return riskBoardService.getDailyWorkDetails();
    }

    /**
     * 当日前五作业单位
     * @return
     */
    @RequestMapping("/risk/board/workStatistics/top")
    @EruptRouter(verifyType = EruptRouter.VerifyType.LOGIN)
    public List<WorkTopOrgResponse> getTopOrgWorkStatistics(){
        return riskBoardService.getTopOrgWorkStatistics();
    }
}
