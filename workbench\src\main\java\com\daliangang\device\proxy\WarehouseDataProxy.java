/*
 * Copyright © 2020-2035 yundingyun All rights reserved.
 */
package com.daliangang.device.proxy;

import cn.hutool.json.JSONObject;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.daliangang.core.DaliangangContext;
import com.daliangang.device.entity.PositionDocking;
import com.daliangang.device.entity.Warehouse;
import com.daliangang.device.form.DockingForm;
import com.daliangang.device.operation.PositionDockingHttpHandler;
import com.daliangang.majorisk.entity.CkReserveReporting;
import com.google.gson.Gson;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import xyz.erupt.annotation.fun.DataProxy;
import xyz.erupt.common.http.EruptRemoteUtils;
import xyz.erupt.core.config.GsonFactory;
import xyz.erupt.core.util.EruptSpringUtil;
import xyz.erupt.jpa.dao.EruptDao;
import xyz.erupt.toolkit.remote.RemoteProxyService;
import xyz.erupt.toolkit.utils.EruptDaoUtils;
import xyz.erupt.upms.service.EruptPlatformService;

import javax.annotation.Resource;
import javax.transaction.Transactional;
import java.util.HashMap;
import java.util.Map;

@Service
@Slf4j
public class WarehouseDataProxy implements DataProxy<Warehouse> {
    @Resource
    private PositionDockingHttpHandler positionDockingHttpHandler;
    @Resource
    private EruptDao eruptDao;

    @Resource
    private RemoteProxyService remoteProxyService;

    @Resource
    private EruptPlatformService eruptPlatformService ;

    public static final String IS_PUSH = "IS_PUSH" ;

    @Transactional
    @Override
    public void afterAdd(Warehouse warehouse) {
        // 同步保存每日储量上报


        CkReserveReporting ckReserveReporting = EruptSpringUtil.getBean(CkReserveReporting.class);
        ckReserveReporting.setCompany(warehouse.getCompany());
        ckReserveReporting.setOrgCode(warehouse.getCompany());
        ckReserveReporting.setYardNum(String.valueOf(warehouse.getId()));
        eruptDao.merge(ckReserveReporting);

        // 调用三方接口

        String isPush = eruptPlatformService.getOption(WarehouseDataProxy.IS_PUSH).getAsString() ;
        if (isPush.equals("true")) {
            if (StringUtils.isNotEmpty(warehouse.getOrgCode()) && ObjectUtils.isEmpty(warehouse.getPositionId())) {

                String token = DaliangangContext.getToken();
                positionDockingHttpHandler.AddHttpDocking(warehouse, "6", warehouse.getOrgCode(), token);
                log.info("推送仓库数据成功 ->  token=" + token);

            }
        }
        // 保存导入的三方地点关系
        if (ObjectUtils.isNotEmpty(warehouse.getPositionId())) {
            // 删除中间表数据
            String sql1 = "delete from tb_position_docking where position_type = '6' and b_id = "+warehouse.getId();
            EruptDaoUtils.updateNoForeignKeyChecks(sql1);

            PositionDocking positionDocking = EruptSpringUtil.getBean(PositionDocking.class);
            positionDocking.setBId(warehouse.getId());
            positionDocking.setPositionId(warehouse.getPositionId());
            positionDocking.setPositionType("6");
            eruptDao.merge(positionDocking);
        }

        JSONObject inputData = new JSONObject();
        inputData.set("clazz", "Warehouse");
        inputData.set("insertData",warehouse);
        EruptRemoteUtils.doPost(remoteProxyService.getMainServerUrl() + "/erupt-api/exchange/data/exchange", inputData, Map.class);

    }

    @Override
    public void afterUpdate(Warehouse warehouse) {
        String isPush = eruptPlatformService.getOption(WarehouseDataProxy.IS_PUSH).getAsString() ;
        if (isPush.equals("true")) {
            if (StringUtils.isNotEmpty(warehouse.getOrgCode())) {
                String token = DaliangangContext.getToken();
                positionDockingHttpHandler.updateHttpDocking(warehouse, "6", warehouse.getOrgCode(), warehouse.getId(), token);
                log.info("推送修改仓库数据成功 ->  token=" + token);

            }
        }
        JSONObject inputData = new JSONObject();
        inputData.set("clazz", "Warehouse");
        inputData.set("insertData",warehouse);
        EruptRemoteUtils.doPost(remoteProxyService.getMainServerUrl() + "/erupt-api/exchange/data/exchange", inputData, Map.class);
    }

    @Override
    public void beforeAdd(Warehouse warehouse) {
        if (ObjectUtils.isNotEmpty(warehouse.getPositionId())) {
            // 数据转换
            Gson gson = GsonFactory.getGson();
            Map map =new HashMap();
            JSONArray objects = JSON.parseArray(warehouse.getMap());
            // JSONObject jsonObject = JSON.parseObject(warehouse.getMap());
            map.put("map", objects);
            String json = JSON.toJSONString(map);
            DockingForm positionDockingForm = gson.fromJson(json, DockingForm.class);
            String listToJsonString = "";
            // 处理风控数据中经纬度数据
            // 判断是否为圆
            if (positionDockingForm.getMap().get(0).getRy() != 0.0) {
                Map mapY = new HashMap();
                mapY.put("type","circle");
                mapY.put("lng",positionDockingForm.getMap().get(0).getHt().get(0).getLng());
                mapY.put("lat",positionDockingForm.getMap().get(0).getHt().get(0).getLat());
                mapY.put("radius",positionDockingForm.getMap().get(0).getRy());
                listToJsonString = gson.toJson(mapY);
            } else {
                Map mapD = new HashMap();
                mapD.put("type","polygon");
                mapD.put("points",positionDockingForm.getMap().get(0).getHt());
                listToJsonString = gson.toJson(mapD);
            }
            warehouse.setMap(listToJsonString);
        }

    }


    @Override
    public void afterDelete(Warehouse warehouse) {
        // 同步保存每日储量上报
      //  CkReserveReporting positionDocking = EruptSpringUtil.getBean(CkReserveReporting.class);
        CkReserveReporting reserveReporting = EruptDaoUtils.selectOne("select * from tb_ck_reserve_reporting where yard_num = " + warehouse.getId(), CkReserveReporting.class);
        eruptDao.getEntityManager().remove(eruptDao.getEntityManager().merge(reserveReporting));

        String token = DaliangangContext.getToken();
        positionDockingHttpHandler.deleteHttpDocking(warehouse, "6", warehouse.getOrgCode(), warehouse.getId(), token);
    }


    @Override
    public void excelImport(Object workbook) {
        String sql1 = "truncate table tb_ck_reserve_reporting";
        EruptDaoUtils.updateNoForeignKeyChecks(sql1);
    }
}
