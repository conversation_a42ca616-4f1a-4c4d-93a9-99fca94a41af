package com.daliangang.device.controller;

import com.daliangang.device.entity.TankFarm;
import com.daliangang.device.entity.TankGroup;
import com.google.gson.internal.LinkedTreeMap;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import xyz.erupt.core.annotation.EruptRouter;
import xyz.erupt.core.module.MetaUserinfo;
import xyz.erupt.core.view.EruptApiModel;
import xyz.erupt.jpa.dao.EruptDao;
import xyz.erupt.toolkit.remote.RemoteProxyService;
import xyz.erupt.toolkit.utils.EruptDaoUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @since :2023/4/18:13:49
 */
@RestController
public class TankGroupController {
    @Resource
    private EruptDao eruptDao;
    @Resource
    private RemoteProxyService remoteProxyService;

    /**
     * 获取企业罐组管理
     * @param
     * @return
     */
    @RequestMapping("erupt-api/get/tankGroup")
    @EruptRouter(verifyType = EruptRouter.VerifyType.LOGIN)
    public EruptApiModel getPortareaName() {
        MetaUserinfo remoteUserInfo = remoteProxyService.getRemoteUserInfo();

//        Enterprise enterprise = EruptDaoUtils.selectOne("select * from tb_enterprise where org_code ='" +remoteUserInfo.getOrg()+"'", Enterprise.class);
//        if (ObjectUtils.isEmpty(enterprise)) {
//            return EruptApiModel.successApi();
//        }
        List<TankGroup> tankGroups = EruptDaoUtils.selectOnes("select * from tb_tank_group where org_code='" + remoteUserInfo.getOrg()+"'", TankGroup.class);
        if (ObjectUtils.isNotEmpty(tankGroups)) {
            List<LinkedTreeMap> list1 = new ArrayList<>();
            tankGroups.forEach(v->{
                LinkedTreeMap map = new LinkedTreeMap();
                map.put("code",v.getId());
                map.put("name",v.getTankGroup());
                list1.add(map);
            });

            return EruptApiModel.successApi(list1);
        }

        return EruptApiModel.successApi();
    }
}
