/*
 * Copyright © 2020-2035 yundingyun All rights reserved.
 */
package com.daliangang.safedaily.entity;

import com.daliangang.safedaily.operation.MSDSBatchUploadHandler;
import com.daliangang.safedaily.proxy.MSDSDataProxy;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import xyz.erupt.annotation.Erupt;
import xyz.erupt.annotation.EruptField;
import xyz.erupt.annotation.sub_erupt.Power;
import xyz.erupt.annotation.sub_erupt.RowOperation;
import xyz.erupt.annotation.sub_field.Edit;
import xyz.erupt.annotation.sub_field.EditType;
import xyz.erupt.annotation.sub_field.View;
import xyz.erupt.annotation.sub_field.sub_edit.AttachmentType;
import xyz.erupt.annotation.sub_field.sub_edit.InputType;
import xyz.erupt.annotation.sub_field.sub_edit.Search;
import xyz.erupt.jpa.model.MetaModelVo;
import xyz.erupt.toolkit.attachment.AttachmentDownloadHandler;
import xyz.erupt.toolkit.db.Comment;

import javax.persistence.Entity;
import javax.persistence.Table;
import java.io.File;

@Erupt(name = "MSDS", importTruncate = true, power = @Power(add = true, delete = true, export = false, importable = true, viewDetails = false, edit = true)
        , dataProxy = MSDSDataProxy.class
        , rowOperation = {
        @RowOperation(title = "下载附件", icon = "fa fa-download", operationHandler = AttachmentDownloadHandler.class, mode = RowOperation.Mode.SINGLE),
        @RowOperation(title = "批量上传", icon = "fa fa-upload", operationHandler = MSDSBatchUploadHandler.class, eruptClass = MSDSBatchUploadHandler.MSDSBatchUploadForm.class, mode = RowOperation.Mode.BUTTON)
})
@Table(name = "tb_m_s_d_s")
@Entity
@Getter
@Setter
@Comment("MSDS")
@ApiModel("MSDS")
@Slf4j
public class MSDS extends MetaModelVo {
    @EruptField(
            views = @View(title = "货种名称",width = "300px"),
            edit = @Edit(title = "货种名称", type = EditType.INPUT, search = @Search(vague = true), show = false,
                    inputType = @InputType))
    @Comment("货种名称")
    @ApiModelProperty("货种名称")
    private String name;

    @EruptField(
            views = @View(title = "货种文件",width = "300px"),
            edit = @Edit(title = "货种文件", type = EditType.ATTACHMENT, notNull = true,
                    attachmentType = @AttachmentType(type = AttachmentType.Type.BASE, maxLimit = 1, fileTypes = {".pdf"/*, ".doc", ".docx"*/})))
    @Comment("货种文件")
    @ApiModelProperty("货种文件")
    private String file;

    @SneakyThrows
    public static void main(String[] args) {
        File dir = new File("/Users/<USER>/Documents/公司日常管理/03 公司项目相关/大连港危险货物港区重大安全风险管控平台升级改造项目/系统初始导入数据/MSDS");
        for (File file : dir.listFiles()) {
            String name = file.getName().replace(".pdf", "");
            System.out.println(name + "\t" + "/MSDS/" + file.getName());
        }
    }
}
