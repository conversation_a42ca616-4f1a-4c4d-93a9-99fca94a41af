package com.daliangang.emergency.operation;


import com.daliangang.core.DaliangangContext;
import org.springframework.stereotype.Service;
import xyz.erupt.annotation.fun.PowerHandler;
import xyz.erupt.annotation.fun.PowerObject;


@Service
public class EmergencyPoolPowerHandler implements PowerHandler {

    @Override
    public void handler(PowerObject power) {
        /*判断当前用户是否为政府用户:只能查看*/
        if (DaliangangContext.isDepartmentUser()) {
            power.setAdd(false);
            power.setDelete(false);
            power.setEdit(false);
            power.setQuery(true);
            power.setViewDetails(true);
        }
        /*判断当前用户是否为企业用户*/
        if(DaliangangContext.isEnterpriseUser()){
            power.setAdd(true);
            power.setDelete(true);
            power.setEdit(true);
            power.setQuery(true);
            power.setViewDetails(true);
        }

    }
}
