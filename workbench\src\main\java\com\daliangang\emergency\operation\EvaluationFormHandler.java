/*
* Copyright © 2020-2035 yundingyun All rights reserved.
*/
package com.daliangang.emergency.operation;

import com.daliangang.emergency.entity.EvaluationForm;
import com.daliangang.emergency.entity.EvaluationManage;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.stereotype.Service;
import xyz.erupt.annotation.fun.OperationHandler;
import xyz.erupt.core.util.EruptUtil;
import xyz.erupt.jpa.dao.EruptDao;
import xyz.erupt.toolkit.utils.EruptDaoUtils;
import xyz.erupt.toolkit.utils.NotifyUtils;

import javax.annotation.Resource;
import javax.transaction.Transactional;
import java.util.List;

@Service
public class EvaluationFormHandler implements OperationHandler<EvaluationForm, Void> {
    @Resource
    EruptDao eruptDao;
    @Override
    @Transactional
   public String exec(List<EvaluationForm> data, Void unused, String[] param) {
        EvaluationForm evaluationForm = EruptDaoUtils.selectOne("select * from tb_evaluation_form ORDER BY id DESC LIMIT 0,1", EvaluationForm.class);

        if(ObjectUtils.isEmpty(evaluationForm)) {
            NotifyUtils.showErrorMsg("管理员尚未上传！");
        }
        String s = EruptUtil.findAttachmentProxy().fileDomain() + evaluationForm.getEvaluationForm();
        String url = "location.href='"+s+"'";
        return url;

	}



}
