package com.daliangang.majorisk.handler;

import org.springframework.stereotype.Service;
import xyz.erupt.annotation.fun.LinkByHandler;

@Service
public class MajorRiskTypeLinkByHandler implements LinkByHandler {

    @Override
    public String placeHolder(Object prop) {
        String code = prop.toString();
        if (code.equals("RISK_OF_POISONING__SUFFOCATION__FIRE_AND_EXPLOSION_IN_LOADING_AN"))     // 16
            return "majorRiskSix";
        else if (code.equals("RISK_OF_LEAKAGE__POISONING__FIRE_AND_EXPLOSION_IN_DANGEROUS_GOOD"))  // 13
            return "majorRiskTwo";
        else if (code.equals("RISK_OF_LEAKAGE__POISONING__FIRE_AND_EXPLOSION_IN_LOADING_AND_UN"))  // 14
            return "majorRiskThree";
        else if (code.equals("RISK_OF_LEAKAGE__POISONING__FIRE_AND_EXPLOSION_IN_THE_TANK_FARM_"))  //12
            return "majorRiskOne";
        else                                                                                       // 15
            return "majorRiskFour";
    }

}
