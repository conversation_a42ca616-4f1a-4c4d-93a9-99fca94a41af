/*
* Copyright © 2020-2035 yundingyun All rights reserved.
*/
package com.daliangang.safedaily.operation;

import cn.hutool.json.JSONObject;
import com.daliangang.safedaily.entity.Release;
import org.springframework.stereotype.Service;
import xyz.erupt.annotation.fun.OperationHandler;
import xyz.erupt.common.http.EruptRemoteUtils;
import xyz.erupt.jpa.dao.EruptDao;
import xyz.erupt.toolkit.remote.RemoteProxyService;
import xyz.erupt.toolkit.utils.NotifyUtils;

import javax.annotation.Resource;
import javax.transaction.Transactional;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

@Service
public class ReleaseEscalationHandler implements OperationHandler<Release, Void> {

    @Resource
    private RemoteProxyService remoteProxyService;

    @Resource
    private EruptDao eruptDao;

    @Override
    @Transactional
   public String exec(List<Release> data, Void unused, String[] param) {
        for (Release release:data) {
            release.setSubmitted(true);
            release.setUpdateTime(LocalDateTime.now());
            eruptDao.merge(release);

            JSONObject inputData = new JSONObject();
            inputData.set("clazz", "Release");
            inputData.set("insertData",release);
            EruptRemoteUtils.doPost(remoteProxyService.getMainServerUrl() + "/erupt-api/exchange/data/exchange", inputData, Map.class);
        }

        return NotifyUtils.getSuccessNotify("上报成功！");
	}

}
