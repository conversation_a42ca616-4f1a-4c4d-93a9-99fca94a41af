package com.daliangang.majorisk.form;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import xyz.erupt.annotation.Erupt;
import xyz.erupt.annotation.EruptField;
import xyz.erupt.annotation.sub_erupt.Power;
import xyz.erupt.annotation.sub_field.Edit;
import xyz.erupt.annotation.sub_field.EditType;
import xyz.erupt.annotation.sub_field.View;
import xyz.erupt.annotation.sub_field.sub_edit.InputType;
import xyz.erupt.annotation.sub_field.sub_edit.Search;
import xyz.erupt.jpa.model.BaseModel;
import xyz.erupt.toolkit.db.Comment;

import javax.persistence.Entity;

/**
 * <AUTHOR>
 * @since :2023/4/7:16:29
 */
@Erupt(name = "专（兼）职应急队伍", power = @Power(add = false, delete = false, export = false, importable = true, viewDetails = false, edit = false)
        , rowOperation = {})

@Getter
@Setter
@Comment("专（兼）职应急队伍")
@ApiModel("专（兼）职应急队伍")
public class FiveEmergencyTeamForm extends BaseModel {

    @EruptField(
            views = @View(title = "队伍名称"),
            edit = @Edit(title = "队伍名称", type = EditType.INPUT, search = @Search(vague = true), notNull = true,
                    inputType = @InputType))
    @Comment("队伍名称")
    @ApiModelProperty("队伍名称")
    private String teamName;


    @EruptField(
            views = @View(title = "人员规模"),
            edit = @Edit(title = "人员规模", type = EditType.INPUT, notNull = true,
                    inputType = @InputType))
    @Comment("人员规模")
    @ApiModelProperty("人员规模")
    private String personnelSize;


    @EruptField(
            views = @View(title = "负责人"),
            edit = @Edit(title = "负责人", type = EditType.INPUT, notNull = true,
                    inputType = @InputType))
    @Comment("负责人")
    @ApiModelProperty("负责人")
    private String personInCharge;

    @EruptField(
            views = @View(title = "值守电话"),
            edit = @Edit(title = "值守电话", type = EditType.INPUT, notNull = true,
                    inputType = @InputType))
    @Comment("值守电话")
    @ApiModelProperty("值守电话")
    private String contactNumber;

}
