/*
 * Copyright © 2020-2035 yundingyun All rights reserved.
 */
package com.daliangang.rndpub.operation;

import com.daliangang.rndpub.entity.Procedure;
import lombok.SneakyThrows;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import xyz.erupt.annotation.fun.OperationHandler;
import xyz.erupt.annotation.query.Condition;
import xyz.erupt.core.context.MetaDrill;
import xyz.erupt.core.invoke.DataProxyInvoke;
import xyz.erupt.core.service.EruptCoreService;
import xyz.erupt.core.service.EruptService;
import xyz.erupt.core.view.EruptModel;
import xyz.erupt.core.view.Page;
import xyz.erupt.core.view.TableQueryVo;
import xyz.erupt.excel.controller.EruptExcelController;
import xyz.erupt.excel.service.EruptExcelService;
import xyz.erupt.excel.util.ExcelUtil;
import xyz.erupt.jpa.dao.EruptDao;
import xyz.erupt.toolkit.db.EruptResultMap;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

@Service
public class ProcedureExportCheckItemsHandler implements OperationHandler<Procedure, Void> {


    @Resource
    private EruptExcelController eruptExcelController;


    @RestController
    public static class InspectionItemsExportController {

        @Resource
        private EruptService eruptService;

        @Resource
        private EruptDao eruptDao;

        @Resource
        private EruptExcelService dataFileService;

        @RequestMapping("/erupt-api/export/InspectionItems")
        //@EruptRouter(verifyType = EruptRouter.VerifyType.LOGIN)
        @SneakyThrows
        public void export(HttpServletRequest request, HttpServletResponse response, @RequestParam(name = "procedureId") long procedureId) {
//            String procedureId = request.getParameter("procedureId");
            TableQueryVo tableQueryVo = new TableQueryVo();
            tableQueryVo.setPageIndex(1);
            tableQueryVo.setDataExport(true);
            List<Condition> conditions = new ArrayList<>();
            conditions.add(new Condition("state", true));
            Procedure procedure = eruptDao.queryEntity(Procedure.class, "id=" + procedureId);
            EruptResultMap procedureQuery = new EruptResultMap();
            procedureQuery.put("id", "" + procedureId);
            procedureQuery.put("name", procedure.getName());
            conditions.add(new Condition("procedure", procedureQuery));
            EruptModel eruptModel = EruptCoreService.getErupt("InspectionItems");
            Optional.of(conditions).ifPresent(tableQueryVo::setCondition);
            Page page = eruptService.getEruptData(eruptModel, tableQueryVo, conditions);
            DateTimeFormatter dfDateTime = DateTimeFormatter.ofPattern("yyyy年MM月dd日HH时mm分");
            try (Workbook wb = dataFileService.exportExcel(eruptModel, page)) {
                DataProxyInvoke.invoke(eruptModel, (dataProxy -> dataProxy.excelExport(wb)));
                wb.write(ExcelUtil.downLoadFile(request, response, procedure.getName() +
                        dfDateTime.format(LocalDateTime.now()) + EruptExcelService.XLSX_FORMAT));
            }
        }
    }

    @Override
    @SneakyThrows
    public String exec(List<Procedure> data, Void unused, String[] param) {
        String url = "window.open('/erupt-api/export/InspectionItems?procedureId=" + MetaDrill.getDrillId() + "', '_blank')";
        return url;
    }
}
