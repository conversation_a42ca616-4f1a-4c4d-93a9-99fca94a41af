package com.daliangang.workbench.operation;

import com.daliangang.workbench.entity.EmployeeCertificateView;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.stereotype.Service;
import xyz.erupt.annotation.Erupt;
import xyz.erupt.annotation.EruptField;
import xyz.erupt.annotation.fun.OperationHandler;
import xyz.erupt.annotation.sub_field.Edit;
import xyz.erupt.annotation.sub_field.EditType;
import xyz.erupt.annotation.sub_field.View;
import xyz.erupt.annotation.sub_field.sub_edit.DateType;
import xyz.erupt.jpa.dao.EruptDao;
import xyz.erupt.jpa.model.BaseModel;
import xyz.erupt.toolkit.db.Comment;

import javax.annotation.Resource;
import javax.transaction.Transactional;
import java.util.List;

@Service
public class EmployeeCertificateValidityPeriodStartUpdateHandler implements OperationHandler<EmployeeCertificateView, EmployeeCertificateValidityPeriodStartUpdateHandler.ValidityPeriodStartUpdate> {

    @Erupt(name = "修改有效期开始")
    @Data
    public static class ValidityPeriodStartUpdate extends BaseModel {
        @EruptField(
                views = @View(title = "有效期开始"),
                edit = @Edit(title = "有效期开始", type = EditType.DATE, notNull = true,
                        dateType = @DateType))
        @Comment("有效期开始")
        @ApiModelProperty("有效期开始")
        private java.util.Date validityPeriodStart;
    }

    @Resource
    private EruptDao eruptDao ;

    @Transactional
    @Override
    public String exec(List<EmployeeCertificateView> data, EmployeeCertificateValidityPeriodStartUpdateHandler.ValidityPeriodStartUpdate date, String[] param) {
        for(EmployeeCertificateView view : data){
            view.setValidityPeriodStart(date.getValidityPeriodStart());
            eruptDao.mergeAndFlush(view);
        }
        return null ;
    }
}
