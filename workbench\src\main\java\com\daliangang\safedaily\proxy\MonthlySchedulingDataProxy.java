/*
* Copyright © 2020-2035 yundingyun All rights reserved.
*/
package com.daliangang.safedaily.proxy;

import com.daliangang.core.DaliangangContext;
import com.daliangang.safedaily.entity.MonthlyScheduling;
import org.springframework.stereotype.Service;
import xyz.erupt.annotation.fun.DataProxy;
import xyz.erupt.annotation.query.Condition;
import xyz.erupt.core.util.DateUtil;

import java.util.Date;
import java.util.List;
import java.util.Map;

@Service
public class MonthlySchedulingDataProxy implements DataProxy<MonthlyScheduling> {

    @Override
    public void addBehavior(MonthlyScheduling monthlyScheduling) {
        monthlyScheduling.setInspectionTime(new Date(System.currentTimeMillis()));
        monthlyScheduling.setReportMonth(DateUtil.getFormatDate(new java.util.Date(), DateUtil.YEAR_MONTH) );
    }

    @Override
    public String beforeFetch(List<Condition> conditions) {
        if (DaliangangContext.isDepartmentUser()) return "submitted=true";
        return null;
    }

    @Override
    public void searchCondition(Map<String, Object> condition) {
        condition.put("reportMonth", DateUtil.getFormatDate(new java.util.Date(), DateUtil.YEAR_MONTH));
//        condition.put("inspectionTime", DateUtil.getFormatDate(new java.util.Date(), DateUtil.DATE));
    }
}
