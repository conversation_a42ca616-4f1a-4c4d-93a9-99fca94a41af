package com.daliangang.emergency.controller;

import com.daliangang.emergency.entity.MaterialReserve;
import com.daliangang.emergency.form.DrillForm;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import xyz.erupt.core.annotation.EruptRouter;
import xyz.erupt.core.module.MetaUserinfo;
import xyz.erupt.core.view.EruptApiModel;
import xyz.erupt.toolkit.remote.RemoteProxyService;
import xyz.erupt.toolkit.utils.EruptDaoUtils;

import javax.annotation.Resource;
import javax.transaction.Transactional;
import java.util.List;

/**
 * <AUTHOR>
 * @since :2023/4/14:15:35
 */
@RestController
public class DrillController {

    @Resource
    private RemoteProxyService remoteProxyService;

    @RequestMapping("erupt-api/Drill/select")
    @EruptRouter(verifyType = EruptRouter.VerifyType.LOGIN)
    @Transactional
    public EruptApiModel selectMaterialReserve() {
        MetaUserinfo remoteUserInfo = remoteProxyService.getRemoteUserInfo();
        DrillForm drillForm = EruptDaoUtils.selectOne("SELECT COUNT(*) as num ,GROUP_CONCAT(name) as name from  tb_drill where date_format(create_time, '%Y') = YEAR(NOW()) and org_code ='" + remoteUserInfo.getOrg() + "'", DrillForm.class);
        return EruptApiModel.successApi(drillForm);
    }
}
