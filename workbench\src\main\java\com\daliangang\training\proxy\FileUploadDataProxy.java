package com.daliangang.training.proxy;

import com.daliangang.training.entity.FileUpload;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import xyz.erupt.annotation.fun.DataProxy;

/**
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Description:
 * @Date: Created in 2023/5/26 9:22
 * @Modified By
 */
@Service
@Slf4j
public class FileUploadDataProxy implements DataProxy<FileUpload> {

    @Override
    public void beforeAdd(FileUpload fileUpload) {
        fileUpload.setName(fileUpload.getFile());
    }
}
