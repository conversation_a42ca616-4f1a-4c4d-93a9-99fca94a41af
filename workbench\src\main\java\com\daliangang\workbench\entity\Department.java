/*
 * Copyright © 2020-2035 yundingyun All rights reserved.
 */
package com.daliangang.workbench.entity;

import cn.hutool.core.lang.RegexPool;
import com.daliangang.workbench.init.InitTemplate;
import com.daliangang.workbench.init.InitTemplateConvertHandler;
import com.daliangang.workbench.proxy.DepartmentDataProxy;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import xyz.erupt.annotation.Erupt;
import xyz.erupt.annotation.EruptField;
import xyz.erupt.annotation.expr.ExprBool;
import xyz.erupt.annotation.sub_erupt.Power;
import xyz.erupt.annotation.sub_erupt.Tree;
import xyz.erupt.annotation.sub_field.Edit;
import xyz.erupt.annotation.sub_field.EditType;
import xyz.erupt.annotation.sub_field.Readonly;
import xyz.erupt.annotation.sub_field.View;
import xyz.erupt.annotation.sub_field.sub_edit.ChoiceType;
import xyz.erupt.annotation.sub_field.sub_edit.InputType;
import xyz.erupt.annotation.sub_field.sub_edit.ReferenceTreeType;
import xyz.erupt.annotation.sub_field.sub_edit.Search;
import xyz.erupt.excel.template.ExcelTemplate;
import xyz.erupt.excel.template.ExcelTemplates;
import xyz.erupt.jpa.model.MetaModel;
import xyz.erupt.toolkit.db.Comment;
import xyz.erupt.toolkit.handler.EruptDictCodeChoiceFetchHandler;
import xyz.erupt.upms.model.auth.DataAuthHandler;
import xyz.erupt.upms.model.auth.OrgCodeRender;
import xyz.erupt.upms.model.template.EruptRoleTemplate;

import javax.persistence.*;
import java.util.Set;

@Erupt(name = "主管部门", power = @Power(add = true, delete = true, export = false, importable = true, viewDetails = true, edit = true)
        , tree = @Tree(id = "id", label = "name", pid = "parent.id")
        , dataProxy = DepartmentDataProxy.class)
@Table(name = "tb_department", uniqueConstraints = @UniqueConstraint(columnNames = "name"))
@Entity
@Getter
@Setter
@Comment("主管部门")
@ApiModel("主管部门")
@ExcelTemplates(@ExcelTemplate(erupt = Department.class, template = "init-template.xlsx", clazz = InitTemplate.class, handler = InitTemplateConvertHandler.class))
@Slf4j
@OrgCodeRender(render = false)
public class Department extends MetaModel implements ExprBool.ExprHandler, Readonly.ReadonlyHandler {

    @EruptField(
            views = @View(title = "账号", sortable = true),
            edit = @Edit(title = "账号", type = EditType.INPUT, search = @Search(vague = true), notNull = true, readonly = @Readonly(exprHandler = Department.class),
                    inputType = @InputType))
    @Comment("账号")
    @ApiModelProperty("账号")
    private String account;
    @EruptField(
            views = @View(title = "主管部门名称", sortable = true),
            edit = @Edit(title = "主管部门名称", type = EditType.INPUT, search = @Search(vague = true), notNull = true, readonly = @Readonly(exprHandler = Department.class),
                    inputType = @InputType))
    @Comment("主管部门名称")
    @ApiModelProperty("主管部门名称")
    private String name;

    @EruptField(
            views = @View(title = "所属港区"),
            edit = @Edit(title = "所属港区", type = EditType.CHOICE, search = @Search(vague = true), notNull = true, readonly = @Readonly(exprHandler = Department.class),
                    choiceType = @ChoiceType(fetchHandler = EruptDictCodeChoiceFetchHandler.class, fetchHandlerParams = "portArea")))
    @Comment("所属港区")
    @ApiModelProperty("所属港区")
    private String portArea;

    @EruptField(
            edit = @Edit(title = "上级部门", type = EditType.REFERENCE_TREE, readonly = @Readonly(exprHandler = Department.class),
                    referenceTreeType = @ReferenceTreeType(pid = "parent.id", label = "name")))
    @ManyToOne
    @Comment("上级部门")
    @ApiModelProperty("上级部门")
    private Department parent;

    @EruptField(
            views = @View(title = "手机号"),
            edit = @Edit(title = "手机号", type = EditType.INPUT, notNull = true,
                    inputType = @InputType(regex= RegexPool.MOBILE)))
    @Comment("手机号")
    @ApiModelProperty("手机号")
    private String phone ;

    @EruptField(
            views = @View(title = "角色", column = "name"),
            edit = @Edit(title = "角色", type = EditType.REFERENCE_TREE, notNull = true, readonly = @Readonly(exprHandler = Department.class),
                    referenceTreeType = @ReferenceTreeType(id = "id", label = "name")))
    @ManyToOne
    @Comment("角色")
    @ApiModelProperty("角色")
    private EruptRoleTemplate role;

    @EruptField
    private Long eruptOrgId;

    @EruptField(
            edit = @Edit(title = "下属企业", type = EditType.TAB_TABLE_ADD, readonly = @Readonly))
    @OneToMany(cascade = CascadeType.ALL, orphanRemoval = true)
    @ApiModelProperty("下属企业")
    @Transient
    private Set<Enterprise> enterprises;

//    @EruptField(
//            edit = @Edit(title = "主管部门用户", type = EditType.TAB_TABLE_ADD))
//    @OneToMany(cascade = CascadeType.ALL, orphanRemoval = true)
//    @ApiModelProperty("主管部门用户")
//    @Transient
//    private Set<EruptUser> users;

//    @EruptField(
//            edit = @Edit(title = "企业用户", type = EditType.TAB_TABLE_ADD))
//    @OneToMany(cascade = CascadeType.ALL, orphanRemoval = true)
//    @ApiModelProperty("企业用户")
//    @Transient
//    private Set<EruptRoleTemplateUser> enterpriseUsers;

    @EruptField(views = @View(title = "初始组织编码", show = false,
            ifRender = @ExprBool(exprHandler = DataAuthHandler.class)),
            edit = @Edit(title = "初始组织编码", show = false))
    private String initOrgCode;

    @Override
    public boolean handler(boolean expr, String[] params) {
        return false;
    }

    @Override
    public boolean add(boolean add, String[] params) {
        return edit(add, params);
    }

    @Override
    public boolean edit(boolean edit, String[] params) {
//        if (DaliangangContext.isEnterpriseUser()) return true;
//        if (DaliangangContext.isDepartmentAdmin()) return false;
//        if (DaliangangContext.isDepartmentArea()) return true;
//        return false;
        return true ;
    }
}
