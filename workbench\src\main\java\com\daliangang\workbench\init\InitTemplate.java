package com.daliangang.workbench.init;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.Sheet;
import xyz.erupt.excel.entity.CommonExcelObject;

/**
 * @Author：chb
 * @Package：com.daliangang.workbench.template
 * @Project：erupt
 * @name：InitTemplate
 * @Date：2023/3/5 17:25
 * @Filename：InitTemplate
 */
@Data
@Slf4j
public class InitTemplate extends CommonExcelObject {

    private String param1;
    private String param2;
    private String param3;
    private String param4;
    private String param5;

    private InitType type;


    /**
     * 检查表头
     *
     * @param sheet
     * @param rowIndex
     */
    @Override
    protected boolean checkHeaders(Sheet sheet, int rowIndex) {
        //根据表名决定哪个检查类
        String sheetName = sheet.getSheetName();
        if (sheetName.equalsIgnoreCase("基准角色")) {
            this.bindClass = InitRole.class;
            this.labelStartIndex = 1;
            this.dataStartIndex = 2;
            this.type = InitType.ROLE;
        } else if (sheetName.equalsIgnoreCase("权限模板")) {
            this.bindClass = InitRoleTemplate.class;
            this.labelStartIndex = 1;
            this.dataStartIndex = 2;
            this.type = InitType.ROLE_TEMPLATE;
        } else if (sheetName.equalsIgnoreCase("主管部门")) {
            this.bindClass = InitDepartment.class;
            this.labelStartIndex = 1;
            this.dataStartIndex = 2;
            this.type = InitType.DEPARTMENT;
        } else if (sheetName.equalsIgnoreCase("主管部门账号")) {
            this.bindClass = InitAccount.class;
            this.labelStartIndex = 1;
            this.dataStartIndex = 2;
            this.type = InitType.DEPT_ACCOUNT;
        } else if (sheetName.equalsIgnoreCase("初始岗位")) {
            this.bindClass = InitPost.class;
            this.labelStartIndex = 1;
            this.dataStartIndex = 2;
            this.type = InitType.POST;
        } else {
            this.bindClass = InitParams.class;
            this.labelStartIndex = 0;
            this.dataStartIndex = 1;
            this.type = InitType.OTHER;
        }

        return super.checkHeaders(sheet, this.labelStartIndex);
    }
}
