/*
 * Copyright © 2020-2035 yundingyun All rights reserved.
 */
package com.daliangang.majorisk.operation;

import com.daliangang.majorisk.entity.RiskDatabaseDepartment;
import org.springframework.stereotype.Service;
import xyz.erupt.annotation.fun.OperationHandler;
import xyz.erupt.jpa.dao.EruptDao;

import javax.annotation.Resource;
import javax.transaction.Transactional;
import java.util.List;

@Service
public class RiskDatabaseDeployHandler implements OperationHandler<RiskDatabaseDepartment, Void> {

    @Resource
    private EruptDao eruptDao;

    @Override
    @Transactional
    public String exec(List<RiskDatabaseDepartment> data, Void unused, String[] param) {
        for (RiskDatabaseDepartment database : data) {
            database.setState(Boolean.parseBoolean(param[0]));
            eruptDao.merge(database);
        }
        return null;
    }
}
