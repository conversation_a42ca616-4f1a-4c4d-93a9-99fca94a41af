package com.daliangang.majorisk.controller;

import cn.hutool.http.HttpGlobalConfig;
import cn.hutool.http.HttpUtil;
import com.daliangang.core.DaliangangContext;
import com.daliangang.device.form.CkReserveReportingForm;
import com.daliangang.majorisk.form.WeatherForm;
import com.daliangang.majorisk.form.WorkInfoForm;
import com.daliangang.majorisk.operation.FiveChecklistsMonitorHandler;
import com.daliangang.majorisk.sql.MajoriskWorkbenchSql;
import com.google.gson.Gson;
import com.google.gson.internal.LinkedTreeMap;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;
import xyz.erupt.common.http.EruptRemoteUtils;
import xyz.erupt.core.annotation.EruptRouter;
import xyz.erupt.core.module.MetaUserinfo;
import xyz.erupt.core.view.EruptApiModel;
import xyz.erupt.toolkit.remote.RemoteProxyService;
import xyz.erupt.toolkit.utils.EruptDaoUtils;
import xyz.erupt.toolkit.utils.SqlUtils;
import xyz.erupt.upms.model.EruptDict;
import xyz.erupt.upms.model.EruptDictItem;
import xyz.erupt.upms.service.EruptPlatformService;
import xyz.erupt.upms.util.EruptDictUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since :2023/4/27:15:38
 */
@RestController
@Slf4j
public class ReserveReportingController {
    @Resource
    private RemoteProxyService remoteProxyService;
    @Resource
    private MajoriskWorkbenchSql majoriskWorkbenchSql;

    @Resource
    private EruptPlatformService eruptPlatformService ;

    @Value("${xinz.gy}")
    private String gy;
    @Value("${xinz.sy}")
    private String sy;

    // 查询事故半径范围作业
    @RequestMapping("erupt-api/get/selectReserveReportingInfo")
    @EruptRouter(verifyType = EruptRouter.VerifyType.LOGIN)
    public EruptApiModel selectAccidentInformationInfo() {
        MetaUserinfo remoteUserInfo = remoteProxyService.getRemoteUserInfo();
        String orgCode = SqlUtils.wrapIn(remoteUserInfo.getAuth());
        // 查询系统三中在范围的数据
        String sql = majoriskWorkbenchSql.selectWorkNum(orgCode);
        List<WorkInfoForm> WorkInfoForm = EruptDaoUtils.selectOnes(sql, WorkInfoForm.class);
        return EruptApiModel.successApi(WorkInfoForm);
    }


    // 应急大屏每日储量上报
    @RequestMapping("erupt-api/get/selectReserveReportInfo/{orgCode}")
    @EruptRouter(verifyType = EruptRouter.VerifyType.LOGIN)
    public EruptApiModel selectReserveReportInfo(@PathVariable("orgCode") String orgCode) {
        List<CkReserveReportingForm> lists = new ArrayList();
        // 仓库数据
        EruptApiModel eruptApiModel = EruptRemoteUtils.doGet(remoteProxyService.getMainServerUrl() + "/erupt-api/get/selectWarehouseInfo", EruptApiModel.class);
        if (ObjectUtils.isNotEmpty(eruptApiModel.getData())) {
            List<LinkedTreeMap> data = (List<LinkedTreeMap>) eruptApiModel.getData();
            List list = new ArrayList();
            data.forEach(v -> {
                list.add(v.get("id"));
            });
            String ids = SqlUtils.wrapIn(list);

            // 仓库上报数据
            List<CkReserveReportingForm> ckReserveReportingListck = EruptDaoUtils.selectOnes("select id,yard_type as yardType,storage_goods as yardNow,yard_num as yardNum from tb_ck_reserve_reporting where   yard_num" + ids, CkReserveReportingForm.class);
            data.forEach(m -> {
                for (CkReserveReportingForm ckReserveReporting : ckReserveReportingListck) {
                    if (m.get("id").equals(ckReserveReporting.getYardNum())) {
                        ckReserveReporting.setCompany(String.valueOf(m.get("company")));
                        ckReserveReporting.setName(String.valueOf(m.get("name")));
                        ckReserveReporting.setMap(String.valueOf(m.get("map")));
                        ckReserveReporting.setType("CK");
                    }
                }
            });
            lists.addAll(ckReserveReportingListck);
        }

        // 堆场
        EruptApiModel eruptApiModeldc = EruptRemoteUtils.doGet(remoteProxyService.getMainServerUrl() + "/erupt-api/get/selectYardInfo", EruptApiModel.class);
        if (ObjectUtils.isNotEmpty(eruptApiModeldc.getData())) {
            List<LinkedTreeMap> data = (List<LinkedTreeMap>) eruptApiModeldc.getData();
            List list = new ArrayList();
            data.forEach(v -> {
                list.add(v.get("id"));
            });
            String ids = SqlUtils.wrapIn(list);
            // 堆场上报
            List<CkReserveReportingForm> ckReserveReportingListdc = EruptDaoUtils.selectOnes("select id,yard_type as yardType,storage_goods as yardNow,yard_num as yardNum from tb_dc_reserve_reporting where   yard_num" + ids, CkReserveReportingForm.class);
            data.forEach(m -> {
                for (CkReserveReportingForm ckReserveReporting : ckReserveReportingListdc) {
                    if (m.get("id").equals(ckReserveReporting.getYardNum())) {
                        ckReserveReporting.setCompany(String.valueOf(m.get("company")));
                        ckReserveReporting.setName(String.valueOf(m.get("name")));
                        ckReserveReporting.setMap(String.valueOf(m.get("map")));
                        ckReserveReporting.setType("DC");
                    }
                }
            });
            lists.addAll(ckReserveReportingListdc);
        }

        // 储罐
        EruptApiModel eruptApiModelcg = EruptRemoteUtils.doGet(remoteProxyService.getMainServerUrl() + "/erupt-api/get/selectStorageTankInfo", EruptApiModel.class);
        if (ObjectUtils.isNotEmpty(eruptApiModelcg.getData())) {
            List<LinkedTreeMap> data = (List<LinkedTreeMap>) eruptApiModelcg.getData();
            List list = new ArrayList();
            data.forEach(v -> {
                list.add(v.get("id"));
            });
            String ids = SqlUtils.wrapIn(list);
            //   上报储罐
            List<CkReserveReportingForm> ckReserveReportingListcg = EruptDaoUtils.selectOnes("select id,yard_type as yardType,storage_goods as yardNow,`number`as yardNum from tb_reserve_reporting where   `number`" + ids, CkReserveReportingForm.class);
            data.forEach(m -> {
                for (CkReserveReportingForm ckReserveReporting : ckReserveReportingListcg) {
                    if (m.get("id").equals(ckReserveReporting.getYardNum())) {
                        ckReserveReporting.setCompany(String.valueOf(m.get("company")));
                        ckReserveReporting.setName(String.valueOf(m.get("name")));
                        ckReserveReporting.setMap(String.valueOf(m.get("map")));
                        ckReserveReporting.setType("CG");
                    }
                }
            });
            if (ObjectUtils.isNotEmpty(ckReserveReportingListcg)) {
                ckReserveReportingListcg.forEach(v->{
                    EruptDictItem yardNow = EruptDictUtils.getDictItem("yardNow", v.getYardNow());
                    if (ObjectUtils.isNotEmpty(yardNow)) {
                        v.setYardNow(yardNow.getName());
                    }
                });
            }

            lists.addAll(ckReserveReportingListcg);
        }
        if (ObjectUtils.isNotEmpty(lists)) {
            lists.forEach(v -> {
                v.setOrgCode(v.getCompany());
                v.setCompany(DaliangangContext.getEnterpriseName(v.getCompany()));
            });
        }

        // 筛选
        List<CkReserveReportingForm> list = new ArrayList<>();
        if (ObjectUtils.isNotEmpty(lists)) {
            lists.forEach(v -> {
                if (v.getOrgCode().equals(orgCode)) {
                    list.add(v);
                }
            });
        }

        return EruptApiModel.successApi(list);
    }


    // 第二大屏每日储量上报
    @RequestMapping("erupt-api/get/selectReserveReportInfoTwo/{orgCode}/{workType}")
    @EruptRouter(verifyType = EruptRouter.VerifyType.LOGIN)
    public EruptApiModel selectReserveReportInfoTwo(@PathVariable("orgCode") String orgCode,@PathVariable("workType") String workType) {
        List<CkReserveReportingForm> lists = new ArrayList();
        // 仓库数据
        EruptApiModel eruptApiModel = EruptRemoteUtils.doGet(remoteProxyService.getMainServerUrl() + "/erupt-api/get/selectWarehouseInfo", EruptApiModel.class);
        if (ObjectUtils.isNotEmpty(eruptApiModel.getData())) {
            List<LinkedTreeMap> data = (List<LinkedTreeMap>) eruptApiModel.getData();
            List list = new ArrayList();
            data.forEach(v -> {
                list.add(v.get("id"));
            });
            String ids = SqlUtils.wrapIn(list);

            // 仓库上报数据
            List<CkReserveReportingForm> ckReserveReportingListck = EruptDaoUtils.selectOnes("select DISTINCT id,yard_type as yardType,storage_goods as yardNow,yard_num as yardNum ,storage_goods as storageGoods from tb_ck_reserve_reporting where   yard_num" + ids, CkReserveReportingForm.class);
            data.forEach(m -> {
                for (CkReserveReportingForm ckReserveReporting : ckReserveReportingListck) {
                    if (m.get("id").equals(ckReserveReporting.getYardNum())) {
                        ckReserveReporting.setCompany(String.valueOf(m.get("company")));
                        ckReserveReporting.setName(String.valueOf(m.get("name")));
                        ckReserveReporting.setMap(String.valueOf(m.get("map")));
                        ckReserveReporting.setType("CK");
                    }
                }
            });
            lists.addAll(ckReserveReportingListck);
        }

        // 堆场
        EruptApiModel eruptApiModeldc = EruptRemoteUtils.doGet(remoteProxyService.getMainServerUrl() + "/erupt-api/get/selectYardInfo", EruptApiModel.class);
        if (ObjectUtils.isNotEmpty(eruptApiModeldc.getData())) {
            List<LinkedTreeMap> data = (List<LinkedTreeMap>) eruptApiModeldc.getData();
            List list = new ArrayList();
            data.forEach(v -> {
                list.add(v.get("id"));
            });
            String ids = SqlUtils.wrapIn(list);
            // 堆场上报
            List<CkReserveReportingForm> ckReserveReportingListdc = EruptDaoUtils.selectOnes("select DISTINCT id,yard_type as yardType,storage_goods as yardNow,yard_num as yardNum,storage_goods as storageGoods from tb_dc_reserve_reporting where   yard_num" + ids, CkReserveReportingForm.class);
            data.forEach(m -> {
                for (CkReserveReportingForm ckReserveReporting : ckReserveReportingListdc) {
                    if (m.get("id").equals(ckReserveReporting.getYardNum())) {
                        ckReserveReporting.setCompany(String.valueOf(m.get("company")));
                        ckReserveReporting.setName(String.valueOf(m.get("name")));
                        ckReserveReporting.setMap(String.valueOf(m.get("map")));
                        ckReserveReporting.setType("DC");
                    }
                }
            });
            lists.addAll(ckReserveReportingListdc);
        }

        // 储罐
        EruptApiModel eruptApiModelcg = EruptRemoteUtils.doGet(remoteProxyService.getMainServerUrl() + "/erupt-api/get/selectStorageTankInfo", EruptApiModel.class);
        if (ObjectUtils.isNotEmpty(eruptApiModelcg.getData())) {
            List<LinkedTreeMap> data = (List<LinkedTreeMap>) eruptApiModelcg.getData();
            List list = new ArrayList();
            data.forEach(v -> {
                list.add(v.get("id"));
            });
            String ids = SqlUtils.wrapIn(list);
            //   上报储罐
            List<CkReserveReportingForm> ckReserveReportingListcg = EruptDaoUtils.selectOnes("select DISTINCT id,yard_type as yardType,storage_goods as yardNow,`number`as yardNum ,storage_goods as storageGoods from tb_reserve_reporting where   `number`" + ids, CkReserveReportingForm.class);
            data.forEach(m -> {
                for (CkReserveReportingForm ckReserveReporting : ckReserveReportingListcg) {
                    if (m.get("id").equals(ckReserveReporting.getYardNum())) {
                        ckReserveReporting.setCompany(String.valueOf(m.get("company")));
                        ckReserveReporting.setName(String.valueOf(m.get("name")));
                        ckReserveReporting.setMap(String.valueOf(m.get("map")));
                        ckReserveReporting.setType("CG");
                    }
                }
            });
            if (ObjectUtils.isNotEmpty(ckReserveReportingListcg)) {
                ckReserveReportingListcg.forEach(v->{
                    EruptDictItem yardNow = EruptDictUtils.getDictItem("yardNow", v.getYardNow());
                    if (ObjectUtils.isNotEmpty(yardNow)) {
                        v.setYardNow(yardNow.getName());
                    }

                });
            }
            lists.addAll(ckReserveReportingListcg);
        }

        if (ObjectUtils.isNotEmpty(lists)) {
            lists.forEach(v -> {
                v.setOrgCode(v.getCompany());
                v.setCompany(DaliangangContext.getEnterpriseName(v.getCompany()));
            });
        }

        List<CkReserveReportingForm> list = new ArrayList<>();
        List<CkReserveReportingForm> listss = new ArrayList<>();
        if (ObjectUtils.isNotEmpty(lists)) {

            // 获取管理员下的企业的存储上报  判断是否是政府管理员
            if (DaliangangContext.isDepartmentUser()) {
                MetaUserinfo remoteUserInfo = remoteProxyService.getRemoteUserInfo();
                List<String> auth = remoteUserInfo.getAuth();
             //   List<String> collect = auth.stream().filter(item -> lists.stream().map(e -> e.getOrgCode()).collect(Collectors.toList()).contains(item)).collect(Collectors.toList());
                List<CkReserveReportingForm> collect1 = lists.stream().filter(it -> auth.contains(it.getOrgCode())).collect(Collectors.toList());
                list.addAll(collect1);
            } else {
                lists.forEach(v -> {
                    if (v.getOrgCode().equals(orgCode)) {
                        list.add(v);
                    }
                });
            }
        }


        if (!workType.equals("all")) {
            List<CkReserveReportingForm> collect = list.stream().filter(it -> workType.contains(it.getType())).collect(Collectors.toList());
            listss.addAll(collect);
            return EruptApiModel.successApi(listss);
        } else {
            return EruptApiModel.successApi(list);
        }

    }


    // 应急大屏每日上报
    @PostMapping("erupt-api/get/getReserveReportInfo")
    @EruptRouter(verifyType = EruptRouter.VerifyType.LOGIN)
    public EruptApiModel getReserveReportInfo(@RequestBody Map map) {
        List<CkReserveReportingForm> lists = new ArrayList();
        // 仓库数据
        EruptApiModel eruptApiModel = EruptRemoteUtils.doGet(remoteProxyService.getMainServerUrl() + "/erupt-api/get/selectWarehouseInfo", EruptApiModel.class);
        if (ObjectUtils.isNotEmpty(eruptApiModel.getData())) {
            List<LinkedTreeMap> data = (List<LinkedTreeMap>) eruptApiModel.getData();
            List list = new ArrayList();
            data.forEach(v -> {
                list.add(v.get("id"));
            });
            String ids = SqlUtils.wrapIn(list);

            // 仓库上报数据
            List<CkReserveReportingForm> ckReserveReportingListck = EruptDaoUtils.selectOnes("select id,yard_type as yardType,storage_goods as yardNow,yard_num as yardNum from tb_ck_reserve_reporting where   yard_num" + ids, CkReserveReportingForm.class);
            data.forEach(m -> {
                for (CkReserveReportingForm ckReserveReporting : ckReserveReportingListck) {
                    if (m.get("id").equals(ckReserveReporting.getYardNum())) {
                        ckReserveReporting.setCompany(String.valueOf(m.get("company")));
                        ckReserveReporting.setName(String.valueOf(m.get("name")));
                        ckReserveReporting.setMap(String.valueOf(m.get("map")));
                        ckReserveReporting.setType("CK");
                    }
                }
            });
            lists.addAll(ckReserveReportingListck);
        }

        // 堆场
        EruptApiModel eruptApiModeldc = EruptRemoteUtils.doGet(remoteProxyService.getMainServerUrl() + "/erupt-api/get/selectYardInfo", EruptApiModel.class);
        if (ObjectUtils.isNotEmpty(eruptApiModeldc.getData())) {
            List<LinkedTreeMap> data = (List<LinkedTreeMap>) eruptApiModeldc.getData();
            List list = new ArrayList();
            data.forEach(v -> {
                list.add(v.get("id"));
            });
            String ids = SqlUtils.wrapIn(list);
            // 堆场上报
            List<CkReserveReportingForm> ckReserveReportingListdc = EruptDaoUtils.selectOnes("select id,yard_type as yardType,storage_goods as yardNow,yard_num as yardNum from tb_dc_reserve_reporting where   yard_num" + ids, CkReserveReportingForm.class);
            data.forEach(m -> {
                for (CkReserveReportingForm ckReserveReporting : ckReserveReportingListdc) {
                    if (m.get("id").equals(ckReserveReporting.getYardNum())) {
                        ckReserveReporting.setCompany(String.valueOf(m.get("company")));
                        ckReserveReporting.setName(String.valueOf(m.get("name")));
                        ckReserveReporting.setMap(String.valueOf(m.get("map")));
                        ckReserveReporting.setType("DC");
                    }
                }
            });
            lists.addAll(ckReserveReportingListdc);
        }

        // 储罐
        EruptApiModel eruptApiModelcg = EruptRemoteUtils.doGet(remoteProxyService.getMainServerUrl() + "/erupt-api/get/selectStorageTankInfo", EruptApiModel.class);
        if (ObjectUtils.isNotEmpty(eruptApiModelcg.getData())) {
            List<LinkedTreeMap> data = (List<LinkedTreeMap>) eruptApiModelcg.getData();
            List list = new ArrayList();
            data.forEach(v -> {
                list.add(v.get("id"));
            });
            String ids = SqlUtils.wrapIn(list);
            //   上报储罐
            List<CkReserveReportingForm> ckReserveReportingListcg = EruptDaoUtils.selectOnes("select id,yard_type as yardType,storage_goods as yardNow,`number`as yardNum from tb_reserve_reporting where   `number`" + ids, CkReserveReportingForm.class);
            data.forEach(m -> {
                for (CkReserveReportingForm ckReserveReporting : ckReserveReportingListcg) {
                    if (m.get("id").equals(ckReserveReporting.getYardNum())) {
                        ckReserveReporting.setCompany(String.valueOf(m.get("company")));
                        ckReserveReporting.setName(String.valueOf(m.get("name")));
                        ckReserveReporting.setMap(String.valueOf(m.get("map")));
                        ckReserveReporting.setType("CG");
                    }
                }
            });
            if (ObjectUtils.isNotEmpty(ckReserveReportingListcg)) {
                ckReserveReportingListcg.forEach(v->{
                    EruptDictItem yardNow = EruptDictUtils.getDictItem("yardNow", v.getYardNow());
                    if (ObjectUtils.isNotEmpty(yardNow)) {
                        v.setYardNow(yardNow.getName());
                    }
                });
            }
            lists.addAll(ckReserveReportingListcg);
        }
        List list = new ArrayList();
        if (ObjectUtils.isNotEmpty(lists)) {
            for (CkReserveReportingForm v : lists
            ) {
                if (v.getYardNum().equals(map.get("id")) && v.getName().equals(map.get("name"))) {
                    v.setCompany(DaliangangContext.getEnterpriseName(v.getCompany()));
                    list.add(v);
                    break;
                }
            }
        }

        return EruptApiModel.successApi(list);
    }


    // 天气
    @RequestMapping("erupt-api/get/selectWeatherFormInfo")
    public EruptApiModel selectWeatherFormInfo() {
        try {
            HttpGlobalConfig.setTimeout(3000);
            String url = eruptPlatformService.getOption(FiveChecklistsMonitorHandler.WEATHER).getAsString() ;
//            String url = "https://api.seniverse.com/v3/weather/now.json?key=" + sy + "&location=辽宁省大连市&language=zh-Hans";
            String result = HttpUtil.get(url);
            Gson gson = new Gson();
            WeatherForm weatherForm = gson.fromJson(result, WeatherForm.class);
            return EruptApiModel.successApi(weatherForm);
        } catch (Exception ex) {
            HttpGlobalConfig.setTimeout(-1);
            log.info("访问天气信息报错 -> " + ex.getMessage());
            return EruptApiModel.errorApi("无法获取天气信息");
        }
    }


}
