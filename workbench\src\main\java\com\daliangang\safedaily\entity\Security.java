/*
 * Copyright © 2020-2035 yundingyun All rights reserved.
 */
package com.daliangang.safedaily.entity;

import com.daliangang.core.CompanyRenderHandler;
import com.daliangang.safedaily.proxy.SecurityDataProxy;
import com.daliangang.workbench.handler.EmployeeExternalRenderHandler;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import xyz.erupt.annotation.Erupt;
import xyz.erupt.annotation.EruptField;
import xyz.erupt.annotation.expr.ExprBool;
import xyz.erupt.annotation.sub_erupt.Power;
import xyz.erupt.annotation.sub_erupt.RowOperation;
import xyz.erupt.annotation.sub_field.Edit;
import xyz.erupt.annotation.sub_field.EditType;
import xyz.erupt.annotation.sub_field.Readonly;
import xyz.erupt.annotation.sub_field.View;
import xyz.erupt.annotation.sub_field.sub_edit.*;
import xyz.erupt.mns.core.DelayMsg;
import xyz.erupt.mns.core.DelayMsgProxy;
import xyz.erupt.toolkit.attachment.AttachmentDownloadHandler;
import xyz.erupt.toolkit.colorfont.ColorStateTimeFont;
import xyz.erupt.toolkit.colorfont.ColorStateTimeFontDataProxy;
import xyz.erupt.toolkit.db.Comment;
import xyz.erupt.toolkit.handler.DelOperationHandler;
import xyz.erupt.toolkit.handler.EditOperationHandler;
import xyz.erupt.toolkit.handler.EruptDictCodeChoiceFetchHandler;
import xyz.erupt.toolkit.handler.RemoteEntityChoiceFetchHandler;
import xyz.erupt.toolkit.utils.TplUtils;
import xyz.erupt.upms.model.auth.DataAuthHandler;
import xyz.erupt.upms.model.auth.DataAuthModel;

import javax.persistence.Entity;
import javax.persistence.Lob;
import javax.persistence.Table;
import javax.persistence.Transient;

@Erupt(name = "港口设施保安备案管理", power = @Power(add = true, delete = false, export = false, importable = true, viewDetails = true, edit = false)
        , dataProxy = {SecurityDataProxy.class, DelayMsgProxy.class, ColorStateTimeFontDataProxy.class}
        , rowOperation = {
        @RowOperation(title = "编辑", icon = "fa fa-edit", code = TplUtils.EDIT_OPER_CODE, eruptClass = Security.class,show = @ExprBool( exprHandler = EmployeeExternalRenderHandler.class),
                operationHandler = EditOperationHandler.class, ifExprBehavior = RowOperation.IfExprBehavior.HIDE, mode = RowOperation.Mode.SINGLE),
        @RowOperation(title = "删除", show = @ExprBool( exprHandler = EmployeeExternalRenderHandler.class),icon = "fa fa-trash-o", operationHandler = DelOperationHandler.class,  ifExprBehavior = RowOperation.IfExprBehavior.HIDE, mode = RowOperation.Mode.SINGLE),
//        @RowOperation(title = "上报", icon = "fa fa-arrow-circle-o-up", operationHandler = SecurityEscalationHandler.class, ifExpr = "item.submitted=='未上报'", mode = RowOperation.Mode.SINGLE),
        @RowOperation(title = "下载附件", icon = "fa fa-download", operationHandler = AttachmentDownloadHandler.class, mode = RowOperation.Mode.SINGLE)
})
@Table(name = "tb_security")
@Entity
@Getter
@Setter
@Comment("港口设施保安备案管理")
@ApiModel("港口设施保安备案管理")
@DelayMsg(timeKey = "effectiveDate")
@ColorStateTimeFont(stateKey = "stateStr", timeKey = "effectiveDate", interval = 1, unit = ColorStateTimeFont.TimeUnit.MONTH)
public class Security extends DataAuthModel {
    @EruptField(
            views = @View(title = "企业名称",width = "200px"),
            edit = @Edit(title = "企业名称", search = @Search(ifRender = @ExprBool(exprHandler = CompanyRenderHandler.class)),
                    readonly = @Readonly(exprHandler = DataAuthHandler.class), type = EditType.CHOICE, notNull = true,
                    choiceType = @ChoiceType(fullSpan = true,fetchHandler = RemoteEntityChoiceFetchHandler.class, fetchHandlerParams = {"main", "Enterprise", "org_code,name"})))
    @Comment("企业名称")
    @ApiModelProperty("企业名称")
    private String company;

    @EruptField(
            views = @View(title = "港口设施名称", show = true,width = "200px"),
            edit = @Edit(title = "港口设施名称", type = EditType.INPUT, show = true, notNull = true,
                    inputType = @InputType))
    @Comment("港口设施名称")
    @ApiModelProperty("港口设施名称")
    private String name;



    @EruptField(
            views = @View(title = "上报状态",width = "80px",show = false),
            edit = @Edit(title = "上报状态", type = EditType.BOOLEAN, show = false, /*notNull = true, */
                    boolType = @BoolType(trueText = "已上报", falseText = "未上报")))
    @Comment("上报状态")
    @ApiModelProperty("上报状态")
    private Boolean submitted;

    @EruptField(
            views = @View(title = "签发日期",width = "70px"),
            edit = @Edit(title = "签发日期", type = EditType.DATE, notNull = true,
                    dateType = @DateType))
    @Comment("签发日期")
    @ApiModelProperty("签发日期")
    private java.util.Date date;

    @EruptField(
            views = @View(title = "有效期至",width = "70px"),
            edit = @Edit(title = "有效期至", type = EditType.DATE, search = @Search(vague = true), notNull = true,
                    dateType = @DateType))
    @Comment("有效期至")
    @ApiModelProperty("有效期至")
    private java.util.Date effectiveDate;

    @EruptField(
            views = @View(title = "状态", show = false),
            edit = @Edit(title = "状态", type = EditType.CHOICE, notNull = false, show = false,
                    choiceType = @ChoiceType(fetchHandler = EruptDictCodeChoiceFetchHandler.class, fetchHandlerParams = "expiredState")))
    @Comment("状态")
    @ApiModelProperty("状态")
    private String state;

    @EruptField(
            views = @View(title = "状态", show = true,width = "100px"),
            edit = @Edit(title = "状态", type = EditType.INPUT, notNull = false, show = false,search = @Search))
    @Transient
    private String stateStr;

    @Lob
    @EruptField(
            views = @View(title = "港口设施地址",width = "200px"),
            edit = @Edit(title = "港口设施地址", type = EditType.TEXTAREA, notNull = true))
    @Comment("港口设施地址")
    @ApiModelProperty("港口设施地址")
    private String adress;

    @EruptField(
            views = @View(title = "上传附件", show = false),
            edit = @Edit(title = "上传附件", type = EditType.DIVIDE))
    @Transient
    @Comment("上传附件")
    @ApiModelProperty("上传附件")
    private String fileDivide;


    @EruptField(
            views = @View(title = "附件上传", show = false),
            edit = @Edit(title = "附件上传", type = EditType.ATTACHMENT, notNull = true,
                    attachmentType = @AttachmentType(
                            type = AttachmentType.Type.BASE,
                            maxLimit = 20,
                            tipMsg = "<t><font color='green'>港口设施保安评估报告，港口设施保安计划，港口设施保安符合证书，年度核查报告，其他文件、资料</font>",
                            fileTypes = {".pdf", ".doc", ".docx", ".xlsx", ".png", ".jpg", ".xls", ".ppt", ".pptx", ".zip", ".rar"})))
    @Comment("附件上传")
    @ApiModelProperty("附件上传")
    private String fileUpload;

//	@EruptField(
//		views = @View(title = "港口设施保安评估报告", show = false),
//		edit = @Edit(title = "港口设施保安评估报告", type = EditType.ATTACHMENT, notNull = true,
//		attachmentType = @AttachmentType(type = AttachmentType.Type.BASE, maxLimit = 20, fileTypes = {".pdf", ".doc", ".docx", ".xlsx", ".png", ".jpg", ".xls", ".ppt", ".pptx", ".zip", ".rar"})))
//	@Comment("港口设施保安评估报告")
//	@ApiModelProperty("港口设施保安评估报告")
//	private String report;
//
//	@EruptField(
//		views = @View(title = "港口设施保安计划", show = false),
//		edit = @Edit(title = "港口设施保安计划", type = EditType.ATTACHMENT, notNull = true,
//		attachmentType = @AttachmentType(type = AttachmentType.Type.BASE, maxLimit = 20, fileTypes = {".pdf", ".doc", ".docx", ".xlsx", ".png", ".jpg", ".xls", ".ppt", ".pptx", ".zip", ".rar"})))
//	@Comment("港口设施保安计划")
//	@ApiModelProperty("港口设施保安计划")
//	private String plan;
//
//	@EruptField(
//		views = @View(title = "港口设施保安符合证书", show = false),
//		edit = @Edit(title = "港口设施保安符合证书", type = EditType.ATTACHMENT, notNull = true,
//		attachmentType = @AttachmentType(type = AttachmentType.Type.BASE, maxLimit = 20, fileTypes = {".pdf", ".doc", ".docx", ".xlsx", ".png", ".jpg", ".xls", ".ppt", ".pptx", ".zip", ".rar"})))
//	@Comment("港口设施保安符合证书")
//	@ApiModelProperty("港口设施保安符合证书")
//	private String certificate;
//
//	@EruptField(
//		views = @View(title = "年度核查报告", show = false),
//		edit = @Edit(title = "年度核查报告", type = EditType.ATTACHMENT, notNull = true,
//		attachmentType = @AttachmentType(type = AttachmentType.Type.BASE, maxLimit = 20, fileTypes = {".pdf", ".doc", ".docx", ".xlsx", ".png", ".jpg", ".xls", ".ppt", ".pptx", ".zip", ".rar"})))
//	@Comment("年度核查报告")
//	@ApiModelProperty("年度核查报告")
//	private String reportFile;
//
//	@EruptField(
//		views = @View(title = "其他文件、资料", show = false),
//		edit = @Edit(title = "其他文件、资料", type = EditType.ATTACHMENT,
//		attachmentType = @AttachmentType(type = AttachmentType.Type.BASE, maxLimit = 20, fileTypes = {".pdf", ".doc", ".docx", ".xlsx", ".png", ".jpg", ".xls", ".ppt", ".pptx", ".zip", ".rar"})))
//	@Comment("其他文件、资料")
//	@ApiModelProperty("其他文件、资料")
//	private String other;

    @EruptField(
            views = @View(title = "是否上报", show = false),
            edit = @Edit(title = "是否上报", type = EditType.BOOLEAN, show = false,
                    boolType = @BoolType(trueText = "是", falseText = "否")))
    @Comment("是否上报")
    @ApiModelProperty("是否上报")
    private Boolean isReport;


}
