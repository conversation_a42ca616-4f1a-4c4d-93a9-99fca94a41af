/*
* Copyright © 2020-2035 yundingyun All rights reserved.
*/
package com.daliangang.emergency.entity;

import com.daliangang.emergency.proxy.SensitivetargetsDataProxy;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import xyz.erupt.annotation.Erupt;
import xyz.erupt.annotation.EruptField;
import xyz.erupt.annotation.sub_erupt.Power;
import xyz.erupt.annotation.sub_field.Edit;
import xyz.erupt.annotation.sub_field.EditType;
import xyz.erupt.annotation.sub_field.View;
import xyz.erupt.annotation.sub_field.sub_edit.InputType;
import xyz.erupt.annotation.sub_field.sub_edit.Search;
import xyz.erupt.jpa.model.MetaModel;
import xyz.erupt.toolkit.db.Comment;

import javax.persistence.Entity;
import javax.persistence.Lob;
import javax.persistence.Table;
import javax.persistence.Transient;

@Erupt(name = "敏感目标管理", importTruncate = true, power = @Power(add = true, delete = true, export = false, importable = true, viewDetails = true, edit = true)
, dataProxy = SensitivetargetsDataProxy.class
, rowOperation = {})
@Table(name = "tb_sensitivetargets")
@Entity
@Getter
@Setter
@Comment("敏感目标管理")
@ApiModel("敏感目标管理")
public class Sensitivetargets extends MetaModel {
	@EruptField(
		views = @View(title = "名称"),
		edit = @Edit(title = "名称", type = EditType.INPUT, search = @Search(vague = true), notNull = true,
		inputType = @InputType))
	@Comment("名称")
	@ApiModelProperty("名称")
	private String name;

	@EruptField(
		views = @View(title = "人口（约）"),
		edit = @Edit(title = "人口（约）", type = EditType.INPUT, notNull = true,
		inputType = @InputType))
	@Comment("人口（约）")
	@ApiModelProperty("人口（约）")
	private String population;


	@EruptField(
		views = @View(title = "地图", show = false),
		edit = @Edit(title = "地图", type = EditType.MAP, show = true))
	@Comment("地图")
	@ApiModelProperty("地图")
	@Lob
	private String map;

	@EruptField(
			views = @View(title = "距离",show = false),
			edit = @Edit(title = "距离", type = EditType.INPUT, show = false,
					inputType = @InputType))
	@Comment("距离")
	@Transient
	@ApiModelProperty("距离")
	private Double distance;

}
