package com.daliangang.rndpub.form;

import lombok.Data;
import xyz.erupt.annotation.Erupt;
import xyz.erupt.annotation.EruptField;
import xyz.erupt.annotation.sub_field.Edit;
import xyz.erupt.annotation.sub_field.EditType;
import xyz.erupt.annotation.sub_field.Readonly;
import xyz.erupt.annotation.sub_field.sub_edit.AttachmentType;
import xyz.erupt.annotation.sub_field.sub_edit.ChoiceType;
import xyz.erupt.annotation.sub_field.sub_edit.DateType;
import xyz.erupt.toolkit.handler.SqlChoiceFetchHandler;

import javax.persistence.Id;
import javax.persistence.Lob;
import javax.persistence.Transient;

/**
 * <AUTHOR>
 * @since :2023/6/1:15:43
 */
@Data
@Erupt(name = "整改", authVerify = false)
public class RectificationForm {
    @Id
    @EruptField
    @Transient
    private Long id;

    private String name;   //检查名字

    private String enterpriseName; //企业名字

    private java.sql.Date inspectionDate; //检查时间



    @Transient
    @EruptField(edit = @Edit(title = "整改id", type = EditType.INPUT, readonly = @Readonly, show = false))
    private String rectifysId;
    @Transient
    @EruptField(
            edit = @Edit(title = "检查名称", type = EditType.CHOICE, notNull = false,readonly = @Readonly,
                    choiceType = @ChoiceType(fetchHandler = SqlChoiceFetchHandler.class, fetchHandlerParams = "select id,name from tb_procedure")
            ))
    private String inspectionName;


    @EruptField(
            edit = @Edit(title = "检查对象", type = EditType.CHOICE, notNull = true,readonly = @Readonly,
                    choiceType = @ChoiceType(fetchHandler = SqlChoiceFetchHandler.class,
                            fetchHandlerParams = {"select org_code,enterprise_name from tb_enterprise_information", "5000", "and state=1"})))
    @Transient
    private String checkObject;

    @EruptField(
            edit = @Edit(title = "检查事项", type = EditType.CHOICE, notNull = false,readonly = @Readonly,
                    choiceType = @ChoiceType(fetchHandler = SqlChoiceFetchHandler.class,
                            fetchHandlerParams = {"select distinct inspection_items from tb_inspection_items", "5000", "and state=1"})))
    @Transient
    private String inspectionItems;



    @EruptField(
            edit = @Edit(title = "问题描述", type = EditType.TEXTAREA, readonly = @Readonly))
    @Transient
    private @Lob
    String problemDescription;


    @EruptField(
            edit = @Edit(title = "检查依据", type = EditType.INPUT, readonly = @Readonly))
    @Transient
    private String inspectionBasis;

    @EruptField(
            edit = @Edit(title = "现场照片", type = EditType.ATTACHMENT, notNull = false, readonly = @Readonly,
                    attachmentType = @AttachmentType(type = AttachmentType.Type.BASE, maxLimit = 1, fileTypes = {".pdf", ".doc", ".docx", ".xlsx", ".png", ".jpg", ".xls", ".ppt", ".pptx", ".zip", ".rar"})))
    @Transient
    private String inspectionBasisFile;

    @EruptField(
            edit = @Edit(title = "整改建议", type = EditType.TEXTAREA, readonly = @Readonly))
    @Transient
    private String proposal;



    @EruptField(
            edit = @Edit(title = "整改截止时间（年月日）", type = EditType.DATE, notNull = false, readonly = @Readonly,
                    dateType = @DateType))
    @Transient
    private java.sql.Date deadline;




    @Transient
    @EruptField(
            edit = @Edit(title = "整改内容", type = EditType.DIVIDE)
    )
    private String divide;

    @Transient
    @EruptField(edit = @Edit(title = "整改说明", type = EditType.TEXTAREA, notNull = true))
    private @Lob String description;

    @EruptField(
            edit = @Edit(title = "整改证明材料", type = EditType.ATTACHMENT, notNull = true,
                    attachmentType = @AttachmentType(type = AttachmentType.Type.BASE, maxLimit = 20, fileTypes = {".pdf", ".doc",".docx", ".xlsx", ".png", ".jpg", ".word", ".ppt", ".zip", ".rar"})))
    @Transient
    private String supportingMaterials;


    @EruptField(
            edit = @Edit(title = "整改时间", type = EditType.DATE, notNull = true,
                    dateType = @DateType))
    @Transient
    private java.sql.Date rectificationTime;
}
