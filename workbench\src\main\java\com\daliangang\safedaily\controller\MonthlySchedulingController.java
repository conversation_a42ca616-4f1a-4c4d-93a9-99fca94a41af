package com.daliangang.safedaily.controller;

import com.daliangang.safedaily.form.NumForm;
import com.daliangang.safedaily.sql.MonthlySchedulingSql;
import org.springframework.stereotype.Repository;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import xyz.erupt.core.annotation.EruptRouter;
import xyz.erupt.core.module.MetaUserinfo;
import xyz.erupt.core.view.EruptApiModel;
import xyz.erupt.toolkit.remote.RemoteProxyService;
import xyz.erupt.toolkit.utils.EruptDaoUtils;
import xyz.erupt.toolkit.utils.SqlUtils;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @since :2023/4/23:14:02
 */
@RestController
public class MonthlySchedulingController {
    @Resource
    private RemoteProxyService remoteProxyService;
    @Resource
    private MonthlySchedulingSql monthlySchedulingSql;
    // 统计上报
    @RequestMapping("erupt-api/get/selectMonthlySchedulingNum")
    @EruptRouter(verifyType = EruptRouter.VerifyType.LOGIN)
    public EruptApiModel selectMonthlySchedulingNum() {
        MetaUserinfo remoteUserInfo = remoteProxyService.getRemoteUserInfo();
        String orgCode = SqlUtils.wrapIn(remoteUserInfo.getAuth());
        String sql = monthlySchedulingSql.selectMonthlySchedulingNum(orgCode);
        NumForm employeeCertificateForm = EruptDaoUtils.selectOne(sql, NumForm.class);
        return EruptApiModel.successApi(employeeCertificateForm);
    }
}
