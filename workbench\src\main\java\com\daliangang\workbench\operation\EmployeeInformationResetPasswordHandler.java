/*
 * Copyright © 2020-2035 yundingyun All rights reserved.
 */
package com.daliangang.workbench.operation;

import com.daliangang.workbench.entity.EmployeeInformation;
import org.springframework.stereotype.Service;
import xyz.erupt.annotation.fun.OperationHandler;
import xyz.erupt.core.util.MD5Util;
import xyz.erupt.toolkit.utils.AssertUtils;
import xyz.erupt.toolkit.utils.EruptDaoUtils;
import xyz.erupt.toolkit.utils.NotifyUtils;
import xyz.erupt.upms.model.EruptUser;
import xyz.erupt.upms.service.EruptUserService;

import javax.annotation.Resource;
import javax.transaction.Transactional;
import java.util.List;

@Service
public class EmployeeInformationResetPasswordHandler implements OperationHandler<EmployeeInformation, Void> {

    @Resource
    private EruptUserService eruptUserService;

    @Override
    @Transactional
    public String exec(List<EmployeeInformation> data, Void unused, String[] param) {
        for (EmployeeInformation employee : data) {
            EruptUser empUser = eruptUserService.findEruptUserByAccount(employee.getPhone());
            AssertUtils.notNull(empUser, "没有这个用户");
            String sql = "update e_upms_user set password='"+ MD5Util.digest("Qwer@120201") +"',is_md5=true where id=" + empUser.getId();
            EruptDaoUtils.updateNoForeignKeyChecks(sql);
            eruptUserService.flushEruptUserCache(empUser);
            return NotifyUtils.getSuccessNotify("密码已重置为【Qwer@120201】，请提醒员工及时修改密码。");
        }
        return null;
    }
}
