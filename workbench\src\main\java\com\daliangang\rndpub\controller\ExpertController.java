package com.daliangang.rndpub.controller;

import com.daliangang.rndpub.RndpubConst;
import com.daliangang.rndpub.entity.Expert;
import com.daliangang.rndpub.sql.ExpertSql;
import com.daliangang.workbench.form.EmployeeCertificateForm;
import com.google.gson.Gson;
import com.google.gson.JsonObject;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import xyz.erupt.annotation.fun.VLModel;
import xyz.erupt.core.annotation.EruptRouter;
import xyz.erupt.core.config.GsonFactory;
import xyz.erupt.core.controller.EruptFileController;
import xyz.erupt.core.module.MetaUserinfo;
import xyz.erupt.core.service.EruptCoreService;
import xyz.erupt.core.util.EruptUtil;
import xyz.erupt.core.view.EruptApiModel;
import xyz.erupt.core.view.EruptModel;
import xyz.erupt.jpa.dao.EruptJpaDao;
import xyz.erupt.toolkit.controller.RemoteDataController;
import xyz.erupt.toolkit.handler.EruptDictCodeChoiceFetchHandler;
import xyz.erupt.toolkit.remote.RemoteProxyService;
import xyz.erupt.toolkit.utils.EruptDaoUtils;

import javax.annotation.Resource;
import javax.transaction.Transactional;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @Author: zengwensi
 * @Description:
 * @Date: Created in 2023/4/14 10:05
 * @Modified By
 */
@Slf4j
@RestController
public class ExpertController {

    @Resource
    private EruptFileController eruptFileController ;

    @Resource
    private EruptJpaDao eruptJpaDao;
    @Resource
    private EruptDictCodeChoiceFetchHandler handler;

    @Resource
    private RemoteDataController remoteDataController;

    @Resource
    private RemoteProxyService remoteProxyService;

    @Resource
    private ExpertSql expertSql;


    /**
     * 上传专家文件
     * @param file 文件
     * @return
     */
    @PostMapping("erupt-api/expert/fileUploads")
    public EruptApiModel uploads( @RequestParam("file") MultipartFile file) {
        return eruptFileController.upload("Qualification", "file", file,false);
    }

    /**
     * 自定义页面--新增专家
     * @param data 专家
     * @return
     */
    @PostMapping("erupt-api/data/Expert/add")
    @SneakyThrows
    @Transactional
    public EruptApiModel addExpert( @RequestBody JsonObject data){

        EruptModel eruptModel = EruptCoreService.getErupt("Expert");
        EruptApiModel eruptApiModel = EruptUtil.validateEruptValue(eruptModel, data);
        if (eruptApiModel.getStatus() == EruptApiModel.Status.ERROR) return eruptApiModel;

        Gson gson = GsonFactory.getGson();
        Expert expert = gson.fromJson(data.toString(), Expert.class);
        expert.setExpertAudidtState(RndpubConst.EXPERT_STATUS_NOT_AUDITED);
        expert.setSource(Boolean.FALSE);
        eruptJpaDao.addEntity(eruptModel.getClazz(), expert);
        log.info("[" + eruptModel.getEruptName() + " ->  专家在链接中填写  ]:" + data.toString());
        return EruptApiModel.successApi();

    }


    /**
     * 获取初始化数据
     * @return
     */
    @GetMapping("erupt-api/data/Expert/getData")
    public EruptApiModel getExpertDict(){

        List<VLModel> educational = handler.fetch(new String[]{"educational"});
        List<VLModel> checkScope =handler.fetch(new String[]{"checkScope"});
        List<VLModel> technical =handler.fetch(new String[]{"postTitle"});

        EruptApiModel eruptApiModel = remoteDataController.queryEntity("Enterprise", "org_code,name", "1", "1");

        Object data = eruptApiModel.getData();
        Map<String,Object> map = new HashMap<>();
        map.put("educational",educational);
        map.put("checkScope",checkScope);
        map.put("technical",technical);
        map.put("enterprise",data);

        eruptApiModel.setData(map);
        return eruptApiModel ;
    }

    //统计专家员工人数
    @RequestMapping("erupt-api/get/selectExpertNum")
    @EruptRouter(verifyType = EruptRouter.VerifyType.LOGIN)
    public EruptApiModel selectExpertNum() {
        MetaUserinfo remoteUserInfo = remoteProxyService.getRemoteUserInfo();
        // String orgCode = SqlUtils.wrapIn(remoteUserInfo.getAuth());
        String sql = expertSql.selectExpertNum(remoteUserInfo.getOrg());
        EmployeeCertificateForm employeeCertificateForm = EruptDaoUtils.selectOne(sql, EmployeeCertificateForm.class);
        return EruptApiModel.successApi(employeeCertificateForm);
    }

}
