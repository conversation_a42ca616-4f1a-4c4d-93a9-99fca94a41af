package com.daliangang.emergency.entity;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import xyz.erupt.annotation.EruptField;
import xyz.erupt.annotation.sub_field.Edit;
import xyz.erupt.annotation.sub_field.EditType;
import xyz.erupt.annotation.sub_field.Readonly;
import xyz.erupt.annotation.sub_field.View;
import xyz.erupt.annotation.sub_field.sub_edit.ChoiceType;
import xyz.erupt.annotation.sub_field.sub_edit.InputType;
import xyz.erupt.annotation.sub_field.sub_edit.Search;
import xyz.erupt.jpa.model.MetaModel;
import xyz.erupt.toolkit.db.Comment;
import xyz.erupt.upms.handler.DictCodeChoiceFetchHandler;
import xyz.erupt.upms.util.EruptMenuUtils;

import javax.persistence.MappedSuperclass;

@MappedSuperclass
@Getter
@Setter
public class EvaluationCommon extends MetaModel implements Readonly.ReadonlyHandler {
    @EruptField(
            views = @View(title = "所属主要素"),
            edit = @Edit(title = "所属主要素", type = EditType.CHOICE, search = @Search, notNull = true, readonly = @Readonly(exprHandler = EvaluationCommon.class),
                    choiceType = @ChoiceType(fetchHandler = DictCodeChoiceFetchHandler.class, fetchHandlerParams = "PrincipalElement")))
    @Comment("所属主要素")
    @ApiModelProperty("所属主要素")
    private String name;

    @EruptField(
            views = @View(title = "子要素名称"),
            edit = @Edit(title = "子要素名称", type = EditType.INPUT, notNull = true, readonly = @Readonly(exprHandler = EvaluationCommon.class),
                    inputType = @InputType))
    @Comment("子要素名称")
    @ApiModelProperty("子要素名称")
    private String childName;

    @EruptField(
            views = @View(title = "权重"),
            edit = @Edit(title = "权重", type = EditType.INPUT, notNull = true, readonly = @Readonly(exprHandler = EvaluationCommon.class),
                    inputType = @InputType))
    @Comment("权重")
    @ApiModelProperty("权重")
    private Double weight;

    @Override
    public boolean add(boolean add, String[] params) {
        return false;
    }

    @Override
    public boolean edit(boolean edit, String[] params) {
        return EruptMenuUtils.isEruptMenu(EvaluationManage.class);
    }


//    @EruptField(
//            views = @View(title = "项目", show = false),
//            edit = @Edit(title = "项目", type = EditType.INPUT, //notNull = true,
//                    inputType = @InputType))
//    @Comment("项目")
//    @ApiModelProperty("项目")
//    private String project;
//
//    @EruptField(
//            views = @View(title = "内容", show = false),
//            edit = @Edit(title = "内容", type = EditType.TEXTAREA))
//    @Comment("内容")
//    @ApiModelProperty("内容")
//    @Lob
//    private String content;
//
//    @EruptField(
//            views = @View(title = "依据", show = false),
//            edit = @Edit(title = "依据", type = EditType.TEXTAREA))
//    @Comment("依据")
//    @ApiModelProperty("依据")
//    @Lob
//    private String basis;
//
//    @EruptField(
//            views = @View(title = "评估办法", show = false),
//            edit = @Edit(title = "评估办法", type = EditType.TEXTAREA))
//    @Comment("评估办法")
//    @ApiModelProperty("评估办法")
//    @Lob
//    private String method;
//
//    @EruptField(
//            views = @View(title = "评估细则", show = false),
//            edit = @Edit(title = "评估细则", type = EditType.TEXTAREA))
//    @Comment("评估细则")
//    @ApiModelProperty("评估细则")
//    @Lob
//    private String rules;
}
