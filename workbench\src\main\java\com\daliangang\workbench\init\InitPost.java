package com.daliangang.workbench.init;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import xyz.erupt.excel.model.ExcelHeader;

/**
 * @Author：chb
 * @Package：com.daliangang.workbench.init
 * @Project：erupt
 * @name：InitDepartment
 * @Date：2023/3/5 22:05
 * @Filename：InitDepartment
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class InitPost extends InitTemplate {

    @ExcelHeader(col = 0, name = "编码")
    private String code;

    @ExcelHeader(col = 1, name = "名称")
    private String name;

    @ExcelHeader(col = 2, name = "权重")
    private Integer weight;

    @ExcelHeader(col = 3, name = "培训岗位")
    private Boolean training;

    @ExcelHeader(col = 4, name = "岗位类别")
    private String postType;

    public static InitPost valueOf(InitTemplate template) {
        return InitPost.builder()
                .code(template.getParam1())
                .name(template.getParam2())
                .weight(Integer.valueOf(template.getParam3()))
                .training(template.getParam4().equals("是"))
                .postType(template.getParam5())
                .build();
    }
}
