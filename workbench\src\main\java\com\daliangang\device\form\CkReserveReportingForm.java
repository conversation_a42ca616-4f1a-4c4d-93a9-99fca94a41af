package com.daliangang.device.form;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import xyz.erupt.annotation.EruptField;
import xyz.erupt.annotation.sub_field.Edit;
import xyz.erupt.annotation.sub_field.EditType;
import xyz.erupt.annotation.sub_field.Readonly;
import xyz.erupt.annotation.sub_field.View;
import xyz.erupt.annotation.sub_field.sub_edit.ChoiceType;
import xyz.erupt.annotation.sub_field.sub_edit.InputType;
import xyz.erupt.toolkit.db.Comment;
import xyz.erupt.toolkit.handler.RemoteCallChoiceFetchHandler;
import xyz.erupt.toolkit.handler.RemoteEntityChoiceFetchHandler;

/**
 * <AUTHOR>
 * @since :2023/5/5:13:24
 */
@Data
public class CkReserveReportingForm {

    private Long id;

    private String name;

    private String company;
    private String orgCode;


    private String yardType;


    private String yardNum;


    private String yardNow;

    private String map;

    private String type;

    private String storageGoods;

}
