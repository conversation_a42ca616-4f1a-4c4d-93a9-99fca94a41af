package com.daliangang.rndpub.proxy;

import com.daliangang.rndpub.entity.ProblemRectificationManage;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import xyz.erupt.annotation.fun.DataProxy;
import xyz.erupt.jpa.dao.EruptDao;
import xyz.erupt.toolkit.utils.EruptDaoUtils;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

@Service
@Slf4j
public class ProblemRectificationManageProxy implements DataProxy<ProblemRectificationManage> {

    @Resource
    private EruptDao eruptDao ;

    @Transactional
    @Override
    public void searchCondition(Map<String, Object> condition) {
        //清空内容
        String truncateSql="truncate table tb_company_inspection_problem";
        eruptDao.getJdbcTemplate().execute(truncateSql);
        String sql = "SELECT" +
                "      *," +
                "      ROUND( publishQuestionNum * 100 / totalQuestion, 2 ) publishRatio," + // 发布率
                "      ROUND( passResult * 100 / totalQuestion, 2 ) rectifiedRatio " + // 整改率
                "     FROM" +
                "     (" +
                "       SELECT " +
                "          name," + // 检查名称
                "          number_of_enterprises enterprisesNum," + // 检查企业数量
                "          r.check_object orgCode," + // 检查对象
                "          inspection_date inspectionDate," + // 检查开始日期
                "          count(*) totalQuestion," + // 检查问题数量
                "          count(IF( publish_status = 1, TRUE, NULL )) publishQuestionNum, " + // 已发布问题数量
                "          count(IF( inspection_result = 'PASS' and publish_status = 1, TRUE, NULL )) passResult," + // 整改通过问题数量
                "          count(IF( rectification_status = 'TO_BE_RECTIFIED' and publish_status = 1, TRUE, NULL )) rectifiedResult," + // 待整改问题数量
                "          count(IF( r.deadline < IFNULL(r.rectification_time, NOW()) and publish_status = 1, TRUE, NULL )) overDue " + // 已逾期问题数量
                "        FROM" +
                "           tb_procedure p" +
                "        LEFT JOIN tb_inspection_results r ON p.id = r.inspection_name  " +
                "        GROUP BY name, number_of_enterprises, r.check_object, inspection_date" +
                ") t" ;
        List<ProblemRectificationManage> list = EruptDaoUtils.selectOnes(sql, ProblemRectificationManage.class);
        list.forEach(item->{
            eruptDao.merge(item);
        });

    }
}
