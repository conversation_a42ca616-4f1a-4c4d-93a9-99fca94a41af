package com.daliangang.majorisk.entity;

import com.daliangang.majorisk.proxy.FiveChecklistsResponsibilityDataProxy;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import xyz.erupt.annotation.Erupt;
import xyz.erupt.annotation.EruptField;
import xyz.erupt.annotation.sub_erupt.Power;
import xyz.erupt.annotation.sub_field.Edit;
import xyz.erupt.annotation.sub_field.EditType;
import xyz.erupt.annotation.sub_field.View;
import xyz.erupt.annotation.sub_field.sub_edit.ChoiceType;
import xyz.erupt.annotation.sub_field.sub_edit.InputType;
import xyz.erupt.jpa.model.MetaModel;
import xyz.erupt.toolkit.db.Comment;
import xyz.erupt.toolkit.handler.EruptDictCodeChoiceFetchHandler;
import xyz.erupt.upms.model.auth.DataAuthModel;

import javax.persistence.*;

/**
 * <AUTHOR>
 * @since :2023/4/4:9:57
 */

@Erupt(name = "五清单责任分工", power = @Power(add = true, delete = true, export = false, importable = true, viewDetails = true, edit = true)
        , dataProxy = FiveChecklistsResponsibilityDataProxy.class
)
@Table(name = "tb_fivechecklists_responsibility")
@Entity
@Getter
@Setter
@Comment("五清单责任分工")
@ApiModel("五清单责任分工")
public class FiveChecklistsResponsibility extends MetaModel {
//    @EruptField(
//            views = @View(title = "企业名称"),
//            edit = @Edit(title = "企业名称", readonly = @Readonly(exprHandler = DataAuthInvoker.class), type = EditType.CHOICE, notNull = true,
//                    choiceType = @ChoiceType(fetchHandler = RemoteEntityChoiceFetchHandler.class, fetchHandlerParams ={"main","Enterprise","org_code,name"})))
//    @Comment("企业名称")
//    @ApiModelProperty("企业名称")
//    private String company;

    @EruptField(
            views = @View(title = "类型"),
            edit = @Edit(title = "类型", type = EditType.CHOICE,notNull = true,
                    choiceType = @ChoiceType(fetchHandler = EruptDictCodeChoiceFetchHandler.class, fetchHandlerParams = "ResponsibleType")))
    @Comment("类型")
    @ApiModelProperty("类型")
    private String responsibleType;

    @EruptField(
            views = @View(title = "姓名"),
            edit = @Edit(title = "姓名", type = EditType.INPUT, notNull = true,
                    inputType = @InputType))
    @Comment("姓名")
    @ApiModelProperty("姓名")
    private String name;

    @EruptField(
            views = @View(title = "职务"),
            edit = @Edit(title = "职务", type = EditType.INPUT, notNull = true,
                    inputType = @InputType))
    @Comment("职务")
    @ApiModelProperty("职务")
    private String post;

    @EruptField(
            views = @View(title = "联系方式"),
            edit = @Edit(title = "联系方式", type = EditType.INPUT,notNull = true,
                    inputType = @InputType))
    @Comment("联系方式")
    @ApiModelProperty("联系方式")
    private String contactTel;


    @Lob
    @EruptField(
            views = @View(title = "管控责任"),
            edit = @Edit(title = "管控责任", type = EditType.TEXTAREA,notNull = true,
                    inputType = @InputType(length = 300)))
    @Comment("管控责任")
    @ApiModelProperty("管控责任")
    private String controlResponsibility;

    @Lob
    @EruptField(
            views = @View(title = "备注"),
            edit = @Edit(
                    title = "备注",
                    type = EditType.TEXTAREA,
                    inputType = @InputType(length = 200)
            )
    )
    private String remark;

    @ManyToOne
    @EruptField
    @JoinColumn(name = "tb_five_detail_id")
    private FiveDetail fiveDetail;

}
