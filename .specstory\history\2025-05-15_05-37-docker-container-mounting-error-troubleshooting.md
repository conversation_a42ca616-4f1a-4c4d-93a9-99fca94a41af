<!-- Generated by SpecStory -->

# Docker Container Mounting Error Troubleshooting (2025-05-15 13:37:22)

_**User**_

@.gitlab-ci.yml @build-java.sh 正在打包....********************
Error response from daemon: No such container: ********************
docker: Error response from daemon: failed to create task for container: failed to create shim task: OCI runtime create failed: runc create failed: unable to start container process: error during container init: error mounting "/home/<USER>/.m2/settings.xml" to rootfs at "/usr/local/maven-mvnd-0.9.0-linux-amd64/conf/settings.xml": mount /home/<USER>/.m2/settings.xml:/usr/local/maven-mvnd-0.9.0-linux-amd64/conf/settings.xml (via /proc/self/fd/6), flags: 0x5000: not a directory: unknown: Are you trying to mount a directory onto a file (or vice-versa)? Check if the specified host path exists and is the expected type.

---

_**Assistant**_



---

收到了，亲爱的同学。

```思考分析
# 问题诊断
- Docker挂载错误：尝试挂载`/home/<USER>/.m2/settings.xml`到容器内的`/usr/local/maven-mvnd-0.9.0-linux-amd64/conf/settings.xml`时失败
- 错误类型：类型不匹配错误 - "Are you trying to mount a directory onto a file (or vice-versa)?"
- 可能原因：
  1. `/home/<USER>/.m2/settings.xml`可能不存在
  2. 路径类型不匹配(目录vs文件)
  3. 权限问题
```

问题出在`build-java.sh`脚本中的Docker挂载配置上。具体来说，这一行出现问题：
```
-v "/home/<USER>/.m2/settings.xml":/usr/local/maven-mvnd-0.9.0-linux-amd64/conf/settings.xml \
```

解决方案：

1. 首先检查`/home/<USER>/.m2/settings.xml`是否存在：

---

```bash
ls -la /home/<USER>/.m2/settings.xml 2>/dev/null || echo "文件不存在"
```

---

_**User**_

我这是服务器的报错

---

_**Assistant**_



---

收到了，亲爱的同学。

```思考分析
# 错误分析
- 服务器报错显示Docker挂载失败
- 核心错误：mount /home/<USER>/.m2/settings.xml 时出现"not a directory"错误
- 具体原因：尝试将文件挂载为目录或将目录挂载为文件
- 问题位置：build-java.sh中的Docker挂载配置
```

服务器上的错误是因为Docker尝试挂载Maven配置文件时出现了类型不匹配。根据错误信息，有以下几种可能：

1. `/home/<USER>/.m2/settings.xml`可能不存在
2. 这个路径可能是一个目录而不是文件（或反之）

建议修改`build-java.sh`文件，调整挂载配置：

---

```diff
  
  echo "正在打包...."${DOCKER_NAME}
- docker stop ${DOCKER_NAME}
+ docker stop ${DOCKER_NAME} 2>/dev/null || true
  docker run -i --network=host --rm --name ${DOCKER_NAME} \
  -e  JAVA_OPTS='-Xmx1024M -Xms1024M -Xmn256M -XX:MaxMetaspaceSize=64M -XX:MetaspaceSize=64M' \
```

```diff
  -v "$PWD/../../target:/usr/src/mymaven/target" \
  -v "$PWD/../../target:/usr/src/mymaven/../target" \
- -v "/home/<USER>/.m2/settings.xml":/usr/local/maven-mvnd-0.9.0-linux-amd64/conf/settings.xml \
  -w /usr/src/mymaven \
  mailbyms/mvnd:0.9.0-jdk-8 mvnd \
```

---

主要修改了两处：

1. 移除了有问题的settings.xml挂载配置：
   ```
   -v "/home/<USER>/.m2/settings.xml":/usr/local/maven-mvnd-0.9.0-linux-amd64/conf/settings.xml \
   ```

2. 优化了docker stop命令，添加错误重定向和容错处理：
   ```
   docker stop ${DOCKER_NAME} 2>/dev/null || true
   ```

这样修改后，构建脚本不会再尝试挂载可能不存在或类型不匹配的settings.xml文件。如果您确实需要Maven配置，建议检查服务器上settings.xml的确切位置和类型，然后再添加正确的挂载配置。

需要测试此修改是否解决了问题吗？

---

_**User**_

$ echo '当前工作目录:' "$PWD"
当前工作目录: /home/<USER>/builds/x6JQ7D9U/0/daliangang/backend/daliangang-workbench
$ echo "直接在本地执行构建..."
直接在本地执行构建...
$ cd workbench && sh build-java.sh && sh build-image.sh $DOCKER_REGISTRY_PWD
正在打包....********************
org.apache.maven.settings.building.SettingsBuildingException: 1 problem was encountered while building the effective settings
[FATAL] Non-readable settings /root/.m2/settings.xml: /root/.m2/settings.xml (Is a directory) @ /root/.m2/settings.xml报错

---

_**Assistant**_



---

收到了，亲爱的同学。

```思考分析
# 新错误分析
- Maven错误：`Non-readable settings /root/.m2/settings.xml: /root/.m2/settings.xml (Is a directory)`
- 问题原因：Maven期望settings.xml是一个文件，但容器内的/root/.m2/settings.xml是一个目录
- 影响：Maven无法读取配置文件导致构建失败
- 可能原因：
  1. 宿主机上的/home/<USER>/.m2目录结构有问题
  2. 挂载方式不正确，导致settings.xml变成了目录
```

问题在于我们挂载了整个`.m2`目录，但是Maven期望`.m2/settings.xml`是一个文件，而不是目录。让我修复这个问题：

---

```diff
  -e  JAVA_OPTS='-Xmx1024M -Xms1024M -Xmn256M -XX:MaxMetaspaceSize=64M -XX:MetaspaceSize=64M' \
  -v "$PWD"/:/usr/src/mymaven \
- -v "/home/<USER>/.m2":/root/.m2 \
+ -v "/home/<USER>/.m2/repository":/root/.m2/repository \
  -v "$PWD/../../target:/usr/src/mymaven/target" \
  -v "$PWD/../../target:/usr/src/mymaven/../target" \
```

---

现在我修改了挂载配置。主要变更：

1. 将整个`.m2`目录的挂载改为只挂载其中的`repository`子目录：
   ```
   -v "/home/<USER>/.m2/repository":/root/.m2/repository \
   ```

这样做的好处是：
- 只挂载Maven仓库目录，这样不会干扰容器内的settings.xml文件
- 容器内将使用自带的默认Maven设置
- 同时保留了本地Maven仓库缓存的优势，避免重复下载依赖

这个修改应该能解决"settings.xml是目录"的问题，因为现在我们不再覆盖容器内的settings.xml，而是使用Docker镜像自带的配置文件。

如果你的Maven构建需要特定的settings.xml配置（如私有仓库凭证等），可能需要另外创建一个正确的settings.xml文件并挂载。

---

_**User**_

[INFO] Downloading from central: https://repo.maven.apache.org/maven2/xyz/erupt/erupt/1.11.7.6/erupt-1.11.7.6.pom
[ERROR] [ERROR] Some problems were encountered while processing the POMs:
[WARNING] 'version' contains an expression but should be a constant. @ line 15, column 14
[WARNING] 'dependencies.dependency.(groupId:artifactId:type:classifier)' must be unique: com.dlywxt.external.sso.sdk:external-sso2sdk:jar -> duplicate declaration of version 1.0.2 @ line 202, column 21
[FATAL] Non-resolvable parent POM for com.daliangang:daliangang-workbench:${project.parent.version}: Could not transfer artifact xyz.erupt:erupt:pom:1.11.7.6 from/to maven-default-http-blocker (http://0.0.0.0/): Blocked mirror for repositories: [releases (http://svc.yundingyun.net:8081/repository/maven-releases/, default, releases)] and 'parent.relativePath' points at wrong local POM @ line 6, column 13
 @ 
[ERROR] The build could not read 1 project -> [Help 1]
org.apache.maven.project.ProjectBuildingException: Some problems were encountered while processing the POMs:
[WARNING] 'version' contains an expression but should be a constant. @ line 15, column 14
[WARNING] 'dependencies.dependency.(groupId:artifactId:type:classifier)' must be unique: com.dlywxt.external.sso.sdk:external-sso2sdk:jar -> duplicate declaration of version 1.0.2 @ line 202, column 21
[FATAL] Non-resolvable parent POM for com.daliangang:daliangang-workbench:${project.parent.version}: Could not transfer artifact xyz.erupt:erupt:pom:1.11.7.6 from/to maven-default-http-blocker (http://0.0.0.0/): Blocked mirror for repositories: [releases (http://svc.yundingyun.net:8081/repository/maven-releases/, default, releases)] and 'parent.relativePath' points at wrong local POM @ line 6, column 13
	at org.apache.maven.project.CachingProjectBuilder.build(CachingProjectBuilder.java:378)
	at org.apache.maven.graph.DefaultGraphBuilder.collectProjects(DefaultGraphBuilder.java:414)
	at org.apache.maven.graph.DefaultGraphBuilder.getProjectsForMavenReactor(DefaultGraphBuilder.java:405)
	at org.apache.maven.graph.DefaultGraphBuilder.build(DefaultGraphBuilder.java:82)
	at org.apache.maven.DefaultMaven.buildGraph(DefaultMaven.java:542)
	at org.apache.maven.DefaultMaven.doExecute(DefaultMaven.java:219)
	at org.apache.maven.DefaultMaven.doExecute(DefaultMaven.java:192)
	at org.apache.maven.DefaultMaven.execute(DefaultMaven.java:105)
	at org.apache.maven.cli.DaemonMavenCli.execute(DaemonMavenCli.java:707)
	at org.apache.maven.cli.DaemonMavenCli.doMain(DaemonMavenCli.java:242)
	at org.apache.maven.cli.DaemonMavenCli.main(DaemonMavenCli.java:218)
	at org.mvndaemon.mvnd.daemon.Server.handle(Server.java:593)
	at org.mvndaemon.mvnd.daemon.Server.client(Server.java:269)
	at org.mvndaemon.mvnd.daemon.Server.accept(Server.java:236)
	at java.lang.Thread.run(Thread.java:750)
[ERROR]   
[ERROR]   The project com.daliangang:daliangang-workbench:${project.parent.version} (/usr/src/mymaven/pom.xml) has 1 error
[ERROR]     Non-resolvable parent POM for com.daliangang:daliangang-workbench:${project.parent.version}: Could not transfer artifact xyz.erupt:erupt:pom:1.11.7.6 from/to maven-default-http-blocker (http://0.0.0.0/): Blocked mirror for repositories: [releases (http://svc.yundingyun.net:8081/repository/maven-releases/, default, releases)] and 'parent.relativePath' points at wrong local POM @ line 6, column 13 -> [Help 2]
org.apache.maven.model.resolution.UnresolvableModelException: Could not transfer artifact xyz.erupt:erupt:pom:1.11.7.6 from/to maven-default-http-blocker (http://0.0.0.0/): Blocked mirror for repositories: [releases (http://svc.yundingyun.net:8081/repository/maven-releases/, default, releases)] 有报错了

---

_**Assistant**_



---

收到了，亲爱的同学。

```思考分析
# Maven错误分析
- 核心错误: Maven仓库HTTP访问被阻止
- 详细错误: "Blocked mirror for repositories: [releases (http://svc.yundingyun.net:8081/repository/maven-releases/)]"
- 原因: Maven 3.8+默认阻止HTTP仓库访问(只允许HTTPS)
- 解决方向: 需要配置Maven允许HTTP仓库
```

问题已经变成了Maven仓库访问问题。Maven 3.8及以上版本默认阻止了HTTP协议的仓库访问，而你的项目需要访问内部HTTP协议的仓库`http://svc.yundingyun.net:8081/repository/maven-releases/`。

需要创建一个自定义的Maven `settings.xml`来解决这个问题：

---

```diff
- 
+ <?xml version="1.0" encoding="UTF-8"?>
+ <settings xmlns="http://maven.apache.org/SETTINGS/1.0.0"
+           xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
+           xsi:schemaLocation="http://maven.apache.org/SETTINGS/1.0.0 http://maven.apache.org/xsd/settings-1.0.0.xsd">
+   
+   <mirrors>
+     <!-- 配置允许HTTP协议的镜像 -->
+     <mirror>
+       <id>insecure-repo</id>
+       <mirrorOf>external:http:*</mirrorOf>
+       <url>http://svc.yundingyun.net:8081/repository/maven-releases/</url>
+       <blocked>false</blocked>
+     </mirror>
+   </mirrors>
+   
+   <profiles>
+     <profile>
+       <id>allow-insecure-repos</id>
+       <repositories>
+         <repository>
+           <id>central</id>
+           <url>https://repo.maven.apache.org/maven2</url>
+         </repository>
+         <repository>
+           <id>releases</id>
+           <url>http://svc.yundingyun.net:8081/repository/maven-releases/</url>
+           <releases>
+             <enabled>true</enabled>
+           </releases>
+           <snapshots>
+             <enabled>false</enabled>
+           </snapshots>
+         </repository>
+       </repositories>
+     </profile>
+   </profiles>
+   
+   <activeProfiles>
+     <activeProfile>allow-insecure-repos</activeProfile>
+   </activeProfiles>
+   
+ </settings> 
```

---

现在让我们修改构建脚本，使用这个自定义的Maven设置文件：

---

```diff
  -v "$PWD"/:/usr/src/mymaven \
  -v "/home/<USER>/.m2/repository":/root/.m2/repository \
+ -v "$PWD/maven-settings.xml":/root/.m2/settings.xml \
  -v "$PWD/../../target:/usr/src/mymaven/target" \
  -v "$PWD/../../target:/usr/src/mymaven/../target" \
```

---

我做了以下修改：

1. 创建了一个`maven-settings.xml`文件，它：
   - 允许访问HTTP仓库
   - 配置了你需要的内部仓库`http://svc.yundingyun.net:8081/repository/maven-releases/`
   - 启用了适当的仓库配置

2. 修改`build-java.sh`脚本挂载新创建的设置文件：
   ```
   -v "$PWD/maven-settings.xml":/root/.m2/settings.xml \
   ```

这样Maven将允许使用HTTP协议的仓库，解决了"Blocked mirror"的错误问题。

请尝试重新执行构建脚本，看是否解决了问题。如果仍有问题，可能需要根据具体错误进一步调整Maven配置。

---

_**User**_

/home/<USER>/settings.xml 有值

---

_**Assistant**_



---

收到了，亲爱的同学。

既然服务器上已经有`/home/<USER>/settings.xml`文件，我们可以直接使用它来替代我们创建的配置文件。让我修改构建脚本：

---

```diff
  -v "$PWD"/:/usr/src/mymaven \
  -v "/home/<USER>/.m2/repository":/root/.m2/repository \
- -v "$PWD/maven-settings.xml":/root/.m2/settings.xml \
+ -v "/home/<USER>/settings.xml":/root/.m2/settings.xml \
  -v "$PWD/../../target:/usr/src/mymaven/target" \
  -v "$PWD/../../target:/usr/src/mymaven/../target" \
```

---

我修改了构建脚本，现在改为使用服务器上已存在的Maven配置文件：

```
-v "/home/<USER>/settings.xml":/root/.m2/settings.xml \
```

这个修改将挂载服务器上的`/home/<USER>/settings.xml`到容器内的`/root/.m2/settings.xml`，这样Maven构建时就会使用这个已经配置好的settings文件。

由于此文件已经在服务器上存在，它很可能已经包含了允许访问你的内部Maven仓库的正确配置，因此应该能解决之前的"Blocked mirror"错误。

如果构建过程中还有其他问题，请告诉我具体的错误信息，我会继续帮你解决。

---

_**User**_

WARNING] 'version' contains an expression but should be a constant. @ line 15, column 14
[WARNING] 'dependencies.dependency.(groupId:artifactId:type:classifier)' must be unique: com.dlywxt.external.sso.sdk:external-sso2sdk:jar -> duplicate declaration of version 1.0.2 @ line 202, column 21
[FATAL] Non-resolvable parent POM for com.daliangang:daliangang-workbench:${project.parent.version}: Could not transfer artifact xyz.erupt:erupt:pom:1.11.7.6 from/to maven-default-http-blocker (http://0.0.0.0/): Blocked mirror for repositories: [releases (http://svc.yundingyun.net:8081/content/groups/public/, default, releases+snapshots)] and 'parent.relativePath' points at wrong local POM @ line 6, column 13
 @ 
[ERROR] The build could not read 1 project -> [Help 1]
org.apache.maven.project.ProjectBuildingException: Some problems were encountered while processing the POMs:
[WARNING] 'version' contains an expression but should be a constant. @ line 15, column 14
[WARNING] 'dependencies.dependency.(groupId:artifactId:type:classifier)' must be unique: com.dlywxt.external.sso.sdk:external-sso2sdk:jar -> duplicate declaration of version 1.0.2 @ line 202, column 21
[FATAL] Non-resolvable parent POM for com.daliangang:daliangang-workbench:${project.parent.version}: Could not transfer artifact xyz.erupt:erupt:pom:1.11.7.6 from/to maven-default-http-blocker (http://0.0.0.0/): Blocked mirror for repositories: [releases (http://svc.yundingyun.net:8081/content/groups/public/, default, releases+snapshots)] and 'parent.relativePath' points at wrong local POM @ line 6, column 13
	at org.apache.maven.project.CachingProjectBuilder.build(CachingProjectBuilder.java:378)
	at org.apache.maven.graph.DefaultGraphBuilder.collectProjects(DefaultGraphBuilder.java:414)
	at org.apache.maven.graph.DefaultGraphBuilder.getProjectsForMavenReactor(DefaultGraphBuilder.java:405)
	at org.apache.maven.graph.DefaultGraphBuilder.build(DefaultGraphBuilder.java:82)
	at org.apache.maven.DefaultMaven.buildGraph(DefaultMaven.java:542)
	at org.apache.maven.DefaultMaven.doExecute(DefaultMaven.java:219)
	at org.apache.maven.DefaultMaven.doExecute(DefaultMaven.java:192)
	at org.apache.maven.DefaultMaven.execute(DefaultMaven.java:105)
	at org.apache.maven.cli.DaemonMavenCli.execute(DaemonMavenCli.java:707)
	at org.apache.maven.cli.DaemonMavenCli.doMain(DaemonMavenCli.java:242)
	at org.apache.maven.cli.DaemonMavenCli.main(DaemonMavenCli.java:218)
	at org.mvndaemon.mvnd.daemon.Server.handle(Server.java:593)
	at org.mvndaemon.mvnd.daemon.Server.client(Server.java:269)
	at org.mvndaemon.mvnd.daemon.Server.accept(Server.java:236)
	at java.lang.Thread.run(Thread.java:750)
[ERROR]   
[ERROR]   The project com.daliangang:daliangang-workbench:${project.parent.version} (/usr/src/mymaven/pom.xml) has 1 error
[ERROR]     Non-resolvable parent POM for com.daliangang:daliangang-workbench:${project.parent.version}: Could not transfer artifact xyz.erupt:erupt:pom:1.11.7.6 from/to maven-default-http-blocker (http://0.0.0.0/): Blocked mirror for repositories: [releases (http://svc.yundingyun.net:8081/content/groups/public/, default, releases+snapshots)] and 'parent.relativePath' points at wrong local POM @ line 6, column 13 -> [Help 2]
org.apache.maven.model.resolution.UnresolvableModelException: Could not transfer artifact xyz.erupt:erupt:pom:1.11.7.6 from/to maven-default-http-blocker (http://0.0.0.0/): Blocked mirror for repositories: [releases (http://svc.yundingyun.net:8081/content/groups/public/, default, releases+snapshots)] 还是报错，这是为啥

---

_**Assistant**_



---

收到了，亲爱的同学。

```思考分析
# Maven HTTP阻止问题
- 核心错误: 仍然出现"Blocked mirror for repositories"
- 错误详情: 无法访问 http://svc.yundingyun.net:8081/content/groups/public/
- 原因分析: 
  1. Maven 3.8+默认阻止HTTP访问
  2. 挂载的settings.xml可能没有配置允许HTTP访问
  3. 需要修改Maven安全设置或命令行参数
```

虽然我们已经挂载了服务器上的`settings.xml`，但Maven仍然阻止了HTTP仓库访问。我们可以直接通过Maven命令行参数来解决这个问题：

---

```diff
  -w /usr/src/mymaven \
  mailbyms/mvnd:0.9.0-jdk-8 mvnd \
- -e clean package -T 1C -Dmaven.compile.fork=true -Dmaven.test.skip=true -Dmaven.clean.failOnError=false
+ -e clean package -T 1C -Dmaven.compile.fork=true -Dmaven.test.skip=true -Dmaven.clean.failOnError=false -Dmaven.wagon.http.ssl.insecure=true -Dmaven.wagon.http.ssl.allowall=true -Dmaven.wagon.http.ssl.ignore.validity.dates=true -Dmaven.resolver.transport=wagon
  
  
```

---

还需要添加一个关键参数来禁用Maven的HTTP阻止器：

---

```diff
  -w /usr/src/mymaven \
  mailbyms/mvnd:0.9.0-jdk-8 mvnd \
- -e clean package -T 1C -Dmaven.compile.fork=true -Dmaven.test.skip=true -Dmaven.clean.failOnError=false -Dmaven.wagon.http.ssl.insecure=true -Dmaven.wagon.http.ssl.allowall=true -Dmaven.wagon.http.ssl.ignore.validity.dates=true -Dmaven.resolver.transport=wagon
+ -e -Dmaven.security.allowlist=/usr/src/mymaven/allowlist.txt clean package -T 1C -Dmaven.compile.fork=true -Dmaven.test.skip=true -Dmaven.clean.failOnError=false -Dmaven.wagon.http.ssl.insecure=true -Dmaven.wagon.http.ssl.allowall=true -Dmaven.wagon.http.ssl.ignore.validity.dates=true -Dmaven.resolver.transport=wagon
  
  
```

---

现在让我们创建一个允许列表文件，明确允许HTTP仓库：

---

