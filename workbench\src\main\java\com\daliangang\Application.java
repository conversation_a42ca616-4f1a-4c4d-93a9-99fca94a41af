package com.daliangang;

import de.codecentric.boot.admin.server.config.EnableAdminServer;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.domain.EntityScan;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.scheduling.annotation.EnableAsync;
import xyz.erupt.core.annotation.EruptAttachmentUpload;
import xyz.erupt.core.annotation.EruptScan;
import xyz.erupt.upload.MinioStoreProxy;
import xyz.erupt.upms.model.auth.DataAuthFields;

import java.util.TimeZone;

@SpringBootApplication
@EruptScan
@EntityScan
//@EruptAttachmentUpload(CosStoreProxy.class)
@EruptAttachmentUpload(MinioStoreProxy.class)
@EnableFeignClients
@EnableCaching
@EnableAsync
@DataAuthFields("company")
@EnableAdminServer
public class Application {

    public static void main(String[] args) {
        //System.setProperty("nashorn.args","--no-deprecation-warning");
        TimeZone.setDefault(TimeZone.getTimeZone("GMT+8"));
        SpringApplication.run(Application.class, args);
    }
}
