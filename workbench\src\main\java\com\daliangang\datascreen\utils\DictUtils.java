package com.daliangang.datascreen.utils;

import xyz.erupt.jpa.dao.EruptDao;
import xyz.erupt.toolkit.utils.EruptDaoUtils;
import xyz.erupt.upms.model.EruptDict;
import xyz.erupt.upms.model.EruptDictItem;

import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 字典工具类
 */
public class DictUtils {

    /**
     * 根据字典类型编码和字典项编码获取字典项的中文名称
     * @param dictTypeCode 字典类型编码
     * @param itemCode 字典项编码
     * @return 对应的中文名称
     */
    public static String getDictItemValue(String dictTypeCode, String itemCode) {
        // 1. 查询字典ID
        Long dictionaryId = queryDictionaryId(dictTypeCode);
        if (dictionaryId == null) {
            return itemCode;
        }

        // 2. 根据字典ID和itemCode查询具体字典项
        EruptDao eruptDao = EruptDaoUtils.getEruptDao();
        EruptDictItem dictItem = eruptDao.queryEntity(
                EruptDictItem.class,
                "erupt_dict_id = :eruptDictId AND code = :code",
                new HashMap<String, Object>() {{
                    put("eruptDictId", dictionaryId);
                    put("code", itemCode);
                }}
        );

        return dictItem != null ? dictItem.getName() : itemCode;
    }

    /**
     * 查询字典ID
     * @param dictTypeCode 字典类型编码
     * @return 字典ID
     */
    private static Long queryDictionaryId(String dictTypeCode) {
        EruptDao eruptDao = EruptDaoUtils.getEruptDao();
        EruptDict dict = eruptDao.queryEntity(EruptDict.class, "code = :code",
                new HashMap<String, Object>() {{
                    put("code", dictTypeCode);
                }}
        );
        return dict != null ? dict.getId() : null;
    }

    /**
     * 获取字典类型下的所有字典项
     * @param dictTypeCode 字典类型编码
     * @return 字典项Map，key为编码，value为名称
     */
    public static Map<String, String> getDictItemMap(String dictTypeCode) {
        Map<String, String> resultMap = new HashMap<>();

        // 1. 查询字典ID
        Long dictionaryId = queryDictionaryId(dictTypeCode);
        if (dictionaryId == null) {
            return resultMap;
        }

        // 2. 查询所有字典项
        EruptDao eruptDao = EruptDaoUtils.getEruptDao();
        List<EruptDictItem> items = eruptDao.queryEntityList(
                EruptDictItem.class,
                "erupt_dict_id = :eruptDictId",
                new HashMap<String, Object>() {{
                    put("eruptDictId", dictionaryId);
                }}
        );

        // 3. 转换为Map
        for (EruptDictItem item : items) {
            resultMap.put(item.getCode(), item.getName());
        }

        return resultMap;
    }

    /**
     * 获取字典类型下的所有字典项列表
     * @param dictTypeCode 字典类型编码
     * @return 字典项列表
     */
    public static List<EruptDictItem> getDictItemList(String dictTypeCode) {
        // 1. 查询字典ID
        Long dictionaryId = queryDictionaryId(dictTypeCode);
        if (dictionaryId == null) {
            return Collections.emptyList();
        }

        // 2. 查询所有字典项
        EruptDao eruptDao = EruptDaoUtils.getEruptDao();
        return eruptDao.queryEntityList(
                EruptDictItem.class,
                "erupt_dict_id = :eruptDictId ORDER BY sort",
                new HashMap<String, Object>() {{
                    put("eruptDictId", dictionaryId);
                }}
        );
    }
}