/*
 * Copyright © 2020-2035 yundingyun All rights reserved.
 */
package com.daliangang.rndpub.entity;

import com.daliangang.rndpub.form.InspectionrResultsReviewForm;
import com.daliangang.rndpub.operation.InspectionResultsExport;
import com.daliangang.rndpub.operation.InspectionResultsReviewHandler;
import com.daliangang.rndpub.operation.InspectionResultsViewDetailHandler;
import com.daliangang.rndpub.proxy.InspectionrResultsViewDataProxy;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import xyz.erupt.annotation.Erupt;
import xyz.erupt.annotation.EruptField;
import xyz.erupt.annotation.sub_erupt.Power;
import xyz.erupt.annotation.sub_erupt.RowOperation;
import xyz.erupt.annotation.sub_erupt.Tpl;
import xyz.erupt.annotation.sub_erupt.Tree;
import xyz.erupt.annotation.sub_field.Edit;
import xyz.erupt.annotation.sub_field.EditType;
import xyz.erupt.annotation.sub_field.Readonly;
import xyz.erupt.annotation.sub_field.View;
import xyz.erupt.annotation.sub_field.sub_edit.*;
import xyz.erupt.toolkit.db.Comment;
import xyz.erupt.toolkit.handler.EruptDictCodeChoiceFetchHandler;
import xyz.erupt.toolkit.handler.RemoteCallChoiceFetchHandler;
import xyz.erupt.toolkit.handler.RemoteEntityChoiceFetchHandler;
import xyz.erupt.toolkit.handler.SqlChoiceFetchHandler;
import xyz.erupt.toolkit.utils.TplUtils;
import xyz.erupt.upms.handler.GotoPageHandler;
import xyz.erupt.upms.model.auth.DataAuthHandler;
import xyz.erupt.upms.model.auth.DataAuthModel;

import javax.persistence.Entity;
import javax.persistence.Lob;
import javax.persistence.Table;
import javax.persistence.Transient;
import java.util.Date;

@Erupt(name = "检查结果管理",
        power = @Power(
                add = false,
                delete = false,
                edit = false,
                viewDetails = false,
                query = true,
                export = false,
                importable = false
        ),
        dataProxy = InspectionrResultsViewDataProxy.class,
        tree = @Tree(id = "id", label = "name", pid = "parent.id"),
        drills = {
//        @Drill(
//                title = "整改内容", icon = "fa fa-list-alt",
//                link = @Link(
//                        linkErupt = Rectify.class, joinColumn = "inspectionResults.id"
//                ))
        },
        rowOperation = {
//            @RowOperation(
//                    title = "复查", icon = "fa fa-pause",
//                    ifExpr = "item.inspectionResult == '待审核'",
//                    code = TplUtils.REMOTE_ERUPT_CODE + "review",
//                    eruptClass = InspectionResultsReviewHandler.InspectionrResultsReviewForm.class,
//                    operationHandler = InspectionResultsReviewHandler.class, mode = RowOperation.Mode.SINGLE
//            ),
//            @RowOperation(
//                    icon = "fa fa-download",
//                    title = "检查结果导出",
////                    code = TplUtils.REMOTE_ERUPT_CODE + "export",
//                    operationHandler = InspectionResultsExport.class,
//                    mode = RowOperation.Mode.SINGLE
//            )
                @RowOperation(
                        icon = "fa fa-search-plus",
                        title = "详情",
                        type = RowOperation.Type.TPL,
                        tplHeight = "600px",
                        tpl = @Tpl(path = "tpl/dlg-check.tpl",tplHandler = InspectionResultsViewDetailHandler.class),
//                    code = TplUtils.REMOTE_ERUPT_CODE + "export",

                        mode = RowOperation.Mode.SINGLE
                ),
                @RowOperation(
                        title = "复查", icon = "fa fa-pause",
                        ifExpr = "item.rectificationStatus == '已整改' && item.rectificationAuditStatus == '待审核'",
                        code = TplUtils.REMOTE_ERUPT_CODE + "review",
                        eruptClass = InspectionrResultsReviewForm.class,
                        operationHandler = InspectionResultsReviewHandler.class, mode = RowOperation.Mode.SINGLE,
                        confirmMsg ="<font color='red'>审核后不能修改</font>"
                ),
                @RowOperation(
                        icon = "fa fa-download",
                        title = "检查结果导出",
//                    code = TplUtils.REMOTE_ERUPT_CODE + "export",
                        operationHandler = InspectionResultsExport.class,
                        mode = RowOperation.Mode.SINGLE
                ),
                @RowOperation(title = "返回", icon = "fa fa-mail-reply",
                        confirm=false,
                        operationParam="RectificationManage",
                        operationHandler = GotoPageHandler.class,
                        mode = RowOperation.Mode.BUTTON),

        }
)
@Table(name = "tb_inspection_results")
@Entity
@Getter
@Setter
@Comment("检查结果管理")
@ApiModel("检查结果管理")
@ToString
//@SystemExcelTemplate(erupt = InspectionrResults.class, template = "检查结果-模板.xlsx")
public class InspectionResultsView extends DataAuthModel {

        @EruptField(
                views = @View(title = "检查对象",show = false),
                edit = @Edit(title = "检查对象", type = EditType.CHOICE, notNull = false, readonly = @Readonly(exprHandler = DataAuthHandler.class),
                        show = false,
                        search = @Search,
                        choiceType = @ChoiceType(fullSpan = true,reload = true,fetchHandler = RemoteEntityChoiceFetchHandler.class, fetchHandlerParams = {"main", "Enterprise", "org_code,name"})))
        @Transient
        private String company;


        @EruptField(
                views = @View(title = "检查名称",show = false),
                edit = @Edit(title = "检查名称", type = EditType.CHOICE, notNull = true,search = @Search,
                        choiceType = @ChoiceType(anewFetch = true ,fetchHandler = RemoteCallChoiceFetchHandler.class, fetchHandlerParams ={"main","erupt-api/get/getInspectionName"})
                ))
        @Comment("检查名称")
        @ApiModelProperty("检查名称")
        private String inspectionName;


        @EruptField(
                views = @View(title = "整改状态", show = true),
                edit = @Edit(title = "整改状态", type = EditType.CHOICE, notNull = false, show = false,search = @Search,
                        choiceType = @ChoiceType(fetchHandler = EruptDictCodeChoiceFetchHandler.class, fetchHandlerParams = "rectificationStatus")))
        @Comment("整改状态")
        @ApiModelProperty("整改状态")
        private String rectificationStatus;

        @EruptField(
                views = @View(title = "审核状态", show = true),
                edit = @Edit(title = "审核状态", type = EditType.CHOICE, notNull = false, show = false,search = @Search,
                        choiceType = @ChoiceType(fetchHandler = EruptDictCodeChoiceFetchHandler.class, fetchHandlerParams = "rectificationAuditStatus")))
        @Comment("审核状态")
        @ApiModelProperty("审核状态")
        private String rectificationAuditStatus;


        @EruptField(
                views = @View(title = "复查结果", show = true),
                edit = @Edit(title = "复查结果", type = EditType.CHOICE, notNull = false, show = false,
                        choiceType = @ChoiceType(fetchHandler = EruptDictCodeChoiceFetchHandler.class, fetchHandlerParams = "rectificationReviewResults")))
        @Comment("复查结果")
        @ApiModelProperty("复查结果")
        private String inspectionResult;

        @EruptField(
                views = @View(title = "检查对象"),
                edit = @Edit(title = "检查对象", type = EditType.REFERENCE_TREE, notNull = true,
                        referenceTreeType = @ReferenceTreeType(type = ReferenceTreeType.Type.CHOICE, dependField = "inspectionName", dependColumn = "procedure_id"),
                        choiceType = @ChoiceType(fetchHandler = SqlChoiceFetchHandler.class,
                                fetchHandlerParams = {"select org_code,enterprise_name from tb_enterprise_information", "5000", "and state=1"})))
        @Comment("检查对象")
        @ApiModelProperty("检查对象")
        private String checkObject;

        @EruptField(
                views = @View(title = "检查事项"),
                edit = @Edit(title = "检查事项", type = EditType.REFERENCE_TREE, notNull = true,
                        referenceTreeType = @ReferenceTreeType(type = ReferenceTreeType.Type.CHOICE,dependField = "",dependColumn = ""),
                        choiceType = @ChoiceType(fetchHandler = SqlChoiceFetchHandler.class,
                                fetchHandlerParams = {"select distinct inspection_items from tb_inspection_items_management", "5000", "and 1=1"})))
        @Comment("检查事项")
        @ApiModelProperty("检查事项")
        private String inspectionItems;

        @EruptField(
                views = @View(title = "检查内容（一级）",show = false),
                edit = @Edit(title = "检查内容（一级）", type = EditType.REFERENCE_TREE, notNull = true,
                        referenceTreeType = @ReferenceTreeType(type = ReferenceTreeType.Type.CHOICE, dependField = "inspectionItems", dependColumn = "inspection_items"),
                        choiceType = @ChoiceType(fetchHandler = SqlChoiceFetchHandler.class,
                                fetchHandlerParams = {"select distinct check_first from tb_inspection_items_management", "5000", "and 1=1"})))
        @Comment("检查内容（一级）")
        @ApiModelProperty("检查内容（一级）")
        private String inspectionContent;

        @EruptField(
                views = @View(title = "问题描述"),
                edit = @Edit(title = "问题描述", type = EditType.TEXTAREA,  notNull = true))
        @Comment("问题描述")
        @ApiModelProperty("问题描述")
        private @Lob String problemDescription;


        @EruptField(
                views = @View(title = "检查依据"),
                edit = @Edit(title = "检查依据", type = EditType.TEXTAREA, notNull = true,
                        inputType = @InputType))
        @Comment("检查依据")
        @ApiModelProperty("检查依据")
        @Lob
        private String inspectionBasis;

        @EruptField(
                views = @View(title = "检查依据附件名称", show = false),
                edit = @Edit(title = "检查依据附件名称", type = EditType.ATTACHMENT,
                        attachmentType = @AttachmentType(type = AttachmentType.Type.BASE, maxLimit = 1, fileTypes = {".pdf", ".doc", ".docx", ".xlsx", ".png", ".jpg", ".xls", ".ppt", ".pptx", ".zip", ".rar"})))
        @Comment("检查依据附件名称")
        @ApiModelProperty("检查依据附件名称")
        private String inspectionBasisFile;

        @EruptField(
                views = @View(title = "整改建议"),
                edit = @Edit(title = "整改建议", type = EditType.TEXTAREA, notNull = true,
                        inputType = @InputType))
        @Comment("整改建议")
        @ApiModelProperty("整改建议")
        @Lob
        private String proposal;

        @EruptField(
                views = @View(title = "整改建议附件名称", show = false),
                edit = @Edit(title = "整改建议附件名称", type = EditType.ATTACHMENT,
                        attachmentType = @AttachmentType(type = AttachmentType.Type.BASE, maxLimit = 1, fileTypes = {".pdf", ".doc", ".docx", ".xlsx", ".png", ".jpg", ".xls", ".ppt", ".pptx", ".zip", ".rar"})))
        @Comment("整改建议附件名称")
        @ApiModelProperty("整改建议附件名称")
        private String proposalFile;



        @EruptField(
                views = @View(title = "整改截止时间（年月日）"),
                edit = @Edit(title = "整改截止时间（年月日）", type = EditType.DATE,notNull = true,
                        dateType = @DateType))
        @Comment("整改截止时间（年月日）")
        @ApiModelProperty("整改截止时间（年月日）")
        private Date deadline;

        @EruptField(
                views = @View(title = "是否逾期", show = true,width = "100px"),
                edit = @Edit(title = "是否逾期", type = EditType.CHOICE, notNull = false, show = false,search = @Search(vague = true),
                        choiceType = @ChoiceType(vl = {
                                @VL(value = "0",label = "未逾期"),
                                @VL(value = "1",label = "已逾期"),
                                @VL(value = "2",label = "逾期已整改")
                        })))
        @Comment("是否逾期")
        @ApiModelProperty("是否逾期")
        private String beOverdue;

        @EruptField(
                views = @View(title = "提交人",show = false),
                edit = @Edit(title = "提交人", type = EditType.INPUT, notNull = false, show = false,readonly = @Readonly))
        @Comment("提交人")
        @ApiModelProperty("提交人")
        private String submitPerson;

        @EruptField(
                views = @View(title = "复查人姓名", show = false),
                edit = @Edit(title = "复查人姓名", type = EditType.INPUT, notNull = false, show = false,readonly = @Readonly))
        @Comment("复查人姓名")
        @ApiModelProperty("复查人姓名")
        private String reviewer;

        @EruptField(
                views = @View(title = "发布状态", show = false),
                edit = @Edit(title = "发布状态",//search = @Search(vague = true),
                        type = EditType.BOOLEAN,notNull = false, show = false,boolType = @BoolType(trueText = "已发布", falseText = "未发布")))
        @Comment("发布状态")
        @ApiModelProperty("发布状态")
        private Boolean publishStatus;
        //--------------------------企业整改结果----------------------------------------
        @EruptField(
                views = @View(title = "整改说明", show = false),
                edit = @Edit(title = "整改说明", type = EditType.TEXTAREA, notNull = false,show = false))
        private @Lob String description;

        @EruptField(
                views = @View(title = "整改证明材料", show = false),
                edit = @Edit(title = "整改证明材料", type = EditType.ATTACHMENT, notNull = false,show = false,
                        attachmentType = @AttachmentType(type = AttachmentType.Type.BASE, maxLimit = 20, fileTypes = {".pdf", ".doc",".docx", ".xlsx", ".png", ".jpg", ".word", ".ppt", ".zip", ".rar"})))
        private String supportingMaterials;


        @EruptField(
                views = @View(title = "整改时间", show = false),
                edit = @Edit(title = "整改时间", type = EditType.DATE, notNull = false,show = false,
                        dateType = @DateType))
        private java.sql.Date rectificationTime;


//--------------------------政府复查结果----------------------------------------
        @EruptField(
                edit = @Edit(title = "整改结果", type = EditType.BOOLEAN, notNull = true,
                        boolType = @BoolType(trueText = "通过", falseText = "不通过")))
        @Comment("整改结果")
        @ApiModelProperty("整改结果")
        private Boolean rectificationResults;

        @EruptField(
                edit = @Edit(title = "重置整改时间", type = EditType.DATE, notNull = false,showBy=@ShowBy(dependField="rectificationResults",expr="value==0"),
                        dateType = @DateType))
        @Transient
        private java.sql.Date rectificationTime1;

        @Transient
        @EruptField(edit = @Edit(title = "备注", type = EditType.TEXTAREA))
        private @Lob String remark;



}
