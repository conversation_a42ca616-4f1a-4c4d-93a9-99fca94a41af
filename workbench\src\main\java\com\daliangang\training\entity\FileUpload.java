package com.daliangang.training.entity;

import com.daliangang.training.proxy.FileUploadDataProxy;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import xyz.erupt.annotation.Erupt;
import xyz.erupt.annotation.EruptField;
import xyz.erupt.annotation.sub_erupt.Power;
import xyz.erupt.annotation.sub_field.Edit;
import xyz.erupt.annotation.sub_field.EditType;
import xyz.erupt.annotation.sub_field.View;
import xyz.erupt.annotation.sub_field.sub_edit.AttachmentType;
import xyz.erupt.toolkit.db.Comment;
import xyz.erupt.toolkit.submit.CheckUpdate;
import xyz.erupt.upms.model.auth.DataAuthModel;

import javax.persistence.Entity;
import javax.persistence.Table;

/**
 * @Author: zengwen<PERSON>
 * @Description:
 * @Date: Created in 2023/5/25 16:57
 * @Modified By
 */
@Erupt(name = "专门上传文件的菜单", authVerify = false,
        power = @Power(delete = false, edit = false), dataProxy = FileUploadDataProxy.class)
@Table(name = "tb_file_upload")
@Entity
@Getter
@Setter
@Comment("专门上传文件的菜单")
@ApiModel("专门上传文件的菜单")
public class FileUpload extends DataAuthModel {

    @EruptField(
            views = @View(title = "文件名称", show = true),
            edit = @Edit(title = "文件名称", type = EditType.INPUT, notNull = false, show = false))
    @Comment("文件名称")
    @ApiModelProperty("文件名称")
    private String name;

    @EruptField(
            views = @View(title = "文件", show = true),
            edit = @Edit(title = "文件", type = EditType.ATTACHMENT, notNull = true,
                    attachmentType = @AttachmentType(type = AttachmentType.Type.BASE, maxLimit = 20, fileTypes = {".pdf", ".doc", ".docx", ".xlsx", ".png", ".jpg", ".xls", ".ppt", ".pptx", ".zip", ".rar", ".mp4", ".flv", ".avi", ".wmv", ".rmvb", "jpeg"})))
    @Comment("文件")
    @ApiModelProperty("文件")
    @CheckUpdate
    private String file;
}
