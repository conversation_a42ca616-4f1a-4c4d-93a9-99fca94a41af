package com.daliangang.workbench.handler;

import cn.hutool.core.collection.CollectionUtil;
import com.daliangang.workbench.entity.EmployeeInformation;
import org.springframework.stereotype.Service;
import xyz.erupt.annotation.fun.DataProxy;
import xyz.erupt.toolkit.utils.EruptDaoUtils;
import xyz.erupt.toolkit.utils.NotifyUtils;
import com.daliangang.workbench.entity.EruptRoleTemplatePost;

import java.util.List;

@Service
public class EmployeePostDataPrepareHandler implements DataProxy<EruptRoleTemplatePost> {

    @Override
    public void beforeDelete(EruptRoleTemplatePost eruptRoleTemplatePost) {
        String selectSql="select * from tb_employee_information where job_title like CONCAT('%','"+eruptRoleTemplatePost.getName()+"','%')";
        List<EmployeeInformation> employeeInformationList = EruptDaoUtils.selectOnes(selectSql, EmployeeInformation.class);
        if (CollectionUtil.isNotEmpty(employeeInformationList)){
            NotifyUtils.showErrorDialog("当前岗位下有员工数据,不能删除!");
        }


    }
}
