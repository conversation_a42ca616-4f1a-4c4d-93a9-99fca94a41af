package com.daliangang.device.controller;

import com.daliangang.core.DaliangangContext;
import com.daliangang.device.entity.Wharf;
import com.google.gson.internal.LinkedTreeMap;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import xyz.erupt.core.annotation.EruptRouter;
import xyz.erupt.core.module.MetaUserinfo;
import xyz.erupt.core.view.EruptApiModel;
import xyz.erupt.jpa.dao.EruptDao;
import xyz.erupt.toolkit.remote.RemoteProxyService;
import xyz.erupt.toolkit.utils.EruptDaoUtils;
import xyz.erupt.toolkit.utils.SqlUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since :2023/4/18:13:41
 */
@RestController
public class WharfController {

    @Resource
    private EruptDao eruptDao;
    @Resource
    private RemoteProxyService remoteProxyService;

    /**
     * 获取码头信息
     *
     * @param
     * @return
     */
    @RequestMapping("erupt-api/get/wharfName")
    @EruptRouter(verifyType = EruptRouter.VerifyType.LOGIN)
    public EruptApiModel getPortareaName() {
        MetaUserinfo remoteUserInfo = remoteProxyService.getRemoteUserInfo();
        if(DaliangangContext.isDepartmentUser()){
            //查全部
            ArrayList<LinkedTreeMap> result = new ArrayList<>();
            List<String> auth = remoteUserInfo.getAuth();
            List<Wharf> wharves = EruptDaoUtils.selectOnes("select * from tb_wharf where org_code " + SqlUtils.wrapIn(auth), Wharf.class);
            if (ObjectUtils.isNotEmpty(wharves)) {
                wharves.forEach(v -> {
                    LinkedTreeMap map = new LinkedTreeMap();
                    map.put("code", v.getId());
                    map.put("name", v.getName());
                    result.add(map);
                });
            }
            return EruptApiModel.successApi(result);
        }
        List<LinkedTreeMap> list = this.queryWharfs(remoteUserInfo.getOrg());
//        if (list.isEmpty())
//            list = this.queryWharfs("");
        return EruptApiModel.successApi(list);
    }

    private List<LinkedTreeMap> queryWharfs(String orgCode) {
        List<LinkedTreeMap> list1 = new ArrayList<>();
        String where = StringUtils.isEmpty(orgCode) ? "" : " where org_code='" + orgCode + "'";
        List<Wharf> wharves = EruptDaoUtils.selectOnes("select * from tb_wharf " + where, Wharf.class);
        if (ObjectUtils.isNotEmpty(wharves)) {
            wharves.forEach(v -> {
                LinkedTreeMap map = new LinkedTreeMap();
                map.put("code", v.getId());
                map.put("name", v.getName());
                list1.add(map);
            });
        }
        return list1;
    }
}
