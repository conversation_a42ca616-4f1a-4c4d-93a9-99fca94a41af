package com.daliangang.statistics.handler;

import com.daliangang.core.DaliangangContext;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import xyz.erupt.toolkit.utils.SqlUtils;
import xyz.erupt.upms.model.EruptOrg;
import xyz.erupt.upms.service.EruptUserService;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.util.List;
import java.util.Map;

/**
 * @Author: zengwensi
 * @Description:
 * @Date: Created in 2023/5/15 15:10
 * @Modified By
 */
@Component
public class CommonHandler {
    @Resource
    private EruptUserService eruptUserService;

    public String exprHandlerAuthority(String param, Map<String, Object> condition, String expr){
        String s = this.exprHandlerAuthoritySql(param, condition, expr);
        if(StringUtils.isNotEmpty(s)){
            String replaceSql =  " and company "+ s;
            expr = expr.replaceAll("#REPLACESQL",replaceSql) ;
        }

        return expr;
    }

    public String exprHandlerAuthorityInspect(String param, Map<String, Object> condition, String expr){
        String s = this.exprHandlerAuthoritySql(param, condition, expr);
        if(StringUtils.isNotEmpty(s)){
            String replaceSql =  " and org_code "+ s;
            expr = expr.replaceAll("#REPLACESQL",replaceSql) ;
        }

        return expr;
    }

    public String exprHandlerAuthorityCheckObject(String param, Map<String, Object> condition, String expr){
        String s = this.exprHandlerAuthoritySql(param, condition, expr);
        if(StringUtils.isNotEmpty(s)){
            String replaceSql =  " and check_object "+ s;
            expr = expr.replaceAll("#REPLACESQL",replaceSql) ;
        }

        return expr;
    }


    public String exprHandlerSearchDate(String param, Map<String, Object> condition, String expr){
        String nowYear ;
        if(null == condition.get("date")){
            nowYear = String.valueOf(LocalDate.now().getYear());
        }else{
            nowYear = (String)condition.get("date") ;
        }
        return expr.replaceAll("#searchDate",nowYear);
    }

    public String exprHandlerAuthoritySql(String param, Map<String, Object> condition, String expr){
        // 权限
        String replaceSql = "";
        //当前登录人是政府
        if (DaliangangContext.isDepartmentUser()) {
            List<String> auth = eruptUserService.getUserAuth();
            if (auth != null && !auth.isEmpty()) {
                replaceSql =  SqlUtils.wrapIn(auth) ;
            }
        } else if (DaliangangContext.isEnterpriseUser()) {
            //当前登录人是企业
            EruptOrg eruptOrg = eruptUserService.getCurrentEruptUser().getEruptOrg();
            if(null != eruptOrg){
                String orgcode = eruptOrg.getCode();
                replaceSql = " = "+ SqlUtils.wrapStr(orgcode) ;
            }
        }

        return replaceSql ;
    }
}
