package com.daliangang.workbench.patch;


import com.daliangang.safedaily.entity.Standardization;
import com.daliangang.safedaily.entity.StandardizationSenond;
import com.daliangang.safedaily.operation.StandardizationEscalationHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import xyz.erupt.devtools.patch.PlatformPatchHandler;
import xyz.erupt.jpa.dao.EruptDao;

import javax.annotation.Resource;
import javax.transaction.Transactional;
import java.util.ArrayList;
import java.util.List;

@Service
@Slf4j
public class StandardizationSecondPatch implements PlatformPatchHandler {

    @Override
    public String getPatchId() {
        return "1.11.7.6-patch-03";
    }

    @Override
    public String getName() {
        return "找回 港口企业安全生产标准化管理 第二年数据";
    }

    @Override
    public String getDesc() {
        return "找回 港口企业安全生产标准化管理 第二年数据 <br>";
    }

    @Resource
    private EruptDao eruptDao;

    @Resource
    private StandardizationEscalationHandler standardizationEscalationHandler ;

    @Override
    @Transactional
    public boolean execute() {
        List<Standardization> standardizationList = eruptDao.queryEntityList(Standardization.class);
        List<StandardizationSenond> standardizationSenonds = eruptDao.queryEntityList(StandardizationSenond.class);
        List<Long> standardizatioIds = new ArrayList();
        for (StandardizationSenond standardizationSenond : standardizationSenonds) {
            standardizatioIds.add(standardizationSenond.getStandardization().getId()) ;
        }
        for (Standardization standardization : standardizationList) {
            if(!standardizatioIds.contains(standardization.getId())){
                List<Standardization> list = new ArrayList<>();
                list.add(standardization);
                standardizationEscalationHandler.exec(list,null,null);
            }
        }
        return true ;
    }
}
