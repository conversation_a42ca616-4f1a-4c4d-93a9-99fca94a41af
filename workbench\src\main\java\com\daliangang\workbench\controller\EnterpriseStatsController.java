package com.daliangang.workbench.controller;


import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.daliangang.core.DaliangangContext;
import com.daliangang.workbench.entity.EnterpriseStats;
import com.daliangang.workbench.form.EnterpriseStatsForm;
import com.daliangang.workbench.sql.EnterpriseCertificateSql;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;
import xyz.erupt.annotation.query.Condition;
import xyz.erupt.core.annotation.EruptRouter;
import xyz.erupt.core.module.MetaUserinfo;
import xyz.erupt.core.util.EruptSpringUtil;
import xyz.erupt.core.view.Page;
import xyz.erupt.core.view.TableQueryVo;
import xyz.erupt.jpa.dao.EruptDao;
import xyz.erupt.toolkit.remote.RemoteProxyService;
import xyz.erupt.toolkit.utils.EruptDaoUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since :2023/5/22:20:33
 */
@RestController
public class EnterpriseStatsController {


    @Resource
    private RemoteProxyService remoteProxyService;

    @Resource
    private EruptDao eruptDao;

    @Resource
    EnterpriseCertificateSql enterpriseCertificateSql;

    @PostMapping("/erupt-api/data/table/EnterpriseStats")
    @EruptRouter(verifyType = EruptRouter.VerifyType.LOGIN)
    public Page getEnterpriseStats(@RequestBody TableQueryVo tableQueryVo){
        List<Condition> conditions = tableQueryVo.getCondition();
        if(null == conditions){
            conditions = new ArrayList<>();
        }
        Condition condition = new Condition("enterpriseStats",true);
        conditions.add(condition);
        Collection<Map<String,Object>> collection = new ArrayList<>();
        EnterpriseStats enterpriseStats = EruptSpringUtil.getBean(EnterpriseStats.class);
        MetaUserinfo remoteUserInfo = remoteProxyService.getRemoteUserInfo();
//        String te = "[\"ab1d1e57\",\"b8163fc2\",\"b30c5ea2\",\"f6336f73\",\"a49ec810\",\"99bddfc4\",\"c42ce061\",\"42b4ac7c\",\"b6a3581c\",\"d8c15335\",\"aee13da8\",\"b8a56789\",\"cf38bf23\",\"d17bf769\",\"df19414d\",\"8a90bb7a\",\"c5a5230a\",\"89b6efb5\",\"ce44f433\",\"2377f594\",\"9ebd7047\",\"0402e3fb\",\"ac38b915\",\"c665084e\",\"2a672653\",\"d153649d\",\"a5196ee5\",\"571a7045\",\"a9b76180\",\"8ce18d85\",\"c4af5b67\",\"67b4c229\",\"485ca73d\",\"ba8f09f7\",\"f3c37d3a\",\"2ba184e1\",\"d650f182\",\"75405747\",\"9ce70011\"]";
        List<String> orgCodeLists = remoteUserInfo.getAuth();
//        List<String> orgCodeLists = JSONUtil.toList(JSONUtil.toJsonStr(te),String.class) ;
        List<Condition> searchOrgCode = conditions.stream().filter(item -> "company".equals(item.getKey())).collect(Collectors.toList());
        if(searchOrgCode.size() !=0){
            orgCodeLists = Collections.singletonList((String) searchOrgCode.get(0).getValue());
        }

        orgCodeLists.forEach(v->{
            if (!v.equals(remoteUserInfo.getOrg())) {
                enterpriseStats.setOrgCode(v);
                enterpriseStats.setCompany(DaliangangContext.getEnterpriseName(v));


                String sql = enterpriseCertificateSql.selectNumThree(v);
                List<EnterpriseStatsForm> enterpriseStatsForms = EruptDaoUtils.selectOnes(sql, EnterpriseStatsForm.class);
                enterpriseStatsForms.forEach(m -> {
                    switch (m.getType()) {
                        case "泊位数量":
                            enterpriseStats.setBerthNum(m.getNum());

                        case "罐区数量":
                            enterpriseStats.setTankFarmNum(m.getNum());

                        case "罐组数量":
                            enterpriseStats.setTankGroupNum(m.getNum());

                        case "储罐数量":
                            enterpriseStats.setStorageTankNum(m.getNum());
                            enterpriseStats.setStorageTankVolume(m.getVolume());

                        case "港经证有效期":
                            enterpriseStats.setHongKongCertificateValidity(m.getHongKongCertificateValidity());

                        case "水路运输从业资格证":
                            enterpriseStats.setWaterTransport(m.getZsnum());

                        case "特种设备操作证书":
                            enterpriseStats.setSpecialEquipment(m.getZsnum());

                        case "特种作业操作证书":
                            enterpriseStats.setSpecialOperation(m.getZsnum());

                        case "其他证书":
                            enterpriseStats.setOtherCertificates(m.getZsnum());
                        case "重大风险源备案":
                            enterpriseStats.setRiskSourceFilingNum(m.getZdnum());

                    }
                });


                //查询附征有效期
                String sql1 = enterpriseCertificateSql.selectFzThree(v);
                EnterpriseStatsForm enterpriseStatsForm1 = EruptDaoUtils.selectOne(sql1, EnterpriseStatsForm.class);
                if (ObjectUtils.isNotEmpty(enterpriseStatsForm1)) {
                    enterpriseStats.setCollateralValidity(enterpriseStatsForm1.getFz());
                }

                // 预案备案
                String sql5 = enterpriseCertificateSql.selectYaThree(v);
                EnterpriseStatsForm enterpriseStatsForm5 = EruptDaoUtils.selectOne(sql5, EnterpriseStatsForm.class);
                if (ObjectUtils.isNotEmpty(enterpriseStatsForm5)) {
                    if (enterpriseStatsForm5.getYa().equals("过期")) {
                        enterpriseStats.setRecordOfPlanNum(enterpriseStatsForm5.getYa());
                    } else {
                        String sql6 = enterpriseCertificateSql.selectYaNumThree(v);
                        EnterpriseStatsForm enterpriseStatsForm6 = EruptDaoUtils.selectOne(sql6, EnterpriseStatsForm.class);
                        enterpriseStats.setRecordOfPlanNum(enterpriseStatsForm6.getYa());
                    }
                }

                // 标准化达标等级
                String sql2 = enterpriseCertificateSql.selectBzhThree(v);
                EnterpriseStatsForm enterpriseStatsForm2 = EruptDaoUtils.selectOne(sql2, EnterpriseStatsForm.class);
                if (ObjectUtils.isNotEmpty(enterpriseStatsForm2)) {
                    enterpriseStats.setStandardization(enterpriseStatsForm2.getBzh());
                }
                // 安全评价备案
                String sql3 = enterpriseCertificateSql.selectAqThree(v);
                EnterpriseStatsForm enterpriseStatsForm3 = EruptDaoUtils.selectOne(sql3, EnterpriseStatsForm.class);
                if (ObjectUtils.isNotEmpty(enterpriseStatsForm3)) {
                    enterpriseStats.setSafetyEvaluationRecord(enterpriseStatsForm3.getAq());
                }
                // 港口设施保安
                String sql4 = enterpriseCertificateSql.selectGkThree(v);
                EnterpriseStatsForm enterpriseStatsForm4 = EruptDaoUtils.selectOne(sql4, EnterpriseStatsForm.class);
                if (ObjectUtils.isNotEmpty(enterpriseStatsForm4)) {
                    enterpriseStats.setPortFacilitySecurity(enterpriseStatsForm4.getGk());
                }

                Map<String, Object> map = JSONObject.parseObject(JSON.toJSONString(enterpriseStats), Map.class);
                if (!map.get("company").equals("")) {
                    collection.add(map);
                }

            }
        });

        Page page = EruptSpringUtil.getBean(Page.class);
        page.setList(collection);
        page.setPageIndex(1);
        page.setPageSize(999);
        page.setSort("");
        return page;


    }

}
