package com.daliangang.emergency.entity;

import com.daliangang.emergency.proxy.EmergencyStatisticsDataProxy;
import io.swagger.annotations.ApiModel;
import lombok.Getter;
import lombok.Setter;
import xyz.erupt.annotation.Erupt;
import xyz.erupt.annotation.EruptField;
import xyz.erupt.annotation.sub_erupt.Power;
import xyz.erupt.annotation.sub_field.Edit;
import xyz.erupt.annotation.sub_field.EditType;
import xyz.erupt.annotation.sub_field.View;
import xyz.erupt.annotation.sub_field.sub_edit.ChoiceType;
import xyz.erupt.annotation.sub_field.sub_edit.NumberType;
import xyz.erupt.annotation.sub_field.sub_edit.Search;
import xyz.erupt.jpa.model.MetaModel;
import xyz.erupt.toolkit.db.Comment;
import xyz.erupt.toolkit.handler.RemoteEntityChoiceFetchHandler;

import javax.persistence.Entity;
import javax.persistence.Table;

/**
 * <AUTHOR>
 * @date 2023/11/3
 */
@Erupt(
        name = "应急资源统计",
        orderBy = "sumTotal desc",
        power = @Power(
                add = false,
                edit = false,
                delete = false,
                viewDetails = false
        ),
        dataProxy = EmergencyStatisticsDataProxy.class
)
@Table(name = "tb_emergency_statistics")
@Entity
@Getter
@Setter
@Comment("应急资源统计")
@ApiModel("应急资源统计")
public class EmergencyStatistics extends MetaModel {

    //企业名称
    @EruptField(
            views = @View(
                    title = "企业名称",width = "500px"
            ),
            edit = @Edit(
                    title = "企业名称",
                    type = EditType.CHOICE,
                    search = @Search,
                    choiceType = @ChoiceType(fullSpan = true,reload = true,fetchHandler = RemoteEntityChoiceFetchHandler.class, fetchHandlerParams = {"main", "Enterprise", "org_code,name"})))
    private String company;

    //总数
    @EruptField(
            views = @View(title = "总数",sortable = true),
            edit = @Edit(
                    title = "总数",
                    type=EditType.NUMBER,
                    numberType = @NumberType(min = 0)
            )
    )
    private Integer sumTotal;

    //应急救援队伍
    @EruptField(
            views = @View(title = "应急救援队伍"),
            edit = @Edit(
                    title = "应急救援队伍",
                    type=EditType.NUMBER,
                    numberType = @NumberType(min = 0)
            )
    )
    private Integer rescueteamCount;

    //应急集合点
    @EruptField(
            views = @View(title = "应急集合点"),
            edit = @Edit(
                    title = "应急集合点",
                    type=EditType.NUMBER,
                    numberType = @NumberType(min = 0)
            )
    )
    private Integer emergencyMusterCount;

    //事故应急池
    @EruptField(
            views = @View(title = "事故应急池"),
            edit = @Edit(
                    title = "事故应急池",
                    type=EditType.NUMBER,
                    numberType = @NumberType(min = 0)
            )
    )
    private Integer emergencyPoolCount;

    //应急物资储备点
    @EruptField(
            views = @View(title = "应急物资储备点"),
            edit = @Edit(
                    title = "应急物资储备点",
                    type=EditType.NUMBER,
                    numberType = @NumberType(min = 0)
            )
    )
    private Integer materialReserveCount;

}
