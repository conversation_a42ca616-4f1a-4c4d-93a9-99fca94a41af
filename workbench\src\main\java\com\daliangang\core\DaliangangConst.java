package com.daliangang.core;

import xyz.erupt.upms.dict.Dict;
import xyz.erupt.upms.dict.DictItem;
import xyz.erupt.upms.dict.Dicts;

@Dicts({

        @Dict(code = "portArea", name = "所属港区", value = {
                @DictItem(code = "BEILIANG_PORT_AREA", value = "北良港区"),
                @DictItem(code = "DAGUSHAN_SOUTH_PORT_DISTRICT", value = "大孤山南港区"),
                @DictItem(code = "LUSHUN_PORT_AREA", value = "旅顺港区"),
                @DictItem(code = "GANJINGZI_PORT_AREA", value = "甘井子港区"),
                @DictItem(code = "DAYAOWAN_PORT_AREA", value = "大窑湾港区"),
                @DictItem(code = "CATFISH_BAY_PORT_AREA", value = "鲶鱼湾港区"),
                @DictItem(code = "CHANGXING_ISLAND_PORT_AREA", value = "长兴岛港区"),
                @DictItem(code = "CHANGHAI_COUNTY", value = "长海县"),
                @DictItem(code = "WAFANGDIAN_PORT_AREA", value = "瓦房店港区")
        }),

        @Dict(code = "type", name = "企业类别", value = {
                @DictItem(code = "OTHER", value = "其他"),
                @DictItem(code = "OIL", value = "油品"),
                @DictItem(code = "CONTAINER", value = "集装箱"),
                @DictItem(code = "ALL", value = "全类型")
        }),

        @Dict(code = "educational", name = "学历", value = {
                @DictItem(code = "PRIMARY_SCHOOL", value = "小学"),
                @DictItem(code = "SENIOR_HIGH_SCHOOL", value = "高中"),
                @DictItem(code = "MASTERS_DEGREE", value = "硕士研究生"),
                @DictItem(code = "UNDERGRADUATE_EDUCATION", value = "本科教育"),
                @DictItem(code = "JUNIOR_HIGH_SCHOOL", value = "初中"),
                @DictItem(code = "JUNIOR_COLLEGE_EDUCATION", value = "专科教育"),
                @DictItem(code = "PHD_STUDENT", value = "博士研究生")
        }),

        @Dict(code = "postTitle", name = "职称", value = {
                @DictItem(code = "NONE", value = "无"),
                @DictItem(code = "TECHNICIAN", value = "技术员"),
                @DictItem(code = "ASSISTANT_ENGINEER", value = "助理工程师"),
                @DictItem(code = "ENGINEER", value = "工程师"),
                @DictItem(code = "SENIOR_ENGINEER", value = "高级工程师"),
                @DictItem(code = "REAL_SENIOR_ENGINEER", value = "正高级工程师")
        }),

        @Dict(code = "expiredState", name = "状态", value = {
                @DictItem(code = "SOON_OVERDUE", value = "即将逾期"),
                @DictItem(code = "EXPIRED", value = "已逾期"),
                @DictItem(code = "NORMAL", value = "正常")
        }),

        @Dict(code = "detectionRange", name = "检测报告检测范围", value = {
                @DictItem(code = "OTHER", value = "其他"),
                @DictItem(code = "BERTH", value = "泊位"),
                @DictItem(code = "TANK_GROUP", value = "罐组")
        }),

        @Dict(code = "workType", name = "作业类型", value = {
                @DictItem(code = "CAR", value = "装卸汽车"),
                @DictItem(code = "TRAIN", value = "装卸火车")
        }),

        @Dict(code = "state", name = "状态", value = {
                @DictItem(code = "NOT_STARTED", value = "未开始"),
                @DictItem(code = "STARTED", value = "已开始"),
                @DictItem(code = "ENDED", value = "已结束")
        }),

        @Dict(code = "riskType", name = "风险类型", value = {
                @DictItem(code = "RISK_OF_POISONING__SUFFOCATION__FIRE_AND_EXPLOSION_IN_LOADING_AN", value = "港口企业液体危险货物装、卸车作业中毒窒息、火灾爆炸风险"),
                @DictItem(code = "RISK_OF_LEAKAGE__POISONING__FIRE_AND_EXPLOSION_IN_DANGEROUS_GOOD", value = "港口企业危险货物堆场仓库泄漏中毒、火灾爆炸风险"),
                @DictItem(code = "RISK_OF_LEAKAGE__POISONING__FIRE_AND_EXPLOSION_IN_LOADING_AND_UN", value = "港口企业危险货物码头装卸作业泄漏中毒、火灾爆炸风险"),
                @DictItem(code = "RISK_OF_LEAKAGE__POISONING__FIRE_AND_EXPLOSION_IN_THE_TANK_FARM_", value = "港口企业危险货物罐区泄漏中毒、火灾爆炸风险"),
                @DictItem(code = "RISK_OF_POISONING__SUFFOCATION__FIRE_AND_EXPLOSION_IN_INSPECTION", value = "港口企业危险货物罐区检维修作业中毒窒息、火灾爆炸风险")
        }),

        @Dict(code = "majorRiskOne", name = "主要致险情景12", value = {
                @DictItem(code = "majorRiskOne1", value = "港口企业危险货物储罐区发生泄漏"),
                @DictItem(code = "majorRiskOne2", value = "港口企业易燃易爆危险货物罐区内违规动火作业、违反操作规程作业"),
                @DictItem(code = "majorRiskOne3", value = "电气故障或避雷装置、防静电装置失效"),
                @DictItem(code = "majorRiskOne4", value = "储罐腐蚀及储罐附属设备损坏,联锁装置、液位监测系统、气体报警装置等失效"),
                @DictItem(code = "majorRiskOne5", value = "未安装紧急切断阀"),
                @DictItem(code = "majorRiskOther", value = "其他")
        }),
        @Dict(code = "majorRiskTwo", name = "主要致险情景13", value = {
                @DictItem(code = "majorRiskTwo1", value = "堆场违规超量、超范围堆存危险货物集装箱"),
                @DictItem(code = "majorRiskTwo2", value = "仓库内禁忌物混存"),
                @DictItem(code = "majorRiskTwo3", value = "堆场箱区设置、堆垛方式、堆码层数及隔离等不符合要求"),
                @DictItem(code = "majorRiskTwo4", value = "硝酸铵类物质的危险货物集装箱违规作业"),
                @DictItem(code = "majorRiskTwo5", value = "毒性气体、液化天然气(LNG)等易燃易爆剧毒集装箱罐柜及其附件损坏"),
                @DictItem(code = "majorRiskTwo6", value = "消防设施及应急能力不足"),
                @DictItem(code = "majorRiskTwo7", value = "恶劣自然环境影响"),
                @DictItem(code = "majorRiskTwo8", value = "违反操作规程作业"),
                @DictItem(code = "majorRiskOther", value = "其他")
        }),

        @Dict(code = "majorRiskThree", name = "主要致险情景14", value = {
                @DictItem(code = "majorRiskThree1", value = "港口企业危险货物码头装卸作业设备故障"),
                @DictItem(code = "majorRiskThree2", value = "码头前沿管道、法兰破损和LNG、液化石油气（LPG）、氨气等易燃易爆剧毒货物泄漏"),
                @DictItem(code = "majorRiskThree3", value = "管道的压力检测或安全泄放装置故障"),
                @DictItem(code = "majorRiskThree4", value = "未按要求加装紧急切断阀或紧急切断阀故障失灵"),
                @DictItem(code = "majorRiskThree5", value = "装卸作业过程中违反操作规程"),
                @DictItem(code = "majorRiskThree6", value = "未设置生产作业及环境监测系统,大风等恶劣天气未及时预警"),
                @DictItem(code = "majorRiskOther", value = "其他")
        }),

        @Dict(code = "majorRiskFour", name = "主要致险情景15", value = {
                @DictItem(code = "majorRiskFour1", value = "罐区动火作业、受限空间作业等特殊作业活动未严格执行企业内部审批制度,未按规定做好隔离、防护和应急措施"),
                @DictItem(code = "majorRiskFour2", value = "未按要求对储罐进行清洗、置换、隔离、通风等"),
                @DictItem(code = "majorRiskFour3", value = "未按要求进行气体检测和分析或检测仪器故障"),
                @DictItem(code = "majorRiskFour4", value = "使用不符合要求(如防爆要求)的工属具"),
                @DictItem(code = "majorRiskFour5", value = "未采取正确的个人防护措施,并按要求落实监护人制度"),
                @DictItem(code = "majorRiskOther", value = "其他")
        }),

        @Dict(code = "majorRiskSix", name = "主要致险情景16", value = {
                @DictItem(code = "majorRiskSix1", value = "装、卸车软管或鹤管有缺陷"),
                @DictItem(code = "majorRiskSix2", value = "罐体未按要求进行检查,存在缺陷，如腐蚀凹坑、裂纹、穿孔等"),
                @DictItem(code = "majorRiskSix3", value = "防火、防爆、防雷、防静电、防泄漏等措施失效"),
                @DictItem(code = "majorRiskSix4", value = "未落实好装卸作业前的各项检查工作"),
                @DictItem(code = "majorRiskSix5", value = "取样时未落实好相关防静电、防火花等措施"),
                @DictItem(code = "majorRiskOther", value = "其他")
        }),

        @Dict(code = "pointsOfPreventionOne", name = "防控要点12", value = {
                @DictItem(code = "pointsOfPreventionOne1", value = "加强储罐监测工作，强化联锁切断装置、高低位液位监测系统、有毒气体可燃气体报警装置等维护保养和巡检"),
                @DictItem(code = "pointsOfPreventionOne2", value = "杜绝罐区内违规动火作业和违反操作规程作业"),
                @DictItem(code = "pointsOfPreventionOne3", value = "加强电气设备和避雷装置、防静电装置的维护保养"),
                @DictItem(code = "pointsOfPreventionOne4", value = "加强持证人员安全教育和业务技能培训"),
                @DictItem(code = "pointsOfPreventionOne5", value = "加强对罐体检维修管理"),
                @DictItem(code = "pointsOfPreventionOne6", value = "强化应急物资储备、应急设施设备配备和应急处置演练，建立区域应急联动机制"),
                @DictItem(code = "pointsOfPreventionOne7", value = "加强从业人员安全意识教育和业务技能培训"),
                @DictItem(code = "pointsOfPreventionOne8", value = "加强日常监督检查"),
                @DictItem(code = "pointsOfPreventionOne9", value = "对涉及重点监管的危险化学品和危险化学品重大危险源的储运设施自动化控制系统装备、重大危险源在线监测监控均实现全覆盖"),
                @DictItem(code = "majorRiskOther", value = "其他")
        }),
        @Dict(code = "pointsOfPreventionTwo", name = "防控要点13", value = {
                @DictItem(code = "pointsOfPreventionTwo1", value = "严格按照相关标准要求堆存易燃易爆剧毒危险货物集装箱,严格按照安全操作规程作业"),
                @DictItem(code = "pointsOfPreventionTwo2", value = "装有《危险货物分类和品名编号》(GB6944)列出的1.1项、1.2项爆炸品和硝酸铵类物质的危险货物集装箱严格实行直装直取,严禁在港区内存放"),
                @DictItem(code = "pointsOfPreventionTwo3", value = "禁忌物严禁混存"),
                @DictItem(code = "pointsOfPreventionTwo4", value = "集装箱堆码的垛型应与机械能力、集装箱类型、箱内货物的特性以及箱区设计要求相适应"),
                @DictItem(code = "pointsOfPreventionTwo5", value = "加强硝酸铵类危险货物集装箱和毒性气体、LNG等易燃易爆剧毒集裝箱罐柜的港口作业以及相关报警装置的维护保养和巡检"),
                @DictItem(code = "pointsOfPreventionTwo6", value = "推进智能主动安防系统建设"),
                @DictItem(code = "pointsOfPreventionTwo7", value = "加强日常监督检查、抽查力度"),
                @DictItem(code = "pointsOfPreventionTwo8", value = "强化应急物资储备、应急设施设备配备和应急处置演练，建立区域应急救援联动机制"),
                @DictItem(code = "pointsOfPreventionTwo9", value = "集装箱堆码应做好恶劣自然环境防范,如防风栓固"),
                @DictItem(code = "pointsOfPreventionTwo10", value = "加强持证人员安全意识教育和业务技能培训"),
                @DictItem(code = "pointsOfPreventionTwo11", value = "对涉及重点监管的危险化学品和危险化学品重大危险源的储运设施自动化控制系统装备、重大危险源在线监测监控均实现全覆盖"),
                @DictItem(code = "majorRiskOther", value = "其他")
        }),
        @Dict(code = "pointsOfPreventionThree", name = "防控要点14", value = {
                @DictItem(code = "pointsOfPreventionThree1", value = "加强装卸设备设施、管道、法兰和紧急切断阀等维护保养、检验检测和巡检"),
                @DictItem(code = "pointsOfPreventionThree2", value = "定期对管道的压，力检测和安全泄放装置进行检验,并加强日常检查"),
                @DictItem(code = "pointsOfPreventionThree3", value = "严格落实装卸作业前船岸安全检查制度,严格装卸作业现场安全管理,杜绝违章操作,强化装卸过程中,船岸界面的安全巡检工作"),
                @DictItem(code = "pointsOfPreventionThree4", value = "加强装卸作业人员的安全意识教育和实操技能培训"),
                @DictItem(code = "pointsOfPreventionThree5", value = "强化应急物资储备、应急设施设备配备和应急处置演练"),
                @DictItem(code = "pointsOfPreventionThree6", value = "按要求设置生产作业及环境监测系统,恶劣天气前停止作业"),
                @DictItem(code = "majorRiskOther", value = "其他")
        }),
        @Dict(code = "pointsOfPreventionFour", name = "防控要点15", value = {
                @DictItem(code = "pointsOfPreventionFour1", value = "对涉及重点监管的危险化学品和危险化学品重大危险源的储运设施自动化控制系统装备、重大危险源在线监测监控均实现全覆盖"),
                @DictItem(code = "pointsOfPreventionFour2", value = "罐区动火作业、受限空间作业等特殊作业活动严格执行企业内部管理制度,并做好隔离、防护和应急措施"),
                @DictItem(code = "pointsOfPreventionFour3", value = "严格按要求进行气体检测和分析,气体检测的仪表要定期检验,同时做好日常维护"),
                @DictItem(code = "pointsOfPreventionFour4", value = "储罐检修时使用的工属具应满足相关要求,穿戴好符合要求的劳动防护用品"),
                @DictItem(code = "pointsOfPreventionFour5", value = "落实好监护人制度,无人监护不得进入受限空间作业"),
                @DictItem(code = "pointsOfPreventionFour6", value = "加强危险性作业劳务外包管理,严格审核承包商的资质条件,做好作业交底和现场监护"),
                @DictItem(code = "majorRiskOther", value = "其他")
        }),
        @Dict(code = "pointsOfPreventionFive", name = "防控要点16", value = {
                @DictItem(code = "pointsOfPreventionFive1", value = "作业软管应定置管理,并定期检测与维护保养"),
                @DictItem(code = "pointsOfPreventionFive2", value = "危险货物罐式车辆按要求进行定期检验,并做好日常检查"),
                @DictItem(code = "pointsOfPreventionFive3", value = "定期对装、卸车台的防火、防爆、防雷、防静电、防泄漏等装置进行检验和检查"),
                @DictItem(code = "pointsOfPreventionFive4", value = "装卸作业开始前严格按要求落实各项检查工作"),
                @DictItem(code = "pointsOfPreventionFive5", value = "取样作业应严格执行相关作业规程,严格落实相关的防静电、防火花等安全措施"),
                @DictItem(code = "pointsOfPreventionFive6", value = "危险货物道路运输企业依据相关规定向港口企业提供托运人制作的危险货物托运清单信息"),
                @DictItem(code = "majorRiskOther", value = "其他")
        }),

        @Dict(code = "checkScope", name = "检查范围", value = {
                @DictItem(code = "OTHER", value = "其他"),
                @DictItem(code = "OIL", value = "油品"),
                @DictItem(code = "CONTAINER", value = "集装箱"),
                @DictItem(code = "ALL", value = "全类型")
        }),

        @Dict(code = "stateBy", name = "状态", value = {
                @DictItem(code = "DISABLED", value = "已禁用"),
                @DictItem(code = "NOT_ENABLED", value = "未启用"),
                @DictItem(code = "ENABLED", value = "已启用")
        }),

        @Dict(code = "auditState", name = "状态", value = {
                @DictItem(code = "NOT_AUDITED", value = "未审核"),
                @DictItem(code = "NOT_PASSED", value = "未通过"),
                @DictItem(code = "PASSED", value = "已通过")
        }),

        @Dict(code = "audit", name = "审核结果", value = {
                @DictItem(code = "BY", value = "通过"),
                @DictItem(code = "REJECTED", value = "驳回")
        }),

        @Dict(code = "enterpriseType", name = "适用企业类型", value = {
                @DictItem(code = "PASSENGERROLLED_DANGEROUS_GOODS", value = "客滚危险货物"),
                @DictItem(code = "OTHER", value = "其他"),
                @DictItem(code = "OIL_AND_GAS_CHEMICAL_WHARF_AND_TANK_FARM", value = "油气化工码头和罐区"),
                @DictItem(code = "CONTAINER", value = "集装箱"),
                @DictItem(code = "FULL_TYPE", value = "全类型"),
                @DictItem(code = "SOLID_BULK_DANGEROUS_GOODS", value = "固体散装危险货物")
        }),


        @Dict(code = "result", name = "整改结果", value = {
                @DictItem(code = "TO_BE_RECTIFIED", value = "待整改"),
                @DictItem(code = "Rectified", value = "已整改"),
                @DictItem(code = "Audited", value = "已审核"),
                @DictItem(code = "NOT_Audited", value = "待审核"),
                @DictItem(code = "PASS", value = "通过"),
                @DictItem(code = "NOT_PASS", value = "不通过")
        }),

        @Dict(code = "accidentType", name = "事故类型", value = {
                @DictItem(code = "VEHICLE_INJURY", value = "车辆伤害"),
                @DictItem(code = "DAMAGE_TO_EQUIPMENT_AND_FACILITIES", value = "设备设施损坏"),
                @DictItem(code = "DROWNING", value = "淹溺"),
                @DictItem(code = "OTHER_EXPLOSIONS", value = "其他爆炸"),
                @DictItem(code = "OTHER_INJURIES", value = "其他伤害"),
                @DictItem(code = "FALLING_FROM_HEIGHT", value = "高处坠落"),
                @DictItem(code = "CONTAINER_EXPLOSION", value = "容器爆炸"),
                @DictItem(code = "MECHANICAL_INJURY", value = "机械伤害"),
                @DictItem(code = "LEAKAGE", value = "泄漏"),
                @DictItem(code = "OBJECT_STRIKE", value = "物体打击"),
                @DictItem(code = "COLLAPSE", value = "坍塌"),
                @DictItem(code = "ELECTRIC_SHOCK", value = "触电"),
                @DictItem(code = "FIRE", value = "火灾"),
                @DictItem(code = "BURNING", value = "灼烫"),
                @DictItem(code = "PRESSURE_RISE", value = "涨压"),
                @DictItem(code = "OIL_SPILL", value = "溢油"),
                @DictItem(code = "LIFTING_INJURY", value = "起重伤害"),
                @DictItem(code = "POISONING_AND_SUFFOCATION", value = "中毒和窒息")
        }),

        @Dict(code = "publicSafety", name = "公众安全", value = {
                @DictItem(code = "PROTECTIVE_EQUIPMENT", value = "防护用品"),
                @DictItem(code = "GENERAL", value = "通用"),
                @DictItem(code = "EVACUATION", value = "疏散")
        }),

        @Dict(code = "emergency", name = "应急响应", value = {
                @DictItem(code = "FIRE_OF_TRUCK_OR_TRAILER_CARGO", value = "卡车或拖车货物着火"),
                @DictItem(code = "A_LOT_OF_LEAKAGE", value = "大量泄漏"),
                @DictItem(code = "FIRST_AID", value = "急救"),
                @DictItem(code = "SPILL_OR_LEAK", value = "溢出或泄漏"),
                @DictItem(code = "GENERAL", value = "通用"),
                @DictItem(code = "SLIGHT_FIRE", value = "轻微火灾"),
                @DictItem(code = "TANK", value = "槽罐"),
                @DictItem(code = "MAJOR_FIRE", value = "重大火灾")
        }),

        @Dict(code = "company", name = "企业名字", value = {
                @DictItem(code = "ENTERPRISE", value = "Enterprise"),
                @DictItem(code = "ADMINISTRATOR_NAME", value = "administrator,name"),
                @DictItem(code = "REMOTE.ENTITY=MAIN", value = "remote.entity=main")
        }),

        @Dict(code = "preplanType", name = "预案类型", value = {
                @DictItem(code = "SITE_DISPOSAL_SCHEME", value = "现场处置方案"),
                @DictItem(code = "COMPREHENSIVE_PLAN", value = "综合预案"),
                @DictItem(code = "SPECIAL_PLAN", value = "专项预案")
        }),

        @Dict(code = "dangerType", name = "重大危险源等级", value = {
                @DictItem(code = "LEVEL_1", value = "一级"),
                @DictItem(code = "LEVEL_II", value = "二级"),
                @DictItem(code = "LEVEL_3", value = "三级"),
                @DictItem(code = "LEVEL_4", value = "四级")
        }),

        @Dict(code = "grade", name = "安全标准化等级", value = {
                @DictItem(code = "LEVEL_1", value = "一级"),
                @DictItem(code = "LEVEL_II", value = "二级")
        }),

        @Dict(code = "workTypeShip", name = "作业船", value = {
                @DictItem(code = "ship", value = "装卸船")
        }),

        @Dict(code = "fiveRiskType", name = "风险类型（五清单）", value = {
                @DictItem(code = "12", value = "12"),
                @DictItem(code = "13", value = "13"),
                @DictItem(code = "14", value = "14"),
                @DictItem(code = "15", value = "15"),
                @DictItem(code = "16", value = "16")
        }),

        @Dict(code = "Casualties", name = "人员伤亡", value = {
                @DictItem(code = "casualties1", value = "3人以下死亡，或者10人以下重伤"),
                @DictItem(code = "casualties2", value = "3人以上10人以下死亡，或者10人以上50人以下重伤"),
                @DictItem(code = "casualties3", value = "10人以上30人以下死亡，或者50人以上100人以下重伤"),
                @DictItem(code = "casualties4", value = "30人以上死亡，或者100人以上重伤")
        }),

        @Dict(code = "PropertyLoss", name = "财产损失", value = {
                @DictItem(code = "propertyLoss1", value = "1000万元以下直接经济损失"),
                @DictItem(code = "propertyLoss2", value = "1000万元以上5000万元以下直接经济损失"),
                @DictItem(code = "propertyLoss3", value = "5000万元以上1亿元以下直接经济损失"),
                @DictItem(code = "propertyLoss4", value = "1亿元以上直接经济损失")
        }),

        @Dict(code = "EnvironmentalImpact", name = "环境影响", value = {
                @DictItem(code = "environmentalImpact1", value = "可能造成一般生态环境灾害或公共卫生事件"),
                @DictItem(code = "environmentalImpact2", value = "可能造成较大生态环境灾害或公共卫生事件"),
                @DictItem(code = "environmentalImpact3", value = "可能造成重大生态环境灾害或公共卫生事件"),
                @DictItem(code = "environmentalImpact4", value = "可能造成特别重大生态环境灾害或公共卫生事件"),
                @DictItem(code = "environmentalImpact5", value = "无")
        }),

        @Dict(code = "SocialInfluence", name = "社会影响", value = {
                @DictItem(code = "socialInfluence1", value = "可能对国家或区域的社会、经济、外交、军事、政治等产生较小影响"),
                @DictItem(code = "socialInfluence2", value = "可能对国家或区域的社会、经济、外交、军事、政治等产生较大影响"),
                @DictItem(code = "socialInfluence3", value = "可能对国家或区域的社会、经济、外交、军事、政治等产生重大影响"),
                @DictItem(code = "socialInfluence4", value = "可能对国家或区域的社会、经济、外交、军事、政治等产生特别重大影响"),
                @DictItem(code = "socialInfluence5", value = "无")
        }),

        @Dict(code = "ResponsibleType", name = "责任人类型", value = {
                @DictItem(code = "responsibleType1", value = "单位责任人"),
                @DictItem(code = "responsibleType2", value = "安全部门责任人"),
                @DictItem(code = "responsibleType3", value = "业务部门责任人"),
                @DictItem(code = "responsibleType4", value = "班组（基层）责任人")
        }),

        @Dict(code = "MaintenanceType", name = "检维修作业类型", value = {
                @DictItem(code = "DH", value = "动火作业"),
                @DictItem(code = "SX", value = "受限空间作业")
        }),

        @Dict(code = "drillType", name = "演练分类", value = {
                @DictItem(code = "DRILL_DESKTOP", value = "桌面演练"),
                @DictItem(code = "DRILL_PRACTICE", value = "实战演练"),
                @DictItem(code = "DRILL_SINGLE", value = "单项演练"),
                @DictItem(code = "DRILL_MULTI", value = "综合演练")
        }),

        @Dict(code = "railwayLevel", name = "铁路等级", value = {
                @DictItem(code = "RAILWAY_LV_I", value = "Ⅰ级"),
                @DictItem(code = "RAILWAY_LV_II", value = "ⅠI级"),
                @DictItem(code = "RAILWAY_LV_III", value = "ⅠII级"),
                @DictItem(code = "RAILWAY_LV_IV", value = "ⅠV级")
        }),

        @Dict(code = "technicalStatus", name = "技术状态等级", value = {
                @DictItem(code = "ClassI", value = "一类（好）"),
                @DictItem(code = "ClassII", value = "二类（较好）"),
                @DictItem(code = "ClassIII", value = "三类（较差）"),
                @DictItem(code = "ClassIV", value = "四类（差）"),
                @DictItem(code = "ClassV", value = "五类（危险）")
        }),

        @Dict(code = "detectionRangeDock", name = "检测报告检测范围码头", value = {
                @DictItem(code = "dock", value = "码头")
        }),

        @Dict(code = "certificateType", name = "证书类型", value = {
                @DictItem(code = "waterwayTransportation", value = "水路运输从业资格证"),
                @DictItem(code = "specialEquipment", value = "特种设备操作证书"),
                @DictItem(code = "specialOperations", value = "特种作业操作证书"),
                @DictItem(code = "otherCertificates", value = "其他证书")
        }),

        @Dict(code = "addressType", name = "作业地点类型", value = {
                @DictItem(code = "1", value = "泊位"),
                @DictItem(code = "2", value = "储罐"),
                @DictItem(code = "3", value = "堆场"),
                @DictItem(code = "4", value = "仓库"),
                @DictItem(code = "5", value = "栈台")
        }),


        @Dict(code = "materialType", name = "物资类型", value = {
                //抢险救援车|侦检器材|警戒器材|灭火器材|通信器材|救生物资|破拆器材|堵漏器材|输转物资|洗消物资|排烟照明器材|其他物资
                @DictItem(code = "EMERGENCY_RESCUE_VEHICLE", value = "抢险救援车"),
                @DictItem(code = "DETECTIVE_EQUIPMENT", value = "侦检器材"),
                @DictItem(code = "CAUTION_EQUIPMENT", value = "警戒器材"),
                @DictItem(code = "FIRE_FIGHTING_EQUIPMENT", value = "灭火器材"),
                @DictItem(code = "COMMUNICATION_EQUIPMENT", value = "通信器材"),
                @DictItem(code = "LIFESAVING_SUPPLIES", value = "救生物资"),
                @DictItem(code = "BREAKING_EQUIPMENT", value = "破拆器材"),
                @DictItem(code = "LEAKAGE_PLUGGING_EQUIPMENT", value = "堵漏器材"),
                @DictItem(code = "TRANSSHIPMENT_OF_MATERIALS", value = "输转物资"),
                @DictItem(code = "DECONTAMINATION_MATERIALS", value = "洗消物资"),
                @DictItem(code = "SMOKE_EXHAUST_LIGHTING_EQUIPMENT", value = "排烟照明器材"),
                @DictItem(code = "OTHER_MATERIALS", value = "其他物资")
        }),

        @Dict(code = "yardNow", name = "当前储存量", value = {
                @DictItem(code = "EMPTY_CAN", value = "空罐"),
                @DictItem(code = "LOW_LIQUID_LEVLE", value = "低液位"),
                @DictItem(code = "NORMAL_LIQUID_LEVLE", value = "正常液位"),
                @DictItem(code = "HIGH_LIQUID_LEVLE", value = "高液位")
        }),

        @Dict(code = "detectionResult", name = "检测结果", value = {
                @DictItem(code = "CONTINUE_TO_USE", value = "继续使用"),
                @DictItem(code = "MONITOR_USE", value = "监控使用"),
                @DictItem(code = "STOP_USE", value = "停止使用")

        }),

        @Dict(code = "roleType", name = "角色类型", value = {
                @DictItem(code = "ENTERPRISE", value = "企业"),
                @DictItem(code = "GOVERNMENT", value = "市级"),
                @DictItem(code = "COUNTY", value = "区县")
        }),
        @Dict(code = "teamLevel", name = "队伍级别", value = {
                @DictItem(code = "teamLevel1", value = "国家级"),
                @DictItem(code = "teamLevel2", value = "省级"),
                @DictItem(code = "teamLevel3", value = "市级"),
                @DictItem(code = "teamLevel4", value = "区（县）级"),
                @DictItem(code = "teamLevel5", value = "企业级"),
                @DictItem(code = "teamLevel6", value = "其他")

        }),
        @Dict(code = "ofProgram", name = "所属栏目", value = {
                @DictItem(code = "news", value = "新闻展示"),
                @DictItem(code = "notice", value = "通知中心"),
                @DictItem(code = "four", value = "四项机制"),
                @DictItem(code = "trust", value = "安全承诺公告"),
                @DictItem(code = "chufa", value = "处罚信息"),
                @DictItem(code = "shigu", value = "事故信息")
        }),

        @Dict(code = "componentType", name = "组件类型", value = {
                @DictItem(code = "rider_news_card", value = "新闻"),
                @DictItem(code = "rider_book_card", value ="通讯录"),
                @DictItem(code = "rider_active_card", value = "活动"),
                @DictItem(code = "rider_imgs_card", value = "图集"),
                @DictItem(code = "rider_mine_card", value = "我的"),
        }),

        @Dict(code = "checkStatus", name = "检测状态", value = {
                @DictItem(code = "NORMAL", value = "正常"),
                @DictItem(code = "SOON_OVERDUE", value = "即将逾期"),
                @DictItem(code = "EXPIRED", value = "已过期")

        }),

        @Dict(code = "rectificationStatus", name = "整改状态", value = {
                @DictItem(code = "TO_BE_RECTIFIED", value = "待整改"),
                @DictItem(code = "Rectified", value = "已整改")
        }),

        @Dict(code = "rectificationAuditStatus", name = "整改审核状态", value = {
                @DictItem(code = "Audited", value = "已审核"),
                @DictItem(code = "NOT_Audited", value = "待审核")
        }),

        @Dict(code = "rectificationReviewResults", name = "整改复查结果", value = {
                @DictItem(code = "PASS", value = "通过"),
                @DictItem(code = "NOT_PASS", value = "不通过")
        }),
        @Dict(
                code = "inspectType", name = "检查类型", value = {
                @DictItem(code = "SuperiorInspection", value = "上级检查"),
                @DictItem(code = "WhistleblowerChecks", value = "吹哨人检查"),
                @DictItem(code = "SecurityDepartmentInspection", value = "安全部门检查"),
                @DictItem(code = "OtherInspection", value = "其他检查")
                }
        )

})
public class DaliangangConst {
}
