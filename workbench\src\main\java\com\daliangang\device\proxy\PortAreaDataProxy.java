/*
 * Copyright © 2020-2035 yundingyun All rights reserved.
 */
package com.daliangang.device.proxy;

import cn.hutool.json.JSONObject;
import com.daliangang.device.entity.PortArea;
import org.springframework.stereotype.Service;
import xyz.erupt.annotation.fun.DataProxy;
import xyz.erupt.common.http.EruptRemoteUtils;
import xyz.erupt.toolkit.remote.RemoteProxyService;

import javax.annotation.Resource;
import java.util.Map;

@Service
public class PortAreaDataProxy implements DataProxy<PortArea> {

    @Resource
    private RemoteProxyService remoteProxyService;
    @Override
    public void excelImport(Object workbook) {
//        EruptDaoUtils.truncate(PortArea.class);
    }


    @Override
    public void afterAdd(PortArea portArea) {
        JSONObject inputData = new JSONObject();
        inputData.set("clazz", "PortArea");
        inputData.set("insertData",portArea);
        EruptRemoteUtils.doPost(remoteProxyService.getMainServerUrl() + "/erupt-api/exchange/data/exchange", inputData, Map.class);
    }

    @Override
    public void afterUpdate(PortArea portArea) {
        JSONObject inputData = new JSONObject();
        inputData.set("clazz", "PortArea");
        inputData.set("insertData",portArea);
        EruptRemoteUtils.doPost(remoteProxyService.getMainServerUrl() + "/erupt-api/exchange/data/exchange", inputData, Map.class);
    }
}
