package com.daliangang.workbench.operation;

import com.daliangang.workbench.entity.Department;
import lombok.Data;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import xyz.erupt.annotation.Erupt;
import xyz.erupt.annotation.EruptField;
import xyz.erupt.annotation.fun.OperationHandler;
import xyz.erupt.annotation.sub_field.Edit;
import xyz.erupt.annotation.sub_field.EditType;
import xyz.erupt.annotation.sub_field.sub_edit.ChoiceType;
import xyz.erupt.annotation.sub_field.sub_edit.InputType;
import xyz.erupt.jpa.model.BaseModel;
import xyz.erupt.toolkit.handler.SqlChoiceFetchHandler;
import xyz.erupt.upms.model.EruptUser;
import xyz.erupt.upms.model.EruptUserDataProxy;

import java.util.List;

public class DepartmentCreateUserHandler implements OperationHandler<Department, DepartmentCreateUserHandler.DepartmentUser> {

    private EruptUserDataProxy eruptUserDataProxy;

    @Override
    public String exec(List<Department> data, DepartmentUser form, String[] param) {
        EruptUser eruptUser = new EruptUser();
        eruptUser.setAccount(form.getAccount());
        eruptUser.setName(form.getName());
//        eruptUser.setEruptOrg(eruptRoleTemplateUser.getEruptOrg());
        eruptUser.setIsAdmin(false);
        eruptUser.setIsMd5(true);
        eruptUser.setPassword(form.getPwd());
        eruptUser.setPasswordA(form.getPwd());
        eruptUser.setPasswordB(form.getPwd());
        eruptUserDataProxy.beforeAdd(eruptUser);

        return null;
    }

    @RestController
    public static class DepartmentUserController {
        @RequestMapping("/erupt-api/data/DepartmentUser/{departmentId}")
        public DepartmentUser initValue(@PathVariable("departmentId") Long departmentId) {
            DepartmentUser user = new DepartmentUser();
            user.setDepartmentId(departmentId);
            return user;
        }
    }

    @Erupt(name = "主管部门用户")
    @Data
    public static class DepartmentUser extends BaseModel {

        @EruptField(edit = @Edit(title = "所属组织", notNull = true, type = EditType.CHOICE, choiceType = @ChoiceType(fetchHandler = SqlChoiceFetchHandler.class, fetchHandlerParams = "select id,name from tb_department")))
        private Long departmentId;

        @EruptField(edit = @Edit(title = "姓名", notNull = true))
        private String name;

        @EruptField(edit = @Edit(title = "登录名", notNull = true))
        private String account;

        @EruptField(edit = @Edit(title = "密码", notNull = true, type = EditType.INPUT, inputType = @InputType(type = "password")))
        private String pwd;

        @EruptField(edit = @Edit(title = "确认密码", notNull = true, type = EditType.INPUT, inputType = @InputType(type = "password")))
        private String pwdConfirm;
    }
}
