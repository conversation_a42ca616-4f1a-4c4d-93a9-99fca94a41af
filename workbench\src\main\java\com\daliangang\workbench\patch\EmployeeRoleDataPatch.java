package com.daliangang.workbench.patch;

import com.daliangang.workbench.entity.EmployeeInformation;
import com.daliangang.workbench.proxy.EmployeeInformationDataProxy;
import com.daliangang.workbench.proxy.InitMenuProxy;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import xyz.erupt.core.config.GsonFactory;
import xyz.erupt.devtools.patch.PlatformPatchHandler;
import xyz.erupt.jpa.dao.EruptDao;
import xyz.erupt.toolkit.db.EruptResultMap;
import xyz.erupt.toolkit.utils.EruptDaoUtils;
import xyz.erupt.upms.model.EruptMenu;
import xyz.erupt.upms.model.EruptRole;
import xyz.erupt.upms.model.EruptUser;
import xyz.erupt.upms.model.EruptUserByRoleView;
import xyz.erupt.upms.model.template.EruptRoleTemplate;
import xyz.erupt.upms.model.template.EruptRoleTemplateUser;
import xyz.erupt.upms.model.template.EruptRoleTemplateUserProxy;
import xyz.erupt.upms.service.EruptUserService;

import javax.annotation.Resource;
import javax.transaction.Transactional;
import java.util.Arrays;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

@Service
@Slf4j
public class EmployeeRoleDataPatch implements PlatformPatchHandler {

    @Override
    public String getPatchId() {
        return "1.11.7.3-patch-02";
    }

    @Override
    public String getName() {
        return "员工角色权限数据修复补丁";
    }

    @Override
    public String getDesc() {
        return "1.修复新建员工错误赋予全局员工角色的问题<br>" + "2.拔除错误的全局角色<br>" + "3.合并全局共享角色权限至隔离角色内<br>";
    }

    @Resource
    private EruptDao eruptDao;

    @Resource
    private EruptRoleTemplateUserProxy eruptRoleTemplateUserProxy;

    @Resource
    private EmployeeInformationDataProxy employeeInformationDataProxy;

    @Resource
    private EruptUserService eruptUserService;

    @Resource
    private InitMenuProxy initMenuProxy;

    @Override
    @Transactional
    public boolean execute() {
        //所有员工数据缓存重置一下
        this.clearEruptUsers();

        //修复错误数据
        this.fixErrorEmployeeRoles();
        this.clearEruptUsers();


        return true;
    }

    private void clearEruptUsers() {
        //所有员工数据缓存重置一下
        List<EruptUser> eruptUsers = eruptDao.queryEntityList(EruptUser.class);
        eruptUsers.forEach(eruptUser -> {
            eruptUserService.flushEruptUserCache(eruptUser);
        });
    }

    @Transactional
    public void fixErrorEmployeeRoles() {
        String userRoleSql = "select * from e_upms_user_role where role_id=4;";
        List<EruptResultMap> errorUsers = EruptDaoUtils.selectOnes(userRoleSql, EruptResultMap.class);
        EruptRole employeeGlobalRole = eruptDao.queryEntity(EruptRole.class, "id=4");

        //回收错误角色
        errorUsers.forEach(vo -> {
            long userId = vo.getLong("user_id");
            EruptUserByRoleView userView = eruptDao.queryEntity(EruptUserByRoleView.class, "id=" + userId);
            if (userView != null && employeeGlobalRole.getUsers().contains(userView)) {
                employeeGlobalRole.getUsers().remove(userView);
                log.info("回收错误角色 -> " + GsonFactory.getGson().toJson(userView));
            }
        });
        eruptDao.merge(employeeGlobalRole);

        //查询所有员工
        List<EmployeeInformation> employees = eruptDao.queryEntityList(EmployeeInformation.class);
        employees.forEach(employee -> {
            String code = "104." + employee.getPhone();//隔离角色的code
            EruptRole isolateRole = eruptDao.queryEntity(EruptRole.class, "code='" + code + "'");
            if (isolateRole == null) {//如果没有隔离角色，帮他创建一个
                EruptRole newRole = new EruptRole();
                newRole.setName(employee.getPhone() + "[" + employeeGlobalRole.getName() + "]");
                newRole.setCode(code);
                newRole.setReserved(false);
                newRole.setShare(false);//独立权限的角色为非公共角色

                //添加菜单
                employeeGlobalRole.getMenus().forEach(menu -> newRole.getMenus().add(menu));

                //查询组织
                EruptRoleTemplateUser eruptRoleTemplateUser = eruptDao.queryEntity(EruptRoleTemplateUser.class, "account='" + employee.getPhone() + "'");
                newRole.setDepts(eruptRoleTemplateUserProxy.getRoleOrgList(eruptRoleTemplateUser));

                eruptDao.persist(newRole);
                log.info("创建员工隔离角色 -> " + employee.getPhone() + " - " + employee.getName());
                isolateRole = newRole;
            }

            if (isolateRole != null) {
                //加入用户
                EruptUser eruptUser = eruptDao.queryEntity(EruptUser.class, "account='" + employee.getPhone() + "'");
                if (!eruptUser.getRoles().contains(isolateRole)) {
                    eruptUser.getRoles().add(isolateRole);
                    eruptDao.merge(eruptUser);
                    eruptUserService.flushEruptUserCache(eruptUser);
                }
            }
        });

        //查询所有已绑定共享自定义角色的数据，拔除相关记录
        String entShareRoleSql = "select * from e_upms_user_role where \n" + "role_id in(select id from e_upms_role where code in(select code from tb_employee_role) )\n" + "or role_id in(select id from e_upms_role where reserved=1 and name in('企业管理员','区县管理员','执法检查人员','安全管理人员','安全监管人员'))";
        List<EruptResultMap> entShareRoles = EruptDaoUtils.selectOnes(entShareRoleSql, EruptResultMap.class);
        entShareRoles.forEach(vo -> {
            long roleId = vo.getLong("role_id");
            long userId = vo.getLong("user_id");
            //找到对应用户
            EruptUser eruptUser = eruptUserService.getEruptUser(userId);
            if (eruptUser != null) {
                EruptRole role = eruptDao.queryEntity(EruptRole.class, "id=" + roleId);
                if (eruptUser.getRoles().contains(role)) {
                    eruptUser.getRoles().remove(role);
                    eruptDao.merge(eruptUser);
                    log.info(eruptUser.getName() + "(" + eruptUser.getAccount() + ") 去除了公共角色 " + role.getName() + "(" + role.getCode() + ")");
                }
                eruptUserService.flushEruptUserCache(eruptUser);
            }
        });

        //查询所有设置了共享角色的员工记录
        String entShareEmployeeSql = "select * from tb_employee_information where owner_role!='员工'";
        employees = EruptDaoUtils.selectOnes(entShareEmployeeSql, EmployeeInformation.class);
        employees.forEach(employee -> {
            EruptUser eruptUser = eruptUserService.findEruptUserByAccount(employee.getPhone());
            EruptRole employeeRole = employeeInformationDataProxy.getIsolateRoleFromEmployee(employee, eruptUser, true);
            Set<String> roles = Arrays.stream(employee.getOwnerRole().split("\\|")).collect(Collectors.toSet());
            //当前所有菜单code
            Set<String> employeeCodes = employeeRole.getMenus().stream().collect(Collectors.toMap(EruptMenu::getCode, menu -> menu)).keySet();
            roles.forEach(roleName -> {
                if (!roleName.equals("员工")) {
                    EruptRole combineRole = employeeInformationDataProxy.getShareRoleBySelectedRoleName(eruptUser, roleName);
                    StringBuffer sb = new StringBuffer();
                    combineRole.getMenus().forEach(menu -> {
                        if (!employeeCodes.contains(menu.getCode())) {
                            employeeRole.getMenus().add(menu);
                            sb.append(/*"\n" +*/ menu.getCode() + "[" + menu.getName() + "]");
                        }
                    });
                    log.info(employee.getName() + " 新增了菜单 -> " + sb.toString());
                }
            });
            eruptDao.merge(employeeRole);
        });
    }
}
