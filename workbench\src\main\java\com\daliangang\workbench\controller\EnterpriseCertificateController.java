package com.daliangang.workbench.controller;

import com.daliangang.workbench.entity.Enterprise;
import com.daliangang.workbench.entity.EnterpriseCertificate;
import com.daliangang.workbench.form.EmployeeCertificateForm;
import com.daliangang.workbench.form.EnterpriseForm;
import com.daliangang.workbench.form.MapFroms;
import com.daliangang.workbench.sql.EnterpriseCertificateSql;
import com.google.gson.Gson;
import com.google.gson.internal.LinkedTreeMap;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.web.bind.annotation.*;
import xyz.erupt.core.annotation.EruptRouter;
import xyz.erupt.core.config.GsonFactory;
import xyz.erupt.core.context.MetaContext;
import xyz.erupt.core.module.MetaUserinfo;
import xyz.erupt.core.util.EruptSpringUtil;
import xyz.erupt.core.view.EruptApiModel;
import xyz.erupt.jpa.dao.EruptDao;
import xyz.erupt.toolkit.remote.RemoteProxyService;
import xyz.erupt.toolkit.remote.RemoteQuery;
import xyz.erupt.toolkit.utils.EruptCompUtils;
import xyz.erupt.toolkit.utils.EruptDaoUtils;

import xyz.erupt.toolkit.utils.SqlUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @since :2023/3/30:13:20
 */
@RestController
public class EnterpriseCertificateController {

    @Resource
   private EruptDao eruptDao;
    @Resource
   private RemoteProxyService remoteProxyService;
    @Resource
    private EnterpriseCertificateSql enterpriseCertificateSql;

    /**
     * 获取企业附征货种名称
     * @param
     * @return
     */
    @RequestMapping("erupt-api/get/goodsType")
    @EruptRouter(verifyType = EruptRouter.VerifyType.LOGIN)
    public EruptApiModel getGoodsType() {
        MetaUserinfo remoteUserInfo = remoteProxyService.getRemoteUserInfo();

        Enterprise enterprise = EruptDaoUtils.selectOne("select * from tb_enterprise where org_code ='" +remoteUserInfo.getOrg()+"'", Enterprise.class);

        if (ObjectUtils.isEmpty(enterprise)) {
            return EruptApiModel.successApi();
        }
        List<EnterpriseCertificate> enterpriseCertificateList = EruptDaoUtils.selectOnes("select * from tb_enterprise_certificate where enterprise_id=" + enterprise.getId(), EnterpriseCertificate.class);
        if (ObjectUtils.isNotEmpty(enterpriseCertificateList)) {
            List<String> list = new ArrayList<>();
            List<LinkedTreeMap> list1 = new ArrayList<>();

            enterpriseCertificateList.forEach(v->{
                List<String> collect = Stream.of(v.getGoodsType().split("\\|")).collect(Collectors.toList());
                list.addAll(collect);
            });
            List<String> goodsType = list.stream().distinct().collect(Collectors.toList());
            if (ObjectUtils.isNotEmpty(goodsType)){
                goodsType.forEach(v->{
                    LinkedTreeMap map = new LinkedTreeMap();
                    map.put("code",v);
                    map.put("name",v);
                    list1.add(map);
                });
            }
            return EruptApiModel.successApi(list1);
        }

        return EruptApiModel.successApi();
    }

    // 统计企业附征
    @RequestMapping("erupt-api/get/selectEnterpriseCertificateNum")
    @EruptRouter(verifyType = EruptRouter.VerifyType.LOGIN)
    public EruptApiModel selectEnterpriseCertificateNum() {
        MetaUserinfo remoteUserInfo = remoteProxyService.getRemoteUserInfo();
//        List<LinkedTreeMap> linkedTreeMaps = remoteProxyService.queryEntity("Enterprise", RemoteQuery.builder().condition("orgCode", remoteUserInfo.getOrg()));
//        String isEnterprise = "1";
//        if (ObjectUtils.isEmpty(linkedTreeMaps)) {  // 不存在则是政府账号
//            isEnterprise = "0";
//        }
        String orgCode = SqlUtils.wrapIn(remoteUserInfo.getAuth());
        String sql = enterpriseCertificateSql.selectEnterpriseCertificate(orgCode);
        List<EmployeeCertificateForm> employeeCertificateForm = EruptDaoUtils.selectOnes(sql, EmployeeCertificateForm.class);
        return EruptApiModel.successApi(employeeCertificateForm);
    }


    // 统计企业数量
    @RequestMapping("erupt-api/get/selectEnterpriseNum")
    @EruptRouter(verifyType = EruptRouter.VerifyType.LOGIN)
    public EruptApiModel selectEnterpriseNum() {
        MetaUserinfo remoteUserInfo = remoteProxyService.getRemoteUserInfo();
        String orgCode = SqlUtils.wrapIn(remoteUserInfo.getAuth());
        String sql = enterpriseCertificateSql.selectEnterprisenum(orgCode);
        EmployeeCertificateForm employeeCertificateForm = EruptDaoUtils.selectOne(sql, EmployeeCertificateForm.class);
        return EruptApiModel.successApi(employeeCertificateForm);
    }

    // 统计企业类型数量
    @RequestMapping("erupt-api/get/selectEnterpriseTypeNum")
    @EruptRouter(verifyType = EruptRouter.VerifyType.LOGIN)
    public EruptApiModel selectEnterpriseTypeNum() {
        MetaUserinfo remoteUserInfo = remoteProxyService.getRemoteUserInfo();
        String orgCode = SqlUtils.wrapIn(remoteUserInfo.getAuth());
        String sql = enterpriseCertificateSql.selectEnterpriseTypeNum(orgCode);
        List<EmployeeCertificateForm> employeeCertificateForm = EruptDaoUtils.selectOnes(sql, EmployeeCertificateForm.class);
        return EruptApiModel.successApi(employeeCertificateForm);
    }

    // 查询事故周边企业
    @RequestMapping("erupt-api/get/selectEnterpriseList")
    public EruptApiModel selectEnterpriseList() {
        List<EnterpriseForm> enterprises = EruptDaoUtils.selectOnes("select org_code as code,name,map from tb_enterprise", EnterpriseForm.class);
//        List<Enterprise> list = new ArrayList<>();
//        Gson gson = GsonFactory.getGson();
//        JSONObject jsonObject = JSON.parseObject(String.valueOf(map.get("map")));
//        enterprises.forEach(v->{
//            MapFroms mapFroms = gson.fromJson(v.getMap(), MapFroms.class);
//            boolean inCircle = GisPointUtil.isInCircle(jsonObject.getDouble("lng"), jsonObject.getDouble("lat"), Double.parseDouble(mapFroms.getLocation().getLon()), Double.parseDouble(mapFroms.getLocation().getLat()), jsonObject.getDouble("radius"));
//            if (inCircle) {
//                list.add(v);
//            }
//        });

        return EruptApiModel.successApi(enterprises);
    }


    // 事故企业下拉
    @RequestMapping("erupt-api/get/selectEnterpriseInfos")
    public EruptApiModel selectEnterpriseInfos() {

        List<EnterpriseForm> enterprises = EruptDaoUtils.selectOnes("select org_code as code,name from tb_enterprise", EnterpriseForm.class);
        return EruptApiModel.successApi(enterprises);
    }

    // 港区信息
    @RequestMapping("erupt-api/get/selectEnterpriseInfogx/{orgCode}")
    @EruptRouter(verifyType = EruptRouter.VerifyType.LOGIN)
    public EruptApiModel selectEnterpriseInfogx(@PathVariable("orgCode") String orgCode) {

        EnterpriseForm enterprises = EruptDaoUtils.selectOne("select port_area as portArea from tb_enterprise where org_code='"+orgCode+"'", EnterpriseForm.class);
        RemoteProxyService remoteProxyService = EruptSpringUtil.getBean(RemoteProxyService.class);
        List<LinkedTreeMap> enterprise = remoteProxyService.queryEntity("PortArea", RemoteQuery.builder().condition("id", enterprises.getPortArea()));
        String name = String.valueOf(enterprise.get(0).get("portareaName"));
        enterprises.setPortArea(name);
        String id = String.valueOf(enterprise.get(0).get("id"));
        List<String> collect = Stream.of(id.split("\\.")).collect(Collectors.toList());
        enterprises.setPortAreaId(collect.get(0));
        return EruptApiModel.successApi(enterprises);
    }

    // 事故信息
    @RequestMapping("erupt-api/get/selectEnterpriseInfosg")
    @EruptRouter(verifyType = EruptRouter.VerifyType.LOGIN)
    public EruptApiModel selectEnterpriseInfosg() {
        List<Map<String, String>> accidentType = EruptCompUtils.getChoiceValues("accidentType");

        return EruptApiModel.successApi(accidentType);
    }

}
