/*
* Copyright © 2020-2035 yundingyun All rights reserved.
*/
package com.daliangang.emergency.proxy;

import cn.hutool.json.JSONObject;
import com.daliangang.core.DaliangangContext;
import com.daliangang.emergency.entity.EmergencyPool;
import com.google.gson.internal.LinkedTreeMap;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.stereotype.Service;
import xyz.erupt.annotation.fun.DataProxy;
import xyz.erupt.common.http.EruptRemoteUtils;
import xyz.erupt.toolkit.remote.RemoteProxyService;
import xyz.erupt.toolkit.remote.RemoteQuery;
import xyz.erupt.upms.model.EruptUser;
import xyz.erupt.upms.service.EruptUserService;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

@Service
public class EmergencyPoolDataProxy implements DataProxy<EmergencyPool> {

    @Resource
    private RemoteProxyService remoteProxyService;
    @Resource
    private EruptUserService eruptUserService;

    @Override
    public void addBehavior(EmergencyPool emergencyPool) {
        if (ObjectUtils.isNotEmpty(emergencyPool.getCompany())) {
            List<LinkedTreeMap> enterprise = remoteProxyService.queryEntity("Enterprise", RemoteQuery.builder().condition("orgCode", emergencyPool.getCompany()));
            if (ObjectUtils.isNotEmpty(enterprise)) {
                //  Integer portArea1 = (int) Float.parseFloat((String) enterprise.get(0).get("portArea"));
                //   String portArea = String.valueOf(enterprise.get(0).get("portArea"));
                String portArea=new BigDecimal(enterprise.get(0).get("portArea").toString()).stripTrailingZeros().toPlainString();
                emergencyPool.setPortArea(portArea);
            }
        }
    }


    @Override
    public void afterAdd(EmergencyPool emergencyPool) {
        JSONObject inputData = new JSONObject();
        inputData.set("clazz", "EmergencyPool");
        inputData.set("insertData",emergencyPool);
        EruptRemoteUtils.doPost(remoteProxyService.getMainServerUrl() + "/erupt-api/exchange/data/exchange", inputData, Map.class);
    }

    @Override
    public void afterUpdate(EmergencyPool emergencyPool) {
        JSONObject inputData = new JSONObject();
        inputData.set("clazz", "EmergencyPool");
        inputData.set("insertData",emergencyPool);
        EruptRemoteUtils.doPost(remoteProxyService.getMainServerUrl() + "/erupt-api/exchange/data/exchange", inputData, Map.class);
    }

    @Override
    public void searchCondition(Map<String, Object> condition) {
        if(!DaliangangContext.isDepartmentUser()){
            EruptUser user = eruptUserService.getCurrentEruptUser();
            if (user != null && user.getEruptOrg() != null)
                condition.put("company", user.getEruptOrg().getCode());
        }
    }
}
