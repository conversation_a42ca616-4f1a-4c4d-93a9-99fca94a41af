/*
* Copyright © 2020-2035 yundingyun All rights reserved.
*/
package com.daliangang.workbench.operation;

import com.daliangang.workbench.entity.Enterprise;
import org.springframework.stereotype.Service;
import xyz.erupt.annotation.fun.OperationHandler;

import java.util.List;

@Service
public class EnterpriseSynchronizePortAreaInformationHandler implements OperationHandler<Enterprise, Void> {
   @Override
   public String exec(List<Enterprise> data, Void unused, String[] param) {
       return null;
	}
}