package com.daliangang.workbench.form;

import com.daliangang.workbench.entity.Enterprise;
import io.swagger.annotations.ApiModel;
import lombok.Getter;
import lombok.Setter;
import xyz.erupt.annotation.Erupt;
import xyz.erupt.annotation.EruptField;
import xyz.erupt.annotation.sub_erupt.Power;
import xyz.erupt.annotation.sub_field.Edit;
import xyz.erupt.annotation.sub_field.EditType;
import xyz.erupt.annotation.sub_field.Readonly;
import xyz.erupt.annotation.sub_field.View;
import xyz.erupt.toolkit.db.Comment;
import xyz.erupt.upms.model.auth.DataAuthModel;

import javax.persistence.Entity;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import javax.persistence.UniqueConstraint;

/**
 * @Author: zengwensi
 * @Description:
 * @Date: Created in 2023/6/30 15:00
 * @Modified By
 */
@Erupt(name = "统计", power = @Power(add = false, delete = false, export = false, importable = false, viewDetails = false, edit = false)
        , rowOperation = {
})
@Table(name = "tb_enterprise_stat",uniqueConstraints={@UniqueConstraint(columnNames={"enterprise_id","head"})} )
@Entity
@Getter
@Setter
@Comment("统计")
@ApiModel("统计")
public class EnterpriseStatForm extends DataAuthModel {


    @EruptField(
            views = @View(title = "名称",className="text-center"),
            edit = @Edit(title = "名称", type = EditType.INPUT, readonly = @Readonly))
    private String head;

    @EruptField(
            views = @View(title = "内容",className="text-center"),
            edit = @Edit(title = "内容", type = EditType.INPUT, readonly = @Readonly))
    private String content;

    @EruptField(
            views = @View(title = "排序",show = false),
            edit = @Edit(title = "排序", type = EditType.NUMBER, readonly = @Readonly))
    private Integer ordernum;

    @ManyToOne
    private Enterprise enterprise;
}
