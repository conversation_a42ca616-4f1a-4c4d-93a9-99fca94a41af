package com.daliangang.core;

import cn.hutool.http.HttpGlobalConfig;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson2.JSONObject;
import com.daliangang.majorisk.service.UnloadShipHttpService;
import com.google.gson.internal.LinkedTreeMap;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import xyz.erupt.core.constant.MenuStatus;
import xyz.erupt.core.context.MetaContext;
import xyz.erupt.core.util.EruptSpringUtil;
import xyz.erupt.toolkit.remote.RemoteProxyService;
import xyz.erupt.toolkit.remote.RemoteQuery;
import xyz.erupt.toolkit.service.EruptCacheRedis;
import xyz.erupt.upms.model.EruptMenu;
import xyz.erupt.upms.model.EruptRole;
import xyz.erupt.upms.model.EruptUser;
import xyz.erupt.upms.model.template.EruptRoleTemplateUser;
import xyz.erupt.upms.model.template.EruptRoleTemplateUserService;
import xyz.erupt.upms.service.EruptPlatformService;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;

@Slf4j
public class DaliangangContext {

    /**
     * 判断当前用户是不是政府用户
     *
     * @return
     */
    public static boolean isDepartmentUser() {
        if (StringUtils.isEmpty(MetaContext.getToken())) return false;
        String baseRole = getBaseRole();
        return baseRole.equals("101") || baseRole.equals("102") || baseRole.equals("108") || baseRole.equals("109");
    }

    public static boolean isDepartmentEmployee() {
        if (StringUtils.isEmpty(MetaContext.getToken())) return false;
        String baseRole = getBaseRole();
        return baseRole.equals("108");
    }

    public static boolean isAreaEmployee() {
        if (StringUtils.isEmpty(MetaContext.getToken())) return false;
        String baseRole = getBaseRole();
        return baseRole.equals("109");
    }

    //市级管理员
    public static boolean isDepartmentAdmin() {
        if (StringUtils.isEmpty(MetaContext.getToken())) return false;
        return getBaseRole().equals("101");
    }

    //市级政府用户
    public static boolean isMunicipaltDepartmentUser() {
        if (StringUtils.isEmpty(MetaContext.getToken())) return false;
        String baseRole = getBaseRole();
        return baseRole.equals("101") || baseRole.equals("108");
    }

    //区县管理员
    public static boolean isDepartmentArea() {
        if (StringUtils.isEmpty(MetaContext.getToken())) return false;
        return getBaseRole().equals("102");
    }

    //区县政府用户
    public static boolean isCountryDepartmentUser() {
        if (StringUtils.isEmpty(MetaContext.getToken())) return false;
        String baseRole = getBaseRole();
        return baseRole.equals("102") || baseRole.equals("109");
    }

    public static boolean isEnterpriseUser() {
        if (StringUtils.isEmpty(MetaContext.getToken())) return false;
        return getBaseRole().equals("103");
    }

    /**
     * 当前登录人，是否是企业员工
     *
     * @return
     */
    public static boolean isEnterpriseEmployee() {
//        if (StringUtils.isEmpty(MetaContext.getToken())) return false;
//        EruptUser eruptUser = EruptSpringUtil.getBean(EruptUserService.class).getCurrentEruptUser();
//        if (eruptUser.getIsAdmin()) return false;
//        EruptOrg eruptOrg = eruptUser.getEruptOrg();
//        if (null != eruptOrg) {
//            String sql = "select * from tb_enterprise where org_code='" + eruptOrg.getCode() + "'";
//            Enterprise enterprise = EruptDaoUtils.selectOne(sql, Enterprise.class);
//            return (null != enterprise);
//        }
//        return false;

        if (StringUtils.isEmpty(MetaContext.getToken())) return false;
        return getBaseRole().equals("104");
    }

    public static String getBaseRole() {
        EruptRoleTemplateUser roleTemplateUser = EruptSpringUtil.getBean(EruptRoleTemplateUserService.class).queryCurrentAccount();
        if (roleTemplateUser == null) return "";
        if (roleTemplateUser.getTemplate() == null) return "";
        String baseRole = roleTemplateUser.getTemplate().getBaseRole();
        return baseRole;
    }


    // 根据企业orgCode获取name
    public static String getEnterpriseName(String orgCode) {
        RemoteProxyService remoteProxyService = EruptSpringUtil.getBean(RemoteProxyService.class);
        List<LinkedTreeMap> enterprise = remoteProxyService.queryEntity("Enterprise", RemoteQuery.builder().condition("org_code", orgCode));
        if (ObjectUtils.isNotEmpty(enterprise)) {
            String name = String.valueOf(enterprise.get(0).get("name"));
            return name;
        }
        return "";
    }

    // 根据企业name转code
    public static String getEnterpriseOrgCode(String name) {
        RemoteProxyService remoteProxyService = EruptSpringUtil.getBean(RemoteProxyService.class);
        List<LinkedTreeMap> enterprise = remoteProxyService.queryEntity("Enterprise", RemoteQuery.builder().condition("name", name));
        if (ObjectUtils.isNotEmpty(enterprise)) {
            String orgCode = String.valueOf(enterprise.get(0).get("orgCode"));
            return orgCode;
        }
        return "";
    }

    // 根据name转code主管部门
    public static String getDepartmentOrgCode(String name) {
        RemoteProxyService remoteProxyService = EruptSpringUtil.getBean(RemoteProxyService.class);
        List<LinkedTreeMap> enterprise = remoteProxyService.queryEntity("Department", RemoteQuery.builder().condition("name", name));
        if (ObjectUtils.isNotEmpty(enterprise)) {
            String orgCode = String.valueOf(enterprise.get(0).get("initOrgCode"));
            return orgCode;
        }
        return "";
    }

    public final static String UNLOAD_TOKEN = "UNLOAD_TOKEN:";
    // 获取三方token
    public static String getToken() {
        EruptPlatformService eruptPlatformService = EruptSpringUtil.getBean(EruptPlatformService.class);
        EruptCacheRedis eruptCacheRedis = EruptSpringUtil.getBean(EruptCacheRedis.class);
        Object value = eruptCacheRedis.get(UNLOAD_TOKEN);
        if(value != null){
            return (String) value ;
        }
        String ip = eruptPlatformService.getOption(UnloadShipHttpService.UNLOAD_TOKEN_IP).getAsString() ;
        String authorization = "";
        String url = ip+"/connect/token";

        try {
            Map map = new HashMap();
            map.put("client_id", "significant-risk-client");
            map.put("client_secret", "SignificantRisk@2023");
            map.put("grant_type", "client_credentials");
            map.put("scope", "SignificantRiskApi");
            HttpGlobalConfig.setTimeout(3000);
            String post = HttpUtil.post(url, map);
            JSONObject jsonObject = JSONObject.parseObject(post);
            String access_token = jsonObject.get("access_token").toString();
            String token_type = jsonObject.get("token_type").toString();
            authorization = token_type + " " + access_token;
            eruptCacheRedis.put(UNLOAD_TOKEN,authorization, TimeUnit.HOURS.toMillis(6));
        } catch (Exception ex) {
            HttpGlobalConfig.setTimeout(-1);
            log.error("获取三方token出错 -> " + url);
            ex.printStackTrace();
        }
        return authorization;
    }

    // 根据罐组名称获取id
    public static String getTankGroupId(String tankGroup) {
        RemoteProxyService remoteProxyService = EruptSpringUtil.getBean(RemoteProxyService.class);
        List<LinkedTreeMap> tankGroups = remoteProxyService.queryEntity("TankGroup", RemoteQuery.builder().condition("tank_group", tankGroup));
        if (ObjectUtils.isNotEmpty(tankGroups)) {
            String groupId = String.valueOf(tankGroups.get(0).get("id"));
            return groupId;
        }
        return "";
    }

    // 根据罐区名称获取id
    public static String getTankFarmId(String tankFarm) {
        RemoteProxyService remoteProxyService = EruptSpringUtil.getBean(RemoteProxyService.class);
        List<LinkedTreeMap> tankFarms = remoteProxyService.queryEntity("TankFarm", RemoteQuery.builder().condition("tank_farm", tankFarm));
        if (ObjectUtils.isNotEmpty(tankFarms)) {
            String farmId = String.valueOf(tankFarms.get(0).get("id"));
            return farmId;
        }
        return "";
    }

    //是否有某个菜单的权限
    public static boolean hasAuth(EruptUser eruptUser, String menuCode) {
        AtomicBoolean hasAuth = new AtomicBoolean(false);
        for (EruptRole role : eruptUser.getRoles()) {
            for (EruptMenu menu : role.getMenus()) {
                if (menu.getCode().equals(menuCode)) {
                    hasAuth.set(menu.getStatus() == MenuStatus.OPEN.ordinal());
                    return hasAuth.get();
                }
            }
        }
        return hasAuth.get();
    }

}
