package com.daliangang.majorisk.service;

import com.daliangang.datascreen.utils.DateUtil;
import com.daliangang.device.entity.*;
import com.daliangang.emergency.entity.Drill;
import com.daliangang.majorisk.entity.OrganProblemResponse;
import com.daliangang.rndpub.entity.InspectionResultsView;
import com.daliangang.rndpub.entity.RectificationManage;
import com.daliangang.safedaily.entity.*;
import com.daliangang.workbench.entity.EmployeeCertificateView;
import com.daliangang.workbench.entity.Enterprise;
import com.daliangang.workbench.entity.EnterpriseCertificateView;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import xyz.erupt.jpa.dao.EruptDao;

import javax.annotation.Resource;
import java.lang.reflect.Method;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.daliangang.workbench.entity.Enterprise.ENDOFMONTH;


@Service
@Slf4j
public class CompanyIssueService {
    // 定义一个枚举来管理所有检测类型
    public enum InspectionType {
        WHARF_STRUCTURE("码头结构检测", WharfStructureInspection.class),
        FIRE_FACILITY("消防设施检测", FacilityInspection.class),
        LIGHTNING_PROTECTION("防雷装置检测", LightningProtection.class),
        PRESSURE_VESSEL("压力容器检测", PressureVessel.class),
        STORAGE_TANK("储罐检测", TankInspection.class),
        PRESSURE_PIPELINE("压力管道检测", Penstock.class),
        OIL_GAS_PIPELINE("输油输气管检测", Hose.class),
        PRESSURE_GAUGE("压力表检测", PressureGauge.class),
        SAFETY_VALVE("安全阀检测", SafetyValve.class),
        GAS_DETECTOR("可燃气体报警器检测", CombustibleGas.class),
        LIFTING_MACHINERY("起重机械检测", HoistingMachinery.class),
        OTHER_INSPECTION("其他检测", OtherTests.class);

        private final String description;
        private final Class<?> entityClass;

        InspectionType(String description, Class<?> entityClass) {
            this.description = description;
            this.entityClass = entityClass;
        }
    }

    @Resource
    private EruptDao eruptDao;

    private static final int BATCH_SIZE = 10;



    public List<OrganProblemResponse> getCompanyIssues(List<String> orgCodes,String workType) {
        // 结果Map: orgCode -> issues列表
        Map<String, List<String>> result = new ConcurrentHashMap<>();

        // 将orgCodes分批
        List<List<String>> batches = Lists.partition(orgCodes, BATCH_SIZE);

        // 并行处理每一批
        CompletableFuture<Void>[] futures = batches.stream()
                .map(batch -> CompletableFuture.runAsync(() -> processBatch(batch, result,workType)))
                .toArray(CompletableFuture[]::new);

        // 等待所有批次处理完成
        CompletableFuture.allOf(futures).join();

        // 转换为 OrganProblemResponse 列表
        List<OrganProblemResponse> responses = new ArrayList<>();
        for (Map.Entry<String, List<String>> entry : result.entrySet()) {
            if (!entry.getValue().isEmpty()) {
                String orgCode = entry.getKey();
                // 查询企业名称
                Enterprise enterprise = eruptDao.queryEntity(Enterprise.class, "orgCode = :orgCode",
                        new HashMap<String, Object>() {{
                            put("orgCode", orgCode);
                        }});

                if (enterprise != null) {
                    responses.add(new OrganProblemResponse(
                            enterprise.getName(),
                            entry.getValue()
                    ));
                }
            }
        }

        return responses;
    }


    private void processBatch(List<String> orgCodes, Map<String, List<String>> result,String workType) {
        try {
            // 1. 首先创建公司名称和orgCode的映射关系
            Map<String, String> companyToOrgCodeMap = orgCodes.stream()
                    .collect(Collectors.toMap(
                            orgCode -> {
                                Enterprise enterprise = eruptDao.queryEntity(Enterprise.class,
                                        "orgCode = :orgCode",
                                        new HashMap<String, Object>() {{
                                            put("orgCode", orgCode);
                                        }});
                                return enterprise != null ? enterprise.getName().trim() : "";
                            },
                            orgCode -> orgCode,
                            (existing, replacement) -> existing
                    ));

            // 2. 获取所有公司名称列表，用于查询
            List<String> companyNames = new ArrayList<>(companyToOrgCodeMap.keySet());
            // 统计作业许可证逾期数量
            Map<String, Long> licenseExpiredCount = eruptDao.queryEntityList(
                    EnterpriseCertificateView.class,
                    "company IN :orgCodes AND expirationTime < :now",
                    new HashMap<String, Object>() {{
                        put("orgCodes", orgCodes);
                        put("now", new Date());
                    }}
            ).stream().collect(Collectors.groupingBy(
                    EnterpriseCertificateView::getCompany,
                    Collectors.counting()
            ));
            // 统计员工证书逾期数量
            Map<String, Long> certExpiredCount = eruptDao.queryEntityList(
                            EmployeeCertificateView.class,
                            "company1 IN :company1 AND validityPeriod < :now",
                            new HashMap<String, Object>() {{
                                put("company1", companyNames);
                                put("now", new Date());
                            }}
                    ).stream()
                    .filter(cert -> companyToOrgCodeMap.containsKey(cert.getCompany1().trim()))
                    .collect(Collectors.groupingBy(
                            cert -> companyToOrgCodeMap.get(cert.getCompany1().trim()),
                            Collectors.counting()
                    ));
            // 本年度安全生产责任制上报情况
            List<String> safetyResponsibility = eruptDao.queryEntityList(
                            CheckFill.class,
                            "orgCode IN :orgCodes AND year >= :currentYearDate and submitted = '1'",
                            new HashMap<String, Object>() {{
                                put("orgCodes", orgCodes);
                                put("currentYearDate", DateUtil.getCurrentYear());
                            }}
                    ).stream()
                    .map(CheckFill::getOrgCode)
                    .collect(Collectors.toList());
            // 本日安全承诺公告情况
            List<String> securityCommitment = eruptDao.queryEntityList(
                            Release.class,
                            "orgCode IN :orgCodes and DATE(fillingDate) = :currentDate",
                            new HashMap<String, Object>() {{
                                put("orgCodes", orgCodes);
                                put("currentDate", DateUtil.getCurrentDayDate());
                            }}
                    ).stream()
                    .map(Release::getOrgCode)
                    .collect(Collectors.toList());
            //本月可靠性报告单
            List<String> reliabilityReport = eruptDao.queryEntityList(
                            Report.class,
                            "orgCode IN :orgCodes and DATE(updateTime) >= :currentMonthDate and submitted = '1'",
                            new HashMap<String, Object>() {{
                                put("orgCodes", orgCodes);
                                put("currentMonthDate", DateUtil.getCurrentMonthDate());
                            }}
                    ).stream()
                    .map(Report::getOrgCode)
                    .collect(Collectors.toList());
            //今日日检查
            List<String> currentInspection = eruptDao.queryEntityList(
                            DailyInspection.class,
                            "orgCode IN :orgCodes and inspectionTime = :inspectionTime and submitted = '1'",
                            new HashMap<String, Object>() {{
                                put("orgCodes", orgCodes);
                                put("inspectionTime", DateUtil.getCurrentDayDate());
                            }}
                    ).stream()
                    .map(DailyInspection::getOrgCode)
                    .collect(Collectors.toList());
            //本周周报告
            List<String> weekReport = eruptDao.queryEntityList(
                            WeeklyReport.class,
                            "orgCode IN :orgCodes AND inspectionTime BETWEEN :startDate AND :endDate and submitted = '1'",
                            new HashMap<String, Object>() {{
                                put("orgCodes", orgCodes);
                                put("startDate", DateUtil.getOneWeekBeginDateAsDate());
                                put("endDate", DateUtil.getOneWeekLaterDateAsDate());
                            }}
                    ).stream()
                    .map(WeeklyReport::getOrgCode)
                    .collect(Collectors.toList());
            //本月月调度
            List<String> monthInspection = eruptDao.queryEntityList(
                            MonthlyScheduling.class,
                            "orgCode IN :orgCodes AND reportMonth = :reportMonth and submitted = '1'",
                            new HashMap<String, Object>() {{
                                put("orgCodes", orgCodes);
                                put("reportMonth", DateUtil.getCurrentMonth());
                            }}
                    ).stream()
                    .map(MonthlyScheduling::getOrgCode)
                    .collect(Collectors.toList());
            //本季度应急演练日
            List<java.sql.Date> range = DateUtil.getCurrentQuarterRangeAsDate();
            List<String> emergencyQuarter = eruptDao.queryEntityList(
                            Drill.class,
                            "orgCode IN :orgCodes and place BETWEEN :startOfQuarter AND :endOfQuarter",
                            new HashMap<String, Object>() {{
                                put("orgCodes", orgCodes);
                                put("startOfQuarter", range.get(0));
                                put("endOfQuarter", range.get(1));
                            }}
                    ).stream()
                    .map(Drill::getOrgCode)
                    .collect(Collectors.toList());
            //本月培训考试日
            List<String> trainingExamination = eruptDao.queryEntityList(
                            OfflineTraining.class,
                            "orgCode IN :orgCodes AND (trainBegin LIKE :monthPattern OR trainEnd LIKE :monthPattern)",
                            new HashMap<String, Object>() {{
                                put("orgCodes", orgCodes);
                                put("monthPattern", DateUtil.getCurrentMonth() + "%");
                            }}
                    ).stream()
                    .map(OfflineTraining::getOrgCode)
                    .collect(Collectors.toList());
            //本周专家检查日
            List<String> expertInspection = eruptDao.queryEntityList(
                            InspectExpertDaily.class,
                            "orgCode IN :orgCodes AND inspectDate BETWEEN :startDate AND :endDate",
                            new HashMap<String, Object>() {{
                                put("orgCodes", orgCodes);
                                put("startDate", DateUtil.getOneWeekBeginDateAsDate());
                                put("endDate", DateUtil.getOneWeekLaterDateAsDate());
                            }}
                    ).stream()
                    .map(InspectExpertDaily::getOrgCode)
                    .collect(Collectors.toList());
            //安全隐患
            List<RectificationManage> rectifications = eruptDao.queryEntityList(RectificationManage.class);
            // 获取当前年份和上一年年份
            Calendar calendar = Calendar.getInstance();
            int currentYear = calendar.get(Calendar.YEAR);
            List<Long> currentYearIds = rectifications.stream()
                    .filter(rect -> rect.getInspectionDate() != null) // 过滤掉无效数据
                    .filter(rect -> {
                        calendar.setTime(rect.getInspectionDate());
                        return calendar.get(Calendar.YEAR) == currentYear;
                    })
                    .map(RectificationManage::getId) // 提取 ID
                    .collect(Collectors.toList());
            Map<String, Long> safeCount = eruptDao.queryEntityList(
                            InspectionResultsView.class,
                            "inspection_name IN :currentYearIds and publish_status = '1' and check_object IN :deptCodes",
                            new HashMap<String, Object>() {{
                                put("currentYearIds", currentYearIds);
                                put("deptCodes", orgCodes);
                            }}).stream()
                    .filter(fil -> {
                        Date now = new Date();
                        Date rectificationTime = fil.getRectificationTime();
                        Date deadline = fil.getDeadline();
                        String inspectionResult = fil.getInspectionResult();

                        // 使用当前时间替代空的 rectificationTime
                        Date effectiveRectificationTime = (rectificationTime != null) ? rectificationTime : now;

                        // 过滤条件
                        return deadline != null && effectiveRectificationTime.compareTo(deadline) > 0 && (fil.getInspectionResult() == null || !"PASS".equals(inspectionResult));
                    })
                    .collect(Collectors.groupingBy(
                            InspectionResultsView::getCheckObject,
                            Collectors.counting()
                    ));

            //应急预案
            Map<String, Long> emergencyPlan = eruptDao.queryEntityList(
                    PlanManagement.class,
                    "orgCode IN :orgCodes and evaluationTime < :now",
                    new HashMap<String, Object>() {{
                        put("orgCodes", orgCodes);
                        put("now", new Date());
                    }}
            ).stream().collect(Collectors.groupingBy(
                    PlanManagement::getOrgCode,
                    Collectors.counting()
            ));
            //重大危险源
            // 计算到期时间：根据备案时间延后 3 年
            Function<PrePlanDangerSource, Date> calculateExpiryDate = source -> {
                Date recordDate = source.getRecordTime();
                Calendar cld = Calendar.getInstance();
                cld.setTime(recordDate);
                cld.add(Calendar.YEAR, 3); // 延后3年得到到期时间
                return cld.getTime();
            };
            // 重大危险源
            Map<String, Long> dangerSource = eruptDao.queryEntityList(
                            PrePlanDangerSource.class,
                            "orgCode IN :orgCodes",
                            new HashMap<String, Object>() {{
                                put("orgCodes", orgCodes);
                            }}
                    ).stream()
                    .filter(source -> {
                        // 计算到期时间
                        Date expiryDate = calculateExpiryDate.apply(source);
                        // 当前时间
                        Date now = new Date();
                        // 如果当前时间超过到期时间，则需要统计
                        return now.after(expiryDate);
                    })
                    .collect(Collectors.groupingBy(
                            PrePlanDangerSource::getOrgCode,
                            Collectors.counting()
                    ));
            //安全评价
            Map<String, Long> safetyEvaluation = eruptDao.queryEntityList(
                    PortRecord.class,
                    "orgCode IN :orgCodes and validityPeriod < :now",
                    new HashMap<String, Object>() {{
                        put("orgCodes", orgCodes);
                        put("now", new Date());
                    }}
            ).stream().collect(Collectors.groupingBy(
                    PortRecord::getOrgCode,
                    Collectors.counting()
            ));
            //港口保安
            Map<String, Long> portSecurity = eruptDao.queryEntityList(
                    Security.class,
                    "orgCode IN :orgCodes and effectiveDate < :now",
                    new HashMap<String, Object>() {{
                        put("orgCodes", orgCodes);
                        put("now", new Date());
                    }}
            ).stream().collect(Collectors.groupingBy(
                    Security::getOrgCode,
                    Collectors.counting()
            ));
            //12个设施检测

            // 处理每个企业的结果
            for (String orgCode : orgCodes) {
                List<String> issues = new ArrayList<>();
                // 添加作业许可证逾期信息
                Long licenseCount = licenseExpiredCount.get(orgCode);
                if (licenseCount != null && licenseCount > 0) {
                    issues.add("作业附证已逾期: " + licenseCount + "个");
                }
                // 添加员工证书逾期信息（暂时隐藏）
//                Long certCount = certExpiredCount.get(orgCode);
//                if (certCount != null && certCount > 0) {
//                    issues.add("员工证书已逾期: " + certCount + "个");
//                }
                // 检查安全生产责任制上报
                if (!safetyResponsibility.contains(orgCode) && workType.equals(ENDOFMONTH)) {
                    issues.add("未上报本年度安全生产责任制");
                }
                // 检查安全承诺公告
                if (!securityCommitment.contains(orgCode)) {
                    issues.add("未上报今日安全承诺公告");
                }
                if (!reliabilityReport.contains(orgCode) && workType.equals(ENDOFMONTH)) {
                    issues.add("未上报本月可靠性报告单");
                }
                if (!currentInspection.contains(orgCode)) {
                    issues.add("未上报今日日检查");
                }
                if (!weekReport.contains(orgCode) && workType.equals(ENDOFMONTH)) {
                    issues.add("未上报本周周报告");
                }
                if (!monthInspection.contains(orgCode) && workType.equals(ENDOFMONTH)) {
                    issues.add("未上报本月月调度");
                }
                if (!emergencyQuarter.contains(orgCode) && workType.equals(ENDOFMONTH)) {
                    issues.add("未上报本季度应急演练日");
                }
                if (!trainingExamination.contains(orgCode) && workType.equals(ENDOFMONTH)) {
                    issues.add("未上报本月培训考试日");
                }
                if (!expertInspection.contains(orgCode) && workType.equals(ENDOFMONTH)) {
                    issues.add("未上报本周专家检查日");
                }
                // 添加安全隐患逾期信息
                Long safe = safeCount.get(orgCode);
                if (safe != null && safe > 0) {
                    issues.add("安全隐患已逾期: " + safe + "项");
                }
                Long emergencyPlanCount = emergencyPlan.get(orgCode);
                if (emergencyPlanCount != null && emergencyPlanCount > 0) {
                    issues.add("应急预案已逾期: " + emergencyPlanCount + "个");
                }
                Long dangerSourceCount = dangerSource.get(orgCode);
                if (dangerSourceCount != null && dangerSourceCount > 0) {
                    issues.add("重大危险源已逾期: " + dangerSourceCount + "个");
                }
                Long safetyEvaluationCount = safetyEvaluation.get(orgCode);
                if (safetyEvaluationCount != null && safetyEvaluationCount > 0) {
                    issues.add("安全评价已逾期: " + safetyEvaluationCount + "个");
                }
                Long portSecurityCount = portSecurity.get(orgCode);
                if (portSecurityCount != null && portSecurityCount > 0) {
                    issues.add("港口保安已逾期: " + portSecurityCount + "个");
                }
                // 12个设施检测
                for (InspectionType type : InspectionType.values()) {
                    Map<String, Long> inspectionCounts = countExpiredInspections(type, orgCodes);
                    Long count = inspectionCounts.get(orgCode);
                    if (count != null && count > 0) {
                        issues.add(type.description + "已逾期: " + count + "个");
                    }
                }
                // 如果有问题，添加到结果中
                if (!issues.isEmpty()) {
                    result.put(orgCode, issues);
                }

            }
        } catch (Exception e) {
            log.error("处理批次数据异常, orgCodes: {}", orgCodes, e);
        }
    }
    // 通用的统计方法
    private <T> Map<String, Long> countExpiredInspections(InspectionType type, List<String> orgCodes) {
        return eruptDao.queryEntityList(
                        type.entityClass,
                        "orgCode IN :orgCodes and validityPeriod < :now",
                        new HashMap<String, Object>() {{
                            put("orgCodes", orgCodes);
                            put("now",DateUtil.getCurrentDayDate());
                        }}
                ).stream()
                .collect(Collectors.groupingBy(
                        this::getOrgCode,
                        Collectors.counting()
                ));
    }
    // 获取机构代码的通用方法
    private String getOrgCode(Object entity) {
        try {
            Method method = entity.getClass().getMethod("getOrgCode");
            return (String) ((Method) method).invoke(entity);
        } catch (Exception e) {
            log.error("获取机构代码失败", e);
            return "";
        }
    }
}