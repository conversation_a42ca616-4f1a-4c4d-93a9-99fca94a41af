/*
 * Copyright © 2020-2035 yundingyun All rights reserved.
 */
package com.daliangang.emergency.entity;

import com.daliangang.core.CompanyRenderHandler;
import com.daliangang.emergency.proxy.DrillDataProxy;
import com.daliangang.safedaily.operation.ButtonShowHandler;
import com.daliangang.safedaily.operation.DepartPowerHandler;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import xyz.erupt.annotation.Erupt;
import xyz.erupt.annotation.EruptField;
import xyz.erupt.annotation.expr.ExprBool;
import xyz.erupt.annotation.sub_erupt.Power;
import xyz.erupt.annotation.sub_erupt.RowOperation;
import xyz.erupt.annotation.sub_field.Edit;
import xyz.erupt.annotation.sub_field.EditType;
import xyz.erupt.annotation.sub_field.Readonly;
import xyz.erupt.annotation.sub_field.View;
import xyz.erupt.annotation.sub_field.sub_edit.*;
import xyz.erupt.toolkit.db.Comment;
import xyz.erupt.toolkit.handler.DelOperationHandler;
import xyz.erupt.toolkit.handler.EditOperationHandler;
import xyz.erupt.toolkit.handler.EruptDictTagFetchHandler;
import xyz.erupt.toolkit.handler.RemoteEntityChoiceFetchHandler;
import xyz.erupt.toolkit.submit.CheckSubmit;
import xyz.erupt.toolkit.utils.TplUtils;
import xyz.erupt.upms.dict.Dict;
import xyz.erupt.upms.dict.DictItem;
import xyz.erupt.upms.dict.Dicts;
import xyz.erupt.upms.model.auth.DataAuthHandler;
import xyz.erupt.upms.model.auth.DataAuthModel;

import javax.persistence.Entity;
import javax.persistence.Table;

@Erupt(name = "演练管理", power = @Power(powerHandler = DepartPowerHandler.class)
        , dataProxy = DrillDataProxy.class
        ,orderBy = "Drill.place desc",
        rowOperation = {
//                @RowOperation(
//                        title = "上报",
//                        icon = "fa fa-arrow-circle-o-up",
//                        operationHandler = DrillEscalationHandler.class,
//                        ifExpr = "item.submitted=='未上报'",
//                        mode = RowOperation.Mode.SINGLE,
//                        show = @ExprBool(exprHandler = ButtonShowHandler.class)
//                ),
                @RowOperation(
                        title = "编辑",
                        icon = "fa fa-edit",
                        code = TplUtils.EDIT_OPER_CODE,
                        eruptClass = Drill.class,
                        operationHandler = EditOperationHandler.class,
//                        ifExpr = "item.submitted=='未上报'",
                        ifExprBehavior = RowOperation.IfExprBehavior.HIDE,
                        mode = RowOperation.Mode.SINGLE,
                        show = @ExprBool(exprHandler = ButtonShowHandler.class)
                ),
                @RowOperation(
                        title = "删除",
                        icon = "fa fa-trash-o",
                        operationHandler = DelOperationHandler.class,
//                        ifExpr = "item.submitted=='未上报'",
                        ifExprBehavior = RowOperation.IfExprBehavior.HIDE,
                        mode = RowOperation.Mode.SINGLE,
                        show = @ExprBool(exprHandler = ButtonShowHandler.class)
                )
        })
@Table(name = "tb_drill")
@Entity
@Getter
@Setter
@Comment("演练管理")
@ApiModel("演练管理")
@Dicts(
        value = {
                @Dict(code = "drillType", name = "演练分类", value = {
                        @DictItem(code = "DRILL_DESKTOP", value = "桌面演练"),
                        @DictItem(code = "DRILL_PRACTICE", value = "实战演练"),
                        @DictItem(code = "DRILL_SINGLE", value = "单项演练"),
                        @DictItem(code = "DRILL_MULTI", value = "综合演练")
                }),
                @Dict(code = "drillLevel", name = "演练级别", value = {
                @DictItem(code = "COMPREHENSIVE", value = "综合预案"),
                @DictItem(code = "SPECIAL", value = "专项预案"),
                @DictItem(code = "DISPOSAL", value = "处置方案"),
        })
        }
)
public class Drill extends DataAuthModel {
    @EruptField(
            views = @View(title = "演练名称",width = "250px"),
            edit = @Edit(title = "演练名称", type = EditType.INPUT, search = @Search(vague = true), notNull = true,
                    inputType = @InputType))
    @Comment("演练名称")
    @ApiModelProperty("演练名称")
    private String name;

    @EruptField(
            views = @View(title = "演练日期",width = "150px"),
            edit = @Edit(title = "演练日期", type = EditType.DATE, search = @Search(vague = true), notNull = true,
                    dateType = @DateType))
    @Comment("演练日期")
    @ApiModelProperty("演练日期")
    private java.util.Date place;

    @EruptField(
            views = @View(title = "演练单位",width = "200px"),
            edit = @Edit(title = "演练单位",
                    search = @Search(ifRender = @ExprBool(exprHandler = CompanyRenderHandler.class)),
                    type = EditType.CHOICE, notNull = true, readonly = @Readonly(exprHandler = DataAuthHandler.class),
                    choiceType = @ChoiceType(fullSpan = true,fetchHandler = RemoteEntityChoiceFetchHandler.class, fetchHandlerParams = {"main", "Enterprise", "org_code,name"})))
    @Comment("演练单位")
    @ApiModelProperty("演练单位")
    private String company;

    @EruptField(
            views = @View(title = "演练分类",width = "100px"),
            edit = @Edit(title = "演练分类", type = EditType.TAGS, search = @Search, notNull = true,
                    tagsType = @TagsType(allowExtension = false, fetchHandler = EruptDictTagFetchHandler.class, fetchHandlerParams = "drillType")))
    @Comment("演练分类")
    @ApiModelProperty("演练分类")
    private String drillType;

    @EruptField(
            views = @View(title = "现场照片", show = false),
            edit = @Edit(title = "现场照片", type = EditType.ATTACHMENT, notNull = true,
                    attachmentType = @AttachmentType(type = AttachmentType.Type.IMAGE, maxLimit = 20, fileTypes = {".png", ".jpg", ".bmp"})))
    @Comment("现场照片")
    @ApiModelProperty("现场照片")
    private String pictures;

    @EruptField(
            views = @View(title = "应急演练报告", show = false),
            edit = @Edit(title = "应急演练报告", type = EditType.ATTACHMENT, notNull = true,
                    attachmentType = @AttachmentType(type = AttachmentType.Type.BASE, maxLimit = 20, fileTypes = {".pdf", ".doc", ".docx", ".xlsx", ".png", ".jpg", ".xls", ".ppt", ".pptx", ".zip", ".rar"})))
    @Comment("应急演练报告")
    @ApiModelProperty("应急演练报告")
    private String keyWord;

    @EruptField(
            views = @View(title = "签到表", show = false),
            edit = @Edit(title = "签到表", type = EditType.ATTACHMENT, notNull = true,
                    attachmentType = @AttachmentType(type = AttachmentType.Type.BASE, maxLimit = 20, fileTypes = {".pdf", ".doc", ".docx", ".xlsx", ".png", ".jpg", ".xls", ".ppt", ".pptx", ".zip", ".rar"})))
    @Comment("签到表")
    @ApiModelProperty("签到表")
    private String signIn;

    @EruptField(
            views = @View(title = "其他附件", show = false),
            edit = @Edit(title = "其他附件", type = EditType.ATTACHMENT,
                    attachmentType = @AttachmentType(type = AttachmentType.Type.BASE, maxLimit = 20, fileTypes = {".pdf", ".doc", ".docx", ".xlsx", ".png", ".jpg", ".xls", ".ppt", ".pptx", ".zip", ".rar"})))
    @Comment("其他附件")
    @ApiModelProperty("其他附件")
    private String accidentConsequence;

    @EruptField(
            views = @View(title = "级别",width = "100px"),
            edit = @Edit(title = "级别", type = EditType.TAGS, search = @Search, notNull = true,
                    tagsType = @TagsType(allowExtension = false, fetchHandler = EruptDictTagFetchHandler.class, fetchHandlerParams = "drillLevel")))
    @Comment("级别")
    @ApiModelProperty("级别")
    private String drillLevel;

//    @EruptField(
//            views = @View(title = "企业", show = false),
//            edit = @Edit(title = "企业", type = EditType.INPUT, search = @Search, show = false, notNull = true,
//                    inputType = @InputType))
//    @Comment("企业")
//    @ApiModelProperty("企业")
//    private String company;

    @EruptField(
            views = @View(title = "上报时间",width = "100px",show = false),
            edit = @Edit(title = "上报时间", type = EditType.DATE, search = @Search, show = false,
                    dateType = @DateType))
    @Comment("上报时间")
    @ApiModelProperty("上报时间")
    private java.util.Date fillingDate;

    @EruptField(
            views = @View(title = "上报状态",width = "100px",show = false),
            edit = @Edit(title = "上报状态", type = EditType.BOOLEAN, show = false,/*notNull = true, */
                    boolType = @BoolType(trueText = "已上报", falseText = "未上报")))
    @CheckSubmit(linkProperty = "createTime", checkTimeDiffPeriod = 1)
    @Comment("上报状态")
    @ApiModelProperty("上报状态")
    private Boolean submitted;

}
