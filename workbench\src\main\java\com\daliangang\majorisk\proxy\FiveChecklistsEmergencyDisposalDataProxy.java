package com.daliangang.majorisk.proxy;

import com.daliangang.majorisk.entity.FiveChecklistsEmergencyDisposal;
import org.springframework.stereotype.Service;
import xyz.erupt.annotation.fun.DataProxy;
import xyz.erupt.annotation.query.Condition;
import xyz.erupt.devtools.nodeinfo.DevNodeInfo;
import xyz.erupt.toolkit.utils.EruptDaoUtils;
import xyz.erupt.upload.MinioUploader;

import javax.annotation.Resource;
import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @since :2023/4/17:10:52
 */
@Service
public class FiveChecklistsEmergencyDisposalDataProxy implements DataProxy<FiveChecklistsEmergencyDisposal> {

}
