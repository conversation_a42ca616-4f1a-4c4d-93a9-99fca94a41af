/*
 * Copyright © 2020-2035 yundingyun All rights reserved.
 */
package com.daliangang.rndpub.entity;

import com.daliangang.rndpub.proxy.ProcedureDataProxy;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import xyz.erupt.annotation.Erupt;
import xyz.erupt.annotation.EruptField;
import xyz.erupt.annotation.sub_erupt.*;
import xyz.erupt.annotation.sub_field.Edit;
import xyz.erupt.annotation.sub_field.EditType;
import xyz.erupt.annotation.sub_field.View;
import xyz.erupt.annotation.sub_field.sub_edit.DateType;
import xyz.erupt.annotation.sub_field.sub_edit.InputType;
import xyz.erupt.annotation.sub_field.sub_edit.NumberType;
import xyz.erupt.annotation.sub_field.sub_edit.Search;
import xyz.erupt.toolkit.db.Comment;
import xyz.erupt.upms.model.auth.DataAuthModel;

import javax.persistence.Entity;
import javax.persistence.Lob;
import javax.persistence.Table;

@Erupt(name = "检查流程", /*authVerify = false,*/ power = @Power(add = true, delete = true, export = false, importable = true, viewDetails = true, edit = true)
        , dataProxy = ProcedureDataProxy.class,orderBy = "inspectionDate desc",
        drills = {
                @Drill(title = "抽取企业", code= "EnterpriseInformation",icon = "fa fa-building-o", link = @Link(linkErupt = EnterpriseInformation.class, joinColumn = "procedure.id", condition = "state=false"), pageModel = @PageModel(flag = true)),
                @Drill(title = "抽取专家", code= "ExpertInformation",icon = "fa fa-address-card", link = @Link(linkErupt = ExpertInformation.class, joinColumn = "procedure.id", condition = "state=false")  , pageModel = @PageModel(flag = true)),
                @Drill(title = "抽取检查人员",code= "Inspector", icon = "fa fa-male text", link = @Link(linkErupt = Inspector.class, joinColumn = "procedure.id", condition = "state=false"), pageModel = @PageModel(flag = true)),
                @Drill(title = "抽取检查事项", code= "InspectionItems",icon = "fa fa-list-ol", link = @Link(linkErupt = InspectionItems.class, joinColumn = "procedure.id", condition = "state=false"), pageModel = @PageModel(flag = true))
        }
        , rowOperation = {
        @RowOperation(title = "查看", icon = "fa fa-search-plus", confirm =false, type = RowOperation.Type.TPL,mode = RowOperation.Mode.SINGLE,tpl = @Tpl(path = "tpl/procedureView.tpl"))
//        @RowOperation(title = "测试Avue表格", icon = "fa fa-building-o text red", mode = RowOperation.Mode.BUTTON, type = RowOperation.Type.TPL, tplWidth = "100vw", tplHeight = "90vh", tpl = @Tpl(path = "tpl/AvueCrud.ftl", tplHandler = AvueCrudTplHandler.class, params = "EruptRoleTemplatePost")),
//        @RowOperation(title = "测试Avue表单", icon = "fa fa-building-o text red", mode = RowOperation.Mode.BUTTON, type = RowOperation.Type.TPL, tplHeight = "50vh", tpl = @Tpl(path = "tpl/AvueForm.ftl", tplHandler = AvueFormTplHandler.class, params = "EruptRoleTemplatePost")),
//        @RowOperation(title = "抽取专家", icon = "fa fa-address-card text red", operationHandler = ProcedureExportCheckItemsHandler.class, mode = RowOperation.Mode.SINGLE),
//        @RowOperation(title = "抽取检查人员", icon = "fa fa-male text red", operationHandler = ProcedureExportCheckItemsHandler.class, mode = RowOperation.Mode.SINGLE),
//        @RowOperation(title = "抽取检查事项", icon = "fa fa-list-ol text red", operationHandler = ProcedureExportCheckItemsHandler.class, mode = RowOperation.Mode.SINGLE),
//        @RowOperation(title = "导出检查事项", icon = "fa fa-arrow-down", operationHandler = ProcedureExportCheckItemsHandler.class, mode = RowOperation.Mode.SINGLE),
})
@Table(name = "tb_procedure")
@Entity
@Getter
@Setter
@Comment("检查流程")
@ApiModel("检查流程")
public class Procedure extends DataAuthModel {
    @EruptField(
            views = @View(title = "检查名称", show = true),
            edit = @Edit(title = "检查名称", type = EditType.INPUT, search = @Search(vague = true), notNull = true,
                    inputType = @InputType))
    @Comment("检查名称")
    @ApiModelProperty("检查名称")
    private String name;

    @EruptField(
            views = @View(title = "检查开始日期", show = true),
            edit = @Edit(title = "检查开始日期", type = EditType.DATE, search = @Search(vague = true), notNull = true,
                    dateType = @DateType))
    @Comment("检查开始日期")
    @ApiModelProperty("检查开始日期")
    private java.util.Date inspectionDate;

    @EruptField(
            views = @View(title = "检查企业数量", show = true),
            edit = @Edit(title = "检查企业数量", type = EditType.NUMBER, notNull = false, show = false,
                    numberType = @NumberType))
    @Comment("检查企业数量")
    @ApiModelProperty("检查企业数量")
    private Integer numberOfEnterprises;

    @EruptField(
            views = @View(title = "检查专家人数", show = true),
            edit = @Edit(title = "检查专家人数", type = EditType.INPUT, notNull = false, show = false,
                    inputType = @InputType))
    @Comment("检查专家人数")
    @ApiModelProperty("检查专家人数")
    private Integer inspectionExpert;

    @EruptField(
            views = @View(title = "检查人员数量", show = true),
            edit = @Edit(title = "检查人员数量", type = EditType.NUMBER, notNull = false, show = false,
                    numberType = @NumberType))
    @Comment("检查人员数量")
    @ApiModelProperty("检查人员数量")
    private Integer inspectorsNo;



    @EruptField(
            views = @View(title = "问题数量", show = false),
            edit = @Edit(title = "问题数量", type = EditType.NUMBER, notNull = false, show = false,
                    numberType = @NumberType))
    @Comment("问题数量")
    @ApiModelProperty("问题数量")
    private Integer numberOfQuestions;

    @EruptField(
            views = @View(title = "已整改数量", show = false),
            edit = @Edit(title = "已整改数量", type = EditType.NUMBER, notNull = false, show = false,
                    numberType = @NumberType))
    @Comment("已整改数量")
    @ApiModelProperty("已整改数量")
    private Integer rectified;

    @EruptField(
            views = @View(title = "未整改数量", show = false),
            edit = @Edit(title = "未整改数量", type = EditType.NUMBER, notNull = false, show = false,
                    numberType = @NumberType))
    @Comment("未整改数量")
    @ApiModelProperty("未整改数量")
    private Integer notRectified;

    @EruptField(
            views = @View(title = "逾期数", show = false),
            edit = @Edit(title = "逾期数", type = EditType.NUMBER, notNull = false, show = false,
                    numberType = @NumberType))
    @Comment("逾期数")
    @ApiModelProperty("逾期数")
    private Integer overdue;

    @EruptField(
            views = @View(title = "检查企业", show = false),
            edit = @Edit(title = "检查企业", type = EditType.INPUT, search = @Search(vague = true), notNull = false, show = false
//                    choiceType = @ChoiceType(fullSpan = true,fetchHandler = RemoteEntityChoiceFetchHandler.class, fetchHandlerParams = {"main", "Enterprise", "org_code,name"})
            )
    )
    @Comment("检查企业")
    @ApiModelProperty("检查企业")
    @Lob
    private String enterprises;


    @EruptField(
            views = @View(title = "检查专家", show = false),
            edit = @Edit(title = "检查专家", type = EditType.INPUT, search = @Search(vague = true), notNull = false, show = false,
                    inputType = @InputType))
    @Comment("检查专家")
    @ApiModelProperty("检查专家")
    @Lob
    private String expert;

    @EruptField(
            views = @View(title = "检查人员", show = false),
            edit = @Edit(title = "检查人员", type = EditType.INPUT, search = @Search(vague = true), notNull = false, show = false,
                    inputType = @InputType))
    @Comment("检查人员")
    @ApiModelProperty("检查人员")
    @Lob
    private String inspectors;

    @EruptField(
            views = @View(title = "检查项目", show = false),
            edit = @Edit(title = "检查项目", type = EditType.INPUT, notNull = false, show = false,
                    inputType = @InputType))
    @Comment("检查项目")
    @ApiModelProperty("检查项目")
    @Lob
    private String inspectionItems;
}
