/*
 * Copyright © 2020-2035 yundingyun All rights reserved.
 */
package com.daliangang.emergency.entity;

import com.daliangang.core.DaliangangContext;
import com.daliangang.emergency.proxy.EmergencyDutyDataProxy;
import com.daliangang.emergency.template.EmergencyDutyTemplate;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;
import xyz.erupt.annotation.Erupt;
import xyz.erupt.annotation.EruptField;
import xyz.erupt.annotation.sub_erupt.Power;
import xyz.erupt.annotation.sub_field.Edit;
import xyz.erupt.annotation.sub_field.EditType;
import xyz.erupt.annotation.sub_field.Readonly;
import xyz.erupt.annotation.sub_field.View;
import xyz.erupt.annotation.sub_field.sub_edit.ChoiceType;
import xyz.erupt.annotation.sub_field.sub_edit.DateType;
import xyz.erupt.annotation.sub_field.sub_edit.InputType;
import xyz.erupt.excel.template.ExcelTemplate;
import xyz.erupt.excel.template.ExcelTemplates;
import xyz.erupt.jpa.model.MetaModel;
import xyz.erupt.toolkit.db.Comment;
import xyz.erupt.toolkit.handler.RemoteEntityChoiceFetchHandler;
import xyz.erupt.upms.model.auth.DataAuthHandler;

import javax.persistence.*;
import java.sql.Timestamp;
import java.util.Set;

@Erupt(name = "应急值守管理",
        power = @Power(
                export = true,
                importable = true
        )
        , dataProxy = EmergencyDutyDataProxy.class,
        orderBy = "dutyTime desc"
        , rowOperation = {})
@Table(name = "tb_emergency_duty")
@Entity
@Getter
@Setter
@Comment("应急值守管理")
@ApiModel("应急值守管理")
@ExcelTemplates({@ExcelTemplate(erupt = EmergencyDuty.class, template = "应急值守管理_template.xls", clazz = EmergencyDutyTemplate.class, handler = EmergencyDutyTemplate.class)})
public class EmergencyDuty extends MetaModel implements Readonly.ReadonlyHandler {

    @EruptField(
            views = @View(title = "所属单位",width = "240px"),
            edit = @Edit(title = "所属单位",
                    type = EditType.CHOICE,
                    readonly = @Readonly(exprHandler = DataAuthHandler.class),
                    choiceType = @ChoiceType(fetchHandler = RemoteEntityChoiceFetchHandler.class,
                            fetchHandlerParams = {"main", "EruptOrg", "code,name"})))
    @Comment("所属单位")
    @ApiModelProperty("所属单位")
    private String company;

    @EruptField(
            views = @View(title = "编号", show = false),
            edit = @Edit(title = "编号", type = EditType.NUMBER, show = false))
    @Comment("编号")
    @ApiModelProperty("编号")
    private long dutyId;

    @EruptField(
            views = @View(title = "开始值守时间（年月日时分）", width = "210px",template = "value.substring(0,value.length-3)"),
            edit = @Edit(title = "开始值守时间（年月日时分）",
                    type = EditType.DATE,
                    notNull = true,
                    dateType = @DateType(type = DateType.Type.DATE_TIME,format = "yyyy-MM-dd HH:mm",showTime = "HH:mm")))
    @Comment("开始值守时间（年月日时分）")
    @ApiModelProperty("开始值守时间（年月日时分）")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm", timezone = "GMT+8")
    private Timestamp dutyTime;

    @EruptField(
            views = @View(title = "结束值守时间（年月日时分）",width = "210px",template = "value.substring(0,value.length-3)"),
            edit = @Edit(title = "结束值守时间（年月日时分）",
                    type = EditType.DATE,
                    notNull = true,
                    dateType = @DateType(type = DateType.Type.DATE_TIME,format = "yyyy-MM-dd HH:mm",showTime = "HH:mm")))
    @Comment("结束值守时间（年月日时分）")
    @ApiModelProperty("结束值守时间（年月日时分）")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm", timezone = "GMT+8")
    private Timestamp endDutyTime;

    @EruptField(
            views = @View(title = "编辑模式", show = false, sortable = true),
            edit = @Edit(title = "编辑模式", type = EditType.BOOLEAN, show = false))
    @Transient
    private Boolean edit = false;

    @EruptField(
            views = @View(title = "值守人", show = false),
            edit = @Edit(title = "值守人", type = EditType.INPUT,
//                    showBy = @ShowBy(dependField = "edit", expr = "value != true"),
                    inputType = @InputType,
                    show = false
            )
    )
    @Comment("值守人")
    @ApiModelProperty("值守人")
    @Transient
    private String personOnDuty;

    @EruptField(
            views = @View(title = "值守人数量",width = "210px"),
            edit = @Edit(
                    readonly = @Readonly,
                    type = EditType.INPUT,
                    show = false,
                    title = "值守人数量")
    )
    @Comment("值守人数量")
    @ApiModelProperty("值守人数量")
    private String personNum;

    @EruptField(
            views = @View(title = "联系方式", show = false),
            edit = @Edit(title = "联系方式", type = EditType.INPUT,
//                    showBy = @ShowBy(dependField = "edit", expr = "value != true"),
                    show = false,
                    inputType = @InputType
            )
    )
    @Comment("联系方式")
    @ApiModelProperty("联系方式")
    @Transient
    private String tel;



    @EruptField(
            edit = @Edit(title = "值守人", type = EditType.TAB_TABLE_ADD, notNull = true))
    @OneToMany(cascade = CascadeType.ALL, orphanRemoval = true)
    @OrderBy
    @JoinColumn(name = "emergency_duty_id")
    @Comment("值守人")
    @ApiModelProperty("值守人")
    private Set<EmergencyDutyPerson> emergencyDutyPeople;


    @Override
    public boolean add(boolean add, String[] params) {
        return edit(add, params);
    }

    @Override
    public boolean edit(boolean edit, String[] params) {
        if (DaliangangContext.isDepartmentUser()) return false;
        return true;
    }
}
