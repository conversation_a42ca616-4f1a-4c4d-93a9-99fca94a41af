package com.daliangang.majorisk.form;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import xyz.erupt.annotation.Erupt;
import xyz.erupt.annotation.EruptField;
import xyz.erupt.annotation.sub_erupt.Power;
import xyz.erupt.annotation.sub_field.Edit;
import xyz.erupt.annotation.sub_field.EditType;
import xyz.erupt.annotation.sub_field.View;
import xyz.erupt.annotation.sub_field.sub_edit.InputType;
import xyz.erupt.annotation.sub_field.sub_edit.Search;
import xyz.erupt.annotation.sub_field.sub_edit.TagsType;
import xyz.erupt.jpa.model.BaseModel;
import xyz.erupt.toolkit.db.Comment;
import xyz.erupt.toolkit.handler.EruptDictTagFetchHandler;

import javax.persistence.Entity;

/**
 * <AUTHOR>
 * @since :2023/4/7:16:29
 */
@Erupt(name = "应急物资", power = @Power(add = false, delete = false, export = false, importable = true, viewDetails = false, edit = false)
        , rowOperation = {})

@Getter
@Setter
@Comment("应急物资")
@ApiModel("应急物资")
public class EmergencySuppliesForm extends BaseModel {
    @EruptField(
            views = @View(title = "应急物资储备点名称"),
            edit = @Edit(title = "应急物资储备点名称", type = EditType.INPUT, search = @Search(vague = true), notNull = true,
                    inputType = @InputType))
    @Comment("应急物资储备点名称")
    @ApiModelProperty("应急物资储备点名称")
    private String materialReserve;


    @EruptField(
            views = @View(title = "物资类型"),
            edit = @Edit(title = "物资类型", type = EditType.TAGS, notNull = true,
                    tagsType = @TagsType(allowExtension = false, fetchHandler = EruptDictTagFetchHandler.class, fetchHandlerParams = "materialType")))
    @Comment("物资类型")
    @ApiModelProperty("物资类型")
    private String materialType;

    @EruptField(
            views = @View(title = "联系人"),
            edit = @Edit(title = "联系人", type = EditType.INPUT, notNull = true,
                    inputType = @InputType))
    @Comment("联系人")
    @ApiModelProperty("联系人")
    private String contacts;

    @EruptField(
            views = @View(title = "联系电话"),
            edit = @Edit(title = "联系电话", type = EditType.INPUT, notNull = true,
                    inputType = @InputType))
    @Comment("联系电话")
    @ApiModelProperty("联系电话")
    private String contactNumber;
}
