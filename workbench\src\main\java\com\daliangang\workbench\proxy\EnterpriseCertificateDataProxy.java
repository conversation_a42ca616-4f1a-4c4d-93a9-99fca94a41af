/*
 * Copyright © 2020-2035 yundingyun All rights reserved.
 */
package com.daliangang.workbench.proxy;


import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.daliangang.workbench.entity.Enterprise;
import com.daliangang.workbench.entity.EnterpriseCertificate;
import com.daliangang.workbench.entity.EnterpriseCertificateCommon;
import com.daliangang.workbench.entity.EnterpriseCertificateView;
import com.google.gson.JsonObject;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.stereotype.Service;
import xyz.erupt.annotation.fun.DataProxy;
import xyz.erupt.annotation.query.Condition;
import xyz.erupt.common.http.EruptRemoteUtils;
import xyz.erupt.core.context.MetaContext;
import xyz.erupt.jpa.dao.EruptDao;
import xyz.erupt.toolkit.remote.RemoteProxyService;
import xyz.erupt.toolkit.utils.AssertUtils;
import xyz.erupt.toolkit.utils.EruptDaoUtils;
import xyz.erupt.toolkit.utils.NotifyUtils;
import xyz.erupt.toolkit.utils.SqlUtils;
import xyz.erupt.upms.model.EruptUser;
import xyz.erupt.upms.service.EruptUserService;

import javax.annotation.Resource;
import javax.transaction.Transactional;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.atomic.AtomicReference;

@Service
public class EnterpriseCertificateDataProxy implements DataProxy<EnterpriseCertificate> {

    @Resource
    private EruptUserService eruptUserService;

    @Resource
    private EruptDao eruptDao;

    @Resource
    private RemoteProxyService remoteProxyService;

    @Override
    public String beforeFetch(List<Condition> conditions) {
        //只能查看自己的数据
        AtomicReference<String> returnStr = new AtomicReference<>(" 1 = 1 and company "+SqlUtils.wrapIn(eruptUserService.getUserAuth())) ;
        //状态查询条件
        Optional<Condition> stateCondition = conditions.stream().filter(condition ->
                "expiredState".equals(condition.getKey())
        ).findFirst();
        stateCondition.ifPresent( f ->{
            LocalDateTime now = LocalDateTime.now();
            String expiredState = (String)f.getValue();
            if("EXPIRED".equals(expiredState)){
                //已逾期
                returnStr.set(returnStr.get()+" and CURRENT_DATE() > expiration_time");
            }else if("NORMAL".equals(expiredState)){
                //正常
                returnStr.set(returnStr.get()+" and  datediff(expiration_time,CURRENT_DATE()) > 90");
            }else {
                //即将逾期
                returnStr.set(returnStr.get() + "and  CURRENT_DATE() <= expiration_time and datediff(expiration_time,CURRENT_DATE()) < 90") ;
            }
            conditions.remove(f) ;
        });
        Optional<Condition> searchCompany = conditions.stream().filter(condition ->
                "searchCompany".equals(condition.getKey())
        ).findFirst();
        searchCompany.ifPresent( f ->{
            f.setKey("company");
        });
        return returnStr.get();
    }

    @Override
    public void addBehavior(EnterpriseCertificate certificate) {
        if (MetaContext.getAttribute(EnterpriseDataProxy.ENTERPRISE_ORG_CODE) != null)
            certificate.setCompany(MetaContext.getAttribute(EnterpriseDataProxy.ENTERPRISE_ORG_CODE).toString());
    }

    @Override
    public void beforeAdd(EnterpriseCertificate certificate) {
        EruptUser eruptUser = eruptUserService.getCurrentEruptUser();
        if (eruptUser != null && eruptUser.getEruptOrg() != null && StringUtils.isEmpty(certificate.getCompany())) {
            certificate.setCompany(eruptUser.getEruptOrg().getCode());
        }
    }

    @Override
    public void afterAdd(EnterpriseCertificate enterpriseCertificate) {
        //同步到一体化平台
        this.syncEnterpriseCertificateData(enterpriseCertificate);
    }

    @Override
    public void afterUpdate(EnterpriseCertificate enterpriseCertificate) {
        //同步到一体化平台
        this.syncEnterpriseCertificateData(enterpriseCertificate);
    }

    /**
     * 同步到一体化平台
     * @param enterpriseCertificate
     */
    public void syncEnterpriseCertificateData(EnterpriseCertificateCommon enterpriseCertificate){
        EnterpriseCertificateView view = JSONUtil.toBean(JSONUtil.toJsonStr(enterpriseCertificate), EnterpriseCertificateView.class);
        Enterprise enterprise = eruptDao.queryEntity(Enterprise.class, "org_code =" + SqlUtils.wrapStr(enterpriseCertificate.getCompany()));
        view.setCompany(enterprise.getName());
        JSONObject inputData = new JSONObject();
        inputData.set("clazz", "EnterpriseCertificate");
        inputData.set("insertData",view);
        EruptRemoteUtils.doPost(remoteProxyService.getMainServerUrl() + "/erupt-api/exchange/data/exchange", inputData, Map.class);
    }

    @Override
    @Transactional
    public void afterFetch(Collection<Map<String, Object>> list) {
        list.forEach(vo -> {
            vo.put("erupt", "EnterpriseCertificate");
//            String sql = "update tb_enterprise_certificate set state = \"%s\" where id = %s" ;
//            eruptDao.getJdbcTemplate().update(String.format(sql,vo.get("state"),vo.get("id")));
        });
    }

    @Override
    public void excelImport(Object workbook) {
        Workbook wb = (Workbook) workbook;
        Sheet sheet = wb.getSheetAt(0);
        for (int i = sheet.getFirstRowNum(); i <= sheet.getLastRowNum(); i++) {
            Row row = sheet.getRow(i);
            Cell cell = row.getCell(1);
            AssertUtils.notNull(cell, "附证编号不可为空");
            String certNo = "";
            switch (cell.getCellType()) {
                case STRING:
                    certNo = cell.getStringCellValue();
                    break;
                case NUMERIC:
                    certNo = cell.getNumericCellValue() + "";
                    break;
            }
            if (StringUtils.isEmpty(certNo)) NotifyUtils.showErrorMsg("附证编号不可为空");
            EnterpriseCertificate certificate = eruptDao.queryEntity(EnterpriseCertificate.class, "certificate_no=" + SqlUtils.wrapStr(certNo));
            AssertUtils.isNull(certificate, "附证编号已存在 -> " + certNo);
        }
    }

    @Override
    public void afterExcelImport(Object workbook, List list) {
        list.forEach(obj -> {
            JsonObject vo = (JsonObject) obj;
            String orgCode = vo.get("company").getAsString();
            Enterprise enterprise = eruptDao.queryEntity(Enterprise.class, "org_code=" + SqlUtils.wrapStr(orgCode));
            AssertUtils.notNull(enterprise, "没有这个企业 -> " + orgCode);
            String updateSql = "update tb_enterprise_certificate set enterprise_id=" + enterprise.getId() + " where certificate_no=" + SqlUtils.wrapStr(vo.get("certificateNo").getAsString());
            EruptDaoUtils.updateNoForeignKeyChecks(updateSql);
        });
    }

    @Override
    public void excelExport(Object workbook) {
        Workbook wb = (Workbook) workbook;
        Sheet sheet = wb.getSheetAt(0);
        Row title = sheet.getRow(0);
        int index = -1;
        for (int i = title.getFirstCellNum(); i <= title.getLastCellNum(); i++) {
            Cell cell = title.getCell(i);
            if (cell.getStringCellValue().equals("状态")) {
                index = i;
                break;
            }
        }
        if (index < 0) return;
        for (int i = 1; i <= sheet.getLastRowNum(); i++) {
            Row row = sheet.getRow(i);
            Cell cell = row.getCell(index);
            String value = cell.getStringCellValue();
            value = value.replace("<span class='e-tag'>", "");
            value = value.replace("</font></span>", "");
            StringTokenizer st = new StringTokenizer(value, "'>");
            st.nextToken();
            st.nextToken();
            value = st.nextToken();
            cell.setCellValue(value);
        }
    }
    //    @Override
//    public void afterFetch(Collection<Map<String, Object>> list) {
//
//        Calendar calendar=Calendar.getInstance();
//        List<EnterpriseCertificate> enterpriseCertificates = EruptDaoUtils.convert(list, EnterpriseCertificate.class);
//
//        for (EnterpriseCertificate enterpriseCertificate : enterpriseCertificates) {
//             if (ObjectUtils.isEmpty(enterpriseCertificate.getId())) {
//                 break;
//             }
//            //若当前时间过了评估时间的三年，则已逾期；若当前时间距离评估时间还有一个月，则即将逾期；其他为正常状态
//            Date expirationTime = enterpriseCertificate.getExpirationTime();
//            Date now = new Date(System.currentTimeMillis());
//            //找到评估时间的30天后
//            calendar.setTime(expirationTime);
//            calendar.add(Calendar.DATE,-90);
//            java.util.Date beforeMonth = calendar.getTime();
//
//
//            //预案状态:正常、即将到期、已逾期
//            if(now.after(expirationTime)){
//                //已逾期
//                enterpriseCertificate.setState(TplUtils.addColor("已逾期", "red"));
//            }else {
//
//                //到期前90天为即将逾期，否则为正常
//                if(now.before(beforeMonth)){
//                    enterpriseCertificate.setState(TplUtils.addColor("正常", "green"));
//                }else{
//                    enterpriseCertificate.setState(TplUtils.addColor("即将逾期", "blue"));
//                }
//
//            }
//
//            EruptDaoUtils.updateAfterFetch(list, enterpriseCertificate.getId(), "state", enterpriseCertificate.getState());
//        }
//
//    }

//    @Override
//    public void beforeAdd(EnterpriseCertificate enterpriseCertificate) {
//        Calendar calendar=Calendar.getInstance();
//        Date expirationTime = enterpriseCertificate.getExpirationTime();
//        Date now = new Date(System.currentTimeMillis());
//        //找到评估时间的30天后
//        calendar.setTime(expirationTime);
//        calendar.add(Calendar.DATE,-90);
//        java.util.Date beforeMonth = calendar.getTime();
//
//        //预案状态:正常、即将到期、已逾期
//        if (isSameDay(now,expirationTime)) {
//            enterpriseCertificate.setState("即将逾期");
//        }
//
//        else if(now.after(expirationTime)){
//            //已逾期
//            enterpriseCertificate.setState("已逾期");
//        }else {
//
//            //到期前90天为即将逾期，否则为正常
//            if(now.before(beforeMonth)){
//                enterpriseCertificate.setState("正常");
//            }else{
//                enterpriseCertificate.setState("即将逾期");
//            }
//
//        }
//    }
//
//    public static boolean isSameDay(Date date1, Date date2) {
//        SimpleDateFormat fmt = new SimpleDateFormat("yyyyMMdd");
//        return fmt.format(date1).equals(fmt.format(date2));
//    }

}
