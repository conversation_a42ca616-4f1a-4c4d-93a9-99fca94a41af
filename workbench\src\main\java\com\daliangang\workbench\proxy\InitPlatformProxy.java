/*
 * Copyright © 2020-2035 yundingyun All rights reserved.
 */
package com.daliangang.workbench.proxy;

import com.daliangang.training.controller.RedisProducer;
import com.daliangang.workbench.entity.Enterprise;
import com.daliangang.workbench.init.InitTemplate;
import com.daliangang.workbench.init.InitTemplateConvertHandler;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import xyz.erupt.annotation.fun.VLModel;
import xyz.erupt.core.util.EruptSpringUtil;
import xyz.erupt.excel.controller.EruptExcelController;
import xyz.erupt.mns.msg.entity.MessageNotify;
import xyz.erupt.mns.todo.entity.TodoNotify;
import xyz.erupt.toolkit.db.EruptResultMap;
import xyz.erupt.toolkit.handler.RemoteEntityChoiceFetchHandler;
import xyz.erupt.toolkit.utils.CollectionUtils;
import xyz.erupt.toolkit.utils.EruptDaoUtils;
import xyz.erupt.toolkit.utils.FileUtils;
import xyz.erupt.toolkit.utils.SqlUtils;
import xyz.erupt.upms.platform.PlatformProxy;

import javax.annotation.Resource;
import javax.transaction.Transactional;
import java.io.InputStream;
import java.util.List;

@Service
@Slf4j
public class InitPlatformProxy implements PlatformProxy {

    @Resource
    private InitTemplateConvertHandler initTemplateConvertHandler;

    @Resource
    private EruptExcelController eruptExcelController;



    @Override
    @Transactional
    public void init(String[] param) {
        int count = EruptDaoUtils.selectMap("select count(*) as count from tb_department").getInt("count");
        //AssertUtils.isTrue(count <= 0, "已初始化完成，请不要重复初始化");
        //清空相关表
        List<EruptResultMap> resultMaps = EruptDaoUtils.selectOnes("select account from e_upms_role_template_user", EruptResultMap.class);
        String deleteUserSql = resultMaps.isEmpty() ? "" : ("delete from e_upms_user where account" + SqlUtils.wrapIn(CollectionUtils.getSubList(resultMaps, "account")));
        String[] truncates = {
                "TRUNCATE e_upms_role;",
                "TRUNCATE e_upms_role_menu;",
                "TRUNCATE e_upms_role_dept;",
                "TRUNCATE e_upms_role_template;",
                "TRUNCATE e_upms_role_template_user;",
                "TRUNCATE e_upms_org;",
                "TRUNCATE e_upms_post;",
                "TRUNCATE e_upms_post_template;",
                "TRUNCATE e_upms_user_role;",
                "TRUNCATE e_upms_template_dept;",
                "TRUNCATE tb_port_area",
                "TRUNCATE tb_department;",
                "TRUNCATE tb_enterprise;",
                "TRUNCATE tb_employee_information;",
                "delete from e_upms_user where id>1;",
                deleteUserSql};
        EruptDaoUtils.updateNoForeignKeyChecks(truncates);

        //初始化脚本
        EruptDaoUtils.executeSql("public/init/init-model-design.sql");
        EruptDaoUtils.executeSql("public/init/init-bi.sql");

        //因为数据远程访问与事务原因，港区数据需要特殊处理
        EruptDaoUtils.executeSql("public/init/init-port-area.sql");
        List<VLModel> portAreas = EruptSpringUtil.getBean(RemoteEntityChoiceFetchHandler.class).fetch(new String[]{"device", "PortArea", "id,portarea_name"});
        log.info("港区数据初始化 -> " + portAreas);

        //加载初始化信息excel
        InitTemplate initTemplate = this.readInitTemplate(FileUtils.readInputStream("public/init/init-template.xlsx"), 0);
        if (initTemplate.getWorkbook().getNumberOfSheets() > 1) {
            for (int i = 1; i < initTemplate.getWorkbook().getNumberOfSheets(); i++) {
                this.readInitTemplate(FileUtils.readInputStream("public/init/init-template.xlsx"), i);
            }
        }

        //刷企业数据同时刷新数据权限范围
        eruptExcelController.importExcel(Enterprise.class, "public/init/01-01-企业列表.xls");

        //工作台数据
        eruptExcelController.importExcel(MessageNotify.class, "public/init/01-02-消息提醒.xls");
        eruptExcelController.importExcel(TodoNotify.class, "public/init/01-03-待办设置.xls");




    }

    @Override
    public int sort() {
        return 1;
    }

    @Transactional
    InitTemplate readInitTemplate(InputStream inputStream, int index) {
        InitTemplate initTemplate = new InitTemplate();
        initTemplate.setFilename("public/init-template.xlsx");
        initTemplate.setBindClass(InitTemplate.class);
        initTemplate.loadAt(inputStream, index);
        initTemplate.readObjects();
        initTemplateConvertHandler.convert(initTemplate.getObjects(), initTemplate);
        return initTemplate;
    }
}
