/*
 * Copyright © 2020-2035 yundingyun All rights reserved.
 */
package com.daliangang.rndpub.operation;

import com.daliangang.rndpub.entity.InspectionResultsView;
import com.daliangang.rndpub.entity.Rectification;
import com.daliangang.rndpub.entity.Rectify;
import com.daliangang.rndpub.form.RectificationForm;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import xyz.erupt.annotation.fun.OperationHandler;
import xyz.erupt.core.annotation.EruptRouter;
import xyz.erupt.jpa.dao.EruptDao;
import xyz.erupt.toolkit.utils.EruptDaoUtils;
import xyz.erupt.toolkit.utils.NotifyUtils;

import javax.annotation.Resource;
import javax.transaction.Transactional;
import java.sql.Date;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

@Service
public class RectificationRectificationHandler implements OperationHandler<Rectification, RectificationForm> {

    @RestController
    @Transactional
    public static class RectificationRectificationController {
        @Resource
        private EruptDao eruptDao;

        @RequestMapping("erupt-api/data/RectificationForm/{id}")
        @EruptRouter(verifyType = EruptRouter.VerifyType.LOGIN)
        public Map<String, Object> getById(@PathVariable("id") Long id) {
            InspectionResultsView inspectionResultsView = eruptDao.queryEntity(InspectionResultsView.class, "id = " + id);
            Map<String, Object> map = EruptDaoUtils.castMap(inspectionResultsView);
            map.put("description","");//整改说明 置空
            map.put("supportingMaterials","");//整改证明材料 置空
            map.put("rectificationTime", new Date(new java.util.Date().getTime()));

            return map;
        }

    }

    @Resource
    private EruptDao eruptDao;
    @Transactional
    @Override
    public String exec(List<Rectification> data, RectificationForm form, String[] param) {

        Rectify rectify = new Rectify();
        rectify.setInspectionResultsId(form.getId());//整改id
        rectify.setRectificationTime(form.getRectificationTime());//整改时间
        rectify.setDescription(form.getDescription());//整改说明
        rectify.setSupportingMaterials(form.getSupportingMaterials());//整改证明材料
        rectify.setCreateTime(LocalDateTime.now());
        eruptDao.persist(rectify);


        InspectionResultsView inspectionResultsView = eruptDao.queryEntity(InspectionResultsView.class, " id = " + form.getId());
        inspectionResultsView.setRectificationStatus("Rectified");//整改状态 为已整改
        inspectionResultsView.setRectificationAuditStatus("NOT_Audited");//审核状态为待审核
        inspectionResultsView.setRectificationTime(form.getRectificationTime());// 重置整改时间
        inspectionResultsView.setDescription(form.getDescription());//整改说明
        inspectionResultsView.setSupportingMaterials(form.getSupportingMaterials());//整改证明材料
        eruptDao.merge(inspectionResultsView);

        return NotifyUtils.getSuccessNotify("提交成功");
    }
}
