<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>xyz.erupt</groupId>
        <artifactId>erupt</artifactId>
        <version>********</version>
        <!--        <relativePath>../pom.xml</relativePath>-->
    </parent>

    <groupId>com.daliangang</groupId>
    <artifactId>daliangang-workbench</artifactId>
    <version>${project.parent.version}</version>
    <name>daliangang-workbench</name>
    <description>daliangang-workbench</description>
    <properties>
        <java.version>1.8</java.version>
    </properties>
    <dependencies>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
        </dependency>
        <dependency>
            <groupId>xyz.erupt</groupId>
            <artifactId>erupt-upms</artifactId>
            <version>${project.parent.version}</version>
        </dependency>
        <dependency>
            <groupId>xyz.erupt</groupId>
            <artifactId>erupt-security</artifactId>
            <version>${project.parent.version}</version>
        </dependency>
        <dependency>
            <groupId>xyz.erupt</groupId>
            <artifactId>erupt-cloud-server</artifactId>
            <version>${project.parent.version}</version>
        </dependency>
        <dependency>
            <groupId>xyz.erupt</groupId>
            <artifactId>erupt-web</artifactId>
            <version>${project.parent.version}</version>
        </dependency>
        <!--        <dependency>-->
        <!--            <groupId>xyz.erupt</groupId>-->
        <!--            <artifactId>erupt-upload-cos</artifactId>-->
        <!--            <version>${project.parent.version}</version>-->
        <!--        </dependency>-->

        <dependency>
            <groupId>xyz.erupt</groupId>
            <artifactId>erupt-upload-minio</artifactId>
            <version>${project.parent.version}</version>
        </dependency>

        <dependency>
            <groupId>xyz.erupt</groupId>
            <artifactId>erupt-dynamic-field</artifactId>
            <version>${project.parent.version}</version>
        </dependency>
        <dependency>
            <groupId>xyz.erupt</groupId>
            <artifactId>erupt-devtools</artifactId>
            <version>${project.parent.version}</version>
        </dependency>
        <dependency>
            <groupId>io.github.classgraph</groupId>
            <artifactId>classgraph</artifactId>
            <version>4.8.137</version>
        </dependency>


        <dependency>
            <groupId>xyz.erupt</groupId>
            <artifactId>erupt-designer-entity</artifactId>
            <version>${project.parent.version}</version>
        </dependency>
        <dependency>
            <groupId>xyz.erupt</groupId>
            <artifactId>erupt-cms</artifactId>
            <version>${project.parent.version}</version>
        </dependency>
        <dependency>
            <groupId>io.minio</groupId>
            <artifactId>minio</artifactId>
            <version>8.3.3</version>
            <exclusions>
                <exclusion>
                    <artifactId>okhttp</artifactId>
                    <groupId>com.squareup.okhttp3</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>xyz.erupt</groupId>
            <artifactId>erupt-mns</artifactId>
            <version>${project.parent.version}</version>
        </dependency>
        <dependency>
            <groupId>xyz.erupt</groupId>
            <artifactId>erupt-sms</artifactId>
            <version>${project.parent.version}</version>
        </dependency>
        <dependency>
            <groupId>xyz.erupt</groupId>
            <artifactId>erupt-hwSms</artifactId>
            <version>${project.parent.version}</version>
        </dependency>
        <dependency>
            <groupId>xyz.erupt</groupId>
            <artifactId>erupt-job</artifactId>
            <version>${project.parent.version}</version>
        </dependency>
        <dependency>
            <groupId>xyz.erupt</groupId>
            <artifactId>erupt-openapi</artifactId>
            <version>${project.parent.version}</version>
        </dependency>
        <dependency>
            <groupId>xyz.erupt</groupId>
            <artifactId>erupt-tpl</artifactId>
            <version>${project.parent.version}</version>
        </dependency>
        <dependency>
            <groupId>xyz.erupt</groupId>
            <artifactId>erupt-tpl-ui.avue</artifactId>
            <version>${project.parent.version}</version>
        </dependency>
        <!--        <dependency>-->
        <!--            <groupId>xyz.erupt</groupId>-->
        <!--            <artifactId>erupt-flow</artifactId>-->
        <!--            <version>${project.parent.version}</version>-->
        <!--        </dependency>-->
        <dependency>
            <groupId>xyz.erupt</groupId>
            <artifactId>erupt-rider</artifactId>
            <version>1.11.7.5-daliangang</version>
        </dependency>
        <dependency>
            <groupId>xyz.erupt</groupId>
            <artifactId>erupt-bi</artifactId>
            <version>${project.parent.version}</version>
        </dependency>
        <dependency>
            <groupId>com.daliangang</groupId>
            <artifactId>daliangang-site</artifactId>
            <version>1.4.78</version>
        </dependency>
        <!-- 1.2是使用最广泛的版本 -->
        <dependency>
            <groupId>javax.interceptor</groupId>
            <artifactId>javax.interceptor-api</artifactId>
            <version>1.2</version>
        </dependency>
        <dependency>
            <groupId>de.codecentric</groupId>
            <artifactId>spring-boot-admin-starter-server</artifactId>
            <version>${spring.boot.version}</version>
        </dependency>
        <dependency>
            <groupId>de.codecentric</groupId>
            <artifactId>spring-boot-admin-starter-client</artifactId>
            <version>${spring.boot.version}</version>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-configuration-processor</artifactId>
            <optional>true</optional>
        </dependency>
        <dependency>
            <groupId>org.apache.logging.log4j</groupId>
            <artifactId>log4j-api</artifactId>
            <version>2.21.1</version>
        </dependency>
        <dependency>
            <groupId>org.apache.logging.log4j</groupId>
            <artifactId>log4j-core</artifactId>
            <version>2.21.1</version>
        </dependency>
        <!--        不能加这个，会导致word导出报错-->
        <!--        <dependency>-->
        <!--            <groupId>org.apache.xmlbeans</groupId>-->
        <!--            <artifactId>xmlbeans</artifactId>-->
        <!--            <version>3.1.0</version>-->
        <!--        </dependency>-->

<!--        <dependency>-->
<!--            <groupId>com.dlywxt.external.sso.sdk</groupId>-->
<!--            <artifactId>external-sso2sdk</artifactId>-->
<!--            <version>1.0.2</version>-->
<!--            <scope>system</scope>-->
<!--            <systemPath>${project.basedir}/external-sso2sdk-1.0.2-JDK1.8.jar</systemPath>-->
<!--        </dependency>-->
        <dependency>
            <groupId>com.dlywxt.external.sso.sdk</groupId>
            <artifactId>external-sso2sdk</artifactId>
            <version>1.0.2</version>
        </dependency>

        <dependency>
            <groupId>com.dlywxt.external.sso.sdk</groupId>
            <artifactId>external-sso2sdk</artifactId>
            <version>1.0.2</version>
        </dependency>
        <dependency>
            <groupId>org.apache.oltu.oauth2</groupId>
            <artifactId>org.apache.oltu.oauth2.client</artifactId>
            <version>1.0.2</version>
        </dependency>

        <dependency>
            <groupId>io.jsonwebtoken</groupId>
            <artifactId>jjwt</artifactId>
            <version>0.6.0</version>
        </dependency>
        <!--        <dependency>-->
        <!--            <groupId>com.dlywxt.external.sso.sdk</groupId>-->
        <!--            <artifactId>external-sso2sdk</artifactId>-->
        <!--            <version>1.0-SNAPSHOT</version>-->
        <!--            <scope>system</scope>-->
        <!--            <systemPath>${project.basedir}/external-sso2sdk-1.0-SNAPSHOT.jar</systemPath>-->
        <!--        </dependency>-->

        <!--        <dependency>-->
        <!--            <groupId>org.bouncycastle</groupId>-->
        <!--            <artifactId>bcprov-jdk15to18</artifactId>-->
        <!--            <version>${bcprov.version}</version>-->
        <!--        </dependency>-->

        <!-- https://mvnrepository.com/artifact/org.bouncycastle/bcprov-jdk18on -->
        <dependency>
            <groupId>org.bouncycastle</groupId>
            <artifactId>bcprov-jdk18on</artifactId>
            <version>1.77</version>
        </dependency>

    </dependencies>

    <build>

<!--        <resources>-->
<!--            <resource>-->
<!--                <directory>${project.basedir}/lib</directory>-->
<!--                <includes>-->
<!--                    <include>*.jar</include>-->
<!--                </includes>-->
<!--            </resource>-->
<!--        </resources>-->
        <directory>${project.basedir}/../target/${project.artifactId}</directory>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-jar-plugin</artifactId>
                <version>2.3.2</version>
                <executions>
                    <execution>
                        <phase>package</phase>
                        <goals>
                            <goal>jar</goal>
                        </goals>
                        <configuration>
                            <finalName>${project.artifactId}</finalName>
                        </configuration>
                    </execution>
                </executions>
                <configuration>
                    <finalName>application</finalName>
                    <archive>
                        <!-- 生成的jar中，不要包含pom.xml和pom.properties这两个文件 -->
                        <addMavenDescriptor>false</addMavenDescriptor>
                        <manifest>
                            <addClasspath>true</addClasspath>
                            <classpathPrefix>lib/</classpathPrefix>
                            <mainClass>com.daliangang.Application</mainClass>
                        </manifest>
                    </archive>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-dependency-plugin</artifactId>
                <executions>
                    <execution>
                        <id>copy-dependencies</id>
                        <phase>package</phase>
                        <goals>
                            <goal>copy-dependencies</goal>
                        </goals>
                        <configuration>
                            <!-- lib依赖包输出目录，打包的时候不打进jar包里 -->
                            <outputDirectory>${project.build.directory}/lib</outputDirectory>
                            <excludeTransitive>false</excludeTransitive>
                            <stripVersion>false</stripVersion>
                            <includeScope>runtime</includeScope>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
            <!-- 压缩jar包，打出来的jar中没有了lib文件夹 -->
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <configuration>
                    <layout>ZIP</layout>
                    <includes>
                        <include>
                            <groupId>nothing</groupId>
                            <artifactId>nothing</artifactId>
                        </include>
                    </includes>
                </configuration>
            </plugin>
            <!-- screws 生成数据库文档  -->
            <!--            <plugin>-->
            <!--                <groupId>cn.smallbun.screw</groupId>-->
            <!--                <artifactId>screw-maven-plugin</artifactId>-->
            <!--                <version>1.0.5</version>-->
            <!--                <dependencies>-->
            <!--                    &lt;!&ndash; 数据库连接 &ndash;&gt;-->
            <!--                    <dependency>-->
            <!--                        <groupId>com.zaxxer</groupId>-->
            <!--                        <artifactId>HikariCP</artifactId>-->
            <!--                        <version>3.4.5</version>-->
            <!--                    </dependency>-->
            <!--                    <dependency>-->
            <!--                        <groupId>mysql</groupId>-->
            <!--                        <artifactId>mysql-connector-java</artifactId>-->
            <!--                        <version>8.0.22</version>-->
            <!--                    </dependency>-->
            <!--                </dependencies>-->
            <!--                <configuration>-->
            <!--                    &lt;!&ndash; 数据库相关配置 &ndash;&gt;-->
            <!--                    <driverClassName>com.mysql.cj.jdbc.Driver</driverClassName>-->
            <!--                    <jdbcUrl>****************************</jdbcUrl>-->
            <!--                    <username>root</username>-->
            <!--                    <password>1234@abcD</password>-->
            <!--                    &lt;!&ndash; screw 配置 &ndash;&gt;-->
            <!--                    <fileType>HTML</fileType>-->
            <!--                    <title>erupt数据库文档</title> &lt;!&ndash;标题&ndash;&gt;-->
            <!--                    <fileName>erupt-db</fileName> &lt;!&ndash;文档名称 为空时:将采用[数据库名称-描述-版本号]作为文档名称&ndash;&gt;-->
            <!--                    <description>数据库文档</description> &lt;!&ndash;描述&ndash;&gt;-->
            <!--                    <version>${project.version}</version> &lt;!&ndash;版本&ndash;&gt;-->
            <!--                    <openOutputDir>false</openOutputDir> &lt;!&ndash;打开文件输出目录&ndash;&gt;-->
            <!--                    <produceType>freemarker</produceType> &lt;!&ndash;生成模板&ndash;&gt;-->
            <!--                </configuration>-->
            <!--                <executions>-->
            <!--                    <execution>-->
            <!--                        <phase>compile</phase>-->
            <!--                        <goals>-->
            <!--                            <goal>run</goal>-->
            <!--                        </goals>-->
            <!--                    </execution>-->
            <!--                </executions>-->
            <!--            </plugin>-->
        </plugins>
    </build>

    <repositories>
        <repository>
            <id>releases</id>
            <url>http://svc.yundingyun.net:8081/repository/maven-releases/</url>
            <releases>
                <enabled>true</enabled>
                <updatePolicy>always</updatePolicy>
            </releases>
            <snapshots>
                <enabled>false</enabled>
            </snapshots>
        </repository>
    </repositories>


</project>
