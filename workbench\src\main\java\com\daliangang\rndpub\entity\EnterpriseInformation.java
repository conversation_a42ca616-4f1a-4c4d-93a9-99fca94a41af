/*
 * Copyright © 2020-2035 yundingyun All rights reserved.
 */
package com.daliangang.rndpub.entity;

import com.daliangang.rndpub.operation.EnterpriseInformationManualSelectionHandler;
import com.daliangang.rndpub.operation.EnterpriseInformationSystemExtractionHandler;
import com.daliangang.rndpub.proxy.EnterpriseInformationDataProxy;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import xyz.erupt.annotation.Erupt;
import xyz.erupt.annotation.EruptField;
import xyz.erupt.annotation.sub_erupt.PageModel;
import xyz.erupt.annotation.sub_erupt.Power;
import xyz.erupt.annotation.sub_erupt.RowOperation;
import xyz.erupt.annotation.sub_field.Edit;
import xyz.erupt.annotation.sub_field.EditType;
import xyz.erupt.annotation.sub_field.View;
import xyz.erupt.annotation.sub_field.sub_edit.*;
import xyz.erupt.toolkit.db.Comment;
import xyz.erupt.toolkit.handler.EruptDictCodeChoiceFetchHandler;
import xyz.erupt.upms.model.auth.DataAuthModel;

import javax.persistence.Entity;
import javax.persistence.ManyToOne;
import javax.persistence.Table;

@Erupt(name = "检查对象", power = @Power(add = false, delete = false, export = false, importable = true, viewDetails = false, edit = false),selectionVisible = false
        , dataProxy = EnterpriseInformationDataProxy.class//, orderBy = "state desc, enterprise_id asc"
        , pageModel = @PageModel(flag = true,pageProxy = EnterpriseInformationDataProxy.class)
        , rowOperation = {
        @RowOperation(title = "自动抽取", icon = "fa fa-chain-broken", eruptClass = EnterpriseInformationSystemExtractionHandler.DrawEnterpriseForm.class, operationHandler = EnterpriseInformationSystemExtractionHandler.class, mode = RowOperation.Mode.BUTTON),
        @RowOperation(confirm = false, title = "抽取企业", ifExpr = "item.state=='未抽取'", icon = "fa fa-hand-o-up", operationHandler = EnterpriseInformationManualSelectionHandler.class, operationParam = "true", mode = RowOperation.Mode.MULTI),
        @RowOperation(confirm = false, title = "放弃抽取", ifExpr = "item.state=='已抽取'", icon = "fa fa-hand-o-down", operationHandler = EnterpriseInformationManualSelectionHandler.class, operationParam = "false", mode = RowOperation.Mode.MULTI),
        @RowOperation(confirm = false,type = RowOperation.Type.CLOSE,title = "确认", mode = RowOperation.Mode.BUTTON)
})
@Table(name = "tb_enterprise_information")
@Entity
@Getter
@Setter
@Comment("检查对象")
@ApiModel("检查对象")
public class EnterpriseInformation extends DataAuthModel {

    @ManyToOne
    private Procedure procedure;

    @EruptField(
            views = @View(title = "状态", show = true, sortable = true),
            edit = @Edit(title = "状态", type = EditType.BOOLEAN, notNull = true, show = false, search = @Search,
                    boolType = @BoolType(trueText = "已抽取", falseText = "未抽取")))
    @Comment("状态")
    @ApiModelProperty("状态")
    private Boolean state;

    @EruptField(
            views = @View(title = "企业ID", show = false),
            edit = @Edit(title = "企业ID", type = EditType.INPUT, notNull = true, show = true))
    @Comment("企业ID")
    @ApiModelProperty("企业ID")
    private String enterpriseId;

    @EruptField(
            views = @View(title = "企业名称", show = true),
            edit = @Edit(title = "企业名称", type = EditType.INPUT, notNull = true, inputType = @InputType))
    @Comment("企业名称")
    @ApiModelProperty("企业名称")
    private String enterpriseName;

    @EruptField(
            views = @View(title = "法定代表人", show = false),
            edit = @Edit(title = "法定代表人", type = EditType.INPUT, notNull = true,
                    inputType = @InputType))
    @Comment("法定代表人")
    @ApiModelProperty("法定代表人")
    private String legalRepresentative;

    @EruptField(
            views = @View(title = "主管部门", show = false),
            edit = @Edit(title = "主管部门", type = EditType.INPUT, notNull = true,
                    inputType = @InputType))
    @Comment("主管部门")
    @ApiModelProperty("主管部门")
    private String competentDepartment;

    @EruptField(
            views = @View(title = "企业类别", show = true),
//            edit = @Edit(title = "企业类别", type = EditType.INPUT, notNull = true)
            edit = @Edit(title = "企业类别", type = EditType.CHOICE,  notNull = true,
                    choiceType = @ChoiceType(fetchHandler = EruptDictCodeChoiceFetchHandler.class, fetchHandlerParams = "type"))
    )
    @Comment("企业类别")
    @ApiModelProperty("企业类别")
    private String type;

    @EruptField(
            views = @View(title = "所属港区", show = false),
            edit = @Edit(title = "所属港区", type = EditType.INPUT, notNull = true))
    @Comment("所属港区")
    @ApiModelProperty("所属港区")
    private String portArea;

    @EruptField(
            views = @View(title = "上次抽查时间", show = true),
            edit = @Edit(title = "上次抽查时间", type = EditType.DATE, notNull = true,
                    dateType = @DateType))
    @Comment("上次抽查时间")
    @ApiModelProperty("上次抽查时间")
    private java.util.Date lastSpotCheck;

    @EruptField(
            views = @View(title = "本次抽查时间", show = false),
            edit = @Edit(title = "本次抽查时间", type = EditType.DATE, notNull = true,
                    dateType = @DateType))
    @Comment("本次抽查时间")
    @ApiModelProperty("本次抽查时间")
    private java.util.Date currentSpotCheck;

}
