package com.daliangang.majorisk.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import xyz.erupt.annotation.Erupt;
import xyz.erupt.annotation.EruptField;
import xyz.erupt.annotation.sub_field.Edit;
import xyz.erupt.annotation.sub_field.EditType;
import xyz.erupt.annotation.sub_field.View;
import xyz.erupt.annotation.sub_field.sub_edit.InputType;
import xyz.erupt.jpa.model.BaseModel;
import xyz.erupt.toolkit.db.Comment;
import javax.persistence.Entity;
import javax.persistence.Table;

/**
 * <AUTHOR>
 * @date 2023/6/30
 */
@Erupt(name = "储存情况")
@Table(name = "tb_memory")
@Entity
@Getter
@Setter
@Comment("储存情况")
@ApiModel("储存情况")
public class Memory extends BaseModel {

    @EruptField(
            views = @View(title = "现存货种"),
            edit = @Edit(
                    title = "现存货种",
                    type = EditType.INPUT,
                    notNull = true
            )
    )
    @Comment("现存货种")
    @ApiModelProperty("现存货种")
    private String yardType;

    @EruptField(
            views = @View(title = "当前存储量"),
            edit = @Edit(title = "当前存储量", type = EditType.INPUT,
                    inputType = @InputType))
    @Comment("当前存储量")
    @ApiModelProperty("当前存储量")
    private String yardNow;

}
