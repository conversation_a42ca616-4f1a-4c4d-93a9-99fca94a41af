package com.daliangang.datascreen.response;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Author: chongmenglin
 * @Date: 2024/12/9 17:40
 */
@Data
@Builder(toBuilder = true)
@NoArgsConstructor
@AllArgsConstructor
public class WorkCondition {
    private Integer shipNumber;
    private Integer carNumber;
    private Integer maintenanceNumber;
    private String maintenance;
    private String carShipArea;
    private String shipArea;
    private String carArea;
    private String maintenanceArea;
    private String shipOrgan;
    private String carOrgan;
    private String maintenanceOrgan;
    private String maintenanceCondition;
    private String shipGoodsType;
    private String carGoodsType;
    private String strengthenSupervisionArea;
    private String keyInspectionContent;
}
