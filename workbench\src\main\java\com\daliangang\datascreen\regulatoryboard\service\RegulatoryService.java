package com.daliangang.datascreen.regulatoryboard.service;

import com.daliangang.core.DaliangangContext;
import com.daliangang.datascreen.annotation.TokenCheck;
import com.daliangang.datascreen.regulatoryboard.response.SecurityDetail;
import com.daliangang.datascreen.regulatoryboard.response.UnreportedCompanyResponse;
import com.daliangang.datascreen.response.AmountCountResponse;
import com.daliangang.datascreen.response.StatisticsGroup;
import com.daliangang.datascreen.riskboard.service.MenuService;
import com.daliangang.datascreen.utils.ClassNameUtil;
import com.daliangang.datascreen.utils.DateUtil;
import com.daliangang.datascreen.utils.OrgUtils;
import com.daliangang.emergency.entity.Drill;
import com.daliangang.rndpub.entity.InspectionResultsView;
import com.daliangang.rndpub.entity.RectificationManage;
import com.daliangang.safedaily.entity.*;
import com.daliangang.workbench.entity.EmployeeCertificateView;
import com.daliangang.workbench.entity.Enterprise;
import com.daliangang.workbench.entity.EnterpriseCertificateView;
import org.springframework.stereotype.Service;
import xyz.erupt.core.context.MetaContext;
import xyz.erupt.jpa.dao.EruptDao;
import xyz.erupt.upms.model.EruptMenu;
import xyz.erupt.upms.service.EruptUserService;

import javax.annotation.Resource;
import javax.persistence.EntityManager;
import javax.servlet.http.HttpServletRequest;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.ZoneId;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.locks.Condition;
import java.util.concurrent.locks.ReentrantLock;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.daliangang.datascreen.utils.OrgUtils.*;

/**
 * @Author: chongmenglin
 * @Date: 2024/11/13 13:56
 */

@Service
@TokenCheck
public class RegulatoryService {
    @Resource
    private EruptDao eruptDao;
    @Resource
    private MenuService menuService;
    @Resource
    private EruptUserService eruptUserService;
    @Resource
    private HttpServletRequest request;
    private static final long THIRTY_DAYS_IN_MILLIS = 30L * 24 * 60 * 60 * 1000;
    private static final int THREE_YEARS_IN_DAYS = 3 * 365;

    /**
     * 获取当日安全承诺统计
     *
     * @param orgCode
     * @return
     */
    public List<AmountCountResponse> getSecurityCommitment(String orgCode) {
        List<String> orgCodes = OrgUtils.getCodes(orgCode);
        List<AmountCountResponse> result = new ArrayList<>();
        List<AmountCountResponse.amountContent> amountContents = new ArrayList<>();
        int isReport = 0;
        for (String code : orgCodes) {
            // 查询条件
            String queryCondition = "orgCode = :orgCode and DATE(fillingDate) = :currentDate";
            // 执行查询
            List<Release> releases = eruptDao.queryEntityList(
                    Release.class,
                    queryCondition,
                    new HashMap<String, Object>() {{
                        put("orgCode", code);
                        put("currentDate", DateUtil.getCurrentDayDate());
                    }}
            );
            isReport += releases.size();
        }

        amountContents.add(
                AmountCountResponse.amountContent.builder()
                        .title("已承诺")
                        .count(String.valueOf(isReport))
                        .unit("家").build()
        );
        amountContents.add(
                AmountCountResponse.amountContent.builder()
                        .title("未承诺")
                        .count(String.valueOf(orgCodes.size() - isReport))
                        .unit("家").build()
        );
        result.add(AmountCountResponse.builder()
                .theme("当日安全承诺统计")
                .content(amountContents).build());
        return result;
    }

    /**
     * 获取未承诺的组织代码和公司名称
     *
     * @param orgCode
     * @return 未承诺的组织代码和公司名称列表
     */
    public List<Map<String, String>> getUncommittedCompanies(String orgCode) {
        // 获取所有的 orgCodes
        List<String> orgCodes = OrgUtils.getCodes(orgCode);
        List<Map<String, String>> uncommittedCompanies = new ArrayList<>();

        // 查询数据库中已承诺的 orgCodes
        String queryCondition = "orgCode = :orgCode and DATE(fillingDate) = :currentDate";
        List<String> committedOrgCodes = new ArrayList<>();

        for (String code : orgCodes) {
            List<Release> releases = eruptDao.queryEntityList(
                    Release.class,
                    queryCondition,
                    new HashMap<String, Object>() {{
                        put("orgCode", code);
                        put("currentDate", DateUtil.getCurrentDayDate());
                    }}
            );

            if (!releases.isEmpty()) {
                committedOrgCodes.add(code);
            }
        }

        // 获取未承诺的 orgCodes
        List<String> uncommittedOrgCodes = new ArrayList<>(orgCodes);
        uncommittedOrgCodes.removeAll(committedOrgCodes);

        // 查询未承诺的公司名称
        for (String uncommittedOrgCode : uncommittedOrgCodes) {
            String companyName = getCompanyName(uncommittedOrgCode); // 使用您已有的 getCompanyName 方法
            Map<String, String> companyInfo = new HashMap<>();
            companyInfo.put("orgCode", uncommittedOrgCode);
            companyInfo.put("companyName", companyName);
            uncommittedCompanies.add(companyInfo);
        }

        return uncommittedCompanies;
    }


    /**
     * 获取当日企业安全承诺
     *
     * @param orgCode
     * @return
     */
    public List<SecurityDetail> getSecurityDetail(String orgCode) {
        List<String> orgCodes = OrgUtils.getCodes(orgCode);
        List<SecurityDetail> result = new ArrayList<>();
        for (String code : orgCodes) {
            // 查询条件
            String queryCondition = "orgCode = :orgCode and DATE(fillingDate) = :currentDate";
            // 执行查询
            List<Release> releases = eruptDao.queryEntityList(
                    Release.class,
                    queryCondition,
                    new HashMap<String, Object>() {{
                        put("orgCode", code);
                        put("currentDate", DateUtil.getCurrentDayDate());
                    }}
            );
            if (!releases.isEmpty()) {
                // 定义日期格式，包含时分
                SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm");

                for (Release release : releases) {
                    String fillingDateStr = dateFormat.format(release.getFillingDate());
                    result.add(
                            SecurityDetail.builder()
                                    .companyName(getCompanyName(code))
                                    .status("已承诺")
                                    .fillingTime(fillingDateStr)
                                    .build()
                    );
                }
            } else {
                result.add(
                        SecurityDetail.builder()
                                .companyName(getCompanyName(code))
                                .status("未承诺")
                                .fillingTime("--")
                                .build()
                );
            }
        }
        return result;
    }

    /**
     * 获取报告统计
     *
     * @param orgCode
     * @return
     */
    // 缓存未上报企业信息
    private final ConcurrentHashMap<String, List<UnreportedCompanyResponse>> unreportedCache = new ConcurrentHashMap<>();
    private final ReentrantLock lock = new ReentrantLock();
    private final Condition reportCompleted = lock.newCondition(); // 条件变量
    private boolean isReportDone = false; // 标记 report/count 是否已完成
    public List<AmountCountResponse> getReportCount(String orgCode) {
        lock.lock(); // 加锁，确保线程安全
        try {
            // 重置状态，防止后续调用失败
            isReportDone = false;
            List<AmountCountResponse> result = new ArrayList<>();
            List<UnreportedCompanyResponse> unreportedMap = new ArrayList<>();
            List<Enterprise> enterprises = OrgUtils.getStartUpOrgCode();
            List<String> orgCodes = OrgUtils.getCodes(orgCode);
            //本年度安全责任制
            getSafetyResponsibility(result, orgCodes,enterprises,unreportedMap);
            //本月度可靠性报告单
            getReliabilityReport(result, orgCodes,enterprises,unreportedMap);
            //本周专家检查日
            getExpertInspection(result, orgCodes,enterprises,unreportedMap);
            //本月培训考试
            getTrainingExamination(result, orgCodes,enterprises,unreportedMap);
            //本日检查
            getCurrentInspection(result, orgCodes,enterprises,unreportedMap);
            //本周检查
            getWeekInspection(result, orgCodes,enterprises,unreportedMap);
            //本月检查
            getMonthInspection(result, orgCodes,enterprises,unreportedMap);
            //本季度应急演练日
            getEmergencyQuarter(result, orgCodes,enterprises,unreportedMap);
            // 更新缓存
            unreportedCache.put(orgCode == null ? "ALL" : orgCode, unreportedMap);
            isReportDone = true;
            reportCompleted.signalAll(); // 唤醒等待的线程
            return result;
        }finally {
            lock.unlock(); // 解锁
        }
    }

    //本季度应急演练日
    private void getEmergencyQuarter(List<AmountCountResponse> result, List<String> orgCodes, List<Enterprise> enterprises, List<UnreportedCompanyResponse> unreportedMap) {
        int isReport = 0;
        List<UnreportedCompanyResponse.CompanyDetail> companyDetails = new ArrayList<>();
        for (String code : orgCodes) {
            List<java.sql.Date> range = DateUtil.getCurrentQuarterRangeAsDate();
            // 查询条件
            String queryCondition = "orgCode = :orgCode and place BETWEEN :startOfQuarter AND :endOfQuarter";
            // 执行查询
            List<Drill> drills = eruptDao.queryEntityList(
                    Drill.class,
                    queryCondition,
                    new HashMap<String, Object>() {{
                        put("orgCode", code);
                        put("startOfQuarter", range.get(0));
                        put("endOfQuarter", range.get(1));
                    }}
            );
            if(drills.isEmpty()) {
                companyDetails.add(
                        UnreportedCompanyResponse.CompanyDetail.builder()
                                .orgCode(code)
                                .companyName(OrgUtils.getCompanyName(code, enterprises))
                                .build());
            }else
                isReport ++;
        }
        int finalIsReport = isReport;
        unreportedMap.add(
                UnreportedCompanyResponse.builder()
                        .title("本季度应急演练日")
                        .company(companyDetails)
                        .code("EMERGENCY_QUARTER_UNREPORT")
                        .build()
        );
        getStatistical("本季度应急演练日", result, orgCodes, finalIsReport);
    }

    //本月检查
    private void getMonthInspection(List<AmountCountResponse> result, List<String> orgCodes, List<Enterprise> enterprises, List<UnreportedCompanyResponse> unreportedMap) {
        int isReport = 0;
        List<UnreportedCompanyResponse.CompanyDetail> companyDetails = new ArrayList<>();
        for (String code : orgCodes) {
            // 查询条件
            String queryCondition = "orgCode = :orgCode AND reportMonth = :reportMonth and submitted = '1'";
            // 执行查询
            List<MonthlyScheduling> monthlyInspections = eruptDao.queryEntityList(
                    MonthlyScheduling.class,
                    queryCondition,
                    new HashMap<String, Object>() {{
                        put("orgCode", code);
                        put("reportMonth", DateUtil.getCurrentMonth());
                    }}
            );
            if(monthlyInspections.isEmpty()) {
                companyDetails.add(
                        UnreportedCompanyResponse.CompanyDetail.builder()
                                .orgCode(code)
                                .companyName(OrgUtils.getCompanyName(code, enterprises))
                                .build());
            }else
                isReport ++;
        }
        int finalIsReport = isReport;
        unreportedMap.add(
                UnreportedCompanyResponse.builder()
                        .title("本月检查")
                        .company(companyDetails)
                        .code("MONTHLY_INSPECTION_UNREPORT")
                        .build()
        );
        getStatistical("本月检查", result, orgCodes, finalIsReport);
    }

    //本周检查
    private void getWeekInspection(List<AmountCountResponse> result, List<String> orgCodes, List<Enterprise> enterprises, List<UnreportedCompanyResponse> unreportedMap) {
        int isReport = 0;
        List<UnreportedCompanyResponse.CompanyDetail> companyDetails = new ArrayList<>();
        for (String code : orgCodes) {
            // 查询条件
            String queryCondition = "orgCode = :orgCode AND inspectionTime BETWEEN :startDate AND :endDate and submitted = '1'";
            // 执行查询
            List<WeeklyReport> weeklyReports = eruptDao.queryEntityList(
                    WeeklyReport.class,
                    queryCondition,
                    new HashMap<String, Object>() {{
                        put("orgCode", code);
                        put("startDate", DateUtil.getOneWeekBeginDateAsDate());
                        put("endDate", DateUtil.getOneWeekLaterDateAsDate());
                    }}
            );
            if(weeklyReports.isEmpty()) {
                companyDetails.add(
                        UnreportedCompanyResponse.CompanyDetail.builder()
                                .orgCode(code)
                                .companyName(OrgUtils.getCompanyName(code, enterprises))
                                .build());
            }else
                isReport ++;
        }
        int finalIsReport = isReport;
        unreportedMap.add(
                UnreportedCompanyResponse.builder()
                        .title("本周检查")
                        .company(companyDetails)
                        .code("WEEKLY_INSPECTION_UNREPORT")
                        .build()
        );
        getStatistical("本周检查", result, orgCodes, finalIsReport);
    }

    //本日检查
    private void getCurrentInspection(List<AmountCountResponse> result, List<String> orgCodes, List<Enterprise> enterprises, List<UnreportedCompanyResponse> unreportedMap) {
        int isReport = 0;
        List<UnreportedCompanyResponse.CompanyDetail> companyDetails = new ArrayList<>();
        for (String code : orgCodes) {
            // 查询条件
            String queryCondition = "orgCode = :orgCode and inspectionTime = :inspectionTime and submitted = '1'";
            // 执行查询
            List<DailyInspection> dailyInspections = eruptDao.queryEntityList(
                    DailyInspection.class,
                    queryCondition,
                    new HashMap<String, Object>() {{
                        put("orgCode", code);
                        put("inspectionTime", DateUtil.getCurrentDayDate());
                    }}
            );
            if(dailyInspections.isEmpty()) {
                companyDetails.add(
                        UnreportedCompanyResponse.CompanyDetail.builder()
                                .orgCode(code)
                                .companyName(OrgUtils.getCompanyName(code, enterprises))
                                .build());
            }else
                isReport ++;
        }
        int finalIsReport = isReport;
        unreportedMap.add(
                UnreportedCompanyResponse.builder()
                        .title("本日检查")
                        .company(companyDetails)
                        .code("CURRENT_INSPECTION_UNREPORT")
                        .build()
        );
        getStatistical("本日检查", result, orgCodes, finalIsReport);
    }


    //本月培训考试
    private void getTrainingExamination(List<AmountCountResponse> result, List<String> orgCodes, List<Enterprise> enterprises, List<UnreportedCompanyResponse> unreportedMap) {
        int isReport = 0;
        List<UnreportedCompanyResponse.CompanyDetail> companyDetails = new ArrayList<>();
        for (String code : orgCodes) {
            // 查询条件
            String queryCondition = "orgCode = :orgCode AND (trainBegin LIKE :monthPattern OR trainEnd LIKE :monthPattern)";
            String monthPattern = DateUtil.getCurrentMonth() + "%";
            // 执行查询
            List<OfflineTraining> offlineTrainings = eruptDao.queryEntityList(
                    OfflineTraining.class,
                    queryCondition,
                    new HashMap<String, Object>() {{
                        put("orgCode", code);
                        put("monthPattern", monthPattern);
                    }}
            );
            if(offlineTrainings.isEmpty()) {
                companyDetails.add(
                        UnreportedCompanyResponse.CompanyDetail.builder()
                                .orgCode(code)
                                .companyName(OrgUtils.getCompanyName(code, enterprises))
                                .build());
            }else
                isReport ++;
        }
        int finalIsReport = isReport;
        unreportedMap.add(
                UnreportedCompanyResponse.builder()
                        .title("本月培训考试")
                        .company(companyDetails)
                        .code("TRAINING_EXAMINATION_UNREPORT")
                        .build()
        );
        getStatistical("本月培训考试", result, orgCodes, finalIsReport);
    }

    //本周专家检查日
    private void getExpertInspection(List<AmountCountResponse> result, List<String> orgCodes, List<Enterprise> enterprises, List<UnreportedCompanyResponse> unreportedMap) {
        int isReport = 0;
        List<UnreportedCompanyResponse.CompanyDetail> companyDetails = new ArrayList<>();
        for (String code : orgCodes) {
            // 查询条件
            String queryCondition = "orgCode = :orgCode AND inspectDate BETWEEN :startDate AND :endDate";
            // 执行查询
            List<InspectExpertDaily> inspectExpertDailies = eruptDao.queryEntityList(
                    InspectExpertDaily.class,
                    queryCondition,
                    new HashMap<String, Object>() {{
                        put("orgCode", code);
                        put("startDate", DateUtil.getOneWeekBeginDateAsDate());
                        put("endDate", DateUtil.getOneWeekLaterDateAsDate());
                    }}
            );
            if(inspectExpertDailies.isEmpty()) {
                companyDetails.add(
                        UnreportedCompanyResponse.CompanyDetail.builder()
                                .orgCode(code)
                                .companyName(OrgUtils.getCompanyName(code, enterprises))
                                .build());
            }else
                isReport ++;
        }
        int finalIsReport = isReport;
        unreportedMap.add(
                UnreportedCompanyResponse.builder()
                        .title("本周专家检查日")
                        .company(companyDetails)
                        .code("EXPERT_INSPECTION_UNREPORT")
                        .build()
        );
        getStatistical("本周专家检查日", result, orgCodes, finalIsReport);
    }

    //本月度可靠性报告单
    private void getReliabilityReport(List<AmountCountResponse> result, List<String> orgCodes, List<Enterprise> enterprises, List<UnreportedCompanyResponse> unreportedMap) {
        int isReport = 0;
        List<UnreportedCompanyResponse.CompanyDetail> companyDetails = new ArrayList<>();
        for (String code : orgCodes) {
            // 查询条件
            String queryCondition = "orgCode = :orgCode and DATE(updateTime) >= :currentMonthDate and submitted = '1'";
            // 执行查询
            List<Report> reports = eruptDao.queryEntityList(
                    Report.class,
                    queryCondition,
                    new HashMap<String, Object>() {{
                        put("orgCode", code);
                        put("currentMonthDate", DateUtil.getCurrentMonthDate());
                    }}
            );
            if(reports.isEmpty()){
                companyDetails.add(
                        UnreportedCompanyResponse.CompanyDetail.builder()
                                .orgCode(code)
                                .companyName(OrgUtils.getCompanyName(code,enterprises))
                                .build());
            }else
                isReport ++;
        }
        int finalIsReport = isReport;
        unreportedMap.add(
                UnreportedCompanyResponse.builder()
                        .title("本月度可靠性报告单")
                        .company(companyDetails)
                        .code("RELIABILITY_UNREPORT")
                        .build()
        );
        getStatistical("本月度可靠性报告单", result, orgCodes, finalIsReport);
    }

    //本年度安全责任制
    private void getSafetyResponsibility(List<AmountCountResponse> result, List<String> orgCodes, List<Enterprise> enterprises, List<UnreportedCompanyResponse> unreportedMap) {
        int isReport = 0;
        List<UnreportedCompanyResponse.CompanyDetail> companyDetails = new ArrayList<>();
        for (String code : orgCodes) {
            // 查询条件
            String queryCondition = "orgCode = :orgCode and year >= :currentYearDate and submitted = '1'";
            // 执行查询
            List<CheckFill> checkFills = eruptDao.queryEntityList(
                    CheckFill.class,
                    queryCondition,
                    new HashMap<String, Object>() {{
                        put("orgCode", code);
                        put("currentYearDate", DateUtil.getCurrentYear());
                    }}
            );
            if(checkFills.isEmpty()){
                companyDetails.add(
                        UnreportedCompanyResponse.CompanyDetail.builder()
                                .orgCode(code)
                                .companyName(OrgUtils.getCompanyName(code,enterprises))
                                .build()
                );
            }else
                isReport ++;
        }
        int finalIsReport = isReport;
        unreportedMap.add(
                UnreportedCompanyResponse.builder()
                        .title("本年度安全责任制")
                        .company(companyDetails)
                        .code("SAFE_RESPONSIBILITY_UNREPORT")
                        .build()
        );
        getStatistical("本年度安全责任制", result, orgCodes, finalIsReport);
    }


    private static void getStatistical(String theme, List<AmountCountResponse> result, List<String> orgCodes, int finalIsReport) {
        result.add(
                AmountCountResponse.builder()
                        .theme(theme)
                        .content(
                                new ArrayList<AmountCountResponse.amountContent>() {{
                                    add(
                                            AmountCountResponse.amountContent.builder()
                                                    .title("已上报")
                                                    .count(String.valueOf(finalIsReport))
                                                    .unit("家").build()
                                    );
                                    add(
                                            AmountCountResponse.amountContent.builder()
                                                    .title("未上报")
                                                    .count(String.valueOf(orgCodes.size() - finalIsReport))
                                                    .unit("家").build()
                                    );
                                }}
                        ).build()
        );
    }

    /**
     * 获取设施检测报告
     *
     * @param orgCode
     * @return
     */
    public StatisticsGroup getFacilityReport(String orgCode) {
        StatisticsGroup statisticsGroup = new StatisticsGroup();
        statisticsGroup.setGroupTitle("设施检测报告");
        List<StatisticsGroup.StatisticsItem> items = new ArrayList<>();
        List<String> orgCodes = OrgUtils.getCodes(orgCode);
        List<EruptMenu> subMenus = menuService.getSubMenus("$dlg-deviceCheckMan");
        List<String> menusCodes = menuService.getSubMenusCode(subMenus);
        for (String className : menusCodes) {
            try {
                Class<?> eruptClass = Class.forName(ClassNameUtil.getFullClassName(className));

                // 获取该类型设备的统计数据
                String menuName = menuService.getSubMenusName(subMenus, className);
                StatisticsGroup.StatisticsItem item = calculateStats(menuName,eruptClass, orgCodes);
                // 创建统计项
                items.add(item);

            } catch (ClassNotFoundException e) {
                System.err.println("无法找到该类: " + className);
            }
        }

        statisticsGroup.setItems(items);
        return statisticsGroup;
    }

    //统计各菜单数据
    private StatisticsGroup.StatisticsItem calculateStats(String menuName ,Class<?> entityClass, List<String> orgCodes) {
        EntityManager em = eruptDao.getEntityManager();
        List<StatisticsGroup.SubStatValue> subStats = new ArrayList<>();

        // 查询总数
        String totalQuery = "SELECT COUNT(e) FROM " + entityClass.getSimpleName() +
                " e WHERE e.orgCode IN :orgCodes";

        // 执行查询
        Long total = em.createQuery(totalQuery, Long.class)
                .setParameter("orgCodes", orgCodes)
                .getSingleResult();

        // 查询已过期数量
        String expiredQuery = totalQuery + " AND e.validityPeriod < :currentDate";
        Long expired = em.createQuery(expiredQuery, Long.class)
                .setParameter("orgCodes", orgCodes)
                .setParameter("currentDate", DateUtil.getCurrentDayDate())
                .getSingleResult();
        createStatisticsItem("已逾期",expired,"个",subStats);


        // 查询即将过期数量
        String expiringQuery = totalQuery + " AND e.validityPeriod BETWEEN :currentDate AND :futureDate";
        Long expiring = em.createQuery(expiringQuery, Long.class)
                .setParameter("orgCodes", orgCodes)
                .setParameter("currentDate", DateUtil.getCurrentDayDate())
                .setParameter("futureDate", DateUtil.getThirtyDaysLaterDateAsDate())
                .getSingleResult();
        createStatisticsItem("即将逾期",expiring,"个",subStats);

        return StatisticsGroup.StatisticsItem.builder()
                .title(menuName)
                .count(Math.toIntExact(total))
                .unit("个")
                .subStats(subStats)
                .build();
    }

    private void createStatisticsItem(String subTitle, Long count, String unit, List<StatisticsGroup.SubStatValue> subStats) {
        subStats.add(StatisticsGroup.SubStatValue.builder()
                .count(Math.toIntExact(count))
                .unit(unit)
                .subTitle(subTitle)
                .build());
    }

    /**
     * 获取备案管理数据
     * @param orgCode
     * @return
     */
    public StatisticsGroup getRecordManagement(String orgCode) {
        StatisticsGroup statisticsGroup = new StatisticsGroup();
        statisticsGroup.setGroupTitle("备案管理");

        List<StatisticsGroup.StatisticsItem> items = new ArrayList<>();
        List<String> orgCodes = OrgUtils.getCodes(orgCode);

        // 应急预案
        getEmergencyPlan(orgCodes,items);
        // 重大危险源
        getDangerSource(orgCodes,items);
        // 安全评价
        getSafetyEvaluation(orgCodes,items);
        // 港口保安
        getPortSecurity(orgCodes,items);

        statisticsGroup.setItems(items);
        return statisticsGroup;
    }

    //港口保安
    private void getPortSecurity(List<String> orgCodes, List<StatisticsGroup.StatisticsItem> items) {
        List<Security> securities = eruptDao.queryEntityList(Security.class, "orgCode IN :orgCodes", Collections.singletonMap("orgCodes", orgCodes));
        StatisticsGroup.StatisticsItem item = new StatisticsGroup.StatisticsItem();
        List<StatisticsGroup.SubStatValue> subStats = new ArrayList<>();
        item.setTitle("港口保安");
        item.setCount(securities.size());
        item.setUnit("个");

        Date currentDate = DateUtil.getCurrentDayDate();
        // 计算即将过期和已逾期的数量
        long expiringSoonCount = calculateExpiringSoonCount(securities, currentDate, 30,Security::getEffectiveDate);
        createStatisticsItem("即将逾期", expiringSoonCount, "个", subStats);

        long expiredCount = calculateExpiredCount(securities, currentDate,Security::getEffectiveDate);
        createStatisticsItem("已逾期", expiredCount, "个", subStats);

        item.setSubStats(subStats);
        items.add(item);
    }

    //安全评价
    private void getSafetyEvaluation(List<String> orgCodes, List<StatisticsGroup.StatisticsItem> items) {
        List<PortRecord> portRecords = eruptDao.queryEntityList(PortRecord.class, "orgCode IN :orgCodes", Collections.singletonMap("orgCodes", orgCodes));
        StatisticsGroup.StatisticsItem item = new StatisticsGroup.StatisticsItem();
        List<StatisticsGroup.SubStatValue> subStats = new ArrayList<>();
        item.setTitle("安全评价");
        item.setCount(portRecords.size());
        item.setUnit("个");

        Date currentDate = DateUtil.getCurrentDayDate();
        // 计算即将过期和已逾期的数量
        long expiringSoonCount = calculateExpiringSoonCount(portRecords, currentDate,30, PortRecord::getValidityPeriod);
        createStatisticsItem("即将逾期", expiringSoonCount, "个", subStats);

        long expiredCount = calculateExpiredCount(portRecords, currentDate,PortRecord::getValidityPeriod);
        createStatisticsItem("已逾期", expiredCount, "个", subStats);

        item.setSubStats(subStats);
        items.add(item);

    }

    // 重大危险源统计
    private void getDangerSource(List<String> orgCodes, List<StatisticsGroup.StatisticsItem> items) {
        List<PrePlanDangerSource> prePlanDangerSources = eruptDao.queryEntityList(PrePlanDangerSource.class, "orgCode IN :orgCodes", Collections.singletonMap("orgCodes", orgCodes));
        StatisticsGroup.StatisticsItem item = new StatisticsGroup.StatisticsItem();
        List<StatisticsGroup.SubStatValue> subStats = new ArrayList<>();
        item.setTitle("重大危险源");
        item.setCount(prePlanDangerSources.size());
        item.setUnit("个");
        // 获取当前日期
        Date currentDate = DateUtil.getCurrentDayDate();
        // 计算到期时间：根据备案时间延后 3 年
        Function<PrePlanDangerSource, Date> calculateExpiryDate = source -> {
            Date recordDate = source.getRecordTime();
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(recordDate);
            calendar.add(Calendar.YEAR, 3); // 延后3年得到到期时间
            return calendar.getTime();
        };

        // 计算即将过期和已逾期的数量
        long expiringSoonCount = calculateExpiringSoonCount(prePlanDangerSources, currentDate,30, calculateExpiryDate);
        createStatisticsItem("即将逾期", expiringSoonCount, "个", subStats);

        long expiredCount = calculateExpiredCount(prePlanDangerSources, currentDate,calculateExpiryDate);
        createStatisticsItem("已逾期", expiredCount, "个", subStats);
        item.setSubStats(subStats);
        items.add(item);
    }

    // 应急预案统计
    private void getEmergencyPlan(List<String> orgCodes, List<StatisticsGroup.StatisticsItem> items) {
        List<PlanManagement> planManagements = eruptDao.queryEntityList(PlanManagement.class, "orgCode IN :orgCodes", Collections.singletonMap("orgCodes", orgCodes));

        StatisticsGroup.StatisticsItem item = new StatisticsGroup.StatisticsItem();
        item.setTitle("应急预案");
        item.setCount(planManagements.size());
        item.setUnit("个");

        List<StatisticsGroup.SubStatValue> subStats = new ArrayList<>();

        // 计算每种预案类型的数量
        Map<String, Long> typeCountMap = planManagements.stream()
                .flatMap(plan -> Stream.of(plan.getPreplanType().split("\\|"))) // 拆分多类型
                .collect(Collectors.groupingBy(type -> type, Collectors.counting()));

        // 创建各预案类型的统计项
        createStatisticsItem("综合预案", typeCountMap.getOrDefault("综合预案", 0L), "个", subStats);
        createStatisticsItem("专项预案", typeCountMap.getOrDefault("专项预案", 0L), "个", subStats);
        createStatisticsItem("现场处置方案", typeCountMap.getOrDefault("现场处置方案", 0L), "个", subStats);

        // 获取当前日期
        Date currentDate = DateUtil.getCurrentDayDate();

        // 计算即将过期和已逾期的数量
        long expiringSoonCount = calculateExpiringSoonCount(planManagements, currentDate,30, PlanManagement::getEvaluationTime);
        createStatisticsItem("即将逾期", expiringSoonCount, "个", subStats);

        long expiredCount = calculateExpiredCount(planManagements, currentDate,PlanManagement::getEvaluationTime);
        createStatisticsItem("已逾期", expiredCount, "个", subStats);

        item.setSubStats(subStats);
        items.add(item);
    }

    /**
     * 统计企业证书
     * @param orgCode
     * @return
     */
    public StatisticsGroup getEnterpriseCertificate(String orgCode) {
        StatisticsGroup statisticsGroup = new StatisticsGroup();
        statisticsGroup.setGroupTitle("危险货物作业附征");
        List<StatisticsGroup.StatisticsItem> items = new ArrayList<>();
        List<String> orgCodes = OrgUtils.getCodes(orgCode);
        List<EnterpriseCertificateView> certificates = eruptDao.queryEntityList(EnterpriseCertificateView.class, "company IN :orgCodes", Collections.singletonMap("orgCodes", orgCodes));
        StatisticsGroup.StatisticsItem item = new StatisticsGroup.StatisticsItem();
        item.setCount(certificates.size());
        item.setUnit("个");

        List<StatisticsGroup.SubStatValue> subStats = new ArrayList<>();
        // 获取当前日期
        Date currentDate = DateUtil.getCurrentDayDate();

        // 计算即将过期和已逾期的数量
        long expiringSoonCount = calculateExpiringSoonCount(certificates, currentDate,90, EnterpriseCertificateView::getExpirationTime);
        long expiredCount = calculateExpiredCount(certificates, currentDate,EnterpriseCertificateView::getExpirationTime);
        createStatisticsItem("正常", certificates.size() - expiringSoonCount - expiredCount, "个", subStats);
        createStatisticsItem("即将逾期", expiringSoonCount, "个", subStats);
        createStatisticsItem("已逾期", expiredCount, "个", subStats);
        item.setSubStats(subStats);
        items.add(item);
        statisticsGroup.setItems(items);
        return statisticsGroup;
    }

    /**
     * 获取本年度执法检查统计
     * @param orgCode
     * @return
     */
    public AmountCountResponse getEnforcementInspection(String orgCode) {
        try {
            // 确保在调用服务方法前注册 token
            MetaContext.registerToken(request.getHeader("token"));

            // 区县级政府
            if (DaliangangContext.isCountryDepartmentUser()) {
                return AmountCountResponse.builder()
                        .theme("本年度执法检查")
                        .derscription("暂无数据")
                        .build();
            }

            List<AmountCountResponse.amountContent> contents = new ArrayList<>();

            List<String> deptCodes = getDeptCodes(orgCode);
            // 获取本年度检查次数
            getCurrentYearCheckCount(deptCodes, contents);
            //1.把当前年份的检查问题id和上一年度的检查问题id集合查询出来
            List<RectificationManage> rectifications = eruptDao.queryEntityList(RectificationManage.class);

            // 获取当前年份和上一年年份
            Calendar calendar = Calendar.getInstance();
            int currentYear = calendar.get(Calendar.YEAR);
            int lastYear = currentYear - 1;

            // 分组为本年度和上一年度的 ID
            List<Long> currentYearIds = rectifications.stream()
                    .filter(rect -> rect.getInspectionDate() != null) // 过滤掉无效数据
                    .filter(rect -> {
                        calendar.setTime(rect.getInspectionDate());
                        return calendar.get(Calendar.YEAR) == currentYear;
                    })
                    .map(RectificationManage::getId) // 提取 ID
                    .collect(Collectors.toList());

            List<Long> lastYearIds = rectifications.stream()
                    .filter(rect -> rect.getInspectionDate() != null)
                    .filter(rect -> {
                        calendar.setTime(rect.getInspectionDate());
                        return calendar.get(Calendar.YEAR) == lastYear;
                    })
                    .map(RectificationManage::getId)
                    .collect(Collectors.toList());

            //2.查询问题表 把问题名称（本年的id）查出来，然后和上一年的id进行比较
            List<InspectionResultsView> currentResults = eruptDao.queryEntityList(InspectionResultsView.class, "inspection_name IN :currentYearIds and publish_status = '1' and check_object IN :deptCodes", new HashMap<String, Object>() {{
                put("currentYearIds", currentYearIds);
                put("deptCodes", deptCodes);
            }});
            List<InspectionResultsView> lastResults = eruptDao.queryEntityList(InspectionResultsView.class, "inspection_name IN :lastYearIds and publish_status = '1' and check_object IN :deptCodes", new HashMap<String, Object>() {{
                put("lastYearIds", lastYearIds);
                put("deptCodes", deptCodes);
            }});
            // 检查问题总数
            getMatterCount(contents,currentResults,lastResults);
            // 整改情况
            getRectification(contents,currentResults);

            return AmountCountResponse.builder().theme("本年度执法检查").content(contents).build();
        } finally {
            // 清理 ThreadLocal 中的数据
            MetaContext.remove();
        }
    }

    //整改情况
    private void getRectification(List<AmountCountResponse.amountContent> contents, List<InspectionResultsView> resultsViews) {

        int countCompleted = 0;          // 已整改
        int countNotCompleted = 0;       // 未整改
        int countOverdueNotCompleted = 0; // 逾期未整改

        // 获取当前日期
        LocalDate currentDate = LocalDate.now();

        // 遍历结果进行统计
        for (InspectionResultsView result : resultsViews) {
            Date deadline = result.getDeadline(); // 截止日期
            java.sql.Date rectificationTime = result.getRectificationTime(); // 整改完成时间
            String inspectionResult = result.getInspectionResult(); // 检查结果

            // 将 deadline 转换为 LocalDate
            LocalDate deadlineLocalDate = deadline != null ? deadline.toInstant().atZone(ZoneId.systemDefault()).toLocalDate() : null;

            // 判断是否完成整改
            boolean isCompleted = rectificationTime != null;
            boolean isOverdue = (deadlineLocalDate != null) && currentDate.isAfter(deadlineLocalDate); // 是否逾期

            if (isCompleted) {
                // 已整改，无需区分是否逾期
                countCompleted++;
            } else if (isOverdue) {
                // 逾期未整改
                if (!"PASS".equals(inspectionResult)) {
                    countOverdueNotCompleted++;
                }
            } else {
                // 未整改且未逾期
                countNotCompleted++;
            }
        }
        // 计算整改率并转换为字符串
        int totalInspections = resultsViews.size();
        String rectificationRate = totalInspections > 0
                ? String.format("%.2f", countCompleted * 100.0 / totalInspections)
                : "0.00";
       contents.add(new AmountCountResponse.amountContent("已整改", String.valueOf(countCompleted), "个","TRCTIFIED"));
       contents.add(new AmountCountResponse.amountContent("未整改", String.valueOf(countNotCompleted + countOverdueNotCompleted), "个","NOT_TRCTIFIED"));
       contents.add(new AmountCountResponse.amountContent("整改率", String.valueOf(rectificationRate), "%","TRCTIFIED_RADIO"));
       contents.add(new AmountCountResponse.amountContent("逾期未整改", String.valueOf(countOverdueNotCompleted), "个","OVERDUE_NOT_TRCTIFIED"));


    }

    // 检查问题总数
    private void getMatterCount(List<AmountCountResponse.amountContent> contents, List<InspectionResultsView> currentResults, List<InspectionResultsView> lastResults) {

        // 3. 统计数量
        long currentYearCount = currentResults.size();
        long lastYearCount = lastResults.size();

        // 4. 计算变化情况
        String change;
        if (currentYearCount > lastYearCount) {
            change = "+" + (currentYearCount - lastYearCount);
        } else if (currentYearCount < lastYearCount) {
            change = "-" + (lastYearCount - currentYearCount);
        } else {
            change = "无变化";
        }

        // 5. 添加统计内容
        processQueryResult(new Object[]{currentYearCount, change}, "本年度检查数量", contents, "CURRENT_YEAR_MATTER");

    }
    // 获取本年度检查次数
    private void getCurrentYearCheckCount(List<String> deptCodes, List<AmountCountResponse.amountContent> contents) {
        String currentYear = DateUtil.getCurrentYear();
        String lastYear = String.valueOf(Integer.parseInt(currentYear) - 1);

        // 根据用户类型调用不同的查询逻辑
        if (DaliangangContext.isDepartmentUser()) {
            // 政府用户
            Object[] result = executeGovernmentQuery(deptCodes, currentYear, lastYear);
            processQueryResult(result, "本年度检查次数", contents,"CURRENT_YEAR_CHECK");
        } else {
            // 企业用户
            Object[] result = executeEnterpriseQuery(deptCodes, currentYear);
            processQueryResult(result, "本年度检查次数", contents, "CURRENT_YEAR_CHECK");
        }
    }

    private Object[] executeGovernmentQuery(List<String> orgCodes, String currentYear, String lastYear) {
        // 政府用户的 SQL 查询
        String sql = "SELECT " +
                "COUNT(IF(YEAR(inspection_date) = :currentYear, 1, NULL)) AS searchCount, " +
                "IF(COUNT(IF(YEAR(inspection_date) = :currentYear, 1, NULL)) > COUNT(IF(YEAR(inspection_date) = :lastYear, 1, NULL)), " +
                "CONCAT('+', COUNT(IF(YEAR(inspection_date) = :currentYear, 1, NULL)) - COUNT(IF(YEAR(inspection_date) = :lastYear, 1, NULL))), " +
                "COUNT(IF(YEAR(inspection_date) = :currentYear, 1, NULL)) - COUNT(IF(YEAR(inspection_date) = :lastYear, 1, NULL))) AS compare " +
                "FROM tb_procedure WHERE org_code IN :orgCodes";

        return (Object[]) eruptDao.getEntityManager()
                .createNativeQuery(sql)
                .setParameter("currentYear", currentYear)
                .setParameter("lastYear", lastYear)
                .setParameter("orgCodes", orgCodes)
                .getSingleResult();
    }

    private Object[] executeEnterpriseQuery(List<String> checkObjects, String currentYear) {
        // 企业用户的 SQL 查询
        String sql = "SELECT " +
                "t.searchCount, " +
                "IF(t.searchCount > t.searchBeforeCount, CONCAT('+', t.searchCount - t.searchBeforeCount), t.searchCount - t.searchBeforeCount) AS compare " +
                "FROM ( " +
                "   SELECT " +
                "       COUNT(IF(YEAR(inspection_date) = :currentYear, TRUE, NULL)) AS searchCount, " +
                "       COUNT(IF(YEAR(inspection_date) = YEAR(DATE_SUB(CONCAT(:currentYear, '-01-01'), INTERVAL 1 YEAR)), TRUE, NULL)) AS searchBeforeCount " +
                "   FROM tb_procedure " +
                "   WHERE 1 = 1 " +
                "   AND id IN (SELECT inspection_name FROM tb_inspection_results WHERE check_object IN :checkObjects) " +
                ") t";

        return (Object[]) eruptDao.getEntityManager()
                .createNativeQuery(sql)
                .setParameter("currentYear", currentYear)
                .setParameter("checkObjects", checkObjects)
                .getSingleResult();
    }

    private void processQueryResult(Object[] result, String title, List<AmountCountResponse.amountContent> contents, String code) {
        // 解析查询结果
        long searchCount = ((Number) result[0]).longValue();
        String compare = (String) result[1];

        // 构建统计项
        contents.add(
                AmountCountResponse.amountContent.builder()
                        .title(title)
                        .count(String.valueOf(searchCount))
                        .unit("次")
                        .code(code)
                        .build()

        );
        // 添加同比数据
        contents.add(
                AmountCountResponse.amountContent.builder()
                        .title("较上一年变化")
                        .count(compare)
                        .code(code + "_THEN_BEFORE")
                        .build()
        );
    }

    /**
     * 获取从业人员证书
     * @param orgCode
     * @return
     */
    public List<AmountCountResponse.amountContent> getPractitionerCertificate(String orgCode) {
        List<AmountCountResponse.amountContent> contents = new ArrayList<>();
        // 获取所有的 orgCodes
        List<String> orgCodes = OrgUtils.getCodes(orgCode);
        List<String> companyNames = getCompanyNamesByBatchQuery(orgCodes);
        // 替换公司名称中的括号，确保匹配中文括号与英文括号
        // 标准化输入的公司名称列表，将括号统一为中文括号
        List<String> companyName = companyNames.stream()
                .map(name -> name.replace("(", "（").replace(")", "）"))
                .collect(Collectors.toList());

        // 在 SQL 中将数据库字段的括号也统一为中文括号
        List<EmployeeCertificateView> views = eruptDao.queryEntityList(
                EmployeeCertificateView.class,
                "REPLACE(REPLACE(company1, '(', '（'), ')', '）') IN :companyNames",
                Collections.singletonMap("companyNames", companyName)
        );
        contents.add(new AmountCountResponse.amountContent("从业人员证书数量", String.valueOf(views.size()), "个","PRACTITIONER_CERTIFICATE"));
        return contents;
    }

    /**
     * 通用方法：计算即将过期的数量
     * @param items 目标对象的列表
     * @param currentDate 当前日期
     * @param daysUntilExpiry 自定义多少天内即将过期
     * @param dateExtractor 提取 Date 字段的函数
     * @param <T> 目标对象的类型
     * @return 即将过期的数量
     */
    public <T> long calculateExpiringSoonCount(List<T> items, Date currentDate, int daysUntilExpiry, Function<T, Date> dateExtractor) {
        long expiryThresholdInMillis = daysUntilExpiry * 24L * 60 * 60 * 1000; // 将天数转换为毫秒
        return items.stream()
                .map(dateExtractor)
                .filter(Objects::nonNull)
                .filter(date -> {
                    long diffInMillis = date.getTime() - currentDate.getTime();
                    return diffInMillis <= expiryThresholdInMillis && diffInMillis > 0;
                })
                .count();
    }

    /**
     * 通用方法：计算已逾期的数量
     * @param items 目标对象的列表
     * @param currentDate 当前日期
     * @param dateExtractor 提取 Date 字段的函数
     * @param <T> 目标对象的类型
     * @return 已逾期的数量
     */
    public <T> long calculateExpiredCount(List<T> items, Date currentDate, Function<T, Date> dateExtractor) {
        return items.stream()
                .map(dateExtractor)
                .filter(Objects::nonNull)
                .filter(date -> date.before(currentDate))
                .count();
    }

    /**
     * 获取公司名称
     * @param orgCode
     * @return
     */
    public List<Map<String, String>> getEnableCompanies(String orgCode) {
        List<String> orgCodes = OrgUtils.getCodes();
        List<String> companyNames = getCompanyNamesByBatchQuery(orgCodes);
        // 将 orgCode 和 companyName 一一对应组合成 Map
        List<Map<String, String>> result = new ArrayList<>();
        for (int i = 0; i < orgCodes.size(); i++) {
            Map<String, String> map = new HashMap<>();
            map.put("orgCode", orgCodes.get(i));
            map.put("companyName", companyNames.get(i));
            result.add(map);
        }

        return result;

    }

    /**
     * 获取未上报企业列表
     *
     * @param orgCode
     * @param code
     * @return
     */
    public List<UnreportedCompanyResponse> getUnreportedCompanies(String orgCode, String code) {
        lock.lock();
        try {
            while (!isReportDone){
                reportCompleted.await();
            }
            List<UnreportedCompanyResponse> responseList = unreportedCache.getOrDefault(orgCode == null ? "ALL" : orgCode, new ArrayList<>());
            if (code != null) {
                responseList = responseList.stream()
                        .filter(response -> response.getCode().equals(code))
                        .collect(Collectors.toList());
            }
            return responseList;
        } catch (InterruptedException e) {
            throw new RuntimeException(e);
        }finally {
            lock.unlock();
        }
    }
}
