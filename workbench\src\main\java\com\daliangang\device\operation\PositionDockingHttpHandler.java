package com.daliangang.device.operation;

import cn.hutool.http.HttpGlobalConfig;
import cn.hutool.http.HttpRequest;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson2.JSON;
import com.daliangang.device.entity.*;
import com.daliangang.majorisk.service.UnloadShipHttpService;
import com.google.gson.internal.LinkedTreeMap;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.stereotype.Service;
import xyz.erupt.core.util.EruptSpringUtil;
import xyz.erupt.jpa.dao.EruptDao;
import xyz.erupt.toolkit.remote.RemoteProxyService;
import xyz.erupt.toolkit.remote.RemoteQuery;
import xyz.erupt.toolkit.utils.EruptDaoUtils;
import xyz.erupt.toolkit.utils.NotifyUtils;
import xyz.erupt.upms.service.EruptPlatformService;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;

/**
 * <AUTHOR>
 * @since :2023/5/18:15:37
 */
@Service
@Slf4j
public class PositionDockingHttpHandler {
    @Resource
    EruptDao eruptDao;
    @Resource
    private RemoteProxyService remoteProxyService;

    @Resource
    private EruptPlatformService eruptPlatformService ;

    public String AddHttpDocking(Object o, String type, String orgCode, String token) {
        try {
            HttpGlobalConfig.setTimeout(3000);
            return AddHttpDocking_(o, type, orgCode, token);
        } catch (Exception ex) {
            HttpGlobalConfig.setTimeout(-1);
            log.error("推送数据失败 -> o=" + JSONUtil.toJsonStr(o) + ", type=" + type + ", orgCode=" + orgCode + "， token=" + token);
            ex.printStackTrace();
        }
        return "";
    }

    // 风险位置添加
    private String AddHttpDocking_(Object o, String type, String orgCode, String token) {
        String ip = eruptPlatformService.getOption(UnloadShipHttpService.UNLOAD_BUSINESS_IP).getAsString() ;
        String url = ip+"/DataSync/AddRiskPosition";
        // 查询风控是否具该企业
        List<LinkedTreeMap> linkedTreeMaps = remoteProxyService.queryEntity("EnterpriseRelevancy", RemoteQuery.builder().condition("org_code", orgCode));
        if (!linkedTreeMaps.isEmpty() && ObjectUtils.isNotEmpty(linkedTreeMaps.get(0))) {
            switch (type) {
                case "1": //储罐
                    StorageTank storageTank = JSON.parseObject(JSON.toJSONString(o), StorageTank.class);
                    // 添加完成后生成uuid绑定地点关系
                    PositionDocking positionDocking = EruptSpringUtil.getBean(PositionDocking.class);
                    positionDocking.setBId(storageTank.getId());
                    positionDocking.setPositionId(UUID.randomUUID().toString());
                    positionDocking.setPositionType("1");
                    eruptDao.merge(positionDocking);

                    // 调用风控接口
                    Map map = new HashMap();
                    map.put("g_id", positionDocking.getPositionId());
                    map.put("g_name", storageTank.getName());
                    map.put("ent_id", linkedTreeMaps.get(0).get("smId"));
                    map.put("gType", 1);
                    map.put("gScopes", storageTank.getMap());
                    String json = JSON.toJSONString(map);
                    log.info("推送新增储罐数据 -> " + json );
                    String body = HttpRequest.post(url)
                            .header("Authorization", token)
                            .header("Content-Type", "application/json")
                            .body(json)
                            .execute().body();
                    log.info("推送新增储罐数据body -> " + body );
                    return body;

                case "2"://泊位
                    Berth berth = JSON.parseObject(JSON.toJSONString(o), Berth.class);
                    // 添加完成后生成uuid绑定地点关系
                    PositionDocking positionDocking1 = EruptSpringUtil.getBean(PositionDocking.class);
                    positionDocking1.setBId(berth.getId());
                    positionDocking1.setPositionId(UUID.randomUUID().toString());
                    positionDocking1.setPositionType("2");
                    eruptDao.merge(positionDocking1);

                    // 调用风控接口
                    Map map1 = new HashMap();
                    map1.put("g_id", positionDocking1.getPositionId());
                    map1.put("g_name", berth.getName());
                    map1.put("ent_id", linkedTreeMaps.get(0).get("smId"));
                    map1.put("gType", 2);
                    map1.put("gScopes", berth.getMap());
                    String json1 = JSON.toJSONString(map1);
                    log.info("推送新增泊位数据 -> " + json1 );
                    String body1 = HttpRequest.post(url)
                            .header("Authorization", token)
                            .header("Content-Type", "application/json")
                            .body(json1)
                            .execute().body();
                    log.info("推送新增泊位数据body -> " + body1 );
                    return body1;
                case "3"://栈台
                    LoadingDock loadingDock = JSON.parseObject(JSON.toJSONString(o), LoadingDock.class);
                    // 添加完成后生成uuid绑定地点关系
                    PositionDocking positionDocking2 = EruptSpringUtil.getBean(PositionDocking.class);
                    positionDocking2.setBId(loadingDock.getId());
                    positionDocking2.setPositionId(UUID.randomUUID().toString());
                    positionDocking2.setPositionType("3");
                    eruptDao.merge(positionDocking2);

                    // 调用风控接口

                    Map map2 = new HashMap();
                    map2.put("g_id", positionDocking2.getPositionId());
                    map2.put("g_name", loadingDock.getName());
                    map2.put("ent_id", linkedTreeMaps.get(0).get("smId"));
                    map2.put("gType", 3);
                    map2.put("gScopes", loadingDock.getMap());
                    String json2 = JSON.toJSONString(map2);
                    log.info("推送新增栈台数据 -> " + json2 );
                    String body2 = HttpRequest.post(url)
                            .header("Authorization", token)
                            .header("Content-Type", "application/json")
                            .body(json2)
                            .execute().body();
                    log.info("推送新增栈台数据body -> " + body2 );
                    return body2;
                case "5"://堆场
                    Yard yard = JSON.parseObject(JSON.toJSONString(o), Yard.class);
                    // 添加完成后生成uuid绑定地点关系
                    PositionDocking positionDocking3 = EruptSpringUtil.getBean(PositionDocking.class);
                    positionDocking3.setBId(yard.getId());
                    positionDocking3.setPositionId(UUID.randomUUID().toString());
                    positionDocking3.setPositionType("5");
                    eruptDao.merge(positionDocking3);

                    // 调用风控接口
                    Map map3 = new HashMap();
                    map3.put("g_id", positionDocking3.getPositionId());
                    map3.put("g_name", yard.getName());
                    map3.put("ent_id", linkedTreeMaps.get(0).get("smId"));
                    map3.put("gType", 5);
                    map3.put("gScopes", yard.getMap());
                    String json3 = JSON.toJSONString(map3);
                    log.info("推送新增堆场数据 -> " + json3 );
                    String body3 = HttpRequest.post(url)
                            .header("Authorization", token)
                            .header("Content-Type", "application/json")
                            .body(json3)
                            .execute().body();
                    log.info("推送新增堆场数据body -> " + body3 );
                    return body3;

                default: //仓库/棚库
                    Warehouse warehouse = JSON.parseObject(JSON.toJSONString(o), Warehouse.class);
                    // 添加完成后生成uuid绑定地点关系
                    PositionDocking positionDocking4 = EruptSpringUtil.getBean(PositionDocking.class);
                    positionDocking4.setBId(warehouse.getId());
                    positionDocking4.setPositionId(UUID.randomUUID().toString());
                    positionDocking4.setPositionType("6");
                    eruptDao.merge(positionDocking4);

                    // 调用风控接口
                    Map map4 = new HashMap();
                    map4.put("g_id", positionDocking4.getPositionId());
                    map4.put("g_name", warehouse.getName());
                    map4.put("ent_id", linkedTreeMaps.get(0).get("smId"));
                    map4.put("gType", 6);
                    map4.put("gScopes", warehouse.getMap());
                    String json4 = JSON.toJSONString(map4);
                    log.info("推送新增仓库/棚库数据 -> " + json4 );
                    String body4 = HttpRequest.post(url)
                            .header("Authorization", token)
                            .header("Content-Type", "application/json")
                            .body(json4)
                            .execute().body();
                    log.info("推送新增仓库/棚库数据body -> " + body4 );
                    return body4;
            }
        }
        return "";
    }

    public String updateHttpDocking(Object o, String type, String orgCode, Long id, String token) {
        try {
            HttpGlobalConfig.setTimeout(3000);
            return updateHttpDockingImpl(o, type, orgCode, id, token);
        } catch (Exception ex) {
            HttpGlobalConfig.setTimeout(-1);
            log.error("同步数据错误 -> o" + JSONUtil.toJsonStr(o)  + ", type=" + type + ", orgCode=" + orgCode + ", id=" + id + ", token=" + token);
            ex.printStackTrace();
            NotifyUtils.showErrorMsg("数据同步错误 -> " + ex.getMessage());
        }
        return null;
    }

    private String updateHttpDockingImpl(Object o, String type, String orgCode, Long id, String token) {
        String ip = eruptPlatformService.getOption(UnloadShipHttpService.UNLOAD_BUSINESS_IP).getAsString() ;
        String url = ip+"/DataSync/UpdateRiskPosition";
        // 查询风控是否具该企业
        List<LinkedTreeMap> linkedTreeMaps = remoteProxyService.queryEntity("EnterpriseRelevancy", RemoteQuery.builder().condition("org_code", orgCode));
        if (ObjectUtils.isNotEmpty(linkedTreeMaps)) {
            // 查询关联表数据没有新增对应表，有则按id修改
            // PositionDocking positionDocking = eruptDao.queryEntity(PositionDocking.class, "b_id=" + id);
            PositionDocking positionDocking = EruptDaoUtils.selectOne("select * from tb_position_docking where b_id =" + id + " and position_type = " + type, PositionDocking.class);
            if (ObjectUtils.isNotEmpty(positionDocking)) {
                switch (type) {
                    case "1": //储罐
                        StorageTank storageTank = JSON.parseObject(JSON.toJSONString(o), StorageTank.class);
                        // 调用风控接口
                        Map map = new HashMap();
                        map.put("g_id", positionDocking.getPositionId());
                        map.put("gScopes", storageTank.getMap());

                        String json = JSON.toJSONString(map);
                        log.info("推送更新储罐数据 -> " + json );
                        String body = HttpRequest.post(url)
                                .header("Authorization", token)
                                .header("Content-Type", "application/json")
                                .body(json)
                                .execute().body();
                        log.info("推送更新储罐数据body -> " + body );
                        return body;

                    case "2"://泊位
                        Berth berth = JSON.parseObject(JSON.toJSONString(o), Berth.class);

                        // 调用风控接口
                        Map map1 = new HashMap();
                        map1.put("g_id", positionDocking.getPositionId());
                        map1.put("gScopes", berth.getMap());

                        String json1 = JSON.toJSONString(map1);
                        log.info("推送更新泊位数据 -> " + json1 );
                        String body1 = HttpRequest.post(url)
                                .header("Authorization", token)
                                .header("Content-Type", "application/json")
                                .body(json1)
                                .execute().body();
                        log.info("推送更新泊位数据body -> " + body1 );
                        return body1;
                    case "3"://栈台
                        LoadingDock loadingDock = JSON.parseObject(JSON.toJSONString(o), LoadingDock.class);

                        // 调用风控接口
                        Map map2 = new HashMap();
                        map2.put("g_id", positionDocking.getPositionId());
                        map2.put("gScopes", loadingDock.getMap());

                        String json2 = JSON.toJSONString(map2);
                        log.info("推送更新栈台数据 -> " + json2 );
                        String body2 = HttpRequest.post(url)
                                .header("Authorization", token)
                                .header("Content-Type", "application/json")
                                .body(json2)
                                .execute().body();
                        log.info("推送更新栈台数据body -> " + body2 );
                        return body2;
                    case "5"://堆场
                        Yard yard = JSON.parseObject(JSON.toJSONString(o), Yard.class);
                        // 调用风控接口
                        Map map3 = new HashMap();
                        map3.put("g_id", positionDocking.getPositionId());
                        map3.put("gScopes", yard.getMap());

                        String json3 = JSON.toJSONString(map3);
                        log.info("推送更新堆场数据 -> " + json3 );
                        String body3 = HttpRequest.post(url)
                                .header("Authorization", token)
                                .header("Content-Type", "application/json")
                                .body(json3)
                                .execute().body();
                        log.info("推送更新堆场数据body -> " + body3 );
                        return body3;

                    default: //仓库/棚库
                        Warehouse warehouse = JSON.parseObject(JSON.toJSONString(o), Warehouse.class);
                        // 调用风控接口
                        Map map4 = new HashMap();
                        map4.put("g_id", positionDocking.getPositionId());
                        map4.put("gScopes", warehouse.getMap());
                        String json4 = JSON.toJSONString(map4);
                        log.info("推送更新仓库/棚库数据 -> " + json4 );
                        String body4 = HttpRequest.post(url)
                                .header("Authorization", token)
                                .header("Content-Type", "application/json")
                                .body(json4)
                                .execute().body();
                        log.info("推送更新仓库/棚库数据body -> " + body4 );
                        return body4;
                }
            }

        }
        return "";
    }

    public void deleteHttpDocking(Object o, String type, String orgCode, Long id, String token) {
        try {
            HttpGlobalConfig.setTimeout(3000);
            deleteHttpDocking_(o, type, orgCode, id, token);
        } catch (Exception ex) {
            HttpGlobalConfig.setTimeout(-1);
            log.error("推送删除数据失败 -> o=" + JSONUtil.toJsonStr(o) + ", type=" + type + ", orgCode=" + orgCode + "， token=" + token);
            ex.printStackTrace();
        }
    }

    public String deleteHttpDocking_(Object o, String type, String orgCode, Long id, String token){
        String ip = eruptPlatformService.getOption(UnloadShipHttpService.UNLOAD_BUSINESS_IP).getAsString() ;
        String url = ip+"/DataSync/DeleteRiskPosition";
        // 查询风控是否具该企业
        List<LinkedTreeMap> linkedTreeMaps = remoteProxyService.queryEntity("EnterpriseRelevancy", RemoteQuery.builder().condition("org_code", orgCode));
        if (ObjectUtils.isNotEmpty(linkedTreeMaps)) {
            // 查询关联表数据没有新增对应表，有则按id删除
            PositionDocking positionDocking = EruptDaoUtils.selectOne("select * from tb_position_docking where b_id =" + id + " and position_type = " + type, PositionDocking.class);
            if (ObjectUtils.isNotEmpty(positionDocking)) {
                Map map1 = new HashMap();
                map1.put("g_id", positionDocking.getPositionId());
                String json1 = JSON.toJSONString(map1);
                log.info("推送删除位置数据 -> " + json1 );
                String body1 = HttpRequest.post(url)
                        .header("Authorization", token)
                        .header("Content-Type", "application/json")
                        .body(json1)
                        .execute().body();
                log.info("推送删除位置数据body -> " + body1 );
            }
        }
        return "";
    }
}
