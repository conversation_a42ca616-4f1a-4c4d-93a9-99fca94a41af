package com.daliangang.rndpub.operation;

import com.daliangang.rndpub.entity.InspectionResults;
import com.daliangang.rndpub.form.InspectorForm;
import com.daliangang.rndpub.form.RectificationForm;
import com.daliangang.rndpub.proxy.InspectionrResultsDataProxy;
import com.deepoove.poi.XWPFTemplate;
import com.deepoove.poi.util.PoitlIOUtils;

import lombok.SneakyThrows;
import org.apache.commons.lang3.ObjectUtils;

import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import xyz.erupt.annotation.fun.OperationHandler;
import xyz.erupt.annotation.sub_erupt.Tpl;
import xyz.erupt.core.util.EruptSpringUtil;

import xyz.erupt.toolkit.utils.EruptDaoUtils;
import xyz.erupt.toolkit.utils.FileUtils;

import xyz.erupt.upms.service.EruptContextService;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.transaction.Transactional;
import java.io.*;
import java.net.URLEncoder;
import java.util.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

/**
 * <AUTHOR>
 * @since :2023/4/7:17:17
 */
@Component
@Service
public class WordExportInspectionHandler implements Tpl.TplHandler {

    @Resource
    protected HttpServletResponse response;


    @Override
    @SneakyThrows
    @Transactional
    public void bindTplData(Map<String, Object> binding, String[] params) {
        String inspectionName = InspectionrResultsDataProxy.getInspectionName();

        // 数据源
        // 检查信息
        List<RectificationForm> rectificationFormList = EruptDaoUtils.selectOnes("SELECT \n" +
                "   tp.id as id,tp.name as name, te.name as enterpriseName, tp.inspection_date as inspectionDate, tir.problem_description as problemDescription,tir.proposal as proposal,tir.deadline as deadline\n" +
                "FROM tb_procedure tp\n" +
                "JOIN tb_inspection_results tir ON tp.id = tir.inspection_name\n" +
                "JOIN tb_enterprise te ON tir.check_object = te.org_code\n" +
                "where tir.inspection_name = " + inspectionName + "  and tir.publish_status = 1", RectificationForm.class);
        // 检查人员
        List<Map<String,Object>> tList=new ArrayList<Map<String,Object>>();
        List<InspectorForm> jcryList = EruptDaoUtils.selectOnes("select name as name ,certificate_no as certificateNo from tb_inspector ti where procedure_id = " + inspectionName, InspectorForm.class);
        if(ObjectUtils.isNotEmpty(jcryList)) {
            jcryList.forEach(v->{
                Map<String,Object> tMap = new HashMap<String, Object>();
                tMap.put("JcryList",v);
                tList.add(tMap);
            });

        }

        // 数据处理按照企业分类
        Map<String, List<RectificationForm>> groupMap = rectificationFormList.stream().collect(Collectors.groupingBy(RectificationForm::getEnterpriseName));

        // 循环导出word
        if (groupMap.size()!=0) {
            // 外层循环word变量
            // 企业数据集合
            List<Map> comoanyList = new ArrayList();
            groupMap.forEach((key, value) -> {
                Map<String, Object> binding2 = new HashMap<>();

                // 内层循环企业数据
                StringBuffer problemDescription = new StringBuffer();
                StringBuffer proposal = new StringBuffer();
                value.forEach(v -> {
                    problemDescription.append(v.getProblemDescription());
                    proposal.append(v.getProposal());
                });
                //格式化格式

                binding2.put("company", key);
                binding2.put("inspectionDate", rectificationFormList.get(0).getInspectionDate());
                binding2.put("typePro", tList);
                binding2.put("problemDescription", problemDescription);
                binding2.put("proposal", proposal);

                comoanyList.add(binding2);

            });

            ZipOutputStream zos=null;
            ByteArrayOutputStream arrayOutputStream=null;
            XWPFTemplate  template = null;
            response.reset();
            response.setContentType("application/octet-stream;charset=UTF-8");
            response.setHeader("Content-Disposition", "attachment;filename=" + java.net.URLEncoder.encode( "行政执法检查记录" + ".zip", "UTF-8"));
            zos = new ZipOutputStream(response.getOutputStream());//压缩文件输出流
            arrayOutputStream= new ByteArrayOutputStream();//ByteArray临时存储流
                for (Map v:comoanyList) {

                    // 获取路径
                    InputStream inputStream = FileUtils.readInputStream("tpl/xzzfjc.docx");
                      template = XWPFTemplate.compile(inputStream).render(v,arrayOutputStream);
                    zos.putNextEntry(new ZipEntry(v.get("company")+"行政执法检查记录.docx"));

                    arrayOutputStream.reset();//重置ByteArray流（为了重复使用）
                    template.write(arrayOutputStream);
                    zos.write(arrayOutputStream.toByteArray());//把ByteArray流内容写入zip输出流
                   // template.write(zos);//把word对象内容写到ByteArray流临时存储
                }
            if(zos!=null){
                zos.closeEntry();//关闭zipEntyp
                zos.flush();//刷新zip输出流缓冲区
                zos.close();//关闭zip输出流
            }
            if(arrayOutputStream!=null){
                template.close();
                arrayOutputStream.close();//关闭ByteArray流

            }

        }


    }

    @SneakyThrows
    public void word(Map v){
        // 获取路径
        InputStream inputStream = FileUtils.readInputStream("tpl/xzzfjc.docx");
        XWPFTemplate  template = XWPFTemplate.compile(inputStream).render(v);

        response.setContentType("application/octet-stream;charset=UTF-8");
        response.setHeader("Content-Disposition", "attachment;filename=" + java.net.URLEncoder.encode(v.get("company") + "行政执法检查记录" + ".docx", "UTF-8"));
        OutputStream out = response.getOutputStream();
        BufferedOutputStream bos = new BufferedOutputStream(out);
        template.write(bos);
        bos.flush();
        out.flush();
        PoitlIOUtils.closeQuietlyMulti( template,bos,out);
    }

    private OutputStream setResponse(HttpServletResponse response, String zipFileName) throws Exception{
        zipFileName = URLEncoder.encode(zipFileName, "UTF-8");
        response.setCharacterEncoding("UTF-8");
        response.setContentType(MediaType.APPLICATION_OCTET_STREAM_VALUE);
        response.setHeader("Access-Control-Expose-Headers", "Content-Disposition");
        response.setHeader("Content-Disposition", "attachment; filename=" + zipFileName);
        response.setHeader("Pragma", "public");
        response.setHeader("Cache-Control", "no-store");
        response.addHeader("Cache-Control", "max-age=0");
        return response.getOutputStream();
    }


    /**
     * 将文件流写入文件中
     *
     * @param is
     */
    private File InputStreamToFile(InputStream is) throws Exception {
        File file = File.createTempFile("EquipmentFile", ".docx");
        FileOutputStream fos = new FileOutputStream(file);
        byte[] b = new byte[1024];
        while ((is.read(b)) != -1) {
            fos.write(b);// 写入数据
        }
        is.close();
        fos.close();// 保存数据
        return file;
    }

    public String inputStream2StringNew(InputStream is) {
        try {
            ByteArrayOutputStream boa = new ByteArrayOutputStream();
            int len = 0;
            byte[] buffer = new byte[1024];

            while ((len = is.read(buffer)) != -1) {
                boa.write(buffer, 0, len);
            }
            is.close();
            boa.close();
            byte[] result = boa.toByteArray();

            String temp = new String(result);

            // 识别编码
            if (temp.contains("utf-8")) {
                return new String(result, "utf-8");
            } else if (temp.contains("gb2312")) {
                return new String(result, "gb2312");
            } else {
                return new String(result, "utf-8");
            }
        } catch (Exception e) {
            // TODO Auto-generated catch block
            e.printStackTrace();
            return null;
        }
    }


   // @RestController
    public static class CodeGenerateController {
      //  @RequestMapping("/erupt-api/designer/module/generateCodes123")
        //@EruptRouter(verifyType = EruptRouter.VerifyType.LOGIN)
        @SneakyThrows
        public void generateCodes(HttpServletRequest request, HttpServletResponse response) {
            String inspectionName = InspectionrResultsDataProxy.getInspectionName();

            // 数据源
            // 检查信息
            List<RectificationForm> rectificationFormList = EruptDaoUtils.selectOnes("SELECT \n" +
                    "   tp.id as id,tp.name as name, te.name as enterpriseName, tp.inspection_date as inspectionDate, tir.problem_description as problemDescription,tir.proposal as proposal,tir.deadline as deadline\n" +
                    "FROM tb_procedure tp\n" +
                    "JOIN tb_inspection_results tir ON tp.id = tir.inspection_name\n" +
                    "JOIN tb_enterprise te ON tir.check_object = te.org_code\n" +
                    "where tir.inspection_name = " + inspectionName + "  and tir.publish_status = 1", RectificationForm.class);
            // 检查人员
            List<Map<String,Object>> tList=new ArrayList<Map<String,Object>>();
            List<InspectorForm> jcryList = EruptDaoUtils.selectOnes("select name as name ,certificate_no as certificateNo from tb_inspector ti where procedure_id = " + inspectionName, InspectorForm.class);
            if(ObjectUtils.isNotEmpty(jcryList)) {
                jcryList.forEach(v->{
                    Map<String,Object> tMap = new HashMap<String, Object>();
                    tMap.put("JcryList",v);
                    tList.add(tMap);
                });

            }

            // 数据处理按照企业分类
            Map<String, List<RectificationForm>> groupMap = rectificationFormList.stream().collect(Collectors.groupingBy(RectificationForm::getEnterpriseName));

            // 循环导出word
            if (groupMap.size()!=0) {
                // 外层循环word变量
                // 企业数据集合
                List<Map> comoanyList = new ArrayList();
                groupMap.forEach((key, value) -> {
                    Map<String, Object> binding2 = new HashMap<>();

                    // 内层循环企业数据
                    StringBuffer problemDescription = new StringBuffer();
                    StringBuffer proposal = new StringBuffer();
                    value.forEach(v -> {
                        problemDescription.append(v.getProblemDescription());
                        proposal.append(v.getProposal());
                    });
                    //格式化格式

                    binding2.put("company", key);
                    binding2.put("inspectionDate", rectificationFormList.get(0).getInspectionDate());
                    binding2.put("typePro", tList);
                    binding2.put("problemDescription", problemDescription);
                    binding2.put("proposal", proposal);

                    comoanyList.add(binding2);

                });

                ZipOutputStream zos=null;
                ByteArrayOutputStream arrayOutputStream=null;
                XWPFTemplate  template = null;
                response.reset();
                response.setContentType("application/octet-stream;charset=UTF-8");
                response.setHeader("Content-Disposition", "attachment;filename=" + java.net.URLEncoder.encode( "行政执法检查记录" + ".zip", "UTF-8"));
                zos = new ZipOutputStream(response.getOutputStream());//压缩文件输出流
                arrayOutputStream= new ByteArrayOutputStream();//ByteArray临时存储流
                for (Map v:comoanyList) {

                    // 获取路径
                    InputStream inputStream = FileUtils.readInputStream("tpl/xzzfjc.docx");
                    template = XWPFTemplate.compile(inputStream).render(v,arrayOutputStream);
                    zos.putNextEntry(new ZipEntry(v.get("company")+"行政执法检查记录.docx"));

                    arrayOutputStream.reset();//重置ByteArray流（为了重复使用）
                    template.write(arrayOutputStream);
                    zos.write(arrayOutputStream.toByteArray());//把ByteArray流内容写入zip输出流
                    // template.write(zos);//把word对象内容写到ByteArray流临时存储
                }
                if(zos!=null){
                    zos.closeEntry();//关闭zipEntyp
                    zos.flush();//刷新zip输出流缓冲区
                    zos.close();//关闭zip输出流
                }
                if(arrayOutputStream!=null){
                    template.close();
                    arrayOutputStream.close();//关闭ByteArray流

                }

            }
        }
    }


   // @Override
    public String exec(List<InspectionResults> data, Void aVoid, String[] param) {

        String url = "window.open('/erupt-api/designer/module/generateCodes123?_token=" + EruptSpringUtil.getBean(EruptContextService.class).getCurrentToken() + "')";
        return url;
    }
}
