package com.daliangang.device.form;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import xyz.erupt.toolkit.db.Comment;

import java.util.List;

/**
 * <AUTHOR>
 * @since :2023/5/31:15:24
 */
@Data
public class DockingForm {

    @Comment("地点位置")
    @ApiModelProperty("地点位置")
    private List<Address> map;


    @Data
    public class Address {
        private List<Ht> Ht;
        private double Ry;
        private int Type;
    }

    @Data
    public class Ht {
        private double lat;
        private double lng;
    }

}
