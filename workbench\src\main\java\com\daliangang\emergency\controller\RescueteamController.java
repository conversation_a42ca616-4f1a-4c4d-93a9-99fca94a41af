package com.daliangang.emergency.controller;

import com.daliangang.emergency.form.DrillForm;
import com.daliangang.emergency.form.EmergencyTeamForm;
import com.daliangang.emergency.sql.RescueteamSql;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import xyz.erupt.core.annotation.EruptRouter;
import xyz.erupt.core.module.MetaUserinfo;
import xyz.erupt.core.view.EruptApiModel;
import xyz.erupt.toolkit.remote.RemoteProxyService;
import xyz.erupt.toolkit.utils.EruptDaoUtils;
import xyz.erupt.toolkit.utils.SqlUtils;

import javax.annotation.Resource;
import javax.transaction.Transactional;
import java.util.List;

/**
 * <AUTHOR>
 * @since :2023/4/14:13:31
 */
@RestController
public class RescueteamController {

    @Resource
    private RemoteProxyService remoteProxyService;
    @Resource
    private RescueteamSql rescueteamSql;

    @RequestMapping("erupt-api/Rescueteam/select")
    @EruptRouter(verifyType = EruptRouter.VerifyType.LOGIN)
    @Transactional
    public EruptApiModel selectRescueteam() {
        MetaUserinfo remoteUserInfo = remoteProxyService.getRemoteUserInfo();
        EmergencyTeamForm emergencyTeamForm = EruptDaoUtils.selectOne("SELECT GROUP_CONCAT('救援队：',team_name,',共：',personnel_size,'，负责人为：',person_in_charge,',联系电话：',contact_number) as teamName from tb_rescueteam where org_code ='" + remoteUserInfo.getOrg() + "'", EmergencyTeamForm.class);
        if (ObjectUtils.isEmpty(emergencyTeamForm.getTeamName())) {
            emergencyTeamForm.setTeamName("无");
            return EruptApiModel.successApi(emergencyTeamForm.getTeamName());
        }
        return EruptApiModel.successApi(emergencyTeamForm.getTeamName());
    }

    //统计应急队伍数
    @RequestMapping("erupt-api/get/selectRescueteamNum")
    @EruptRouter(verifyType = EruptRouter.VerifyType.LOGIN)
    public EruptApiModel selectRescueteamNum() {
        MetaUserinfo remoteUserInfo = remoteProxyService.getRemoteUserInfo();
        String orgCode = SqlUtils.wrapIn(remoteUserInfo.getAuth());
        String sql = rescueteamSql.selectRescueteamNum(orgCode);
        List<DrillForm> employeeCertificateForm = EruptDaoUtils.selectOnes(sql, DrillForm.class);
        return EruptApiModel.successApi(employeeCertificateForm);
    }
}
