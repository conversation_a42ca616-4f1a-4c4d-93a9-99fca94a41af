/*
* Copyright © 2020-2035 yundingyun All rights reserved.
*/
package com.daliangang.rndpub.proxy;

import cn.hutool.core.util.ObjectUtil;
import com.daliangang.emergency.entity.EmergencyDuty;
import com.daliangang.rndpub.entity.Rectification;
import com.daliangang.rndpub.entity.Rectify;
import org.mockito.internal.util.StringUtil;
import org.springframework.stereotype.Service;
import xyz.erupt.annotation.fun.DataProxy;
import xyz.erupt.toolkit.db.EruptResultMap;
import xyz.erupt.toolkit.utils.EruptDaoUtils;

import java.util.Collection;
import java.util.Date;
import java.util.List;
import java.util.Map;

@Service
public class RectifyDataProxy implements DataProxy<Rectify> {
    @Override
    public void beforeAdd(Rectify rectify) {
       rectify.setRemark(rectify.getRemark()==null?"":rectify.getRemark());
    }

    @Override
    public void beforeUpdate(Rectify rectify) {
        rectify.setRemark(rectify.getRemark()==null?"":rectify.getRemark());
    }

    @Override
    public void afterFetch(Collection<Map<String, Object>> list) {
        List<Rectify> rectifies = EruptDaoUtils.convert(list, Rectify.class);
        for (Rectify rectify : rectifies) {
            EruptDaoUtils.updateAfterFetch(list, rectify.getId(), "remark", ObjectUtil.isEmpty(rectify.getRemark())?"":rectify.getRemark());
        }
    }
}
