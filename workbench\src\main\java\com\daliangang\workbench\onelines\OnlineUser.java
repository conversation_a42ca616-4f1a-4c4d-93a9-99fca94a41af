package com.daliangang.workbench.onelines;

import io.swagger.annotations.ApiModel;
import lombok.Data;
import org.springframework.stereotype.Service;
import xyz.erupt.annotation.Erupt;
import xyz.erupt.annotation.EruptField;
import xyz.erupt.annotation.fun.DataProxy;
import xyz.erupt.annotation.sub_erupt.Power;
import xyz.erupt.annotation.sub_field.Edit;
import xyz.erupt.annotation.sub_field.View;
import xyz.erupt.jpa.model.BaseModel;
import xyz.erupt.toolkit.db.Comment;

import javax.persistence.Entity;
import javax.persistence.Table;

@Data
@Entity
@Erupt(name = "一网协同用户信息", dataProxy = OnlineUser.OnlineUserDataProxy.class,power = @Power(add = false, delete = false, export = true, importable = false, viewDetails = false, edit = false))
@Table(name = "tb_oneline_user")
@Comment("一网协同用户信息")
@ApiModel("一网协同用户信息")
public class OnlineUser extends BaseModel {

    @EruptField(
            views = @View(title = "personUuid"),
            edit = @Edit(title = "personUuid")
    )
    private String personUuid ;

    @EruptField(
            views = @View(title = "手机号码"),
            edit = @Edit(title = "手机号码")
    )
    private String phoneNumber ;




    @EruptField(
            views = @View(title = "UUID"),
            edit = @Edit(title = "UUID")
    )
    private String uuid;

    @EruptField(
            views = @View(title = "账号名"),
            edit = @Edit(title = "账号名")
    )
    private String accountName;




    @EruptField(
            views = @View(title = "创建日期"),
            edit = @Edit(title = "创建日期")
    )
    private Long createdDate;

    @EruptField(
            views = @View(title = "部门名称"),
            edit = @Edit(title = "部门名称")
    )
    private String depName;

    @EruptField(
            views = @View(title = "部门UUID"),
            edit = @Edit(title = "部门UUID")
    )
    private String depUuid;

    @EruptField(
            views = @View(title = "显示顺序"),
            edit = @Edit(title = "显示顺序")
    )
    private Integer displayOrder;

    @EruptField(
            views = @View(title = "职务级别"),
            edit = @Edit(title = "职务级别")
    )
    private String dutyLevelName;

    @EruptField(
            views = @View(title = "职位名称"),
            edit = @Edit(title = "职位名称")
    )
    private String jobName;

    @EruptField(
            views = @View(title = "职位类型"),
            edit = @Edit(title = "职位类型")
    )
    private String jobType;

    @EruptField(
            views = @View(title = "最后修改日期"),
            edit = @Edit(title = "最后修改日期")
    )
    private Long lastModifiedDate;

    @EruptField(
            views = @View(title = "主账号"),
            edit = @Edit(title = "主账号")
    )
    private Boolean mainAccount;

    @EruptField(
            views = @View(title = "组织名称"),
            edit = @Edit(title = "组织名称")
    )
    private String orgName;

    @EruptField(
            views = @View(title = "组织UUID"),
            edit = @Edit(title = "组织UUID")
    )
    private String orgUuid;

    @EruptField(
            views = @View(title = "人员姓名"),
            edit = @Edit(title = "人员姓名")
    )
    private String personName;


    @EruptField(
            views = @View(title = "状态码"),
            edit = @Edit(title = "状态码")
    )
    private String statusCode;



    @EruptField(
            views = @View(title = "版本"),
            edit = @Edit(title = "版本")
    )
    private Integer version;


    @Service
    public static class OnlineUserDataProxy implements DataProxy<OnlineUser> {

    }
}
