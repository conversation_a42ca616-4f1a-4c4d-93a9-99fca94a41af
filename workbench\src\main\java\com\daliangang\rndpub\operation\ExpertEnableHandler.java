/*
* Copyright © 2020-2035 yundingyun All rights reserved.
*/
package com.daliangang.rndpub.operation;

import com.daliangang.rndpub.entity.Expert;
import org.springframework.stereotype.Service;
import xyz.erupt.annotation.fun.OperationHandler;
import xyz.erupt.jpa.dao.EruptDao;

import javax.annotation.Resource;
import javax.transaction.Transactional;
import java.util.List;

@Service
public class ExpertEnableHandler implements OperationHandler<Expert, Void> {

    @Resource
    private EruptDao eruptDao;
   @Override
   @Transactional
   public String exec(List<Expert> data, Void unused, String[] param) {
       Expert expert = data.get(0);
       expert.setUseState(Boolean.parseBoolean(param[0]));
       eruptDao.merge(expert);
       return null;
	}
}