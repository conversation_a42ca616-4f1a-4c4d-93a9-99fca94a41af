package com.daliangang.rndpub.controller;

import cn.hutool.core.util.RandomUtil;
import cn.hutool.json.JSONObject;
import com.daliangang.rndpub.RndpubConst;
import com.daliangang.rndpub.entity.Expert;
import com.daliangang.rndpub.entity.InspectionItemsManagement;
import com.daliangang.rndpub.entity.Inspector;
import com.google.gson.internal.LinkedTreeMap;
import org.springframework.web.bind.annotation.*;
import xyz.erupt.annotation.query.Condition;
import xyz.erupt.core.annotation.EruptRouter;
import xyz.erupt.core.controller.EruptDataController;
import xyz.erupt.core.view.EruptApiModel;
import xyz.erupt.core.view.Page;
import xyz.erupt.core.view.TableQueryVo;
import xyz.erupt.jpa.dao.EruptDao;
import xyz.erupt.toolkit.remote.RemoteProxyService;
import xyz.erupt.toolkit.remote.RemoteQuery;
import xyz.erupt.toolkit.utils.EruptDaoUtils;
import xyz.erupt.toolkit.utils.SqlUtils;

import javax.annotation.Resource;
import java.util.*;

/**
 * @Author: zengwensi
 * @Description:
 * @Date: Created in 2023/4/3 17:32
 * @Modified By
 */
@RestController
public class ProcedureController {

    @Resource
    private RemoteProxyService remoteProxyService;

    @Resource
    private EruptDao eruptDao;

    @Resource
    private EruptDataController eruptDataController ;

    /**
     * 系统查询检查对象
     * @param
     * @return
     */
    @RequestMapping("erupt-api/procedure/getCompany")
    @EruptRouter(verifyType = EruptRouter.VerifyType.LOGIN)
    public Page getCompany(@RequestBody TableQueryVo tableQueryVo){
        List<Condition> condition = tableQueryVo.getCondition();
        if(null == condition){
            condition = new ArrayList<>();
        }

        return remoteProxyService.getEruptData("main", "Enterprise",
                tableQueryVo.getPageIndex(),tableQueryVo.getPageSize(),tableQueryVo.getSort(),
                condition.toArray(new Condition[condition.size()]));
    }

    /**
     * 系统随机抽取检查对象（公司）
     * @param
     * @return
     */
    @RequestMapping("erupt-api/procedure/randomCompany")
    @EruptRouter(verifyType = EruptRouter.VerifyType.LOGIN)
    public EruptApiModel getRandomCompany(@RequestParam("count") Integer count){
        List<LinkedTreeMap> enterprises = remoteProxyService.queryEntity("main", "Enterprise", RemoteQuery.builder().multiConditions("1", "1"));
        if(enterprises.size() == 0){
            return EruptApiModel.successApi();
        }
        int randomSize =  Math.min(count,enterprises.size());
        Set<LinkedTreeMap> randomSet = RandomUtil.randomEleSet(enterprises,randomSize);
        return EruptApiModel.successApi(randomSet);
    }

    /**
     * 系统随机抽取执法专家
     * @param
     * @return
     */
    @RequestMapping("erupt-api/procedure/randomExpert")
    @EruptRouter(verifyType = EruptRouter.VerifyType.LOGIN)
    public EruptApiModel getRandomExpert(@RequestParam("count") Integer count, @RequestBody JSONObject data){
//        checkScope  根据抽取的企业类别抽取对应属性专家【检查范围】
        List<String > types = data.getBeanList("type",String.class);
        List<String > companys = data.getBeanList("company",String.class);
        if(null == types ||  types.size() == 0){
            return EruptApiModel.successApi("企业类别不能为空");
        }
        if(null == companys ||  companys.size() == 0){
            return EruptApiModel.successApi("企业名称不能为空");
        }
        StringBuilder sql = new StringBuilder("select * from tb_expert where state = '" + RndpubConst.EXPERT_STATUS_PASS
                + "' and check_scope " + SqlUtils.wrapIn(types));

        //去除规避企业
        for(String company : companys){
            sql.append(" and evade_company not like '%").append(company).append("%'");
        }

        List<Expert> experts = EruptDaoUtils.selectOnes(sql.toString() , Expert.class);
        if(experts.size() == 0){
            return EruptApiModel.successApi();
        }
        int randomSize =  Math.min(count,experts.size());
        Set<Expert> randomSet = RandomUtil.randomEleSet(experts,randomSize);
        return EruptApiModel.successApi(randomSet);
    }


    /**
     * 系统随机抽取检查人员
     * @param
     * @return
     */
    @RequestMapping("erupt-api/procedure/randomInspector")
    @EruptRouter(verifyType = EruptRouter.VerifyType.LOGIN)
    public EruptApiModel randomInspector(@RequestParam("count") Integer count){

        if(count < 2){
            return EruptApiModel.successApi("检查人员人数最少2名");
        }
        List<Inspector> inspector = EruptDaoUtils.selectOnes("select * from tb_inspector " , Inspector.class);
        if(inspector.size() == 0){
            return EruptApiModel.successApi();
        }
        int randomSize =  Math.min(count,inspector.size());
        Set<Inspector> randomSet = RandomUtil.randomEleSet(inspector,randomSize);
        return EruptApiModel.successApi(randomSet);
    }


    /**
     * 系统随机抽取检查事项
     * @param
     * @return
     */
    @RequestMapping("erupt-api/procedure/randomInspectionItems")
    @EruptRouter(verifyType = EruptRouter.VerifyType.LOGIN)
    public EruptApiModel randomInspectionItems(@RequestParam("count") Integer count){

        List<InspectionItemsManagement> inspectionItems = EruptDaoUtils.selectOnes("select * from tb_inspection_items_management ", InspectionItemsManagement.class);
        if(inspectionItems.size() == 0){
            return EruptApiModel.successApi();
        }
        int randomSize =  Math.min(count,inspectionItems.size());
        Set<InspectionItemsManagement> randomSet = RandomUtil.randomEleSet(inspectionItems,randomSize);
        return EruptApiModel.successApi(randomSet);
    }

    /**
     * 查询检查结果数据
     * @return
     */
    @GetMapping("erupt-api/procedure/queryInspectionResults/{inspectionResultsId}")
    @EruptRouter(verifyType = EruptRouter.VerifyType.LOGIN)
    public EruptApiModel queryInspectionResults(@PathVariable String inspectionResultsId){
        TableQueryVo tableQueryVo = new TableQueryVo();
        tableQueryVo.setPageIndex(1);
        tableQueryVo.setSort("");
        List<Condition> conditions = new ArrayList<>();
        tableQueryVo.setCondition(conditions);

        //检查结果
        Page inspectionResultsViewData = remoteProxyService.getEruptData("main","InspectionResultsView",
                tableQueryVo.getPageIndex(),
                1,
                tableQueryVo.getSort(),
                conditions.toArray(new Condition[conditions.size()]));
        //检查和复查历史
        Condition condition = new Condition("inspectionResultsId",inspectionResultsId);
        conditions.add(condition);
        tableQueryVo.setCondition(conditions);
        Page rectifyData = remoteProxyService.getEruptData("main","Rectify",
                tableQueryVo.getPageIndex(),
                1000,
                tableQueryVo.getSort(),
                conditions.toArray(new Condition[conditions.size()]));

        Map<String,Object> map = new HashMap<>();
        map.put("inspectionResultsView",inspectionResultsViewData.getList()) ;
        map.put("rectifies",rectifyData.getList()) ;
        return EruptApiModel.successApi(map) ;

    }
}
