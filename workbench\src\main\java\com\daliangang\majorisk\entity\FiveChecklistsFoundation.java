package com.daliangang.majorisk.entity;

import com.daliangang.majorisk.handler.MajorRiskTypeLinkByHandler;
import com.daliangang.majorisk.proxy.FiveChecklistsFoundationDataProxy;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import xyz.erupt.annotation.Erupt;
import xyz.erupt.annotation.EruptField;
import xyz.erupt.annotation.sub_erupt.Power;
import xyz.erupt.annotation.sub_field.Edit;
import xyz.erupt.annotation.sub_field.EditType;
import xyz.erupt.annotation.sub_field.Readonly;
import xyz.erupt.annotation.sub_field.View;
import xyz.erupt.annotation.sub_field.sub_edit.*;
import xyz.erupt.toolkit.db.Comment;
import xyz.erupt.toolkit.handler.EruptDictCodeChoiceFetchHandler;
import xyz.erupt.toolkit.handler.EruptDictTagFetchHandler;
import xyz.erupt.toolkit.handler.RemoteEntityChoiceFetchHandler;
import xyz.erupt.upms.model.auth.DataAuthHandler;
import xyz.erupt.upms.model.auth.DataAuthModel;

import javax.persistence.*;

/**
 * <AUTHOR>
 * @since :2023/4/4:9:57
 */

@Erupt(name = "五清单基础信息", power = @Power(add = true, delete = true, export = false, importable = true, viewDetails = true, edit = false)
        , dataProxy = FiveChecklistsFoundationDataProxy.class
//        drills = {@Drill(
//                title = "责任分工", icon = "fa fa-list-alt",
//                link = @Link(
//                        linkErupt = FiveChecklistsResponsibility.class, joinColumn = "fiveChecklistsFoundation.id"
//                )
//        )},
//        rowOperation = {
////                @RowOperation(title = "查看", icon = "", tip = "查看", eruptClass = FiveDetail.class,
////                        operationHandler = FiveDetailHandler.class, mode = RowOperation.Mode.SINGLE/*, code = TplUtils.REMOTE_ERUPT_CODE + "detail"*/),
//                @RowOperation(title = "基础清单", icon = "fa fa-tasks", /*tip = "基础清单",*/ eruptClass = FiveChecklistsFoundation.class,
//                        operationHandler = FiveChecklistsFoundationHandler.class, mode = RowOperation.Mode.SINGLE/*, code = TplUtils.REMOTE_ERUPT_CODE + "foundation"*/),
//                @RowOperation(title = "防控措施", icon = "fa fa-fire-extinguisher", /*tip = "防控措施",*/ eruptClass = FiveChecklistsPreventionAndControl.class,
//                        operationHandler = FiveChecklistsPreventionAndControlHandler.class, mode = RowOperation.Mode.SINGLE/*, code = TplUtils.REMOTE_ERUPT_CODE + "preventionandcontrol"*/),
//                @RowOperation(title = "监控检测", icon = "fa fa-area-chart", /*tip = "监控检测",*/ eruptClass = FiveChecklistsMonitor.class,
//                        operationHandler = FiveChecklistsMonitorHandler.class, mode = RowOperation.Mode.SINGLE/*, code = TplUtils.REMOTE_ERUPT_CODE + "monitor"*/),
//                @RowOperation(title = "应急处置", icon = "fa fa-ambulance", /*tip = "应急处置",*/ eruptClass = FiveChecklistsEmergencyDisposal.class,
//                        operationHandler = FiveChecklistsEmergencyDisposalHandler.class, mode = RowOperation.Mode.SINGLE/*, code = TplUtils.REMOTE_ERUPT_CODE + "mergencydisposal"*/),
//                @RowOperation(title = "word导出", icon = "",
//                        //ifExpr = "item.type=='下拉选择' && item.linkClass!=''",
//                        mode = RowOperation.Mode.BUTTON,
//                        //operationHandler = WordExportHandler.class,
//                        // type = RowOperation.Type.TPL,
//                        type = RowOperation.Type.TPL,
//                        tpl = @Tpl(path = "tpl/重大风险系统五个清单.docx",
//                                engine = Tpl.Engine.FreeMarker,
//                                tplHandler = WordExportHandler.class))
//        }

)
@Table(name = "tb_fivechecklists_foundation")
@Entity
@Getter
@Setter
@Comment("五清单基础信息")
@ApiModel("五清单基础信息")
public class FiveChecklistsFoundation extends DataAuthModel {

    @EruptField(
            views = @View(title = "风险类型"),
            edit = @Edit(title = "风险类型", type = EditType.CHOICE, search = @Search, notNull = true,
                    choiceType = @ChoiceType(fullSpan = true,
                            fetchHandler = EruptDictCodeChoiceFetchHandler.class, fetchHandlerParams = "riskType",
                            reload = true, linkBy = @LinkBy(property = "majorRisk", handler = MajorRiskTypeLinkByHandler.class)
                    )))
    @Comment("风险类型")
    @ApiModelProperty("风险类型")
    private String fiveRiskType;

    @EruptField(
            views = @View(title = "重大风险名称"),
            edit = @Edit(title = "重大风险名称", type = EditType.INPUT, search = @Search, notNull = true,
                    inputType = @InputType(fullSpan = true)))
    @Comment("重大风险名称")
    @ApiModelProperty("重大风险名称")
    private String name;


    @EruptField(
            views = @View(title = "地理位置", show = false),
            edit = @Edit(title = "地理位置", type = EditType.INPUT,notNull = true,
                    inputType = @InputType(fullSpan = true)))
    @Comment("地理位置")
    @ApiModelProperty("地理位置")
    private String geographicalLocation;

    @EruptField(
            views = @View(title = "基础信息内容", show = false),
            edit = @Edit(title = "基础信息内容", type = EditType.DIVIDE))
    @Transient
    @Comment("基础信息内容")
    @ApiModelProperty("基础信息内容")
    private String jbxx;

    @EruptField(
            views = @View(title = "所在单位"),
            edit = @Edit(title = "所在单位", readonly = @Readonly(exprHandler = DataAuthHandler.class), type = EditType.CHOICE, notNull = true,
                    choiceType = @ChoiceType(fetchHandler = RemoteEntityChoiceFetchHandler.class, fetchHandlerParams = {"main", "Enterprise", "org_code,name"})))
    @Comment("所在单位")
    @ApiModelProperty("所在单位")
    private String company;

//    @EruptField(
//            views = @View(title = "所在单位",show = false),
//            edit = @Edit(title = "所在单位", type = EditType.CHOICE,
//                    choiceType = @ChoiceType(fetchHandler = RemoteCallChoiceFetchHandler.class, fetchHandlerParams ={"main","erupt-api/get/goodsType"})))
//    @Comment("所在单位")
//    @ApiModelProperty("所在单位")
//    private String WorkUnit;

    @EruptField(
            views = @View(title = "主要负责人"),
            edit = @Edit(title = "主要负责人", type = EditType.INPUT,notNull = true,
                    inputType = @InputType))
    @Comment("主要负责人")
    @ApiModelProperty("主要负责人")
    private String principalPerson;

    @EruptField(
            views = @View(title = "联系方式", show = false),
            edit = @Edit(title = "联系方式", type = EditType.INPUT,notNull = true,
                    inputType = @InputType))
    @Comment("联系方式")
    @ApiModelProperty("联系方式")
    private String contactTel;

    @EruptField(
            views = @View(title = "单位地址", show = false),
            edit = @Edit(title = "单位地址", type = EditType.INPUT,notNull = true,
                    inputType = @InputType(fullSpan = true)))
    @Comment("单位地址")
    @ApiModelProperty("单位地址")
    private String unitAddress;

    @EruptField(
            views = @View(title = "主要致险情景", show = false),
            edit = @Edit(title = "主要致险情景", type = EditType.TAGS,notNull = true,
                    tagsType = @TagsType(fetchHandler = EruptDictTagFetchHandler.class, fetchHandlerParams = "${property}")))
    @Comment("主要致险情景")
    @ApiModelProperty("主要致险情景")
    private String majorRisk;

//    @EruptField(
//            views = @View(title = "主要致险情景1", show = false),
//            edit = @Edit(title = "主要致险情景1", type = EditType.TAGS,
//                    tagsType = @TagsType( fetchHandler = EruptDictTagFetchHandler.class, fetchHandlerParams = "majorRiskFour")))
//    @Comment("主要致险情景1")
//    @ApiModelProperty("主要致险情景1")
//    private String majorRisTest;

    @Lob
    @EruptField(
            views = @View(title = "其他备注", show = false),
            edit = @Edit(
                    title = "其他备注",
                    showBy = @ShowBy(dependField = "majorRisk", expr = "value != null && value.includes(\"其他\")"),
                    type = EditType.TEXTAREA,
                    inputType = @InputType(length = 500)
            )
    )
    private String otherRemark;

    @EruptField(
            views = @View(title = "风险失控后果及影响范围", show = false),
            edit = @Edit(title = "风险失控后果及影响范围", type = EditType.DIVIDE))
    @Transient
    @Comment("风险失控后果及影响范围")
    @ApiModelProperty("风险失控后果及影响范围")
    private String runawayConsequences;

    @EruptField(
            views = @View(title = "人员伤亡", show = false),
            edit = @Edit(title = "人员伤亡", type = EditType.CHOICE, notNull = true,
                    choiceType = @ChoiceType(fullSpan = true, fetchHandler = EruptDictCodeChoiceFetchHandler.class, fetchHandlerParams = "Casualties")))
    @Comment("人员伤亡")
    @ApiModelProperty("人员伤亡")
    private String casualties;

    @EruptField(
            views = @View(title = "财产损失", show = false),
            edit = @Edit(title = "财产损失", type = EditType.CHOICE, notNull = true,
                    choiceType = @ChoiceType(fullSpan = true, fetchHandler = EruptDictCodeChoiceFetchHandler.class, fetchHandlerParams = "PropertyLoss")))
    @Comment("财产损失")
    @ApiModelProperty("财产损失")

    private String propertyLoss;

    @EruptField(
            views = @View(title = "环境影响", show = false),
            edit = @Edit(title = "环境影响", type = EditType.CHOICE, notNull = true,
                    choiceType = @ChoiceType(fullSpan = true, fetchHandler = EruptDictCodeChoiceFetchHandler.class, fetchHandlerParams = "EnvironmentalImpact")))
    @Comment("环境影响")
    @ApiModelProperty("环境影响")
    private String environmentalImpact;

    @EruptField(
            views = @View(title = "社会影响", show = false),
            edit = @Edit(title = "社会影响", type = EditType.CHOICE, notNull = true,
                    choiceType = @ChoiceType(fullSpan = true, fetchHandler = EruptDictCodeChoiceFetchHandler.class, fetchHandlerParams = "SocialInfluence")))
    @Comment("社会影响")
    @ApiModelProperty("社会影响")
    private String socialInfluence;

    @OneToOne
    @EruptField
    @JoinColumn(name = "tb_five_detail_id")
    private FiveDetail fiveDetail;

    //    @OneToOne(cascade = CascadeType.ALL)
//    @EruptField(
//
//            edit = @Edit(title = "基础清单", type = EditType.COMBINE)
//    )
//    private FiveChecklistsFoundation fiveChecklistsFoundation;

//    @EruptField(
//            views = @View(title = "编辑模式", show = true, sortable = true),
//            edit = @Edit(title = "编辑模式", type = EditType.BOOLEAN, notNull = true, show = false))
//    @Transient
//    private Boolean edit = false;
//
//    @OneToOne(cascade = CascadeType.ALL)
//    @EruptField(edit = @Edit(title = "防控措施清单", type = EditType.COMBINE, readonly = @Readonly, showBy = @ShowBy(dependField = "edit", expr = "value != true")))
//    private FiveChecklistsPreventionAndControl fiveChecklistsPreventionAndControl;
//
//
//    @OneToOne(cascade = CascadeType.ALL)
//    @EruptField(edit = @Edit(title = "监控检测清单", type = EditType.COMBINE, readonly = @Readonly, showBy = @ShowBy(dependField = "edit", expr = "value != true")))
//    private FiveChecklistsMonitor fiveChecklistsMonitor;
//
//
//    @OneToOne(cascade = CascadeType.ALL)
//    @EruptField(edit = @Edit(title = "应急处置清单", type = EditType.COMBINE, readonly = @Readonly, showBy = @ShowBy(dependField = "edit", expr = "value != true")))
//    private FiveChecklistsEmergencyDisposal fiveChecklistsEmergencyDisposal;
//
//    @EruptField(edit = @Edit(title = "责任分工清单", type = EditType.TAB_TABLE_ADD, showBy = @ShowBy(dependField = "edit", expr = "value != true")))
//    @OneToMany(cascade = CascadeType.ALL, orphanRemoval = true)
//    @OrderBy
//    @JoinColumn(name = "tb_fivechecklists_foundation_id")
//    @Comment("责任分工清单")
//    @ApiModelProperty("责任分工清单")
//    private Set<FiveChecklistsResponsibility> file;

//    @EruptField(
//            views = @View(title = "创建人", width = "100px"),
//            edit = @Edit(title = "创建人", show = false)
//    )
//
//    private String createBy;
//
//    @EruptField(
//            views = @View(title = "创建时间", sortable = true),
//            edit = @Edit(title = "创建时间",show = false, dateType = @DateType(type = DateType.Type.DATE_TIME))
//    )
//
//    private LocalDateTime createTime;
//
//    @EruptField(
//            views = @View(title = "更新人", width = "100px"),
//            edit = @Edit(title = "更新人", show = false)
//    )
//
//    private String updateBy;
//
//    @EruptField(
//            views = @View(title = "更新时间", sortable = true),
//            edit = @Edit(title = "更新时间", show = false, dateType = @DateType(type = DateType.Type.DATE_TIME))
//    )
//
//    private LocalDateTime updateTime;
}
