package com.daliangang.majorisk.operation;

import com.daliangang.datascreen.response.WorkCondition;
import com.daliangang.datascreen.utils.DateUtil;
import com.daliangang.datascreen.utils.OrgUtils;
import com.daliangang.majorisk.entity.OrganProblemResponse;
import com.daliangang.majorisk.service.CompanyIssueService;
import com.daliangang.majorisk.service.WorkReportService;
import com.daliangang.workbench.entity.Enterprise;
import com.deepoove.poi.XWPFTemplate;
import com.deepoove.poi.config.Configure;
import com.deepoove.poi.data.TextRenderData;
import com.deepoove.poi.data.style.Style;
import com.deepoove.poi.plugin.table.LoopRowTableRenderPolicy;
import com.deepoove.poi.util.PoitlIOUtils;

import io.micrometer.core.instrument.util.StringUtils;
import lombok.SneakyThrows;
import org.apache.poi.xwpf.usermodel.*;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import xyz.erupt.annotation.sub_erupt.Tpl;
import xyz.erupt.jpa.dao.EruptDao;
import xyz.erupt.toolkit.utils.FileUtils;
import xyz.erupt.upms.model.EruptUser;
import xyz.erupt.upms.platform.EruptOption;
import xyz.erupt.upms.platform.EruptOptions;
import xyz.erupt.upms.service.EruptPlatformService;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.BufferedOutputStream;
import java.io.InputStream;
import java.io.OutputStream;
import java.net.URLEncoder;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Author: chongmenglin
 * @Date: 2024/12/6 11:44
 */
@Component
@Service
@EruptOptions({
    @EruptOption(value = "2,3,4,5,6,6.1,8,9", name = WordExportReportHandler.NEED_FILTER_GOOD, desc = "需要过滤的货种类型"),
    @EruptOption(value = "false", name = WordExportReportHandler.NEED_FILTER_ORGAN, desc = "是否需要过滤企业过滤")
})
public class WordExportReportHandler implements Tpl.TplHandler{
    public static final String NEED_FILTER_GOOD = "NEED_FILTER_GOOD";
    public static final String NEED_FILTER_ORGAN = "NEED_FILTER_ORGAN";
    @Resource
    private WorkReportService workReportService;
    @Resource
    private CompanyIssueService companyIssueService;
    @Resource
    private EruptPlatformService eruptPlatformService;
    @Resource
    private EruptDao eruptDao;


    private String generateSupervisionContent(WorkCondition workCount, boolean isPredict) {
        StringBuilder content = new StringBuilder();
        String prefix = isPredict ? "提前加强对" : "加强对";

        // 添加监管区域
        content.append(prefix)
                .append(StringUtils.isBlank(workCount.getStrengthenSupervisionArea()) ? "相关作业区域" : workCount.getStrengthenSupervisionArea())
                .append("作业监管");

        // 如果有任何作业，添加"重点检查"部分
        if (workCount.getShipNumber() > 0 || workCount.getCarNumber() > 0 || workCount.getMaintenanceNumber() > 0) {
            content.append("，重点检查");

            List<String> items = new ArrayList<>();

            // 添加船舶作业
            if (workCount.getShipNumber() > 0) {
                items.add(workCount.getShipOrgan() + "危险货物装卸船作业");
            }

            // 添加车辆作业
            if (workCount.getCarNumber() > 0) {
                items.add(workCount.getCarOrgan() + "装卸车作业");
            }

            // 添加检维修作业
            if (workCount.getMaintenanceNumber() > 0) {
                items.add(workCount.getKeyInspectionContent() + "检维修作业");
            }

            content.append(String.join("，", items))
                    .append("风险的防控措施落实情况");
        }

        return content.toString();
    }

    private String generateWorkSituationContent(WorkCondition workCount) {
        StringBuilder content = new StringBuilder();

        if (workCount.getCarNumber() + workCount.getShipNumber() == 0) {
            content.append("本日暂未上报危险货物车船装卸作业");
        } else {
            content.append("本日全市港口危险货物车船装卸作业")
                    .append(workCount.getCarNumber() + workCount.getShipNumber())
                    .append("个，主要集中在")
                    .append(workCount.getCarShipArea());

            if (workCount.getShipNumber() > 0) {
                content.append("，其中船舶装卸作业")
                        .append(workCount.getShipNumber())
                        .append("个，主要分布在")
                        .append(workCount.getShipArea())
                        .append("区域，主要作业场所为")
                        .append(workCount.getShipOrgan())
                        .append("，作业的货种涉及")
                        .append(workCount.getShipGoodsType());
            }

            if (workCount.getCarNumber() > 0) {
                content.append("；汽车装卸作业")
                        .append(workCount.getCarNumber())
                        .append("个，主要分布在")
                        .append(workCount.getCarArea())
                        .append("区域，主要作业场所为")
                        .append(workCount.getCarOrgan())
                        .append("，作业的货种涉及")
                        .append(workCount.getCarGoodsType());
            }
        }

        return content.toString();
    }
    @SneakyThrows
    @Override
    public void bindTplData(Map<String, Object> binding, String[] params) {
        String workType = params[0];
        Boolean asBool = eruptPlatformService.getOption(WordExportReportHandler.NEED_FILTER_ORGAN).getAsBool();
        try {
            // 设置默认样式
            Style style = Style.builder()
                    .buildFontFamily("仿宋_GB2312")
                    .buildFontSize(16)
                    .build();
            Map<String, Object> dataMap = new HashMap<>();

            List<Enterprise> enterprises;
            if(asBool) {
                enterprises = OrgUtils.getStartUpOrgCode();
            }else {
                EruptUser user = eruptDao.queryEntity(EruptUser.class, " account = :account", Collections.singletonMap("account", "admin"));
                enterprises = OrgUtils.getStartUpOrgCode(user);
            }
            List<String> codes = enterprises.stream()
                    .map(Enterprise::getOrgCode)
                    .collect(Collectors.toList());
            //1.获取当前时间年月日
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy年M月d日");
            dataMap.put("currentDate", new TextRenderData(sdf.format(new Date()), style));
            //2.当日作业情况
            WorkCondition currentWrokCount = workReportService.getWorkCondition(enterprises,codes, DateUtil.getCurrentDayDate());
            dataMap.put("portOperateNumber", new TextRenderData(String.valueOf(currentWrokCount.getCarNumber()+currentWrokCount.getShipNumber()),style));
            dataMap.put("carShipNumber", new TextRenderData(String.valueOf(currentWrokCount.getCarNumber()+currentWrokCount.getShipNumber()),style));

            if(currentWrokCount.getMaintenanceNumber() == 0) {
                currentWrokCount.setMaintenance("未上报");
                currentWrokCount.setMaintenanceCondition("系统暂未上报动火、受限空间等特殊作业");
            }
            else {
                currentWrokCount.setMaintenance(currentWrokCount.getMaintenanceNumber() + "个");
                currentWrokCount.setMaintenanceCondition(
                        "动火等特殊作业及检维修作业" + currentWrokCount.getMaintenanceNumber() + "个，" +
                                "主要分布在" + currentWrokCount.getMaintenanceArea() + "区域。" +
                                "动火、受限空间等作业主要作业场所为" + currentWrokCount.getMaintenanceOrgan());
            }

            dataMap.put("currentWorkCount", currentWrokCount);
            // 当日作业情况
            dataMap.put("currentWorkSituation", new TextRenderData(generateWorkSituationContent(currentWrokCount), style));
            dataMap.put("currentSupervisionContent", new TextRenderData(generateSupervisionContent(currentWrokCount, false), style));


            //2.次日作业预计情况
            //明天的年月日
            dataMap.put("tomorrowDate", sdf.format(DateUtil.getTomorrowDate()));
            WorkCondition predictWrokCount = workReportService.getWorkCondition(enterprises,codes, DateUtil.getTomorrowDate());
            dataMap.put("portOperatePredictNumber", predictWrokCount.getCarNumber()+predictWrokCount.getShipNumber());
            dataMap.put("carShipPredictNumber", predictWrokCount.getCarNumber()+predictWrokCount.getShipNumber());
            if(predictWrokCount.getMaintenanceNumber() == 0) {
                predictWrokCount.setMaintenance("未上报");
                predictWrokCount.setMaintenanceCondition("系统暂未上报动火、受限空间等特殊作业");
            }else {
                predictWrokCount.setMaintenance(currentWrokCount.getMaintenanceNumber() + "个");
                predictWrokCount.setMaintenanceCondition(
                        "动火等特殊作业及检维修作业" + predictWrokCount.getMaintenanceNumber() + "个，" +
                                "主要分布在" + predictWrokCount.getMaintenanceArea() + "区域。" +
                                "动火、受限空间等作业主要作业场所为" + predictWrokCount.getMaintenanceOrgan());
            }
            dataMap.put("predictWorkCount", predictWrokCount);
            // 次日作业情况
            dataMap.put("predictWorkSituation", new TextRenderData(generateWorkSituationContent(predictWrokCount), style));
            dataMap.put("predictSupervisionContent", new TextRenderData(generateSupervisionContent(predictWrokCount, true), style));


            //3. 表格中存在问题单元格内容
            List<OrganProblemResponse> companyIssues = companyIssueService.getCompanyIssues(codes,workType);


            // 创建表格数据
            dataMap.put("tableList", companyIssues.stream().map(issue -> {
                Map<String, String> row = new HashMap<>();
                row.put("company", issue.getCompany());
                // 将问题集合转换为字符串，每个问题占一行
                row.put("problem", String.join("\n", issue.getPromblems()));
                return row;
            }).collect(Collectors.toList()));
            // 配置表格循环策略
            LoopRowTableRenderPolicy policy = new LoopRowTableRenderPolicy();
            Configure config = Configure.builder()
                    .bind("tableList", policy)
                    .useSpringEL()
                    .build();

            dataMap.put("notReportNumber",companyIssues.size());
            // 获取模板并渲染
            InputStream inputStream = FileUtils.readInputStream("tpl/大连市港口危险货物作业工作简报.docx");
            XWPFTemplate template = XWPFTemplate.compile(inputStream, config).render(dataMap);

            // 获取文档对象
            XWPFDocument document = template.getXWPFDocument();
            // 设置表格字体
            for (XWPFTable table : document.getTables()) {
                for (XWPFTableRow row : table.getRows()) {
                    for (XWPFTableCell cell : row.getTableCells()) {
                        for (XWPFParagraph paragraph : cell.getParagraphs()) {
                            for (XWPFRun run : paragraph.getRuns()) {
                                run.setFontFamily("宋体");
                                run.setFontSize(10.5);  // 表格可以用小一点的字号
                            }
                        }
                    }
                }
            }

            // 输出文件处理
            HttpServletResponse response = ((ServletRequestAttributes) Objects.requireNonNull(RequestContextHolder.getRequestAttributes())).getResponse();
            assert response != null;
            response.reset();
            response.setContentType("application/octet-stream;charset=UTF-8");
            response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode("大连市港口危险货物作业工作简报"+ sdf.format(new Date()) + ".docx", "UTF-8"));
            OutputStream out = response.getOutputStream();
            BufferedOutputStream bos = new BufferedOutputStream(out);
            template.write(bos);
            bos.flush();
            out.flush();
            PoitlIOUtils.closeQuietlyMulti(template, bos, out);
        } catch (Exception e) {
            e.printStackTrace();
            System.out.println("模板渲染异常：" + e.getMessage());
        }
    }
}
