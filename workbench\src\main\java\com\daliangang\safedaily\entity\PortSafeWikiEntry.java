package com.daliangang.safedaily.entity;

import com.daliangang.safedaily.proxy.PortSafeWikiEntryProxy;
import lombok.Data;
import lombok.SneakyThrows;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import xyz.erupt.annotation.Erupt;
import xyz.erupt.annotation.EruptField;
import xyz.erupt.annotation.fun.DataProxy;
import xyz.erupt.annotation.sub_erupt.LinkTree;
import xyz.erupt.annotation.sub_erupt.Power;
import xyz.erupt.annotation.sub_erupt.RowOperation;
import xyz.erupt.annotation.sub_field.Edit;
import xyz.erupt.annotation.sub_field.EditType;
import xyz.erupt.annotation.sub_field.Readonly;
import xyz.erupt.annotation.sub_field.View;
import xyz.erupt.annotation.sub_field.sub_edit.AttachmentType;
import xyz.erupt.annotation.sub_field.sub_edit.ReferenceTreeType;
import xyz.erupt.annotation.sub_field.sub_edit.Search;
import xyz.erupt.jpa.dao.EruptDao;
import xyz.erupt.jpa.model.MetaModel;
import xyz.erupt.jpa.model.MetaModelVo;
import xyz.erupt.toolkit.attachment.AttachmentDownloadHandler;
import xyz.erupt.toolkit.utils.AssertUtils;
import xyz.erupt.toolkit.utils.FileUtils;

import javax.annotation.Resource;
import javax.persistence.Entity;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import java.io.File;
import java.util.StringTokenizer;

@Erupt(name = "港口安全知识库文档", importTruncate = true, power = @Power(importable = true),
        linkTree = @LinkTree(field = "wikiDir", dependNode = true),
        dataProxy = PortSafeWikiEntryProxy.class,
        rowOperation = {
        @RowOperation(title = "下载附件", icon = "fa fa-download", operationHandler = AttachmentDownloadHandler.class, mode = RowOperation.Mode.SINGLE),
})
@Component
@Entity
@Data
@Table(name = "tb_port_safe_wiki_entry")
public class PortSafeWikiEntry extends MetaModel {

    @ManyToOne
    @EruptField(views = @View(title = "所属目录", column = "name", show = false),
            edit = @Edit(title = "所属目录", type = EditType.REFERENCE_TREE, show = true, readonly = @Readonly,
                    referenceTreeType = @ReferenceTreeType(pid = "parent.id")))
    private PortSafeWikiDir wikiDir;

    @EruptField(views = @View(title = "文档名称"), edit = @Edit(title = "文档名称", show = false, search = @Search(vague = true)))
    private String name;

    @EruptField(views = @View(title = "文档附件"),
            edit = @Edit(title = "文档附件", notNull = true,
                    type = EditType.ATTACHMENT,
                    attachmentType = @AttachmentType(type = AttachmentType.Type.BASE, maxLimit = 1,fileTypes = {".pdf"})))
    private String file;


    @RestController
    public static class PortSafeWikiEntryInitController {

        @Resource
        private EruptDao eruptDao;

        @RequestMapping("/erupt-api/data/init-value/PortSafeWikiEntry")
        public PortSafeWikiEntry initValue(@RequestParam Long linkTreeVal) {
            PortSafeWikiDir dir = eruptDao.queryEntity(PortSafeWikiDir.class, "id=" + linkTreeVal);
            AssertUtils.notNull(dir, "目录不存在");
            PortSafeWikiEntry entry = new PortSafeWikiEntry();
            entry.setWikiDir(dir);
            return entry;
        }
    }

    @SneakyThrows
    public static void main(String[] args) {
        File dir = new File("/Users/<USER>/Documents/公司日常管理/03 公司项目相关/大连港危险货物港区重大安全风险管控平台升级改造项目/系统初始导入数据/法律法规库");
        for (File subDir : dir.listFiles()) {
            if (subDir.getName().startsWith(".")) continue;
            for (File file : subDir.listFiles()) {
                System.out.println(subDir.getName() + "\t" + file.getName().replace(".pdf", "") + "\t" + "/法律法规库/" + subDir.getName() + "/" + file.getName());
            }
        }
    }
}
