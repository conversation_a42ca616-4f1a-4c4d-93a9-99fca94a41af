package com.daliangang.statistics.handler;

import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.stereotype.Component;
import xyz.erupt.bi.fun.EruptBiHandler;
import xyz.erupt.core.util.EruptSpringUtil;
import xyz.erupt.upms.model.EruptOrg;
import xyz.erupt.upms.service.EruptUserService;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * 双随机-检查清单
 * @Author: zengwensi
 * @Description:
 * @Date: Created in 2023/5/10 13:54
 * @Modified By
 */
@Component
public class RndpubInspectChecklistHandler implements EruptBiHandler {

    @Resource
    private CommonHandler commonHandler;
    @Override
    public String exprHandler(String param, Map<String, Object> condition, String expr) {

        EruptOrg eruptOrg = EruptSpringUtil.getBean(EruptUserService.class).getCurrentEruptUser().getEruptOrg();
        expr = commonHandler.exprHandlerSearchDate(param,condition,expr);
        if(null != eruptOrg){
            String orgcode = eruptOrg.getCode();
            return (expr.replaceAll("#ORGCODE",orgcode));
        }

//        expr = commonHandler.exprHandlerAuthority(param,condition,expr);
        return expr ;
    }

    @Override
    public void resultHandler(String param, Map<String, Object> condition, List<Map<String, Object>> result) {

    }

    @Override
    public void exportHandler(String param, Map<String, Object> condition, Workbook workbook) {

    }
}
