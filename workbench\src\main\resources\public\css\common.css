html,body{
	padding:0px;
	margin:0px;
	height:100%;
	width:100%;
}
.wrap{
display:flex;
justify-content: center;
align-items: center;
background-image: url('../img/bg.jpg');
background-size: cover;
}
.title{
font-family: "阿里汉仪智能黑体";
font-size:28px; font-weight: bold;
width:100%; text-align: center;
}
.wrap{
width:100%;
height: 100%;
}
.wrapMain {
width:80%;
display: flex;
justify-content: center;
flex-direction: column;

}
.wrapMain ul{
display:flex;
flex-flow:row wrap;
margin:40px auto;
width:100%;
padding:0px;

}
.wrapMain li{
width:29%;
height:15vh;
margin:20px 2%;
cursor: pointer;
}
.wrapMain .bg{
position:absolute;
width:100px; height:100px;
z-index:-1;
left:0p; top:0px;
}
.item{
display:flex;
justify-content: center;
/* align-items: center; */
color:#fff;
flex-direction: column;
text-align:left;
padding:20px 0px;

}
.item .info{
margin:0px 20px;
}
.wrapMain ul li .name{
font-size:20px;
margin-bottom: 20px;
font-weight: bold;
}
.link{
font-size:15px;
cursor: pointer;
}
