/*
* Copyright © 2020-2035 yundingyun All rights reserved.
*/
package com.daliangang.workbench.operation;

import cn.hutool.json.JSON;
import cn.hutool.json.JSONUtil;
import com.daliangang.workbench.entity.Enterprise;
import org.springframework.stereotype.Service;
import xyz.erupt.annotation.fun.OperationHandler;
import xyz.erupt.core.service.EruptCoreService;
import xyz.erupt.core.view.EruptModel;

import java.util.List;

@Service
public class EnterpriseInfoImportAttachedCertificateHandler implements OperationHandler<Enterprise, List> {
   @Override
   public String exec(List<Enterprise> data, List unused, String[] param) {
       EruptModel enterpriseCertificate = EruptCoreService.getEruptView("EnterpriseCertificate");
       JSON parse = JSONUtil.parse(enterpriseCertificate);

       String url = "this.importableExcel("+parse+")";
       return url;
	}
}
