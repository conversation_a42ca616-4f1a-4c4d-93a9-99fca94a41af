package com.daliangang.emergency.entity;

import com.daliangang.core.CompanyRenderHandler;
import com.daliangang.emergency.proxy.AccidentInformationDataProxy;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import xyz.erupt.annotation.Erupt;
import xyz.erupt.annotation.EruptField;
import xyz.erupt.annotation.expr.ExprBool;
import xyz.erupt.annotation.sub_erupt.Power;
import xyz.erupt.annotation.sub_field.Edit;
import xyz.erupt.annotation.sub_field.EditType;
import xyz.erupt.annotation.sub_field.Readonly;
import xyz.erupt.annotation.sub_field.View;
import xyz.erupt.annotation.sub_field.sub_edit.*;
import xyz.erupt.toolkit.db.Comment;
import xyz.erupt.toolkit.handler.EruptDictTagFetchHandler;
import xyz.erupt.toolkit.handler.RemoteCallTagFetchHandler;
import xyz.erupt.toolkit.handler.RemoteEntityChoiceFetchHandler;
import xyz.erupt.toolkit.handler.SqlChoiceFetchHandler;
import xyz.erupt.upms.model.auth.DataAuthHandler;
import xyz.erupt.upms.model.auth.DataAuthModel;

import javax.persistence.Entity;
import javax.persistence.Lob;
import javax.persistence.Table;
import javax.persistence.Transient;
import java.util.Date;

/**
 * <AUTHOR>
 * @since :2023/4/26:10:23
 */
@Erupt(name = "事故信息", power = @Power(add = false, delete = true, export = false, importable = true, viewDetails = true, edit = true)
        , dataProxy = AccidentInformationDataProxy.class
        , rowOperation = {})
@Table(name = "tb_accident_information")
@Entity
@Getter
@Setter
@Comment("事故信息")
@ApiModel("事故信息")
public class AccidentInformation extends DataAuthModel {

    @EruptField(
            views = @View(title = "企业名称"),
            edit = @Edit(title = "企业名称", search = @Search(ifRender = @ExprBool(exprHandler = CompanyRenderHandler.class)),
                    readonly = @Readonly(exprHandler = DataAuthHandler.class),type = EditType.CHOICE, notNull = true,
                    choiceType = @ChoiceType(fullSpan = true,reload = true,fetchHandler = RemoteEntityChoiceFetchHandler.class, fetchHandlerParams ={"main","Enterprise","org_code,name"})))
    @Comment("企业名称")
    @ApiModelProperty("企业名称")
    private String company;

    @EruptField(
            views = @View(title = "事故发生时间"),
            edit = @Edit(title = "事故发生时间", type = EditType.DATE, notNull = true,
                    dateType = @DateType))
    @Comment("事故发生时间")
    @ApiModelProperty("事故发生时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date time;



//    @EruptField(
//            views = @View(title = "所属港区"),
//            edit = @Edit(title = "所属港区", type = EditType.INPUT, notNull = true,
//                    inputType = @InputType))
//    @Comment("所属港区")
//    @ApiModelProperty("所属港区")
//    private String portArea;
    @EruptField(
            views = @View(title = "所属港区"),
            edit = @Edit(title = "所属港区", type = EditType.CHOICE, notNull = true, readonly = @Readonly,search = @Search,
                    choiceType = @ChoiceType(fetchHandler = SqlChoiceFetchHandler.class, fetchHandlerParams = {"select id,portarea_name from tb_port_area"})))
    @Comment("所属港区")
    @ApiModelProperty("所属港区")
    private String portArea;

    @EruptField(
            views = @View(title = "所属港区名称",show = false),
            edit = @Edit(title = "所属港区名称", type = EditType.INPUT, show = false,
                    inputType = @InputType))
    @Comment("所属港区名称")
    @ApiModelProperty("所属港区名称")
    private String  portAreaName;

    @EruptField(
            views = @View(title = "事故影响范围（m）"),
            edit = @Edit(title = "事故影响范围（m）", type = EditType.NUMBER, notNull = true,
                    numberType = @NumberType))
    @Comment("事故影响范围（m）")
    @ApiModelProperty("事故影响范围（m）")
    private Double  influenceScope;

    @EruptField(
            views = @View(title = "事故类型"),
            edit = @Edit(title = "事故类型", type = EditType.TAGS, search = @Search(vague = true), notNull = true,
                    tagsType = @TagsType(allowExtension = false, fetchHandler = EruptDictTagFetchHandler.class, fetchHandlerParams = "accidentType")))
    @Comment("事故类型")
    @ApiModelProperty("事故类型")
    private String accidentType;




    @EruptField(
            views = @View(title = "涉及货种"),
            edit = @Edit(title = "涉及货种", type = EditType.TAGS,
                    tagsType  = @TagsType(allowExtension = false, fetchHandler = RemoteCallTagFetchHandler.class, fetchHandlerParams ={"main","erupt-api/get/emergencyWikiName"})))
    @Comment("涉及货种")
    @ApiModelProperty("涉及货种")
    private String goodsName;

    @Lob
    @EruptField(
            views = @View(title = "事故地点描述"),
            edit = @Edit(title = "事故地点描述", type = EditType.TEXTAREA,
                    inputType = @InputType))
    @Comment("事故地点描述")
    @ApiModelProperty("事故地点描述")
    private String placeDescription;


    @EruptField(
            views = @View(title = "事故地点", show = false),
            edit = @Edit(title = "事故地点", type = EditType.MAP, show = true))
    @Comment("事故地点")
    @ApiModelProperty("事故地点")
    @Lob
    private String map;


    @EruptField(
            views = @View(title = "企业名称",show = false),
            edit = @Edit(title = "企业名称", type = EditType.INPUT,show = false,
                    inputType = @InputType))
    @Comment("企业名称")
    @Transient
    @ApiModelProperty("企业名称")
    private String companyName;

}
