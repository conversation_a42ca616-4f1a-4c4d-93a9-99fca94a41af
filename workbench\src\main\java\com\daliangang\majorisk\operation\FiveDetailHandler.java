package com.daliangang.majorisk.operation;

import com.daliangang.majorisk.entity.FiveChecklistsFoundation;
import com.daliangang.majorisk.entity.FiveDetail;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import xyz.erupt.annotation.fun.OperationHandler;
import xyz.erupt.core.annotation.EruptRouter;
import xyz.erupt.toolkit.db.EruptResultMap;

import javax.annotation.Resource;
import javax.transaction.Transactional;
import java.util.List;

/**
 * <AUTHOR>
 * @since :2023/4/14:16:34
 */
@Service
public class FiveDetailHandler implements OperationHandler<FiveChecklistsFoundation, FiveDetail> {



    @Override
    public String exec(List<FiveChecklistsFoundation> data, FiveDetail fiveDetail, String[] param) {
        return null;
    }

    @RestController
    @Transactional
    public static class FiveDetailController {

        @Resource
        private FiveChecklistsEmergencyDisposalHandler fiveChecklistsEmergencyDisposalHandler;

        @Resource
        private FiveChecklistsMonitorHandler fiveChecklistsMonitorHandler;

        @Resource
        private FiveChecklistsPreventionAndControlHandler fiveChecklistsPreventionAndControlHandler;

        @Resource
        private FiveChecklistsFoundationHandler fiveChecklistsFoundationHandler;


        @RequestMapping("erupt-api/data/FiveChecklistsFoundation/operator/detail")
        @EruptRouter(verifyType = EruptRouter.VerifyType.LOGIN)
        public String recreate(@RequestBody EruptResultMap reuquest) {

            return null;
        }

//        @RequestMapping("/erupt-api/data/FiveDetail/{id}")
//        @EruptRouter(verifyType = EruptRouter.VerifyType.LOGIN)
//        public FiveDetail initValue(@PathVariable("id") Long id) {
//            FiveDetail fiveDetail = EruptSpringUtil.getBean(FiveDetail.class);
//
//            // 基础信息清单
//            FiveChecklistsFoundation fiveChecklistsFoundation = fiveChecklistsFoundationHandler.detail(id);
//            // 责任分工清单
//
//            // 防控措施清单
//            FiveChecklistsPreventionAndControl fiveChecklistsPreventionAndControl = fiveChecklistsPreventionAndControlHandler.detail(id);
//            // 监控检测清单
//            FiveChecklistsMonitor fiveChecklistsMonitor = fiveChecklistsMonitorHandler.detail(id);
//            // 应急处置清单
//            FiveChecklistsEmergencyDisposal fiveChecklistsEmergencyDisposal = fiveChecklistsEmergencyDisposalHandler.detail(id);
//
//            fiveDetail.setFiveChecklistsFoundation(fiveChecklistsFoundation);
//            fiveDetail.setFiveChecklistsPreventionAndControl(fiveChecklistsPreventionAndControl);
//            fiveDetail.setFiveChecklistsEmergencyDisposal(fiveChecklistsEmergencyDisposal);
//            fiveDetail.setFiveChecklistsMonitor(fiveChecklistsMonitor);
//            return fiveDetail;
//        }
    }

}
