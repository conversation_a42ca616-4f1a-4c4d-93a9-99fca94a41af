package com.daliangang.datascreen.utils;

import io.github.classgraph.ClassGraph;
import io.github.classgraph.ScanResult;

import java.util.HashMap;
import java.util.Map;

public class ClassNameUtil {
    // 类名到全限定类名的映射
    private static final Map<String, String> classNameToFullNameMap = new HashMap<>();
    static {
        try (ScanResult scanResult = new ClassGraph()
                .acceptPackages("com.daliangang") // 只接受 com.daliangang 包路径
                .scan()) {

            scanResult.getAllClasses().forEach(classInfo -> {
                String simpleName = classInfo.getSimpleName();
                String fullName = classInfo.getName();
                classNameToFullNameMap.put(simpleName, fullName);
            });
        }
    }
    /**
     * 根据类名获取全限定类名
     * @param simpleClassName 类名（不包含包名）
     * @return 全限定类名（字符串）
     * @throws ClassNotFoundException 如果类名不存在
     */
    public static String getFullClassName(String simpleClassName) throws ClassNotFoundException {
        String fullName = classNameToFullNameMap.get(simpleClassName);
        if (fullName == null) {
            throw new ClassNotFoundException("Class not found for name: " + simpleClassName);
        }
        return fullName;
    }

}
