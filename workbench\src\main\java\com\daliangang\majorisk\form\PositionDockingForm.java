package com.daliangang.majorisk.form;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import xyz.erupt.annotation.EruptField;
import xyz.erupt.annotation.sub_field.Edit;
import xyz.erupt.annotation.sub_field.EditType;
import xyz.erupt.annotation.sub_field.View;
import xyz.erupt.annotation.sub_field.sub_edit.InputType;
import xyz.erupt.toolkit.db.Comment;

/**
 * <AUTHOR>
 * @since :2023/5/24:17:41
 */
@Data
public class PositionDockingForm {


    @Comment("本系统位置id")
    @ApiModelProperty("本系统位置id")
    private Long bId;


    @Comment("位置类型")
    @ApiModelProperty("位置类型")
    private String positionType;


    @Comment("位置名称")
    @ApiModelProperty("位置名称")
    private String positionName;


    @Comment("三方系统位置id")
    @ApiModelProperty("三方系统位置id")
    private String positionId;
}
