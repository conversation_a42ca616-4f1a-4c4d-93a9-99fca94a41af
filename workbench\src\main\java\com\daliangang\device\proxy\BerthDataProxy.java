/*
 * Copyright © 2020-2035 yundingyun All rights reserved.
 */
package com.daliangang.device.proxy;

import cn.hutool.json.JSONObject;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.daliangang.core.DaliangangContext;
import com.daliangang.device.entity.Berth;
import com.daliangang.device.entity.PositionDocking;
import com.daliangang.device.form.DockingForm;
import com.daliangang.device.operation.PositionDockingHttpHandler;
import com.google.gson.Gson;
import com.google.gson.internal.LinkedTreeMap;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import xyz.erupt.annotation.fun.DataProxy;
import xyz.erupt.common.http.EruptRemoteUtils;
import xyz.erupt.core.config.GsonFactory;
import xyz.erupt.core.util.EruptSpringUtil;
import xyz.erupt.jpa.dao.EruptDao;
import xyz.erupt.toolkit.remote.RemoteProxyService;
import xyz.erupt.toolkit.remote.RemoteQuery;
import xyz.erupt.toolkit.utils.EruptDaoUtils;
import xyz.erupt.upms.service.EruptPlatformService;

import javax.annotation.Resource;
import javax.transaction.Transactional;
import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
@Slf4j
public class BerthDataProxy implements DataProxy<Berth> {
    @Resource
    private RemoteProxyService remoteProxyService;
    @Resource
    private PositionDockingHttpHandler positionDockingHttpHandler;
    @Resource
    private EruptDao eruptDao;

    @Resource
    private EruptPlatformService eruptPlatformService ;

    public static final String IS_PUSH = "IS_PUSH" ;

    @Override
    public void addBehavior(Berth berth) {
        this.changePortAreaValue(berth);
    }

    @Override
    public void editBehavior(Berth berth) {
        this.changePortAreaValue(berth);
    }

    private void changePortAreaValue(Berth berth){
        if (ObjectUtils.isNotEmpty(berth.getCompany())) {
            List<LinkedTreeMap> enterprise = remoteProxyService.queryEntity("Enterprise", RemoteQuery.builder().condition("orgCode", berth.getCompany()));
            if (ObjectUtils.isNotEmpty(enterprise)) {
                //  Integer portArea1 = (int) Float.parseFloat((String) enterprise.get(0).get("portArea"));
                //   String portArea = String.valueOf(enterprise.get(0).get("portArea"));
                String portArea=new BigDecimal(enterprise.get(0).get("portArea").toString()).stripTrailingZeros().toPlainString();
                berth.setPortArea(portArea);
            }
        }
    }

    @Override
    public void excelImport(Object workbook) {
//        EruptDaoUtils.truncate(Berth.class);
    }

    @Transactional
    @Override
    public void afterAdd(Berth berth) {
        String isPush = eruptPlatformService.getOption(BerthDataProxy.IS_PUSH).getAsString() ;
        if (isPush.equals("true")) {

            if (StringUtils.isNotEmpty(berth.getOrgCode()) && ObjectUtils.isEmpty(berth.getPositionId())) {
                String token = DaliangangContext.getToken();
                positionDockingHttpHandler.AddHttpDocking(berth, "2", berth.getOrgCode(), token);
                log.info("推送泊位数据成功 ->  token=" + token);

            }
        }
        // 保存导入的三方地点关系
        if (ObjectUtils.isNotEmpty(berth.getPositionId())) {
            // 删除中间表数据
            String sql1 = "delete from tb_position_docking where position_type = '2' and b_id = "+berth.getId();
            EruptDaoUtils.updateNoForeignKeyChecks(sql1);

            PositionDocking positionDocking = EruptSpringUtil.getBean(PositionDocking.class);
            positionDocking.setBId(berth.getId());
            positionDocking.setPositionId(berth.getPositionId());
            positionDocking.setPositionType("2");
            eruptDao.merge(positionDocking);

        }

        JSONObject inputData = new JSONObject();
        inputData.set("clazz", "Berth");
        inputData.set("insertData",berth);
        EruptRemoteUtils.doPost(remoteProxyService.getMainServerUrl() + "/erupt-api/exchange/data/exchange", inputData, Map.class);

    }

    @Override
    public void afterUpdate(Berth berth) {
        String isPush = eruptPlatformService.getOption(BerthDataProxy.IS_PUSH).getAsString() ;
        if (isPush.equals("true")) {
            if (StringUtils.isNotEmpty(berth.getOrgCode())) {
                String token = DaliangangContext.getToken();
                positionDockingHttpHandler.updateHttpDocking(berth, "2", berth.getOrgCode(), berth.getId(), token);
                log.info("推送修改泊位数据成功 ->  token=" + token);

            }
        }
        JSONObject inputData = new JSONObject();
        inputData.set("clazz", "Berth");
        inputData.set("insertData",berth);
        EruptRemoteUtils.doPost(remoteProxyService.getMainServerUrl() + "/erupt-api/exchange/data/exchange", inputData, Map.class);
    }

    @Override
    public void beforeAdd(Berth berth) {
        if (ObjectUtils.isNotEmpty(berth.getPositionId())) {
            // 数据转换
            Gson gson = GsonFactory.getGson();
            Map map =new HashMap();
            JSONArray objects = JSON.parseArray(berth.getMap());
            // JSONObject jsonObject = JSON.parseObject(warehouse.getMap());
            map.put("map", objects);
            String json = JSON.toJSONString(map);
            DockingForm positionDockingForm = gson.fromJson(json, DockingForm.class);
            String listToJsonString = "";
            // 处理风控数据中经纬度数据
            // 判断是否为圆
            if (positionDockingForm.getMap().get(0).getRy() != 0.0) {
                Map mapY = new HashMap();
                mapY.put("type","circle");
                mapY.put("lng",positionDockingForm.getMap().get(0).getHt().get(0).getLng());
                mapY.put("lat",positionDockingForm.getMap().get(0).getHt().get(0).getLat());
                mapY.put("radius",positionDockingForm.getMap().get(0).getRy());
                listToJsonString = gson.toJson(mapY);
            } else {
                Map mapD = new HashMap();
                mapD.put("type","polygon");
                mapD.put("points",positionDockingForm.getMap().get(0).getHt());
                listToJsonString = gson.toJson(mapD);
            }
            berth.setMap(listToJsonString);
        }
    }

    @Override
    public void afterDelete(Berth berth) {
        String token = DaliangangContext.getToken();
        positionDockingHttpHandler.deleteHttpDocking(berth, "2", berth.getOrgCode(), berth.getId(), token);
    }


    //    @Override
//    public String beforeFetch(List<Condition> conditions) {
//        AtomicReference<String> returnStr = new AtomicReference<>(" 1 = 1 ") ;
//        //所属码头查询条件
//        Optional<Condition> checkObjectFirst = conditions.stream().filter(condition ->
//                "wharf1".equals(condition.getKey())
//        ).findFirst();
//        checkObjectFirst.ifPresent( f ->{
//            returnStr.set(returnStr.get()+"and wharf  =" + f.getValue() );
//            conditions.remove(f) ;
//        });
//        return returnStr.get();
//    }
}
