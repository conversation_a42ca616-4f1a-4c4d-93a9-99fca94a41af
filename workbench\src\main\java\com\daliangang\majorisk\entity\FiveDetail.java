package com.daliangang.majorisk.entity;

import com.daliangang.majorisk.operation.*;
import com.daliangang.majorisk.proxy.FiveChecklistsFoundationDataProxy;
import com.daliangang.majorisk.proxy.FiveChecklistsResponsibilityDataProxy;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.BeanUtils;
import xyz.erupt.annotation.Erupt;
import xyz.erupt.annotation.EruptField;
import xyz.erupt.annotation.fun.DataProxy;
import xyz.erupt.annotation.sub_erupt.*;
import xyz.erupt.annotation.sub_field.Edit;
import xyz.erupt.annotation.sub_field.EditType;
import xyz.erupt.annotation.sub_field.Readonly;
import xyz.erupt.annotation.sub_field.View;
import xyz.erupt.annotation.sub_field.sub_edit.ChoiceType;
import xyz.erupt.annotation.sub_field.sub_edit.InputType;
import xyz.erupt.annotation.sub_field.sub_edit.ShowBy;
import xyz.erupt.core.module.MetaUserinfo;
import xyz.erupt.core.util.EruptSpringUtil;
import xyz.erupt.jpa.dao.EruptDao;
import xyz.erupt.toolkit.db.Comment;
import xyz.erupt.toolkit.handler.RemoteEntityChoiceFetchHandler;
import xyz.erupt.toolkit.remote.RemoteProxyService;
import xyz.erupt.toolkit.utils.EruptCompUtils;
import xyz.erupt.toolkit.utils.EruptDaoUtils;
import xyz.erupt.toolkit.utils.NotifyUtils;
import xyz.erupt.toolkit.utils.TplUtils;
import xyz.erupt.upms.model.auth.DataAuthHandler;
import xyz.erupt.upms.model.auth.DataAuthModel;

import javax.persistence.*;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @since :2023/4/14:16:33
 */
@Erupt(name = "五清单详情", power = @Power(add = true, delete = true, export = false, importable = true, viewDetails = true, edit = false)
        , dataProxy = FiveDetail.class,orderBy = "createTime desc",
        drills = {@Drill(
                title = "责任分工", icon = "fa fa-list-alt", position = 3,
                link = @Link(
                        linkErupt = FiveChecklistsResponsibility.class, joinColumn = "fiveDetail.id"
                )
        )},
        rowOperation = {
//                @RowOperation(title = "查看", icon = "", tip = "查看", eruptClass = FiveDetail.class,
//                        operationHandler = FiveDetailHandler.class, mode = RowOperation.Mode.SINGLE/*, code = TplUtils.REMOTE_ERUPT_CODE + "detail"*/),
                @RowOperation(title = "基础清单", icon = "fa fa-tasks", /*tip = "基础清单",*/ eruptClass = FiveChecklistsFoundation.class,
                        operationHandler = FiveChecklistsFoundationHandler.class, mode = RowOperation.Mode.SINGLE, code = TplUtils.REMOTE_ERUPT_CODE + "foundation"),
                @RowOperation(title = "防控措施", icon = "fa fa-fire-extinguisher", /*tip = "防控措施",*/ eruptClass = FiveChecklistsPreventionAndControl.class,
                        operationHandler = FiveChecklistsPreventionAndControlHandler.class, mode = RowOperation.Mode.SINGLE, code = TplUtils.REMOTE_ERUPT_CODE + "preventionandcontrol"),
                @RowOperation(title = "监测监控", icon = "fa fa-area-chart", /*tip = "监测监控",*/ eruptClass = FiveChecklistsMonitor.class,
                        operationHandler = FiveChecklistsMonitorHandler.class, mode = RowOperation.Mode.SINGLE, code = TplUtils.REMOTE_ERUPT_CODE + "monitor"),
                @RowOperation(title = "应急处置", icon = "fa fa-ambulance", /*tip = "应急处置",*/ eruptClass = FiveChecklistsEmergencyDisposal.class,
                        operationHandler = FiveChecklistsEmergencyDisposalHandler.class, mode = RowOperation.Mode.SINGLE, code = TplUtils.REMOTE_ERUPT_CODE + "mergencydisposal"),
                @RowOperation(title = "word导出", icon = "fa fa-arrow-up",
                        //ifExpr = "item.type=='下拉选择' && item.linkClass!=''",
                        mode = RowOperation.Mode.MULTI,

                        //operationHandler = WordExportHandler.class
                        isDown = true,
                        type = RowOperation.Type.TPL,
                        tpl = @Tpl(path = "tpl/qy.docx",
                                engine = Tpl.Engine.FreeMarker,
                                tplHandler = WordExportHandler.class)
                )
        }

)
@Entity
@Getter
@Setter
@Comment("五清单详情")
@ApiModel("五清单详情")
@Table(name = "tb_five_detail")
public class FiveDetail extends DataAuthModel implements DataProxy<FiveDetail> {

    @EruptField(
            views = @View(title = "重大风险名称"),
            edit = @Edit(title = "重大风险名称", show = false, type = EditType.INPUT,// notNull = false,
                    inputType = @InputType(fullSpan = true)))
    @Comment("重大风险名称")
    @ApiModelProperty("重大风险名称")
    private String riskName;

    @EruptField(
            views = @View(title = "所在单位"),
            edit = @Edit(title = "所在单位",  readonly = @Readonly(exprHandler = DataAuthHandler.class), type = EditType.CHOICE, show = false,//notNull = true,
                    choiceType = @ChoiceType(fetchHandler = RemoteEntityChoiceFetchHandler.class, fetchHandlerParams = {"main", "Enterprise", "org_code,name"})))
    @Comment("所在单位")
    @ApiModelProperty("所在单位")
    private String company;

    @EruptField(
            views = @View(title = "编辑模式", show = false, sortable = true),
            edit = @Edit(title = "编辑模式", type = EditType.BOOLEAN, notNull = true, show = false))
    @Transient
    private Boolean edit = false;

    @OneToOne(cascade = CascadeType.ALL)
    @EruptField(edit = @Edit(title = "基础清单", type = EditType.COMBINE/*, readonly = @Readonly, showBy = @ShowBy(dependField = "edit", expr = "value != true")*/))
    private FiveChecklistsFoundation fiveChecklistsFoundation;

    @OneToOne(cascade = CascadeType.ALL)
    @EruptField(edit = @Edit(title = "防控措施清单", type = EditType.COMBINE, /*readonly = @Readonly,*/ showBy = @ShowBy(dependField = "edit", expr = "value != true")))
    private FiveChecklistsPreventionAndControl fiveChecklistsPreventionAndControl;


    @OneToOne(cascade = CascadeType.ALL)
    @EruptField(edit = @Edit(title = "监测监控清单", type = EditType.COMBINE, /*readonly = @Readonly,*/ showBy = @ShowBy(dependField = "edit", expr = "value != true")))
    private FiveChecklistsMonitor fiveChecklistsMonitor;

    @OneToOne(cascade = CascadeType.ALL)
    @EruptField(edit = @Edit(title = "应急处置清单", type = EditType.COMBINE, /*readonly = @Readonly,*/ showBy = @ShowBy(dependField = "edit", expr = "value != true")))
    private FiveChecklistsEmergencyDisposal fiveChecklistsEmergencyDisposal;

    @EruptField(edit = @Edit(title = "责任分工清单", type = EditType.TAB_TABLE_ADD, showBy = @ShowBy(dependField = "edit", expr = "value != true")))
    @OneToMany(cascade = CascadeType.ALL, orphanRemoval = true)
    @OrderBy
    @JoinColumn(name = "tb_five_detail_id")
    @Comment("责任分工清单")
    @ApiModelProperty("责任分工清单")
    private Set<FiveChecklistsResponsibility> fiveChecklistsResponsibilities;

    @Override
    public void addBehavior(FiveDetail fiveDetail) {
        fiveDetail.setEdit(true);

       fiveDetail.setFiveChecklistsFoundation(new FiveChecklistsFoundation());
       // fiveDetail.setRiskName(fiveDetail.getRiskName());
        fiveDetail.getFiveChecklistsFoundation().setOrgCode(fiveDetail.getOrgCode());
        fiveDetail.getFiveChecklistsFoundation().setCompany(fiveDetail.getOrgCode());
    }

    @Override
    public void beforeAdd(FiveDetail fiveDetail) {

        fiveDetail.setRiskName(fiveDetail.getFiveChecklistsFoundation().getName());
        fiveDetail.setOrgCode(fiveDetail.getFiveChecklistsFoundation().getCompany());
        fiveDetail.setCompany(fiveDetail.getOrgCode());
        fiveDetail.fiveChecklistsFoundation.setOrgCode(fiveDetail.getOrgCode());

        fiveDetail.fiveChecklistsEmergencyDisposal.setOrgCode(fiveDetail.getOrgCode());
        fiveDetail.fiveChecklistsEmergencyDisposal.setCreateTime(fiveDetail.getCreateTime());
        FiveChecklistsEmergencyDisposal fiveChecklistsEmergencyDisposal1 = EruptDaoUtils.selectOne("select * from tb_fivechecklists_mergencydisposal where org_code='" + fiveDetail.getOrgCode()+"' ORDER BY id DESC LIMIT 0,1", FiveChecklistsEmergencyDisposal.class);
        if (ObjectUtils.isNotEmpty(fiveChecklistsEmergencyDisposal1)) {
            fiveDetail.fiveChecklistsEmergencyDisposal.setEmergencyPlan(fiveChecklistsEmergencyDisposal1.getEmergencyPlan()!=null?fiveChecklistsEmergencyDisposal1.getEmergencyPlan():null);
            fiveDetail.fiveChecklistsEmergencyDisposal.setEmergencyManagement(fiveChecklistsEmergencyDisposal1.getEmergencyManagement()!=null?fiveChecklistsEmergencyDisposal1.getEmergencyManagement():null);
            fiveDetail.fiveChecklistsEmergencyDisposal.setFile(fiveChecklistsEmergencyDisposal1.getFile()!=null?fiveChecklistsEmergencyDisposal1.getFile():null);
            fiveDetail.fiveChecklistsEmergencyDisposal.setEmergencyMeasure(fiveChecklistsEmergencyDisposal1.getEmergencyMeasure()!=null?fiveChecklistsEmergencyDisposal1.getEmergencyMeasure():null);
            fiveDetail.fiveChecklistsEmergencyDisposal.setEventDisposal(fiveChecklistsEmergencyDisposal1.getEventDisposal()!=null?fiveChecklistsEmergencyDisposal1.getEventDisposal():null);
        }




        fiveDetail.fiveChecklistsPreventionAndControl.setOrgCode(fiveDetail.getOrgCode());
        fiveDetail.fiveChecklistsPreventionAndControl.setCreateTime(fiveDetail.getCreateTime());
        fiveDetail.fiveChecklistsPreventionAndControl.setFiveRiskType(fiveDetail.getFiveChecklistsFoundation().getFiveRiskType());
        FiveChecklistsPreventionAndControl fiveChecklistsPreventionAndControl2 = EruptDaoUtils.selectOne("select * from tb_fivechecklists_prevention_and_control where org_code ='" + fiveDetail.getOrgCode()+"' ORDER BY id DESC LIMIT 0,1", FiveChecklistsPreventionAndControl.class);
        if (ObjectUtils.isNotEmpty(fiveChecklistsPreventionAndControl2)) {
            fiveDetail.fiveChecklistsPreventionAndControl.setRiskControlSystem(fiveChecklistsPreventionAndControl2.getRiskControlSystem()!=null?fiveChecklistsPreventionAndControl2.getRiskControlSystem():null);
            fiveDetail.fiveChecklistsPreventionAndControl.setRiskDisclosure(fiveChecklistsPreventionAndControl2.getRiskDisclosure()!=null?fiveChecklistsPreventionAndControl2.getRiskDisclosure():null);
            fiveDetail.fiveChecklistsPreventionAndControl.setPointsOfPrevention(fiveChecklistsPreventionAndControl2.getPointsOfPrevention()!=null?fiveChecklistsPreventionAndControl2.getPointsOfPrevention():null);
        }




        fiveDetail.fiveChecklistsMonitor.setOrgCode(fiveDetail.getOrgCode());
        fiveDetail.fiveChecklistsMonitor.setCreateTime(fiveDetail.getCreateTime());
        FiveChecklistsMonitor fiveChecklistsMonitor1 = EruptDaoUtils.selectOne("select * from tb_fivechecklists_monitor where org_code ='" + fiveDetail.getOrgCode() +"' ORDER BY id DESC LIMIT 0,1", FiveChecklistsMonitor.class);
        if (ObjectUtils.isNotEmpty(fiveChecklistsMonitor1)) {
            fiveDetail.fiveChecklistsMonitor.setMonitoring(fiveChecklistsMonitor1.getMonitoring()!=null?fiveChecklistsMonitor1.getMonitoring():null);
        }

    }

//    @Override
//    public void afterAdd(FiveDetail fiveDetail) {
//        FiveDetail fiveDetail1 = EruptDaoUtils.selectOne("select * from tb_five_detail where org_code ='" + fiveDetail.getOrgCode() +"' ORDER BY id DESC LIMIT 1,1", FiveDetail.class);
//        if (ObjectUtils.isNotEmpty(fiveDetail1)) {
//            List<FiveChecklistsResponsibility> fiveChecklistsResponsibilitys = EruptDaoUtils.selectOnes("select * from tb_fivechecklists_responsibility where id =" + fiveDetail1.getId() , FiveChecklistsResponsibility.class);
//            //FiveChecklistsResponsibilityDataProxy fiveChecklistsResponsibilityDataProxy = EruptSpringUtil.getBean(FiveChecklistsResponsibilityDataProxy.class);
//            EruptDao eruptDao = EruptSpringUtil.getBean(EruptDao.class);
//            if(ObjectUtils.isNotEmpty(fiveChecklistsResponsibilitys)) {
//                fiveChecklistsResponsibilitys.forEach(v->{
//                    v.setFiveDetail(fiveDetail1);
//                    eruptDao.merge(v);
//                });
//            }
//        }
//
//
//    }


    @Override
    public FiveDetail queryDataById(long id) {
        FiveDetail fiveDetail = EruptSpringUtil.getBean(FiveDetail.class);
        FiveChecklistsFoundationHandler fiveChecklistsFoundationHandler = EruptSpringUtil.getBean(FiveChecklistsFoundationHandler.class);
        FiveChecklistsResponsibilityHandler fiveChecklistsResponsibilityHandler = EruptSpringUtil.getBean(FiveChecklistsResponsibilityHandler.class);
        FiveChecklistsPreventionAndControlHandler fiveChecklistsPreventionAndControlHandler = EruptSpringUtil.getBean(FiveChecklistsPreventionAndControlHandler.class);
        FiveChecklistsMonitorHandler fiveChecklistsMonitorHandler = EruptSpringUtil.getBean(FiveChecklistsMonitorHandler.class);
        FiveChecklistsEmergencyDisposalHandler fiveChecklistsEmergencyDisposalHandler = EruptSpringUtil.getBean(FiveChecklistsEmergencyDisposalHandler.class);

        FiveChecklistsFoundation fiveChecklistsFoundation = fiveChecklistsFoundationHandler.detail(id);
        Set<FiveChecklistsResponsibility> fiveChecklistsResponsibilities = fiveChecklistsResponsibilityHandler.detail(id);
        FiveChecklistsPreventionAndControl fiveChecklistsPreventionAndControl = fiveChecklistsPreventionAndControlHandler.detail(id);
        FiveChecklistsMonitor fiveChecklistsMonitor = fiveChecklistsMonitorHandler.detail(id);
        FiveChecklistsEmergencyDisposal fiveChecklistsEmergencyDisposal = fiveChecklistsEmergencyDisposalHandler.detail(id);
        fiveDetail.setFiveChecklistsFoundation(fiveChecklistsFoundation);
        fiveDetail.setFiveChecklistsResponsibilities(fiveChecklistsResponsibilities);
        fiveDetail.setFiveChecklistsPreventionAndControl(fiveChecklistsPreventionAndControl);
        fiveDetail.setFiveChecklistsMonitor(fiveChecklistsMonitor);
        fiveDetail.setFiveChecklistsEmergencyDisposal(fiveChecklistsEmergencyDisposal);
        return fiveDetail;
    }
}
