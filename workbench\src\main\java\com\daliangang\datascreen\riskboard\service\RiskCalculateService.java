package com.daliangang.datascreen.riskboard.service;

import com.daliangang.datascreen.utils.ClassNameUtil;
import com.daliangang.datascreen.utils.DateUtil;
import com.daliangang.datascreen.utils.OrgUtils;
import com.daliangang.rndpub.entity.InspectionResultsView;
import com.daliangang.workbench.entity.Enterprise;
import com.daliangang.workbench.entity.EnterpriseCertificate;
import lombok.var;
import org.springframework.stereotype.Service;
import xyz.erupt.jpa.dao.EruptDao;
import xyz.erupt.upms.service.EruptUserService;


import javax.annotation.Resource;
import java.lang.reflect.Field;
import java.time.LocalDate;
import java.time.ZoneId;
import java.time.temporal.ChronoUnit;
import java.util.*;

/**
 * @Author: chongmenglin
 * @Date: 2024/11/7 15:34
 */
@Service
public class RiskCalculateService {
    @Resource
    private EruptUserService eruptUserService;
    @Resource
    private EruptDao eruptDao;
    @Resource
    private MenuService riskMenuService;

    //设备设施年检
    private static final int FACILITY_PENALTY_PER_OCCURRENCE = 2;
    private static final int FACILITY_MAX_PENALTY = 15;

    //作业情况
    private static final int WORK_PENALTY_PER_OCCURRENCE = 5;
    private static final int WORK_MAX_PENALTY = 30;

    //隐患整改
    private static final int INSPECTIO_IN_DEADLINE = 2;
    private static final int INSPECTIO_OUT_DEADLINE = 4;
    private static final int INSPECTIO_MAX_PENALTY = 10;

    //四项机制
    private static final int FOUR_PENALTY_PER_OCCURRENCE = 3;
    private static final int FOUR_MAX_PENALTY = 18;

    //安全三日
    private static final int SAFE_PENALTY_PER_OCCURRENCE = 3;
    private static final int SAFE_MAX_PENALTY = 9;

    //制度备案
    private static final int FILE_MAN_PER_OCCURRENCE = 2;
    private static final int FILE_MAN_MAX_PENALTY = 8;

    //员工持证
    private static final int CERTIFICATION_PER_OCCURRENCE = 2;
    private static final int CERTIFICATION_MAX_PENALTY = 10;


    /**
     * 计算风险规则
     *
     * @param orgCodes 企业组织机构代码
     * @return 企业组织机构代码和得分的映射
     */
    public Map<String, Integer> getRiskScore(Set<String> highRiskOrgCodes,
                                             List<String> orgCodes,
                                             Map<String, String> riskTypeMap,
                                             List<Enterprise> enterprises) {
        List<EnterpriseCertificate> certificates = eruptDao.queryEntityList(
                EnterpriseCertificate.class,
                "company IN (:orgCodes)",
                new HashMap<String, Object>() {{
                    put("orgCodes", orgCodes);
                }}
        );

        for (EnterpriseCertificate certificate : certificates) {
            Date expirationTime = certificate.getExpirationTime();
            if (expirationTime != null && expirationTime.before(new Date())) {
                String companyName = enterprises.stream()
                        .filter(e -> e.getOrgCode().equals(certificate.getCompany()))
                        .map(Enterprise::getName)
                        .findFirst()
                        .orElse("未知公司");
                highRiskOrgCodes.add(certificate.getCompany());
                riskTypeMap.put(companyName, "高风险");
            }
        }

        orgCodes.removeAll(highRiskOrgCodes);


        //设施监测12个菜单得分
        Map<String, Integer> stringIntegerMap = calculateFacilityScores(orgCodes);
        //作业情况
        calculateWorkScores(orgCodes, stringIntegerMap);
        //隐患整改情况
        calculateInspectionrResultScores(orgCodes, stringIntegerMap);
        //四项机制上报情况
        calculateFourScores(orgCodes, stringIntegerMap);
        //安全三日管理上报情况
        calculateSaleThreeScores(orgCodes, stringIntegerMap);
        //制度备案情况
        calculateFilingManScores(orgCodes, stringIntegerMap);
        //人员持证情况
        calculateCertificationScores(orgCodes, stringIntegerMap);
        return stringIntegerMap;
    }

    /**
     * 人员持证情况
     * @param orgCodes
     * @param stringIntegerMap
     */
    private void calculateCertificationScores(List<String> orgCodes, Map<String, Integer> stringIntegerMap) {
        for (String orgCode : orgCodes) {
            int score = 0;
            String companyName = OrgUtils.getCompanyName(orgCode);

            // 查询已过期的持证情况
            String query = "SELECT COUNT(e) FROM EmployeeCertificateView e WHERE " +
                    "REPLACE(REPLACE(e.company1, '(', '（'), ')', '）') = :company1 " +
                    "AND e.validityPeriod < CURRENT_DATE";

            // 替换 orgCode 的括号为中文括号并执行查询
            Long expiredCount = eruptDao.getEntityManager()
                    .createQuery(query, Long.class)
                    .setParameter("company1", companyName.replace("(", "（").replace(")", "）")) // 标准化括号
                    .getSingleResult();

            // 根据已过期数量计算扣分
            score += expiredCount.intValue() * CERTIFICATION_PER_OCCURRENCE; // 扣除分数

            // 确保分数不超过最大惩罚分数
            if (score >= CERTIFICATION_MAX_PENALTY) {
                score = CERTIFICATION_MAX_PENALTY;
            }

            // 将分数累计到结果映射中
            stringIntegerMap.put(orgCode, stringIntegerMap.getOrDefault(orgCode, 0) + score);
        }
    }


    /**
     * 制度备案
     * @param orgCodes
     * @param stringIntegerMap
     * @return
     */
    private Map<String, Integer> calculateFilingManScores(List<String> orgCodes, Map<String, Integer> stringIntegerMap) {
        List<String> menuClassNames = riskMenuService.getSubMenusCode("filingMan");
        // 使用 Arrays.asList 创建可变的 Set
        Set<String> classesToExclude = new HashSet<>(Arrays.asList("Record","Preplan","Design","QualificationManage"));
        menuClassNames.add("PrePlanDangerSource");
        // 移除在 classesToExclude 集合中的类
        menuClassNames.removeIf(classesToExclude::contains);
        for (String orgCode : orgCodes) {
            int score = 0;
            for (String className : menuClassNames) {
                try {
                    Class<?> riskClass = Class.forName(ClassNameUtil.getFullClassName(className)); // 动态加载类
                    int occurrences = checkFileState(riskClass, orgCode);
                    // 如果没有查到记录，则计分
                    if (occurrences == 0)
                        score += FILE_MAN_PER_OCCURRENCE;
                    // 达到最大扣分即跳出
                    if (score >= FILE_MAN_MAX_PENALTY) {
                        score = FILE_MAN_MAX_PENALTY;
                        break;
                    }
                } catch (ClassNotFoundException e) {
                    System.err.println("无法找到该类: " + className); // 输出错误日志
                }
            }
            // 从 riskScores 获取现有分数，并累加当前 score
            stringIntegerMap.put(orgCode, stringIntegerMap.getOrDefault(orgCode, 0) + score);
        }
        return stringIntegerMap;

    }

    private int checkFileState(Class<?> entityClass, String orgCode) {
        // 按优先级顺序定义可能的字段名称
        String[] possibleFieldNames = {"evaluationTime","validityPeriod","effectiveDate","recordTime"};
        String fieldName = null;

        // 通过反射依次检查字段是否存在，找到第一个匹配的字段
        for (String possibleField : possibleFieldNames) {
            try {
                Field field = entityClass.getDeclaredField(possibleField);
                fieldName = possibleField;
                break; // 找到字段后立即退出循环
            } catch (NoSuchFieldException e) {
                // 如果字段不存在则继续检查下一个
            }
        }

        // 如果没有找到任何匹配的字段，可以选择抛出异常或返回默认值
        if (fieldName == null) {
            throw new IllegalArgumentException("找不到适用的日期字段");
        }
        // 构建查询，根据字段名称来决定是否需要加三年
        Date targetDate = null;
        String query;
        if ("recordTime".equals(fieldName)) { // 将 recordTime 加三年
            LocalDate recordTimePlusThreeYears = LocalDate.now().minus(3, ChronoUnit.YEARS);
            targetDate = Date.from(recordTimePlusThreeYears.atStartOfDay(ZoneId.systemDefault()).toInstant());
            // 如果是 recordTime 字段，则在查询中直接将它加三年
            query = "SELECT e FROM " + entityClass.getSimpleName()
                    + " e WHERE e.orgCode = :orgCode AND CURRENT_DATE > :targetDate";
        } else {
            // 其他字段直接与当前日期比较
            query = "SELECT e FROM " + entityClass.getSimpleName()
                    + " e WHERE e.orgCode = :orgCode AND e." + fieldName + " < CURRENT_DATE";
        }
        var queryBuilder = eruptDao.getEntityManager()
                .createQuery(query)
                .setParameter("orgCode", orgCode);
        if (targetDate != null) {
            queryBuilder.setParameter("targetDate", targetDate);
        }
        List<?> results = queryBuilder.getResultList();
        return results.size();
    }

    /**
     * 安全三日管理上报情况
     * @param orgCodes
     * @param stringIntegerMap
     * @return
     */
    private Map<String, Integer> calculateSaleThreeScores(List<String> orgCodes, Map<String, Integer> stringIntegerMap) {
        List<String> menuClassNames = riskMenuService.getSubMenusCode("$dlg-sanre");
        for (String orgCode : orgCodes) {
            int score = 0;
            for (String className : menuClassNames) {
                try {
                    Class<?> riskClass = Class.forName(ClassNameUtil.getFullClassName(className)); // 动态加载类
                    int occurrences = checkSafeState(riskClass, orgCode);
                    // 如果没有查到记录，则计分
                    if (occurrences == 0)
                        score += SAFE_PENALTY_PER_OCCURRENCE;
                    // 达到最大扣分即跳出
                    if (score >= SAFE_MAX_PENALTY) {
                        score = SAFE_MAX_PENALTY;
                        break;
                    }
                } catch (ClassNotFoundException e) {
                    System.err.println("无法找到该类: " + className); // 输出错误日志
                }
            }
            // 从 riskScores 获取现有分数，并累加当前 score
            stringIntegerMap.put(orgCode, stringIntegerMap.getOrDefault(orgCode, 0) + score);
        }
        return stringIntegerMap;

    }

    private int checkSafeState(Class<?> entityClass, String orgCode) {
        // 基本查询
        StringBuilder queryBuilder = new StringBuilder("SELECT e FROM ");
        queryBuilder.append(entityClass.getSimpleName()).append(" e WHERE e.orgCode = :orgCode");

        // 根据类名添加额外的查询条件
        switch (entityClass.getSimpleName()) {
            case "Drill":
                queryBuilder.append(" AND e.place BETWEEN :startOfQuarter AND :endOfQuarter");
                break;
            case "OfflineTraining":
                queryBuilder.append(" AND (e.trainBegin LIKE :monthPattern OR e.trainEnd LIKE :monthPattern)");
                break;
            case "InspectExpertDaily":
                queryBuilder.append(" AND e.inspectDate BETWEEN :startDate AND :endDate");
                break;

        }
        // 转换为查询字符串
        String query = queryBuilder.toString();

        // 创建查询对象
        var queryObj = eruptDao.getEntityManager().createQuery(query)
                .setParameter("orgCode", orgCode);

        // 根据类名设置额外的查询参数
        switch (entityClass.getSimpleName()) {
            case "Drill"://Date
                List<java.sql.Date> range = DateUtil.getCurrentQuarterRangeAsDate();
                queryObj.setParameter("startOfQuarter", range.get(0));
                queryObj.setParameter("endOfQuarter", range.get(1));
                break;
            case "OfflineTraining"://String
                String monthPattern = DateUtil.getCurrentMonth() + "%";
                queryObj.setParameter("monthPattern", monthPattern);
                break;
            case "InspectExpertDaily"://Date
                queryObj.setParameter("startDate", DateUtil.getOneWeekBeginDateAsDate());
                queryObj.setParameter("endDate",DateUtil.getOneWeekLaterDateAsDate());
                break;
        }
        // 执行查询并返回结果
        List<?> results = queryObj.getResultList();
        return results.size();

    }

    /**
     * 四项机制上报
     * @param orgCodes
     * @param stringIntegerMap
     * @return
     */
    private Map<String, Integer> calculateFourScores(List<String> orgCodes, Map<String, Integer> stringIntegerMap) {
        List<String> menuClassNames = riskMenuService.getSubMenusCode("$dlg-four");
        for (String orgCode : orgCodes) {
            int score = 0;
            for (String className : menuClassNames) {
                try {
                    Class<?> riskClass = Class.forName(ClassNameUtil.getFullClassName(className)); // 动态加载类
                    int occurrences = checkFourState(riskClass, orgCode);
                    score += occurrences * FOUR_PENALTY_PER_OCCURRENCE;

                    // 达到最大扣分即跳出
                    if (score >= FOUR_MAX_PENALTY) {
                        score = FOUR_MAX_PENALTY;
                        break;
                    }
                } catch (ClassNotFoundException e) {
                    System.err.println("无法找到该类: " + className); // 输出错误日志
                }
            }
            // 从 riskScores 获取现有分数，并累加当前 score
            stringIntegerMap.put(orgCode, stringIntegerMap.getOrDefault(orgCode, 0) + score);
        }
        return stringIntegerMap;
    }

    private int checkFourState(Class<?> entityClass, String orgCode) {
        // 基本查询
        StringBuilder queryBuilder = new StringBuilder("SELECT e FROM ");
        queryBuilder.append(entityClass.getSimpleName()).append(" e WHERE e.orgCode = :orgCode AND e.submitted = :submitted");

        // 根据类名添加额外的查询条件
        switch (entityClass.getSimpleName()) {
            case "CheckFill":
                queryBuilder.append(" AND e.year = :year");
                break;
            case "Release":
                queryBuilder.append(" AND e.fillingDate = :fillingDate");
                break;
            case "Report":
                queryBuilder.append(" AND e.modifyTime = :modifyTime");
                break;
            case "DailyInspection":
                queryBuilder.append(" AND e.inspectionTime = :inspectionTime");
                break;
            case "WeeklyReport":
                queryBuilder.append(" AND e.inspectionTime BETWEEN :startDate AND :endDate");
                break;
            case "MonthlyScheduling":
                queryBuilder.append(" AND e.reportMonth = :reportMonth");
                break;
        }
        // 转换为查询字符串
        String query = queryBuilder.toString();

        // 创建查询对象
        var queryObj = eruptDao.getEntityManager().createQuery(query)
                .setParameter("orgCode", orgCode)
                .setParameter("submitted", false);

        // 根据类名设置额外的查询参数
        switch (entityClass.getSimpleName()) {
            case "CheckFill"://String
                queryObj.setParameter("year", DateUtil.getCurrentYear());
                break;
            case "Release"://Date
                queryObj.setParameter("fillingDate", DateUtil.getCurrentDayDate());
                break;
            case "Report"://String
                queryObj.setParameter("modifyTime", DateUtil.getCurrentMonth());
                break;
            case "DailyInspection"://Date
                queryObj.setParameter("inspectionTime", DateUtil.getCurrentDayDate());
                break;
            case "WeeklyReport"://Date
                queryObj.setParameter("startDate", DateUtil.getOneWeekBeginDateAsDate());
                queryObj.setParameter("endDate",DateUtil.getOneWeekLaterDateAsDate());
                break;
            case "MonthlyScheduling"://String
                queryObj.setParameter("reportMonth", DateUtil.getCurrentMonth());
                break;

        }

        // 执行查询并返回结果
        List<?> results = queryObj.getResultList();
        return results.size();
    }

    /**
     * 隐患整改情况
     * @param orgCodes
     * @param stringIntegerMap
     * @return
     */
    private Map<String, Integer> calculateInspectionrResultScores(List<String> orgCodes, Map<String, Integer> stringIntegerMap) {
        for (String orgCode : orgCodes) {
            int score = 0;
            //未逾期未整改的数据
            String query = "SELECT e FROM InspectionResultsView e WHERE e.checkObject = :checkObject " +
                    "AND e.publishStatus = '1' " +
                    "AND e.rectificationStatus = 'TO_BE_RECTIFIED' " +
                    "AND (CASE WHEN e.rectificationTime IS NOT NULL THEN e.rectificationTime ELSE CURRENT_DATE END) < e.deadline";

            List<InspectionResultsView> results = eruptDao.getEntityManager()
                    .createQuery(query, InspectionResultsView.class)
                    .setParameter("checkObject", orgCode)
                    .getResultList();

            // 计算分数，根据 results 的数量或其他逻辑计算
            score += results.size() * INSPECTIO_IN_DEADLINE; // 未逾期未整改扣除分数

            query = "SELECT e FROM InspectionResultsView e WHERE e.checkObject = :checkObject " +
                    "AND e.publishStatus = '1' " +
                    "AND e.rectificationStatus = 'TO_BE_RECTIFIED' " +
                    "AND (CASE WHEN e.rectificationTime IS NOT NULL THEN e.rectificationTime ELSE CURRENT_DATE END) > e.deadline";

            results = eruptDao.getEntityManager()
                    .createQuery(query, InspectionResultsView.class)
                    .setParameter("checkObject", orgCode)
                    .getResultList();

            score += results.size() * INSPECTIO_OUT_DEADLINE; // 逾期未整改扣除分数
            if (score >= INSPECTIO_MAX_PENALTY)
                score = INSPECTIO_MAX_PENALTY;
            //逾期未整改的数据
            stringIntegerMap.put(orgCode, stringIntegerMap.getOrDefault(orgCode, 0) + score);
        }

        return stringIntegerMap;
    }



    /**
     * 作业情况
     * @param orgCodes
     * @param stringIntegerMap
     * @return
     */
    private Map<String, Integer> calculateWorkScores(List<String> orgCodes, Map<String, Integer> stringIntegerMap) {
        List<String> menuClassNames = riskMenuService.getSubMenusCode("$dlg-jobMan");
        for (String orgCode : orgCodes) {
            int score = 0;
            for (String className : menuClassNames) {
                try {
                    Class<?> riskClass = Class.forName(ClassNameUtil.getFullClassName(className)); // 动态加载类
                    int occurrences = checkWokeState(riskClass, orgCode);
                    score += occurrences * WORK_PENALTY_PER_OCCURRENCE;

                    // 达到最大扣分即跳出
                    if (score >= WORK_MAX_PENALTY) {
                        score = WORK_MAX_PENALTY;
                        break;
                    }
                } catch (ClassNotFoundException e) {
                    System.err.println("无法找到该类: " + className); // 输出错误日志
                }
            }
            // 从 riskScores 获取现有分数，并累加当前 score
            stringIntegerMap.put(orgCode, stringIntegerMap.getOrDefault(orgCode, 0) + score);
        }
        return stringIntegerMap;
    }


    private int checkWokeState(Class<?> entityClass, String orgCode) {
        // 按优先级顺序定义可能的字段名称
        String[] possibleFieldNames = {"aeTime", "endDate"};
        String fieldName = null;

        // 通过反射依次检查字段是否存在，找到第一个匹配的字段
        for (String possibleField : possibleFieldNames) {
            try {
                Field field = entityClass.getDeclaredField(possibleField);
                fieldName = possibleField;
                break; // 找到字段后立即退出循环
            } catch (NoSuchFieldException e) {
                // 如果字段不存在则继续检查下一个
            }
        }

        // 如果没有找到任何匹配的字段，可以选择抛出异常或返回默认值
        if (fieldName == null) {
            throw new IllegalArgumentException("找不到适用的日期字段");
        }
        String query = "SELECT e FROM " + entityClass.getSimpleName() + " e WHERE e.orgCode = :orgCode AND CURRENT_DATE < e."+fieldName;
        List<?> results = eruptDao.getEntityManager()
                .createQuery(query)
                .setParameter("orgCode", orgCode)
                .getResultList();

        return results.size();
    }

    /**
     * 计算设施年检12个子菜单得分
     * @param orgCodes
     * @return
     */
    public Map<String, Integer> calculateFacilityScores(List<String> orgCodes) {
        Map<String, Integer> riskScores = new HashMap<>();
        List<String> menuClassNames = riskMenuService.getSubMenusCode("$dlg-deviceCheckMan");
        for (String orgCode : orgCodes) {
            int score = 0;
            for (String className : menuClassNames) {
                try {
                    Class<?> riskClass = Class.forName(ClassNameUtil.getFullClassName(className)); // 动态加载类
                    int occurrences = checkOverdueOccurrences(riskClass, orgCode);
                    score += occurrences * FACILITY_PENALTY_PER_OCCURRENCE;

                    // 达到最大扣分即跳出
                    if (score >= FACILITY_MAX_PENALTY) {
                        score = FACILITY_MAX_PENALTY;
                        break;
                    }
                } catch (ClassNotFoundException e) {
                    System.err.println("无法找到该类: " + className); // 输出错误日志
                }
            }
            // 从 riskScores 获取现有分数，并累加当前 score
            riskScores.put(orgCode, riskScores.getOrDefault(orgCode, 0) + score);
        }
        return riskScores;
    }

    private int checkOverdueOccurrences(Class<?> entityClass, String orgCode) {
        String query = "SELECT e FROM " + entityClass.getSimpleName() + " e WHERE e.orgCode = :orgCode AND CURRENT_DATE > e.validityPeriod";
        List<?> results = eruptDao.getEntityManager()
                .createQuery(query)
                .setParameter("orgCode", orgCode)
                .getResultList();

        return results.size();
    }
}
