package com.daliangang.workbench.init;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import xyz.erupt.excel.model.ExcelHeader;

/**
 * @Author：chb
 * @Package：com.daliangang.workbench.init
 * @Project：erupt
 * @name：InitRoleTemplate
 * @Date：2023/3/5 22:04
 * @Filename：InitRoleTemplate
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class InitRoleTemplate extends InitTemplate {

    @ExcelHeader(col = 0, name = "模板名称")
    private String name;

    @ExcelHeader(col = 1, name = "基准角色")
    private String role;

    @ExcelHeader(col = 2, name = "数据范围")
    private String range;

    @ExcelHeader(col = 3, name = "是否复制角色")
    private String duplicate;

    public static InitRoleTemplate valueOf(InitTemplate template) {
        return InitRoleTemplate.builder().name(template.getParam1())
                .role(template.getParam2())
                .range(template.getParam3())
                .duplicate(template.getParam4())
                .build();
    }
}
