package com.daliangang.safedaily.operation;

import com.daliangang.safedaily.entity.MSDS;
import lombok.Data;
import org.springframework.stereotype.Service;
import xyz.erupt.annotation.Erupt;
import xyz.erupt.annotation.EruptField;
import xyz.erupt.annotation.fun.OperationHandler;
import xyz.erupt.annotation.sub_field.Edit;
import xyz.erupt.annotation.sub_field.EditType;
import xyz.erupt.annotation.sub_field.sub_edit.AttachmentType;
import xyz.erupt.jpa.dao.EruptDao;
import xyz.erupt.jpa.model.BaseModel;
import xyz.erupt.toolkit.utils.FileUtils;
import xyz.erupt.toolkit.utils.NotifyUtils;
import xyz.erupt.toolkit.utils.SqlUtils;
import xyz.erupt.upms.util.MetaUtil;

import javax.annotation.Resource;
import javax.transaction.Transactional;
import java.util.List;
import java.util.StringTokenizer;

@Service
public class MSDSBatchUploadHandler implements OperationHandler<MSDS, MSDSBatchUploadHandler.MSDSBatchUploadForm> {

    @Resource
    private EruptDao eruptDao;

    @Override
    @Transactional
    public String exec(List<MSDS> data, MSDSBatchUploadForm uploadForm, String[] param) {
        StringTokenizer st = new StringTokenizer(uploadForm.getFile(), "|");
        while (st.hasMoreTokens()) {
            String path = st.nextToken().trim();
            if (!path.contains(".") || path.endsWith(".zip") || path.contains("MACOSX") || !path.contains(".pdf") ) {
                NotifyUtils.showErrorNotify("压缩包内文件格式应为PDF格式");
            }

            String name = FileUtils.getFilename(path);
            MSDS msds = eruptDao.queryEntity(MSDS.class, "name=" + SqlUtils.wrapStr(name));
            if (msds == null) {
                msds = new MSDS();
                msds.setName(name);
                msds.setFile(path);
                MetaUtil.prepareMetaInfo(msds, true, true);
                eruptDao.persist(msds);
            } else {
                msds.setFile(path);
                eruptDao.merge(msds);
            }
        }
        return null;
    }


    @Erupt(name = "MSDS批量上传", authVerify = false)
    @Data
    public static class MSDSBatchUploadForm extends BaseModel {
        @EruptField(
                edit = @Edit(
                        title = "货种文件（压缩包）",
                        type = EditType.ATTACHMENT, notNull = true,
                        attachmentType = @AttachmentType(type = AttachmentType.Type.BASE,
                                tipMsg = "<t><font color='red'>压缩包内文件格式应为PDF格式</font>",
                                maxLimit = 1,
                                fileTypes = {".zip"}, unzip = true)))
        private String file;
    }

}
