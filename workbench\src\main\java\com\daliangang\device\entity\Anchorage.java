/*
* Copyright © 2020-2035 yundingyun All rights reserved.
*/
package com.daliangang.device.entity;

import com.daliangang.device.proxy.AnchorageDataProxy;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import xyz.erupt.annotation.Erupt;
import xyz.erupt.annotation.EruptField;
import xyz.erupt.annotation.sub_erupt.Power;
import xyz.erupt.annotation.sub_field.Edit;
import xyz.erupt.annotation.sub_field.EditType;
import xyz.erupt.annotation.sub_field.View;
import xyz.erupt.annotation.sub_field.sub_edit.InputType;
import xyz.erupt.annotation.sub_field.sub_edit.Search;
import xyz.erupt.jpa.model.MetaModel;
import xyz.erupt.toolkit.db.Comment;

import javax.persistence.Entity;
import javax.persistence.Lob;
import javax.persistence.Table;

@Erupt(name = "锚地管理", power = @Power(add = true, delete = true, export = false, importable = true, viewDetails = true, edit = true)
, dataProxy = AnchorageDataProxy.class
, rowOperation = {})
@Table(name = "tb_anchorage")
@Entity
@Getter
@Setter
@Comment("锚地管理")
@ApiModel("锚地管理")
public class Anchorage extends MetaModel {
	@EruptField(
		views = @View(title = "锚地名称"),
		edit = @Edit(title = "锚地名称", type = EditType.INPUT, search = @Search(vague = true), notNull = true,
		inputType = @InputType))
	@Comment("锚地名称")
	@ApiModelProperty("锚地名称")
	private String anchorage;

	@EruptField(
		views = @View(title = "锚地类别"),
		edit = @Edit(title = "锚地类别", type = EditType.INPUT, notNull = true,
		inputType = @InputType))
	@Comment("锚地类别")
	@ApiModelProperty("锚地类别")
	private String categoryOfAnchorage;

	@EruptField(
		views = @View(title = "锚地位置"),
		edit = @Edit(title = "锚地位置", type = EditType.INPUT, notNull = true,
		inputType = @InputType))
	@Comment("锚地位置")
	@ApiModelProperty("锚地位置")
	private String anchoragePosition;

	@EruptField(
		views = @View(title = "锚地水深"),
		edit = @Edit(title = "锚地水深", type = EditType.INPUT, notNull = true,
		inputType = @InputType))
	@Comment("锚地水深")
	@ApiModelProperty("锚地水深")
	private String anchorageDepth;

	@EruptField(
		views = @View(title = "锚地面积（万平方米）"),
		edit = @Edit(title = "锚地面积（万平方米）", type = EditType.INPUT, notNull = true,
		inputType = @InputType))
	@Comment("锚地面积（万平方米）")
	@ApiModelProperty("锚地面积（万平方米）")
	private String anchorageArea;

	@EruptField(
		views = @View(title = "系船浮筒个数（个）"),
		edit = @Edit(title = "系船浮筒个数（个）", type = EditType.INPUT, notNull = true,
		inputType = @InputType))
	@Comment("系船浮筒个数（个）")
	@ApiModelProperty("系船浮筒个数（个）")
	private String mooringBuoy;

	@EruptField(
		views = @View(title = "锚泊能力_吨级"),
		edit = @Edit(title = "锚泊能力_吨级", type = EditType.INPUT, notNull = true,
		inputType = @InputType))
	@Comment("锚泊能力_吨级")
	@ApiModelProperty("锚泊能力_吨级")
	private String tonnage;

	@EruptField(
		views = @View(title = "锚泊能力_艘数"),
		edit = @Edit(title = "锚泊能力_艘数", type = EditType.INPUT, notNull = true,
		inputType = @InputType))
	@Comment("锚泊能力_艘数")
	@ApiModelProperty("锚泊能力_艘数")
	private String number;

//	@EruptField(
//		views = @View(title = "经纬度", show = false),
//		edit = @Edit(title = "经纬度", type = EditType.INPUT, show = false, notNull = true,
//		inputType = @InputType))
//	@Comment("经纬度")
//	@ApiModelProperty("经纬度")
//	private String longitudeAndLatitude;
//
//	@EruptField(
//		views = @View(title = "经度", show = false),
//		edit = @Edit(title = "经度", type = EditType.INPUT, show = false,
//		inputType = @InputType))
//	@Comment("经度")
//	@ApiModelProperty("经度")
//	private String longitude;
//
//	@EruptField(
//		views = @View(title = "纬度", show = false),
//		edit = @Edit(title = "纬度", type = EditType.INPUT, show = false,
//		inputType = @InputType))
//	@Comment("纬度")
//	@ApiModelProperty("纬度")
//	private String latitude;

	@EruptField(
		views = @View(title = "地图", show = false),
		edit = @Edit(title = "地图", type = EditType.MAP, show = true))
	@Comment("地图")
	@ApiModelProperty("地图")
	@Lob
	private String map;

}
