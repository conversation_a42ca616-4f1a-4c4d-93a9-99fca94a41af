/*
 * Copyright © 2020-2035 yundingyun All rights reserved.
 */
package com.daliangang.emergency.entity;

import com.daliangang.emergency.operation.EvaluationFormHandler;
import com.daliangang.emergency.operation.EvaluationManageReleaseHandler;
import com.daliangang.emergency.operation.EvaluationManageTemplateFormHandler;
import com.daliangang.emergency.proxy.EvaluationManageDataProxy;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import xyz.erupt.annotation.Erupt;
import xyz.erupt.annotation.EruptField;
import xyz.erupt.annotation.sub_erupt.Power;
import xyz.erupt.annotation.sub_erupt.RowOperation;
import xyz.erupt.annotation.sub_erupt.Tree;
import xyz.erupt.annotation.sub_field.Edit;
import xyz.erupt.annotation.sub_field.EditType;
import xyz.erupt.annotation.sub_field.Readonly;
import xyz.erupt.annotation.sub_field.View;
import xyz.erupt.annotation.sub_field.sub_edit.*;
import xyz.erupt.toolkit.db.Comment;
import xyz.erupt.toolkit.handler.DelOperationHandler;
import xyz.erupt.toolkit.handler.RemoteEntityChoiceFetchHandler;
import xyz.erupt.upms.model.auth.DataAuthHandler;
import xyz.erupt.upms.model.auth.DataAuthModel;

import javax.persistence.*;
import java.util.List;

@Erupt(name = "评估管理", power = @Power(add = true, delete = false, export = false, importable = true, viewDetails = true, edit = true)
        , dataProxy = EvaluationManageDataProxy.class, tree = @Tree(id = "id", label = "name", pid = "parent.id"),
        rowOperation = {
                @RowOperation(title = "下载评估表", icon = "fa fa-download", operationHandler = EvaluationFormHandler.class, mode = RowOperation.Mode.BUTTON),
//        @RowOperation(title = "发布", icon = "fa fa-paper-plane", operationHandler = EvaluationManageReleaseHandler.class, mode = RowOperation.Mode.SINGLE),
//                @RowOperation(title = "编辑",
//                        icon = "fa fa-edit",
//                        code = TplUtils.EDIT_OPER_CODE,
//                        eruptClass = EvaluationManage.class,
//                        operationHandler = EditOperationHandler.class,
//                        ifExpr = "item.publicState=='未发布'",
//                        ifExprBehavior = RowOperation.IfExprBehavior.HIDE,
//                        mode = RowOperation.Mode.SINGLE),
                @RowOperation(title = "删除", icon = "fa fa-trash-o",
                        operationHandler = DelOperationHandler.class, ifExpr = "item.publicState=='未发布'", ifExprBehavior = RowOperation.IfExprBehavior.HIDE, mode = RowOperation.Mode.SINGLE),
                @RowOperation(title = "发布", icon = "fa fa-send-o", operationHandler = EvaluationManageReleaseHandler.class, ifExpr = "item.publicState=='未发布'", ifExprBehavior = RowOperation.IfExprBehavior.HIDE, mode = RowOperation.Mode.SINGLE),
//        @RowOperation(title = "评估表", icon = "fa fa-server", operationHandler = EvaluationManageEvaluationFormHandler.class, mode = RowOperation.Mode.SINGLE),
                @RowOperation(title = "不符合项记录表", icon = "fa fa-server", operationHandler = EvaluationManageTemplateFormHandler.class, mode = RowOperation.Mode.BUTTON),
})
@Table(name = "tb_evaluation_manage")
@Entity
@Getter
@Setter
@Comment("评估管理")
@ApiModel("评估管理")
public class EvaluationManage extends DataAuthModel {
//    @EruptField(
//            views = @View(title = "序号", show = false),
//            edit = @Edit(title = "序号", type = EditType.INPUT, show = false, notNull = true,
//                    inputType = @InputType))
//    @Comment("序号")
//    @ApiModelProperty("序号")
//    private String num;

    @EruptField(
            views = @View(title = "企业名称"),
            edit = @Edit(title = "企业名称",
                    readonly = @Readonly(exprHandler = DataAuthHandler.class),
                    type = EditType.CHOICE,
                    notNull = true,
                    choiceType = @ChoiceType(
                            fetchHandler = RemoteEntityChoiceFetchHandler.class,
                            fetchHandlerParams = {"main", "Enterprise", "org_code,name"},
                            fullSpan = true
                    )
            )
    )
    @Comment("企业名称")
    @ApiModelProperty("企业名称")
    private String company;

    @EruptField(
            views = @View(title = "评估时间"),
            edit = @Edit(title = "评估时间", search = @Search(vague = true), type = EditType.DATE, notNull = true))
    @Comment("评估时间")
    @ApiModelProperty("评估时间")
    private String evaDate;

    @EruptField(
            views = @View(title = "评估小组组长", show = false),
            edit = @Edit(title = "评估小组组长", type = EditType.INPUT, notNull = true,
                    inputType = @InputType))
    @Comment("评估小组组长")
    @ApiModelProperty("评估小组组长")
    private String evaGroup;

    @EruptField(
            views = @View(title = "评估小组副组长", show = false),
            edit = @Edit(title = "评估小组副组长", type = EditType.INPUT, notNull = true,
                    inputType = @InputType))
    @Comment("评估小组副组长")
    @ApiModelProperty("评估小组副组长")
    private String evaDeputy;

    @EruptField(
            views = @View(title = "评估小组成员", show = false),
            edit = @Edit(title = "评估小组成员", type = EditType.TEXTAREA, notNull = true))
    @Comment("评估小组成员")
    @ApiModelProperty("评估小组成员")
    private @Lob String evaMember;



//    @EruptField(
//            views = @View(title = "企业名称"),
//            edit = @Edit(title = "企业名称", type = EditType.CHOICE, notNull = true,
//                    search = @Search(vague = true),
////                    readonly = @Readonly(exprHandler = DataAuthHandler.class),
//                    choiceType = @ChoiceType(fetchHandler = RemoteEntityChoiceFetchHandler.class, fetchHandlerParams ={"main","Enterprise","org_code,name"})))
//    @Comment("企业名称")
//    @ApiModelProperty("企业名称")
//    private String company;

    @EruptField(
            views = @View(title = "上传评估表", show = false),
            edit = @Edit(title = "上传评估表", type = EditType.ATTACHMENT, notNull = true,
                    attachmentType = @AttachmentType(type = AttachmentType.Type.BASE, maxLimit = 20, fileTypes = {".pdf", ".doc", ".docx", ".xlsx", ".png", ".jpg", ".xls", ".ppt", ".pptx", ".zip", ".rar"})))
    @Comment("上传评估表")
    @ApiModelProperty("上传评估表")
    private String evaluationForm;

    @EruptField(
            views = @View(title = "上传不符合项记录表", show = false),
            edit = @Edit(title = "上传不符合项记录表", type = EditType.ATTACHMENT, notNull = true,
                    attachmentType = @AttachmentType(type = AttachmentType.Type.BASE, maxLimit = 20, fileTypes = {".pdf", ".doc", ".docx", ".xlsx", ".png", ".jpg", ".xls", ".ppt", ".pptx", ".zip", ".rar"})))
    @Comment("上传不符合项记录表")
    @ApiModelProperty("上传不符合项记录表")
    private String notConform;

    @EruptField(
            views = @View(title = "总得分"),
            edit = @Edit(title = "总得分", type = EditType.INPUT, readonly = @Readonly(add = true, edit = true), inputType = @InputType(fullSpan = false)))
    @Comment("总得分")
    @ApiModelProperty("总得分")
    private String score;

    @EruptField(
            views = @View(title = "评估等级", column = "name"),
            edit = @Edit(title = "评估等级", readonly = @Readonly(add = true, edit = true), type = EditType.REFERENCE_TREE, referenceTreeType = @ReferenceTreeType(id = "id", label = "name"), search = @Search(vague = true)))
    @Comment("评估等级")
    @ApiModelProperty("评估等级")
    @ManyToOne
    @JoinColumn(name = "eval_grade_id")
    private EvaluationGrade grade;

//    @EruptField(
//            views = @View(title = "评估人"),
//            edit = @Edit(title = "评估人", type = EditType.INPUT, notNull = true,
//                    inputType = @InputType))
//    @Comment("评估人")
//    @ApiModelProperty("评估人")
//    private String evaPeople;

//    @EruptField(
//            views = @View(title = "评估结果"),
//            edit = @Edit(title = "评估结果", type = EditType.INPUT, show = false, notNull = false,
//                    inputType = @InputType))
//    @Comment("评估结果")
//    @ApiModelProperty("评估结果")
//    private String evaEnd;

    @EruptField(
            views = @View(title = "发布状态"),
            edit = @Edit(title = "发布状态",
                    type = EditType.BOOLEAN,
                    show = false,
                    boolType = @BoolType(trueText = "已发布", falseText = "未发布")
            )
    )
    @Comment("发布状态")
    @ApiModelProperty("发布状态")
    private Boolean publicState=false;

    @OneToMany(cascade = CascadeType.ALL, orphanRemoval = true)
    @JoinColumn(name = "eval_id")
    @EruptField(edit = @Edit(title = "单项得分", type = EditType.TAB_TABLE_ADD))
    @OrderBy
    private List<EvaluationScore> scores;
}
