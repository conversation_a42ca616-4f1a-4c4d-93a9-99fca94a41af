package com.daliangang.workbench.entity;

import cn.hutool.core.lang.RegexPool;
import com.daliangang.core.CompanyRenderHandler;
import com.daliangang.statistics.handler.EnterpriseReadOnlyHandler;
import com.daliangang.workbench.operation.EmployeeCertificateValidityPeriodStartUpdateHandler;
import com.daliangang.workbench.operation.EmployeeCertificateValidityPeriodUpdateHandler;
import com.daliangang.workbench.proxy.EmployeeCertificateViewProxy;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import xyz.erupt.annotation.Erupt;
import xyz.erupt.annotation.EruptField;
import xyz.erupt.annotation.expr.ExprBool;
import xyz.erupt.annotation.sub_erupt.Power;
import xyz.erupt.annotation.sub_erupt.RowOperation;
import xyz.erupt.annotation.sub_field.Edit;
import xyz.erupt.annotation.sub_field.EditType;
import xyz.erupt.annotation.sub_field.Readonly;
import xyz.erupt.annotation.sub_field.View;
import xyz.erupt.annotation.sub_field.sub_edit.*;
import xyz.erupt.excel.template.SystemExcelTemplate;
import xyz.erupt.toolkit.colorfont.ColorStateTimeFont;
import xyz.erupt.toolkit.colorfont.ColorStateTimeFontDataProxy;
import xyz.erupt.toolkit.db.Comment;
import xyz.erupt.toolkit.handler.EruptDictCodeChoiceFetchHandler;
import xyz.erupt.toolkit.handler.SqlChoiceFetchHandler;
import xyz.erupt.upms.model.auth.DataAuthModel;

import javax.persistence.Entity;
import javax.persistence.Table;
import javax.persistence.Transient;

@Erupt(name = "从业人员证书",  power = @Power(export = true, importable = true, viewDetails = true, edit = true),
        dataProxy ={ EmployeeCertificateViewProxy.class, ColorStateTimeFontDataProxy.class}
        , rowOperation = {
        @RowOperation(confirm = false, icon = "fa fa-calendar-check-o",title = "修改有效期开始",  eruptClass = EmployeeCertificateValidityPeriodStartUpdateHandler.ValidityPeriodStartUpdate.class,operationHandler = EmployeeCertificateValidityPeriodStartUpdateHandler.class, mode = RowOperation.Mode.MULTI),
        @RowOperation(confirm = false, icon = "fa fa-calendar",title = "修改有效期截至",  eruptClass = EmployeeCertificateValidityPeriodUpdateHandler.ValidityPeriodUpdate.class,operationHandler = EmployeeCertificateValidityPeriodUpdateHandler.class, mode = RowOperation.Mode.MULTI),
})
@Table(name = "tb_person_certificate")
@Entity
@Getter
@Setter
@Comment("从业人员证书")
@ApiModel("从业人员证书")
@SystemExcelTemplate(erupt = EmployeeCertificateView.class, template = "从业人员证书-模板.xls")
@ColorStateTimeFont(stateKey = "stateStr")
public class EmployeeCertificateView  extends DataAuthModel {


//6、操作：批量修改【有效期开始】【有效期截至】


    @EruptField(
            views = @View(title = "持有者名称"),
            edit = @Edit(title = "持有者名称", type = EditType.INPUT, search = @Search(), notNull = true, inputType = @InputType))
    @Comment("持有者名称")
    @ApiModelProperty("持有者名称")
    private String name;
    @EruptField(
            views = @View(title = "所属企业",show = true),
            edit = @Edit(title = "所属企业",
                    search = @Search(ifRender = @ExprBool(exprHandler = CompanyRenderHandler.class)),
                    show = true,
                    type = EditType.CHOICE,
                    notNull = true,
                    readonly = @Readonly(exprHandler = EnterpriseReadOnlyHandler.class),
                    choiceType = @ChoiceType(fullSpan = true,fetchHandler = SqlChoiceFetchHandler.class, fetchHandlerParams = "select org_code,name from tb_enterprise where state = '1'")))
//                    choiceType = @ChoiceType(fullSpan = true,fetchHandler = RemoteEntityChoiceFetchHandler.class, fetchHandlerParams = {"main", "Enterprise", "org_code,name"})))
    @Comment("所属企业")
    @ApiModelProperty("所属企业")
    private String company;



    private String company1;

    @EruptField(
            views = @View(title = "身份证号码",show = false),
            edit = @Edit(title = "身份证号码", type = EditType.INPUT, show = false, inputType = @InputType(regex= RegexPool.CITIZEN_ID )
            ))
    @Comment("身份证号码")
    @ApiModelProperty("身份证号码")
    private String idCard;

    @EruptField(
            views = @View(title = "性别",show = false),
            edit = @Edit(title = "性别", type = EditType.BOOLEAN, show = false,
                    boolType = @BoolType(trueText = "男", falseText = "女")))
    @Comment("性别")
    @ApiModelProperty("性别")
    private Boolean sex;

    @EruptField(
            views = @View(title = "联系方式",show = false),
            edit = @Edit(title = "联系方式",type = EditType.INPUT,
                    inputType = @InputType(fullSpan = true,regex= RegexPool.MOBILE)
            )
    )
    @Comment("联系方式")
    @ApiModelProperty("联系方式")
    private String phone;

    @EruptField(
            views = @View(title = "出生日期",show = false),
            edit = @Edit(title = "出生日期", type = EditType.DATE, show = false,
                    dateType = @DateType(pickerMode = DateType.PickerMode.RANGE, startToday = "1940-01-01 00:00:00", endToday = "now")))
    @Comment("出生日期")
    @ApiModelProperty("出生日期")
    private java.util.Date birthday;

    @EruptField(
            views = @View(title = "证书编号"),
            edit = @Edit(title = "证书编号", type = EditType.INPUT,
                    inputType = @InputType))
    @Comment("证书编号")
    @ApiModelProperty("证书编号")
    private String certificateNum;


    @EruptField(
            views = @View(title = "证书名称",show = false),
            edit = @Edit(title = "证书名称", type = EditType.INPUT, show = false,
                    inputType = @InputType))
    @Comment("证书名称")
    @ApiModelProperty("证书名称")
    private String certificateName;

    @EruptField(
            views = @View(title = "证书类型"),
            edit = @Edit(title = "证书类型", type = EditType.CHOICE, notNull = true,search = @Search(),
                    choiceType = @ChoiceType(fetchHandler = EruptDictCodeChoiceFetchHandler.class, fetchHandlerParams = "certificateType"))
    )
    @Comment("证书类型")
    @ApiModelProperty("证书类型")
    private String certificateType;


    @EruptField(
            views = @View(title = "有效期开始"),
            edit = @Edit(title = "有效期开始", type = EditType.DATE, notNull = true,
                    dateType = @DateType))
    @Comment("有效期开始")
    @ApiModelProperty("有效期开始")
    private java.util.Date validityPeriodStart;

    @EruptField(
            views = @View(title = "有效期截止"),
            edit = @Edit(title = "有效期截止", type = EditType.DATE, notNull = true,
                    dateType = @DateType))
    @Comment("有效期截止")
    @ApiModelProperty("有效期截止")
    private java.util.Date validityPeriod;


    @EruptField(
            views = @View(title = "证书扫描件",show = false),
            edit = @Edit(title = "证书扫描件", type = EditType.ATTACHMENT, notNull = true,
                    attachmentType = @AttachmentType(type = AttachmentType.Type.IMAGE, maxLimit = 20)))
    @Comment("证书扫描件")
    @ApiModelProperty("证书扫描件")
    private String scanFile;


    @EruptField(
            views = @View(title = "状态",show = false),
            edit = @Edit(title = "状态",
                    search = @Search(), type = EditType.CHOICE,  notNull = false, show = false,
                    choiceType = @ChoiceType(fetchHandler = EruptDictCodeChoiceFetchHandler.class, fetchHandlerParams = "expiredState")
            )
    )
    @Comment("状态")
    @ApiModelProperty("状态")
    @Transient
    private String state;

    @EruptField(
            views = @View(title = "状态", show = true),
            edit = @Edit(title = "状态", type = EditType.INPUT, notNull = false, show = false))
    @Transient
    private String stateStr;
}
