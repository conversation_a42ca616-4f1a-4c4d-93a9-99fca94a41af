package com.daliangang.safedaily.operation;

import cn.hutool.json.JSONObject;
import com.daliangang.safedaily.entity.DoubleCheckManagement;
import com.daliangang.safedaily.entity.OfflineTraining;
import org.springframework.stereotype.Service;
import xyz.erupt.annotation.fun.OperationHandler;
import xyz.erupt.common.http.EruptRemoteUtils;
import xyz.erupt.jpa.dao.EruptDao;
import xyz.erupt.toolkit.remote.RemoteProxyService;
import xyz.erupt.toolkit.utils.NotifyUtils;

import javax.annotation.Resource;
import javax.transaction.Transactional;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * @Title: OfflineTrainingEscalationHandler
 * <AUTHOR>
 * @Package com.daliangang.safedaily.operation
 * @Date 2024/3/8 10:05
 * @description: 培训考试日上报
 */
@Service
public class OfflineTrainingEscalationHandler implements OperationHandler<OfflineTraining,Void> {

    @Resource
    private RemoteProxyService remoteProxyService;

    @Resource
    private EruptDao eruptDao;

    @Override
    @Transactional
    public String exec(List<OfflineTraining> data, Void unused, String[] param) {
        for (OfflineTraining offlineTraining:data) {
            offlineTraining.setSubmitted(true);
            offlineTraining.setUpdateTime(LocalDateTime.now());
            eruptDao.merge(offlineTraining);

            JSONObject inputData = new JSONObject();
            inputData.set("clazz", "OfflineTraining");
            inputData.set("insertData",offlineTraining);
            EruptRemoteUtils.doPost(remoteProxyService.getMainServerUrl() + "/erupt-api/exchange/data/exchange", inputData, Map.class);
        }
        return NotifyUtils.getSuccessNotify("上报成功！");
    }
}
