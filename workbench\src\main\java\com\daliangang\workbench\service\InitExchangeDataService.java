package com.daliangang.workbench.service;

import cn.hutool.json.JSONObject;
import com.daliangang.workbench.controller.ExchangeDataController;
import com.google.gson.internal.LinkedTreeMap;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import xyz.erupt.common.http.EruptRemoteUtils;
import xyz.erupt.core.annotation.EruptHandlerNaming;
import xyz.erupt.job.handler.EruptJobHandler;
import xyz.erupt.toolkit.remote.RemoteProxyService;
import xyz.erupt.toolkit.remote.RemoteQuery;

import javax.annotation.Resource;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

@EruptHandlerNaming("推送大数据平台——初始化")
@Service
@Slf4j
public class InitExchangeDataService  implements EruptJobHandler {

    @Resource
    private RemoteProxyService remoteProxyService;


    @Override
    public String exec(String code, String param) {
        //若参数为空，则全部执行,否则单个执行
        Set<String> keys = new HashSet<>();
        if(StringUtils.isEmpty(param)){
            keys = ExchangeDataController.BUSINESS_CODE.keySet();
        }else{
            keys.add(param);
        }
        for (String clazz : keys) {
            List<LinkedTreeMap> list = remoteProxyService.queryEntity(clazz, RemoteQuery.builder().condition("1", "1"));
            JSONObject inputData = new JSONObject();
            inputData.set("clazz", clazz);
            inputData.set("insertData",list);
            EruptRemoteUtils.doPost(remoteProxyService.getMainServerUrl() + "/erupt-api/exchange/data/exchange", inputData, Map.class);

        }
        return null ;
    }
}
