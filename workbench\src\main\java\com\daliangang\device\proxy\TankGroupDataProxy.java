/*
* Copyright © 2020-2035 yundingyun All rights reserved.
*/
package com.daliangang.device.proxy;

import cn.hutool.json.JSONObject;
import com.daliangang.core.DaliangangContext;
import com.daliangang.device.entity.StorageTank;
import com.daliangang.device.entity.TankGroup;
import com.google.gson.internal.LinkedTreeMap;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.stereotype.Service;
import xyz.erupt.annotation.fun.DataProxy;
import xyz.erupt.annotation.query.Condition;
import xyz.erupt.common.http.EruptRemoteUtils;
import xyz.erupt.jpa.dao.EruptDao;
import xyz.erupt.toolkit.remote.RemoteProxyService;
import xyz.erupt.toolkit.remote.RemoteQuery;
import xyz.erupt.toolkit.utils.EruptDaoUtils;
import xyz.erupt.toolkit.utils.NotifyUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.atomic.AtomicReference;
import java.util.regex.Pattern;

@Service
public class TankGroupDataProxy implements DataProxy<TankGroup> {
    @Resource
    private RemoteProxyService remoteProxyService;
    @Resource
    private EruptDao eruptDao ;
    @Override
    public void addBehavior(TankGroup tankGroup) {
        if (ObjectUtils.isNotEmpty(tankGroup.getCompany())) {
            List<LinkedTreeMap> enterprise = remoteProxyService.queryEntity("Enterprise", RemoteQuery.builder().condition("orgCode", tankGroup.getCompany()));
            if (ObjectUtils.isNotEmpty(enterprise)) {
                //  Integer portArea1 = (int) Float.parseFloat((String) enterprise.get(0).get("portArea"));
                //   String portArea = String.valueOf(enterprise.get(0).get("portArea"));
                String portArea=new BigDecimal(enterprise.get(0).get("portArea").toString()).stripTrailingZeros().toPlainString();
                tankGroup.setPortArea(portArea);
            }
        }
    }

    @Override
    public void beforeDelete(TankGroup tankGroup) {
        //所属罐区
      //  String tankGroupId = tankGroup.getTankFarm();
            String selectSql="select * from tb_storage_tank where tank_group="+tankGroup.getId();
            List<StorageTank> storageTank = EruptDaoUtils.selectOnes(selectSql, StorageTank.class);
            if(storageTank.size()>0){
                NotifyUtils.showErrorMsg("当前罐组已有下级储罐信息，不可删除!");
            }

    }

    @Override
    public String beforeFetch(List<Condition> conditions) {
        AtomicReference<String> returnStr = new AtomicReference<>(" 1 = 1 ") ;
        //所属码头查询条件
        Optional<Condition> checkObjectFirst = conditions.stream().filter(condition ->
                "tankFarm1".equals(condition.getKey())
        ).findFirst();
        checkObjectFirst.ifPresent( f ->{
            returnStr.set(returnStr.get()+"and tank_farm  =" + f.getValue() );
            conditions.remove(f) ;
        });
        return returnStr.get();
    }


    @Override
    public void beforeUpdate(TankGroup tankGroup) {

        //所属罐区
        String tankFarm = tankGroup.getTankFarm() ;
        //汉字格式
        Pattern p = Pattern.compile("[\u4e00-\u9fa5]");
        if(tankFarm!=null&&p.matcher(tankFarm).find()){
            String tankFarmpId = DaliangangContext.getTankFarmId(tankFarm);
            tankGroup.setTankFarm(tankFarmpId.substring(0,tankFarmpId.indexOf(".")));
        }
    }

    @Override
    public void afterAdd(TankGroup tankGroup) {
        JSONObject inputData = new JSONObject();
        inputData.set("clazz", "TankGroup");
        inputData.set("insertData",tankGroup);
        EruptRemoteUtils.doPost(remoteProxyService.getMainServerUrl() + "/erupt-api/exchange/data/exchange", inputData, Map.class);
    }

    @Override
    public void afterUpdate(TankGroup tankGroup) {
        JSONObject inputData = new JSONObject();
        inputData.set("clazz", "TankGroup");
        inputData.set("insertData",tankGroup);
        EruptRemoteUtils.doPost(remoteProxyService.getMainServerUrl() + "/erupt-api/exchange/data/exchange", inputData, Map.class);
    }
}
