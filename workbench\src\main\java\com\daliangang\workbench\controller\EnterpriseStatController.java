package com.daliangang.workbench.controller;

import com.daliangang.workbench.entity.Enterprise;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;
import xyz.erupt.annotation.EruptField;
import xyz.erupt.annotation.query.Condition;
import xyz.erupt.core.annotation.EruptRouter;
import xyz.erupt.core.service.EruptCoreService;
import xyz.erupt.core.view.EruptModel;
import xyz.erupt.core.view.Page;
import xyz.erupt.core.view.TableQueryVo;
import xyz.erupt.jpa.dao.EruptDao;
import xyz.erupt.toolkit.utils.EruptDaoUtils;

import javax.annotation.Resource;
import javax.transaction.Transactional;
import java.lang.reflect.Field;
import java.util.*;

/**
 * @Author: zengwensi
 * @Description:
 * @Date: Created in 2023/6/30 15:36
 * @Modified By
 */
@RestController
public class EnterpriseStatController {

    @Resource
    private EruptDao eruptDao;

    @Resource
    private EnterpriseStatsController enterpriseStatsController ;

    @PostMapping("/erupt-api/data/Enterprise/drill/EnterpriseStatForm/{enterpriseId}")
    @EruptRouter(verifyType = EruptRouter.VerifyType.LOGIN)
    public Page enterpriseStatForm(@PathVariable Long enterpriseId, @RequestBody TableQueryVo queryVo ) {
        Enterprise enterprise = eruptDao.queryEntity(Enterprise.class, "id = " + enterpriseId);
        TableQueryVo tableQueryVo = new TableQueryVo();
        tableQueryVo.setPageIndex(0);
        tableQueryVo.setPageSize(1000);
        Condition condition = new Condition("company",enterprise.getOrgCode());
        List<Condition> conditionList = new ArrayList<>();
        conditionList.add(condition);
        tableQueryVo.setCondition(conditionList);

        //字段名为key，标题为value 的map
        Map<String,String> fieldTitleMap = new LinkedHashMap<>();
        //获取到 表头和字段的关系
        EruptModel eruptModel = EruptCoreService.getErupt("EnterpriseStats");
        for (Field field : eruptModel.getClazz().getDeclaredFields()) {
            EruptField eruptField = field.getAnnotation(EruptField.class);
            if (eruptField == null) continue;
            fieldTitleMap.put(field.getName(),eruptField.edit().title());
        }

        Page enterpriseStats = enterpriseStatsController.getEnterpriseStats(tableQueryVo);
        Collection<Map<String, Object>> list = enterpriseStats.getList();
        //获取是否根据 head 查询
        String headValue = null ;
        List<Condition> queryVoCondition = queryVo.getCondition();
        if(null != queryVoCondition && queryVoCondition.size() !=0){
            Condition headCondition = queryVoCondition.get(0);
            if(null != headCondition){
                headValue = (String)headCondition.getValue();
            }
        }




        List<Map<String,Object>> all = new ArrayList<>();
        for(Map<String, Object> result : list){
            int i = 1 ;

            for(String key : fieldTitleMap.keySet()){
                if(key.equals("companyId")) {
                    continue;
                }

                String head = fieldTitleMap.get(key);
                String content = result.get(key)==null?"":(String)result.get(key);
                Map<String,Object> map = new LinkedHashMap<>();
                map.put("head",head);
                map.put("content",content);
                //说明有查询条件
                if(headValue !=null){
                    if(head.contains(headValue)){
                        all.add(map);
                    }

                }else{
                    all.add(map);
                }

            }
        }
        Page page = new Page();


        page.setList(all);
        page.setPageIndex(1);
        page.setPageSize(999);
        page.setSort("");
        return page;

    }


    /**
     * 更新统计下钻页数据
     * @param enterpriseStatList
     */
    @Transactional
    public void updateEnterpriseStat(Collection<Map<String, Object>> enterpriseStatList){
        List<String> all = new ArrayList<>();
//        String deleteSql = "delete from tb_enterprise_stat where 1 = 1 ";
//        all.add(deleteSql);
        TableQueryVo tableQueryVo = new TableQueryVo();
        tableQueryVo.setPageIndex(0);
        tableQueryVo.setPageSize(1000);

        //字段名为key，标题为value 的map
        Map<String,String> fieldTitleMap = new LinkedHashMap<>();
        //获取到 表头和字段的关系
        EruptModel eruptModel = EruptCoreService.getErupt("EnterpriseStats");
        for (Field field : eruptModel.getClazz().getDeclaredFields()) {
            EruptField eruptField = field.getAnnotation(EruptField.class);
            if (eruptField == null) continue;
            fieldTitleMap.put(field.getName(),eruptField.edit().title());
        }

        Page enterpriseStats = enterpriseStatsController.getEnterpriseStats(tableQueryVo);
        Collection<Map<String, Object>> list = enterpriseStats.getList();

        String sql = " replace into tb_enterprise_stat(enterprise_id,head,content,ordernum) values('%s','%s','%s',%s)" ;
        for(Map<String, Object> result : list){
            int i = 1 ;
            String enterpriseId = (String)result.get("companyId");
            for(String key : fieldTitleMap.keySet()){
                if(key.equals("companyId")) {
                    continue;
                }
                String head = fieldTitleMap.get(key);
                String content = result.get(key)==null?"":(String)result.get(key);
                all.add(String.format(sql,enterpriseId,head,content,i++));
//                EruptDaoUtils.getEruptDao().getJdbcTemplate().update(String.format(sql,enterpriseId,head,content,i++)) ;
            }
        }


        try{
            EruptDaoUtils.getEruptDao().getJdbcTemplate().batchUpdate(all.toArray(new String[all.size()]));
        }catch (Exception e){
//            log.error("第一次更新EnterpriseStatForm下钻表，发生死锁，报错了！！！");
            e.printStackTrace();
        }


    }
}
