package com.daliangang.training.entity;

import lombok.*;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Date 2023/5/19
 * @Description:
 */
@Getter
@Setter
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class OrganSyncEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    //组织编码
    private String orgCode;

    //组织名称
    private String orgName;

    //组织主系统id
    private Long orgId;

    //组织级别 0- 市 1-区县 2 -企业
    private Integer orgType;

    //父组织ID
    private Long parentOrgId;

    //删除标识
    private Boolean delFlag;

    //更新/新增标识
    private Boolean updateFlag;
}
