package com.daliangang.rndpub.entity;

import com.daliangang.core.CompanyRenderHandler;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import xyz.erupt.annotation.EruptField;
import xyz.erupt.annotation.expr.ExprBool;
import xyz.erupt.annotation.sub_field.Edit;
import xyz.erupt.annotation.sub_field.EditType;
import xyz.erupt.annotation.sub_field.View;
import xyz.erupt.annotation.sub_field.sub_edit.*;
import xyz.erupt.jpa.model.MetaModel;
import xyz.erupt.toolkit.db.Comment;
import xyz.erupt.toolkit.handler.EruptDictCodeChoiceFetchHandler;
import xyz.erupt.toolkit.handler.RemoteEntityTagFetchHandler;

import javax.persistence.*;
import java.util.Set;

/**
 * @Author: zengwensi
 * @Description:
 * @Date: Created in 2023/4/14 16:01
 * @Modified By
 */
@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@MappedSuperclass
public class ExpertInfo extends MetaModel {

    @EruptField(
            views = @View(title = "企业名称"),
            edit = @Edit(title = "企业名称", search = @Search(ifRender = @ExprBool(exprHandler = CompanyRenderHandler.class)),
                    type = EditType.INPUT, show = true, notNull = true))
    @Comment("企业名称")
    @ApiModelProperty("企业名称")
    private String expertCompany;

    @EruptField(
            views = @View(title = "姓名"),
            edit = @Edit(title = "姓名", type = EditType.INPUT, search = @Search(vague = true), notNull = true,
                    inputType = @InputType))
    @Comment("姓名")
    @ApiModelProperty("姓名")
    private String name;

    @EruptField(
            views = @View(title = "性别"),
            edit = @Edit(title = "性别", type = EditType.BOOLEAN, search = @Search(vague = true), notNull = true,
                    boolType = @BoolType(trueText = "男", falseText = "女")))
    @Comment("性别")
    @ApiModelProperty("性别")
    private Boolean sex;

    @EruptField(
            views = @View(title = "出生日期"),
            edit = @Edit(title = "出生日期", type = EditType.DATE, notNull = true,
                    dateType = @DateType(pickerMode = DateType.PickerMode.RANGE, startToday = "1940-01-01 00:00:00", endToday = "now")))
    @Comment("出生日期")
    @ApiModelProperty("出生日期")
    private String date;

    @EruptField(
            views = @View(title = "学历"),
            edit = @Edit(title = "学历", type = EditType.CHOICE, notNull = true,
                    choiceType = @ChoiceType(fetchHandler = EruptDictCodeChoiceFetchHandler.class, fetchHandlerParams = "educational")))
    @Comment("学历")
    @ApiModelProperty("学历")
    private String educational;

    @EruptField(
            views = @View(title = "检查范围"),
            edit = @Edit(title = "检查范围", type = EditType.CHOICE, notNull = true, search = @Search,
                    choiceType = @ChoiceType(fetchHandler = EruptDictCodeChoiceFetchHandler.class, fetchHandlerParams = "checkScope")))
    @Comment("检查范围")
    @ApiModelProperty("检查范围")
    private String checkScope;

    @EruptField(
            views = @View(title = "专业类别"),
            edit = @Edit(title = "专业类别", type = EditType.INPUT, notNull = true,
                    inputType = @InputType))
    @Comment("专业类别")
    @ApiModelProperty("专业类别")
    private String category;

    @EruptField(
            views = @View(title = "职称"),
            edit = @Edit(title = "职称", type = EditType.CHOICE, notNull = true,
                    choiceType = @ChoiceType(fetchHandler = EruptDictCodeChoiceFetchHandler.class, fetchHandlerParams = "postTitle")))
    @Comment("职称")
    @ApiModelProperty("职称")
    private String technical;



    @EruptField(
            views = @View(title = "规避企业"),
            edit = @Edit(title = "规避企业", type = EditType.TAGS, notNull = false,
                    tagsType = @TagsType(allowExtension = false, fetchHandler = RemoteEntityTagFetchHandler.class, fetchHandlerParams = {"main", "Enterprise", "name"})))
    @Comment("规避企业")
    @ApiModelProperty("规避企业")
    private String evadeCompany;

    @EruptField(
            edit = @Edit(title = "资质证书",
                    type = EditType.TAB_TABLE_ADD,
                    notNull = false
            )
    )
    @OneToMany(cascade = CascadeType.ALL, orphanRemoval = true)
    @OrderBy
    @JoinColumn(name = "expert_id")
    @Comment("资质证书")
    @ApiModelProperty("资质证书")
    private Set<Qualification> file;

    @EruptField(
            edit = @Edit(title = "执法信息", type = EditType.TAB_TABLE_ADD, notNull = false, show = false))
    @OneToMany(cascade = CascadeType.ALL, orphanRemoval = true)
    @OrderBy
    @JoinColumn(name = "expert_id")
    @Comment("执法信息")
    @ApiModelProperty("执法信息")
    private Set<LawenForcement> law;

    @EruptField(
            views = @View(title = "审批状态"),
            edit = @Edit(title = "审批状态", type = EditType.CHOICE, notNull = false, show = false, search = @Search,
                    choiceType = @ChoiceType(fetchHandler = EruptDictCodeChoiceFetchHandler.class, fetchHandlerParams = "auditState")))
    @Comment("审批状态")
    @ApiModelProperty("审批状态")
    private String expertAudidtState;


    @EruptField(
            views = @View(title = "状态", show = true),
            edit = @Edit(title = "状态", type = EditType.BOOLEAN, notNull = false, show = true, search = @Search,
                    boolType = @BoolType(trueText = "启用", falseText = "禁用")))
    @Comment("状态")
    @ApiModelProperty("状态")
    private Boolean useState;

    @EruptField(
            views = @View(title = "来源", show = false),
            edit = @Edit(title = "来源", type = EditType.BOOLEAN, notNull = false, show = false,
                    boolType = @BoolType(trueText = "系统", falseText = "外链")))
    @Comment("来源")
    @ApiModelProperty("来源")
    private Boolean source;
}
