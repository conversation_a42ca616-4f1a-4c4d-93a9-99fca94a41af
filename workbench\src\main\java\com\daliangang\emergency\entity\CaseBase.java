/*
 * Copyright © 2020-2035 yundingyun All rights reserved.
 */
package com.daliangang.emergency.entity;

import com.daliangang.emergency.operation.CaseBasePowerHandler;
import com.daliangang.emergency.proxy.CaseBaseDataProxy;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import xyz.erupt.annotation.Erupt;
import xyz.erupt.annotation.EruptField;
import xyz.erupt.annotation.sub_erupt.Power;
import xyz.erupt.annotation.sub_erupt.RowOperation;
import xyz.erupt.annotation.sub_field.Edit;
import xyz.erupt.annotation.sub_field.EditType;
import xyz.erupt.annotation.sub_field.View;
import xyz.erupt.annotation.sub_field.sub_edit.*;
import xyz.erupt.jpa.model.MetaModel;
import xyz.erupt.toolkit.attachment.AttachmentDownloadHandler;
import xyz.erupt.toolkit.db.Comment;
import xyz.erupt.toolkit.handler.EruptDictTagFetchHandler;

import javax.persistence.Entity;
import javax.persistence.Lob;
import javax.persistence.Table;

@Erupt(name = "案例库管理", power = @Power(
//        add = true,
//        delete = true,
//        export = true,
//        importable = true,
//        viewDetails = true,
//        edit = true
        powerHandler = CaseBasePowerHandler.class
)
        , dataProxy = CaseBaseDataProxy.class
        , rowOperation = {
        @RowOperation(title = "下载附件", icon = "fa fa-download",
                ifExpr = "item.uploadReport !== '' && item.uploadReport != null",
                operationHandler = AttachmentDownloadHandler.class, mode = RowOperation.Mode.SINGLE)

})
@Table(name = "tb_case_base")
@Entity
@Getter
@Setter
@Comment("案例库管理")
@ApiModel("案例库管理")
public class CaseBase extends MetaModel {
    @EruptField(
            views = @View(title = "事故名称",width = "300px"),
            edit = @Edit(title = "事故名称", type = EditType.INPUT, search = @Search(vague = true), notNull = true,
                    inputType = @InputType))
    @Comment("事故名称")
    @ApiModelProperty("事故名称")
    private String accidentName;

    @EruptField(
            views = @View(title = "发生时间",width = "100px"),
            edit = @Edit(title = "发生时间", type = EditType.DATE, search = @Search(vague = true), notNull = true,
                    dateType = @DateType))
    @Comment("发生时间")
    @ApiModelProperty("发生时间")
    private java.util.Date timeOfOccurrence;

    @EruptField(
            views = @View(title = "事故类型",width = "100px"),
            edit = @Edit(title = "事故类型", type = EditType.TAGS, search = @Search(vague = true), notNull = false,
                    tagsType = @TagsType(fetchHandler = EruptDictTagFetchHandler.class, fetchHandlerParams = "accidentType")))
    @Comment("事故类型")
    @ApiModelProperty("事故类型")
    private String accidentType;

    @EruptField(
            views = @View(title = "关键词",width = "100px"),
            edit = @Edit(title = "关键词", type = EditType.INPUT, search = @Search(vague = true), notNull = false))
    @Comment("关键字")
    @ApiModelProperty("关键字")
    private String keywords;

    @EruptField(
            views = @View(title = "事故国家",width = "100px"),
            edit = @Edit(title = "事故国家", type = EditType.INPUT, search = @Search(vague = true), notNull = true,
                    inputType = @InputType))
    @Comment("事故国家")
    @ApiModelProperty("事故国家")
    private String country;

    @EruptField(
            views = @View(title = "事故地区",width = "100px"),
            edit = @Edit(title = " 事故地区", type = EditType.INPUT, search = @Search(vague = true), notNull = true,
                    inputType = @InputType))
    @Comment("事故地区")
    @ApiModelProperty("事故地区")
    private String area;

    @EruptField(
            views = @View(title = "事故过程及处置救援（限1000字）", show = false),
            edit = @Edit(title = "事故过程及处置救援（限1000字）", type = EditType.TEXTAREA,
                    notNull = true,
                    inputType = @InputType(length = 1000)
            ))
    @Comment("事故过程及处置救援（限1000字）")
    @ApiModelProperty("事故过程及处置救援（限1000字）")
    private @Lob String accidentProcess;

    @EruptField(
            views = @View(title = "事故后果（限300字）", show = false),
            edit = @Edit(title = "事故后果（限300字）",
                    type = EditType.TEXTAREA,
                    notNull = true,
                    inputType = @InputType(length = 300)
            ))
    @Comment("事故后果（限300字）")
    @ApiModelProperty("事故后果（限300字）")
    private @Lob String accidentConsequence;

    @EruptField(
            views = @View(title = "事故直接原因（500字）", show = false),
            edit = @Edit(title = "事故直接原因（500字）", type = EditType.TEXTAREA,
                    notNull = true,
            inputType = @InputType(length = 500)))
    @Comment("事故直接原因（500字）")
    @ApiModelProperty("事故直接原因（500字）")
    private @Lob String accidentCause;

    @EruptField(
            views = @View(title = "事故调查报告", show = false),
            edit = @Edit(title = "事故调查报告", type = EditType.ATTACHMENT, notNull = false,
                    attachmentType = @AttachmentType(type = AttachmentType.Type.BASE, maxLimit = 20, fileTypes = {".pdf", ".doc", ".docx", ".xlsx", ".png", ".jpg", ".xls", ".ppt", ".pptx", ".zip", ".rar"})))
    @Comment("事故调查报告")
    @ApiModelProperty("事故调查报告")
    private String uploadReport;

}
