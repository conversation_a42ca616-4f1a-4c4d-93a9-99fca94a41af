package com.daliangang.safedaily.proxy;

import com.daliangang.safedaily.entity.StandardizationScore;
import org.apache.commons.lang3.ObjectUtils;
import xyz.erupt.annotation.fun.DataProxy;
import xyz.erupt.toolkit.utils.NotifyUtils;

import java.math.BigDecimal;

public class StandardizationScoreDataProxy implements DataProxy<StandardizationScore> {
    @Override
    public void beforeAdd(StandardizationScore standardizationScore) {
        if (ObjectUtils.isEmpty(standardizationScore.getScoreStandard())||ObjectUtils.isEmpty(standardizationScore.getScore())) {
            NotifyUtils.showErrorDialog("有未填写分数项!");
        }
        if(standardizationScore.getScoreStandard().compareTo(standardizationScore.getScore())<0){
            NotifyUtils.showErrorDialog("实际分数应小于应得分数!");
        }
    }

    @Override
    public void beforeUpdate(StandardizationScore standardizationScore) {
        if (ObjectUtils.isEmpty(standardizationScore.getScoreStandard()) || ObjectUtils.isEmpty(standardizationScore.getScore())) {
            NotifyUtils.showErrorDialog("有未填写分数项!");
        }
        if(standardizationScore.getScoreStandard()!=null){
            if(standardizationScore.getScoreStandard().compareTo(standardizationScore.getScore())<0){
                NotifyUtils.showErrorDialog("实际分数应小于应得分数!");
            }
        }

    }
}
