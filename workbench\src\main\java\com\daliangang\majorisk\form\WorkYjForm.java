package com.daliangang.majorisk.form;

import com.daliangang.device.entity.*;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @since :2023/5/23:18:50
 */
@Data
public class WorkYjForm {
    private Long id;

    private String name;

    private String type;

    private String company;
    private String companyName;

    private String map;

    private String addressId;

    private LocalDateTime starTime;
    private LocalDateTime endTime;
    private String state;
    private String goodsName;
    private String riskName;

    private List<AddressInfo> address;

    @Data
    public class AddressInfo{
        private String addressType;
        private String map;
    }

}
