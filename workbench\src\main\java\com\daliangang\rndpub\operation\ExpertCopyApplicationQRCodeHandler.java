/*
 * Copyright © 2020-2035 yundingyun All rights reserved.
 */
package com.daliangang.rndpub.operation;

import com.daliangang.rndpub.entity.Expert;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import xyz.erupt.annotation.fun.OperationHandler;

import java.util.List;

@Service
public class ExpertCopyApplicationQRCodeHandler implements OperationHandler<Expert, Void> {

    @Value("${erupt.expert.applyLink}")
    private String expertApplyLink;

    @Override
    public String exec(List<Expert> data, Void unused, String[] param) {
        String str =
                "    var aux = document.createElement('input'); " +
                        "    aux.setAttribute('value', '" + expertApplyLink + "'); " +
                        "    document.body.appendChild(aux); " +
                        "    aux.select(); " +
                        "    document.execCommand('copy'); " +
                        "    document.body.removeChild(aux); " +
                        "    this.msg.success('复制成功');";
        return str;
    }
}
