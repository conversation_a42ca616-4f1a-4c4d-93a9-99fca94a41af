/*
 * Copyright © 2020-2035 yundingyun All rights reserved.
 */
package com.daliangang.workbench.operation;

import com.daliangang.workbench.entity.Enterprise;
import org.springframework.stereotype.Service;
import xyz.erupt.annotation.fun.OperationHandler;
import xyz.erupt.core.util.MD5Util;
import xyz.erupt.jpa.dao.EruptDao;
import xyz.erupt.toolkit.utils.AssertUtils;
import xyz.erupt.toolkit.utils.EruptDaoUtils;
import xyz.erupt.toolkit.utils.NotifyUtils;
import xyz.erupt.toolkit.utils.SqlUtils;
import xyz.erupt.upms.model.EruptUser;
import xyz.erupt.upms.service.EruptPlatformService;
import xyz.erupt.upms.service.EruptUserService;

import javax.annotation.Resource;
import javax.transaction.Transactional;
import java.util.List;

@Service
public class EnterpriseResetPasswordHandler implements OperationHandler<Enterprise, Void> {

    @Resource
    private EruptDao eruptDao;
    @Resource
    private EruptUserService eruptUserService;

    @Override
    @Transactional
    public String exec(List<Enterprise> data, Void unused, String[] param) {
        for (Enterprise enterprise : data) {
            EruptUser adminUser = eruptDao.queryEntity(EruptUser.class, "account=" + SqlUtils.wrapStr(enterprise.getAdministrator()));
            AssertUtils.notNull(adminUser, "没有这个用户");
            String sql = "update e_upms_user set password='"+ MD5Util.digest("Qwer@120201")+"',is_md5=true where id=" + adminUser.getId();
            EruptDaoUtils.updateNoForeignKeyChecks(sql);
            eruptUserService.flushEruptUserCache(adminUser);
            return NotifyUtils.getSuccessNotify("密码已重置为【Qwer@120201】，请提醒企业及时修改密码。");
        }
        /*for (Enterprise enterprise : data) {
            EruptUser adminUser = eruptDao.queryEntity(EruptUser.class, "account=" + SqlUtils.wrapStr(enterprise.getAdministrator()));
            AssertUtils.notNull(adminUser, "没有这个用户");
            adminUser.setPassword("430df56ce0f1dfea4ba749d4060cd05b");
            adminUser.setIsMd5(true);
            eruptDao.merge(adminUser);
            return NotifyUtils.getSuccessNotify("密码已重置为【Qwer@120201】，请提醒企业及时修改密码。");
        }*/
        return null;
    }
}
