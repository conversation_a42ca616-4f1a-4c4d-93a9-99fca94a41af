kind: PersistentVolumeClaim
apiVersion: v1
metadata:
  name: dlg-workbench-data
  annotations:
    volume.beta.kubernetes.io/storage-class: "base-pv-nfs-storage"
spec:
  accessModes:
    - ReadWriteOnce
  resources:
    requests:
      storage: 10G
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: dlg-workbench
  namespace: default
spec:
  replicas: 1
  selector:
    matchLabels:
      app: dlg-workbench
  template:
    metadata:
      labels:
        app: dlg-workbench
    spec:
      volumes:
        - name: volume-localtime
          hostPath:
            path: /usr/share/zoneinfo/Asia/Shanghai
            type: ''
        - name: dlg-workbench-data
          persistentVolumeClaim:
            claimName: dlg-workbench-data
      containers:
        - name: dlg-workbench
          image: registry.cn-zhangjiakou.aliyuncs.com/ydy-lowcode/dlg-workbench:latest
          imagePullPolicy: IfNotPresent
          volumeMounts:
            - name: dlg-workbench-data
              mountPath: /application/data
            - name: volume-localtime
              mountPath: /etc/localtime
          ports:
            - containerPort: 8080
              name: app
            - containerPort: 4000
              name: glowroot
          livenessProbe:
            tcpSocket:
              port: 8080
            initialDelaySeconds: 30
            timeoutSeconds: 6
          readinessProbe:
            tcpSocket:
              port: 8080
            initialDelaySeconds: 30
            timeoutSeconds: 6
          env:
            - name: APP_REGISTRY
              valueFrom:
                configMapKeyRef:
                  key: APP_REGISTRY
                  name: dlg-workbench
            - name: APP_NAME
              valueFrom:
                configMapKeyRef:
                  key: APP_NAME
                  name: dlg-workbench
            - name: APP_VERSION
              valueFrom:
                configMapKeyRef:
                  key: APP_VERSION
                  name: dlg-workbench
            - name: SERVER_PORT
              valueFrom:
                configMapKeyRef:
                  key: SERVER_PORT
                  name: dlg-workbench
            - name: ERUPT_DOMAIN
              valueFrom:
                configMapKeyRef:
                  key: ERUPT_DOMAIN
                  name: dlg-workbench
            - name: ERUPT_FILE_DOMAIN
              valueFrom:
                configMapKeyRef:
                  key: ERUPT_FILE_DOMAIN
                  name: dlg-workbench
            - name: ERUPT_SOURCE_DOMAIN
              valueFrom:
                configMapKeyRef:
                  key: ERUPT_SOURCE_DOMAIN
                  name: dlg-workbench
            - name: ERUPT_OPERATE_DOMAIN
              valueFrom:
                configMapKeyRef:
                  key: ERUPT_OPERATE_DOMAIN
                  name: dlg-workbench
            - name: ERUPT_TITLE
              valueFrom:
                configMapKeyRef:
                  key: ERUPT_TITLE
                  name: dlg-workbench
            - name: ERUPT_DESC
              valueFrom:
                configMapKeyRef:
                  key: ERUPT_DESC
                  name: dlg-workbench
            - name: ERUPT_COPURIGHT
              valueFrom:
                configMapKeyRef:
                  key: ERUPT_COPURIGHT
                  name: dlg-workbench
            - name: ERUPT_AMAP_KEY
              valueFrom:
                configMapKeyRef:
                  key: ERUPT_AMAP_KEY
                  name: dlg-workbench
            - name: ERUPT_LOGO_PATH
              valueFrom:
                configMapKeyRef:
                  key: ERUPT_LOGO_PATH
                  name: dlg-workbench
            - name: ERUPT_LOGO_TEXT
              valueFrom:
                configMapKeyRef:
                  key: ERUPT_LOGO_TEXT
                  name: dlg-workbench
            - name: ERUPT_RIGHT_TOP_TEXT
              valueFrom:
                configMapKeyRef:
                  key: ERUPT_RIGHT_TOP_TEXT
                  name: dlg-workbench
            - name: ERUPT_HOME_PAGE
              valueFrom:
                configMapKeyRef:
                  key: ERUPT_HOME_PAGE
                  name: dlg-workbench
            - name: ERUPT_REGISTER_PAGE
              valueFrom:
                configMapKeyRef:
                  key: ERUPT_REGISTER_PAGE
                  name: dlg-workbench
            - name: ERUPT_FORGOT_PWD_PAGE
              valueFrom:
                configMapKeyRef:
                  key: ERUPT_FORGOT_PWD_PAGE
                  name: dlg-workbench
            - name: VERIFY_CODE_COUNT
              valueFrom:
                configMapKeyRef:
                  key: VERIFY_CODE_COUNT
                  name: dlg-workbench
            - name: CLOUD_NAME_SPACE
              valueFrom:
                configMapKeyRef:
                  key: CLOUD_NAME_SPACE
                  name: dlg-workbench
            - name: MYSQL_PRO
              valueFrom:
                configMapKeyRef:
                  key: MYSQL_PRO
                  name: dlg-workbench
            - name: MYSQL_HOST
              valueFrom:
                configMapKeyRef:
                  key: MYSQL_HOST
                  name: dlg-workbench
            - name: MYSQL-PORT
              valueFrom:
                configMapKeyRef:
                  key: MYSQL-PORT
                  name: dlg-workbench
            - name: MYSQL_DB
              valueFrom:
                configMapKeyRef:
                  key: MYSQL_DB
                  name: dlg-workbench
            - name: MYSQL_USER
              valueFrom:
                configMapKeyRef:
                  key: MYSQL_USER
                  name: dlg-workbench
            - name: MYSQL_PWD
              valueFrom:
                configMapKeyRef:
                  key: MYSQL_PWD
                  name: dlg-workbench
            - name: MYSQL_DRIVER
              valueFrom:
                configMapKeyRef:
                  key: MYSQL_DRIVER
                  name: dlg-workbench
            - name: REDIS_DATABASE
              valueFrom:
                configMapKeyRef:
                  key: REDIS_DATABASE
                  name: dlg-workbench
            - name: REDIS_HOST
              valueFrom:
                configMapKeyRef:
                  key: REDIS_HOST
                  name: dlg-workbench
            - name: REDIS_PORT
              valueFrom:
                configMapKeyRef:
                  key: REDIS_PORT
                  name: dlg-workbench
            - name: REDIS_PWD
              valueFrom:
                configMapKeyRef:
                  key: REDIS_PWD
                  name: dlg-workbench
            - name: DOC_API_TITILE
              valueFrom:
                configMapKeyRef:
                  key: DOC_API_TITILE
                  name: dlg-workbench
            - name: DOC_API_DESCRIPTION
              valueFrom:
                configMapKeyRef:
                  key: DOC_API_DESCRIPTION
                  name: dlg-workbench
            - name: DOC_API_COCAT
              valueFrom:
                configMapKeyRef:
                  key: DOC_API_COCAT
                  name: dlg-workbench
            - name: DOC_API_VERSION
              valueFrom:
                configMapKeyRef:
                  key: DOC_API_VERSION
                  name: dlg-workbench
            - name: DOC_API_GROUP
              valueFrom:
                configMapKeyRef:
                  key: DOC_API_GROUP
                  name: dlg-workbench
            - name: DOC_API_PACKAGE
              valueFrom:
                configMapKeyRef:
                  key: DOC_API_PACKAGE
                  name: dlg-workbench
            - name: DOC_MANUAL_TITILE
              valueFrom:
                configMapKeyRef:
                  key: DOC_MANUAL_TITILE
                  name: dlg-workbench
            - name: DOC_MANUAL_GROUP
              valueFrom:
                configMapKeyRef:
                  key: DOC_MANUAL_GROUP
                  name: dlg-workbench
            - name: COS_ACCESS_KEY
              valueFrom:
                configMapKeyRef:
                  key: COS_ACCESS_KEY
                  name: dlg-workbench
            - name: COS_SECRET_KEY
              valueFrom:
                configMapKeyRef:
                  key: COS_SECRET_KEY
                  name: dlg-workbench
            - name: COS_BUCKET
              valueFrom:
                configMapKeyRef:
                  key: COS_BUCKET
                  name: dlg-workbench
            - name: COS_REGION
              valueFrom:
                configMapKeyRef:
                  key: COS_REGION
                  name: dlg-workbench
            - name: MINIO_ENDPOINT
              valueFrom:
                configMapKeyRef:
                  key: MINIO_ENDPOINT
                  name: dlg-workbench
            - name: MINIO_BUCKET_NAME
              valueFrom:
                configMapKeyRef:
                  key: MINIO_BUCKET_NAME
                  name: dlg-workbench
            - name: MINIO_ACCESS_KEY
              valueFrom:
                configMapKeyRef:
                  key: MINIO_ACCESS_KEY
                  name: dlg-workbench
            - name: MINIO_SECRET_KEY
              valueFrom:
                configMapKeyRef:
                  key: MINIO_SECRET_KEY
                  name: dlg-workbench
            - name: XXL_JOB_HOST
              valueFrom:
                configMapKeyRef:
                  key: XXL_JOB_HOST
                  name: dlg-workbench
            - name: XXL_JOB_APPNAME
              valueFrom:
                configMapKeyRef:
                  key: XXL_JOB_APPNAME
                  name: dlg-workbench
            - name: GRAYLOG_HOST
              valueFrom:
                configMapKeyRef:
                  key: GRAYLOG_HOST
                  name: dlg-workbench
            - name: GRAYLOG_PORT
              valueFrom:
                configMapKeyRef:
                  key: GRAYLOG_PORT
                  name: dlg-workbench
            - name: GRAYLOG_ENV
              valueFrom:
                configMapKeyRef:
                  key: GRAYLOG_ENV
                  name: dlg-workbench
      imagePullSecrets:
        - name: docker-registry

---
apiVersion: v1
kind: Service
metadata:
  name: dlg-workbench
  namespace: default
spec:
  type: NodePort
  selector:
    app: dlg-workbench
  ports:
    - name: dlg-workbench-app
      port: 8080
      targetPort: 8080
      nodePort: 31080
    - name: dlg-workbench-glowroot
      port: 4000
      targetPort: 4000
      nodePort: 30400
