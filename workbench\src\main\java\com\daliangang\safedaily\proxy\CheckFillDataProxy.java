/*
 * Copyright © 2020-2035 yundingyun All rights reserved.
 */
package com.daliangang.safedaily.proxy;

import com.daliangang.core.DaliangangContext;
import com.daliangang.safedaily.entity.CheckFill;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import xyz.erupt.annotation.fun.DataProxy;
import xyz.erupt.annotation.query.Condition;
import xyz.erupt.jpa.dao.EruptDao;
import xyz.erupt.toolkit.utils.AssertUtils;
import xyz.erupt.toolkit.utils.EruptDaoUtils;
import xyz.erupt.toolkit.utils.SqlUtils;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

@Service
@Slf4j
public class CheckFillDataProxy implements DataProxy<CheckFill> {

    @Resource
    private EruptDao eruptDao;

    @Override
    public void beforeAdd(CheckFill checkFill) {
        String sql = "select * from tb_check_fill where year=" + checkFill.getYear() + " and org_code=" + SqlUtils.wrapStr(checkFill.getOrgCode());
        CheckFill inCheckFill = EruptDaoUtils.selectOne(sql, CheckFill.class);
        AssertUtils.isNull(inCheckFill, "同一年度只能上传一次");

        checkFill.setModifyTime(new Date());
    }


    @Override
    public String beforeFetch(List<Condition> conditions) {
        if (DaliangangContext.isDepartmentUser()) return "submitted=true";
        return null;
    }

    @Override
    public void beforeUpdate(CheckFill checkFill) {
        AssertUtils.isFalse(checkFill.getSubmitted(), "已提交不能修改");
        String sql = "select * from tb_check_fill where year=" + checkFill.getYear() + " and org_code=" + SqlUtils.wrapStr(checkFill.getOrgCode())+" and submitted=1";
        CheckFill inCheckFill = EruptDaoUtils.selectOne(sql, CheckFill.class);
        AssertUtils.isNull(inCheckFill, "同一年度只能上传一次");
    }

    @Override
    public void beforeDelete(CheckFill checkFill) {
        AssertUtils.isFalse(checkFill.getSubmitted(), "已提交不能删除");
    }

    @Override
    public void afterUpdate(CheckFill checkFill) {
        checkFill.setSubmitted(false);
        checkFill.setModifyTime(new Date());
        eruptDao.merge(checkFill);
    }
}
