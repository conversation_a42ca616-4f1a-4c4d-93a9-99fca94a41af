package com.daliangang.workbench.handler;

import com.daliangang.workbench.entity.EmployeeView;
import org.springframework.stereotype.Service;
import xyz.erupt.annotation.sub_field.Readonly;

@Service
public class EmployeeOwnerRoleReadOnlyHandler implements Readonly.ReadonlyHandler {
    @Override
    public boolean add(boolean add, String[] params) {
        if (EmployeeView.isSelfInfoMenu()) return true;
        return false;
    }

    @Override
    public boolean edit(boolean edit, String[] params) {
        //if(EmployeeView.isSelfInfoMenu())return true;
        return true;
    }
}
