package com.daliangang.majorisk.operation;

import com.daliangang.core.DaliangangContext;
import com.daliangang.majorisk.entity.*;
import com.daliangang.safedaily.entity.PlanManagement;
import com.deepoove.poi.XWPFTemplate;
import com.deepoove.poi.config.Configure;
import com.deepoove.poi.config.ConfigureBuilder;
import com.deepoove.poi.data.DocumentRenderData;
import com.deepoove.poi.data.Documents;
import com.deepoove.poi.data.Includes;
import com.deepoove.poi.data.Paragraphs;
import com.deepoove.poi.plugin.table.LoopRowTableRenderPolicy;
import com.deepoove.poi.policy.DocumentRenderPolicy;
import com.deepoove.poi.util.PoitlIOUtils;
import com.google.gson.internal.LinkedTreeMap;
import lombok.SneakyThrows;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.ClassPathResource;

import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;
import org.springframework.util.ClassUtils;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import xyz.erupt.annotation.fun.OperationHandler;
import xyz.erupt.annotation.sub_erupt.Tpl;
import xyz.erupt.common.http.EruptRemoteUtils;
import xyz.erupt.core.util.EruptSpringUtil;
import xyz.erupt.core.util.EruptUtil;
import xyz.erupt.core.view.EruptApiModel;
import xyz.erupt.jpa.model.BaseModel;
import xyz.erupt.toolkit.utils.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.transaction.Transactional;
import java.io.*;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @since :2023/4/7:17:17
 */
@Component
@Service
public class WordExportHandler implements Tpl.TplHandler{
        @Resource
        private FiveChecklistsEmergencyDisposalHandler fiveChecklistsEmergencyDisposalHandler;

        @Resource
        private FiveChecklistsMonitorHandler fiveChecklistsMonitorHandler;

        @Resource
        private FiveChecklistsPreventionAndControlHandler fiveChecklistsPreventionAndControlHandler;

        @Resource
        private FiveChecklistsResponsibilityHandler fiveChecklistsResponsibilityHandler;

        @Resource
        private FiveChecklistsFoundationHandler fiveChecklistsFoundation;
        @Value("${erupt-site.fileDomain}")
        private String fileDomain;


    @Override
    @SneakyThrows
    @Transactional
    public void bindTplData(Map<String, Object> binding, String[] params) {

        List<FiveDetail> data = (List<FiveDetail>) binding.get("rows");
        HttpServletResponse response = ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getResponse();
        Map<String, Object> binding2 = new HashMap<>();
        List<Map<String,Object>> tList=new ArrayList<Map<String,Object>>();
        data.forEach(v->{
            Map<String,Object> tMap = new HashMap<String, Object>();
            //基础清单
          //  FiveChecklistsFoundation Foundation = fiveChecklistsFoundation.detail(v.getFiveChecklistsFoundation().getId());
            FiveChecklistsFoundation Foundation = fiveChecklistsFoundation.detail(v.getId());
            // 企业名称
            String enterpriseName = DaliangangContext.getEnterpriseName(Foundation.getOrgCode());
            Foundation.setCompany(enterpriseName);
            // 企业名字标题
            binding2.put("company",enterpriseName);
            // 风险名字
            binding2.put("name",v.getRiskName());
            // 风险类型
            String fiveRiskType = EruptCompUtils.getChoiceValue(Foundation.getFiveRiskType());
            Foundation.setFiveRiskType(fiveRiskType);
            // 主要致险
            if (ObjectUtils.isNotEmpty(Foundation.getOtherRemark())) {
                if (Foundation.getMajorRisk().contains("其他")) {
                    String majorRisk = Foundation.getMajorRisk();
                    String replace = majorRisk.replace("其他", Foundation.getOtherRemark());
                    Foundation.setMajorRisk(replace);
                } else {
                    String majorRisk = Foundation.getMajorRisk()+"|"+Foundation.getOtherRemark();
                    Foundation.setMajorRisk(majorRisk);
                }

            }

            //人员伤亡
            String casualties = EruptCompUtils.getChoiceValue(Foundation.getCasualties());
            Foundation.setCasualties(casualties);
            // 财产损失
            String propertyLoss = EruptCompUtils.getChoiceValue(Foundation.getPropertyLoss());
            Foundation.setPropertyLoss(propertyLoss);
            // 环境影响
            String environmentalImpact = EruptCompUtils.getChoiceValue(Foundation.getEnvironmentalImpact());
            Foundation.setEnvironmentalImpact(environmentalImpact);
            // 社会影响
            String socialInfluence = EruptCompUtils.getChoiceValue(Foundation.getSocialInfluence());
            Foundation.setSocialInfluence(socialInfluence);

            // 责任分工清单
            Set<FiveChecklistsResponsibility> fiveChecklistsResponsibilities = fiveChecklistsResponsibilityHandler.detail(v.getId());
            List<FiveChecklistsResponsibility> unit = new ArrayList<>();
            List<FiveChecklistsResponsibility> safe = new ArrayList<>();
            List<FiveChecklistsResponsibility> business = new ArrayList<>();
            List<FiveChecklistsResponsibility> Team = new ArrayList<>();
            if (ObjectUtils.isNotEmpty(fiveChecklistsResponsibilities)) {
                fiveChecklistsResponsibilities.forEach(e->{
                    switch (e.getResponsibleType()){
                        case "responsibleType1":
                            String type1 = EruptCompUtils.getChoiceValue(e.getResponsibleType());
                            e.setResponsibleType(type1);
                            unit.add(e);
                            break;
                        case "responsibleType2":
                            String type2 = EruptCompUtils.getChoiceValue(e.getResponsibleType());
                            e.setResponsibleType(type2);
                            safe.add(e);
                            break;
                        case "responsibleType3":
                            String type3 = EruptCompUtils.getChoiceValue(e.getResponsibleType());
                            e.setResponsibleType(type3);
                            business.add(e);
                            break;
                        default :
                            String type4 = EruptCompUtils.getChoiceValue(e.getResponsibleType());
                            e.setResponsibleType(type4);
                            Team.add(e);
                    }

                });
            }
            if (unit.size() == 0) {
                FiveChecklistsResponsibility messageController = EruptSpringUtil.getBean(FiveChecklistsResponsibility.class);
                messageController.setName("暂无");
                messageController.setPost("暂无");
                messageController.setContactTel("暂无");
                messageController.setControlResponsibility("暂无");
                unit.add(messageController);
            }
            if (safe.size() == 0) {
                FiveChecklistsResponsibility messageController = EruptSpringUtil.getBean(FiveChecklistsResponsibility.class);
                messageController.setName("暂无");
                messageController.setPost("暂无");
                messageController.setContactTel("暂无");
                messageController.setControlResponsibility("暂无");
                safe.add(messageController);
            }
            if (business.size() == 0) {
                FiveChecklistsResponsibility messageController = EruptSpringUtil.getBean(FiveChecklistsResponsibility.class);
                messageController.setName("暂无");
                messageController.setPost("暂无");
                messageController.setContactTel("暂无");
                messageController.setControlResponsibility("暂无");
                business.add(messageController);
            }
            if (Team.size() == 0) {
                FiveChecklistsResponsibility messageController = EruptSpringUtil.getBean(FiveChecklistsResponsibility.class);
                messageController.setName("暂无");
                messageController.setPost("暂无");
                messageController.setContactTel("暂无");
                messageController.setControlResponsibility("暂无");
                Team.add(messageController);
            }
            tMap.put("unit",unit);
            tMap.put("safe",safe);
            tMap.put("business",business);
            tMap.put("Team",Team);



            // 防控措施清单
            FiveChecklistsPreventionAndControl Prevention = fiveChecklistsPreventionAndControlHandler.detail(v.getId());
            if (ObjectUtils.isNotEmpty(Prevention.getOtherRemark())) {
                if (Prevention.getPointsOfPrevention().contains("其他")) {
                    String majorRisk = Prevention.getPointsOfPrevention();
                    String replace = majorRisk.replace("其他", Prevention.getOtherRemark());
                    Prevention.setPointsOfPrevention(replace);
                } else {
                    String majorRisk = Prevention.getPointsOfPrevention()+"|"+Prevention.getOtherRemark();
                    Prevention.setPointsOfPrevention(majorRisk);
                }

            }

            // 监控检测清单
            FiveChecklistsMonitor Monitor = fiveChecklistsMonitorHandler.detail(v.getId());
            StringBuffer stringBuffer = new StringBuffer();
            if (!Monitor.getShipWorkNum().equals("0")) {
                stringBuffer.append("今日装卸船作业共"+Monitor.getShipWorkNum()+"项，位于"+Monitor.getShipWorkAddress());
            }
            if (!Monitor.getCarWorkNum().equals("0")) {
                stringBuffer.append("今日装卸汽车作业共"+Monitor.getCarWorkNum()+"项，位于"+Monitor.getCarWorkAddress());
            }
            if (!Monitor.getTrainWorkNum().equals("0")) {
                stringBuffer.append("今日装卸火车作业共"+Monitor.getTrainWorkNum()+"项，位于"+Monitor.getTrainWorkAddress());
            }
            if (!Monitor.getFireWorkNum().equals("0")) {
                stringBuffer.append("今日动火作业共"+Monitor.getFireWorkNum()+"项");
            }
            if (!Monitor.getRestrictedWorkNum().equals("0")) {
                stringBuffer.append("今日受限空间作业共"+Monitor.getRestrictedWorkNum()+"项");
            }
            if (stringBuffer.length() == 0) {
                stringBuffer.append("本企业今日暂无相关作业");
            }

            tMap.put("workInfo",stringBuffer);

            // 应急处置清单
            FiveChecklistsEmergencyDisposal Disposal = fiveChecklistsEmergencyDisposalHandler.detail(v.getId());
            List<Map<String,Object>> fileList=new ArrayList<Map<String,Object>>();

            List<String> fileUrl = Stream.of(Disposal.getFile().split("\\|")).collect(Collectors.toList());
            fileUrl.forEach(m->{
                Map<String,Object> file = new HashMap<String, Object>();
                Map<String,String> tu = new HashMap<String, String>();
               // String url = "http://116.204.97.7:30900/test"+m;
               // String url = fileDomain+m;
                String url = EruptUtil.findAttachmentProxy().internalDomain()+m;
                tu.put("file",url);

                file.put("tu",tu);
                fileList.add(file);
            });
            // 图片
            tMap.put("fileInfo",fileList);

            String num= Disposal.getEmergencyDrillNum()!=null ? Disposal.getEmergencyDrillNum(): "0";
            String name = Disposal.getEmergencyDrillName()!=null ? Disposal.getEmergencyDrillName(): "无";
            String emergency = "";
            if(name.equals("无")) {
                emergency = "本年度公司共开展各类应急演练"+num+"次";

            } else {
                emergency = "本年度公司共开展各类应急演练"+num+"次，包括"+name;
            }

            Disposal.setEmergencyDrillName(emergency);


            tMap.put("Foundation", Foundation);
            tMap.put("Prevention", Prevention);
            tMap.put("Monitor", Monitor);
            tMap.put("Disposal", Disposal);
            tList.add(tMap);
        });
        //格式化格式
        String format = "YYYY-MM-dd";
        String formatDateTime = LocalDateTime.now().format(DateTimeFormatter.ofPattern(format));

        binding2.put("typePro",tList);

        binding2.put("datatime",formatDateTime);


        // 生成文档

        // 绑定插件，用于循环表格
        LoopRowTableRenderPolicy policy = new LoopRowTableRenderPolicy();
        Configure config = Configure.builder()
                .bind("unit", policy)
                .bind("safe", policy)
                .bind("business", policy)
                .bind("Team", policy)
                .useSpringEL()
                .build();
        // 获取路径
        InputStream inputStream = FileUtils.readInputStream("tpl/qy.docx");

        XWPFTemplate template = XWPFTemplate.compile(inputStream,config).render(binding2);
        response.reset();
        response.setContentType("application/octet-stream;charset=UTF-8");
        response.setHeader("Content-Disposition","attachment;filename=" + java.net.URLEncoder.encode("企业五清单" + ".docx",  "UTF-8"));
        OutputStream out = response.getOutputStream();
        BufferedOutputStream bos = new BufferedOutputStream(out);
        template.write(bos);
        bos.flush();
        out.flush();
        PoitlIOUtils.closeQuietlyMulti(template, bos, out);
    }

    //--------------------------------------------------------------------------------------------------------------------------

//    @Transactional
//    @SneakyThrows
//    @Override
//    public String exec(List<FiveDetail> data, Void aVoid, String[] param) {
//
//        Export export = EruptSpringUtil.getBean(Export.class);
//        export.exportWork(data);
//
//         return null;
//    }
//
//
//
//    @RestController
//    @Transactional
//    public class Export{
//        @Resource
//        private FiveChecklistsEmergencyDisposalHandler fiveChecklistsEmergencyDisposalHandler;
//
//        @Resource
//        private FiveChecklistsMonitorHandler fiveChecklistsMonitorHandler;
//
//        @Resource
//        private FiveChecklistsPreventionAndControlHandler fiveChecklistsPreventionAndControlHandler;
//
//        @Resource
//        private FiveChecklistsResponsibilityHandler fiveChecklistsResponsibilityHandler;
//
//        @Resource
//        private FiveChecklistsFoundationHandler fiveChecklistsFoundation;
//
//        @RequestMapping("/erupt-api/download/exportWork")
//        @SneakyThrows
//        public void exportWork(List<FiveDetail> data) {
//            //  List<FiveDetail> data = (List<FiveDetail>) binding.get("rows");
//            HttpServletResponse response = ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getResponse();
//            Map<String, Object> binding2 = new HashMap<>();
//            List<Map<String,Object>> tList=new ArrayList<Map<String,Object>>();
//            data.forEach(v->{
//                Map<String,Object> tMap = new HashMap<String, Object>();
//                //基础清单
//                FiveChecklistsFoundation Foundation = fiveChecklistsFoundation.detail(v.getFiveChecklistsFoundation().getId());
//                // 企业名称
//                String enterpriseName = DaliangangContext.getEnterpriseName(Foundation.getOrgCode());
//                Foundation.setCompany(enterpriseName);
//                // 企业名字标题
//                binding2.put("company",enterpriseName);
//                // 风险名字
//                binding2.put("name",v.getRiskName());
//                // 风险类型
//                String fiveRiskType = EruptCompUtils.getChoiceValue(Foundation.getFiveRiskType());
//                Foundation.setFiveRiskType(fiveRiskType);
//                // 主要致险
//
//                //人员伤亡
//                String casualties = EruptCompUtils.getChoiceValue(Foundation.getCasualties());
//                Foundation.setCasualties(casualties);
//                // 财产损失
//                String propertyLoss = EruptCompUtils.getChoiceValue(Foundation.getPropertyLoss());
//                Foundation.setPropertyLoss(propertyLoss);
//                // 环境影响
//                String environmentalImpact = EruptCompUtils.getChoiceValue(Foundation.getEnvironmentalImpact());
//                Foundation.setEnvironmentalImpact(environmentalImpact);
//                // 社会影响
//                String socialInfluence = EruptCompUtils.getChoiceValue(Foundation.getSocialInfluence());
//                Foundation.setSocialInfluence(socialInfluence);
//
//                // 责任分工清单
//                Set<FiveChecklistsResponsibility> fiveChecklistsResponsibilities = fiveChecklistsResponsibilityHandler.detail(v.getId());
//                List<FiveChecklistsResponsibility> unit = new ArrayList<>();
//                List<FiveChecklistsResponsibility> safe = new ArrayList<>();
//                List<FiveChecklistsResponsibility> business = new ArrayList<>();
//                List<FiveChecklistsResponsibility> Team = new ArrayList<>();
//                if (ObjectUtils.isNotEmpty(fiveChecklistsResponsibilities)) {
//                    fiveChecklistsResponsibilities.forEach(e->{
//                        switch (e.getResponsibleType()){
//                            case "responsibleType1":
//                                String type1 = EruptCompUtils.getChoiceValue(e.getResponsibleType());
//                                e.setResponsibleType(type1);
//                                unit.add(e);
//                                break;
//                            case "responsibleType2":
//                                String type2 = EruptCompUtils.getChoiceValue(e.getResponsibleType());
//                                e.setResponsibleType(type2);
//                                safe.add(e);
//                                break;
//                            case "responsibleType3":
//                                String type3 = EruptCompUtils.getChoiceValue(e.getResponsibleType());
//                                e.setResponsibleType(type3);
//                                business.add(e);
//                                break;
//                            default :
//                                String type4 = EruptCompUtils.getChoiceValue(e.getResponsibleType());
//                                e.setResponsibleType(type4);
//                                Team.add(e);
//                        }
//
//                    });
//                }
//                if (unit.size() == 0) {
//                    FiveChecklistsResponsibility messageController = EruptSpringUtil.getBean(FiveChecklistsResponsibility.class);
//                    messageController.setName("暂无");
//                    messageController.setPost("暂无");
//                    messageController.setContactTel("暂无");
//                    messageController.setControlResponsibility("暂无");
//                    unit.add(messageController);
//                }
//                if (safe.size() == 0) {
//                    FiveChecklistsResponsibility messageController = EruptSpringUtil.getBean(FiveChecklistsResponsibility.class);
//                    messageController.setName("暂无");
//                    messageController.setPost("暂无");
//                    messageController.setContactTel("暂无");
//                    messageController.setControlResponsibility("暂无");
//                    safe.add(messageController);
//                }
//                if (business.size() == 0) {
//                    FiveChecklistsResponsibility messageController = EruptSpringUtil.getBean(FiveChecklistsResponsibility.class);
//                    messageController.setName("暂无");
//                    messageController.setPost("暂无");
//                    messageController.setContactTel("暂无");
//                    messageController.setControlResponsibility("暂无");
//                    business.add(messageController);
//                }
//                if (Team.size() == 0) {
//                    FiveChecklistsResponsibility messageController = EruptSpringUtil.getBean(FiveChecklistsResponsibility.class);
//                    messageController.setName("暂无");
//                    messageController.setPost("暂无");
//                    messageController.setContactTel("暂无");
//                    messageController.setControlResponsibility("暂无");
//                    Team.add(messageController);
//                }
//                tMap.put("unit",unit);
//                tMap.put("safe",safe);
//                tMap.put("business",business);
//                tMap.put("Team",Team);
//
//
//
//                // 防控措施清单
//                FiveChecklistsPreventionAndControl Prevention = fiveChecklistsPreventionAndControlHandler.detail(v.getFiveChecklistsPreventionAndControl().getId());
//                // 监控检测清单
//                FiveChecklistsMonitor Monitor = fiveChecklistsMonitorHandler.detail(v.getFiveChecklistsMonitor().getId());
//                StringBuffer stringBuffer = new StringBuffer();
//                if (!Monitor.getShipWorkNum().equals("0")) {
//                    stringBuffer.append("今日装卸船作业共"+Monitor.getShipWorkNum()+"项，位于"+Monitor.getShipWorkAddress());
//                }
//                if (!Monitor.getCarWorkNum().equals("0")) {
//                    stringBuffer.append("今日装卸车作业共"+Monitor.getCarWorkNum()+"项，位于"+Monitor.getCarWorkAddress());
//                }
//                if (!Monitor.getTrainWorkNum().equals("0")) {
//                    stringBuffer.append("今日装卸火车作业共"+Monitor.getTrainWorkNum()+"项，位于"+Monitor.getTrainWorkAddress());
//                }
//                if (!Monitor.getFireWorkNum().equals("0")) {
//                    stringBuffer.append("今日装卸船作业共"+Monitor.getFireWorkNum()+"项");
//                }
//                if (!Monitor.getRestrictedWorkNum().equals("0")) {
//                    stringBuffer.append("今日装卸船作业共"+Monitor.getRestrictedWorkNum()+"项");
//                }
//                if (stringBuffer.length() == 0) {
//                    stringBuffer.append("本企业今日暂无相关作业");
//                }
//
//                tMap.put("workInfo",stringBuffer);
//
//                // 应急处置清单
//                FiveChecklistsEmergencyDisposal Disposal = fiveChecklistsEmergencyDisposalHandler.detail(v.getFiveChecklistsEmergencyDisposal().getId());
//                List<Map<String,Object>> fileList=new ArrayList<Map<String,Object>>();
//
//                List<String> fileUrl = Stream.of(Disposal.getFile().split("\\|")).collect(Collectors.toList());
//                fileUrl.forEach(m->{
//                    Map<String,Object> file = new HashMap<String, Object>();
//                    Map<String,String> tu = new HashMap<String, String>();
//                    String url = "http://116.204.97.7:30900/test"+m;
//                    tu.put("file",url);
//
//                    file.put("tu",tu);
//                    fileList.add(file);
//                });
//                // 图片
//                tMap.put("fileInfo",fileList);
//
//                String num= Disposal.getEmergencyDrillNum()!=null ? Disposal.getEmergencyDrillNum(): "0";
//                String name = Disposal.getEmergencyDrillName()!=null ? Disposal.getEmergencyDrillName(): "无";
//                String emergency = "本年度公司共开展各类应急演练"+num+"次，包括"+name;
//                Disposal.setEmergencyDrillName(emergency);
//
//
//                tMap.put("Foundation", Foundation);
//                tMap.put("Prevention", Prevention);
//                tMap.put("Monitor", Monitor);
//                tMap.put("Disposal", Disposal);
//                tList.add(tMap);
//            });
//            //格式化格式
//            String format = "YYYY-MM-dd";
//            String formatDateTime = LocalDateTime.now().format(DateTimeFormatter.ofPattern(format));
//
//            binding2.put("typePro",tList);
//
//            binding2.put("datatime",formatDateTime);
//
//
//            // 生成文档
//
//            // 绑定插件，用于循环表格
//            LoopRowTableRenderPolicy policy = new LoopRowTableRenderPolicy();
//            Configure config = Configure.builder()
//                    .bind("unit", policy)
//                    .bind("safe", policy)
//                    .bind("business", policy)
//                    .bind("Team", policy)
//                    .useSpringEL()
//                    .build();
//
//            File directory = new File("src/main/resources");
//            String reportPath = directory.getCanonicalPath();
//            String resource =reportPath + "/tpl/qy.docx";
//            XWPFTemplate template = XWPFTemplate.compile(resource,config).render(binding2);
//
//
//            response.reset();
//            response.setContentType("application/octet-stream;charset=UTF-8");
//            response.setHeader("Content-Disposition","attachment;filename=" + java.net.URLEncoder.encode("企业五清单" + ".docx",  "UTF-8"));
//            OutputStream out = response.getOutputStream();
//            BufferedOutputStream bos = new BufferedOutputStream(out);
//            template.write(bos);
//            bos.flush();
//            out.flush();
//            PoitlIOUtils.closeQuietlyMulti(template, bos, out);
//
//        }
//    }


    /**
     * 将文件流写入文件中
     * @param is
     */
    private File InputStreamToFile(InputStream is) throws Exception{
        File file = File.createTempFile("EquipmentFile", ".docx");
        FileOutputStream fos = new FileOutputStream(file);
        byte[] b = new byte[1024];
        while ((is.read(b)) != -1) {
            fos.write(b);// 写入数据
        }
        is.close();
        fos.close();// 保存数据
        return file;
    }

    public String inputStream2StringNew(InputStream is) {
        try {
            ByteArrayOutputStream boa = new ByteArrayOutputStream();
            int len = 0;
            byte[] buffer = new byte[1024];

            while ((len = is.read(buffer)) != -1) {
                boa.write(buffer, 0, len);
            }
            is.close();
            boa.close();
            byte[] result = boa.toByteArray();

            String temp = new String(result);

            // 识别编码
            if (temp.contains("utf-8")) {
                return new String(result, "utf-8");
            } else if (temp.contains("gb2312")) {
                return new String(result, "gb2312");
            } else {
                return new String(result, "utf-8");
            }
        } catch (Exception e) {
            // TODO Auto-generated catch block
            e.printStackTrace();
            return null;
        }
    }


}
