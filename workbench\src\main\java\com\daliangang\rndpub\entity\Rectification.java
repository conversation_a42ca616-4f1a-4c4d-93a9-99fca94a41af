/*
* Copyright © 2020-2035 yundingyun All rights reserved.
*/
package com.daliangang.rndpub.entity;

import com.daliangang.rndpub.form.RectificationForm;
import com.daliangang.rndpub.operation.RectificationRectificationHandler;
import com.daliangang.rndpub.proxy.RectificationDataProxy;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import xyz.erupt.annotation.Erupt;
import xyz.erupt.annotation.EruptField;
import xyz.erupt.annotation.sub_erupt.Power;
import xyz.erupt.annotation.sub_erupt.RowOperation;
import xyz.erupt.annotation.sub_field.Edit;
import xyz.erupt.annotation.sub_field.EditType;
import xyz.erupt.annotation.sub_field.Readonly;
import xyz.erupt.annotation.sub_field.View;
import xyz.erupt.annotation.sub_field.sub_edit.*;
import xyz.erupt.jpa.model.MetaModel;
import xyz.erupt.toolkit.db.Comment;
import xyz.erupt.toolkit.handler.EruptDictCodeChoiceFetchHandler;
import xyz.erupt.toolkit.handler.SqlChoiceFetchHandler;
import xyz.erupt.toolkit.utils.TplUtils;
import xyz.erupt.upms.handler.GotoPageHandler;

import javax.persistence.*;
import java.util.Date;
import java.util.HashSet;
import java.util.Set;

@Erupt(name = "整改管理", power = @Power(add = false, delete = false, export = true, importable = false, viewDetails = false, edit = false)
, dataProxy = RectificationDataProxy.class
, rowOperation = {

@RowOperation(title = "整改", icon = "fa fa-pause",
        ifExpr = "item.rectificationStatus == '待整改'",
        code = TplUtils.REMOTE_ERUPT_CODE + "rectification",
        eruptClass = RectificationForm.class,
        operationHandler = RectificationRectificationHandler.class,
        mode = RowOperation.Mode.SINGLE),
        @RowOperation(title = "返回", icon = "fa fa-mail-reply",
                confirm=false,
                operationParam="ProblemRectificationManage",
                operationHandler = GotoPageHandler.class,
                mode = RowOperation.Mode.BUTTON),
})
@Entity
@Table(name = "tb_inspection_results")
@Getter
@Setter
@Comment("整改管理")
public class Rectification extends MetaModel {

    @EruptField(
            views = @View(title = "检查名称",show = false),
            edit = @Edit(title = "检查名称", type = EditType.CHOICE, notNull = true,
                    search = @Search,
                    choiceType = @ChoiceType(fetchHandler = SqlChoiceFetchHandler.class, fetchHandlerParams = "select id,name from tb_procedure", reload = true)
            ))
    @Comment("检查名称")
    @ApiModelProperty("检查名称")
    private String inspectionName;

    @EruptField(
            views = @View(title = "检查对象",show = false),
            edit = @Edit(title = "检查对象", type = EditType.REFERENCE_TREE, notNull = true,
                    referenceTreeType = @ReferenceTreeType(type = ReferenceTreeType.Type.CHOICE, dependField = "inspectionName", dependColumn = "procedure_id"),
                    choiceType = @ChoiceType(fetchHandler = SqlChoiceFetchHandler.class,
                            fetchHandlerParams = {"select org_code,enterprise_name from tb_enterprise_information", "5000", "and state=1"})))
    @Comment("检查对象")
    @ApiModelProperty("检查对象")
    private String checkObject;

    @EruptField(
            views = @View(title = "检查事项"),
            edit = @Edit(title = "检查事项", type = EditType.REFERENCE_TREE, notNull = true,
                    referenceTreeType = @ReferenceTreeType(type = ReferenceTreeType.Type.CHOICE, dependField = "inspectionName", dependColumn = "procedure_id"),
                    choiceType = @ChoiceType(fetchHandler = SqlChoiceFetchHandler.class,
                            fetchHandlerParams = {"select distinct inspection_items from tb_inspection_items", "5000", "and state=1"})))
    @Comment("检查事项")
    @ApiModelProperty("检查事项")
    private String inspectionItems;

//	@EruptField(
//		views = @View(title = "检查内容（一级）"),
//		edit = @Edit(title = "检查内容（一级）", type = EditType.CHOICE, notNull = true,
//		choiceType = @ChoiceType(fetchHandler = SqlChoiceFetchHandler.class, fetchHandlerParams = "select check_first from tb_inspection_items")))
//	@Comment("检查内容（一级）")
//	@ApiModelProperty("检查内容（一级）")
//	private String inspectionContent;

    @EruptField(
            views = @View(title = "问题描述"),
            edit = @Edit(title = "问题描述", type = EditType.TEXTAREA, search = @Search(vague = true),notNull = true))
    @Comment("问题描述")
    @ApiModelProperty("问题描述")
    private @Lob String problemDescription;


    @EruptField(
            views = @View(title = "检查依据"),
            edit = @Edit(title = "检查依据", type = EditType.TEXTAREA, notNull = true,
                    inputType = @InputType))
    @Comment("检查依据")
    @ApiModelProperty("检查依据")
    @Lob
    private String inspectionBasis;

    @EruptField(
            views = @View(title = "检查依据附件名称", show = false),
            edit = @Edit(title = "检查依据附件名称", type = EditType.ATTACHMENT,
                    attachmentType = @AttachmentType(type = AttachmentType.Type.BASE, maxLimit = 1, fileTypes = {".pdf", ".doc", ".docx", ".xlsx", ".png", ".jpg", ".xls", ".ppt", ".pptx", ".zip", ".rar"})))
    @Comment("检查依据附件名称")
    @ApiModelProperty("检查依据附件名称")
    private String inspectionBasisFile;

    @EruptField(
            views = @View(title = "整改建议"),
            edit = @Edit(title = "整改建议", type = EditType.TEXTAREA, notNull = true,
                    inputType = @InputType))
    @Comment("整改建议")
    @ApiModelProperty("整改建议")
    @Lob
    private String proposal;

    @EruptField(
            views = @View(title = "整改建议附件名称", show = false),
            edit = @Edit(title = "整改建议附件名称", type = EditType.ATTACHMENT,
                    attachmentType = @AttachmentType(type = AttachmentType.Type.BASE, maxLimit = 1, fileTypes = {".pdf", ".doc", ".docx", ".xlsx", ".png", ".jpg", ".xls", ".ppt", ".pptx", ".zip", ".rar"})))
    @Comment("整改建议附件名称")
    @ApiModelProperty("整改建议附件名称")
    private String proposalFile;

    @EruptField(
            views = @View(title = "整改状态", show = true),
            edit = @Edit(title = "整改状态", type = EditType.CHOICE, notNull = false, show = false,
                    choiceType = @ChoiceType(fetchHandler = EruptDictCodeChoiceFetchHandler.class, fetchHandlerParams = "rectificationStatus")))
    @Comment("整改状态")
    @ApiModelProperty("整改状态")
    private String rectificationStatus;

    @EruptField(
            views = @View(title = "审核状态", show = true),
            edit = @Edit(title = "审核状态", type = EditType.CHOICE, notNull = false, show = false,
                    choiceType = @ChoiceType(fetchHandler = EruptDictCodeChoiceFetchHandler.class, fetchHandlerParams = "rectificationAuditStatus")))
    @Comment("审核状态")
    @ApiModelProperty("审核状态")
    private String rectificationAuditStatus;


    @EruptField(
            views = @View(title = "复查结果", show = true),
            edit = @Edit(title = "复查结果", type = EditType.CHOICE, notNull = false, show = false,
                    choiceType = @ChoiceType(fetchHandler = EruptDictCodeChoiceFetchHandler.class, fetchHandlerParams = "rectificationReviewResults")))
    @Comment("复查结果")
    @ApiModelProperty("复查结果")
    private String inspectionResult;

    @EruptField(
            views = @View(title = "整改截止时间（年月日）"),
            edit = @Edit(title = "整改截止时间（年月日）", type = EditType.DATE,
                    dateType = @DateType))
    @Comment("整改截止时间（年月日）")
    @ApiModelProperty("整改截止时间（年月日）")
    private Date deadline;

    @EruptField(
            views = @View(title = "是否逾期", show = true,width = "100px"),
            edit = @Edit(title = "是否逾期", type = EditType.CHOICE, notNull = false, show = false,search = @Search(vague = true),
                    choiceType = @ChoiceType(vl = {
                            @VL(value = "0",label = "未逾期"),
                            @VL(value = "1",label = "已逾期"),
                            @VL(value = "2",label = "逾期已整改")
                    })))
    @Comment("是否逾期")
    @ApiModelProperty("是否逾期")
    private String beOverdue;


    @EruptField(
            views = @View(title = "发布状态", show = false),
            edit = @Edit(title = "发布状态",
                    type = EditType.BOOLEAN,notNull = false, show = false,boolType = @BoolType(trueText = "已发布", falseText = "未发布")))
    @Comment("发布状态")
    @ApiModelProperty("发布状态")
    private Boolean publishStatus;

    @EruptField(
            views = @View(title = "整改时间", show = false),
            edit = @Edit(title = "整改时间", type = EditType.DATE, notNull = true,show = false,
                    dateType = @DateType))
    private java.sql.Date rectificationTime;


    @OneToMany(cascade = CascadeType.ALL, orphanRemoval = true)
    @JoinColumn(name = "rectifys_id")
    @EruptField(views = @View(title = "整改内容", show = false),edit = @Edit(title = "整改内容", type = EditType.TAB_TABLE_ADD, readonly = @Readonly(add = true, edit = true)))
    private Set<Rectify> rectifys;

    public Set<Rectify> getRectifysOrDefault(){
        if(rectifys == null) rectifys = new HashSet<>();
        return this.rectifys;
    }
}
