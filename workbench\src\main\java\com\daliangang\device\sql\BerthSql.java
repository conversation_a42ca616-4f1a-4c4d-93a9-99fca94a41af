package com.daliangang.device.sql;

import org.springframework.stereotype.Repository;

/**
 * <AUTHOR>
 * @since :2023/4/24:14:46
 */
@Repository
public class BerthSql {

    // 统计员工人数
    public String selectBerthNum (String orgCode) {

        String sql = "select COUNT(*) as num ,'bw' as type from tb_berth tb where ";
        sql += "  org_code  "+orgCode+"";

        sql += "  UNION all select COUNT(*) as num ,'cg' as type from tb_storage_tank tst where ";
        sql += "  org_code  "+orgCode+"";

        sql += "  UNION all select COUNT(*) as num ,'dc' as type from tb_yard ty where";
        sql += "  org_code  "+orgCode+"";

        sql += "  UNION all select COUNT(*) as num ,'ck' as type  from tb_warehouse tw where";
        sql += "  org_code  "+orgCode+"";

        sql += "  UNION all select COUNT(*) as num ,'zxt' as type  from tb_loading_dock tld where";
        sql += "  org_code "+orgCode+"";

        return sql;
    }


//    public String selectNumThree (String orgCode) {
//
//        String sql = " select COUNT(*) as num,'无' as volume,'泊位数量' as type,'无' as hongKongCertificateValidity, '无' as zdnum,'无' as zsnum  from tb_berth tb where ";
//        sql += " org_code ='"+orgCode+"'";
//
//        sql += "  UNION all select  COUNT(*) as num,'无' as volume,'罐区数量' as type,'无' as hongKongCertificateValidity, '无' as zdnum,'无' as zsnum from tb_tank_farm ttf where ";
//        sql += " org_code ='"+orgCode+"'";
//
//        sql += "  UNION all  select  COUNT(*) as num,'无' as volume,'罐组数量' as type,'无' as hongKongCertificateValidity, '无' as zdnum,'无' as zsnum  from tb_tank_group ttg where ";
//        sql += " org_code ='"+orgCode+"'";
//
//        sql += "  UNION all  select COUNT(*) as num,SUM(volume) as volume,'储罐数量' as type,'无' as hongKongCertificateValidity, '无' as zdnum,'无' as zsnum  from tb_storage_tank tst where";
//        sql += " org_code ='"+orgCode+"'";
//
//        sql += " UNION all select  '无' as num,'无' as volume,'港经证有效期' as type, (CASE when CURRENT_DATE() > validity_period  then '过期' when CURRENT_DATE() <= validity_period then  validity_period  end) as hongKongCertificateValidity,'无' as zdnum,'无' as zsnum from tb_enterprise te where ";
//        sql += " org_code ='"+orgCode+"'";
//
//        sql += " UNION all select '无' as num,'无' as volume,'水路运输从业资格证' as type, '无' as hongKongCertificateValidity, '无' as zdnum,count(tec.id) as zsnum  from tb_employee_information tei  left join tb_employee_certificate tec on tei.id  = tec.employee_information_id  where tec.certificate_type = 'waterwayTransportation' and ";
//        sql += " tei.org_code ='"+orgCode+"'";
//
//        sql += " UNION all select '无' as num,'无' as volume,'特种设备操作证书' as type, '无' as hongKongCertificateValidity, '无' as zdnum,count(tec.id) as zsnum  from tb_employee_information tei  left join tb_employee_certificate tec on tei.id  = tec.employee_information_id where tec.certificate_type = 'specialEquipment' and ";
//        sql += " tei.org_code ='"+orgCode+"'";
//
//        sql += "  UNION all select '无' as num,'无' as volume,'特种作业操作证书' as type, '无' as hongKongCertificateValidity, '无' as zdnum,count(tec.id) as zsnum  from tb_employee_information tei  left join tb_employee_certificate tec on tei.id  = tec.employee_information_id where tec.certificate_type = 'specialOperations' and ";
//        sql += " tei.org_code ='"+orgCode+"'";
//
//        sql += "  UNION all select '无' as num,'无' as volume, '其他证书' as type,'无' as hongKongCertificateValidity, '无' as zdnum,count(tec.id) as zsnum  from tb_employee_information tei  left join tb_employee_certificate tec on tei.id  = tec.employee_information_id where tec.certificate_type = 'otherCertificates' and ";
//        sql += " tei.org_code ='"+orgCode+"'";
//
//        sql += " UNION all select  '无' as num,'无' as volume,'重大风险源备案' as type, '无' as hongKongCertificateValidity, COUNT(tpd.id) as zdnum,'无' as zsnum from tb_preplan tp left join tb_preplan_data tpd on tp.id = tpd.data_source_id where ";
//        sql += " tp.org_code ='"+orgCode+"'";
//
//        return sql;






  //  }
}
