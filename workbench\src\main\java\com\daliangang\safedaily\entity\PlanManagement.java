/*
 * Copyright © 2020-2035 yundingyun All rights reserved.
 */
package com.daliangang.safedaily.entity;

import com.daliangang.core.CompanyRenderHandler;
import com.daliangang.safedaily.proxy.PlanManagementDataProxy;
import com.daliangang.workbench.handler.EmployeeExternalRenderHandler;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import xyz.erupt.annotation.Erupt;
import xyz.erupt.annotation.EruptField;
import xyz.erupt.annotation.PreDataProxy;
import xyz.erupt.annotation.expr.ExprBool;
import xyz.erupt.annotation.sub_erupt.Power;
import xyz.erupt.annotation.sub_erupt.RowOperation;
import xyz.erupt.annotation.sub_field.Edit;
import xyz.erupt.annotation.sub_field.EditType;
import xyz.erupt.annotation.sub_field.Readonly;
import xyz.erupt.annotation.sub_field.View;
import xyz.erupt.annotation.sub_field.sub_edit.*;
import xyz.erupt.toolkit.attachment.AttachmentDownloadHandler;
import xyz.erupt.toolkit.colorfont.ColorStateTimeFont;
import xyz.erupt.toolkit.colorfont.ColorStateTimeFontDataProxy;
import xyz.erupt.toolkit.db.Comment;
import xyz.erupt.toolkit.handler.DelOperationHandler;
import xyz.erupt.toolkit.handler.EruptDictTagFetchHandler;
import xyz.erupt.toolkit.handler.RemoteEntityChoiceFetchHandler;
import xyz.erupt.toolkit.submit.CheckUpdateService;
import xyz.erupt.upms.model.auth.DataAuthHandler;
import xyz.erupt.upms.model.auth.DataAuthModel;

import javax.persistence.Entity;
import javax.persistence.Table;
import java.io.*;

@Erupt(name = "预案管理", power = @Power(add = true, delete = false, export = false, importable = true, viewDetails = true, edit = true)
        , dataProxy = {PlanManagementDataProxy.class, ColorStateTimeFontDataProxy.class}
        , rowOperation = {
        @RowOperation(title = "删除", icon = "fa fa-trash-o", show = @ExprBool( exprHandler = EmployeeExternalRenderHandler.class),operationHandler = DelOperationHandler.class, ifExprBehavior = RowOperation.IfExprBehavior.HIDE, mode = RowOperation.Mode.SINGLE),
//        @RowOperation(title = "上报", icon = "fa fa-arrow-circle-o-up", operationHandler = PlanManagementEscalationHandler.class, ifExpr = "item.submitted=='未上报'", mode = RowOperation.Mode.SINGLE),
        //@RowOperation(title = "再次备案", icon = "fa fa-window-restore", code = TplUtils.REMOTE_ERUPT_CODE + "again",operationHandler = PlanManagementFilingAgainHandler.class,eruptClass =PlanManagementFilingAgainHandler.PlanManagementAgain.class, mode = RowOperation.Mode.SINGLE),
        @RowOperation(title = "下载附件", icon = "fa fa-download", operationHandler = AttachmentDownloadHandler.class, mode = RowOperation.Mode.SINGLE)
})
@Table(name = "tb_plan_management")
@Entity
@Getter
@Setter
@Comment("预案管理")
@ApiModel("预案管理")
@PreDataProxy(CheckUpdateService.class)
@ColorStateTimeFont(stateKey = "prePlanStatus", timeKey = "evaluationTime", interval = 1, unit = ColorStateTimeFont.TimeUnit.MONTH)
public class PlanManagement extends DataAuthModel {

    @EruptField(
            views = @View(title = "企业名称",width = "230px"),
            edit = @Edit(title = "企业名称", search = @Search(ifRender = @ExprBool(exprHandler = CompanyRenderHandler.class)),
                    readonly = @Readonly(exprHandler = DataAuthHandler.class), type = EditType.CHOICE, notNull = true,
                    choiceType = @ChoiceType(fullSpan = true,fetchHandler = RemoteEntityChoiceFetchHandler.class, fetchHandlerParams = {"main", "Enterprise", "org_code,name"})))
    @Comment("企业名称")
    @ApiModelProperty("企业名称")
    private String company;

    @EruptField(
            views = @View(title = "预案名称",width = "230px"),
            edit = @Edit(title = "预案名称", type = EditType.INPUT, search = @Search(vague = true), notNull = true,
                    inputType = @InputType))
    @Comment("预案名称")
    @ApiModelProperty("预案名称")
    private String preplanName;


    @EruptField(
            views = @View(title = "上报状态", show = false),
            edit = @Edit(title = "上报状态", type = EditType.BOOLEAN, show = false, /*notNull = true, */
                    boolType = @BoolType(trueText = "已上报", falseText = "未上报")))
    @Comment("上报状态")
    @ApiModelProperty("上报状态")
    private Boolean submitted;

    @EruptField(
            views = @View(title = "预案类型",width = "100px"),
            edit = @Edit(title = "预案类型",
                    type = EditType.TAGS, search = @Search(vague = true), notNull = true,
                    tagsType = @TagsType(allowExtension = false, fetchHandler = EruptDictTagFetchHandler.class, fetchHandlerParams = "preplanType"))
    )
    @Comment("预案类型")
    @ApiModelProperty("预案类型")
    private String preplanType;

    @EruptField(
            views = @View(title = "是否备案"),
            edit = @Edit(title = "是否备案", type = EditType.BOOLEAN, search = @Search, show = true, /*notNull = true,*/
                    boolType = @BoolType(trueText = "是", falseText = "否")))
    @Comment("是否备案")
    @ApiModelProperty("是否备案")
    private Boolean isRecord;

    @EruptField(
            views = @View(title = "评估到期时间"),
            edit = @Edit(title = "评估到期时间", type = EditType.DATE, notNull = true,
                    dateType = @DateType))
    @Comment("评估到期时间")
    @ApiModelProperty("评估到期时间")
    private java.util.Date evaluationTime;

    @EruptField(
            views = @View(title = "备案时间"),
            edit = @Edit(title = "备案时间", type = EditType.DATE,
                    dateType = @DateType))
    @Comment("备案时间")
    @ApiModelProperty("备案时间")
    private java.util.Date recordTime;

    @EruptField(
            views = @View(title = "预案状态"),
            edit = @Edit(title = "预案状态",
                    type = EditType.INPUT,
                    show = false,
                    tagsType = @TagsType(allowExtension = false,
                            fetchHandler = EruptDictTagFetchHandler.class,
                            fetchHandlerParams = "prePlanStatus"
                    ),
                    search = @Search
            )
    )
    @Comment("预案状态")
    @ApiModelProperty("预案状态")
    private String prePlanStatus;

//    @EruptField(
//            views = @View(
//                    title = "评估状态",
//                    show = false
//            ),
//            edit = @Edit(title = "评估状态",
//                    type = EditType.INPUT,
//                    show = false,
//                    inputType = @InputType
//            )
//    )
//    @Comment("评估状态")
//    @ApiModelProperty("评估状态")
//    private String preplanState;


    @EruptField(
            views = @View(title = "联系人"),
            edit = @Edit(title = "联系人", type = EditType.INPUT, search = @Search(vague = true), notNull = true,
                    inputType = @InputType))
    @Comment("联系人")
    @ApiModelProperty("联系人")
    private String contact;

    @EruptField(
            views = @View(title = "联系方式"),
            edit = @Edit(title = "联系方式", type = EditType.INPUT, notNull = true,
                    inputType = @InputType))
    @Comment("联系方式")
    @ApiModelProperty("联系方式")
    private String contactTel;

    @EruptField(
            views = @View(title = "上传附件", show = false),
            edit = @Edit(title = "上传附件", type = EditType.DIVIDE))
    @Comment("上传附件")
    @ApiModelProperty("上传附件")
    private String fileDivide;

    @EruptField(
            views = @View(title = "预案文件上传", show = false),
            edit = @Edit(title = "预案文件上传", type = EditType.ATTACHMENT, notNull = true,
                    attachmentType = @AttachmentType(type = AttachmentType.Type.BASE, maxLimit = 20, fileTypes = {".pdf", ".doc", ".docx", ".xlsx", ".png", ".jpg", ".xls", ".ppt", ".pptx", ".zip", ".rar"})))
    @Comment("预案文件上传")
    @ApiModelProperty("预案文件上传")
    private String fileUpload;

//    @EruptField(
//            views = @View(title = "应急预案", show = false),
//            edit = @Edit(title = "应急预案", type = EditType.ATTACHMENT, notNull = true,
//                    attachmentType = @AttachmentType(type = AttachmentType.Type.BASE, maxLimit = 20, fileTypes = {".pdf", ".doc", ".docx", ".xlsx", ".png", ".jpg", ".xls", ".ppt", ".pptx", ".zip", ".rar"})))
//    @Comment("应急预案")
//    @ApiModelProperty("应急预案")
//    private String emergency;
//
//    @EruptField(
//            views = @View(title = "应急预案评审意见", show = false),
//            edit = @Edit(title = "应急预案评审意见", type = EditType.ATTACHMENT, notNull = true,
//                    attachmentType = @AttachmentType(type = AttachmentType.Type.BASE, maxLimit = 20, fileTypes = {".pdf", ".doc", ".docx", ".xlsx", ".png", ".jpg", ".xls", ".ppt", ".pptx", ".zip", ".rar"})))
//    @Comment("应急预案评审意见")
//    @ApiModelProperty("应急预案评审意见")
//    private String emerOpinion;
//
//    @EruptField(
//            views = @View(title = "事故风险辨识", show = false),
//            edit = @Edit(title = "事故风险辨识", type = EditType.ATTACHMENT, notNull = true,
//                    attachmentType = @AttachmentType(type = AttachmentType.Type.BASE, maxLimit = 20, fileTypes = {".pdf", ".doc", ".docx", ".xlsx", ".png", ".jpg", ".xls", ".ppt", ".pptx", ".zip", ".rar"})))
//    @Comment("事故风险辨识")
//    @ApiModelProperty("事故风险辨识")
//    private String riskIden;
//
//    @EruptField(
//            views = @View(title = "评估报告",show = false),
//            edit = @Edit(title = "评估报告", type = EditType.ATTACHMENT, notNull = true,
//                    attachmentType = @AttachmentType(type = AttachmentType.Type.BASE, maxLimit = 20, fileTypes = {".pdf", ".doc", ".docx", ".xlsx", ".png", ".jpg", ".xls", ".ppt", ".pptx", ".zip", ".rar"})))
//    @Comment("评估报告")
//    @ApiModelProperty("评估报告")
//    private String reportEva;
//
//    @EruptField(
//            views = @View(title = "应急资源调查清单",show = false),
//            edit = @Edit(title = "应急资源调查清单", type = EditType.ATTACHMENT, notNull = true,
//                    attachmentType = @AttachmentType(type = AttachmentType.Type.BASE, maxLimit = 20, fileTypes = {".pdf", ".doc", ".docx", ".xlsx", ".png", ".jpg", ".xls", ".ppt", ".pptx", ".zip", ".rar"})))
//    @Comment("应急资源调查清单")
//    @ApiModelProperty("应急资源调查清单")
//    private String emerList;
//
//    @EruptField(
//            views = @View(title = "应急预案备案登记表（备案回执）",show = false),
//            edit = @Edit(title = "应急预案备案登记表（备案回执）", type = EditType.ATTACHMENT, notNull = true,
//                    attachmentType = @AttachmentType(type = AttachmentType.Type.BASE, maxLimit = 20, fileTypes = {".pdf", ".doc", ".docx", ".xlsx", ".png", ".jpg", ".xls", ".ppt", ".pptx", ".zip", ".rar"})))
//    @Comment("应急预案备案登记表（备案回执）")
//    @ApiModelProperty("应急预案备案登记表（备案回执）")
//    private String emerTable;
//
//    @EruptField(
//            views = @View(title = "其他文件/资料",show = false),
//            edit = @Edit(title = "其他文件/资料", type = EditType.ATTACHMENT, notNull = true,
//                    attachmentType = @AttachmentType(type = AttachmentType.Type.BASE, maxLimit = 20, fileTypes = {".pdf", ".doc", ".docx", ".xlsx", ".png", ".jpg", ".xls", ".ppt", ".pptx", ".zip", ".rar"})))
//    @Comment("其他文件/资料")
//    @ApiModelProperty("其他文件/资料")
//    private String otherFile;

    @EruptField(
            views = @View(title = "是否上报", show = false),
            edit = @Edit(title = "是否上报", type = EditType.BOOLEAN, show = false,/* notNull = true,*/
                    boolType = @BoolType(trueText = "是", falseText = "否")))
    @Comment("是否上报")
    @ApiModelProperty("是否上报")
    private Boolean isReport;


    public PlanManagement deepClone() {
        PlanManagement plan = null;
        try {
            ByteArrayOutputStream baos = new ByteArrayOutputStream();
            ObjectOutputStream oos = new ObjectOutputStream(baos);
            oos.writeObject(this);
            // 将流序列化成对象
            ByteArrayInputStream bais = new ByteArrayInputStream(baos.toByteArray());
            ObjectInputStream ois = new ObjectInputStream(bais);
            plan = (PlanManagement) ois.readObject();
        } catch (IOException | ClassNotFoundException e) {
            e.printStackTrace();
        }
        return plan;
    }
}
