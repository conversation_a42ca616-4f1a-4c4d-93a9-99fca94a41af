package com.daliangang.rndpub.config;

import com.daliangang.rndpub.entity.*;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.annotation.Order;
import xyz.erupt.core.constant.MenuTypeEnum;
import xyz.erupt.core.module.EruptModule;
import xyz.erupt.core.module.EruptModuleInvoke;
import xyz.erupt.core.module.MetaMenu;
import xyz.erupt.core.module.ModuleInfo;
import xyz.erupt.core.util.EruptSpringUtil;
import xyz.erupt.upms.service.EruptMenuService;
import xyz.erupt.upms.util.EruptMenuUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * @Author: zengwensi
 * @Description:
 * @Date: Created in 2023/3/17 15:18
 * @Modified By
 */
@Configuration
@Order
public class RndpubConfiguration implements EruptModule, ApplicationRunner {

    static {
        EruptModuleInvoke.addEruptModule(RndpubConfiguration.class);
    }

    @Override
    public ModuleInfo info() {
        return ModuleInfo.builder().name("daliangang-rndpub").build();
    }

    @Override
    public List<MetaMenu> initMenus() {
        EruptMenuService eruptMenuService = EruptSpringUtil.getBean(EruptMenuService.class);
        String moduleId = "rndpub";
        eruptMenuService.registerModule(moduleId, "港区危险货物企业“双随机、一公开”系统", 5);
        AtomicInteger rootSort = new AtomicInteger(500);
        AtomicInteger sort = new AtomicInteger();
        List<MetaMenu> menus = new ArrayList<>();

        MetaMenu checkMan = EruptMenuUtils.createRootMenu(null, "$dlg-checkMan", "基础库管理", "fa fa-check", rootSort, moduleId);
        menus.add(checkMan);
        menus.add(EruptMenuUtils.createMenu(Expert.class, checkMan, sort, MenuTypeEnum.TABLE, "专家列表", "Expert", moduleId, null));//专家列表
        menus.add(MetaMenu.createSimpleMenu("ExpertApply", "专家申请链接", "/expert-application/index.html", checkMan, rootSort.getAndIncrement(), MenuTypeEnum.NEW_WINDOW.getCode()).hide().module(moduleId));
        menus.add(EruptMenuUtils.createMenu(Expertaudit.class, checkMan, sort, MenuTypeEnum.TABLE, "专家审核", "Expertaudit", moduleId, null)); //专家审核
        menus.add(EruptMenuUtils.createMenu(InspectionItemsManagement.class, checkMan, sort, MenuTypeEnum.TABLE, "检查事项", "InspectionItemsManagement", moduleId, null));//检查事项管理
        menus.add(EruptMenuUtils.createMenu(Qualification.class, checkMan, sort, MenuTypeEnum.TABLE, "专家资质证书", "Qualification", moduleId, null).hide());//专家资质证书
        menus.add(EruptMenuUtils.createMenu(CheckPerson.class, checkMan, rootSort, MenuTypeEnum.TABLE, "检查人员", "Checkperson", moduleId, "fa fa-odnoklassniki"));//检查人员管理

        MetaMenu procedure = EruptMenuUtils.createMenu(Procedure.class, null, rootSort, MenuTypeEnum.TABLE, "检查流程管理", "Procedure", moduleId, "fa fa-gg");
        menus.add(procedure);//检查流程
        menus.add(EruptMenuUtils.createMenu(EnterpriseInformation.class, procedure, sort, MenuTypeEnum.TABLE, "抽取企业", "EnterpriseInformation", moduleId, null).hide());
        menus.add(EruptMenuUtils.createMenu(ExpertInformation.class, procedure, sort, MenuTypeEnum.TABLE, "抽取专家", "ExpertInformation", moduleId, null).hide());
        menus.add(EruptMenuUtils.createMenu(Inspector.class, procedure, sort, MenuTypeEnum.TABLE, "抽取检查人员", "Inspector", moduleId, null).hide());
        menus.add(EruptMenuUtils.createMenu(InspectionItems.class, procedure, sort, MenuTypeEnum.TABLE, "抽取检查事项", "InspectionItems", moduleId, null).hide());

        MetaMenu inspectResult = EruptMenuUtils.createMenu(InspectionResults.class, null, rootSort, MenuTypeEnum.TABLE, "检查结果录入", "InspectionResults", moduleId, "fa fa-html5");
        menus.add(inspectResult);//检查问题管理
        MetaMenu inspectResultView = EruptMenuUtils.createMenu(InspectionResultsView.class, null, rootSort, MenuTypeEnum.TABLE, "检查结果管理", "InspectionResultsView", moduleId, "fa fa-html5");
        menus.add(inspectResultView);//检查结果管理
        menus.add(EruptMenuUtils.createMenu(Rectify.class, inspectResult, sort, MenuTypeEnum.TABLE, "整改管理下钻", "RectifyDrill", moduleId, null).hide());
        menus.add(EruptMenuUtils.createMenu(Rectify.class, inspectResultView, sort, MenuTypeEnum.TABLE, "整改管理下钻", "RectifyDrill", moduleId, null).hide());
//        menus.add(EruptMenuUtils.createMenu(InspectionResults.class, null, rootSort, MenuTypeEnum.TABLE, "检查结果", "InspectionResults", moduleId, "fa fa-file-text"));//检查结果
        menus.add(EruptMenuUtils.createMenu(Rectification.class, null, rootSort, MenuTypeEnum.TABLE, "问题整改", "Rectification", moduleId, "fa fa-star-half-o"));//问题整改

//        MetaMenu rndpubStats = EruptMenuUtils.createRootMenu(null, "$rndpub-stats", "数据统计", "fa fa-area-chart", rootSort, moduleId);
//        menus.add(rndpubStats);
        menus.add(MetaMenu.createSimpleMenu("jORjaKWC", "数据分析", "jORjaKWC", null, rootSort.getAndIncrement(), "bi").module(moduleId));
        menus.add(MetaMenu.createSimpleMenu("JI6pTjkM", "数据统计", "JI6pTjkM", null, rootSort.getAndIncrement(), "bi").module(moduleId));

        //企业检查档案
        menus.add(EruptMenuUtils.createMenu(CompanyInspect.class, null, sort, MenuTypeEnum.TABLE, "企业检查档案", "CompanyInspect", moduleId,"fa fa-star-half-o"));
        menus.add(EruptMenuUtils.createMenu(CompanyInspectDetail.class, null, sort, MenuTypeEnum.TABLE, "企业检查详情", "CompanyInspectDetail", moduleId,"fa fa-star-half-o").hide());
        return menus;
    }

    @Override
    public void run(ApplicationArguments args) throws Exception {
        //EruptSpringUtil.getBean(EruptExcelController.class).importExcel(InspectionItemsManagement.class, "public/init/04-检查事项管理.xls");
    }
}
