package com.daliangang.majorisk.service;

import cn.hutool.http.HttpRequest;
import com.alibaba.fastjson2.JSON;
import com.daliangang.device.proxy.BerthDataProxy;
import com.daliangang.majorisk.entity.UnloadShip;
import com.google.gson.internal.LinkedTreeMap;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.stereotype.Service;
import xyz.erupt.jpa.dao.EruptDao;
import xyz.erupt.toolkit.remote.RemoteProxyService;
import xyz.erupt.toolkit.remote.RemoteQuery;
import xyz.erupt.toolkit.utils.EruptDaoUtils;
import xyz.erupt.upms.platform.EruptOption;
import xyz.erupt.upms.platform.EruptOptions;
import xyz.erupt.upms.service.EruptPlatformService;

import javax.annotation.Resource;
import java.util.*;

/**
 * <AUTHOR>
 * @since :2023/5/18:17:28
 */
@EruptOptions(
        {
                @EruptOption(name = UnloadShipHttpService.UNLOAD_BUSINESS_IP, value = "http://************:20048/api", desc = "风控业务的ip"),
                @EruptOption(name = UnloadShipHttpService.UNLOAD_TOKEN_IP, value = "http://************:42315", desc = "风控token的ip"),
        }


)
@Slf4j
@Service
public class UnloadShipHttpService {
    @Resource
    EruptDao eruptDao;
    @Resource
    private RemoteProxyService remoteProxyService;


    @Resource
    private EruptPlatformService eruptPlatformService ;

    public static final String UNLOAD_BUSINESS_IP = "UNLOAD_BUSINESS_IP" ;

    public static final String UNLOAD_TOKEN_IP = "UNLOAD_TOKEN_IP";



    @Data
    public class AddressB{
        private Long workId;
        private Long addressId;
    }

    @Data
    public class AddressS{
        private String positionId;
    }

    public String UpdateHttpUnloadShip(Object o, String orgCode, Long id,String token) {
        String isPush = eruptPlatformService.getOption(BerthDataProxy.IS_PUSH).getAsString() ;
        if (isPush.equals("false")) {
            return "";
        }
        String ip = eruptPlatformService.getOption(UnloadShipHttpService.UNLOAD_BUSINESS_IP).getAsString() ;
        String url = ip+"/DataSync/UpdateJobPosition";
        // 查询风控是否具该企业
        List<LinkedTreeMap> linkedTreeMaps = remoteProxyService.queryEntity("EnterpriseRelevancy", RemoteQuery.builder().condition("org_code", orgCode));
        if (ObjectUtils.isNotEmpty(linkedTreeMaps)) {

            UnloadShip unloadShip = JSON.parseObject(JSON.toJSONString(o), UnloadShip.class);
            // 查询作业地点关联表，获取本系统地点id
            List<Map<Long,Object>> bIds = new ArrayList();

            // 泊位,类型是2
            List<AddressB> addressBS1 = EruptDaoUtils.selectOnes("select unload_id as workId , berths_id as addressId from tb_unload_ship_berths tub where tub.unload_id ="+id, AddressB.class);
             if (ObjectUtils.isNotEmpty(addressBS1)) {
                 addressBS1.forEach(v->{
                     Map<Long,Object> map = new HashMap<>();
                     map.put(v.getAddressId(),"2") ;
                     bIds.add(map);
                 });
             }
            // 堆场,类型是5
            List<AddressB> addressBS2 = EruptDaoUtils.selectOnes("select unload_id as workId , yards_id as addressId from tb_unload_ship_yards tub where tub.unload_id ="+id, AddressB.class);
            if (ObjectUtils.isNotEmpty(addressBS2)) {
                addressBS2.forEach(v->{
                    Map<Long,Object> map = new HashMap<>();
                    map.put(v.getAddressId(),"5") ;
                    bIds.add(map);
                });
            }
            // 仓库,类型是6
            List<AddressB> addressBS3 = EruptDaoUtils.selectOnes("select unload_id as workId , wares_id as addressId from tb_unload_ship_wares tub where tub.unload_id ="+id, AddressB.class);
            if (ObjectUtils.isNotEmpty(addressBS3)) {
                addressBS3.forEach(v->{
                    Map<Long,Object> map = new HashMap<>();
                    map.put(v.getAddressId(),"6") ;
                    bIds.add(map);
                });
            }
            // 栈台,类型是3
            List<AddressB> addressBS4 = EruptDaoUtils.selectOnes("select unload_id as workId , wharfs_id as addressId from tb_unload_ship_wharfs tub where tub.unload_id ="+id, AddressB.class);
            if (ObjectUtils.isNotEmpty(addressBS4)) {
                addressBS4.forEach(v->{
                    Map<Long,Object> map = new HashMap<>();
                    map.put(v.getAddressId(),"3") ;
                    bIds.add(map);
                });
            }
            // 储罐,类型是1
            List<AddressB> addressBS5 = EruptDaoUtils.selectOnes("select unload_id as workId , tanks_id as addressId from tb_unload_ship_tanks tub where tub.unload_id ="+id, AddressB.class);
            if (ObjectUtils.isNotEmpty(addressBS5)) {
                addressBS5.forEach(v->{
                    Map<Long,Object> map = new HashMap<>();
                    map.put(v.getAddressId(),"1") ;
                    bIds.add(map);
                });
            }

            //根据本系统作业id查询三方地点关联表返回三方地点数据id
            List<String> lists = new ArrayList();
            for(Map<Long,Object> bidMap : bIds){
                Set<Long> keys = bidMap.keySet();
                for(Long key : keys){
                    List<AddressS> addressS1 = EruptDaoUtils.selectOnes("select position_id as positionId from tb_position_docking where b_id = "+key +" and position_type = '"+bidMap.get(key)+"'", AddressS.class);
                    if (ObjectUtils.isNotEmpty(addressS1)) {
                        addressS1.forEach(v->{
                            lists.add(v.getPositionId());
                        });
                    }
                }

            }


            String join = String.join(",", lists);

            // 调用风控接口
            Map map = new HashMap();
            map.put("job_id", unloadShip.getJobId());//作业id
            map.put("position", join);//作业位置
            map.put("job_name", unloadShip.getName());//作业名称
            map.put("start_time", unloadShip.getAbTime());//开始时间
            map.put("ent_time", unloadShip.getAeTime());//结束时间
            String json = JSON.toJSONString(map);
            log.info("推送装卸船原始数据 -> 接口：" + url+",数据："+json);
            String body = HttpRequest.post(url)
                    .header("Authorization", token)
                    .header("Content-Type","application/json")
                    .body(json)
                    .execute().body();
            log.info("推送装卸船返回body -> " +body);
            return body;
        }
        return "";
    }

    /**
     * 推送删除作业接口
     * @param o
     * @param token
     * @return
     */
    public String deleteHttpUnloadShip(Object o,String token){
        String isPush = eruptPlatformService.getOption(BerthDataProxy.IS_PUSH).getAsString() ;
        if (isPush.equals("false")) {
            return "";
        }
        String ip = eruptPlatformService.getOption(UnloadShipHttpService.UNLOAD_BUSINESS_IP).getAsString() ;
        String url = ip+"/DataSync/DeleteJob";
        // 调用风控接口
        Map map = new HashMap();
        UnloadShip unloadShip = JSON.parseObject(JSON.toJSONString(o), UnloadShip.class);
        map.put("job_id", unloadShip.getJobId());
        String json = JSON.toJSONString(map);
        log.info("推送装卸船删除原始数据 -> 接口：" + url+",数据："+json);
        String body = HttpRequest.post(url)
                .header("Authorization", token)
                .header("Content-Type","application/json")
                .body(json)
                .execute().body();
        log.info("推送装卸船删除返回body -> " +body);
        return body;
    }


}
