/*
 * Copyright © 2020-2035 yundingyun All rights reserved.
 */
package com.daliangang.rndpub.operation;

import com.daliangang.rndpub.entity.EnterpriseInformation;
import com.daliangang.rndpub.entity.ExpertInformation;
import com.daliangang.rndpub.entity.Procedure;
import lombok.Data;
import org.springframework.stereotype.Service;
import xyz.erupt.annotation.Erupt;
import xyz.erupt.annotation.EruptField;
import xyz.erupt.annotation.fun.OperationHandler;
import xyz.erupt.annotation.sub_field.Edit;
import xyz.erupt.annotation.sub_field.EditType;
import xyz.erupt.core.context.MetaDrill;
import xyz.erupt.jpa.dao.EruptDao;
import xyz.erupt.jpa.model.BaseModel;
import xyz.erupt.toolkit.db.EruptResultMap;
import xyz.erupt.toolkit.utils.EruptDaoUtils;
import xyz.erupt.toolkit.utils.NotifyUtils;
import xyz.erupt.toolkit.utils.SqlUtils;
import xyz.erupt.upms.model.EruptDictItem;
import xyz.erupt.upms.util.EruptDictUtils;

import javax.annotation.Resource;
import javax.transaction.Transactional;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

@Service
public class ExpertInformationSystemExtractionHandler implements OperationHandler<ExpertInformation, ExpertInformationSystemExtractionHandler.DrawExpertForm> {

    @Erupt(name = "自动抽取专家")
    @Data
    public static class DrawExpertForm extends BaseModel {
        @EruptField(
                edit = @Edit(title = "抽取专家个数", type = EditType.NUMBER, notNull = true))
        private int count = 10;
    }

    @Resource
    private EruptDao eruptDao;

    @Override
    @Transactional
    public String exec(List<ExpertInformation> data, ExpertInformationSystemExtractionHandler.DrawExpertForm form, String[] param) {
        //获取企业信息
        String enterpriseSql = "select enterprise_name,type from tb_enterprise_information where procedure_id=" + MetaDrill.getDrillId() + " and state=1";
        List<EnterpriseInformation> enterpriseList = EruptDaoUtils.selectOnes(enterpriseSql, EnterpriseInformation.class);
        if (enterpriseList.size() == 0) {
            NotifyUtils.showErrorMsg("请先抽取企业");
        }
        String expertInformationSql = "select * from tb_expert_information where state=0";
        List<ExpertInformation> expertInformationList = EruptDaoUtils.selectOnes(expertInformationSql, ExpertInformation.class);
        if (expertInformationList.size() == 0) {
            NotifyUtils.showErrorMsg("暂无专家可选！");
        }
        Set<String> enterpriseNames = new HashSet<>();
        Set<String> categorys = new HashSet<>();
        Set<String> categoryStr = new HashSet<>();
        enterpriseList.forEach(enterprise -> {
            enterpriseNames.add(enterprise.getEnterpriseName());
            if (!categorys.contains(enterprise.getType())) {
                categorys.add(enterprise.getType());
                EruptDictItem dictItem = EruptDictUtils.getDictItem("checkScope", enterprise.getType());
                if (dictItem != null) categoryStr.add(dictItem.getName()+" ");
            }
        });


        Procedure procedure = eruptDao.queryEntity(Procedure.class, "id=" + MetaDrill.getDrillId());
        //所有条目设置为未抽取
        String resetSql = "update tb_expert_information set state=0 where procedure_id=" + MetaDrill.getDrillId();
        eruptDao.getJdbcTemplate().execute(resetSql);

        StringBuilder sql = new StringBuilder("select * from tb_expert_information where procedure_id=").append(MetaDrill.getDrillId()).append(" and state=0 ");
        if (categorys.size() != 0) {
            sql.append("  and (check_scope " + SqlUtils.wrapIn(categorys.stream().collect(Collectors.toList())) +" or check_scope = 'ALL') ");
        }

        //去除规避企业
        for (String company : enterpriseNames) {
            sql.append(" and expert_company not like '%").append(company).append("%'");
        }
        sql.append(" order by rand() limit " + form.count);

        List<ExpertInformation> expertInformation = EruptDaoUtils.selectOnes(sql.toString(), ExpertInformation.class);

        if (expertInformation.isEmpty()) {
            String msg = "暂无专家可选<br>";
            msg += "当前检查对象需要 " + categoryStr + " 类专家，库中没有此类专家。";
            NotifyUtils.showErrorMsg(msg);
        }

        for (ExpertInformation info : expertInformation) {
            String updSql = "update tb_expert_information set state=1 where id=" + info.getId();
            eruptDao.getJdbcTemplate().execute(updSql);
            eruptDao.flush();
        }

        updateCountAndName(procedure);

        return null;
    }

    public void updateCountAndName(Procedure procedure) {
        String countSql = "select count(*) as count,group_concat(name)name from tb_expert_information where procedure_id=" + procedure.getId() + " and state=1";
        EruptResultMap eruptResultMap = EruptDaoUtils.selectMap(countSql);
        procedure.setInspectionExpert(eruptResultMap.getInt("count"));
        procedure.setExpert(eruptResultMap.getString("name"));
        eruptDao.merge(procedure);
    }
}
