package com.daliangang.device.form;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import xyz.erupt.annotation.EruptField;
import xyz.erupt.annotation.sub_field.Edit;
import xyz.erupt.annotation.sub_field.EditType;
import xyz.erupt.annotation.sub_field.View;
import xyz.erupt.annotation.sub_field.sub_edit.InputType;
import xyz.erupt.toolkit.db.Comment;

import javax.persistence.Lob;
import java.util.List;

/**
 * <AUTHOR>
 * @since :2023/5/17:16:36
 */
@Data
public class PositionDockingForm {


    @Comment("三方系统位置id")
    @ApiModelProperty("三方系统位置id")
    private String Id;


    @Comment("位置类型")
    @ApiModelProperty("位置类型")
    private String type;


    @Comment("位置名称")
    @ApiModelProperty("位置名称")
    private String name;


    @Comment("地点位置")
    @ApiModelProperty("地点位置")
    private List<Address> address;

    @Comment("企业uuid")
    @ApiModelProperty("企业uuid")
    private String companyId;


    @Comment("删除状态")
    @ApiModelProperty("删除状态")
    private String deleteState;

    @Comment("地点位置")
    @ApiModelProperty("地点位置")
    private String map;

    @Data
    public class Address {
        private List<Ht> Ht;
        private double Ry;
        private int Type;
    }

    @Data
    public class Ht {
        private double lat;
        private double lng;
    }

}
