package com.daliangang.datascreen.serveboard.controller;

import com.daliangang.datascreen.response.AmountCountResponse;
import com.daliangang.datascreen.response.amountResult;
import com.daliangang.datascreen.serveboard.service.ServeBoardService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import xyz.erupt.core.annotation.EruptRouter;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

@RestController
@Component
@Slf4j
public class ServeBoardController {
    @Resource
    private ServeBoardService serveBoardService;

    //企业总数
    @RequestMapping("/serve/board/enterprise/count")
    private List<AmountCountResponse.amountContent> getEnterpriseCount(){
       return serveBoardService.getEnterpriseCount();
    }

    //用户总数
    @RequestMapping("/serve/board/user/count")
    private List<AmountCountResponse.amountContent> getUserCount(){
        return serveBoardService.getUserCount();
    }
    //重大风险总数
    @RequestMapping("/serve/board/risk/count")
    private List<AmountCountResponse.amountContent> getRiskCount(){
        return serveBoardService.getRiskCount();
    }

    //应急救援资源
    @RequestMapping("/serve/board/emergency/rescue")
    private List<AmountCountResponse.amountContent> getEmergencRrescue(){
        return serveBoardService.getEmergencRrescue();
    }

    //安全法规统计
    @RequestMapping("/serve/board/safe/law/count")
    private List<AmountCountResponse.amountContent> getSafeLawCount(){
        return serveBoardService.getSafeLawCount();
    }

    //事故案例
    @RequestMapping("/serve/board/accident/case")
    private List<AmountCountResponse.amountContent> getAccidentCase(){
        return serveBoardService.getAccidentCase();
    }

    //国内外事故案例
    @RequestMapping("/serve/board/accident/case/foreign")
    private List<AmountCountResponse.amountContent> getAccidentCaseForeign(){
        return serveBoardService.getAccidentCaseByCountry();
    }

    //本年度应急能力评估
    @RequestMapping("/serve/board/emergency/ability")
    private List<AmountCountResponse.amountContent> getEmergencyAbility(){
        return serveBoardService.getEmergencyAbility();
    }

    //未评估企业列表
    @RequestMapping("/serve/board/not/evaluated/companies")
    private List<Map<String, String>> getNotEvaluatedCompanies(){
        return serveBoardService.getNotEvaluatedCompanies();
    }

    //岗位胜任力评估
    @RequestMapping("/serve/board/position/competence")
    @EruptRouter(verifyType = EruptRouter.VerifyType.LOGIN)
    private List<amountResult> getPositionCompetence(){
        return serveBoardService.getPositionCompetence();
    }

    //最近一次评估柱状图
    @RequestMapping("/serve/board/last/evaluation")
    @EruptRouter(verifyType = EruptRouter.VerifyType.LOGIN)
    private List<amountResult> getLastEvaluation(){
        return serveBoardService.getLastEvaluation();
    }

    //本年度教育培训
    @RequestMapping("/serve/board/education/training")
    @EruptRouter(verifyType = EruptRouter.VerifyType.LOGIN)
    private List<AmountCountResponse.amountContent> getEducationTraining(){
        return serveBoardService.getEducationTraining();
    }
    //最近一次培训情况
    @RequestMapping("/serve/board/last/training")
    @EruptRouter(verifyType = EruptRouter.VerifyType.LOGIN)
    private List<AmountCountResponse.amountContent> getLastTraining(){
        return serveBoardService.getLastTraining();
    }

}
