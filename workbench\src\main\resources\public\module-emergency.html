<!DOCTYPE html>
<html lang="en">
<head>
	<meta charset="UTF-8">
	<meta http-equiv="X-UA-Compatible" content="IE=edge">
	<meta name="viewport" content="width=device-width, initial-scale=1.0">
	<title>首页</title>
	<script src="./js/vue.min.js"></script>
	<script src="./js/common.js"></script>
	<link rel="stylesheet" href="./css/common.css"/>
<link rel="stylesheet" href="./css/element-ui.css"/>
	<script src="./js/vue-resource.min.js"></script>
<script src="./js/element-ui.js"></script>
</head>
<body>
	<div class="wrap"id="app">
		<div class="wrapMain">
			<div class="title">{{title}}</div>
			<ul>
				<li v-for="(item,index) in list" :key="index"  @click="goTo(item)" class="item" :style="{'background':`url(./img/${item.img}) no-repeat`,'background-size':'100% 100%'}">
					<div class="info">
					<div class="name">{{item.name}}</div>
					<div class="link">点击进入>></div>
				</div>
				</li>
			</ul>
		</div>
	</div>
</body>
</html>
<script>
	var app = new Vue({
		el: '#app',
		mixins:[common],
		data: {
			title:'危险货物重大突发事件智慧管理系统',
list:[
{name:'应急值守管理',url:'/#/build/table/EmergencyDuty',code:'EmergencyDuty',img:'bg1.png'},
{name:'应急演练管理',url:'/#/build/table/Drill', code:'Drill',img:'bg2.png'},
{name:'救援评估管理',url:'/#/build/table/Rescueassessment',code:'Rescueassessment',img:'bg3.png'},
{name:'应急物资储备点管理',url:'/#/build/table/MaterialReserve',code:'MaterialReserve',img:'bg1.png'},
{name:'应急集合点管理',url:'/#/build/table/EmergencyMuster',code:'EmergencyMuster',img:'bg3.png'},
{name:'应急救援一张图',url:'/datav-accident/index.html#/',code:'/datav-accident/index.html',blank:true,img:'bg2.png'},
]
		},
		created:{

		},
		methods:{
		}
	  })
</script>

<style>

</style>