package com.daliangang.majorisk.proxy;

import com.daliangang.majorisk.entity.FiveChecklistsFoundation;
import com.daliangang.majorisk.entity.FiveDetail;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.stereotype.Service;
import xyz.erupt.annotation.fun.DataProxy;
import xyz.erupt.core.module.MetaUserinfo;
import xyz.erupt.core.util.EruptSpringUtil;
import xyz.erupt.toolkit.remote.RemoteProxyService;
import xyz.erupt.toolkit.utils.EruptCompUtils;
import xyz.erupt.toolkit.utils.EruptDaoUtils;
import xyz.erupt.toolkit.utils.NotifyUtils;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @since :2023/4/4:11:14
 */
@Service
public class FiveChecklistsFoundationDataProxy implements DataProxy<FiveChecklistsFoundation> {

    @Resource
    private RemoteProxyService remoteProxyService;

    @Override
    public void addBehavior(FiveChecklistsFoundation fiveChecklistsFoundation) {
        MetaUserinfo remoteUserInfo = remoteProxyService.getRemoteUserInfo();
        FiveChecklistsFoundation fiveChecklistsFoundation1 = EruptDaoUtils.selectOne("select tff.* FROM  tb_five_detail tfd left join  tb_fivechecklists_foundation tff  on tfd.five_checklists_foundation_id = tff.id where tff.org_code ='" + remoteUserInfo.getOrg() + "' ORDER BY id DESC LIMIT 0,1", FiveChecklistsFoundation.class);

         if (ObjectUtils.isNotEmpty(fiveChecklistsFoundation.getFiveRiskType())) {
             FiveDetail fiveDetail = EruptSpringUtil.getBean(FiveDetail.class);
             if (ObjectUtils.isNotEmpty(fiveChecklistsFoundation1)) {
                 fiveChecklistsFoundation.setGeographicalLocation(fiveChecklistsFoundation1.getGeographicalLocation());
                 fiveChecklistsFoundation.setPrincipalPerson(fiveChecklistsFoundation1.getPrincipalPerson());
                 fiveChecklistsFoundation.setContactTel(fiveChecklistsFoundation1.getContactTel());
                 fiveChecklistsFoundation.setUnitAddress(fiveChecklistsFoundation1.getUnitAddress());
             //    fiveChecklistsFoundation.setMajorRisk(fiveChecklistsFoundation1.getMajorRisk());
                 fiveChecklistsFoundation.setCasualties(fiveChecklistsFoundation1.getCasualties());
                 fiveChecklistsFoundation.setPropertyLoss(fiveChecklistsFoundation1.getPropertyLoss());
                 fiveChecklistsFoundation.setEnvironmentalImpact(fiveChecklistsFoundation1.getEnvironmentalImpact());
                 fiveChecklistsFoundation.setSocialInfluence(fiveChecklistsFoundation1.getSocialInfluence());
             }
             String choiceValue = EruptCompUtils.getChoiceValue(fiveChecklistsFoundation.getFiveRiskType());
             fiveChecklistsFoundation.setName(choiceValue);
             fiveDetail.setRiskName(choiceValue);
             fiveDetail.addBehavior(fiveDetail);
         }

    }



    @Override
    public void beforeAdd(FiveChecklistsFoundation fiveChecklistsFoundation) {
        FiveChecklistsFoundation fiveChecklistsFoundation1 = EruptDaoUtils.selectOne("select * from tb_fivechecklists_foundation where name ='" + fiveChecklistsFoundation.getName() + "'", FiveChecklistsFoundation.class);
        if (ObjectUtils.isNotEmpty(fiveChecklistsFoundation1)) {
            NotifyUtils.showErrorMsg("重大风险名称已经存在！");
        }
    }
}
