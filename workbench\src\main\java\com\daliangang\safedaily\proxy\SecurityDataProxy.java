/*
* Copyright © 2020-2035 yundingyun All rights reserved.
*/
package com.daliangang.safedaily.proxy;

import com.daliangang.core.DaliangangContext;
import com.daliangang.safedaily.entity.Security;
import org.springframework.stereotype.Service;
import xyz.erupt.annotation.fun.DataProxy;
import xyz.erupt.annotation.query.Condition;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;

@Service
public class SecurityDataProxy implements DataProxy<Security> {
    @Override
    public void beforeAdd(Security security) {
        security.setSubmitted(true);
    }

    @Override
    public void beforeUpdate(Security security) {
//        if (security.getSubmitted()) {
//            NotifyUtils.showErrorMsg("报告单已经上报，请勿修改！");
//        }
    }

    @Override
    public void beforeDelete(Security security) {
//        if (security.getSubmitted()) {
//            NotifyUtils.showErrorMsg("报告单已经上报，请勿删除！");
//        }
    }

//    @Override
//    public void afterFetch(Collection<Map<String, Object>> list)  {
//        List<Security> securities = EruptDaoUtils.convert(list, Security.class);
//
//
//        for (Security security : securities) {
//            //若当前时间大于【有效期至】 ，则已逾期；若当前时间距离评估时间还有一个月，则即将逾期；其他为正常状态
//            Date effectiveDate = security.getEffectiveDate();
//            java.util.Date now = Date.from(LocalDate.now().atStartOfDay().atZone(ZoneId.systemDefault()).toInstant());
//
//
//            long month = DateUtil.betweenMonth(now, effectiveDate, false);
//
//            if (now.after(effectiveDate)) {
//                security.setState(TplUtils.addColor("已逾期", "#f5222d"));
//            } else if (month >= 1) {
//                security.setState(TplUtils.addColor("正常", "#52c41a"));
//            }   else {
//                security.setState(TplUtils.addColor("即将逾期", "#f5bd00"));
//
//            }
//
//            EruptDaoUtils.updateAfterFetch(list, security.getId(), "stateStr", security.getState());
//        }
//
//    }

    @Override
    public String beforeFetch(List<Condition> conditions) {
        StringBuilder str = new StringBuilder();
        // 当前时间设置为当天 0 点 0 分 0 秒
        LocalDateTime nowStartOfDay = LocalDateTime.now().toLocalDate().atStartOfDay();
        // 当前时间加上 30 天
        LocalDateTime thirtyDaysLaterStartOfDay = nowStartOfDay.plusDays(30);

        // 定义日期格式化器
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        // 格式化时间
        String nowFormatted = nowStartOfDay.format(formatter);
        String thirtyDaysLaterFormatted = thirtyDaysLaterStartOfDay.format(formatter);

        // 遍历 conditions 参数，构造查询逻辑
        for (Condition condition : conditions) {
            if ("stateStr".equals(condition.getKey())) {
                conditions.remove(condition);
                Object value = condition.getValue();
                if ("正常".equals(value.toString())) {
                    // 当前时间晚于 effective_date + 30 天
                    str.append(" AND effective_date > '").append(thirtyDaysLaterFormatted).append("'");
                } else if ("已逾期".equals(value.toString())) {
                    // 当前时间晚于 effective_date
                    str.append(" AND effective_date < '").append(nowFormatted).append("'");
                } else if ("即将逾期".equals(value.toString())) {
                    // 当前时间在 effective_date 和 effective_date + 30 天之间
                    str.append(" AND effective_date BETWEEN '")
                            .append(nowFormatted).append("' AND '").append(thirtyDaysLaterFormatted).append("'");
                }
            }
        }

        // 根据是否是部门用户决定是否添加 `submitted=true`
        if (DaliangangContext.isDepartmentUser()) {
            str.append(" AND submitted=true");
        }

        // 如果没有生成任何条件，返回空字符串
        if (str.length() > 0) {
            return str.substring(5); // 移除开头的 " AND"
        } else {
            return "";
        }
    }
}
