package com.daliangang.rndpub.controller;

import com.daliangang.device.form.NumForms;
import com.daliangang.rndpub.entity.Procedure;
import com.daliangang.rndpub.form.InspectionResultNumForm;
import com.daliangang.rndpub.sql.InspectionResultSql;
import com.google.gson.internal.LinkedTreeMap;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import xyz.erupt.core.annotation.EruptRouter;
import xyz.erupt.core.module.MetaUserinfo;
import xyz.erupt.core.util.EruptSpringUtil;
import xyz.erupt.core.view.EruptApiModel;
import xyz.erupt.toolkit.db.EruptResultMap;
import xyz.erupt.toolkit.remote.RemoteProxyService;
import xyz.erupt.toolkit.utils.EruptDaoUtils;
import xyz.erupt.toolkit.utils.SqlUtils;
import xyz.erupt.upms.model.EruptRole;
import xyz.erupt.upms.model.EruptUser;
import xyz.erupt.upms.service.EruptUserService;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

@RestController
public class InspectionResultController {

    @Resource
    private RemoteProxyService remoteProxyService;
    @Resource
    private InspectionResultSql inspectionResultSql;

    //政府企业检查统计
    @RequestMapping("erupt-api/get/selectInspectionResultNum")
    @EruptRouter(verifyType = EruptRouter.VerifyType.LOGIN)
    public EruptApiModel selectInspectionResultNum() {
        MetaUserinfo remoteUserInfo = remoteProxyService.getRemoteUserInfo();
        String orgCode = SqlUtils.wrapIn(remoteUserInfo.getAuth());
        String sql = inspectionResultSql.selectInspectionResultNum(orgCode);
        List<InspectionResultNumForm> employeeCertificateForm = EruptDaoUtils.selectOnes(sql, InspectionResultNumForm.class);

        return EruptApiModel.successApi(employeeCertificateForm);
    }

    // 统计今年本企业已逾期的数量
    @RequestMapping("erupt-api/get/selectQyYqNum")
    @EruptRouter(verifyType = EruptRouter.VerifyType.LOGIN)
    public EruptApiModel selectQyYqNum() {
        MetaUserinfo remoteUserInfo = remoteProxyService.getRemoteUserInfo();
        // String orgCode = SqlUtils.wrapIn(remoteUserInfo.getAuth());
        String sql = inspectionResultSql.selectQyYqNum(remoteUserInfo.getOrg());
        InspectionResultNumForm employeeCertificateForm = EruptDaoUtils.selectOne(sql, InspectionResultNumForm.class);

        return EruptApiModel.successApi(employeeCertificateForm);
    }

    // 统计今年本企业被检查次数
    @RequestMapping("erupt-api/get/selectQyCheckNum")
    @EruptRouter(verifyType = EruptRouter.VerifyType.LOGIN)
    public EruptApiModel selectQyCheckNum() {
        MetaUserinfo remoteUserInfo = remoteProxyService.getRemoteUserInfo();
        // String orgCode = SqlUtils.wrapIn(remoteUserInfo.getAuth());
        String sql = inspectionResultSql.selectQyCheckNum(remoteUserInfo.getOrg());
        InspectionResultNumForm employeeCertificateForm = EruptDaoUtils.selectOne(sql, InspectionResultNumForm.class);

        return EruptApiModel.successApi(employeeCertificateForm);
    }

    //统计今年产生的问题数
    @RequestMapping("erupt-api/get/selectQyQuesNum")
    @EruptRouter(verifyType = EruptRouter.VerifyType.LOGIN)
    public EruptApiModel selectQyQuesNum() {
        MetaUserinfo remoteUserInfo = remoteProxyService.getRemoteUserInfo();
        // String orgCode = SqlUtils.wrapIn(remoteUserInfo.getAuth());
        String sql = inspectionResultSql.selectQyQuesNum(remoteUserInfo.getOrg());
        InspectionResultNumForm employeeCertificateForm = EruptDaoUtils.selectOne(sql, InspectionResultNumForm.class);

        return EruptApiModel.successApi(employeeCertificateForm);
    }

    //查询检查名称
    @RequestMapping("erupt-api/get/getInspectionName")
    @EruptRouter(verifyType = EruptRouter.VerifyType.LOGIN)
    public EruptApiModel getInspectionName() {
        //获取当前用户
        EruptUser currentEruptUser = EruptSpringUtil.getBean(EruptUserService.class).getCurrentEruptUser();
        //获取当前用户的orgCode
        String orgCode = "";
        if (currentEruptUser.getEruptOrg() != null) {
            orgCode = currentEruptUser.getEruptOrg().getCode();
        }
        //查询检查流程
        String selectSql = "select * from tb_procedure";
        String whereSql = " where org_code='" + orgCode + "'";
        selectSql += currentEruptUser.getIsAdmin() ? "" : whereSql;

        List<Procedure> procedures = EruptDaoUtils.selectOnes(selectSql, Procedure.class);
        if (ObjectUtils.isNotEmpty(procedures)) {
            List<LinkedTreeMap> list1 = new ArrayList<>();
            procedures.forEach(e -> {
                LinkedTreeMap map = new LinkedTreeMap();
                map.put("code", e.getId());
                map.put("name", e.getName());
                list1.add(map);
            });
            return EruptApiModel.successApi(list1);
        }
        return EruptApiModel.successApi();
    }

    //统计今年产生的问题数
    @RequestMapping("erupt-api/get/jcdx")
    @EruptRouter(verifyType = EruptRouter.VerifyType.LOGIN)
    public EruptApiModel selectJCDX() {
        MetaUserinfo remoteUserInfo = remoteProxyService.getRemoteUserInfo();
        String orgCode = SqlUtils.wrapIn(remoteUserInfo.getAuth());
        List<EruptResultMap> enterprises = EruptDaoUtils.selectOnes("select * from tb_enterprise where org_code "+orgCode+" order by name", EruptResultMap.class);
        List<LinkedTreeMap> list1 = new ArrayList<>();
        for (EruptResultMap vo : enterprises) {
            LinkedTreeMap map = new LinkedTreeMap();
            map.put("code", vo.getString("org_code"));
            map.put("name", vo.getString("name"));
            list1.add(map);
        }
        return EruptApiModel.successApi(list1);
    }



}
