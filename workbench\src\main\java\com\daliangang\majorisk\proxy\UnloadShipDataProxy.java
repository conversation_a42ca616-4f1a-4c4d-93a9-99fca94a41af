/*
 * Copyright © 2020-2035 yundingyun All rights reserved.
 */
package com.daliangang.majorisk.proxy;

import com.daliangang.core.DaliangangContext;
import com.daliangang.majorisk.entity.UnloadShip;
import com.daliangang.majorisk.service.UnloadShipHttpService;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import xyz.erupt.annotation.fun.DataProxy;
import xyz.erupt.annotation.query.Condition;
import xyz.erupt.jpa.dao.EruptDao;
import xyz.erupt.toolkit.service.EruptCacheRedis;
import xyz.erupt.toolkit.utils.NotifyUtils;
import xyz.erupt.upms.model.EruptUser;
import xyz.erupt.upms.service.EruptUserService;

import javax.annotation.Resource;
import javax.transaction.Transactional;
import java.time.LocalDateTime;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

@Service
public class UnloadShipDataProxy implements DataProxy<UnloadShip> {
    @Resource
    private EruptDao eruptDao;
    @Resource
    UnloadShipHttpService unloadShipHttpService;
    @Resource
    private EruptUserService eruptUserService;

    @Resource
    private EruptCacheRedis eruptCacheRedis;

    @Override
    public String beforeFetch(List<Condition> conditions) {
        Condition condition = new Condition("cancelState","0");
        conditions.add(condition);
        return "";
    }

    @Override
    @Transactional
    public void afterFetch(Collection<Map<String, Object>> list) {
        LocalDateTime now = LocalDateTime.now();
        list.forEach(vo -> {
            if (ObjectUtils.isNotEmpty(vo.get("abTime")) && ObjectUtils.isNotEmpty(vo.get("aeTime"))) {
                LocalDateTime abTime = LocalDateTime.parse(vo.get("abTime").toString());
                LocalDateTime aeTime = LocalDateTime.parse(vo.get("aeTime").toString());
                if (abTime.isAfter(now)) vo.put("workState", "未开始");
                else if (aeTime.isBefore(now)) vo.put("workState", "已结束");
                else vo.put("workState", "进行中");
            } else {
                vo.put("workState", "未开始");
            }


        });

    }

    @Override
    public void beforeAdd(UnloadShip unloadShip) {
        LocalDateTime now = LocalDateTime.now();
        if (unloadShip.getAbTime().isAfter(now)) {
            unloadShip.setWorkState("未开始");// 未开始
        } else if (unloadShip.getAeTime().isBefore(now)) {
            unloadShip.setWorkState("已结束");// 已结束
        } else {
            unloadShip.setWorkState("进行中");// 进行中
        }
    }


    @Override
    public void afterUpdate(UnloadShip unloadShip) {
        if (StringUtils.isNotEmpty(unloadShip.getOrgCode())) {
            String token = DaliangangContext.getToken();
            unloadShipHttpService.UpdateHttpUnloadShip(unloadShip, unloadShip.getOrgCode(), unloadShip.getId(), token);
        }
    }

    @Override
    public void searchCondition(Map<String, Object> condition) {
        if (!DaliangangContext.isDepartmentUser()) {
            EruptUser user = eruptUserService.getCurrentEruptUser();
            if (user != null && user.getEruptOrg() != null)
                condition.put("company", user.getEruptOrg().getCode());
        }
    }

    @Override
    public void editBehavior(UnloadShip unloadShip) {
        UnloadShip unloadShip1 = this.queryDataById(unloadShip.getId());
        unloadShip.setPosition(unloadShip1.getPosition());
    }

    @Override
    public UnloadShip queryDataById(long id) {
        UnloadShip unload = eruptDao.queryEntity(UnloadShip.class, "id=" + id);
        List<String> maps = eruptDao.getJdbcTemplate().queryForList("select t.`name` from tb_unload_ship_tanks st,tb_storage_tank t\n" +
                " where st.tanks_id = t.id  and st.unload_id = " +id +
                " union all\n" +
                " select t.`name` from tb_unload_ship_yards st,tb_yard t\n" +
                " where st.yards_id = t.id and st.unload_id = " +id +
                " union all\n" +
                " select t.`name` from tb_unload_ship_wares st,tb_warehouse t\n" +
                " where st.wares_id = t.id and st.unload_id = " +id +
                " union all\n" +
                " select t.`name` from tb_unload_ship_berths st,tb_berth t\n" +
                " where st.berths_id = t.id and st.unload_id = " +id +
                " union all\n" +
                " select t.`name` from tb_unload_ship_wharfs st,tb_loading_dock t\n" +
                " where st.wharfs_id = t.id and st.unload_id = " +id ,String.class);
        if(maps!=null && maps.size()!=0){
            String join = StringUtils.join(maps, "|");
            unload.setPosition(join);
        }

        eruptCacheRedis.getStringRedisTemplate().opsForValue().set(UnloadShip.ATTR_UNLOAD_SHIP,unload.getOrgCode(), 1,TimeUnit.MINUTES);
//        MetaContext.setAttribute(UnloadShip.ATTR_UNLOAD_SHIP, unload);
        return unload;
    }

    @Override
    public void beforeUpdate(UnloadShip unloadShip) {
        if (ObjectUtils.isEmpty(unloadShip.getTanks()) &&
                ObjectUtils.isEmpty(unloadShip.getYards()) &&
                ObjectUtils.isEmpty(unloadShip.getWares()) &&
                ObjectUtils.isEmpty(unloadShip.getBerths()) &&
                ObjectUtils.isEmpty(unloadShip.getWharfs())
        ) {
            NotifyUtils.showErrorDialog("请选择至少一个作业地点！");
        }
    }


//    @Override
//    public void afterDelete(UnloadShip unloadShip) {
//        if (StringUtils.isNotEmpty(unloadShip.getOrgCode())) {
//            String token = DaliangangContext.getToken();
//            unloadShipHttpService.deleteHttpUnloadShip(unloadShip,token);
//        }
//    }
}
