package com.daliangang.datascreen.aspect;

import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import xyz.erupt.core.exception.EruptApiErrorTip;

import xyz.erupt.upms.constant.SessionKey;
import xyz.erupt.upms.service.EruptSessionService;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

@Aspect
@Component
public class TokenCheckAspect {
    @Resource
    private EruptSessionService sessionService;

    @Before("@within(com.daliangang.datascreen.annotation.TokenCheck) || @annotation(com.daliangang.datascreen.annotation.TokenCheck)")
    public void checkToken() {
        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        assert attributes != null;
        HttpServletRequest request = attributes.getRequest();
        String token = request.getHeader("token");

        if (isTokenValid(token)) {
            throw new EruptApiErrorTip("Token已过期,请重新登录");
        }
    }

    private boolean isTokenValid(String token) {
        return null == token || null == sessionService.get(SessionKey.TOKEN_OLINE + token);
    }
}