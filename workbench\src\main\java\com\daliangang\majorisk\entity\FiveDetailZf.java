package com.daliangang.majorisk.entity;

import com.daliangang.majorisk.operation.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import xyz.erupt.annotation.Erupt;
import xyz.erupt.annotation.EruptField;
import xyz.erupt.annotation.fun.DataProxy;
import xyz.erupt.annotation.sub_erupt.*;
import xyz.erupt.annotation.sub_field.Edit;
import xyz.erupt.annotation.sub_field.EditType;
import xyz.erupt.annotation.sub_field.Readonly;
import xyz.erupt.annotation.sub_field.View;
import xyz.erupt.annotation.sub_field.sub_edit.ChoiceType;
import xyz.erupt.annotation.sub_field.sub_edit.InputType;
import xyz.erupt.annotation.sub_field.sub_edit.ShowBy;
import xyz.erupt.core.util.EruptSpringUtil;
import xyz.erupt.toolkit.db.Comment;
import xyz.erupt.toolkit.handler.RemoteEntityChoiceFetchHandler;
import xyz.erupt.toolkit.utils.TplUtils;
import xyz.erupt.upms.model.auth.DataAuthHandler;
import xyz.erupt.upms.model.auth.DataAuthModel;

import javax.persistence.*;
import java.util.Set;

/**
 * <AUTHOR>
 * @since :2023/4/14:16:33
 */
@Erupt(name = "五清单详情", power = @Power(add = true, delete = true, export = false, importable = false, viewDetails = true, edit = true)
        , dataProxy = FiveDetailZf.class,orderBy = "company desc",
        drills = {@Drill(
                title = "责任分工", icon = "fa fa-list-alt",position = 1,
                link = @Link(
                        linkErupt = FiveChecklistsResponsibility.class, joinColumn = "fiveDetail.id"
                )
        )},
        rowOperation = {
//                @RowOperation(title = "查看", icon = "", tip = "查看", eruptClass = FiveDetail.class,
//                        operationHandler = FiveDetailHandler.class, mode = RowOperation.Mode.SINGLE/*, code = TplUtils.REMOTE_ERUPT_CODE + "detail"*/),
//                @RowOperation(title = "基础清单", icon = "fa fa-tasks", /*tip = "基础清单",*/ eruptClass = FiveChecklistsFoundation.class,
//                        operationHandler = FiveChecklistsFoundationHandler.class, mode = RowOperation.Mode.SINGLE, code = TplUtils.REMOTE_ERUPT_CODE + "foundation"),
//                @RowOperation(title = "防控措施", icon = "fa fa-fire-extinguisher", /*tip = "防控措施",*/ eruptClass = FiveChecklistsPreventionAndControl.class,
//                        operationHandler = FiveChecklistsPreventionAndControlHandler.class, mode = RowOperation.Mode.SINGLE, code = TplUtils.REMOTE_ERUPT_CODE + "preventionandcontrol"),
//                @RowOperation(title = "监控检测", icon = "fa fa-area-chart", /*tip = "监控检测",*/ eruptClass = FiveChecklistsMonitor.class,
//                        operationHandler = FiveChecklistsMonitorHandler.class, mode = RowOperation.Mode.SINGLE, code = TplUtils.REMOTE_ERUPT_CODE + "monitor"),
//                @RowOperation(title = "应急处置", icon = "fa fa-ambulance", /*tip = "应急处置",*/ eruptClass = FiveChecklistsEmergencyDisposal.class,
//                        operationHandler = FiveChecklistsEmergencyDisposalHandler.class, mode = RowOperation.Mode.SINGLE, code = TplUtils.REMOTE_ERUPT_CODE + "mergencydisposal"),
                @RowOperation(title = "word导出", icon = "fa fa-arrow-up",
                        //ifExpr = "item.type=='下拉选择' && item.linkClass!=''",
                        mode = RowOperation.Mode.MULTI,

                        //operationHandler = WordExportHandler.class
                        isDown = true,
                        type = RowOperation.Type.TPL,
                        tpl = @Tpl(path = "tpl/zf.docx",
                                engine = Tpl.Engine.FreeMarker,
                                tplHandler = WordExportZfHandler.class)
                )
        }
)
@Entity
@Getter
@Setter
@Comment("五清单详情")
@ApiModel("五清单详情")
@Table(name = "tb_five_detail")
public class FiveDetailZf extends DataAuthModel implements DataProxy<FiveDetailZf> {

    @EruptField(
            views = @View(title = "重大风险名称"),
            edit = @Edit(title = "重大风险名称", show = false, type = EditType.INPUT,// notNull = false,
                    inputType = @InputType(fullSpan = true)))
    @Comment("重大风险名称")
    @ApiModelProperty("重大风险名称")
    private String riskName;

    @EruptField(
            views = @View(title = "所在单位"),
            edit = @Edit(title = "所在单位",  readonly = @Readonly(exprHandler = DataAuthHandler.class), type = EditType.CHOICE, show = false,//notNull = true,
                    choiceType = @ChoiceType(fetchHandler = RemoteEntityChoiceFetchHandler.class, fetchHandlerParams = {"main", "Enterprise", "org_code,name"})))
    @Comment("所在单位")
    @ApiModelProperty("所在单位")
    private String company;

    @EruptField(
            views = @View(title = "编辑模式", show = false, sortable = true),
            edit = @Edit(title = "编辑模式", type = EditType.BOOLEAN, notNull = true, show = false))
    @Transient
    private Boolean edit = false;

    @OneToOne(cascade = CascadeType.ALL)
    @EruptField(edit = @Edit(title = "基础清单", type = EditType.COMBINE/*, readonly = @Readonly, showBy = @ShowBy(dependField = "edit", expr = "value != true")*/))
    private FiveChecklistsFoundation fiveChecklistsFoundation;

    @OneToOne(cascade = CascadeType.ALL)
    @EruptField(edit = @Edit(title = "防控措施清单", type = EditType.COMBINE, /*readonly = @Readonly,*/ showBy = @ShowBy(dependField = "edit", expr = "value != true")))
    private FiveChecklistsPreventionAndControl fiveChecklistsPreventionAndControl;


    @OneToOne(cascade = CascadeType.ALL)
    @EruptField(edit = @Edit(title = "监测监控清单", type = EditType.COMBINE, /*readonly = @Readonly,*/ showBy = @ShowBy(dependField = "edit", expr = "value != true")))
    private FiveChecklistsMonitor fiveChecklistsMonitor;

    @OneToOne(cascade = CascadeType.ALL)
    @EruptField(edit = @Edit(title = "应急处置清单", type = EditType.COMBINE, /*readonly = @Readonly,*/ showBy = @ShowBy(dependField = "edit", expr = "value != true")))
    private FiveChecklistsEmergencyDisposal fiveChecklistsEmergencyDisposal;

    @EruptField(edit = @Edit(title = "责任分工清单", type = EditType.TAB_TABLE_ADD, showBy = @ShowBy(dependField = "edit", expr = "value != true")))
    @OneToMany(cascade = CascadeType.ALL, orphanRemoval = true)
    @OrderBy
    @JoinColumn(name = "tb_five_detail_id")
    @Comment("责任分工清单")
    @ApiModelProperty("责任分工清单")
    private Set<FiveChecklistsResponsibility> fiveChecklistsResponsibilities;

//    @Override
//    public void addBehavior(FiveDetailZf fiveDetail) {
//        fiveDetail.setEdit(true);
//        fiveDetail.setFiveChecklistsFoundation(new FiveChecklistsFoundation());
//       // fiveDetail.setRiskName(fiveDetail.getRiskName());
//        fiveDetail.getFiveChecklistsFoundation().setOrgCode(fiveDetail.getOrgCode());
//        fiveDetail.getFiveChecklistsFoundation().setCompany(fiveDetail.getOrgCode());
//    }
//
//    @Override
//    public void beforeAdd(FiveDetailZf fiveDetail) {
//
//        fiveDetail.setRiskName(fiveDetail.getFiveChecklistsFoundation().getName());
//        fiveDetail.setOrgCode(fiveDetail.getFiveChecklistsFoundation().getCompany());
//        fiveDetail.setCompany(fiveDetail.getOrgCode());
//        fiveDetail.fiveChecklistsFoundation.setOrgCode(fiveDetail.getOrgCode());
//        fiveDetail.fiveChecklistsEmergencyDisposal.setOrgCode(fiveDetail.getOrgCode());
//        fiveDetail.fiveChecklistsPreventionAndControl.setOrgCode(fiveDetail.getOrgCode());
//        fiveDetail.fiveChecklistsMonitor.setOrgCode(fiveDetail.getOrgCode());
//    }


    @Override
    public FiveDetailZf queryDataById(long id) {
        FiveDetailZf fiveDetail = EruptSpringUtil.getBean(FiveDetailZf.class);
        FiveChecklistsFoundationHandler fiveChecklistsFoundationHandler = EruptSpringUtil.getBean(FiveChecklistsFoundationHandler.class);
        FiveChecklistsResponsibilityHandler fiveChecklistsResponsibilityHandler = EruptSpringUtil.getBean(FiveChecklistsResponsibilityHandler.class);
        FiveChecklistsPreventionAndControlHandler fiveChecklistsPreventionAndControlHandler = EruptSpringUtil.getBean(FiveChecklistsPreventionAndControlHandler.class);
        FiveChecklistsMonitorHandler fiveChecklistsMonitorHandler = EruptSpringUtil.getBean(FiveChecklistsMonitorHandler.class);
        FiveChecklistsEmergencyDisposalHandler fiveChecklistsEmergencyDisposalHandler = EruptSpringUtil.getBean(FiveChecklistsEmergencyDisposalHandler.class);

        FiveChecklistsFoundation fiveChecklistsFoundation = fiveChecklistsFoundationHandler.detail(id);
        Set<FiveChecklistsResponsibility> fiveChecklistsResponsibilities = fiveChecklistsResponsibilityHandler.detail(id);
        FiveChecklistsPreventionAndControl fiveChecklistsPreventionAndControl = fiveChecklistsPreventionAndControlHandler.detail(id);
        FiveChecklistsMonitor fiveChecklistsMonitor = fiveChecklistsMonitorHandler.detail(id);
        FiveChecklistsEmergencyDisposal fiveChecklistsEmergencyDisposal = fiveChecklistsEmergencyDisposalHandler.detail(id);
        fiveDetail.setFiveChecklistsFoundation(fiveChecklistsFoundation);
        fiveDetail.setFiveChecklistsResponsibilities(fiveChecklistsResponsibilities);
        fiveDetail.setFiveChecklistsPreventionAndControl(fiveChecklistsPreventionAndControl);
        fiveDetail.setFiveChecklistsMonitor(fiveChecklistsMonitor);
        fiveDetail.setFiveChecklistsEmergencyDisposal(fiveChecklistsEmergencyDisposal);
        return fiveDetail;
    }
}
