package com.daliangang.safedaily.entity;

import com.daliangang.core.CompanyRenderHandler;
import com.daliangang.safedaily.operation.ButtonShowHandler;
import com.daliangang.safedaily.operation.DepartPowerHandler;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import xyz.erupt.annotation.Erupt;
import xyz.erupt.annotation.EruptField;
import xyz.erupt.annotation.expr.ExprBool;
import xyz.erupt.annotation.fun.DataProxy;
import xyz.erupt.annotation.query.Condition;
import xyz.erupt.annotation.sub_erupt.Power;
import xyz.erupt.annotation.sub_erupt.RowOperation;
import xyz.erupt.annotation.sub_field.Edit;
import xyz.erupt.annotation.sub_field.EditType;
import xyz.erupt.annotation.sub_field.Readonly;
import xyz.erupt.annotation.sub_field.View;
import xyz.erupt.annotation.sub_field.sub_edit.*;
import xyz.erupt.toolkit.db.Comment;
import xyz.erupt.toolkit.handler.DelOperationHandler;
import xyz.erupt.toolkit.handler.EditOperationHandler;
import xyz.erupt.toolkit.handler.RemoteEntityChoiceFetchHandler;
import xyz.erupt.toolkit.submit.CheckSubmit;
import xyz.erupt.toolkit.utils.TplUtils;
import xyz.erupt.upms.model.auth.DataAuthHandler;
import xyz.erupt.upms.model.auth.DataAuthModel;

import javax.persistence.Entity;
import javax.persistence.Table;
import java.util.Date;
import java.util.List;

/**
 * @Title: SafetyAlertDayManagement
 * <AUTHOR>
 * @Package com.daliangang.safedaily.entity
 * @Date 2024/3/7 15:16
 * @description: 七一六安全警示
 */
@Erupt(name = "七一六安全警示",
        power = @Power(powerHandler = DepartPowerHandler.class),
        dataProxy = SafetyAlertDayManagement.class,
        orderBy = "SafetyAlertDayManagement.updateTime desc",
        rowOperation = {
//                @RowOperation(
//                        title = "上报",
//                        icon = "fa fa-arrow-circle-o-up",
//                        operationHandler = SafetyAlertEscalationHandler.class,
//                        ifExpr = "item.submitted=='未上报'",
//                        mode = RowOperation.Mode.SINGLE,
//                        show = @ExprBool(exprHandler = ButtonShowHandler.class)
//                ),
                @RowOperation(
                        title = "编辑",
                        icon = "fa fa-edit",
                        code = TplUtils.EDIT_OPER_CODE,
                        eruptClass = SafetyAlertDayManagement.class,
                        operationHandler = EditOperationHandler.class,
//                        ifExpr = "item.submitted=='未上报'",
                        ifExprBehavior = RowOperation.IfExprBehavior.HIDE,
                        mode = RowOperation.Mode.SINGLE,
                        show = @ExprBool(exprHandler = ButtonShowHandler.class)

                ),
                @RowOperation(
                        title = "删除",
                        icon = "fa fa-trash-o",
                        operationHandler = DelOperationHandler.class,
//                        ifExpr = "item.submitted=='未上报'",
                        ifExprBehavior = RowOperation.IfExprBehavior.HIDE,
                        mode = RowOperation.Mode.SINGLE,
                        show = @ExprBool(exprHandler = ButtonShowHandler.class)

                )
        })
@Table(name = "tb_safety_alert_management")
@Entity
@Getter
@Setter
@Comment("七一六安全警示")
public class SafetyAlertDayManagement extends DataAuthModel implements DataProxy<SafetyAlertDayManagement> {

    @EruptField(
            views = @View(title = "单位名称",width = "260px"),
            edit = @Edit(title = "单位名称",search = @Search(ifRender = @ExprBool(exprHandler = CompanyRenderHandler.class)), readonly = @Readonly(exprHandler = DataAuthHandler.class), type = EditType.CHOICE, notNull = true,
                    choiceType = @ChoiceType(fullSpan = true,fetchHandler = RemoteEntityChoiceFetchHandler.class, fetchHandlerParams = {"main", "Enterprise", "org_code,name"}, reload = true)))
    @Comment("单位名称")
    @ApiModelProperty("单位名称")
    private String company;

    @EruptField(
            views = @View(title = "活动地点",width = "150px"),
            edit = @Edit(title = "活动地点", type = EditType.TEXTAREA, notNull = true))
    @Comment("活动地点")
    @ApiModelProperty("活动地点")
    private String activityArea;

    @EruptField(
            views = @View(title = "活动名称",width = "150px"),
            edit = @Edit(title = "活动名称", type = EditType.INPUT, notNull = true,
                    inputType = @InputType(fullSpan = true)))
    @Comment("活动名称")
    @ApiModelProperty("活动名称")
    private String activityName;

    @EruptField(
            views = @View(title = "参与人数",width = "150px"),
            edit = @Edit(title = "参与人数", type = EditType.INPUT, notNull = true,
                    inputType = @InputType(fullSpan = true)))
    @Comment("参与人数")
    @ApiModelProperty("参与人数")
    private String peopleNum;

    @EruptField(
            views = @View(title = "活动情况",width = "150px"),
            edit = @Edit(title = "活动情况", type = EditType.TEXTAREA, notNull = true))
    @Comment("活动情况")
    @ApiModelProperty("活动情况")
    private String activitySituation;

    @EruptField(
            views = @View(title = "特点和亮点",width = "150px"),
            edit = @Edit(title = "特点和亮点", type = EditType.TEXTAREA, notNull = true))
    @Comment("特点和亮点")
    @ApiModelProperty("特点和亮点")
    private String activityPeculiarity;

    @EruptField(
            views = @View(title = "上报时间",width = "100px",show = false),
            edit = @Edit(title = "上报时间", type = EditType.DATE, search = @Search, show = false,
                    dateType = @DateType))
    @Comment("上报时间")
    @ApiModelProperty("上报时间")
    private java.util.Date fillingDate;

    @EruptField(
            views = @View(title = "上报状态",width = "100px",show = false),
            edit = @Edit(title = "上报状态", type = EditType.BOOLEAN, show = false,
                    boolType = @BoolType(trueText = "已上报", falseText = "未上报")))
    @CheckSubmit(linkProperty = "createTime", checkTimeDiffPeriod = 1)
    @Comment("上报状态")
    @ApiModelProperty("上报状态")
    private Boolean submitted;

    @Override
    public String beforeFetch(List<Condition> conditions) {
//        if (DaliangangContext.isDepartmentUser()) return "submitted=true";
        return DataProxy.super.beforeFetch(conditions);
    }

    @Override
    public void addBehavior(SafetyAlertDayManagement safetyAlertDayManagement) {
        safetyAlertDayManagement.setSubmitted(Boolean.FALSE);
        safetyAlertDayManagement.setSubmitted(Boolean.TRUE);
        safetyAlertDayManagement.setFillingDate(new Date());
    }

    @Override
    public void beforeAdd(SafetyAlertDayManagement safetyAlertDayManagement) {
//        EruptDao eruptDao = EruptSpringUtil.getBean(EruptDao.class);
//        List<SafetyAlertDayManagement> safetyAlertDayManagements = eruptDao.queryEntityList(SafetyAlertDayManagement.class, "company='" + safetyAlertDayManagement.getCompany() + "'");
//        safetyAlertDayManagements = safetyAlertDayManagements.stream().filter(d -> FindDateUtil.isThisYear(d.getFillingDate())).collect(Collectors.toList());
//        if(safetyAlertDayManagements.size()>0){
//            NotifyUtils.showErrorMsg("本年数据已经添加，请勿重复添加！");
//
//        }
    }
}
