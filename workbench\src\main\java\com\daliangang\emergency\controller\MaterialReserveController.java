package com.daliangang.emergency.controller;

import com.daliangang.emergency.entity.MaterialReserve;
import com.daliangang.emergency.form.EmergencyTeamForm;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import xyz.erupt.core.annotation.EruptRouter;
import xyz.erupt.core.module.MetaUserinfo;
import xyz.erupt.core.view.EruptApiModel;
import xyz.erupt.toolkit.remote.RemoteProxyService;
import xyz.erupt.toolkit.utils.EruptDaoUtils;

import javax.annotation.Resource;
import javax.transaction.Transactional;
import java.util.List;

/**
 * <AUTHOR>
 * @since :2023/4/14:14:40
 */
@RestController
public class MaterialReserveController {

    @Resource
    private RemoteProxyService remoteProxyService;

    @RequestMapping("erupt-api/MaterialReserve/select")
    @EruptRouter(verifyType = EruptRouter.VerifyType.LOGIN)
    @Transactional
    public EruptApiModel selectMaterialReserve() {
        MetaUserinfo remoteUserInfo = remoteProxyService.getRemoteUserInfo();
        List<MaterialReserve> emergencyTeamForm = EruptDaoUtils.selectOnes("SELECT  material_reserve,material_type,contacts,contact_number from  tb_material_reserve where org_code ='" + remoteUserInfo.getOrg() + "'", MaterialReserve.class);
        return EruptApiModel.successApi(emergencyTeamForm);
    }
}
