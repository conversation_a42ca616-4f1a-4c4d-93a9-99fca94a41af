/*
* Copyright © 2020-2035 yundingyun All rights reserved.
*/
package com.daliangang.workbench.operation;

import org.springframework.stereotype.*;
import xyz.erupt.annotation.fun.*;
import java.util.*;
import com.daliangang.workbench.entity.*;

@Service
public class EmployeeCertificateAnnexExtensionHandler implements OperationHandler<EmployeeCertificate, Void> {
   @Override
   public String exec(List<EmployeeCertificate> data, Void unused, String[] param) {
       return null;
	}
}