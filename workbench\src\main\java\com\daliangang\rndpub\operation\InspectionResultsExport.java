package com.daliangang.rndpub.operation;

import com.daliangang.rndpub.entity.InspectionResults;
import com.daliangang.rndpub.entity.InspectionResultsView;
import com.daliangang.rndpub.entity.Procedure;
import com.daliangang.workbench.entity.Enterprise;
import org.apache.poi.hssf.usermodel.HSSFCell;
import org.apache.poi.hssf.usermodel.HSSFRow;
import org.apache.poi.hssf.usermodel.HSSFSheet;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.util.CellRangeAddress;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import xyz.erupt.annotation.fun.OperationHandler;
import xyz.erupt.jpa.dao.EruptDao;
import xyz.erupt.toolkit.utils.FileUtils;
import xyz.erupt.toolkit.utils.ZipUtils;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.transaction.Transactional;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.InputStream;
import java.io.OutputStream;
import java.net.HttpURLConnection;
import java.net.URL;
import java.text.SimpleDateFormat;
import java.util.List;
import java.util.zip.ZipOutputStream;

@Service
public class InspectionResultsExport implements OperationHandler<InspectionResultsView, Void> {

    @Override
    public String exec(List<InspectionResultsView> data, Void unused, String[] param) {
        InspectionResultsView model = data.get(0);
        String link = model.getClass().getSimpleName() + "/" + model.getId();
        String url = "window.open('/erupt-api/download/zip/" + link + "', '_blank')";
        return url;
    }

    @RestController
    @Transactional
    public static class InspectionrResultsExportController {
        @Value("${minio.endpoint}")
        private String endpoint;

        @Value("${minio.bucketName}")
        private String bucketName;

        @Resource
        private EruptDao eruptDao;

        /**
         * 获取文件二进制流
         */
        public byte[] getFileBytes(String urlMsg,String fileName,ZipOutputStream zipOutputStream) {
            try {
                URL url = new URL(urlMsg);
                // 打开链接
                HttpURLConnection connection = (HttpURLConnection)url.openConnection();
                // get请求
                connection.setRequestMethod("GET");
                // 超时响应时间
                connection.setConnectTimeout(5*1000);
                //获取流
                connection.connect();// 打开连接
                InputStream inputStream = connection.getInputStream();
                ByteArrayOutputStream outputStream = new ByteArrayOutputStream();

                byte[] buf = new byte[4096];
                int length = inputStream.read(buf);
                //保存文件
                while(length != -1)
                {
                    outputStream.write(buf, 0, length);
                    length = inputStream.read(buf);
                }

                byte[] finalBytes = outputStream.toByteArray();
                ZipUtils.writeEntry(zipOutputStream, fileName, finalBytes);
                inputStream.close();
                outputStream.close();
                connection.disconnect();
                return finalBytes;
            }catch (Exception e){
                System.out.println(e);
            }
            return null;
        }

        //确定按钮
        @RequestMapping("/erupt-api/download/zip/{erupt}/{id}")
        public String callback(@PathVariable("erupt") String eruptName, @PathVariable("id") Long id, HttpServletResponse response) {
            try {
                //根据id查询到检查结果的记录
                String selectSql = "id=" + id;
                InspectionResults inspectionResultsEntity = eruptDao.queryEntity(InspectionResults.class, selectSql);
                //创建poi对象
                //创建HSSFWorkbook对象
                HSSFWorkbook wb = new HSSFWorkbook();
                //建立sheet对象
                HSSFSheet sheet = wb.createSheet("检查结果表");
                //在sheet里创建第一行，参数为行索引
                HSSFRow row1 = sheet.createRow(0);
                //创建单元格
                HSSFCell cell = row1.createCell(0);
                //设置单元格内容
                cell.setCellValue("检查结果表");
                //合并单元格CellRangeAddress构造参数依次表示起始行，截至行，起始列， 截至列
                sheet.addMergedRegion(new CellRangeAddress(0, 0, 0, 3));
                //在sheet里创建第二行
                HSSFRow row2 = sheet.createRow(1);
                //创建单元格并设置单元格内容
                row2.createCell(0).setCellValue("检查名称");
                row2.createCell(1).setCellValue("检查对象");
                row2.createCell(2).setCellValue("检查事项");
                row2.createCell(3).setCellValue("问题描述");
                row2.createCell(4).setCellValue("检查依据");
                row2.createCell(5).setCellValue("整改建议");
                row2.createCell(6).setCellValue("整改截止时间（年月日）");
                //在sheet里创建第三行
                //将检查对象的编码和检查事项的编号转换为名称,然后写入表格中
                HSSFRow row3 = sheet.createRow(2);
                //根据检查流程的id查询检查流程名称
                String procedureById = "id=" + Long.parseLong(inspectionResultsEntity.getInspectionName());
                Procedure procedureEntity = eruptDao.queryEntity(Procedure.class, procedureById);
                row3.createCell(0).setCellValue(procedureEntity.getName());
                //根据公司编码查询公司
                String enterpriseSql = "org_code='" + inspectionResultsEntity.getCheckObject() + "'";
                Enterprise enterprise = eruptDao.queryEntity(Enterprise.class, enterpriseSql);
                row3.createCell(1).setCellValue(enterprise.getName());
                //根据检查事项的id查询检查事项
                row3.createCell(2).setCellValue(inspectionResultsEntity.getInspectionItems());
                //问题描述
                row3.createCell(3).setCellValue(inspectionResultsEntity.getProblemDescription());
                //检查依据
                row3.createCell(4).setCellValue(inspectionResultsEntity.getInspectionBasis());
                //整改建议
                row3.createCell(5).setCellValue(inspectionResultsEntity.getProposal());
                //整改截止日期
                SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
                String deadLineText = sdf.format(inspectionResultsEntity.getDeadline());
                row3.createCell(6).setCellValue(deadLineText);
                //获取输出流
                String module = procedureEntity.getName()+".zip";
                OutputStream out = FileUtils.downLoadFile(response, module);
                //创建一个压缩输出流
                ZipOutputStream zipOutputStream = ZipUtils.createZip(out);
                ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
                wb.write(byteArrayOutputStream);
                byteArrayOutputStream.flush();
                //excel写入压缩输出流
                ZipUtils.writeEntry(zipOutputStream, module + File.separator + "检查结果表.xlsx", byteArrayOutputStream.toByteArray());
                byteArrayOutputStream.close();
                wb.close();
                if(inspectionResultsEntity.getInspectionBasisFile()!=null&&!inspectionResultsEntity.getInspectionBasisFile().equals("")){
                    getFileBytes(endpoint + "/" + bucketName  + inspectionResultsEntity.getInspectionBasisFile(),module + File.separator +inspectionResultsEntity.getInspectionBasisFile().substring(inspectionResultsEntity.getInspectionBasisFile().lastIndexOf("/")+1),zipOutputStream);

                }
                if(inspectionResultsEntity.getProposalFile()!=null&&!inspectionResultsEntity.getProposalFile().equals("")){
                    getFileBytes(endpoint + "/" + bucketName  + inspectionResultsEntity.getProposalFile(),module + File.separator +inspectionResultsEntity.getProposalFile().substring(inspectionResultsEntity.getProposalFile().lastIndexOf("/")+1),zipOutputStream);
                }
                zipOutputStream.close();
                out.close();
            } catch (Exception e) {
                System.out.println(e);
            }
            return null;
        }
    }

}
