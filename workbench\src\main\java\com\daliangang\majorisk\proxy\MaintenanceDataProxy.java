/*
* Copyright © 2020-2035 yundingyun All rights reserved.
*/
package com.daliangang.majorisk.proxy;

import com.daliangang.core.DaliangangContext;
import lombok.*;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.stereotype.*;
import xyz.erupt.annotation.fun.*;
import com.daliangang.majorisk.entity.*;
import xyz.erupt.annotation.query.Condition;
import xyz.erupt.annotation.sub_field.Edit;
import xyz.erupt.jpa.dao.EruptDao;
import xyz.erupt.toolkit.utils.EruptDaoUtils;
import xyz.erupt.toolkit.utils.NotifyUtils;
import xyz.erupt.upms.model.EruptUser;
import xyz.erupt.upms.service.EruptUserService;

import javax.annotation.*;
import javax.transaction.Transactional;
import java.net.CacheRequest;
import java.time.LocalDateTime;
import java.util.*;

@Service
@Transactional
public class MaintenanceDataProxy implements DataProxy<Maintenance> {

    @Override
    public String beforeFetch(List<Condition> conditions) {
        if (DaliangangContext.isDepartmentUser()) return "submitted=true";
        conditions.forEach(System.out::println);
        return null;
    }

    @Resource
    private EruptDao eruptDao;
    @Resource
    private EruptUserService eruptUserService;
    @Override
    public void beforeAdd(Maintenance maintenance) {
        LocalDateTime now = LocalDateTime.now();

        if (maintenance.getStarDate().isAfter(now)) {
            maintenance.setState("NOT_STARTED");// 未开始
        } else if (maintenance.getEndDate().isBefore(now)) {
            maintenance.setState("ENDED");// 已结束
        } else {
            maintenance.setState("STARTED");// 进行中
        }
        //开始时间必须是当天
        if(maintenance.getStarDate().getDayOfMonth()!=now.getDayOfMonth()){
            NotifyUtils.showErrorMsg("开始时间必须是当天!");
        }
        //结束时间不能在开始时间之前
        if(maintenance.getEndDate().isBefore(maintenance.getStarDate())){
            NotifyUtils.showErrorMsg("结束时间不能在开始时间之前!");
        }

    }

    @Override
    public void afterFetch(Collection<Map<String, Object>> list) {
        List<Maintenance> maintenances = EruptDaoUtils.convert(list, Maintenance.class);
        if (ObjectUtils.isNotEmpty(maintenances)) {
            LocalDateTime now = LocalDateTime.now();
            maintenances.forEach(v->{
                if (v.getStarDate().isAfter(now)) {
                    v.setState("NOT_STARTED");// 未开始
                } else if (v.getEndDate().isBefore(now)) {
                    v.setState("ENDED");// 已结束
                } else {
                    v.setState("STARTED");// 进行中
                }
                eruptDao.merge(v);
            });

        }
    }

    @Override
    public void beforeUpdate(Maintenance report) {
        LocalDateTime now = LocalDateTime.now();
        if (report.getSubmitted()) {
            NotifyUtils.showErrorMsg("报告单已经上报，请勿修改！");
        }
        //开始时间必须是当天
        if(report.getStarDate().getDayOfMonth()!=now.getDayOfMonth()){
            NotifyUtils.showErrorMsg("开始时间必须是当天!");
        }
        //结束时间不能在开始时间之前
        if(report.getEndDate().isBefore(report.getStarDate())){
            NotifyUtils.showErrorMsg("结束时间不能在开始时间之前!");
        }
    }

    @Override
    public void beforeDelete(Maintenance report) {
        if (report.getSubmitted()) {
            NotifyUtils.showErrorMsg("报告单已经上报，请勿删除！");
        }
    }

//    @Override
//    public void searchCondition(Map<String, Object> condition) {
//        if(!DaliangangContext.isDepartmentUser()){
//            EruptUser user = eruptUserService.getCurrentEruptUser();
//            if (user != null && user.getEruptOrg() != null)
//                condition.put("company", user.getEruptOrg().getCode());
//        }
//    }
}
