/*
* Copyright © 2020-2035 yundingyun All rights reserved.
*/
package com.daliangang.majorisk.entity;

import com.daliangang.core.CompanyRenderHandler;
import com.daliangang.majorisk.proxy.ReserveReportingDataProxy;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import xyz.erupt.annotation.Erupt;
import xyz.erupt.annotation.EruptField;
import xyz.erupt.annotation.expr.ExprBool;
import xyz.erupt.annotation.sub_erupt.Power;
import xyz.erupt.annotation.sub_field.Edit;
import xyz.erupt.annotation.sub_field.EditType;
import xyz.erupt.annotation.sub_field.Readonly;
import xyz.erupt.annotation.sub_field.View;
import xyz.erupt.annotation.sub_field.sub_edit.ChoiceType;
import xyz.erupt.annotation.sub_field.sub_edit.Search;
import xyz.erupt.toolkit.db.Comment;
import xyz.erupt.toolkit.handler.EruptDictCodeChoiceFetchHandler;
import xyz.erupt.toolkit.handler.RemoteCallChoiceFetchHandler;
import xyz.erupt.toolkit.handler.RemoteEntityChoiceFetchHandler;
import xyz.erupt.upms.model.auth.DataAuthHandler;
import xyz.erupt.upms.model.auth.DataAuthModel;

import javax.persistence.Entity;
import javax.persistence.Lob;
import javax.persistence.Table;

@Erupt(name = "储罐每日储量上报", power = @Power(add = false, delete = false, export = false, importable = false, viewDetails = true, edit = true)
, dataProxy = ReserveReportingDataProxy.class
, rowOperation = {
//@RowOperation(title = "上报", icon = "fa Example ofcloud-upload", operationHandler = ReserveReportingEscalationHandler.class, mode = RowOperation.Mode.BUTTON)
//		@RowOperation(
//				title = "上报",
//				icon = "fa fa-arrow-circle-o-up",
//				operationHandler = ReserveReportingEscalationHandler.class,
//				ifExpr = "item.submitted=='未上报'",
//				mode = RowOperation.Mode.SINGLE
//		),
//		@RowOperation(title = "编辑",
//				icon = "fa fa-edit",
//				code = TplUtils.EDIT_OPER_CODE,
//				eruptClass = ReserveReporting.class,
//				operationHandler = EditOperationHandler.class,
//				ifExpr = "item.submitted=='未上报'",
//				ifExprBehavior = RowOperation.IfExprBehavior.HIDE,
//				mode = RowOperation.Mode.SINGLE),
//		@RowOperation(title = "删除",
//				icon = "fa fa-trash-o",
//				operationHandler = DelOperationHandler.class,
//				ifExpr = "item.submitted=='未上报'",
//				ifExprBehavior = RowOperation.IfExprBehavior.HIDE,
//				mode = RowOperation.Mode.SINGLE
//		),
})
@Table(name = "tb_reserve_reporting")
@Entity
@Getter
@Setter
@Comment("储罐每日储量上报")
@ApiModel("储罐每日储量上报")
public class ReserveReporting extends DataAuthModel {
	@EruptField(
		views = @View(title = "企业名称",width = "260px"),
		edit = @Edit(title = "企业名称",search = @Search(ifRender = @ExprBool(exprHandler = CompanyRenderHandler.class)), readonly = @Readonly(exprHandler = DataAuthHandler.class) , type = EditType.CHOICE, notNull = true,
		choiceType = @ChoiceType(fullSpan = true,fetchHandler = RemoteEntityChoiceFetchHandler.class, fetchHandlerParams ={"main","Enterprise","org_code,name"})))
	@Comment("企业名称")
	@ApiModelProperty("企业名称")
	private String company;

//	@EruptField(
//		views = @View(title = "作业类型"),
//		edit = @Edit(title = "作业类型", type = EditType.CHOICE, search = @Search(vague = true), notNull = true,
//		choiceType = @ChoiceType(fetchHandler = EruptDictCodeChoiceFetchHandler.class, fetchHandlerParams = "type")))
//	@Comment("作业类型")
//	@ApiModelProperty("作业类型")
//	private String type;

	@EruptField(
		views = @View(title = "储罐编号",width = "150px"),
		edit = @Edit(title = "储罐编号", type = EditType.CHOICE,readonly = @Readonly, notNull = true,
	    choiceType = @ChoiceType(fetchHandler = RemoteCallChoiceFetchHandler.class, fetchHandlerParams ={"main","erupt-api/get/tankName"})))
	@Comment("储罐编号")
	@ApiModelProperty("储罐编号")
	private String number;

//	@EruptField(
//		views = @View(title = "现存货种"),
//			edit = @Edit(
//					title = "现存货种",
//					type = EditType.CHOICE,
//					notNull = true,
//					choiceType = @ChoiceType(fetchHandler = RemoteCallChoiceFetchHandler.class, fetchHandlerParams ={"main","erupt-api/get/goodsType"})
//			)
//		)
//	@Comment("现存货种")
//	@ApiModelProperty("现存货种")
//	private String yardType;
	@EruptField(
			views = @View(title = "现存货种",width = "150px"),
			edit = @Edit(
					title = "现存货种",
					type = EditType.INPUT,
					notNull = true
			)
	)
	@Comment("现存货种")
	@ApiModelProperty("现存货种")
	private String yardType;

	@EruptField(
		views = @View(title = "当前储存量",width = "150px"),
		edit = @Edit(title = "当前储存量",
				type = EditType.INPUT


		)
	)
	@Comment("当前储存量")
	@ApiModelProperty("当前储存量")
	private String yardNow;

	@EruptField(
			views = @View(title = "货种存储情况",show = false),
			edit = @Edit(title = "货种存储情况", type = EditType.CHOICE,
					choiceType = @ChoiceType(fetchHandler = EruptDictCodeChoiceFetchHandler.class, fetchHandlerParams = "yardNow"),
					notNull = true))

	@Comment("货种存储情况")
	@ApiModelProperty("货种存储情况")
	private  String storageGoods;

	@EruptField(
			views = @View(title = "更新时间",width = "150px"),
			edit = @Edit(title = "更新时间", show = false))
	@Comment("更新时间")
	@ApiModelProperty("更新时间")
	private java.util.Date modifyTime;


/*	@EruptField(
		views = @View(title = "经度_位置", show = false),
		edit = @Edit(title = "经度_位置", type = EditType.NUMBER,
		numberType = @NumberType))
	@Comment("经度_位置")
	@ApiModelProperty("经度_位置")
	private Integer longitude;

	@EruptField(
		views = @View(title = "纬度_位置", show = false),
		edit = @Edit(title = "纬度_位置", type = EditType.NUMBER,
		numberType = @NumberType))
	@Comment("纬度_位置")
	@ApiModelProperty("纬度_位置")
	private Integer latitude;*/

//	@EruptField(
//		views = @View(title = "堆场/仓库编号", show = false),
//		edit = @Edit(title = "堆场/仓库编号", type = EditType.CHOICE, notNull = true,
//		choiceType = @ChoiceType(fetchHandler = SqlChoiceFetchHandler.class, fetchHandlerParams = "select '测试项','测试项'")))
//	@Comment("堆场/仓库编号")
//	@ApiModelProperty("堆场/仓库编号")
//	private String yardNum;

//	@EruptField(
//		views = @View(title = "货种储存情况", show = false),
//		edit = @Edit(title = "货种储存情况", type = EditType.INPUT, notNull = true,
//		inputType = @InputType))
//	@Comment("货种储存情况")
//	@ApiModelProperty("货种储存情况")
//	private String yardNow;
//	@EruptField(
//			views = @View(title = "状态"),
//			edit = @Edit(title = "状态",
//					type = EditType.BOOLEAN,
//					show = false,
//					boolType = @BoolType(trueText = "已上报", falseText = "未上报")
//			)
//	)
//	@CheckSubmit(linkProperty = "year", checkTimeDiffPeriod = 1)
//	@Comment("状态")
//	@ApiModelProperty("状态")
//	private Boolean submitted;

}
