/*
* Copyright © 2020-2035 yundingyun All rights reserved.
*/
package com.daliangang.emergency.operation;

import org.springframework.stereotype.*;
import xyz.erupt.annotation.fun.*;
import java.util.*;
import com.daliangang.emergency.entity.*;
import xyz.erupt.jpa.dao.EruptDao;
import xyz.erupt.toolkit.utils.NotifyUtils;

import javax.annotation.Resource;
import javax.transaction.Transactional;

@Service
public class EvaluationManageReleaseHandler implements OperationHandler<EvaluationManage, Void> {
    @Resource
    EruptDao eruptDao;
    @Override
    @Transactional
   public String exec(List<EvaluationManage> data, Void unused, String[] param) {
        data.forEach(v->{
            v.setPublicState(true);
            eruptDao.merge(v);
        });
        return NotifyUtils.getSuccessNotify("发布成功！");
	}
}
