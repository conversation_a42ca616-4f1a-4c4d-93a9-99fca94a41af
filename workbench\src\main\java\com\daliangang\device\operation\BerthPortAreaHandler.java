package com.daliangang.device.operation;

import org.springframework.stereotype.Service;
import xyz.erupt.annotation.fun.LinkByHandler;

@Service
public class BerthPortAreaHandler  implements LinkByHandler {

    @Override
    public String placeHolder(Object prop) {
        return (String) prop ;

//        if(StringUtils.isNotEmpty((String)prop)){
//            return String.format("\n and company = '%s'",prop);
//        }else{
//            return  "and 1 = 1";
//        }
    }

}
