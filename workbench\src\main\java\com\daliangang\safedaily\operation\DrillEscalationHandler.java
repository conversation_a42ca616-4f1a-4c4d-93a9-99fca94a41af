package com.daliangang.safedaily.operation;

import cn.hutool.json.JSONObject;
import com.daliangang.core.FindDateUtil;
import com.daliangang.emergency.entity.Drill;
import com.daliangang.safedaily.entity.Release;
import org.springframework.stereotype.Service;
import xyz.erupt.annotation.fun.OperationHandler;
import xyz.erupt.common.http.EruptRemoteUtils;
import xyz.erupt.jpa.dao.EruptDao;
import xyz.erupt.toolkit.remote.RemoteProxyService;
import xyz.erupt.toolkit.utils.NotifyUtils;

import javax.annotation.Resource;
import javax.transaction.Transactional;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @Title: DrillEscalationHandler
 * <AUTHOR>
 * @Package com.daliangang.safedaily.operation
 * @Date 2024/3/7 16:44
 * @description: 应急管理日
 */
@Service
public class DrillEscalationHandler implements OperationHandler <Drill,Void> {

    @Resource
    private RemoteProxyService remoteProxyService;

    @Resource
    private EruptDao eruptDao;

    @Override
    @Transactional
    public String exec(List<Drill> data, Void unused, String[] param) {

        for (Drill drill:data) {
            drill.setSubmitted(true);
            drill.setUpdateTime(LocalDateTime.now());
            eruptDao.merge(drill);

            JSONObject inputData = new JSONObject();
            inputData.set("clazz", "Drill");
            inputData.set("insertData",drill);
            EruptRemoteUtils.doPost(remoteProxyService.getMainServerUrl() + "/erupt-api/exchange/data/exchange", inputData, Map.class);
        }
        return NotifyUtils.getSuccessNotify("上报成功！");
    }
}
