# DoubleCheckManagement

本文档用于说明 双倒查管理 的创建与更新。

# 1 创建 双倒查管理 对象

请求方式：POST<br>
请求地址：/erupt-api/open-api/data/DoubleCheckManagement

## 1.1 Body参数
参数说明：以JSON格式在Body体内提交

|  参数  |  类型  |  是否必填  |  说明  |  备注  |
| --- | --- | --- | --- | --- |
|  company  |  String  |  是  |  单位名称  |       |
|  inspectType  |  String  |  是  |  检查类型  |       |
|  troubleShootIssues  |  String  |  是  |  排查问题  |       |
|  personResponsible  |  String  |  是  |  排查责任人  |       |
|  assessmentInitiatives  |  String  |  是  |  考核举措  |       |
|  fillingDate  |  Date  |  否  |  上报时间  |       |
|  submitted  |  Boolean  |  否  |  上报状态  |       |
|  orgCode  |  String  |  否  |  组织编码  |       |
|  id  |  Long  |  否  |    |       |
## 1.2 返回值
返回值说明：以JSON格式返回

|  参数  |  类型  |  说明  |  备注  |
| --- | --- | --- | --- |
| status  | 状态   |  SUCCESS/ERROR |  |
| promptWay  | 类型   |  MESSAGE，表示返回类型为消息 |   |
| message  | 消息内容   |  报错信息（如果发生错误） |   |
| data  | 携带数据   |  请求返回的数据（如果有数据）|   |
| errorIntercept  | 保留字段   |    |   |
