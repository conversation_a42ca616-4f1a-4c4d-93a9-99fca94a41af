package com.daliangang.workbench.patch;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import xyz.erupt.devtools.patch.PlatformPatchHandler;
import xyz.erupt.jpa.dao.EruptDao;
import xyz.erupt.upms.model.EruptMenuModule;

import javax.annotation.Resource;
import javax.transaction.Transactional;

@Service
@Slf4j
public class ModuleIndexPatch implements PlatformPatchHandler {

    @Override
    public String getPatchId() {
        return "1.11.7.4-patch-04";
    }

    @Override
    public String getName() {
        return "切换模块首页补丁";
    }

    @Override
    public String getDesc() {
        return "1.添加切换模块的首页记录<br>";
    }

    @Resource
    private EruptDao eruptDao;


    @Override
    @Transactional
    public boolean execute() {
        String[] modules = new String[]{
                "safedaily", "device", "majorisk", "rndpub", "training", "emergency"
        };
        for (String moduleId : modules) {
            EruptMenuModule module = eruptDao.queryEntity(EruptMenuModule.class, "module_id='" + moduleId + "'");
            if (module != null) {
                module.setModuleIndexType("link");
                module.setModuleIndexPath("module-" + moduleId + ".html");
                eruptDao.merge(module);
            }
        }
        EruptMenuModule workbench = eruptDao.queryEntity(EruptMenuModule.class, "module_id='workbench'");
        workbench.setModuleIndexType("");
        workbench.setModuleIndexPath("");
        eruptDao.merge(workbench);
        return true;
    }

}
