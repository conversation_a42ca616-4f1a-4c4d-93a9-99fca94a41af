/*
 * Copyright © 2020-2035 yundingyun All rights reserved.
 */
package com.daliangang.emergency.proxy;

import cn.hutool.json.JSONObject;
import com.daliangang.emergency.entity.EmergencyDuty;
import com.daliangang.emergency.entity.EmergencyDutyPerson;
import org.springframework.stereotype.Service;
import xyz.erupt.annotation.fun.DataProxy;
import xyz.erupt.annotation.query.Condition;
import xyz.erupt.common.http.EruptRemoteUtils;
import xyz.erupt.core.util.EruptSpringUtil;
import xyz.erupt.jpa.dao.EruptDao;
import xyz.erupt.toolkit.db.EruptResultMap;
import xyz.erupt.toolkit.remote.RemoteProxyService;
import xyz.erupt.toolkit.utils.EruptDaoUtils;
import xyz.erupt.toolkit.utils.NotifyUtils;
import xyz.erupt.toolkit.utils.SqlUtils;
import xyz.erupt.upms.model.EruptUser;
import xyz.erupt.upms.service.EruptUserService;

import javax.annotation.Resource;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Set;

@Service
public class EmergencyDutyDataProxy implements DataProxy<EmergencyDuty> {

    @Resource
    private EruptDao eruptDao;
    @Resource
    private EruptUserService eruptUserService;
    @Resource
    private RemoteProxyService remoteProxyService;

    @Override
    public String beforeFetch(List<Condition> conditions) {
        //当前登录的用户
        EruptUser currentEruptUser = EruptSpringUtil.getBean(EruptUserService.class).getCurrentEruptUser();
        if (currentEruptUser!=null)
            return "company=" + SqlUtils.wrapStr(currentEruptUser.getEruptOrg().getCode());
        return null;
    }

    @Override
    public void afterFetch(Collection<Map<String, Object>> list) {
        List<EmergencyDuty> emergencyDuties = EruptDaoUtils.convert(list, EmergencyDuty.class);
        for (EmergencyDuty emergencyDuty : emergencyDuties) {
            String countSql="select count(*) as personNum from tb_emergency_duty_person where emergency_duty_id="+emergencyDuty.getId();
            EruptResultMap eruptResultMap = EruptDaoUtils.selectMap(countSql);
            String personNum = String.valueOf(eruptResultMap.get("personNum"));
            emergencyDuty.setPersonNum(personNum);
            EruptDaoUtils.updateAfterFetch(list, emergencyDuty.getId(), "personNum", emergencyDuty.getPersonNum());
        }
    }

    @Override
    public void addBehavior(EmergencyDuty emergencyDuty) {
        EruptUser currentEruptUser = eruptUserService.getCurrentEruptUser();
        emergencyDuty.setCompany(currentEruptUser.getEruptOrg().getCode());
        emergencyDuty.setEdit(true);
        String sql = "select * from tb_emergency_duty order by duty_id desc limit 1";
        EmergencyDuty duty = EruptDaoUtils.selectOne(sql, EmergencyDuty.class);
        emergencyDuty.setDutyId(duty != null ? (duty.getDutyId()) + 1 : 1);
    }

    @Override
    public void editBehavior(EmergencyDuty emergencyDuty) {
        emergencyDuty.setEdit(true);
    }

    @Override
    public void beforeAdd(EmergencyDuty emergencyDuty) {
        //当前登录的用户
        EruptUser currentEruptUser = EruptSpringUtil.getBean(EruptUserService.class).getCurrentEruptUser();
        if (currentEruptUser!=null){
            emergencyDuty.setCreateBy(currentEruptUser.getName());
        }
        for (EmergencyDutyPerson person : emergencyDuty.getEmergencyDutyPeople()) {
            person.setDutyTime(emergencyDuty.getDutyTime());
        }
        if(emergencyDuty.getDutyTime().compareTo(emergencyDuty.getEndDutyTime())==0){
            NotifyUtils.showErrorDialog("开始值守时间不能等于结束值守时间！");
        }else if(emergencyDuty.getDutyTime().compareTo(emergencyDuty.getEndDutyTime())>0){
            NotifyUtils.showErrorDialog("结束值守时间不能小于开始值守时间！");
        }

    }

    @Override
    public void beforeUpdate(EmergencyDuty emergencyDuty) {
        if(emergencyDuty.getDutyTime().compareTo(emergencyDuty.getEndDutyTime())==0){
            NotifyUtils.showErrorDialog("开始值守时间不能等于结束值守时间！");
        }else if(emergencyDuty.getDutyTime().compareTo(emergencyDuty.getEndDutyTime())>0){
            NotifyUtils.showErrorDialog("结束值守时间不能小于开始值守时间！");
        }
    }

    @Override
    public void afterAdd(EmergencyDuty emergencyDuty) {
        //推送数据
        JSONObject inputData = new JSONObject();
        inputData.set("clazz", "EmergencyDuty");
        inputData.set("insertData",emergencyDuty);
        EruptRemoteUtils.doPost(remoteProxyService.getMainServerUrl() + "/erupt-api/exchange/data/exchange", inputData, Map.class);
        Set<EmergencyDutyPerson> emergencyDutyPeople = emergencyDuty.getEmergencyDutyPeople();
        if(null != emergencyDutyPeople && 0 != emergencyDutyPeople.size() ){
            JSONObject personInputData = new JSONObject();
            personInputData.set("clazz", "EmergencyDutyPerson");
            personInputData.set("insertData",emergencyDutyPeople);
            EruptRemoteUtils.doPost(remoteProxyService.getMainServerUrl() + "/erupt-api/exchange/data/exchange", personInputData, Map.class);
        }
    }

    @Override
    public void afterUpdate(EmergencyDuty emergencyDuty) {
        //推送数据
        JSONObject inputData = new JSONObject();
        inputData.set("clazz", "EmergencyDuty");
        inputData.set("insertData",emergencyDuty);
        EruptRemoteUtils.doPost(remoteProxyService.getMainServerUrl() + "/erupt-api/exchange/data/exchange", inputData, Map.class);
        Set<EmergencyDutyPerson> emergencyDutyPeople = emergencyDuty.getEmergencyDutyPeople();
        if(null != emergencyDutyPeople && 0 != emergencyDutyPeople.size() ){
            JSONObject personInputData = new JSONObject();
            personInputData.set("clazz", "EmergencyDutyPerson");
            personInputData.set("insertData",emergencyDutyPeople);
            EruptRemoteUtils.doPost(remoteProxyService.getMainServerUrl() + "/erupt-api/exchange/data/exchange", personInputData, Map.class);
        }
    }
}
