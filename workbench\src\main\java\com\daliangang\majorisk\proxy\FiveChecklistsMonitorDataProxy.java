package com.daliangang.majorisk.proxy;

import com.daliangang.majorisk.entity.FiveChecklistsMonitor;
import com.daliangang.majorisk.form.CurrentJobForm;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.stereotype.Service;
import xyz.erupt.annotation.fun.DataProxy;
import xyz.erupt.core.module.MetaUserinfo;
import xyz.erupt.toolkit.remote.RemoteProxyService;
import xyz.erupt.toolkit.utils.EruptDaoUtils;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @since :2023/4/13:10:25
 */
@Service
public class FiveChecklistsMonitorDataProxy implements DataProxy<FiveChecklistsMonitor> {
    @Resource
    private RemoteProxyService remoteProxyService;
    @Override
    public void addBehavior(FiveChecklistsMonitor fiveChecklistsMonitor) {
        MetaUserinfo remoteUserInfo = remoteProxyService.getRemoteUserInfo();

        // 今日装卸船作业
        CurrentJobForm currentJobForm = EruptDaoUtils.selectOne("SELECT COUNT(*) as num,GROUP_CONCAT(address) as address  from tb_unload_ship where NOW() between ab_time and ae_time and org_code ='"+remoteUserInfo.getOrg()+"'", CurrentJobForm.class);
        if (ObjectUtils.isNotEmpty(currentJobForm)) {
            fiveChecklistsMonitor.setShipWorkNum(currentJobForm.getNum());
            fiveChecklistsMonitor.setShipWorkAddress(currentJobForm.getAddress());
        }
        // 今日装卸车/火车
        List<CurrentJobForm> currentJobFormCar = EruptDaoUtils.selectOnes("SELECT COUNT(*) as num,GROUP_CONCAT(address) as address ,work_type as type from tb_unload where NOW() between ab_time and ae_time and org_code ='"+remoteUserInfo.getOrg()+"' GROUP by work_type ", CurrentJobForm.class);
        if (ObjectUtils.isNotEmpty(currentJobFormCar)) {
            currentJobFormCar.forEach(v->{
                if (v.getType().equals("CAR")) {
                    fiveChecklistsMonitor.setCarWorkNum(v.getNum());
                    fiveChecklistsMonitor.setCarWorkAddress(v.getAddress());
                } else {
                    fiveChecklistsMonitor.setTrainWorkNum(v.getNum());
                    fiveChecklistsMonitor.setTrainWorkAddress(v.getAddress());
                }
            });
        }

        // 动火/受限
        List<CurrentJobForm> currentJobForms = EruptDaoUtils.selectOnes("SELECT COUNT(*) as num,type from tb_maintenance where  NOW() between star_date and end_date and org_code ='"+remoteUserInfo.getOrg()+"' GROUP by type ", CurrentJobForm.class);
        if (ObjectUtils.isNotEmpty(currentJobForms)) {
            currentJobForms.forEach(v->{
                if (v.getType().equals("DH")) {
                    fiveChecklistsMonitor.setFireWorkNum(v.getNum());
                } else {
                    fiveChecklistsMonitor.setRestrictedWorkNum(v.getNum());
                }
            });
        }

         StringBuffer stringBuffer = new StringBuffer();

        // 消防设施检测
        CurrentJobForm currentJobForm2 = EruptDaoUtils.selectOne("SELECT COUNT(*) as num  from tb_facility_inspection  where CURRENT_DATE() > validity_period and org_code ='"+remoteUserInfo.getOrg()+"'", CurrentJobForm.class);
        if (!currentJobForm2.getNum().equals("0")) {
            stringBuffer.append("消防设施已过期!");
        }
        // 防雷装置检测
        CurrentJobForm currentJobForm3 = EruptDaoUtils.selectOne("SELECT COUNT(*) as num  from tb_lightning_protection  where CURRENT_DATE() > validity_period and org_code ='"+remoteUserInfo.getOrg()+"'", CurrentJobForm.class);
        if (!currentJobForm3.getNum().equals("0")) {
            stringBuffer.append("防雷装置已过期!");
        }
        // 压力表检测
        CurrentJobForm currentJobForm4 = EruptDaoUtils.selectOne("SELECT COUNT(*) as num  from tb_pressure_gauge  where CURRENT_DATE() > validity_period and org_code ='"+remoteUserInfo.getOrg()+"'", CurrentJobForm.class);
        if (!currentJobForm4.getNum().equals("0")) {
            stringBuffer.append("压力表已过期!");
        }
        //安全阀检测
        CurrentJobForm currentJobForm5 = EruptDaoUtils.selectOne("SELECT COUNT(*) as num  from tb_safety_valve  where CURRENT_DATE() > validity_period and org_code ='"+remoteUserInfo.getOrg()+"'", CurrentJobForm.class);
        if (!currentJobForm5.getNum().equals("0")) {
            stringBuffer.append("安全阀已过期!");
        }
        //可燃气体报警检测
        CurrentJobForm currentJobForm6 = EruptDaoUtils.selectOne("SELECT COUNT(*) as num  from tb_combustible_gas  where CURRENT_DATE() > validity_period and org_code ='"+remoteUserInfo.getOrg()+"'", CurrentJobForm.class);
        if (!currentJobForm6.getNum().equals("0")) {
            stringBuffer.append("可燃气体报警已过期!");
        }
        if (stringBuffer.length() == 0) {
            stringBuffer.append("本企业港口设施维护管理系统中录入的设备设施均在检测有效期内");
        }
        fiveChecklistsMonitor.setDeviceStatus(String.valueOf(stringBuffer));
    }
}
