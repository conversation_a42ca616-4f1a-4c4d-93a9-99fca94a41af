-- 未执行
-- 上等保时，需要更改tb_department表的account（账号）字段
-- 把e_upms_option表里的LEVEL3_FLAG变成true，把主管部门的菜单放出来，并配置权限（只能有更新权限，不能有新增、删除、导入权限）
--  e_upms_user 用户的手机号要统一加一下
-- 生产环境需要加一下ERUPT_UPMS_EXPIRETIMEBYLOGIN变量
-- 先去掉员工信息菜单、首页
delete from e_upms_role_menu where menu_id in (select id as menuid from e_upms_menu where (code = '$home') or code = 'EmployeeSelfInfo') and role_id in (select id as roleid from e_upms_role where (code like '104.%' or code like'108.%' or code like'109.%' or code in('104', '108', '109') ))

-- 插入首页/员工信息
 insert into e_upms_role_menu(role_id,menu_id)
select roleid,menuid from
(select id as roleid from e_upms_role where (code like '104.%' or code like'108.%' or code like'109.%' or code in('104', '108', '109') )) role,
(select id as menuid from e_upms_menu where (code = '$home' or code = 'EmployeeSelfInfo')) menu
-- 隐藏掉员工信息菜单

--已执行
--更新bisql
UPDATE `e_bi_chart` SET   `sql_statement` = 'SELECT\r\n	t2.name \'检查名称\',\r\n	count( t1.inspection_name ) \'问题数量\',\r\n	count(\r\n	IF\r\n	( t1.inspection_result = \'BY\' and publish_status =1, TRUE, NULL )) \'已整改数量\',\r\n	count(\r\n	IF\r\n	( t1.inspection_result != \'BY\' and publish_status =1, TRUE, NULL )) \'未整改数量\',\r\n	count(\r\n	IF\r\n	( t1.deadline < now() and publish_status =1, TRUE, NULL )) \'逾期数量\' \r\nFROM\r\n	tb_inspection_results t1\r\n	RIGHT OUTER JOIN (\r\n	SELECT\r\n		id,\r\n	NAME \r\n	FROM\r\n		tb_procedure \r\n) t2 ON t1.inspection_name = t2.id\r\n	where YEAR ( t1.create_time )=\'#searchDate\'\r\n	and t1.check_object = \'#ORGCODE\'\r\nGROUP BY t2.`NAME`' WHERE `name` = '检查清单';
UPDATE `e_bi_chart` SET  `sql_statement` = 'SELECT\r\n	t1.searchCount,\r\nIF\r\n	( t1.searchCount > t2.searchBeforeCount, CONCAT( \'+\', t1.searchCount - t2.searchBeforeCount ), t1.searchCount - t2.searchBeforeCount ) compare \r\nFROM\r\n	( SELECT count( DISTINCT enterprise_name ) searchCount FROM tb_enterprise_information \r\n	 WHERE  \r\n	 state = 1 \r\n	 AND procedure_id in (select id from tb_procedure where YEAR (inspection_date )= \'#searchDate\' ) \r\n	 #REPLACESQL\r\n	) t1,\r\n	(\r\n	SELECT\r\n		count( DISTINCT enterprise_name ) searchBeforeCount \r\n	FROM\r\n		tb_enterprise_information \r\n	WHERE \r\n	\r\n		state = 1 \r\n		AND procedure_id in (select id from tb_procedure where YEAR ( inspection_date )=YEAR (\r\n		DATE_SUB( concat( \'#searchDate\', \'-01-01\' ), INTERVAL 1 YEAR )) )\r\n		#REPLACESQL	\r\n	) t2\r\n' WHERE  `name` = '检查企业数' ;

-- 子系统新加的首页不显示
UPDATE e_upms_menu_module  set module_index_path  = 'module-rndpub.html', module_index_type  = 'link' WHERE  module_id  = 'rndpub' ;
UPDATE e_upms_menu_module  set module_index_path  = '', module_index_type  = '' WHERE  module_id  = 'workbench' ;
UPDATE e_upms_menu_module  set module_index_path  = 'module-emergency.html', module_index_type  = 'link' WHERE  module_id  = 'emergency' ;
UPDATE e_upms_menu_module  set module_index_path  = 'module-majorisk.html', module_index_type  = 'link' WHERE  module_id  = 'majorisk' ;
UPDATE e_upms_menu_module  set module_index_path  = 'module-training.html', module_index_type  = 'link' WHERE  module_id  = 'training' ;
UPDATE e_upms_menu_module  set module_index_path  = 'module-safedaily.html', module_index_type  = 'link' WHERE  module_id  = 'safedaily' ;
UPDATE e_upms_menu_module  set module_index_path  = 'module-device.html', module_index_type  = 'link' WHERE  module_id  = 'device' ;

--daliangang 库  2023-8-22
-- 危险货物港区重大安全风险管控平台 改为 基础信息管理
update e_upms_menu_module set module_name = '基础信息管理' where module_id = 'workbench' ;

-- daliangang 库 加上企业名称搜索框
update tb_check_fill set company = org_code;
update tb_report set company = org_code;

-- daliangang 库：2023-8-18号 新增批量推送数据
INSERT INTO `daliangang`.`e_job`( `create_by`, `create_time`, `update_by`, `update_time`, `code`, `cron`, `handler`, `handler_param`, `name`, `notify_emails`, `remark`, `status`) VALUES ( '系统', '2023-08-17 09:59:29', '系统', '2023-08-18 10:09:16', 'xMWRr433', '无', 'com.daliangang.workbench.service.InitExchangeDataService', '', '推送大数据平台——初始化', NULL, '任务参数可填写单个同步的实体类，若填了，则同步单个实体类，若不填，则同步全部的类。因为可能全部同步时，单个实体类同步失败，所以需要单个实体类再同步一次', 0);

-- daliangnag 库：2023-8-11号 修复标准化第二年没数据的问题
insert into tb_standardization_second(id,
                                      create_by,
                                      create_time,
                                      org_code,
                                      update_by,
                                      update_time,
                                      company,
                                      effective_date,
                                      final_score,
                                      gate_score,
                                      grade,
                                      issue_date,
                                      most_total_score,
                                      other_file,
                                      self_evaluation,
                                      standard_file,
                                      state,
                                      submitted,
                                      year,
                                      tb_standardization_id
)
select null,create_by,create_time,org_code,
       update_by,update_time,company,effective_date,
       final_score, gate_score,grade,issue_date ,
       most_total_score,other_file,self_evaluation,standard_file,state,0,(year + 1),id
from  tb_standardization
where submitted =1 and id not in (select tb_standardization_id from tb_standardization_second );


insert into tb_standardization_score_second
(id,
 score,
 score_proportion,
 score_standard,
 series_id,
 standard,
 tandards_score,
 standard_id
)
select null,score.score,score_proportion,score_standard,series_id,standard,tandards_score,secondstandard.id from  tb_standardization_score score,tb_standardization_second secondstandard,tb_standardization standard where secondstandard.tb_standardization_id = standard.id and score.standard_id = standard.id and standard.submitted =1 and secondstandard.id not in (
    select standard_id from tb_standardization_score_second
);
-- 查询修复是否成功
select * from tb_standardization where submitted =1;

select * from tb_standardization_second;

select * from tb_standardization_score where standard_id in (select id from tb_standardization where submitted =1 );

select * from tb_standardization_score_second where standard_id in (select id from tb_standardization_second  );


-- 2023-8-9号：
INSERT INTO `e_job`(`id`, `create_by`, `create_time`, `update_by`, `update_time`, `code`, `cron`, `handler`, `handler_param`, `name`, `notify_emails`, `remark`, `status`) VALUES (9, '系统', '2023-08-08 17:52:39', '系统', '2023-08-08 17:50:53', 'Ea7uwNKT', '0 0/20 * * * ? ', 'com.daliangang.workbench.service.ExchangeDataTokenService', NULL, '推送大数据平台——获取token', NULL, NULL, b'1');
INSERT INTO `e_job`(`id`, `create_by`, `create_time`, `update_by`, `update_time`, `code`, `cron`, `handler`, `handler_param`, `name`, `notify_emails`, `remark`, `status`) VALUES (10, '系统', '2023-08-08 17:53:41', '系统', '2023-08-09 09:31:48', 'RrMF6XUN', '0 0/1 * * * ? *', 'com.daliangang.workbench.controller.ExchangeDataController', NULL, '推送大数据平台', NULL, NULL, b'1');









daliangang 库：
alter table tb_design modify column file_upload longtext;


update tb_emergency_duty set org_code = 'f6336f73' where create_by ='国家管网集团大连液化天然气有限公司' ;
update tb_emergency_duty set org_code = '9ebd7047' where create_by ='中国石油天然气股份有限公司大连石化分公司（码头）' ;

-- 更新专家链接
update e_upms_option set param = 'http://120.201.246.77:30080/expert-application/index.html' where code = 'EXPERT_APPLY_QRCODE';

update tb_employee_information set job_title = replace( job_title,'2ba184e1',org_code) where locate('.' ,job_title) >0 and locate(org_code ,job_title) <=0  ;

UPDATE `e_bi_chart`  set `sql_statement` = 'SELECT\r\n	ifnull(searchCount,0) searchCount,\r\nifnull(IF\r\n	( searchCount > searchBeforeCount, CONCAT( \'+\', searchCount - searchBeforeCount ), searchCount - searchBeforeCount ) ,0)compare \r\nFROM\r\n	( SELECT sum(  number_of_enterprises ) AS searchCount FROM tb_procedure WHERE YEAR ( inspection_date )= \'#searchDate\' \r\n #REPLACESQL \r\n) AS t1,\r\n(SELECT\r\n	ifnull(sum(  number_of_enterprises ),0) AS searchBeforeCount \r\nFROM\r\n	tb_procedure \r\nWHERE\r\n	YEAR ( inspection_date ) = YEAR (\r\n	DATE_SUB( concat( \'#searchDate\' , \'-01-01\' ), INTERVAL 1 YEAR )) \r\n #REPLACESQL \r\n) t2' WHERE `id` = 16;


tkaq_test 库：
delete from t_courseware where url like '/initCourseware%';
set names gbk ;

INSERT INTO `t_courseware` (`organ_id`, `tag_id`, `name`, `video_name`, `video_id`, `duration`, `video_status`, `file_type`, `url`,`expert_id`, `period`, `status`, `source`, `remark`, `start_time`, `end_time`, `submit_time`,`create_time`, `update_time`, `create_user`, `update_user`, `videosize`, `times`, `cancel`, `taster` )VALUES('2573', '213', 'k1-学习贯彻习近平总书记关于安全生产重要论述1-杨尊昭', 'k1-学习贯彻习近平总书记关于安全生产重要论述1-杨尊昭.mp4', NULL, '2507',0, 'VIDEO', '/initCourseware/k1-学习贯彻习近平总书记关于安全生产重要论述1-杨尊昭.mp4', NULL, NULL,3, 0, NULL, '2023-08-05 21:49:11', '3000-01-01 00:00:00', '2023-08-05 21:49:11', '2023-08-05 21:49:11', '2023-08-05 21:49:11', 7578, 7578, '79277369', 0, 0, 0 );
INSERT INTO `t_courseware` (`organ_id`, `tag_id`, `name`, `video_name`, `video_id`, `duration`, `video_status`, `file_type`, `url`,`expert_id`, `period`, `status`, `source`, `remark`, `start_time`, `end_time`, `submit_time`,`create_time`, `update_time`, `create_user`, `update_user`, `videosize`, `times`, `cancel`, `taster` )VALUES('2573', '213', 'k10-企业安全生产主体责任3-徐凤霞', 'k10-企业安全生产主体责任3-徐凤霞.mp4', NULL, '728',0, 'VIDEO', '/initCourseware/k10-企业安全生产主体责任3-徐凤霞.mp4', NULL, NULL,3, 0, NULL, '2023-08-05 21:49:11', '3000-01-01 00:00:00', '2023-08-05 21:49:11', '2023-08-05 21:49:11', '2023-08-05 21:49:11', 7578, 7578, '456690907', 0, 0, 0 );
INSERT INTO `t_courseware` (`organ_id`, `tag_id`, `name`, `video_name`, `video_id`, `duration`, `video_status`, `file_type`, `url`,`expert_id`, `period`, `status`, `source`, `remark`, `start_time`, `end_time`, `submit_time`,`create_time`, `update_time`, `create_user`, `update_user`, `videosize`, `times`, `cancel`, `taster` )VALUES('2573', '213', 'k11-企业安全生产主体责任4-徐凤霞', 'k11-企业安全生产主体责任4-徐凤霞.mp4', NULL, '7892',0, 'VIDEO', '/initCourseware/k11-企业安全生产主体责任4-徐凤霞.mp4', NULL, NULL,3, 0, NULL, '2023-08-05 21:49:11', '3000-01-01 00:00:00', '2023-08-05 21:49:11', '2023-08-05 21:49:11', '2023-08-05 21:49:11', 7578, 7578, '2147483647', 0, 0, 0 );
INSERT INTO `t_courseware` (`organ_id`, `tag_id`, `name`, `video_name`, `video_id`, `duration`, `video_status`, `file_type`, `url`,`expert_id`, `period`, `status`, `source`, `remark`, `start_time`, `end_time`, `submit_time`,`create_time`, `update_time`, `create_user`, `update_user`, `videosize`, `times`, `cancel`, `taster` )VALUES('2573', '213', 'k12-企业安全生产主体责任5-徐凤霞', 'k12-企业安全生产主体责任5-徐凤霞.mp4', NULL, '668',0, 'VIDEO', '/initCourseware/k12-企业安全生产主体责任5-徐凤霞.mp4', NULL, NULL,3, 0, NULL, '2023-08-05 21:49:11', '3000-01-01 00:00:00', '2023-08-05 21:49:11', '2023-08-05 21:49:11', '2023-08-05 21:49:11', 7578, 7578, '295965386', 0, 0, 0 );
INSERT INTO `t_courseware` (`organ_id`, `tag_id`, `name`, `video_name`, `video_id`, `duration`, `video_status`, `file_type`, `url`,`expert_id`, `period`, `status`, `source`, `remark`, `start_time`, `end_time`, `submit_time`,`create_time`, `update_time`, `create_user`, `update_user`, `videosize`, `times`, `cancel`, `taster` )VALUES('2573', '213', 'k13-重大危险源辨识-房慧辰', 'k13-重大危险源辨识-房慧辰.mp4', NULL, '4347',0, 'VIDEO', '/initCourseware/k13-重大危险源辨识-房慧辰.mp4', NULL, NULL,3, 0, NULL, '2023-08-05 21:49:11', '3000-01-01 00:00:00', '2023-08-05 21:49:11', '2023-08-05 21:49:11', '2023-08-05 21:49:11', 7578, 7578, '1220893989', 0, 0, 0 );
INSERT INTO `t_courseware` (`organ_id`, `tag_id`, `name`, `video_name`, `video_id`, `duration`, `video_status`, `file_type`, `url`,`expert_id`, `period`, `status`, `source`, `remark`, `start_time`, `end_time`, `submit_time`,`create_time`, `update_time`, `create_user`, `update_user`, `videosize`, `times`, `cancel`, `taster` )VALUES('2573', '213', 'k14-安全生产标准化建设-柴劲', 'k14-安全生产标准化建设-柴劲.mp4', NULL, '4367',0, 'VIDEO', '/initCourseware/k14-安全生产标准化建设-柴劲.mp4', NULL, NULL,3, 0, NULL, '2023-08-05 21:49:11', '3000-01-01 00:00:00', '2023-08-05 21:49:11', '2023-08-05 21:49:11', '2023-08-05 21:49:11', 7578, 7578, '1265646795', 0, 0, 0 );
INSERT INTO `t_courseware` (`organ_id`, `tag_id`, `name`, `video_name`, `video_id`, `duration`, `video_status`, `file_type`, `url`,`expert_id`, `period`, `status`, `source`, `remark`, `start_time`, `end_time`, `submit_time`,`create_time`, `update_time`, `create_user`, `update_user`, `videosize`, `times`, `cancel`, `taster` )VALUES('2573', '213', 'k15-企业如何加强分包过程中的安全管理-刘先锋', 'k15-企业如何加强分包过程中的安全管理-刘先锋.mp4', NULL, '3516',0, 'VIDEO', '/initCourseware/k15-企业如何加强分包过程中的安全管理-刘先锋.mp4', NULL, NULL,3, 0, NULL, '2023-08-05 21:49:11', '3000-01-01 00:00:00', '2023-08-05 21:49:11', '2023-08-05 21:49:11', '2023-08-05 21:49:11', 7578, 7578, '866039685', 0, 0, 0 );
INSERT INTO `t_courseware` (`organ_id`, `tag_id`, `name`, `video_name`, `video_id`, `duration`, `video_status`, `file_type`, `url`,`expert_id`, `period`, `status`, `source`, `remark`, `start_time`, `end_time`, `submit_time`,`create_time`, `update_time`, `create_user`, `update_user`, `videosize`, `times`, `cancel`, `taster` )VALUES('2573', '213', 'k16-新安全生产法解读1-徐凤霞', 'k16-新安全生产法解读1-徐凤霞.mp4', NULL, '3364',0, 'VIDEO', '/initCourseware/k16-新安全生产法解读1-徐凤霞.mp4', NULL, NULL,3, 0, NULL, '2023-08-05 21:49:11', '3000-01-01 00:00:00', '2023-08-05 21:49:11', '2023-08-05 21:49:11', '2023-08-05 21:49:11', 7578, 7578, '1657538862', 0, 0, 0 );
INSERT INTO `t_courseware` (`organ_id`, `tag_id`, `name`, `video_name`, `video_id`, `duration`, `video_status`, `file_type`, `url`,`expert_id`, `period`, `status`, `source`, `remark`, `start_time`, `end_time`, `submit_time`,`create_time`, `update_time`, `create_user`, `update_user`, `videosize`, `times`, `cancel`, `taster` )VALUES('2573', '213', 'k17-新安全生产法解读2-徐凤霞', 'k17-新安全生产法解读2-徐凤霞.mp4', NULL, '2091',0, 'VIDEO', '/initCourseware/k17-新安全生产法解读2-徐凤霞.mp4', NULL, NULL,3, 0, NULL, '2023-08-05 21:49:11', '3000-01-01 00:00:00', '2023-08-05 21:49:11', '2023-08-05 21:49:11', '2023-08-05 21:49:11', 7578, 7578, '1017493069', 0, 0, 0 );
INSERT INTO `t_courseware` (`organ_id`, `tag_id`, `name`, `video_name`, `video_id`, `duration`, `video_status`, `file_type`, `url`,`expert_id`, `period`, `status`, `source`, `remark`, `start_time`, `end_time`, `submit_time`,`create_time`, `update_time`, `create_user`, `update_user`, `videosize`, `times`, `cancel`, `taster` )VALUES('2573', '213', 'k18-消防安全技能-刘先锋', 'k18-消防安全技能-刘先锋.mp4', NULL, '2503',0, 'VIDEO', '/initCourseware/k18-消防安全技能-刘先锋.mp4', NULL, NULL,3, 0, NULL, '2023-08-05 21:49:11', '3000-01-01 00:00:00', '2023-08-05 21:49:11', '2023-08-05 21:49:11', '2023-08-05 21:49:11', 7578, 7578, '662510872', 0, 0, 0 );
INSERT INTO `t_courseware` (`organ_id`, `tag_id`, `name`, `video_name`, `video_id`, `duration`, `video_status`, `file_type`, `url`,`expert_id`, `period`, `status`, `source`, `remark`, `start_time`, `end_time`, `submit_time`,`create_time`, `update_time`, `create_user`, `update_user`, `videosize`, `times`, `cancel`, `taster` )VALUES('2573', '213', 'k19-职业病危害因素及危害特性-张奇', 'k19-职业病危害因素及危害特性-张奇.mp4', NULL, '7280',0, 'VIDEO', '/initCourseware/k19-职业病危害因素及危害特性-张奇.mp4', NULL, NULL,3, 0, NULL, '2023-08-05 21:49:11', '3000-01-01 00:00:00', '2023-08-05 21:49:11', '2023-08-05 21:49:11', '2023-08-05 21:49:11', 7578, 7578, '2147483647', 0, 0, 0 );
INSERT INTO `t_courseware` (`organ_id`, `tag_id`, `name`, `video_name`, `video_id`, `duration`, `video_status`, `file_type`, `url`,`expert_id`, `period`, `status`, `source`, `remark`, `start_time`, `end_time`, `submit_time`,`create_time`, `update_time`, `create_user`, `update_user`, `videosize`, `times`, `cancel`, `taster` )VALUES('2573', '213', 'k2-学习贯彻习近平总书记关于安全生产重要论述2-杨尊昭', 'k2-学习贯彻习近平总书记关于安全生产重要论述2-杨尊昭.mp4', NULL, '2572',0, 'VIDEO', '/initCourseware/k2-学习贯彻习近平总书记关于安全生产重要论述2-杨尊昭.mp4', NULL, NULL,3, 0, NULL, '2023-08-05 21:49:11', '3000-01-01 00:00:00', '2023-08-05 21:49:11', '2023-08-05 21:49:11', '2023-08-05 21:49:11', 7578, 7578, '73766871', 0, 0, 0 );
INSERT INTO `t_courseware` (`organ_id`, `tag_id`, `name`, `video_name`, `video_id`, `duration`, `video_status`, `file_type`, `url`,`expert_id`, `period`, `status`, `source`, `remark`, `start_time`, `end_time`, `submit_time`,`create_time`, `update_time`, `create_user`, `update_user`, `videosize`, `times`, `cancel`, `taster` )VALUES('2573', '213', 'k20-特种设备安全技术-李瑞', 'k20-特种设备安全技术-李瑞.mp4', NULL, '3261',0, 'VIDEO', '/initCourseware/k20-特种设备安全技术-李瑞.mp4', NULL, NULL,3, 0, NULL, '2023-08-05 21:49:11', '3000-01-01 00:00:00', '2023-08-05 21:49:11', '2023-08-05 21:49:11', '2023-08-05 21:49:11', 7578, 7578, '583287090', 0, 0, 0 );
INSERT INTO `t_courseware` (`organ_id`, `tag_id`, `name`, `video_name`, `video_id`, `duration`, `video_status`, `file_type`, `url`,`expert_id`, `period`, `status`, `source`, `remark`, `start_time`, `end_time`, `submit_time`,`create_time`, `update_time`, `create_user`, `update_user`, `videosize`, `times`, `cancel`, `taster` )VALUES('2573', '213', 'k21-动火作业安全技术-张爱利', 'k21-动火作业安全技术-张爱利.mp4', NULL, '4405',0, 'VIDEO', '/initCourseware/k21-动火作业安全技术-张爱利.mp4', NULL, NULL,3, 0, NULL, '2023-08-05 21:49:11', '3000-01-01 00:00:00', '2023-08-05 21:49:11', '2023-08-05 21:49:11', '2023-08-05 21:49:11', 7578, 7578, '1526797906', 0, 0, 0 );
INSERT INTO `t_courseware` (`organ_id`, `tag_id`, `name`, `video_name`, `video_id`, `duration`, `video_status`, `file_type`, `url`,`expert_id`, `period`, `status`, `source`, `remark`, `start_time`, `end_time`, `submit_time`,`create_time`, `update_time`, `create_user`, `update_user`, `videosize`, `times`, `cancel`, `taster` )VALUES('2573', '213', 'k22-特殊工种( 电工)安全培训-宋志坚', 'k22-特殊工种( 电工)安全培训-宋志坚.mp4', NULL, '5296',0, 'VIDEO', '/initCourseware/k22-特殊工种( 电工)安全培训-宋志坚.mp4', NULL, NULL,3, 0, NULL, '2023-08-05 21:49:11', '3000-01-01 00:00:00', '2023-08-05 21:49:11', '2023-08-05 21:49:11', '2023-08-05 21:49:11', 7578, 7578, '1774896330', 0, 0, 0 );
INSERT INTO `t_courseware` (`organ_id`, `tag_id`, `name`, `video_name`, `video_id`, `duration`, `video_status`, `file_type`, `url`,`expert_id`, `period`, `status`, `source`, `remark`, `start_time`, `end_time`, `submit_time`,`create_time`, `update_time`, `create_user`, `update_user`, `videosize`, `times`, `cancel`, `taster` )VALUES('2573', '213', 'k23-特殊工种(焊工)安全培训-康连有', 'k23-特殊工种(焊工)安全培训-康连有.mp4', NULL, '6567',0, 'VIDEO', '/initCourseware/k23-特殊工种(焊工)安全培训-康连有.mp4', NULL, NULL,3, 0, NULL, '2023-08-05 21:49:11', '3000-01-01 00:00:00', '2023-08-05 21:49:11', '2023-08-05 21:49:11', '2023-08-05 21:49:11', 7578, 7578, '2147483647', 0, 0, 0 );
INSERT INTO `t_courseware` (`organ_id`, `tag_id`, `name`, `video_name`, `video_id`, `duration`, `video_status`, `file_type`, `url`,`expert_id`, `period`, `status`, `source`, `remark`, `start_time`, `end_time`, `submit_time`,`create_time`, `update_time`, `create_user`, `update_user`, `videosize`, `times`, `cancel`, `taster` )VALUES('2573', '213', 'k24-受限空间安全管理-李立新', 'k24-受限空间安全管理-李立新.mp4', NULL, '3958',0, 'VIDEO', '/initCourseware/k24-受限空间安全管理-李立新.mp4', NULL, NULL,3, 0, NULL, '2023-08-05 21:49:11', '3000-01-01 00:00:00', '2023-08-05 21:49:11', '2023-08-05 21:49:11', '2023-08-05 21:49:11', 7578, 7578, '1386704638', 0, 0, 0 );
INSERT INTO `t_courseware` (`organ_id`, `tag_id`, `name`, `video_name`, `video_id`, `duration`, `video_status`, `file_type`, `url`,`expert_id`, `period`, `status`, `source`, `remark`, `start_time`, `end_time`, `submit_time`,`create_time`, `update_time`, `create_user`, `update_user`, `videosize`, `times`, `cancel`, `taster` )VALUES('2573', '213', 'k25-消防安全基础知识-刘先锋', 'k25-消防安全基础知识-刘先锋.mp4', NULL, '662',0, 'VIDEO', '/initCourseware/k25-消防安全基础知识-刘先锋.mp4', NULL, NULL,3, 0, NULL, '2023-08-05 21:49:11', '3000-01-01 00:00:00', '2023-08-05 21:49:11', '2023-08-05 21:49:11', '2023-08-05 21:49:11', 7578, 7578, '186465866', 0, 0, 0 );
INSERT INTO `t_courseware` (`organ_id`, `tag_id`, `name`, `video_name`, `video_id`, `duration`, `video_status`, `file_type`, `url`,`expert_id`, `period`, `status`, `source`, `remark`, `start_time`, `end_time`, `submit_time`,`create_time`, `update_time`, `create_user`, `update_user`, `videosize`, `times`, `cancel`, `taster` )VALUES('2573', '213', 'k26-安全生产十五条硬措施-徐凤霞', 'k26-安全生产十五条硬措施-徐凤霞.mp4', NULL, '4100',0, 'VIDEO', '/initCourseware/k26-安全生产十五条硬措施-徐凤霞.mp4', NULL, NULL,3, 0, NULL, '2023-08-05 21:49:11', '3000-01-01 00:00:00', '2023-08-05 21:49:11', '2023-08-05 21:49:11', '2023-08-05 21:49:11', 7578, 7578, '1526303802', 0, 0, 0 );
INSERT INTO `t_courseware` (`organ_id`, `tag_id`, `name`, `video_name`, `video_id`, `duration`, `video_status`, `file_type`, `url`,`expert_id`, `period`, `status`, `source`, `remark`, `start_time`, `end_time`, `submit_time`,`create_time`, `update_time`, `create_user`, `update_user`, `videosize`, `times`, `cancel`, `taster` )VALUES('2573', '213', 'k27-现代安全管理理论及实践1-胡艳华', 'k27-现代安全管理理论及实践1-胡艳华.mp4', NULL, '3152',0, 'VIDEO', '/initCourseware/k27-现代安全管理理论及实践1-胡艳华.mp4', NULL, NULL,3, 0, NULL, '2023-08-05 21:49:11', '3000-01-01 00:00:00', '2023-08-05 21:49:11', '2023-08-05 21:49:11', '2023-08-05 21:49:11', 7578, 7578, '1020334767', 0, 0, 0 );
INSERT INTO `t_courseware` (`organ_id`, `tag_id`, `name`, `video_name`, `video_id`, `duration`, `video_status`, `file_type`, `url`,`expert_id`, `period`, `status`, `source`, `remark`, `start_time`, `end_time`, `submit_time`,`create_time`, `update_time`, `create_user`, `update_user`, `videosize`, `times`, `cancel`, `taster` )VALUES('2573', '213', 'k28-现代安全管理理论及实践2-胡艳华', 'k28-现代安全管理理论及实践2-胡艳华.mp4', NULL, '2232',0, 'VIDEO', '/initCourseware/k28-现代安全管理理论及实践2-胡艳华.mp4', NULL, NULL,3, 0, NULL, '2023-08-05 21:49:11', '3000-01-01 00:00:00', '2023-08-05 21:49:11', '2023-08-05 21:49:11', '2023-08-05 21:49:11', 7578, 7578, '777657077', 0, 0, 0 );
INSERT INTO `t_courseware` (`organ_id`, `tag_id`, `name`, `video_name`, `video_id`, `duration`, `video_status`, `file_type`, `url`,`expert_id`, `period`, `status`, `source`, `remark`, `start_time`, `end_time`, `submit_time`,`create_time`, `update_time`, `create_user`, `update_user`, `videosize`, `times`, `cancel`, `taster` )VALUES('2573', '213', 'k29-安全生产技术相关知识1-王绪亭', 'k29-安全生产技术相关知识1-王绪亭.mp4', NULL, '3249',0, 'VIDEO', '/initCourseware/k29-安全生产技术相关知识1-王绪亭.mp4', NULL, NULL,3, 0, NULL, '2023-08-05 21:49:11', '3000-01-01 00:00:00', '2023-08-05 21:49:11', '2023-08-05 21:49:11', '2023-08-05 21:49:11', 7578, 7578, '1409755022', 0, 0, 0 );
INSERT INTO `t_courseware` (`organ_id`, `tag_id`, `name`, `video_name`, `video_id`, `duration`, `video_status`, `file_type`, `url`,`expert_id`, `period`, `status`, `source`, `remark`, `start_time`, `end_time`, `submit_time`,`create_time`, `update_time`, `create_user`, `update_user`, `videosize`, `times`, `cancel`, `taster` )VALUES('2573', '213', 'k3-学习贯彻习近平总书记关于安全生产重要论述3-杨尊昭', 'k3-学习贯彻习近平总书记关于安全生产重要论述3-杨尊昭.mp4', NULL, '2307',0, 'VIDEO', '/initCourseware/k3-学习贯彻习近平总书记关于安全生产重要论述3-杨尊昭.mp4', NULL, NULL,3, 0, NULL, '2023-08-05 21:49:11', '3000-01-01 00:00:00', '2023-08-05 21:49:11', '2023-08-05 21:49:11', '2023-08-05 21:49:11', 7578, 7578, '66370456', 0, 0, 0 );
INSERT INTO `t_courseware` (`organ_id`, `tag_id`, `name`, `video_name`, `video_id`, `duration`, `video_status`, `file_type`, `url`,`expert_id`, `period`, `status`, `source`, `remark`, `start_time`, `end_time`, `submit_time`,`create_time`, `update_time`, `create_user`, `update_user`, `videosize`, `times`, `cancel`, `taster` )VALUES('2573', '213', 'k30-安全生产技术相关知识2-王绪亭', 'k30-安全生产技术相关知识2-王绪亭.mp4', NULL, '4226',0, 'VIDEO', '/initCourseware/k30-安全生产技术相关知识2-王绪亭.mp4', NULL, NULL,3, 0, NULL, '2023-08-05 21:49:11', '3000-01-01 00:00:00', '2023-08-05 21:49:11', '2023-08-05 21:49:11', '2023-08-05 21:49:11', 7578, 7578, '1796673710', 0, 0, 0 );
INSERT INTO `t_courseware` (`organ_id`, `tag_id`, `name`, `video_name`, `video_id`, `duration`, `video_status`, `file_type`, `url`,`expert_id`, `period`, `status`, `source`, `remark`, `start_time`, `end_time`, `submit_time`,`create_time`, `update_time`, `create_user`, `update_user`, `videosize`, `times`, `cancel`, `taster` )VALUES('2573', '213', 'k31-安全生产技术相关知识3-王绪亭', 'k31-安全生产技术相关知识3-王绪亭.mp4', NULL, '3138',0, 'VIDEO', '/initCourseware/k31-安全生产技术相关知识3-王绪亭.mp4', NULL, NULL,3, 0, NULL, '2023-08-05 21:49:11', '3000-01-01 00:00:00', '2023-08-05 21:49:11', '2023-08-05 21:49:11', '2023-08-05 21:49:11', 7578, 7578, '1387299751', 0, 0, 0 );
INSERT INTO `t_courseware` (`organ_id`, `tag_id`, `name`, `video_name`, `video_id`, `duration`, `video_status`, `file_type`, `url`,`expert_id`, `period`, `status`, `source`, `remark`, `start_time`, `end_time`, `submit_time`,`create_time`, `update_time`, `create_user`, `update_user`, `videosize`, `times`, `cancel`, `taster` )VALUES('2573', '213', 'k32-安全生产技术相关知识4-王绪亭', 'k32-安全生产技术相关知识4-王绪亭.mp4', NULL, '2408',0, 'VIDEO', '/initCourseware/k32-安全生产技术相关知识4-王绪亭.mp4', NULL, NULL,3, 0, NULL, '2023-08-05 21:49:11', '3000-01-01 00:00:00', '2023-08-05 21:49:11', '2023-08-05 21:49:11', '2023-08-05 21:49:11', 7578, 7578, '1185923692', 0, 0, 0 );
INSERT INTO `t_courseware` (`organ_id`, `tag_id`, `name`, `video_name`, `video_id`, `duration`, `video_status`, `file_type`, `url`,`expert_id`, `period`, `status`, `source`, `remark`, `start_time`, `end_time`, `submit_time`,`create_time`, `update_time`, `create_user`, `update_user`, `videosize`, `times`, `cancel`, `taster` )VALUES('2573', '213', 'k34-重大危险源管控-邬长城', 'k34-重大危险源管控-邬长城.mp4', NULL, '2650',0, 'VIDEO', '/initCourseware/k34-重大危险源管控-邬长城.mp4', NULL, NULL,3, 0, NULL, '2023-08-05 21:49:11', '3000-01-01 00:00:00', '2023-08-05 21:49:11', '2023-08-05 21:49:11', '2023-08-05 21:49:11', 7578, 7578, '649198283', 0, 0, 0 );
INSERT INTO `t_courseware` (`organ_id`, `tag_id`, `name`, `video_name`, `video_id`, `duration`, `video_status`, `file_type`, `url`,`expert_id`, `period`, `status`, `source`, `remark`, `start_time`, `end_time`, `submit_time`,`create_time`, `update_time`, `create_user`, `update_user`, `videosize`, `times`, `cancel`, `taster` )VALUES('2573', '213', 'k35-重大危险源评估-邬长城', 'k35-重大危险源评估-邬长城.mp4', NULL, '2527',0, 'VIDEO', '/initCourseware/k35-重大危险源评估-邬长城.mp4', NULL, NULL,3, 0, NULL, '2023-08-05 21:49:11', '3000-01-01 00:00:00', '2023-08-05 21:49:11', '2023-08-05 21:49:11', '2023-08-05 21:49:11', 7578, 7578, '642925079', 0, 0, 0 );
INSERT INTO `t_courseware` (`organ_id`, `tag_id`, `name`, `video_name`, `video_id`, `duration`, `video_status`, `file_type`, `url`,`expert_id`, `period`, `status`, `source`, `remark`, `start_time`, `end_time`, `submit_time`,`create_time`, `update_time`, `create_user`, `update_user`, `videosize`, `times`, `cancel`, `taster` )VALUES('2573', '213', 'k36-典型事故案例分析1-邬长城', 'k36-典型事故案例分析1-邬长城.mp4', NULL, '3912',0, 'VIDEO', '/initCourseware/k36-典型事故案例分析1-邬长城.mp4', NULL, NULL,3, 0, NULL, '2023-08-05 21:49:11', '3000-01-01 00:00:00', '2023-08-05 21:49:11', '2023-08-05 21:49:11', '2023-08-05 21:49:11', 7578, 7578, '917738540', 0, 0, 0 );
INSERT INTO `t_courseware` (`organ_id`, `tag_id`, `name`, `video_name`, `video_id`, `duration`, `video_status`, `file_type`, `url`,`expert_id`, `period`, `status`, `source`, `remark`, `start_time`, `end_time`, `submit_time`,`create_time`, `update_time`, `create_user`, `update_user`, `videosize`, `times`, `cancel`, `taster` )VALUES('2573', '213', 'k37-典型事故案例分析2-邬长城', 'k37-典型事故案例分析2-邬长城.mp4', NULL, '1789',0, 'VIDEO', '/initCourseware/k37-典型事故案例分析2-邬长城.mp4', NULL, NULL,3, 0, NULL, '2023-08-05 21:49:11', '3000-01-01 00:00:00', '2023-08-05 21:49:11', '2023-08-05 21:49:11', '2023-08-05 21:49:11', 7578, 7578, '432770321', 0, 0, 0 );
INSERT INTO `t_courseware` (`organ_id`, `tag_id`, `name`, `video_name`, `video_id`, `duration`, `video_status`, `file_type`, `url`,`expert_id`, `period`, `status`, `source`, `remark`, `start_time`, `end_time`, `submit_time`,`create_time`, `update_time`, `create_user`, `update_user`, `videosize`, `times`, `cancel`, `taster` )VALUES('2573', '213', 'k38-安全生产典型事故案例-刘养红', 'k38-安全生产典型事故案例-刘养红.mp4', NULL, '6666',0, 'VIDEO', '/initCourseware/k38-安全生产典型事故案例-刘养红.mp4', NULL, NULL,3, 0, NULL, '2023-08-05 21:49:11', '3000-01-01 00:00:00', '2023-08-05 21:49:11', '2023-08-05 21:49:11', '2023-08-05 21:49:11', 7578, 7578, '2147483647', 0, 0, 0 );
INSERT INTO `t_courseware` (`organ_id`, `tag_id`, `name`, `video_name`, `video_id`, `duration`, `video_status`, `file_type`, `url`,`expert_id`, `period`, `status`, `source`, `remark`, `start_time`, `end_time`, `submit_time`,`create_time`, `update_time`, `create_user`, `update_user`, `videosize`, `times`, `cancel`, `taster` )VALUES('2573', '213', 'k39-国内外先进安全管理经验-胡艳华', 'k39-国内外先进安全管理经验-胡艳华.mp4', NULL, '2539',0, 'VIDEO', '/initCourseware/k39-国内外先进安全管理经验-胡艳华.mp4', NULL, NULL,3, 0, NULL, '2023-08-05 21:49:11', '3000-01-01 00:00:00', '2023-08-05 21:49:11', '2023-08-05 21:49:11', '2023-08-05 21:49:11', 7578, 7578, '818126977', 0, 0, 0 );
INSERT INTO `t_courseware` (`organ_id`, `tag_id`, `name`, `video_name`, `video_id`, `duration`, `video_status`, `file_type`, `url`,`expert_id`, `period`, `status`, `source`, `remark`, `start_time`, `end_time`, `submit_time`,`create_time`, `update_time`, `create_user`, `update_user`, `videosize`, `times`, `cancel`, `taster` )VALUES('2573', '213', 'k4-学习贯彻习近平总书记关于安全生产重要论述4-杨尊昭', 'k4-学习贯彻习近平总书记关于安全生产重要论述4-杨尊昭.mp4', NULL, '2462',0, 'VIDEO', '/initCourseware/k4-学习贯彻习近平总书记关于安全生产重要论述4-杨尊昭.mp4', NULL, NULL,3, 0, NULL, '2023-08-05 21:49:11', '3000-01-01 00:00:00', '2023-08-05 21:49:11', '2023-08-05 21:49:11', '2023-08-05 21:49:11', 7578, 7578, '74647200', 0, 0, 0 );
INSERT INTO `t_courseware` (`organ_id`, `tag_id`, `name`, `video_name`, `video_id`, `duration`, `video_status`, `file_type`, `url`,`expert_id`, `period`, `status`, `source`, `remark`, `start_time`, `end_time`, `submit_time`,`create_time`, `update_time`, `create_user`, `update_user`, `videosize`, `times`, `cancel`, `taster` )VALUES('2573', '213', 'k40-企业安全文化-房慧辰', 'k40-企业安全文化-房慧辰.mp4', NULL, '2233',0, 'VIDEO', '/initCourseware/k40-企业安全文化-房慧辰.mp4', NULL, NULL,3, 0, NULL, '2023-08-05 21:49:11', '3000-01-01 00:00:00', '2023-08-05 21:49:11', '2023-08-05 21:49:11', '2023-08-05 21:49:11', 7578, 7578, '1142975481', 0, 0, 0 );
INSERT INTO `t_courseware` (`organ_id`, `tag_id`, `name`, `video_name`, `video_id`, `duration`, `video_status`, `file_type`, `url`,`expert_id`, `period`, `status`, `source`, `remark`, `start_time`, `end_time`, `submit_time`,`create_time`, `update_time`, `create_user`, `update_user`, `videosize`, `times`, `cancel`, `taster` )VALUES('2573', '213', 'k41-企业安全文化-房慧辰', 'k41-企业安全文化-房慧辰.mp4', NULL, '2576',0, 'VIDEO', '/initCourseware/k41-企业安全文化-房慧辰.mp4', NULL, NULL,3, 0, NULL, '2023-08-05 21:49:11', '3000-01-01 00:00:00', '2023-08-05 21:49:11', '2023-08-05 21:49:11', '2023-08-05 21:49:11', 7578, 7578, '669803055', 0, 0, 0 );
INSERT INTO `t_courseware` (`organ_id`, `tag_id`, `name`, `video_name`, `video_id`, `duration`, `video_status`, `file_type`, `url`,`expert_id`, `period`, `status`, `source`, `remark`, `start_time`, `end_time`, `submit_time`,`create_time`, `update_time`, `create_user`, `update_user`, `videosize`, `times`, `cancel`, `taster` )VALUES('2573', '213', 'k42-GB16994.1-2021《港口作业安全要求第一部分油气化工码头》 解读-徐凤霞', 'k42-GB16994.1-2021《港口作业安全要求第一部分油气化工码头》 解读-徐凤霞.mp4', NULL, '5514',0, 'VIDEO', '/initCourseware/k42-GB16994.1-2021《港口作业安全要求第一部分油气化工码头》 解读-徐凤霞.mp4', NULL, NULL,3, 0, NULL, '2023-08-05 21:49:11', '3000-01-01 00:00:00', '2023-08-05 21:49:11', '2023-08-05 21:49:11', '2023-08-05 21:49:11', 7578, 7578, '2140813286', 0, 0, 0 );
INSERT INTO `t_courseware` (`organ_id`, `tag_id`, `name`, `video_name`, `video_id`, `duration`, `video_status`, `file_type`, `url`,`expert_id`, `period`, `status`, `source`, `remark`, `start_time`, `end_time`, `submit_time`,`create_time`, `update_time`, `create_user`, `update_user`, `videosize`, `times`, `cancel`, `taster` )VALUES('2573', '213', 'k43-GB30871-2022《危险化学品企业特殊作业安全规范》解读-房慧辰', 'k43-GB30871-2022《危险化学品企业特殊作业安全规范》解读-房慧辰.mp4', NULL, '4689',0, 'VIDEO', '/initCourseware/k43-GB30871-2022《危险化学品企业特殊作业安全规范》解读-房慧辰.mp4', NULL, NULL,3, 0, NULL, '2023-08-05 21:49:11', '3000-01-01 00:00:00', '2023-08-05 21:49:11', '2023-08-05 21:49:11', '2023-08-05 21:49:11', 7578, 7578, '1328996850', 0, 0, 0 );
INSERT INTO `t_courseware` (`organ_id`, `tag_id`, `name`, `video_name`, `video_id`, `duration`, `video_status`, `file_type`, `url`,`expert_id`, `period`, `status`, `source`, `remark`, `start_time`, `end_time`, `submit_time`,`create_time`, `update_time`, `create_user`, `update_user`, `videosize`, `times`, `cancel`, `taster` )VALUES('2573', '213', 'k44-危险化学品企业事故案例分析-蒋治强', 'k44-危险化学品企业事故案例分析-蒋治强.mp4', NULL, '2727',0, 'VIDEO', '/initCourseware/k44-危险化学品企业事故案例分析-蒋治强.mp4', NULL, NULL,3, 0, NULL, '2023-08-05 21:49:11', '3000-01-01 00:00:00', '2023-08-05 21:49:11', '2023-08-05 21:49:11', '2023-08-05 21:49:11', 7578, 7578, '1835407947', 0, 0, 0 );
INSERT INTO `t_courseware` (`organ_id`, `tag_id`, `name`, `video_name`, `video_id`, `duration`, `video_status`, `file_type`, `url`,`expert_id`, `period`, `status`, `source`, `remark`, `start_time`, `end_time`, `submit_time`,`create_time`, `update_time`, `create_user`, `update_user`, `videosize`, `times`, `cancel`, `taster` )VALUES('2573', '213', 'k45-应急管理1-刘先锋', 'k45-应急管理1-刘先锋.mp4', NULL, '2902',0, 'VIDEO', '/initCourseware/k45-应急管理1-刘先锋.mp4', NULL, NULL,3, 0, NULL, '2023-08-05 21:49:11', '3000-01-01 00:00:00', '2023-08-05 21:49:11', '2023-08-05 21:49:11', '2023-08-05 21:49:11', 7578, 7578, '1011300681', 0, 0, 0 );
INSERT INTO `t_courseware` (`organ_id`, `tag_id`, `name`, `video_name`, `video_id`, `duration`, `video_status`, `file_type`, `url`,`expert_id`, `period`, `status`, `source`, `remark`, `start_time`, `end_time`, `submit_time`,`create_time`, `update_time`, `create_user`, `update_user`, `videosize`, `times`, `cancel`, `taster` )VALUES('2573', '213', 'k46-应急管理2-刘先锋', 'k46-应急管理2-刘先锋.mp4', NULL, '2985',0, 'VIDEO', '/initCourseware/k46-应急管理2-刘先锋.mp4', NULL, NULL,3, 0, NULL, '2023-08-05 21:49:11', '3000-01-01 00:00:00', '2023-08-05 21:49:11', '2023-08-05 21:49:11', '2023-08-05 21:49:11', 7578, 7578, '941901257', 0, 0, 0 );
INSERT INTO `t_courseware` (`organ_id`, `tag_id`, `name`, `video_name`, `video_id`, `duration`, `video_status`, `file_type`, `url`,`expert_id`, `period`, `status`, `source`, `remark`, `start_time`, `end_time`, `submit_time`,`create_time`, `update_time`, `create_user`, `update_user`, `videosize`, `times`, `cancel`, `taster` )VALUES('2573', '213', 'k47-安全风险管控与隐患排查双重预防体系建设培训-曹煜', 'k47-安全风险管控与隐患排查双重预防体系建设培训-曹煜.mp4', NULL, '3586',0, 'VIDEO', '/initCourseware/k47-安全风险管控与隐患排查双重预防体系建设培训-曹煜.mp4', NULL, NULL,3, 0, NULL, '2023-08-05 21:49:11', '3000-01-01 00:00:00', '2023-08-05 21:49:11', '2023-08-05 21:49:11', '2023-08-05 21:49:11', 7578, 7578, '2147483647', 0, 0, 0 );
INSERT INTO `t_courseware` (`organ_id`, `tag_id`, `name`, `video_name`, `video_id`, `duration`, `video_status`, `file_type`, `url`,`expert_id`, `period`, `status`, `source`, `remark`, `start_time`, `end_time`, `submit_time`,`create_time`, `update_time`, `create_user`, `update_user`, `videosize`, `times`, `cancel`, `taster` )VALUES('2573', '213', 'k48-应急救护培训-张海林', 'k48-应急救护培训-张海林.mp4', NULL, '4938',0, 'VIDEO', '/initCourseware/k48-应急救护培训-张海林.mp4', NULL, NULL,3, 0, NULL, '2023-08-05 21:49:11', '3000-01-01 00:00:00', '2023-08-05 21:49:11', '2023-08-05 21:49:11', '2023-08-05 21:49:11', 7578, 7578, '2123006308', 0, 0, 0 );
INSERT INTO `t_courseware` (`organ_id`, `tag_id`, `name`, `video_name`, `video_id`, `duration`, `video_status`, `file_type`, `url`,`expert_id`, `period`, `status`, `source`, `remark`, `start_time`, `end_time`, `submit_time`,`create_time`, `update_time`, `create_user`, `update_user`, `videosize`, `times`, `cancel`, `taster` )VALUES('2573', '213', 'k49-刑法修正案十一-李长满', 'k49-刑法修正案十一-李长满.mp4', NULL, '3529',0, 'VIDEO', '/initCourseware/k49-刑法修正案十一-李长满.mp4', NULL, NULL,3, 0, NULL, '2023-08-05 21:49:11', '3000-01-01 00:00:00', '2023-08-05 21:49:11', '2023-08-05 21:49:11', '2023-08-05 21:49:11', 7578, 7578, '2147483647', 0, 0, 0 );
INSERT INTO `t_courseware` (`organ_id`, `tag_id`, `name`, `video_name`, `video_id`, `duration`, `video_status`, `file_type`, `url`,`expert_id`, `period`, `status`, `source`, `remark`, `start_time`, `end_time`, `submit_time`,`create_time`, `update_time`, `create_user`, `update_user`, `videosize`, `times`, `cancel`, `taster` )VALUES('2573', '213', 'k5-学习贯彻习近平总书记关于安全生产重要论述5-杨尊昭', 'k5-学习贯彻习近平总书记关于安全生产重要论述5-杨尊昭.mp4', NULL, '2482',0, 'VIDEO', '/initCourseware/k5-学习贯彻习近平总书记关于安全生产重要论述5-杨尊昭.mp4', NULL, NULL,3, 0, NULL, '2023-08-05 21:49:11', '3000-01-01 00:00:00', '2023-08-05 21:49:11', '2023-08-05 21:49:11', '2023-08-05 21:49:11', 7578, 7578, '101017019', 0, 0, 0 );
INSERT INTO `t_courseware` (`organ_id`, `tag_id`, `name`, `video_name`, `video_id`, `duration`, `video_status`, `file_type`, `url`,`expert_id`, `period`, `status`, `source`, `remark`, `start_time`, `end_time`, `submit_time`,`create_time`, `update_time`, `create_user`, `update_user`, `videosize`, `times`, `cancel`, `taster` )VALUES('2573', '213', 'k50-安全风险分级管控与隐患排查双控预防机制1-徐凤霞_1', 'k50-安全风险分级管控与隐患排查双控预防机制1-徐凤霞_1.mp4', NULL, '3056',0, 'VIDEO', '/initCourseware/k50-安全风险分级管控与隐患排查双控预防机制1-徐凤霞_1.mp4', NULL, NULL,3, 0, NULL, '2023-08-05 21:49:11', '3000-01-01 00:00:00', '2023-08-05 21:49:11', '2023-08-05 21:49:11', '2023-08-05 21:49:11', 7578, 7578, '1243251109', 0, 0, 0 );
INSERT INTO `t_courseware` (`organ_id`, `tag_id`, `name`, `video_name`, `video_id`, `duration`, `video_status`, `file_type`, `url`,`expert_id`, `period`, `status`, `source`, `remark`, `start_time`, `end_time`, `submit_time`,`create_time`, `update_time`, `create_user`, `update_user`, `videosize`, `times`, `cancel`, `taster` )VALUES('2573', '213', 'k51-安全风险分级管控与隐患排查双控预防机制2-徐凤霞', 'k51-安全风险分级管控与隐患排查双控预防机制2-徐凤霞.mp4', NULL, '2849',0, 'VIDEO', '/initCourseware/k51-安全风险分级管控与隐患排查双控预防机制2-徐凤霞.mp4', NULL, NULL,3, 0, NULL, '2023-08-05 21:49:11', '3000-01-01 00:00:00', '2023-08-05 21:49:11', '2023-08-05 21:49:11', '2023-08-05 21:49:11', 7578, 7578, '1855455748', 0, 0, 0 );
INSERT INTO `t_courseware` (`organ_id`, `tag_id`, `name`, `video_name`, `video_id`, `duration`, `video_status`, `file_type`, `url`,`expert_id`, `period`, `status`, `source`, `remark`, `start_time`, `end_time`, `submit_time`,`create_time`, `update_time`, `create_user`, `update_user`, `videosize`, `times`, `cancel`, `taster` )VALUES('2573', '213', 'k52-安全风险分级管控与隐患排查双控预防机制3-徐凤霞', 'k52-安全风险分级管控与隐患排查双控预防机制3-徐凤霞.mp4', NULL, '3566',0, 'VIDEO', '/initCourseware/k52-安全风险分级管控与隐患排查双控预防机制3-徐凤霞.mp4', NULL, NULL,3, 0, NULL, '2023-08-05 21:49:11', '3000-01-01 00:00:00', '2023-08-05 21:49:11', '2023-08-05 21:49:11', '2023-08-05 21:49:11', 7578, 7578, '2077352000', 0, 0, 0 );
INSERT INTO `t_courseware` (`organ_id`, `tag_id`, `name`, `video_name`, `video_id`, `duration`, `video_status`, `file_type`, `url`,`expert_id`, `period`, `status`, `source`, `remark`, `start_time`, `end_time`, `submit_time`,`create_time`, `update_time`, `create_user`, `update_user`, `videosize`, `times`, `cancel`, `taster` )VALUES('2573', '213', 'k53-安全风险分级管控与隐患排查双控预防机制4-徐凤霞', 'k53-安全风险分级管控与隐患排查双控预防机制4-徐凤霞.mp4', NULL, '2625',0, 'VIDEO', '/initCourseware/k53-安全风险分级管控与隐患排查双控预防机制4-徐凤霞.mp4', NULL, NULL,3, 0, NULL, '2023-08-05 21:49:11', '3000-01-01 00:00:00', '2023-08-05 21:49:11', '2023-08-05 21:49:11', '2023-08-05 21:49:11', 7578, 7578, '1446654921', 0, 0, 0 );
INSERT INTO `t_courseware` (`organ_id`, `tag_id`, `name`, `video_name`, `video_id`, `duration`, `video_status`, `file_type`, `url`,`expert_id`, `period`, `status`, `source`, `remark`, `start_time`, `end_time`, `submit_time`,`create_time`, `update_time`, `create_user`, `update_user`, `videosize`, `times`, `cancel`, `taster` )VALUES('2573', '213', 'k54-安全风险分级管控与隐患排查双控预防机制5-徐凤霞', 'k54-安全风险分级管控与隐患排查双控预防机制5-徐凤霞.mp4', NULL, '2412',0, 'VIDEO', '/initCourseware/k54-安全风险分级管控与隐患排查双控预防机制5-徐凤霞.mp4', NULL, NULL,3, 0, NULL, '2023-08-05 21:49:11', '3000-01-01 00:00:00', '2023-08-05 21:49:11', '2023-08-05 21:49:11', '2023-08-05 21:49:11', 7578, 7578, '1071338753', 0, 0, 0 );
INSERT INTO `t_courseware` (`organ_id`, `tag_id`, `name`, `video_name`, `video_id`, `duration`, `video_status`, `file_type`, `url`,`expert_id`, `period`, `status`, `source`, `remark`, `start_time`, `end_time`, `submit_time`,`create_time`, `update_time`, `create_user`, `update_user`, `videosize`, `times`, `cancel`, `taster` )VALUES('2573', '213', 'k55-电气安全管理-赵伟浩', 'k55-电气安全管理-赵伟浩.mp4', NULL, '3751',0, 'VIDEO', '/initCourseware/k55-电气安全管理-赵伟浩.mp4', NULL, NULL,3, 0, NULL, '2023-08-05 21:49:11', '3000-01-01 00:00:00', '2023-08-05 21:49:11', '2023-08-05 21:49:11', '2023-08-05 21:49:11', 7578, 7578, '1772542353', 0, 0, 0 );
INSERT INTO `t_courseware` (`organ_id`, `tag_id`, `name`, `video_name`, `video_id`, `duration`, `video_status`, `file_type`, `url`,`expert_id`, `period`, `status`, `source`, `remark`, `start_time`, `end_time`, `submit_time`,`create_time`, `update_time`, `create_user`, `update_user`, `videosize`, `times`, `cancel`, `taster` )VALUES('2573', '213', 'k56-k59-事故现场应急救援救护处置-杨尊昭', 'k56-k59-事故现场应急救援救护处置-杨尊昭.mp4', NULL, '12069',0, 'VIDEO', '/initCourseware/k56-k59-事故现场应急救援救护处置-杨尊昭.mp4', NULL, NULL,3, 0, NULL, '2023-08-05 21:49:11', '3000-01-01 00:00:00', '2023-08-05 21:49:11', '2023-08-05 21:49:11', '2023-08-05 21:49:11', 7578, 7578, '2147483647', 0, 0, 0 );
INSERT INTO `t_courseware` (`organ_id`, `tag_id`, `name`, `video_name`, `video_id`, `duration`, `video_status`, `file_type`, `url`,`expert_id`, `period`, `status`, `source`, `remark`, `start_time`, `end_time`, `submit_time`,`create_time`, `update_time`, `create_user`, `update_user`, `videosize`, `times`, `cancel`, `taster` )VALUES('2573', '213', 'k6-学习贯彻习近平总书记关于安全生产重要论述6-杨尊昭', 'k6-学习贯彻习近平总书记关于安全生产重要论述6-杨尊昭.mp4', NULL, '2490',0, 'VIDEO', '/initCourseware/k6-学习贯彻习近平总书记关于安全生产重要论述6-杨尊昭.mp4', NULL, NULL,3, 0, NULL, '2023-08-05 21:49:11', '3000-01-01 00:00:00', '2023-08-05 21:49:11', '2023-08-05 21:49:11', '2023-08-05 21:49:11', 7578, 7578, '84562156', 0, 0, 0 );
INSERT INTO `t_courseware` (`organ_id`, `tag_id`, `name`, `video_name`, `video_id`, `duration`, `video_status`, `file_type`, `url`,`expert_id`, `period`, `status`, `source`, `remark`, `start_time`, `end_time`, `submit_time`,`create_time`, `update_time`, `create_user`, `update_user`, `videosize`, `times`, `cancel`, `taster` )VALUES('2573', '213', 'k60应急管理培训-王立浩', 'k60应急管理培训-王立浩.mp4', NULL, '2754',0, 'VIDEO', '/initCourseware/k60应急管理培训-王立浩.mp4', NULL, NULL,3, 0, NULL, '2023-08-05 21:49:11', '3000-01-01 00:00:00', '2023-08-05 21:49:11', '2023-08-05 21:49:11', '2023-08-05 21:49:11', 7578, 7578, '2147483647', 0, 0, 0 );
INSERT INTO `t_courseware` (`organ_id`, `tag_id`, `name`, `video_name`, `video_id`, `duration`, `video_status`, `file_type`, `url`,`expert_id`, `period`, `status`, `source`, `remark`, `start_time`, `end_time`, `submit_time`,`create_time`, `update_time`, `create_user`, `update_user`, `videosize`, `times`, `cancel`, `taster` )VALUES('2573', '213', 'k61-应急管理培训-王立浩', 'k61-应急管理培训-王立浩.mp4', NULL, '3284',0, 'VIDEO', '/initCourseware/k61-应急管理培训-王立浩.mp4', NULL, NULL,3, 0, NULL, '2023-08-05 21:49:11', '3000-01-01 00:00:00', '2023-08-05 21:49:11', '2023-08-05 21:49:11', '2023-08-05 21:49:11', 7578, 7578, '2147483647', 0, 0, 0 );
INSERT INTO `t_courseware` (`organ_id`, `tag_id`, `name`, `video_name`, `video_id`, `duration`, `video_status`, `file_type`, `url`,`expert_id`, `period`, `status`, `source`, `remark`, `start_time`, `end_time`, `submit_time`,`create_time`, `update_time`, `create_user`, `update_user`, `videosize`, `times`, `cancel`, `taster` )VALUES('2573', '213', 'k62-隐患直击-祸从违章来1', 'k62-隐患直击-祸从违章来1.mp4', NULL, '1471',0, 'VIDEO', '/initCourseware/k62-隐患直击-祸从违章来1.mp4', NULL, NULL,3, 0, NULL, '2023-08-05 21:49:11', '3000-01-01 00:00:00', '2023-08-05 21:49:11', '2023-08-05 21:49:11', '2023-08-05 21:49:11', 7578, 7578, '587211088', 0, 0, 0 );
INSERT INTO `t_courseware` (`organ_id`, `tag_id`, `name`, `video_name`, `video_id`, `duration`, `video_status`, `file_type`, `url`,`expert_id`, `period`, `status`, `source`, `remark`, `start_time`, `end_time`, `submit_time`,`create_time`, `update_time`, `create_user`, `update_user`, `videosize`, `times`, `cancel`, `taster` )VALUES('2573', '213', 'k63-隐患直击-祸从违章来2', 'k63-隐患直击-祸从违章来2.mp4', NULL, '1439',0, 'VIDEO', '/initCourseware/k63-隐患直击-祸从违章来2.mp4', NULL, NULL,3, 0, NULL, '2023-08-05 21:49:11', '3000-01-01 00:00:00', '2023-08-05 21:49:11', '2023-08-05 21:49:11', '2023-08-05 21:49:11', 7578, 7578, '574536600', 0, 0, 0 );
INSERT INTO `t_courseware` (`organ_id`, `tag_id`, `name`, `video_name`, `video_id`, `duration`, `video_status`, `file_type`, `url`,`expert_id`, `period`, `status`, `source`, `remark`, `start_time`, `end_time`, `submit_time`,`create_time`, `update_time`, `create_user`, `update_user`, `videosize`, `times`, `cancel`, `taster` )VALUES('2573', '213', 'k64-隐患直击-祸从违章来3', 'k64-隐患直击-祸从违章来3.mp4', NULL, '1493',0, 'VIDEO', '/initCourseware/k64-隐患直击-祸从违章来3.mp4', NULL, NULL,3, 0, NULL, '2023-08-05 21:49:11', '3000-01-01 00:00:00', '2023-08-05 21:49:11', '2023-08-05 21:49:11', '2023-08-05 21:49:11', 7578, 7578, '595594604', 0, 0, 0 );
INSERT INTO `t_courseware` (`organ_id`, `tag_id`, `name`, `video_name`, `video_id`, `duration`, `video_status`, `file_type`, `url`,`expert_id`, `period`, `status`, `source`, `remark`, `start_time`, `end_time`, `submit_time`,`create_time`, `update_time`, `create_user`, `update_user`, `videosize`, `times`, `cancel`, `taster` )VALUES('2573', '213', 'k65-隐患直击-祸从违章来4', 'k65-隐患直击-祸从违章来4.mp4', NULL, '1381',0, 'VIDEO', '/initCourseware/k65-隐患直击-祸从违章来4.mp4', NULL, NULL,3, 0, NULL, '2023-08-05 21:49:11', '3000-01-01 00:00:00', '2023-08-05 21:49:11', '2023-08-05 21:49:11', '2023-08-05 21:49:11', 7578, 7578, '549190316', 0, 0, 0 );
INSERT INTO `t_courseware` (`organ_id`, `tag_id`, `name`, `video_name`, `video_id`, `duration`, `video_status`, `file_type`, `url`,`expert_id`, `period`, `status`, `source`, `remark`, `start_time`, `end_time`, `submit_time`,`create_time`, `update_time`, `create_user`, `update_user`, `videosize`, `times`, `cancel`, `taster` )VALUES('2573', '213', 'k66-隐患直击-祸从违章来5', 'k66-隐患直击-祸从违章来5.mp4', NULL, '1214',0, 'VIDEO', '/initCourseware/k66-隐患直击-祸从违章来5.mp4', NULL, NULL,3, 0, NULL, '2023-08-05 21:49:11', '3000-01-01 00:00:00', '2023-08-05 21:49:11', '2023-08-05 21:49:11', '2023-08-05 21:49:11', 7578, 7578, '484004365', 0, 0, 0 );
INSERT INTO `t_courseware` (`organ_id`, `tag_id`, `name`, `video_name`, `video_id`, `duration`, `video_status`, `file_type`, `url`,`expert_id`, `period`, `status`, `source`, `remark`, `start_time`, `end_time`, `submit_time`,`create_time`, `update_time`, `create_user`, `update_user`, `videosize`, `times`, `cancel`, `taster` )VALUES('2573', '213', 'k67-隐患直击-祸从违章来6', 'k67-隐患直击-祸从违章来6.mp4', NULL, '1585',0, 'VIDEO', '/initCourseware/k67-隐患直击-祸从违章来6.mp4', NULL, NULL,3, 0, NULL, '2023-08-05 21:49:11', '3000-01-01 00:00:00', '2023-08-05 21:49:11', '2023-08-05 21:49:11', '2023-08-05 21:49:11', 7578, 7578, '631556496', 0, 0, 0 );
INSERT INTO `t_courseware` (`organ_id`, `tag_id`, `name`, `video_name`, `video_id`, `duration`, `video_status`, `file_type`, `url`,`expert_id`, `period`, `status`, `source`, `remark`, `start_time`, `end_time`, `submit_time`,`create_time`, `update_time`, `create_user`, `update_user`, `videosize`, `times`, `cancel`, `taster` )VALUES('2573', '213', 'k68-防静电安全培训-赵伟浩', 'k68-防静电安全培训-赵伟浩.mp4', NULL, '4428',0, 'VIDEO', '/initCourseware/k68-防静电安全培训-赵伟浩.mp4', NULL, NULL,3, 0, NULL, '2023-08-05 21:49:11', '3000-01-01 00:00:00', '2023-08-05 21:49:11', '2023-08-05 21:49:11', '2023-08-05 21:49:11', 7578, 7578, '2147483647', 0, 0, 0 );
INSERT INTO `t_courseware` (`organ_id`, `tag_id`, `name`, `video_name`, `video_id`, `duration`, `video_status`, `file_type`, `url`,`expert_id`, `period`, `status`, `source`, `remark`, `start_time`, `end_time`, `submit_time`,`create_time`, `update_time`, `create_user`, `update_user`, `videosize`, `times`, `cancel`, `taster` )VALUES('2573', '213', 'k69-化工过程安全管理培训-张文倩', 'k69-化工过程安全管理培训-张文倩.mp4', NULL, '4897',0, 'VIDEO', '/initCourseware/k69-化工过程安全管理培训-张文倩.mp4', NULL, NULL,3, 0, NULL, '2023-08-05 21:49:11', '3000-01-01 00:00:00', '2023-08-05 21:49:11', '2023-08-05 21:49:11', '2023-08-05 21:49:11', 7578, 7578, '2147483647', 0, 0, 0 );
INSERT INTO `t_courseware` (`organ_id`, `tag_id`, `name`, `video_name`, `video_id`, `duration`, `video_status`, `file_type`, `url`,`expert_id`, `period`, `status`, `source`, `remark`, `start_time`, `end_time`, `submit_time`,`create_time`, `update_time`, `create_user`, `update_user`, `videosize`, `times`, `cancel`, `taster` )VALUES('2573', '213', 'k7-学习贯彻习近平总书记关于安全生产重要论述7-杨尊昭', 'k7-学习贯彻习近平总书记关于安全生产重要论述7-杨尊昭.mp4', NULL, '2226',0, 'VIDEO', '/initCourseware/k7-学习贯彻习近平总书记关于安全生产重要论述7-杨尊昭.mp4', NULL, NULL,3, 0, NULL, '2023-08-05 21:49:11', '3000-01-01 00:00:00', '2023-08-05 21:49:11', '2023-08-05 21:49:11', '2023-08-05 21:49:11', 7578, 7578, '987027881', 0, 0, 0 );
INSERT INTO `t_courseware` (`organ_id`, `tag_id`, `name`, `video_name`, `video_id`, `duration`, `video_status`, `file_type`, `url`,`expert_id`, `period`, `status`, `source`, `remark`, `start_time`, `end_time`, `submit_time`,`create_time`, `update_time`, `create_user`, `update_user`, `videosize`, `times`, `cancel`, `taster` )VALUES('2573', '213', 'k70-港口设施保安计划制订工作要点1-蒋治强', 'k70-港口设施保安计划制订工作要点1-蒋治强.mp4', NULL, '1468',0, 'VIDEO', '/initCourseware/k70-港口设施保安计划制订工作要点1-蒋治强.mp4', NULL, NULL,3, 0, NULL, '2023-08-05 21:49:11', '3000-01-01 00:00:00', '2023-08-05 21:49:11', '2023-08-05 21:49:11', '2023-08-05 21:49:11', 7578, 7578, '731163884', 0, 0, 0 );
INSERT INTO `t_courseware` (`organ_id`, `tag_id`, `name`, `video_name`, `video_id`, `duration`, `video_status`, `file_type`, `url`,`expert_id`, `period`, `status`, `source`, `remark`, `start_time`, `end_time`, `submit_time`,`create_time`, `update_time`, `create_user`, `update_user`, `videosize`, `times`, `cancel`, `taster` )VALUES('2573', '213', 'k71-港口企业安全检查常用标准规范介绍-冯悦', 'k71-港口企业安全检查常用标准规范介绍-冯悦.mp4', NULL, '5139',0, 'VIDEO', '/initCourseware/k71-港口企业安全检查常用标准规范介绍-冯悦.mp4', NULL, NULL,3, 0, NULL, '2023-08-05 21:49:11', '3000-01-01 00:00:00', '2023-08-05 21:49:11', '2023-08-05 21:49:11', '2023-08-05 21:49:11', 7578, 7578, '2147483647', 0, 0, 0 );
INSERT INTO `t_courseware` (`organ_id`, `tag_id`, `name`, `video_name`, `video_id`, `duration`, `video_status`, `file_type`, `url`,`expert_id`, `period`, `status`, `source`, `remark`, `start_time`, `end_time`, `submit_time`,`create_time`, `update_time`, `create_user`, `update_user`, `videosize`, `times`, `cancel`, `taster` )VALUES('2573', '213', 'k72-安全教育培训管理知识-石之鹏', 'k72-安全教育培训管理知识-石之鹏.mp4', NULL, '3355',0, 'VIDEO', '/initCourseware/k72-安全教育培训管理知识-石之鹏.mp4', NULL, NULL,3, 0, NULL, '2023-08-05 21:49:11', '3000-01-01 00:00:00', '2023-08-05 21:49:11', '2023-08-05 21:49:11', '2023-08-05 21:49:11', 7578, 7578, '2147483647', 0, 0, 0 );
INSERT INTO `t_courseware` (`organ_id`, `tag_id`, `name`, `video_name`, `video_id`, `duration`, `video_status`, `file_type`, `url`,`expert_id`, `period`, `status`, `source`, `remark`, `start_time`, `end_time`, `submit_time`,`create_time`, `update_time`, `create_user`, `update_user`, `videosize`, `times`, `cancel`, `taster` )VALUES('2573', '213', 'k73-消防安全基本知识及火场应急-王保志', 'k73-消防安全基本知识及火场应急-王保志.mp4', NULL, '7395',0, 'VIDEO', '/initCourseware/k73-消防安全基本知识及火场应急-王保志.mp4', NULL, NULL,3, 0, NULL, '2023-08-05 21:49:11', '3000-01-01 00:00:00', '2023-08-05 21:49:11', '2023-08-05 21:49:11', '2023-08-05 21:49:11', 7578, 7578, '2147483647', 0, 0, 0 );
INSERT INTO `t_courseware` (`organ_id`, `tag_id`, `name`, `video_name`, `video_id`, `duration`, `video_status`, `file_type`, `url`,`expert_id`, `period`, `status`, `source`, `remark`, `start_time`, `end_time`, `submit_time`,`create_time`, `update_time`, `create_user`, `update_user`, `videosize`, `times`, `cancel`, `taster` )VALUES('2573', '213', 'k74-劳动防护用品识别评价选择-柴劲', 'k74-劳动防护用品识别评价选择-柴劲.mp4', NULL, '3871',0, 'VIDEO', '/initCourseware/k74-劳动防护用品识别评价选择-柴劲.mp4', NULL, NULL,3, 0, NULL, '2023-08-05 21:49:11', '3000-01-01 00:00:00', '2023-08-05 21:49:11', '2023-08-05 21:49:11', '2023-08-05 21:49:11', 7578, 7578, '1237513985', 0, 0, 0 );
INSERT INTO `t_courseware` (`organ_id`, `tag_id`, `name`, `video_name`, `video_id`, `duration`, `video_status`, `file_type`, `url`,`expert_id`, `period`, `status`, `source`, `remark`, `start_time`, `end_time`, `submit_time`,`create_time`, `update_time`, `create_user`, `update_user`, `videosize`, `times`, `cancel`, `taster` )VALUES('2573', '213', 'k75-个体防护装备的分类、选用和管理-柴劲', 'k75-个体防护装备的分类、选用和管理-柴劲.mp4', NULL, '3592',0, 'VIDEO', '/initCourseware/k75-个体防护装备的分类、选用和管理-柴劲.mp4', NULL, NULL,3, 0, NULL, '2023-08-05 21:49:11', '3000-01-01 00:00:00', '2023-08-05 21:49:11', '2023-08-05 21:49:11', '2023-08-05 21:49:11', 7578, 7578, '926999476', 0, 0, 0 );
INSERT INTO `t_courseware` (`organ_id`, `tag_id`, `name`, `video_name`, `video_id`, `duration`, `video_status`, `file_type`, `url`,`expert_id`, `period`, `status`, `source`, `remark`, `start_time`, `end_time`, `submit_time`,`create_time`, `update_time`, `create_user`, `update_user`, `videosize`, `times`, `cancel`, `taster` )VALUES('2573', '213', 'k76-k77-风险分析方法技术1-2-卢琳琳', 'k76-k77-风险分析方法技术1-2-卢琳琳.mp4', NULL, '2582',0, 'VIDEO', '/initCourseware/k76-k77-风险分析方法技术1-2-卢琳琳.mp4', NULL, NULL,3, 0, NULL, '2023-08-05 21:49:11', '3000-01-01 00:00:00', '2023-08-05 21:49:11', '2023-08-05 21:49:11', '2023-08-05 21:49:11', 7578, 7578, '1244508725', 0, 0, 0 );
INSERT INTO `t_courseware` (`organ_id`, `tag_id`, `name`, `video_name`, `video_id`, `duration`, `video_status`, `file_type`, `url`,`expert_id`, `period`, `status`, `source`, `remark`, `start_time`, `end_time`, `submit_time`,`create_time`, `update_time`, `create_user`, `update_user`, `videosize`, `times`, `cancel`, `taster` )VALUES('2573', '213', 'k77-风险分析方法技术1-2-卢琳琳', 'k77-风险分析方法技术1-2-卢琳琳.mp4', NULL, '3259',0, 'VIDEO', '/initCourseware/k77-风险分析方法技术1-2-卢琳琳.mp4', NULL, NULL,3, 0, NULL, '2023-08-05 21:49:11', '3000-01-01 00:00:00', '2023-08-05 21:49:11', '2023-08-05 21:49:11', '2023-08-05 21:49:11', 7578, 7578, '1909362441', 0, 0, 0 );
INSERT INTO `t_courseware` (`organ_id`, `tag_id`, `name`, `video_name`, `video_id`, `duration`, `video_status`, `file_type`, `url`,`expert_id`, `period`, `status`, `source`, `remark`, `start_time`, `end_time`, `submit_time`,`create_time`, `update_time`, `create_user`, `update_user`, `videosize`, `times`, `cancel`, `taster` )VALUES('2573', '213', 'k8-企业安全生产主体责任1-徐凤霞', 'k8-企业安全生产主体责任1-徐凤霞.mp4', NULL, '1704',0, 'VIDEO', '/initCourseware/k8-企业安全生产主体责任1-徐凤霞.mp4', NULL, NULL,3, 0, NULL, '2023-08-05 21:49:11', '3000-01-01 00:00:00', '2023-08-05 21:49:11', '2023-08-05 21:49:11', '2023-08-05 21:49:11', 7578, 7578, '238673466', 0, 0, 0 );
INSERT INTO `t_courseware` (`organ_id`, `tag_id`, `name`, `video_name`, `video_id`, `duration`, `video_status`, `file_type`, `url`,`expert_id`, `period`, `status`, `source`, `remark`, `start_time`, `end_time`, `submit_time`,`create_time`, `update_time`, `create_user`, `update_user`, `videosize`, `times`, `cancel`, `taster` )VALUES('2573', '213', 'k9-企业安全生产主体责任2-徐凤霞', 'k9-企业安全生产主体责任2-徐凤霞.mp4', NULL, '1165',0, 'VIDEO', '/initCourseware/k9-企业安全生产主体责任2-徐凤霞.mp4', NULL, NULL,3, 0, NULL, '2023-08-05 21:49:11', '3000-01-01 00:00:00', '2023-08-05 21:49:11', '2023-08-05 21:49:11', '2023-08-05 21:49:11', 7578, 7578, '*********', 0, 0, 0 );

--daliangang 库  2023-8-22
-- 危险货物港区重大安全风险管控平台 改为 基础信息管理
update e_upms_menu_module set module_name = '基础信息管理' where module_id = 'workbench' ;

-- daliangang 库 加上企业名称搜索框
update tb_check_fill set company = org_code;
update tb_report set company = org_code;

/*更改可靠性报告单的更新时间字段类型*/
alter table tb_report modify `modify_time` varchar(255);

/*截取字符串*/
UPDATE `tb_report` SET modify_time = DATE_FORMAT( modify_time, '%Y-%m') WHERE modify_time LIKE '%:%';

