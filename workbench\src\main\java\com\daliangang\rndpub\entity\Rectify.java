package com.daliangang.rndpub.entity;

import com.daliangang.rndpub.proxy.RectifyDataProxy;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import xyz.erupt.annotation.Erupt;
import xyz.erupt.annotation.EruptField;
import xyz.erupt.annotation.sub_erupt.Power;
import xyz.erupt.annotation.sub_field.Edit;
import xyz.erupt.annotation.sub_field.EditType;
import xyz.erupt.annotation.sub_field.View;
import xyz.erupt.annotation.sub_field.sub_edit.AttachmentType;
import xyz.erupt.annotation.sub_field.sub_edit.BoolType;
import xyz.erupt.annotation.sub_field.sub_edit.DateType;
import xyz.erupt.annotation.sub_field.sub_edit.Search;
import xyz.erupt.jpa.model.MetaModel;
import xyz.erupt.toolkit.db.Comment;
import xyz.erupt.upms.model.auth.OrgCodeRender;

import javax.persistence.Entity;
import javax.persistence.Lob;
import javax.persistence.Table;

/**
 * @Author: zengwensi
 * @Description:
 * @Date: Created in 2023/4/10 16:16
 * @Modified By
 */
@Erupt(name = "整改", power = @Power(add = true, delete = true, export = true, importable = true, viewDetails = true, edit = true)
		, dataProxy = RectifyDataProxy.class
		, rowOperation = {
})
@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "tb_rectify")
@Comment("整改")
@ApiModel("整改")
@OrgCodeRender(render = false)
public class Rectify extends MetaModel {

	@EruptField(
			views = @View(title = "整改id"),
			edit = @Edit(title = "整改id", type = EditType.INPUT, notNull = true,show = false,
					search = @Search))
	@Comment("整改id")
	@ApiModelProperty("整改id")
	private Long  inspectionResultsId ;

	@EruptField(
			views = @View(title = "整改时间"),
			edit = @Edit(title = "整改时间", type = EditType.DATE, notNull = true,
					dateType = @DateType))
	@Comment("整改时间")
	@ApiModelProperty("整改时间")
	private java.util.Date rectificationTime;

    @EruptField(
		views = @View(title = "整改说明", show = true),
		edit = @Edit(title = "整改说明", type = EditType.TEXTAREA, notNull = true))
	@Comment("整改说明")
	@ApiModelProperty("整改说明")
	private @Lob String description;

	@EruptField(
		views = @View(title = "整改证明材料", show = true),
		edit = @Edit(title = "整改证明材料", type = EditType.ATTACHMENT, notNull = true,
		attachmentType = @AttachmentType(type = AttachmentType.Type.BASE, maxLimit = 20, fileTypes = {".pdf", ".doc", ".docx", ".xlsx", ".png", ".jpg", ".word", ".ppt", ".zip", ".rar"})))
	@Comment("整改证明材料")
	@ApiModelProperty("整改证明材料")
	private String supportingMaterials;

	@EruptField(
			views = @View(title = "复查时间"),
			edit = @Edit(title = "复查时间", type = EditType.DATE, notNull = false))
	@Comment("复查时间")
	@ApiModelProperty("复查时间")
	private java.util.Date reviewDate;


	@EruptField(
			views = @View(title = "复查结果"),
			edit = @Edit(title = "复查结果", type = EditType.BOOLEAN, notNull = false,boolType = @BoolType(trueText = "通过", falseText = "不通过")))
	@Comment("复查结果")
	@ApiModelProperty("复查结果")
	private Boolean reviewResult;


	@EruptField(
			views = @View(title = "整改截止时间"),
			edit = @Edit(title = "整改截止时间", type = EditType.DATE, notNull = false))
	@Comment("整改截止时间")
	@ApiModelProperty("整改截止时间")
	private java.sql.Date deadline;
	@EruptField(
			views = @View(title = "备注"),
			edit = @Edit(title = "备注", type = EditType.TEXTAREA, notNull = false))
	@Comment("备注")
	@ApiModelProperty("备注")
	private @Lob String remark;


}
