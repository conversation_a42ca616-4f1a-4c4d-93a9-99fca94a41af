@echo off
chcp 65001 >nul
setlocal enabledelayedexpansion

echo 🚀 启动大连港工作台 Docker 环境...

REM 检查必要文件
if not exist ".env" (
    echo ❌ 错误：.env 文件不存在！
    echo 请复制 .env.example 为 .env 并配置相应的环境变量
    pause
    exit /b 1
)

if not exist "docker-compose.yml" (
    echo ❌ 错误：docker-compose.yml 文件不存在！
    pause
    exit /b 1
)

REM 创建必要的目录
echo 📁 创建必要的目录...
if not exist "docker\mysql\conf.d" mkdir "docker\mysql\conf.d"
if not exist "docker\mysql\init" mkdir "docker\mysql\init"
if not exist "docker\redis" mkdir "docker\redis"
if not exist "logs" mkdir "logs"
if not exist "uploads" mkdir "uploads"

REM 检查Docker是否运行
docker info >nul 2>&1
if errorlevel 1 (
    echo ❌ 错误：Docker 未运行或无法访问！
    echo 请确保 Docker Desktop 已安装并正在运行
    pause
    exit /b 1
)

REM 检查Docker Compose是否可用
docker-compose --version >nul 2>&1
if errorlevel 1 (
    echo ❌ 错误：docker-compose 命令不可用！
    echo 请安装 Docker Desktop 或 Docker Compose
    pause
    exit /b 1
)

REM 停止现有容器（如果存在）
echo 🛑 停止现有容器...
docker-compose down --remove-orphans

REM 构建并启动服务
echo 🔨 构建并启动服务...
docker-compose up --build -d

REM 等待服务启动
echo ⏳ 等待服务启动...
timeout /t 10 /nobreak >nul

REM 检查服务状态
echo 🔍 检查服务状态...
docker-compose ps

REM 读取端口配置
for /f "tokens=2 delims==" %%a in ('findstr "APP_PORT" .env') do set APP_PORT=%%a
for /f "tokens=2 delims==" %%a in ('findstr "MYSQL_PORT" .env') do set MYSQL_PORT=%%a
for /f "tokens=2 delims==" %%a in ('findstr "REDIS_PORT" .env') do set REDIS_PORT=%%a
for /f "tokens=2 delims==" %%a in ('findstr "MINIO_PORT" .env') do set MINIO_PORT=%%a
for /f "tokens=2 delims==" %%a in ('findstr "MINIO_CONSOLE_PORT" .env') do set MINIO_CONSOLE_PORT=%%a

REM 显示服务信息
echo.
echo ✅ 服务启动完成！
echo.
echo 📋 服务访问信息：
echo   🌐 大连港工作台: http://localhost:!APP_PORT!
echo   🗄️  MySQL: localhost:!MYSQL_PORT!
echo   🔴 Redis: localhost:!REDIS_PORT!
echo   📦 MinIO: http://localhost:!MINIO_PORT!
echo   🎛️  MinIO Console: http://localhost:!MINIO_CONSOLE_PORT!
echo.
echo 📝 查看日志：
echo   docker-compose logs -f daliangang-app
echo.
echo 🛑 停止服务：
echo   docker-compose down
echo.
echo 🔄 重启服务：
echo   docker-compose restart
echo.

REM 检查应用健康状态
echo 🏥 检查应用健康状态...
set /a count=0
:healthcheck
set /a count+=1
curl -f http://localhost:!APP_PORT!/actuator/health >nul 2>&1
if !errorlevel! equ 0 (
    echo ✅ 应用健康检查通过！
    echo 🌐 应用访问地址: http://localhost:!APP_PORT!
    goto :success
)
if !count! lss 30 (
    echo ⏳ 等待应用启动... (!count!/30^)
    timeout /t 5 /nobreak >nul
    goto :healthcheck
)

echo ⚠️  应用可能启动失败，请检查日志：
echo   docker-compose logs daliangang-app
echo 🔍 手动检查健康状态：
echo   curl http://localhost:!APP_PORT!/actuator/health

:success
echo.
echo 🎉 大连港工作台 Docker 环境启动完成！
echo.
echo 按任意键退出...
pause >nul
