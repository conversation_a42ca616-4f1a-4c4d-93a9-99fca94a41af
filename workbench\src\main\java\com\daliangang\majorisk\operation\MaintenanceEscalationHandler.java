/*
* Copyright © 2020-2035 yundingyun All rights reserved.
*/
package com.daliangang.majorisk.operation;

import org.springframework.stereotype.*;
import xyz.erupt.annotation.fun.*;
import java.util.*;
import com.daliangang.majorisk.entity.*;
import xyz.erupt.jpa.dao.EruptDao;
import xyz.erupt.toolkit.utils.NotifyUtils;

import javax.annotation.Resource;
import javax.transaction.Transactional;

@Service
public class MaintenanceEscalationHandler implements OperationHandler<Maintenance, Void> {

    @Resource
    private EruptDao eruptDao;
   @Override
   @Transactional
   public String exec(List<Maintenance> data, Void unused, String[] param) {


       data.forEach(v->{
           v.setSubmitted(true);
           eruptDao.merge(v);
       });
       return NotifyUtils.getSuccessNotify("上报成功！");
	}
}
