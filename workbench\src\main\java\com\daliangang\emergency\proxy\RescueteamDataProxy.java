/*
 * Copyright © 2020-2035 yundingyun All rights reserved.
 */
package com.daliangang.emergency.proxy;

import cn.hutool.json.JSONObject;
import com.daliangang.core.DaliangangContext;
import com.daliangang.emergency.entity.Rescueteam;
import com.google.gson.internal.LinkedTreeMap;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import xyz.erupt.annotation.fun.DataProxy;
import xyz.erupt.annotation.query.Condition;
import xyz.erupt.common.http.EruptRemoteUtils;
import xyz.erupt.core.module.MetaUserinfo;
import xyz.erupt.toolkit.remote.RemoteProxyService;
import xyz.erupt.toolkit.remote.RemoteQuery;
import xyz.erupt.toolkit.utils.SqlUtils;
import xyz.erupt.upms.model.EruptUser;
import xyz.erupt.upms.service.EruptUserService;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

@Service
public class RescueteamDataProxy implements DataProxy<Rescueteam> {
    @Resource
    private RemoteProxyService remoteProxyService;
    @Resource
    private EruptUserService eruptUserService;

    @Override
    public void addBehavior(Rescueteam rescueteam) {
        MetaUserinfo remoteUserInfo = remoteProxyService.getRemoteUserInfo();
        if (StringUtils.isNotEmpty(rescueteam.getCompany())) {
            List<LinkedTreeMap> enterprise = remoteProxyService.queryEntity("Enterprise", RemoteQuery.builder().condition("orgCode", remoteUserInfo.getOrg()));
            if (ObjectUtils.isNotEmpty(enterprise)) {
                String portArea=new BigDecimal(enterprise.get(0).get("portArea").toString()).stripTrailingZeros().toPlainString();
                rescueteam.setPortArea(portArea);
            }
        }
    }

//    @Override
//    public String beforeFetch(List<Condition> conditions) {
//        MetaUserinfo remoteUserInfo = remoteProxyService.getRemoteUserInfo();
//        if (DaliangangContext.isEnterpriseUser() && remoteUserInfo != null) {
//            return "org_code='" + remoteUserInfo.getOrg() + "'";
//        } else if (DaliangangContext.isDepartmentUser() && remoteUserInfo != null) {
//            return "org_code " + SqlUtils.wrapIn(eruptUserService.getUserAuth());
//        }
//        return null;
//    }

    @Override
    public void beforeAdd(Rescueteam rescueteam) {
        MetaUserinfo remoteUserInfo = remoteProxyService.getRemoteUserInfo();
        if (DaliangangContext.isDepartmentUser() && remoteUserInfo != null) {
            rescueteam.setOrgCode(rescueteam.getCompany());
            rescueteam.setCompany(rescueteam.getCompany());
        }
//        else {
//            if (StringUtils.isNotEmpty(rescueteam.getCompany())) {
//                String enterpriseOrgCode = DaliangangContext.getEnterpriseOrgCode(rescueteam.getCompany());
//                rescueteam.setOrgCode(enterpriseOrgCode);
//            }
//        }
    }

    @Override
    public void afterAdd(Rescueteam rescueteam) {
        //推送数据
        JSONObject inputData = new JSONObject();
        inputData.set("clazz", "Rescueteam");
        inputData.set("insertData",rescueteam);
        EruptRemoteUtils.doPost(remoteProxyService.getMainServerUrl() + "/erupt-api/exchange/data/exchange", inputData, Map.class);
    }

    @Override
    public void afterUpdate(Rescueteam rescueteam) {
        //推送数据
        JSONObject inputData = new JSONObject();
        inputData.set("clazz", "Rescueteam");
        inputData.set("insertData",rescueteam);
        EruptRemoteUtils.doPost(remoteProxyService.getMainServerUrl() + "/erupt-api/exchange/data/exchange", inputData, Map.class);
    }

    @Override
    public void searchCondition(Map<String, Object> condition) {
        if(!DaliangangContext.isDepartmentUser()){
            EruptUser user = eruptUserService.getCurrentEruptUser();
            if (user != null && user.getEruptOrg() != null)
                condition.put("company", user.getEruptOrg().getCode());
        }
    }
}
