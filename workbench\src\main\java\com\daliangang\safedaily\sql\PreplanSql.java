package com.daliangang.safedaily.sql;

import org.springframework.stereotype.Repository;

/**
 * <AUTHOR>
 * @since :2023/4/24:15:44
 */
@Repository
public class PreplanSql {
    //
    public String selectPreplanNum (String orgCode) {
        String sql = "SELECT SUM(num) AS num FROM (select COUNT(*) as num,tp.org_code as name from tb_preplan tp left join tb_preplan_data tpd on tp.id =tpd.data_source_id  where tp.submitted = 1";
        sql += " and tp.org_code "+orgCode+"";
        sql += "  GROUP by tp.org_code ORDER by num DESC) AS result";
        return sql;
    }
}
