package com.daliangang.safedaily.sql;

import org.springframework.stereotype.Repository;

/**
 * <AUTHOR>
 * @since :2023/4/23:14:32
 */
@Repository
public class WeeklyReportSql {

    public String selectWeeklyReportNum (String orgCode) {

        String sql = "select COUNT(DISTINCT org_code) as num  from tb_weekly_report  WHERE YEARWEEK(inspection_time,1) = YEARWEEK(now(),1) and submitted = 1 and ";
        sql += " org_code "+orgCode+"";
        return sql;
    }
}
