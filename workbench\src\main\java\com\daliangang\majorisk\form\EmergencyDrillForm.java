package com.daliangang.majorisk.form;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import xyz.erupt.annotation.Erupt;
import xyz.erupt.annotation.EruptField;
import xyz.erupt.annotation.sub_erupt.Power;
import xyz.erupt.annotation.sub_field.Edit;
import xyz.erupt.annotation.sub_field.EditType;
import xyz.erupt.annotation.sub_field.View;
import xyz.erupt.annotation.sub_field.sub_edit.ChoiceType;
import xyz.erupt.annotation.sub_field.sub_edit.DateType;
import xyz.erupt.annotation.sub_field.sub_edit.InputType;
import xyz.erupt.annotation.sub_field.sub_edit.Search;
import xyz.erupt.jpa.model.BaseModel;
import xyz.erupt.toolkit.db.Comment;
import xyz.erupt.toolkit.handler.RemoteEntityChoiceFetchHandler;

import javax.persistence.Entity;

/**
 * <AUTHOR>
 * @since :2023/4/7:16:29
 */
@Erupt(name = "应急演练情况", power = @Power(add = false, delete = false, export = false, importable = true, viewDetails = false, edit = false)
        , rowOperation = {})

@Getter
@Setter
@Comment("应急演练情况")
@ApiModel("应急演练情况")
public class EmergencyDrillForm extends BaseModel {
    @EruptField(
            views = @View(title = "演练名称"),
            edit = @Edit(title = "演练名称", type = EditType.INPUT, search = @Search(vague = true), notNull = true,
                    inputType = @InputType))
    @Comment("演练名称")
    @ApiModelProperty("演练名称")
    private String accidentName;

    @EruptField(
            views = @View(title = "演练日期"),
            edit = @Edit(title = "演练日期", type = EditType.DATE, search = @Search(vague = true), notNull = true,
                    dateType = @DateType))
    @Comment("演练日期")
    @ApiModelProperty("演练日期")
    private java.util.Date place;

}
