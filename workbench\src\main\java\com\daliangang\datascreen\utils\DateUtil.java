package com.daliangang.datascreen.utils;

import java.time.LocalDate;
import java.time.Month;
import java.time.format.DateTimeFormatter;
import java.time.temporal.TemporalAdjusters;
import java.time.temporal.WeekFields;
import java.util.ArrayList;
import java.util.List;
import java.sql.Date;

public class DateUtil {


    /**
     * 获取30天后的Date对象
     * @return 30天后的Date对象
     */
    public static Date getThirtyDaysLaterDateAsDate() {
        LocalDate now = LocalDate.now();
        LocalDate thirtyDaysLater = now.plusDays(30);
        return Date.valueOf(thirtyDaysLater);
    }

    /**
     * 获取当前年份的字符串（格式：yyyy）
     * @return 本年的年份字符串，例如："2024"
     */
    public static String getCurrentYear() {
        LocalDate now = LocalDate.now();
        return now.format(DateTimeFormatter.ofPattern("yyyy"));
    }

    /**
     * 获取当前年份的Date对象（yyyy-01-01）
     * @return 本年的Date对象，表示本年第一天
     */
    public static Date getCurrentYearDate() {
        LocalDate now = LocalDate.now().withDayOfYear(1); // 设置为本年第一天
        return Date.valueOf(now);
    }

    /**
     * 获取当前日期的字符串（格式：yyyy-MM-dd）
     * @return 本日的日期字符串，例如："2024-11-11"
     */
    public static String getCurrentDay() {
        LocalDate now = LocalDate.now();
        return now.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
    }

    /**
     * 获取当前日期的Date对象
     * @return 本日的Date对象，例如：java.sql.Date 类型
     */
    public static Date getCurrentDayDate() {
        LocalDate now = LocalDate.now();
        return Date.valueOf(now);
    }
    public static Date getTomorrowDate() {
        LocalDate tomorrow = LocalDate.now().plusDays(1); // 获取明天的日期
        return Date.valueOf(tomorrow); // 转换为 java.sql.Date
    }

    /**
     * 获取当前月份的字符串（格式：yyyy-MM）
     * @return 本月的月份字符串，例如："2024-11"
     */
    public static String getCurrentMonth() {
        LocalDate now = LocalDate.now();
        return now.format(DateTimeFormatter.ofPattern("yyyy-MM"));
    }

    /**
     * 获取当前月份的Date对象（本月的第一天）
     * @return 本月的Date对象，例如：java.sql.Date 类型
     */
    public static Date getCurrentMonthDate() {
        LocalDate now = LocalDate.now().with(TemporalAdjusters.firstDayOfMonth());
        return Date.valueOf(now);
    }

    /**
     * 获取当前周的第一天字符串（格式：yyyy-MM-dd）
     * @return 一周后的日期字符串，例如："2024-11-18"
     */
    public static String getOneWeekBeginDate() {
        LocalDate now = LocalDate.now();
        LocalDate oneWeekLater = now.with(WeekFields.ISO.dayOfWeek(), 1);
        // 获取一周后的日期
        return oneWeekLater.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
    }

    /**
     * 获取当前周的第一天（通常是星期日）的 Date 对象
     * @return 当前周的结束日期，例如：2024-11-17
     */
    public static Date getOneWeekBeginDateAsDate() {
        LocalDate now = LocalDate.now();
        // 获取当前周的最后一天（例如，默认情况下是星期日）
        LocalDate endOfWeek = now.with(WeekFields.ISO.dayOfWeek(), 1);
        return Date.valueOf(endOfWeek);
    }

    /**
     * 获取一周后的日期字符串（格式：yyyy-MM-dd）
     * @return 一周后的日期字符串，例如："2024-11-18"
     */
    public static String getOneWeekLaterDate() {
        LocalDate now = LocalDate.now();
        LocalDate oneWeekLater = now.with(WeekFields.ISO.dayOfWeek(), 7);
        // 获取一周后的日期
        return oneWeekLater.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
    }

    /**
     * 获取当前周的最后一天（通常是星期日）的 Date 对象
     * @return 当前周的结束日期，例如：2024-11-17
     */
    public static Date getOneWeekLaterDateAsDate() {
        LocalDate now = LocalDate.now();
        // 获取当前周的最后一天（例如，默认情况下是星期日）
        LocalDate endOfWeek = now.with(WeekFields.ISO.dayOfWeek(), 7);
        return Date.valueOf(endOfWeek);
    }

    /**
     * 获取本季的日期范围集合，List[0] 为本季的起始日期，List[1] 为本季的结束日期
     * @return 本季的日期范围集合，例如：[2024-10-01, 2024-12-31]
     */
    public static List<String> getCurrentQuarterRange() {
        LocalDate now = LocalDate.now();
        Month startMonth = now.getMonth().firstMonthOfQuarter();
        LocalDate startOfQuarter = now.withMonth(startMonth.getValue()).with(TemporalAdjusters.firstDayOfMonth());
        LocalDate endOfQuarter = startOfQuarter.plusMonths(2).with(TemporalAdjusters.lastDayOfMonth());

        List<String> quarterRange = new ArrayList<>();
        quarterRange.add(startOfQuarter.format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
        quarterRange.add(endOfQuarter.format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));

        return quarterRange;
    }

    /**
     * 获取本季的Date对象范围集合，List[0] 为本季的起始Date，List[1] 为本季的结束Date
     * @return 本季的Date对象范围集合，例如：[2024-10-01, 2024-12-31]
     */
    public static List<Date> getCurrentQuarterRangeAsDate() {
        LocalDate now = LocalDate.now();
        Month startMonth = now.getMonth().firstMonthOfQuarter();
        LocalDate startOfQuarter = now.withMonth(startMonth.getValue()).with(TemporalAdjusters.firstDayOfMonth());
        LocalDate endOfQuarter = startOfQuarter.plusMonths(2).with(TemporalAdjusters.lastDayOfMonth());

        List<Date> quarterRange = new ArrayList<>();
        quarterRange.add(Date.valueOf(startOfQuarter));
        quarterRange.add(Date.valueOf(endOfQuarter));

        return quarterRange;
    }
}
