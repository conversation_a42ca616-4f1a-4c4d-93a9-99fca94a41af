/*
 * Copyright © 2020-2035 yundingyun All rights reserved.
 */
package com.daliangang.safedaily.proxy;

import com.daliangang.core.DaliangangContext;
import com.daliangang.safedaily.entity.PlanManagement;
import org.springframework.stereotype.Service;
import xyz.erupt.annotation.fun.DataProxy;
import xyz.erupt.annotation.query.Condition;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;

@Service
public class PlanManagementDataProxy implements DataProxy<PlanManagement> {

    @Override
    public void beforeAdd(PlanManagement planManagement) {
        planManagement.setSubmitted(true);
    }

    @Override
    public String beforeFetch(List<Condition> conditions) {
        StringBuilder str = new StringBuilder();
        // 当前时间设置为当天 0 点 0 分 0 秒
        LocalDateTime nowStartOfDay = LocalDateTime.now().toLocalDate().atStartOfDay();
        // 当前时间加上 30 天
        LocalDateTime thirtyDaysLaterStartOfDay = nowStartOfDay.plusDays(30);

        // 定义日期格式化器
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        // 格式化时间
        String nowFormatted = nowStartOfDay.format(formatter);
        String thirtyDaysLaterFormatted = thirtyDaysLaterStartOfDay.format(formatter);

        // 遍历 conditions 参数，构造查询逻辑
        for (Condition condition : conditions) {
            if ("prePlanStatus".equals(condition.getKey())) {
                conditions.remove(condition);
                Object value = condition.getValue();
                if ("正常".equals(value.toString())) {
                    // 当前时间晚于 evaluationTime + 30 天
                    str.append(" AND evaluationTime > '").append(thirtyDaysLaterFormatted).append("'");
                } else if ("已逾期".equals(value.toString())) {
                    // 当前时间晚于 evaluationTime
                    str.append(" AND evaluationTime < '").append(nowFormatted).append("'");
                } else if ("即将逾期".equals(value.toString())) {
                    // 当前时间在 evaluationTime 和 evaluationTime + 30 天之间
                    str.append(" AND evaluationTime BETWEEN '")
                            .append(nowFormatted).append("' AND '").append(thirtyDaysLaterFormatted).append("'");
                }
            }
        }

        // 根据是否是部门用户决定是否添加 `submitted=true`
        if (DaliangangContext.isDepartmentUser()) {
            str.append(" AND submitted=true");
        }

        // 如果没有生成任何条件，返回空字符串
        if (str.length() > 0) {
            return str.substring(5); // 移除开头的 " AND"
        } else {
            return "";
        }
    }

//    @Override
//    public void afterFetch(Collection<Map<String, Object>> list) {
//        Calendar calendar=Calendar.getInstance();
//        List<PlanManagement> plans = EruptDaoUtils.convert(list, PlanManagement.class);
//        for (PlanManagement plan : plans) {
//            //若当前时间过了评估时间的三年，则已逾期；若当前时间距离评估时间还有一个月，则即将逾期；其他为正常状态
//            Date evaluationTime = plan.getEvaluationTime();
//            Date now = new Date(System.currentTimeMillis());
//            //找到评估时间的30天后
//            calendar.setTime(evaluationTime);
//            calendar.add(Calendar.DATE,-30);
//            java.util.Date beforeMonth = calendar.getTime();
//            long year = DateUtil.betweenYear(now, evaluationTime, false);
//            long month = DateUtil.betweenMonth(now, evaluationTime, false);
//            if (year >= 3) {
//                plan.setPreplanState(TplUtils.addColor("已逾期", "red"));
//            } else if (month >= 1) {
//                plan.setPreplanState(TplUtils.addColor("即将逾期", "yellow"));
//            } else {
//                plan.setPreplanState(TplUtils.addColor("正常", "green"));
//            }
//
//            //预案状态:正常、即将到期、已逾期
//            if(now.after(evaluationTime)){
//                //已逾期
//                plan.setPrePlanStatus(TplUtils.addColor("已逾期", "red"));
//            }else {
//
//                //到期前30天为即将逾期，否则为正常
//                if(now.before(beforeMonth)){
//                    plan.setPrePlanStatus(TplUtils.addColor("正常", "green"));
//                }else{
//                    plan.setPrePlanStatus(TplUtils.addColor("即将逾期", "blue"));
//                }
//
//            }
//
//            EruptDaoUtils.updateAfterFetch(list, plan.getId(), "preplanState", plan.getPreplanState());
//            EruptDaoUtils.updateAfterFetch(list, plan.getId(), "prePlanStatus", plan.getPrePlanStatus());
//        }
//
//    }

    @Override
    public void beforeUpdate(PlanManagement planManagement) {
//        if (planManagement.getSubmitted()) {
//            NotifyUtils.showErrorMsg("报告单已经上报，请勿修改！");
//        }
        planManagement.setSubmitted(true);
    }

    @Override
    public void beforeDelete(PlanManagement planManagement) {
//        if (planManagement.getSubmitted()) {
//            NotifyUtils.showErrorMsg("报告单已经上报，请勿删除！");
//        }
    }


}
