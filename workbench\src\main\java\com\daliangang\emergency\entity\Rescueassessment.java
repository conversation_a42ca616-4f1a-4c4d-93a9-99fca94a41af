/*
 * Copyright © 2020-2035 yundingyun All rights reserved.
 */
package com.daliangang.emergency.entity;

import com.daliangang.core.CompanyRenderHandler;
import com.daliangang.emergency.proxy.RescueassessmentDataProxy;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import xyz.erupt.annotation.Erupt;
import xyz.erupt.annotation.EruptField;
import xyz.erupt.annotation.expr.ExprBool;
import xyz.erupt.annotation.sub_erupt.Power;
import xyz.erupt.annotation.sub_field.Edit;
import xyz.erupt.annotation.sub_field.EditType;
import xyz.erupt.annotation.sub_field.Readonly;
import xyz.erupt.annotation.sub_field.View;
import xyz.erupt.annotation.sub_field.sub_edit.*;
import xyz.erupt.toolkit.db.Comment;
import xyz.erupt.toolkit.handler.RemoteEntityChoiceFetchHandler;
import xyz.erupt.upms.model.auth.DataAuthHandler;
import xyz.erupt.upms.model.auth.DataAuthModel;

import javax.persistence.Entity;
import javax.persistence.Table;

@Erupt(name = "应急救援评估", power = @Power(add = true, delete = true, export = false, importable = true, viewDetails = true, edit = true)
        , dataProxy = RescueassessmentDataProxy.class,
        orderBy = "time desc"
        , rowOperation = {})
@Table(name = "tb_rescueassessment")
@Entity
@Getter
@Setter
@Comment("应急救援评估")
@ApiModel("应急救援评估")
public class Rescueassessment extends DataAuthModel {
    @EruptField(
            views = @View(title = "所属单位", width = "400"),
            edit = @Edit(title = "所属单位", search = @Search(ifRender = @ExprBool(exprHandler = CompanyRenderHandler.class) ), type = EditType.CHOICE, notNull = true, readonly = @Readonly(exprHandler = DataAuthHandler.class),
                    choiceType = @ChoiceType(fullSpan = true,fetchHandler = RemoteEntityChoiceFetchHandler.class, fetchHandlerParams = {"main", "EruptOrg", "code,name"})))
    @Comment("所属单位")
    @ApiModelProperty("所属单位")
    private String company;

    @EruptField(
            views = @View(title = "事故名称", width = "200"),
            edit = @Edit(title = "事故名称", type = EditType.INPUT, notNull = true,
                    inputType = @InputType))
    @Comment("事故名称")
    @ApiModelProperty("事故名称")
    private String name;

    @EruptField(
            views = @View(title = "事故发生时间",sortable = true,width="300px"),
            edit = @Edit(title = "事故发生时间", type = EditType.DATE, notNull = true,
                    search = @Search(vague = true ),
                    dateType = @DateType))
    @Comment("事故发生时间")
    @ApiModelProperty("事故发生时间")
    private java.util.Date time;

    @EruptField(
            views = @View(title = "事故发生地点", width = "200"),
            edit = @Edit(title = "事故发生地点", type = EditType.INPUT, notNull = true, search = @Search(vague = true),
                    inputType = @InputType))
    @Comment("事故发生地点")
    @ApiModelProperty("事故发生地点")
    private String address;

    @EruptField(
            views = @View(title = "应急救援评估附件"),
            edit = @Edit(title = "应急救援评估附件", type = EditType.ATTACHMENT, notNull = true,
                    attachmentType = @AttachmentType(type = AttachmentType.Type.BASE, maxLimit = 20, fileTypes = {".pdf", ".doc", ".docx", ".xlsx", ".png", ".jpg", ".xls", ".ppt", ".pptx", ".zip", ".rar"})))
    @Comment("应急救援评估附件")
    @ApiModelProperty("应急救援评估附件")
    private String file;

}
