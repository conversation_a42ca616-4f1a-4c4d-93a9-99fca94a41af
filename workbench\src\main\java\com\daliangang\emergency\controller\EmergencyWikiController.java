package com.daliangang.emergency.controller;

import com.daliangang.core.DaliangangContext;
import com.daliangang.emergency.entity.*;
import com.daliangang.emergency.form.EmergencyDutyPersonForm;
import com.daliangang.emergency.form.RescueteamForm;
import com.google.gson.internal.LinkedTreeMap;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.web.bind.annotation.*;
import xyz.erupt.core.annotation.EruptRouter;
import xyz.erupt.core.module.MetaUserinfo;
import xyz.erupt.core.view.EruptApiModel;
import xyz.erupt.toolkit.remote.RemoteProxyService;
import xyz.erupt.toolkit.remote.RemoteQuery;
import xyz.erupt.toolkit.utils.EruptDaoUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @since :2023/4/26:10:50
 */
@RestController
public class EmergencyWikiController {
    @Resource
    private RemoteProxyService remoteProxyService;
    /**
     * 获取货种名称
     * @param
     * @return
     */
    @RequestMapping("erupt-api/get/emergencyWikiName")
    public EruptApiModel getGoodsType() {
//        Enterprise enterprise = EruptDaoUtils.selectOne("select * from tb_enterprise where org_code ='" +remoteUserInfo.getOrg()+"'", Enterprise.class);
//        if (ObjectUtils.isEmpty(enterprise)) {
//            return EruptApiModel.successApi();
//        }
        List<EmergencyWiki> emergencyWikis = EruptDaoUtils.selectOnes("select id,name from tb_emergency_wiki", EmergencyWiki.class);
        if (ObjectUtils.isNotEmpty(emergencyWikis)) {
            List<LinkedTreeMap> list1 = new ArrayList<>();
            emergencyWikis.forEach(v->{
                LinkedTreeMap map = new LinkedTreeMap();
                map.put("code",v.getId());
                map.put("name",v.getName());
                list1.add(map);
            });

            return EruptApiModel.successApi(list1);
        }

        return EruptApiModel.successApi();
    }

    // 根据名字查询火种详细
    @PostMapping("erupt-api/get/selectEmergencyWikiInfo")
    public EruptApiModel selectEmergencyWikiInfo(@RequestBody Map map) {
        if (ObjectUtils.isEmpty(map)) {
            return EruptApiModel.successApi();
        }
        String name = String.valueOf(map.get("name"));
        EmergencyWiki emergencyExperts = EruptDaoUtils.selectOne("select * from tb_emergency_wiki where name = '"+name+"'", EmergencyWiki.class);
        return EruptApiModel.successApi(emergencyExperts);
    }

    // 查询应急专家
    @RequestMapping("erupt-api/get/selectEmergencyExpertInfo/{id}")
    @EruptRouter(verifyType = EruptRouter.VerifyType.LOGIN)
    public EruptApiModel selectEmergencyExpertInfo(@PathVariable("id") String id) {

        List<LinkedTreeMap> linkedTreeMaps = remoteProxyService.queryEntity("Enterprise", RemoteQuery.builder().condition("orgCode", id));
        List<EmergencyExpert> emergencyExperts = EruptDaoUtils.selectOnes("SELECT * FROM tb_emergency_expert where professional_expertise ='"+linkedTreeMaps.get(0).get("type")+"'", EmergencyExpert.class);

        return EruptApiModel.successApi(emergencyExperts);
    }

    // 查询专家
    @RequestMapping("erupt-api/get/selectEmergencyExperts/{type}")
    public EruptApiModel selectEmergencyExperts(@PathVariable("type") String type) {
        if (type.equals("ALL")) {
            List<EmergencyExpert> emergencyExperts = EruptDaoUtils.selectOnes("SELECT * FROM tb_emergency_expert ", EmergencyExpert.class);
            return EruptApiModel.successApi(emergencyExperts);
        }
        List<EmergencyExpert> emergencyExperts = EruptDaoUtils.selectOnes("SELECT * FROM tb_emergency_expert where professional_expertise ='"+type+"'", EmergencyExpert.class);
        return EruptApiModel.successApi(emergencyExperts);
    }

    // 查询专家信息
    @RequestMapping("erupt-api/get/selectEmergencyExpertXq/{id}")
    public EruptApiModel selectEmergencyExpertXq(@PathVariable("id") Long id) {

        EmergencyExpert emergencyExperts = EruptDaoUtils.selectOne("SELECT * FROM tb_emergency_expert where id ="+id, EmergencyExpert.class);

        return EruptApiModel.successApi(emergencyExperts);
    }

    // 查询事故案例
    @RequestMapping("erupt-api/get/selectCaseBaseInfo")
    public EruptApiModel selectCaseBaseInfo(String accidentType) {
        String replace = accidentType.replace("|", ",");
        List<CaseBase> caseBases = EruptDaoUtils.selectOnes("SELECT * FROM tb_case_base where concat(',',replace(accident_type, '|', ','), ',') regexp concat(',',replace('"+replace+"',',',',|,'),',')", CaseBase.class);
        return EruptApiModel.successApi(caseBases);
    }

    //查询案例库详情
    @RequestMapping("erupt-api/get/selectCaseBaseInfo/{id}")
    public EruptApiModel selectCaseBaseInfoXq(@PathVariable("id") Long id) {

      CaseBase caseBases = EruptDaoUtils.selectOne("SELECT * FROM tb_case_base where id ="+id, CaseBase.class);
        return EruptApiModel.successApi(caseBases);
    }

    // 查询应急值守管理信息
    @RequestMapping("erupt-api/get/selectEmergencyDutyInfo")
    @EruptRouter(verifyType = EruptRouter.VerifyType.LOGIN)
    public EruptApiModel selectEmergencyDutyInfo(String date,String orgCode) {
       // MetaUserinfo remoteUserInfo = remoteProxyService.getRemoteUserInfo();
        List<EmergencyDutyPersonForm> emergencyDutyPeople = EruptDaoUtils.selectOnes(" select tedp.*,ted .end_duty_time FROM tb_emergency_duty ted left join tb_emergency_duty_person tedp on ted .id = tedp.emergency_duty_id where ted .company = '"+orgCode+"' and CONVERT(ted.duty_time,DATE) = '"+date+"'", EmergencyDutyPersonForm.class);
        return EruptApiModel.successApi(emergencyDutyPeople);
    }


    // 日程管理-值守人信息
    @RequestMapping("erupt-api/get/ScheduleManagement/selectEmergencyDutyInfo")
    @EruptRouter(verifyType = EruptRouter.VerifyType.LOGIN)
    public EruptApiModel scheduleManagement(String date) {
         MetaUserinfo remoteUserInfo = remoteProxyService.getRemoteUserInfo();
        List<EmergencyDutyPersonForm> emergencyDutyPeople = EruptDaoUtils.selectOnes(" select tedp.*,ted .end_duty_time FROM tb_emergency_duty ted left join tb_emergency_duty_person tedp on ted .id = tedp.emergency_duty_id where ted .company = '"+remoteUserInfo.getOrg()+"' and date_format(ted.duty_time, '%Y-%m' ) = '"+date+"'", EmergencyDutyPersonForm.class);
        return EruptApiModel.successApi(emergencyDutyPeople);
    }




    // 应急物资
    @RequestMapping("erupt-api/get/selectMaterialReserveList/{orgCode}")
    public EruptApiModel selectMaterialReserveList(@PathVariable("orgCode") String orgCode) {

        if (orgCode.equals("all")) {
            List<MaterialReserve> emergencyDutyPeople = EruptDaoUtils.selectOnes("select * from tb_material_reserve ", MaterialReserve.class);
            return EruptApiModel.successApi(emergencyDutyPeople);
        }
        List<MaterialReserve> emergencyDutyPeople = EruptDaoUtils.selectOnes("select * from tb_material_reserve where org_code = '"+orgCode+"'", MaterialReserve.class);
        return EruptApiModel.successApi(emergencyDutyPeople);
    }
    // 应急物资详情
    @RequestMapping("erupt-api/get/selectMaterialReserveInfo/{id}")
    public EruptApiModel selectMaterialReserveInfo(@PathVariable("id") Long id) {
        MaterialReserve emergencyDutyPeople = EruptDaoUtils.selectOne("select * from tb_material_reserve where id="+id, MaterialReserve.class);
        emergencyDutyPeople.setCompany(DaliangangContext.getEnterpriseName(emergencyDutyPeople.getCompany()));
        return EruptApiModel.successApi(emergencyDutyPeople);
    }


    // 应急集合点
    @RequestMapping("erupt-api/get/selectEmergencyMusterList/{orgCode}")
    public EruptApiModel selectEmergencyMusterList(@PathVariable("orgCode") String orgCode) {
        if (orgCode.equals("all")) {
            List<EmergencyMuster> emergencyDutyPeople = EruptDaoUtils.selectOnes("select * from tb_emergency_muster ", EmergencyMuster.class);
            return EruptApiModel.successApi(emergencyDutyPeople);
        }
        List<EmergencyMuster> emergencyDutyPeople = EruptDaoUtils.selectOnes("select * from tb_emergency_muster where org_code = '"+orgCode+"'", EmergencyMuster.class);
        return EruptApiModel.successApi(emergencyDutyPeople);
    }
    //应急集合点详情
    @RequestMapping("erupt-api/get/selectEmergencyMusterInfo/{id}")
    public EruptApiModel selectEmergencyMusterInfo(@PathVariable("id") Long id) {
        EmergencyMuster emergencyDutyPeople = EruptDaoUtils.selectOne("select * from tb_emergency_muster where id="+id, EmergencyMuster.class);
        emergencyDutyPeople.setCompany(DaliangangContext.getEnterpriseName(emergencyDutyPeople.getCompany()));
        return EruptApiModel.successApi(emergencyDutyPeople);
    }

    //事故池
    @RequestMapping("erupt-api/get/selectEmergencyPoolList/{orgCode}")
    public EruptApiModel selectEmergencyPoolList(@PathVariable("orgCode") String orgCode) {
        if (orgCode.equals("all")) {
            List<EmergencyPool> emergencyDutyPeople = EruptDaoUtils.selectOnes("select * from tb_emergency_pool ", EmergencyPool.class);
            return EruptApiModel.successApi(emergencyDutyPeople);
        }

        List<EmergencyPool> emergencyDutyPeople = EruptDaoUtils.selectOnes("select * from tb_emergency_pool where org_code = '"+orgCode+"'", EmergencyPool.class);
        return EruptApiModel.successApi(emergencyDutyPeople);
    }
    //事故池详情
    @RequestMapping("erupt-api/get/selectEmergencyPoolInfo/{id}")
    public EruptApiModel selectEmergencyPoolInfo(@PathVariable("id") Long id) {
        EmergencyPool emergencyDutyPeople = EruptDaoUtils.selectOne("select * from tb_emergency_pool where id="+id, EmergencyPool.class);
        return EruptApiModel.successApi(emergencyDutyPeople);
    }

    //医院
    @RequestMapping("erupt-api/get/selectHospitalList")
    public EruptApiModel selectHospitalList() {
        List<Hospital> emergencyDutyPeople = EruptDaoUtils.selectOnes("select * from tb_hospital", Hospital.class);
        return EruptApiModel.successApi(emergencyDutyPeople);
    }
    //医院详情
    @RequestMapping("erupt-api/get/selectHospitalInfo/{id}")
    public EruptApiModel selectHospitalInfo(@PathVariable("id") Long id) {
        Hospital emergencyDutyPeople = EruptDaoUtils.selectOne("select * from tb_hospital where id="+id, Hospital.class);
        return EruptApiModel.successApi(emergencyDutyPeople);
    }

    //救援队
    @RequestMapping("erupt-api/get/selectRescueteamList/{orgCode}")
    public EruptApiModel selectRescueteamList(@PathVariable("orgCode") String orgCode) {
        if (orgCode.equals("all")) {
            List<RescueteamForm> emergencyDutyPeople = EruptDaoUtils.selectOnes("select * from tb_rescueteam ", RescueteamForm.class);
            return EruptApiModel.successApi(emergencyDutyPeople);
        }

        List<RescueteamForm> emergencyDutyPeople = EruptDaoUtils.selectOnes("select * from tb_rescueteam where org_code = '"+orgCode+"'", RescueteamForm.class);
        return EruptApiModel.successApi(emergencyDutyPeople);
    }
    //救援队详情
    @RequestMapping("erupt-api/get/selectRescueteamInfo/{id}")
    public EruptApiModel selectRescueteamInfo(@PathVariable("id") Long id) {
        RescueteamForm emergencyDutyPeople = EruptDaoUtils.selectOne("select * from tb_rescueteam where id="+id, RescueteamForm.class);
        emergencyDutyPeople.setCompany(DaliangangContext.getEnterpriseName(emergencyDutyPeople.getCompany()));
        return EruptApiModel.successApi(emergencyDutyPeople);
    }



}
