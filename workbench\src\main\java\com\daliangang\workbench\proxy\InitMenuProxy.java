/*
 * Copyright © 2020-2035 yundingyun All rights reserved.
 */
package com.daliangang.workbench.proxy;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import xyz.erupt.designer.entity.handler.MenuDefaultRoleHandler;
import xyz.erupt.designer.entity.model.ModelMenu;
import xyz.erupt.excel.controller.EruptExcelController;
import xyz.erupt.upms.platform.PlatformProxy;

import javax.annotation.Resource;
import javax.transaction.Transactional;

@Service
@Slf4j
public class InitMenuProxy implements PlatformProxy {

    @Resource
    private MenuDefaultRoleHandler menuDefaultRoleHandler;

    @Resource
    private EruptExcelController eruptExcelController;


    @Override
    @Transactional
    public void init(String[] param) {
        //导菜单权限
        eruptExcelController.importExcel(ModelMenu.class, "public/init/01-00-菜单设置.xls");
        menuDefaultRoleHandler.setTruncate(true);
        menuDefaultRoleHandler.exec(null, null, new String[]{"all"});
        menuDefaultRoleHandler.setTruncate(false);
    }

    @Override
    public int sort() {
        return 2;
    }
}
