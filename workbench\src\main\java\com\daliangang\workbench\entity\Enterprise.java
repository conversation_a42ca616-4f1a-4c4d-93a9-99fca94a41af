/*
 * Copyright © 2020-2035 yundingyun All rights reserved.
 */
package com.daliangang.workbench.entity;

import com.daliangang.core.DaliangangContext;
import com.daliangang.majorisk.operation.WordExportReportHandler;
import com.daliangang.workbench.form.EnterpriseStatForm;
import com.daliangang.workbench.operation.EnterpriseEnabledHandler;
import com.daliangang.workbench.operation.EnterpriseImportAttachedCertificateHandler;
import com.daliangang.workbench.operation.EnterpriseResetPasswordHandler;
import com.daliangang.workbench.proxy.EnterpriseDataProxy;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import xyz.erupt.annotation.Erupt;
import xyz.erupt.annotation.EruptField;
import xyz.erupt.annotation.expr.ExprBool;
import xyz.erupt.annotation.fun.PowerHandler;
import xyz.erupt.annotation.fun.PowerObject;
import xyz.erupt.annotation.sub_erupt.*;
import xyz.erupt.annotation.sub_field.Edit;
import xyz.erupt.annotation.sub_field.EditType;
import xyz.erupt.annotation.sub_field.Readonly;
import xyz.erupt.annotation.sub_field.View;
import xyz.erupt.annotation.sub_field.sub_edit.ReferenceTreeType;
import xyz.erupt.annotation.sub_field.sub_edit.Search;
import xyz.erupt.core.util.EruptSpringUtil;
import xyz.erupt.toolkit.db.Comment;
import xyz.erupt.upms.model.EruptUser;
import xyz.erupt.upms.service.EruptUserService;

import javax.persistence.*;
import java.util.Set;

@Erupt(name = "企业列表", closeTreeView = true, //importTruncate = true,
        power = @Power(export = true, importable = true, powerHandler = Enterprise.class),
        drills = {
                @Drill(code = "EnterpriseStatForm", title = "统计", icon = "fa fa-table", link = @Link(linkErupt = EnterpriseStatForm.class, joinColumn = "enterprise.id")),

        },
        dataProxy = EnterpriseDataProxy.class, rowOperation = {
        @RowOperation(confirm = false, title = "导入附证", icon = "fa fa-arrow-down", operationHandler = EnterpriseImportAttachedCertificateHandler.class, mode = RowOperation.Mode.BUTTON),
//        @RowOperation(show = @ExprBool(value = false), title = "导出附证", icon = "fa fa-arrow-up", operationHandler = EnterpriseExportAttachedCertificateHandler.class, mode = RowOperation.Mode.BUTTON),
        //@RowOperation(title = "同步货种信息", icon = "fa fa-compress", operationHandler = EnterpriseSynchronizeCargoTypeInformationHandler.class, mode = RowOperation.Mode.BUTTON),
        //@RowOperation(title = "同步港区信息", icon = "fa fa-compress", operationHandler = EnterpriseSynchronizePortAreaInformationHandler.class, mode = RowOperation.Mode.BUTTON),
        @RowOperation(confirm = false, show = @ExprBool(exprHandler = Enterprise.class), title = "启用", icon = "fa fa-toggle-off", ifExpr = "item.state=='禁用'", ifExprBehavior = RowOperation.IfExprBehavior.HIDE, operationHandler = EnterpriseEnabledHandler.class, operationParam = "on", mode = RowOperation.Mode.MULTI),
        @RowOperation(confirm = false, show = @ExprBool(exprHandler = Enterprise.class), title = "禁用", icon = "fa fa-toggle-on", ifExpr = "item.state=='启用'", ifExprBehavior = RowOperation.IfExprBehavior.HIDE, operationHandler = EnterpriseEnabledHandler.class, operationParam = "off", mode = RowOperation.Mode.MULTI),
        @RowOperation(title = "重置密码", icon = "fa fa-key", operationHandler = EnterpriseResetPasswordHandler.class, mode = RowOperation.Mode.SINGLE),
        @RowOperation(title = "工作简报导出(日常)", icon = "fa fa-arrow-up",
                mode = RowOperation.Mode.BUTTON,
                isDown = true,
                type = RowOperation.Type.TPL,
                tpl = @Tpl(
                        params = Enterprise.DALIY,
                        path = "tpl/大连市港口危险货物作业工作简报.docx",
                        engine = Tpl.Engine.FreeMarker,
                        tplHandler = WordExportReportHandler.class)
        ),
        @RowOperation(title = "工作简报导出(月底)", icon = "fa fa-arrow-up",
        mode = RowOperation.Mode.BUTTON,
        isDown = true,
        type = RowOperation.Type.TPL,
        tpl = @Tpl(
                params = Enterprise.ENDOFMONTH,
                path = "tpl/大连市港口危险货物作业工作简报.docx",
                engine = Tpl.Engine.FreeMarker,
                tplHandler = WordExportReportHandler.class)
)})
@Table(name = "tb_enterprise", uniqueConstraints = @UniqueConstraint(columnNames = "name"))
@Entity
@Getter
@Setter
@Comment("企业列表")
@ApiModel("企业列表")
//@SystemExcelTemplate(erupt = Enterprise.class, template = "企业列表-模板.xls")
@Slf4j
public class Enterprise extends EnterpriseView implements PowerHandler {
    public static final String DALIY = "daily";
    public static final String ENDOFMONTH = "endOfMonth";

    @EruptField(
            views = @View(title = "主管部门", column = "name", show = true, ifRender = @ExprBool(value = true)),
            edit = @Edit(title = "主管部门", readonly = @Readonly(exprHandler = DepartmentReadOnlyHandler.class), type = EditType.REFERENCE_TREE, notNull = true, search = @Search(vague = true),
                    referenceTreeType = @ReferenceTreeType(pid = "parent.id", label = "name")))
    @ManyToOne
    @Comment("主管部门")
    @ApiModelProperty("主管部门")
    private Department imDepartment;

    @EruptField(
            edit = @Edit(title = "企业附证", type = EditType.TAB_TABLE_ADD))
    @OneToMany(cascade = CascadeType.ALL, orphanRemoval = true)
    @OrderBy
    @JoinColumn(name = "enterprise_id")
    @Comment("企业附证")
    @ApiModelProperty("企业附证")
    private Set<EnterpriseCertificate> certificates;

    @Override
    public void handler(PowerObject power) {
        if (DaliangangContext.isDepartmentUser()) {
            power.setAdd(true);
            power.setDelete(true);
        } else {
            power.setAdd(false);
            power.setDelete(false);
        }

        EruptUserService eruptUserService = EruptSpringUtil.getBean(EruptUserService.class);
        EruptUser eruptUser = eruptUserService.getCurrentEruptUser();
        power.setImportable(DaliangangContext.hasAuth(eruptUser, "Enterprise@IMPORTABLE"));
        power.setExport(DaliangangContext.hasAuth(eruptUser, "Enterprise@EXPORT"));
    }

    @Service
    public static class DepartmentReadOnlyHandler implements Readonly.ReadonlyHandler {

        @Override
        public boolean add(boolean add, String[] params) {

            return this.edit(add, params);
        }

        @Override
        public boolean edit(boolean edit, String[] params) {
            return !DaliangangContext.isDepartmentAdmin();
        }
    }
}
