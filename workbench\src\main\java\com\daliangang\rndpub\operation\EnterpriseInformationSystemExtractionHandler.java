/*
 * Copyright © 2020-2035 yundingyun All rights reserved.
 */
package com.daliangang.rndpub.operation;

import cn.hutool.core.collection.CollectionUtil;
import com.daliangang.rndpub.entity.EnterpriseInformation;
import com.daliangang.rndpub.entity.Procedure;
import lombok.Data;
import org.springframework.stereotype.Service;
import xyz.erupt.annotation.Erupt;
import xyz.erupt.annotation.EruptField;
import xyz.erupt.annotation.fun.OperationHandler;
import xyz.erupt.annotation.sub_field.Edit;
import xyz.erupt.annotation.sub_field.EditType;
import xyz.erupt.core.context.MetaDrill;
import xyz.erupt.jpa.dao.EruptDao;
import xyz.erupt.jpa.model.BaseModel;
import xyz.erupt.toolkit.db.EruptResultMap;
import xyz.erupt.toolkit.utils.EruptDaoUtils;

import javax.annotation.Resource;
import javax.transaction.Transactional;
import java.util.List;

@Service
public class EnterpriseInformationSystemExtractionHandler implements OperationHandler<EnterpriseInformation, EnterpriseInformationSystemExtractionHandler.DrawEnterpriseForm> {

    @Erupt(name = "自动抽取企业")
    @Data
    public static class DrawEnterpriseForm extends BaseModel {
        @EruptField(
                edit = @Edit(title = "抽取企业个数", type = EditType.NUMBER, notNull = true))
        private int count = 10;
    }

    @Resource
    private EruptDao eruptDao;

    @Override
    @Transactional
    public String exec(List<EnterpriseInformation> data, DrawEnterpriseForm form, String[] param) {
        Long produceId=0L;
        if(CollectionUtil.isNotEmpty(data)){
            produceId=data.get(0).getProcedure().getId();
        }else{
            produceId=MetaDrill.getDrillId();
        }
        Procedure procedure = eruptDao.queryEntity(Procedure.class, "id=" + produceId);
        //所有条目设置为未抽取
        String resetSql = "update tb_enterprise_information set state=0 where procedure_id=" + produceId;
        eruptDao.getJdbcTemplate().execute(resetSql);

        String sql = "select * from tb_enterprise_information where procedure_id=" + produceId + " and state=0 order by rand() limit " + form.count;
        List<EnterpriseInformation> enterpriseInformations = EruptDaoUtils.selectOnes(sql, EnterpriseInformation.class);
        for (EnterpriseInformation info : enterpriseInformations) {
            String updSql = "update tb_enterprise_information set state=1 , current_spot_check = now() where id=" + info.getId();
            eruptDao.getJdbcTemplate().execute(updSql);
            eruptDao.flush();
        }
        //更新条数
        updateCountAndName(procedure);
        return null;
    }

    public void updateCountAndName(Procedure procedure){
        String countSql = "select count(*) as count,group_concat(enterprise_name)name from tb_enterprise_information where procedure_id=" + procedure.getId() + " and state=1";
        EruptResultMap eruptResultMap = EruptDaoUtils.selectMap(countSql);
        procedure.setNumberOfEnterprises(eruptResultMap.getInt("count"));
        procedure.setEnterprises(eruptResultMap.getString("name"));
        eruptDao.merge(procedure);
    }
}
