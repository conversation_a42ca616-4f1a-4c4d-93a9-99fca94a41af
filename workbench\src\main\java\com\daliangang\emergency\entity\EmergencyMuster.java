/*
 * Copyright © 2020-2035 yundingyun All rights reserved.
 */
package com.daliangang.emergency.entity;

import com.daliangang.core.CompanyRenderHandler;
import com.daliangang.emergency.proxy.EmergencyMusterDataProxy;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import xyz.erupt.annotation.Erupt;
import xyz.erupt.annotation.EruptField;
import xyz.erupt.annotation.expr.ExprBool;
import xyz.erupt.annotation.sub_erupt.Power;
import xyz.erupt.annotation.sub_field.Edit;
import xyz.erupt.annotation.sub_field.EditType;
import xyz.erupt.annotation.sub_field.Readonly;
import xyz.erupt.annotation.sub_field.View;
import xyz.erupt.annotation.sub_field.sub_edit.ChoiceType;
import xyz.erupt.annotation.sub_field.sub_edit.InputType;
import xyz.erupt.annotation.sub_field.sub_edit.NumberType;
import xyz.erupt.annotation.sub_field.sub_edit.Search;
import xyz.erupt.toolkit.db.Comment;
import xyz.erupt.toolkit.handler.RemoteEntityChoiceFetchHandler;
import xyz.erupt.toolkit.handler.SqlChoiceFetchHandler;
import xyz.erupt.upms.model.auth.DataAuthHandler;
import xyz.erupt.upms.model.auth.DataAuthModel;

import javax.persistence.Entity;
import javax.persistence.Lob;
import javax.persistence.Table;

@Erupt(name = "应急集合点管理",importTruncate = true, power = @Power(add = true, delete = true, export = false, importable = true, viewDetails = true, edit = true)
        , dataProxy = EmergencyMusterDataProxy.class
        , rowOperation = {})
@Table(name = "tb_emergency_muster")
@Entity
@Getter
@Setter
@Comment("应急集合点管理")
@ApiModel("应急集合点管理")
public class EmergencyMuster extends DataAuthModel {
    @EruptField(
            views = @View(title = "企业名称"),
            edit = @Edit(title = "企业名称",
                    type = EditType.CHOICE,
                    notNull = true,
                    search = @Search(ifRender = @ExprBool(exprHandler = CompanyRenderHandler.class)),
                    readonly = @Readonly(exprHandler = DataAuthHandler.class),
                    choiceType = @ChoiceType(
                            fullSpan = true,
                            fetchHandler = RemoteEntityChoiceFetchHandler.class,
                            fetchHandlerParams = {"main", "Enterprise", "org_code,name"}
                    )
            )
    )
    @Comment("企业名称")
    @ApiModelProperty("企业名称")
    private String company;

    @EruptField(
            views = @View(title = "名称"),
            edit = @Edit(title = "名称", type = EditType.INPUT, search = @Search(vague = true), notNull = true,
                    inputType = @InputType))
    @Comment("名称")
    @ApiModelProperty("名称")
    private String name;



    @EruptField(
            views = @View(title = "所属港区"),
            edit = @Edit(title = "所属港区", type = EditType.CHOICE, notNull = true,readonly = @Readonly,
                    search = @Search,
                    choiceType = @ChoiceType(fetchHandler = SqlChoiceFetchHandler.class, fetchHandlerParams ={"select id,portarea_name from tb_port_area"})))
    @Comment("所属港区")
    @ApiModelProperty("所属港区")
    private String portArea;

    @EruptField(
            views = @View(title = "面积（平方米）"),
            edit = @Edit(title = "面积（平方米）", type = EditType.NUMBER, notNull = true,
                    numberType = @NumberType))
    @Comment("面积（平方米）")
    @ApiModelProperty("面积（平方米）")
    private Integer area;

    @EruptField(
            views = @View(title = "大约集合人数"),
            edit = @Edit(title = "大约集合人数", type = EditType.NUMBER, notNull = true,
                    numberType = @NumberType))
    @Comment("大约集合人数")
    @ApiModelProperty("大约集合人数")
    private Integer numberOfPeople;

    @EruptField(
            views = @View(title = "大约停放车辆数"),
            edit = @Edit(title = "大约停放车辆数", type = EditType.NUMBER, notNull = true,
                    numberType = @NumberType))
    @Comment("大约停放车辆数")
    @ApiModelProperty("大约停放车辆数")
    private Integer parkingVehicles;


    @EruptField(
            views = @View(title = "地图", show = false),
            edit = @Edit(title = "地图", type = EditType.MAP, show = true))
    @Comment("地图")
    @ApiModelProperty("地图")
    @Lob
    private String map;

}
