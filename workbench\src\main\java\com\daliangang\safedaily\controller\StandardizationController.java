package com.daliangang.safedaily.controller;

import com.daliangang.safedaily.form.NumForm;
import com.daliangang.safedaily.sql.StandardizationSql;
import com.daliangang.safedaily.sql.WeeklyReportSql;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import xyz.erupt.core.annotation.EruptRouter;
import xyz.erupt.core.module.MetaUserinfo;
import xyz.erupt.core.view.EruptApiModel;
import xyz.erupt.toolkit.remote.RemoteProxyService;
import xyz.erupt.toolkit.utils.EruptDaoUtils;
import xyz.erupt.toolkit.utils.SqlUtils;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @since :2023/4/23:16:26
 */
@RestController
public class StandardizationController {
    @Resource
    private RemoteProxyService remoteProxyService;
    @Resource
    private StandardizationSql standardizationSql;
    //
    @RequestMapping("erupt-api/get/selectStandardizationNum")
    @EruptRouter(verifyType = EruptRouter.VerifyType.LOGIN)
    public EruptApiModel selectStandardizationNum() {
        MetaUserinfo remoteUserInfo = remoteProxyService.getRemoteUserInfo();
        String orgCode = SqlUtils.wrapIn(remoteUserInfo.getAuth());
        String sql = standardizationSql.selectStandardizationNum(orgCode);
        List<NumForm> employeeCertificateForm = EruptDaoUtils.selectOnes(sql, NumForm.class);
        return EruptApiModel.successApi(employeeCertificateForm);
    }
}
