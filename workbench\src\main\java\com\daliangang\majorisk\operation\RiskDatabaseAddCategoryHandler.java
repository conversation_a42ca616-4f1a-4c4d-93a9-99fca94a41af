/*
 * Copyright © 2020-2035 yundingyun All rights reserved.
 */
package com.daliangang.majorisk.operation;

import com.daliangang.majorisk.entity.RiskDatabaseCommon;
import org.springframework.stereotype.Service;
import xyz.erupt.annotation.fun.OperationHandler;

import java.util.List;

@Service
public class RiskDatabaseAddCategoryHandler implements OperationHandler<RiskDatabaseCommon, Void> {
    @Override
    public String exec(List<RiskDatabaseCommon> data, Void unused, String[] param) {
        return null;
    }
}
