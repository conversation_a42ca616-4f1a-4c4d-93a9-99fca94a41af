/*
 * Copyright © 2020-2035 yundingyun All rights reserved.
 */
package com.daliangang.workbench.entity;

import com.daliangang.workbench.operation.EmployeeCertificateAnnexExtensionHandler;
import com.daliangang.workbench.proxy.EmployeeCertificateDataProxy;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import xyz.erupt.annotation.Erupt;
import xyz.erupt.annotation.EruptField;
import xyz.erupt.annotation.sub_erupt.Power;
import xyz.erupt.annotation.sub_erupt.RowOperation;
import xyz.erupt.annotation.sub_field.Edit;
import xyz.erupt.annotation.sub_field.EditType;
import xyz.erupt.annotation.sub_field.View;
import xyz.erupt.annotation.sub_field.sub_edit.AttachmentType;
import xyz.erupt.annotation.sub_field.sub_edit.ChoiceType;
import xyz.erupt.annotation.sub_field.sub_edit.DateType;
import xyz.erupt.annotation.sub_field.sub_edit.InputType;
import xyz.erupt.jpa.model.MetaModel;
import xyz.erupt.toolkit.db.Comment;
import xyz.erupt.toolkit.handler.EruptDictCodeChoiceFetchHandler;
import xyz.erupt.upms.dict.Dict;
import xyz.erupt.upms.dict.DictItem;
import xyz.erupt.upms.dict.Dicts;
import xyz.erupt.upms.model.auth.DataAuthModel;

import javax.persistence.Entity;
import javax.persistence.Table;

@Erupt(name = "员工证书", authVerify = false, power = @Power(add = true, delete = true, export = false, importable = true, viewDetails = true, edit = true), dataProxy = EmployeeCertificateDataProxy.class
        , rowOperation = {
        @RowOperation(title = "附件延期", icon = "fa fa-history", operationHandler = EmployeeCertificateAnnexExtensionHandler.class, mode = RowOperation.Mode.BUTTON)
})
@Table(name = "tb_employee_certificate")
@Entity
@Getter
@Setter
@Comment("员工证书")
@ApiModel("员工证书")
@Dicts(
        @Dict(code = "certificateType", name = "证书类型", value = {
                @DictItem(code = "waterwayTransportation", value = "水路运输从业资格证"),
                @DictItem(code = "specialEquipment", value = "特种设备操作证书"),
                @DictItem(code = "specialOperations", value = "特种作业操作证书"),
                @DictItem(code = "otherCertificates", value = "其他证书")
        })
)
public class EmployeeCertificate extends DataAuthModel {
    @EruptField(
            views = @View(title = "资质证书名称"),
            edit = @Edit(title = "资质证书名称", type = EditType.INPUT, notNull = true,
                    inputType = @InputType))
    @Comment("资质证书名称")
    @ApiModelProperty("资质证书名称")
    private String certificateName;

    @EruptField(
            views = @View(title = "证书类型"),
            edit = @Edit(title = "证书类型", type = EditType.CHOICE, notNull = true,
                    choiceType = @ChoiceType(fetchHandler = EruptDictCodeChoiceFetchHandler.class, fetchHandlerParams = "certificateType"))
    )
    @Comment("证书类型")
    @ApiModelProperty("证书类型")
    private String certificateType;

    @EruptField(
            views = @View(title = "有效期至"),
            edit = @Edit(title = "有效期至", type = EditType.DATE, notNull = true,
                    dateType = @DateType))
    @Comment("有效期至")
    @ApiModelProperty("有效期至")
    private java.util.Date validityPeriod;

    @EruptField(
            views = @View(title = "资质证书附件"),
            edit = @Edit(title = "资质证书附件", type = EditType.ATTACHMENT, notNull = true,
                    attachmentType = @AttachmentType(type = AttachmentType.Type.IMAGE, maxLimit = 50, fileTypes = {".jpg", ".bmp", ".png"})))
    @Comment("资质证书附件")
    @ApiModelProperty("资质证书附件")
    private String certificateFile;

    @EruptField(
            views = @View(title = "资质证书更新时间"),
            edit = @Edit(title = "资质证书更新时间", type = EditType.DATE, notNull = true,
                    dateType = @DateType))
    @Comment("资质证书更新时间")
    @ApiModelProperty("资质证书更新时间")
    private java.util.Date certificateTime;

//	@EruptField(
//		views = @View(title = "是否是检查人员"),
//		edit = @Edit(title = "是否是检查人员", type = EditType.BOOLEAN, notNull = true,
//		boolType = @BoolType(trueText = "是", falseText = "否")))
//	@Comment("是否是检查人员")
//	@ApiModelProperty("是否是检查人员")
//	private Boolean checkPersion;

}
