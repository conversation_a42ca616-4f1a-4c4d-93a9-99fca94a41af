/*
* Copyright © 2020-2035 yundingyun All rights reserved.
*/
package com.daliangang.emergency.proxy;

import cn.hutool.json.JSONObject;
import com.daliangang.emergency.entity.Sensitivetargets;
import org.springframework.stereotype.Service;
import xyz.erupt.annotation.fun.DataProxy;
import xyz.erupt.common.http.EruptRemoteUtils;
import xyz.erupt.toolkit.remote.RemoteProxyService;

import javax.annotation.Resource;
import java.util.Map;

@Service
public class SensitivetargetsDataProxy implements DataProxy<Sensitivetargets> {

    @Resource
    private RemoteProxyService remoteProxyService;

    @Override
    public void afterAdd(Sensitivetargets sensitivetargets) {
        JSONObject inputData = new JSONObject();
        inputData.set("clazz", "Sensitivetargets");
        inputData.set("insertData",sensitivetargets);
        EruptRemoteUtils.doPost(remoteProxyService.getMainServerUrl() + "/erupt-api/exchange/data/exchange", inputData, Map.class);
    }

    @Override
    public void afterUpdate(Sensitivetargets sensitivetargets) {
        JSONObject inputData = new JSONObject();
        inputData.set("clazz", "Sensitivetargets");
        inputData.set("insertData",sensitivetargets);
        EruptRemoteUtils.doPost(remoteProxyService.getMainServerUrl() + "/erupt-api/exchange/data/exchange", inputData, Map.class);
    }
}
