package com.daliangang.emergency.entity;

import lombok.Data;
import xyz.erupt.annotation.Erupt;
import xyz.erupt.annotation.EruptField;
import xyz.erupt.annotation.fun.DataProxy;
import xyz.erupt.annotation.sub_erupt.Power;
import xyz.erupt.annotation.sub_field.Edit;
import xyz.erupt.annotation.sub_field.EditType;
import xyz.erupt.annotation.sub_field.View;
import xyz.erupt.annotation.sub_field.sub_edit.Search;
import xyz.erupt.upms.model.base.HyperModel;

import javax.persistence.Entity;
import javax.persistence.Lob;
import javax.persistence.Table;

@Entity
@Table(name = "tb_emergency_wiki")
@Erupt(name = "应急知识库", importTruncate = true, power = @Power(importable = true),orderBy = "EmergencyWiki.updateTime desc")
@Data
public class EmergencyWiki extends HyperModel implements DataProxy<EmergencyWiki> {

    @EruptField(views = @View(title = "货种名称"), edit = @Edit(title = "货种名称", search = @Search(vague = true)))
    private String name;

    @EruptField(views = @View(title = "IMDG中的运输名称"), edit = @Edit(title = "IMDG中的运输名称"))
    private String transName;

    @EruptField(views = @View(title = "UN号"), edit = @Edit(title = "UN号",search = @Search(vague = true )))
    private String un;

    //潜在危险
    @EruptField(edit = @Edit(title = "潜在危险", type = EditType.DIVIDE))
    @Lob
    private String riskDiv;

    //火灾或者爆炸
    @EruptField(views = @View(title = "潜在危险-火灾或者爆炸", show = false), edit = @Edit(title = "火灾或者爆炸", type = EditType.TEXTAREA))
    @Lob
    private String riskFire;

    //健康
    @EruptField(views = @View(title = "潜在危险-健康", show = false), edit = @Edit(title = "健康", type = EditType.TEXTAREA))
    @Lob
    private String riskHealth;

    //公众安全
    @EruptField(edit = @Edit(title = "公众安全", type = EditType.DIVIDE))
    @Lob
    private String safeDiv;

    //通用
    @EruptField(views = @View(title = "公众安全-通用", show = false), edit = @Edit(title = "通用", type = EditType.TEXTAREA))
    @Lob
    private String safeCommon;

    //防护用品
    @EruptField(views = @View(title = "公众安全-防护用品", show = false), edit = @Edit(title = "防护用品", type = EditType.TEXTAREA))
    @Lob
    private String safeProtection;

    // 疏散
    @EruptField(views = @View(title = "公众安全-疏散", show = false), edit = @Edit(title = "疏散", type = EditType.TEXTAREA))
    @Lob
    private String safeEvacuation;

    //应急响应
    @EruptField(edit = @Edit(title = "应急响应", type = EditType.DIVIDE))
    @Lob
    private String responseDiv;

    //工艺控制
    @EruptField(views = @View(title = "应急响应-工艺控制", show = false), edit = @Edit(title = "工艺控制", type = EditType.TEXTAREA))
    @Lob
    private String processControl;

    //火灾通用
    @EruptField(views = @View(title = "应急响应-火灾通用", show = false), edit = @Edit(title = "火灾通用", type = EditType.TEXTAREA))
    @Lob
    private String respFireCommon;

    //轻微火灾
    @EruptField(views = @View(title = "应急响应-轻微火灾", show = false), edit = @Edit(title = "轻微火灾", type = EditType.TEXTAREA))
    @Lob
    private String respFireSmall;

    //重大火灾
    @EruptField(views = @View(title = "应急响应-重大火灾", show = false), edit = @Edit(title = "重大火灾", type = EditType.TEXTAREA))
    @Lob
    private String respFireLarge;

    //槽罐、卡车或拖车货物着火
    @EruptField(views = @View(title = "应急响应-槽罐、卡车或拖车货物着火", show = false), edit = @Edit(title = "槽罐、卡车或拖车货物着火", type = EditType.TEXTAREA))
    @Lob
    private String respFireTruck;

    //溢出或泄漏
    @EruptField(views = @View(title = "应急响应-溢出或泄漏", show = false), edit = @Edit(title = "溢出或泄漏", type = EditType.TEXTAREA))
    @Lob
    private String respOverlow;

    //大量泄漏
    @EruptField(views = @View(title = "应急响应-大量泄漏", show = false), edit = @Edit(title = "大量泄漏", type = EditType.TEXTAREA))
    @Lob
    private String respOverlowLarge;

    //急救
    @EruptField(views = @View(title = "应急响应-急救", show = false), edit = @Edit(title = "急救", type = EditType.TEXTAREA))
    @Lob
    private String respFirstAid;

}
