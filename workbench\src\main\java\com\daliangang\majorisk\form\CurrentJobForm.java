package com.daliangang.majorisk.form;

import com.daliangang.rndpub.proxy.QualificationDataProxy;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.SuperBuilder;
import xyz.erupt.annotation.Erupt;
import xyz.erupt.annotation.EruptField;
import xyz.erupt.annotation.sub_erupt.Power;
import xyz.erupt.annotation.sub_field.Edit;
import xyz.erupt.annotation.sub_field.EditType;
import xyz.erupt.annotation.sub_field.Readonly;
import xyz.erupt.annotation.sub_field.View;
import xyz.erupt.annotation.sub_field.sub_edit.ChoiceType;
import xyz.erupt.annotation.sub_field.sub_edit.DateType;
import xyz.erupt.annotation.sub_field.sub_edit.InputType;
import xyz.erupt.annotation.sub_field.sub_edit.Search;
import xyz.erupt.jpa.model.BaseModel;
import xyz.erupt.toolkit.db.Comment;
import xyz.erupt.toolkit.handler.EruptDictCodeChoiceFetchHandler;
import xyz.erupt.toolkit.handler.SqlChoiceFetchHandler;

import javax.persistence.Entity;
import javax.persistence.Table;
import javax.persistence.Transient;

/**
 * <AUTHOR>
 * @since :2023/4/7:13:39
 */
@Erupt(name = "当前作业情况", power = @Power(add = false, delete = false, export = false, importable = true, viewDetails = false, edit = false)
        , rowOperation = {})

@Getter
@Setter
@Comment("当前作业情况")
@ApiModel("当前作业情况")
public  class  CurrentJobForm extends BaseModel {



    @EruptField(
            views = @View(title = "作业数量",show = false),
            edit = @Edit(title = "装作业数量", type = EditType.INPUT,readonly = @Readonly,
                    inputType = @InputType))
    @Comment("业数量")
    @ApiModelProperty("作业数量")
    private String num;

    @EruptField(
            views = @View(title = "作业位置",show = false),
            edit = @Edit(title = "作业位置", type = EditType.INPUT,readonly = @Readonly,
                    inputType = @InputType))
    @Comment("作业位置")
    @ApiModelProperty("作业位置")
    private String address;



    @EruptField(
            views = @View(title = "作业类型",show = false),
            edit = @Edit(title = "作业类型", type = EditType.INPUT,readonly = @Readonly,
                    inputType = @InputType))
    @Comment("作业类型")
    @ApiModelProperty("作业类型")
    private String type;

}
