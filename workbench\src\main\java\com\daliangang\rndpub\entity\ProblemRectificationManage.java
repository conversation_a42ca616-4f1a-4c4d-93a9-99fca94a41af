package com.daliangang.rndpub.entity;

import com.daliangang.rndpub.proxy.ProblemRectificationManageProxy;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import xyz.erupt.annotation.Erupt;
import xyz.erupt.annotation.EruptField;
import xyz.erupt.annotation.sub_erupt.Power;
import xyz.erupt.annotation.sub_field.Edit;
import xyz.erupt.annotation.sub_field.EditType;
import xyz.erupt.annotation.sub_field.View;
import xyz.erupt.annotation.sub_field.ViewType;
import xyz.erupt.annotation.sub_field.sub_edit.DateType;
import xyz.erupt.toolkit.db.Comment;
import xyz.erupt.upms.model.auth.DataAuthModel;

import javax.persistence.Entity;
import javax.persistence.Table;

@Erupt(name = "问题整改管理", importTruncate = true, power = @Power(add = false, delete = false, export = false, importable = false, viewDetails = false, edit = false)
        ,orderBy = "inspectionDate desc"
        , dataProxy = ProblemRectificationManageProxy.class
        , rowOperation = {

})
@Getter
@Setter
@Table(name = "tb_company_inspection_problem")
@Entity
public class ProblemRectificationManage extends DataAuthModel {

    @EruptField(
            views = @View(title = "检查名称",type = ViewType.LINK_HREF,linkName="Rectification",condition="[{'code':'name','searchCode':'inspectionName'}]"),
            edit = @Edit(title = "检查名称", type = EditType.INPUT))
    private String name;

    @EruptField(
            views = @View(title = "检查问题数量",type = ViewType.LINK_HREF,linkName="Rectification",condition="[{'code':'name','searchCode':'inspectionName'}]"),
            edit = @Edit(title = "检查问题数量", type = EditType.INPUT))
    private String publishQuestionNum;

    @EruptField(
            views = @View(title = "整改通过问题数量",type = ViewType.LINK_HREF,linkName="Rectification",condition="[{'code':'name','searchCode':'inspectionName'}]"),
            edit = @Edit(title = "整改通过问题数量", type = EditType.INPUT))
    private String passResult;

    @EruptField(
            views = @View(title = "待整改问题数量",type = ViewType.LINK_HREF,linkName="Rectification",condition="[{'code':'name','searchCode':'inspectionName'}]"),
            edit = @Edit(title = "待整改问题数量", type = EditType.INPUT))
    private String rectifiedResult;

    @EruptField(
            views = @View(title = "已逾期问题数量",type = ViewType.LINK_HREF,linkName="Rectification",condition="[{'code':'name','searchCode':'inspectionName'}]"),
            edit = @Edit(title = "已逾期问题数量", type = EditType.INPUT))
    private String overDue;

    @EruptField(
            views = @View(title = "检查开始日期", show = false),
            edit = @Edit(title = "检查开始日期", type = EditType.DATE, notNull = true,
                    dateType = @DateType))
    @Comment("检查开始日期")
    @ApiModelProperty("检查开始日期")
    private java.util.Date inspectionDate;
}
