package com.daliangang.majorisk.operation;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson2.JSON;
import com.daliangang.core.DaliangangContext;
import com.daliangang.majorisk.entity.Unload;
import com.daliangang.rndpub.entity.Procedure;
import com.daliangang.workbench.entity.Enterprise;
import org.apache.poi.hssf.usermodel.HSSFCell;
import org.apache.poi.hssf.usermodel.HSSFRow;
import org.apache.poi.hssf.usermodel.HSSFSheet;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.util.CellRangeAddress;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.*;
import xyz.erupt.annotation.fun.OperationHandler;
import xyz.erupt.core.module.MetaUserinfo;
import xyz.erupt.core.util.EruptSpringUtil;
import xyz.erupt.jpa.dao.EruptDao;
import xyz.erupt.toolkit.db.EruptResultMap;
import xyz.erupt.toolkit.utils.EruptDaoUtils;
import xyz.erupt.toolkit.utils.FileUtils;
import xyz.erupt.toolkit.utils.SqlUtils;
import xyz.erupt.upms.model.EruptOrg;
import xyz.erupt.upms.model.EruptUser;
import xyz.erupt.upms.model.template.EruptRoleTemplateUser;
import xyz.erupt.upms.model.template.EruptRoleTemplateUserService;
import xyz.erupt.upms.service.EruptSessionService;
import xyz.erupt.upms.service.EruptUserService;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.transaction.Transactional;
import java.io.*;
import java.net.URL;
import java.net.URLEncoder;
import java.text.SimpleDateFormat;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;


@Service
@Transactional
public class ExportStatic implements OperationHandler<Unload,Void> {
    @Override
    public String exec(List<Unload> data, Void unused, String[] param) {


//        //风险类型统计
//        String url = "window.open('/erupt-api/typeOfRisk')";
//        return url;
//        //企业作业数量统计
//        String url = "window.open('/erupt-api/businessOperations')";
//        return url;
        //作业风险数量统计
        String url = "window.open('/erupt-api/operationalRisk')";
        return url;



    }
    @RestController
    @Transactional
    public static class ExportStaticController {

        @Resource
        private EruptDao eruptDao;

        @Resource
        private EruptUserService eruptUserService;
        @Resource
        private EruptSessionService sessionService;
        @Resource
        private EruptRoleTemplateUserService eruptRoleTemplateUserService;


        //根据token查找用户
        public EruptUser getUser(String token){
            Object info = sessionService.get("erupt-auth:user:" + token);
            MetaUserinfo metaUserinfo = info == null ? null : JSON.parseObject(info.toString(), MetaUserinfo.class);
            Long uid= null == metaUserinfo ? null : metaUserinfo.getId();
            EruptUser eruptUser = eruptUserService.getEruptUser(uid);
            return eruptUser;
        }
        //判断用户角色
        public String judgeUser(EruptUser eruptUser){
            EruptRoleTemplateUser eruptRoleTemplateUser = eruptRoleTemplateUserService.queryByAccount(eruptUser.getAccount());
            String baseRole = eruptRoleTemplateUser.getTemplate().getBaseRole();
            if(baseRole.equals("101") || baseRole.equals("102")){
                return "Department";
            }
            return "Enterprise";
        }

        //风险类型统计
        @GetMapping("/erupt-api/typeOfRisk")
        public void typeOfRisk(HttpServletRequest req, HttpServletResponse resp,@RequestParam("token")String token) throws UnsupportedEncodingException {
            //设置请求的编码格式
            req.setCharacterEncoding("UTF-8");
            //防止乱码，客户端和服务端解析编码要相同
            resp.setContentType("text/html;charset=UTF-8");
            BufferedOutputStream bos=null;
            try {
                resp.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
                resp.setHeader("Content-Disposition","attachment;fileName="+ java.net.URLEncoder.encode("风险类型统计表" + ".xlsx",  "UTF-8"));
                bos= new BufferedOutputStream(resp.getOutputStream());
                //创建poi对象
                //创建HSSFWorkbook对象
                HSSFWorkbook wb = new HSSFWorkbook();
                //建立sheet对象
                HSSFSheet sheet = wb.createSheet("风险类型统计表");
                //在sheet里创建第一行，参数为行索引
                HSSFRow row1 = sheet.createRow(0);
                //创建单元格
                HSSFCell cell = row1.createCell(0);
                //设置单元格内容
                cell.setCellValue("风险类型统计表");
                //合并单元格CellRangeAddress构造参数依次表示起始行，截至行，起始列， 截至列
                sheet.addMergedRegion(new CellRangeAddress(0, 0, 0, 3));
                //在sheet里创建第二行
                int rowNum=1;
                HSSFRow row2 = sheet.createRow(rowNum++);
                //创建单元格并设置单元格内容
                row2.createCell(0).setCellValue("序号");
                row2.createCell(1).setCellValue("风险类型");
                row2.createCell(2).setCellValue("历史风险数量");
                row2.createCell(3).setCellValue("现有风险数量");
                //在sheet里创建第三行
                String replaceSql="";
                EruptUser user = getUser(token);
                String judgeUser = judgeUser(user);
                if (judgeUser.equals("Department")) {
                    //当前登录人是政府
                    List<String> auth = eruptUserService.getUserAuth(user);
                    if (auth != null && !auth.isEmpty()) {
                        replaceSql = "and company "+ SqlUtils.wrapIn(auth) ;
                    }
                } else if (judgeUser.equals("Enterprise")) {
                    //当前登录人是企业
                    EruptOrg eruptOrg = user.getEruptOrg();
                    if(null != eruptOrg){
                        String orgCode = eruptOrg.getCode();
                        replaceSql = "and company = "+ SqlUtils.wrapStr(orgCode) ;
                    }
                }
                String selectSql="SELECT\n" +
                        "\tname '风险类型',\n" +
                        "\tsum( process ) '现有风险数量',\n" +
                        "\tsum( finished ) '历史风险数量' \n" +
                        "FROM\n" +
                        "(SELECT\n" +
                        "\t\ts3.`NAME`,\n" +
                        "\t\tcount(\n" +
                        "\t\tIF\n" +
                        "\t\t( now() BETWEEN s.ab_time AND s.ae_time, TRUE, NULL )) process,\n" +
                        "\t\tcount(\n" +
                        "\t\tIF\n" +
                        "\t\t( now() > s.ae_time, TRUE, NULL )) finished \n" +
                        "FROM\n" +
                        "\t\ttb_unload s,\n" +
                        "\t\t(SELECT id,risk_type FROM tb_risk_database_enterprise) s2,\n" +
                        "\t\t(\tSELECT\n" +
                        "\t\t\t\ti.NAME NAME,\n" +
                        "\t\t\t\ti.`code` CODE \n" +
                        "\t\t\tFROM\n" +
                        "\t\t\t\te_dict d\n" +
                        "\t\t\t\tINNER JOIN e_dict_item i ON d.id = i.erupt_dict_id\n" +
                        "\t\t\tAND d.`code` = 'riskType'\t) s3\n" +
                        "WHERE s.risk_database=s2.id\tand s2.risk_type=s3.`code`\n" +
                        "#REPLACESQL" +
                        "GROUP BY s3.`NAME`\t\t\n" +
                        "UNION ALL\n" +
                        "SELECT\n" +
                        "\t\ts3.`NAME`,\n" +
                        "\t\tcount(\n" +
                        "\t\tIF\n" +
                        "\t\t( now() BETWEEN s.ab_time AND s.ae_time, TRUE, NULL )) process,\n" +
                        "\t\tcount(\n" +
                        "\t\tIF\n" +
                        "\t\t( now() > s.ae_time, TRUE, NULL )) finished \n" +
                        "FROM\n" +
                        "\t\ttb_unload_ship s,\n" +
                        "\t\t(SELECT id,risk_type FROM tb_risk_database_enterprise) s2,\n" +
                        "\t\t(\tSELECT\n" +
                        "\t\t\t\ti.NAME NAME,\n" +
                        "\t\t\t\ti.`code` CODE \n" +
                        "\t\t\tFROM\n" +
                        "\t\t\t\te_dict d\n" +
                        "\t\t\t\tINNER JOIN e_dict_item i ON d.id = i.erupt_dict_id\n" +
                        "\t\t\tAND d.`code` = 'riskType'\t) s3\n" +
                        "WHERE s.risk_database=s2.id\tand s2.risk_type=s3.`code`\n" +
                        "#REPLACESQL" +
                        "GROUP BY s3.`NAME`) k\t\t\n" +
                        "GROUP BY name";
                String finalSql = selectSql.replaceAll("#REPLACESQL", replaceSql);
                List<Map<String,Object>> objects = EruptDaoUtils.selectOnes(finalSql, Map.class);
               //遍历数据
                int no=1;
                for (Map<String, Object> map : objects) {
                    //创建行
                    HSSFRow row = sheet.createRow(rowNum++);
                    int column=0;
                    row.createCell(column++).setCellValue(no++);
                    //将列依次放入数据
                    for (Object value : map.values()) {
                        if(column==1){
                            row.createCell(column++).setCellValue(String.valueOf(value));
                        }else{
                            row.createCell(column++).setCellValue(Integer.valueOf(String.valueOf(value)));
                        }
                    }
                }

                //文件输出流
                wb.write(bos);
            }catch (Exception e){
               e.printStackTrace();
            }finally {
                try {
                    if(bos!=null){
                        bos.close();
                    }
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
        //企业作业数量统计
        @GetMapping("/erupt-api/businessOperations")
        public void businessOperations(HttpServletRequest req, HttpServletResponse resp,@RequestParam("token")String token) throws UnsupportedEncodingException {
            //设置请求的编码格式
            req.setCharacterEncoding("UTF-8");
            //防止乱码，客户端和服务端解析编码要相同
            resp.setContentType("text/html;charset=UTF-8");
            BufferedOutputStream bos=null;
            try {
                resp.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
                resp.setHeader("Content-Disposition","attachment;fileName="+ java.net.URLEncoder.encode("企业作业数量统计表" + ".xlsx",  "UTF-8"));
                bos= new BufferedOutputStream(resp.getOutputStream());
                //创建poi对象
                //创建HSSFWorkbook对象
                HSSFWorkbook wb = new HSSFWorkbook();
                //建立sheet对象
                HSSFSheet sheet = wb.createSheet("企业作业数量统计");
                //在sheet里创建第一行，参数为行索引
                HSSFRow row1 = sheet.createRow(0);
                //创建单元格
                HSSFCell cell = row1.createCell(0);
                //设置单元格内容
                cell.setCellValue("企业作业数量统计");
                //合并单元格CellRangeAddress构造参数依次表示起始行，截至行，起始列， 截至列
                sheet.addMergedRegion(new CellRangeAddress(0, 0, 0, 5));
                //在sheet里创建第二行
                int rowNum=1;
                HSSFRow row2 = sheet.createRow(rowNum++);
                //创建单元格并设置单元格内容
                row2.createCell(0).setCellValue("序号");
                row2.createCell(1).setCellValue("企业名称");
                row2.createCell(2).setCellValue("作业总数");
                row2.createCell(3).setCellValue("已结束作业");
                row2.createCell(4).setCellValue("未开始作业");
                row2.createCell(5).setCellValue("正在进行中作业");
                //在sheet里创建第三行
                String replaceSql="";
                EruptUser user = getUser(token);
                String judgeUser = judgeUser(user);
                if (judgeUser.equals("Department")) {
                    //当前登录人是政府
                    List<String> auth = eruptUserService.getUserAuth(user);
                    if (auth != null && !auth.isEmpty()) {
                        replaceSql = "and company "+ SqlUtils.wrapIn(auth) ;
                    }
                } else if (judgeUser.equals("Enterprise")) {
                    //当前登录人是企业
                    EruptOrg eruptOrg = user.getEruptOrg();
                    if(null != eruptOrg){
                        String orgCode = eruptOrg.getCode();
                        replaceSql = "and company = "+ SqlUtils.wrapStr(orgCode) ;
                    }
                }
                String selectSql="SELECT\n" +
                        "\te.NAME '企业名称',\n" +
                        "\tifnull( t.unstart, 0 ) '未开始作业',\n" +
                        "\tifnull( t.progress, 0 ) '正在进行中作业',\n" +
                        "\tifnull( t.finish, 0 ) '已结束作业',\n" +
                        "\tifnull( t.total, 0 ) '作业总数' \n" +
                        "FROM\n" +
                        "\ttb_enterprise e\n" +
                        "\tLEFT JOIN (\n" +
                        "\tSELECT\n" +
                        "\t\torg_code,\n" +
                        "\t\tSUM( unstart ) unstart,\n" +
                        "\t\tSUM( progress ) progress,\n" +
                        "\t\tSUM( finish ) finish,\n" +
                        "\t\tSUM( total ) total \n" +
                        "\tFROM\n" +
                        "\t\t(\n" +
                        "\t\tSELECT\n" +
                        "\t\t\torg_code,\n" +
                        "\t\t\tcount( IF ( now() < ab_time, TRUE, NULL )) unstart,\n" +
                        "\t\t\tcount( IF ( now() BETWEEN ab_time AND ae_time, TRUE, NULL )) progress,\n" +
                        "\t\t\tcount( IF ( now() > ae_time, TRUE, NULL )) finish,\n" +
                        "\t\t\tcount(*) total \n" +
                        "\t\tFROM\n" +
                        "\t\t\ttb_unload_ship \n" +
                        "\t\t\twhere 1 =1 \n" +
                        "#REPLACESQL" +
                        "\t\tGROUP BY\n" +
                        "\t\t\torg_code UNION \n" +
                        "\n" +
                        "\t\tSELECT\n" +
                        "\t\t\torg_code,\n" +
                        "\t\t\tcount( IF ( now() < ab_time, TRUE, NULL )) unstart,\n" +
                        "\t\t\tcount( IF ( now() BETWEEN ab_time AND ae_time, TRUE, NULL )) progress,\n" +
                        "\t\t\tcount( IF ( now() > ae_time, TRUE, NULL )) finish,\n" +
                        "\t\t\tcount(*) total \n" +
                        "\t\tFROM\n" +
                        "\t\t\ttb_unload \n" +
                        "\t\t\twhere 1 =1 \n" +
                        "#REPLACESQL" +
                        "\t\tGROUP BY\n" +
                        "\t\t\torg_code UNION \n" +
                        "\t\tSELECT\n" +
                        "\t\t\torg_code,\n" +
                        "\t\t\tcount( IF ( now() < star_date, TRUE, NULL )) unstart,\n" +
                        "\t\t\tcount( IF ( now() BETWEEN star_date AND end_date, TRUE, NULL )) progress,\n" +
                        "\t\t\tcount( IF ( now() > end_date, TRUE, NULL )) finish,\n" +
                        "\t\t\tcount(*) total \n" +
                        "\t\tFROM\n" +
                        "\t\t\ttb_maintenance \n" +
                        "\t\t\twhere 1 =1 \n" +
                        "#REPLACESQL" +
                        "\t\tGROUP BY\n" +
                        "\t\t\torg_code \n" +
                        "\t\t) t \n" +
                        "\tGROUP BY\n" +
                        "\torg_code \n" +
                        "\t) t ON e.org_code = t.org_code";
                String finalSql = selectSql.replaceAll("#REPLACESQL", replaceSql);
                List<Map<String,Object>> objects = EruptDaoUtils.selectOnes(finalSql, Map.class);
               //遍历数据
                int no=1;
                for (Map<String, Object> map : objects) {
                    //创建行
                    HSSFRow row = sheet.createRow(rowNum++);
                    int column=0;
                    row.createCell(column++).setCellValue(no++);
                    //将列依次放入数据
                    for (Object value : map.values()) {
                        if(column==1){
                            row.createCell(column++).setCellValue(String.valueOf(value));
                        }else{
                            row.createCell(column++).setCellValue(Integer.valueOf(String.valueOf(value)));
                        }
                    }
                }

                //文件输出流
                wb.write(bos);
            }catch (Exception e){
               e.printStackTrace();
            }finally {
                try {
                    if(bos!=null){
                        bos.close();
                    }
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
        //作业风险数量统计
        @GetMapping("/erupt-api/operationalRisk")
        public void operationalRisk(HttpServletRequest req, HttpServletResponse resp,@RequestParam("token")String token) throws UnsupportedEncodingException {
            //设置请求的编码格式
            req.setCharacterEncoding("UTF-8");
            //防止乱码，客户端和服务端解析编码要相同
            resp.setContentType("text/html;charset=UTF-8");
            BufferedOutputStream bos=null;
            try {
                resp.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
                resp.setHeader("Content-Disposition","attachment;fileName="+ java.net.URLEncoder.encode("作业风险数量统计表" + ".xlsx",  "UTF-8"));
                bos= new BufferedOutputStream(resp.getOutputStream());
                //创建poi对象
                //创建HSSFWorkbook对象
                HSSFWorkbook wb = new HSSFWorkbook();
                //建立sheet对象
                HSSFSheet sheet = wb.createSheet("作业风险数量统计");
                //在sheet里创建第一行，参数为行索引
                HSSFRow row1 = sheet.createRow(0);
                //创建单元格
                HSSFCell cell = row1.createCell(0);
                //设置单元格内容
                cell.setCellValue("作业风险数量统计");
                //合并单元格CellRangeAddress构造参数依次表示起始行，截至行，起始列， 截至列
                sheet.addMergedRegion(new CellRangeAddress(0, 0, 0, 3));
                //在sheet里创建第二行
                int rowNum=1;
                HSSFRow row2 = sheet.createRow(rowNum++);
                //创建单元格并设置单元格内容
                row2.createCell(0).setCellValue("序号");
                row2.createCell(1).setCellValue("作业类型");
                row2.createCell(2).setCellValue("历史风险数量");
                row2.createCell(3).setCellValue("现有风险数量");
                //在sheet里创建第三行
                String replaceSql="";
                EruptUser user = getUser(token);
                String judgeUser = judgeUser(user);
                if (judgeUser.equals("Department")) {
                    //当前登录人是政府
                    List<String> auth = eruptUserService.getUserAuth(user);
                    if (auth != null && !auth.isEmpty()) {
                        replaceSql = "and company "+ SqlUtils.wrapIn(auth) ;
                    }
                } else if (judgeUser.equals("Enterprise")) {
                    //当前登录人是企业
                    EruptOrg eruptOrg = user.getEruptOrg();
                    if(null != eruptOrg){
                        String orgCode = eruptOrg.getCode();
                        replaceSql = "and company = "+ SqlUtils.wrapStr(orgCode) ;
                    }
                }
                String selectSql="SELECT\n" +
                        "\ti.NAME '作业类型',\n" +
                        "\tcount( IF ( now() BETWEEN ab_time AND ae_time, TRUE, NULL )) '现有风险数量',\n" +
                        "\tcount( IF ( now() > ae_time, TRUE, NULL )) '历史风险数量' \n" +
                        "FROM\n" +
                        "\te_dict d,\n" +
                        "\te_dict_item i,\n" +
                        "\ttb_unload_ship t \n" +
                        "WHERE\n" +
                        "\td.id = i.erupt_dict_id \n" +
                        "\tAND d.CODE = 'workTypeShip' \n" +
                        "\tAND t.work_type = i.`code` \n" +
                        "#REPLACESQL" +
                        "\tgroup by t.work_type,i.name union all\n" +
                        "\t\n" +
                        "\t\n" +
                        "\tSELECT\n" +
                        "\ti.NAME '作业类型',\n" +
                        "\tcount( IF ( now() BETWEEN ab_time AND ae_time, TRUE, NULL )) '现有风险数量',\n" +
                        "\tcount( IF ( now() > ae_time, TRUE, NULL )) '历史风险数量' \n" +
                        "FROM\n" +
                        "\te_dict d,\n" +
                        "\te_dict_item i,\n" +
                        "\ttb_unload t \n" +
                        "WHERE\n" +
                        "\td.id = i.erupt_dict_id \n" +
                        "\tAND d.CODE = 'workType' \n" +
                        "\tAND t.work_type = i.`code` \n" +
                        "#REPLACESQL" +
                        "\tgroup by t.work_type,i.name union all\n" +
                        "\t\n" +
                        "\tSELECT\n" +
                        "\ti.NAME '作业类型',\n" +
                        "\tcount( IF ( now() BETWEEN star_date AND end_date, TRUE, NULL )) '现有风险数量' ,\n" +
                        "\tcount( IF ( now() > end_date, TRUE, NULL )) '历史风险数量' \n" +
                        "FROM\n" +
                        "\te_dict d,\n" +
                        "\te_dict_item i,\n" +
                        "\ttb_maintenance t \n" +
                        "WHERE\n" +
                        "\td.id = i.erupt_dict_id \n" +
                        "\tAND d.CODE = 'MaintenanceType' \n" +
                        "\tAND t.type = i.`code` \n" +
                        "#REPLACESQL" +
                        "\tgroup by t.type,i.name \n";
                String finalSql = selectSql.replaceAll("#REPLACESQL", replaceSql);
                System.out.println(finalSql);
                List<Map<String,Object>> objects = EruptDaoUtils.selectOnes(finalSql, Map.class);
                //遍历数据
                int no=1;
                for (Map<String, Object> map : objects) {
                    //创建行
                    HSSFRow row = sheet.createRow(rowNum++);
                    int column=0;
                    row.createCell(column++).setCellValue(no++);
                    //将列依次放入数据
                    for (Object value : map.values()) {
                        if(column==1){
                            row.createCell(column++).setCellValue(String.valueOf(value));
                        }else{
                            row.createCell(column++).setCellValue(Integer.valueOf(String.valueOf(value)));
                        }
                    }
                }

                //文件输出流
                wb.write(bos);
            }catch (Exception e){
                e.printStackTrace();
            }finally {
                try {
                    if(bos!=null){
                        bos.close();
                    }
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }


    }
}
