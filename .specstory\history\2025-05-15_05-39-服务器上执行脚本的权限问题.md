<!-- Generated by SpecStory -->

# 服务器上执行脚本的权限问题 (2025-05-15 13:39:40)

_**User**_

sudo: ./build-java.sh：找不到命令
[root@erupt-ui workbench]# pwd
/home/<USER>/builds/x6JQ7D9U/0/daliangang/backend/daliangang-workbench/workbench
[root@erupt-ui workbench]# ./build-java.sh
-bash: ./build-java.sh: 权限不够
[root@erupt-ui workbench]# 我在服务器上，为啥会报错呢，我是root


---

_**Assistant**_



---

收到了，亲爱的同学。

```思考过程分析
# 问题诊断
我看到您在服务器上尝试执行 build-java.sh 脚本时遇到了两个错误：
1. 首先是"找不到命令"错误
2. 然后是"权限不够"错误

这表明：
- 脚本文件可能存在但没有执行权限
- 您当前在 /home/<USER>/builds/x6JQ7D9U/0/daliangang/backend/daliangang-workbench/workbench 目录
- 虽然您使用 root 用户，但仍遇到权限问题
```

您需要检查两件事：

1. 确认 build-java.sh 是否存在：
```
ls -la | grep build-java.sh
```

2. 如果文件存在，给它添加执行权限：
```
chmod +x build-java.sh
```

如果文件不存在，可能需要确认正确的脚本名称。我注意到您的项目中有一个 build-image.sh 文件，而不是 build-java.sh。

也可能是脚本所在的文件系统被以只读方式挂载，或者有特殊的安全限制（如SELinux）阻止了执行。

请尝试以上步骤，然后告诉我结果。

---

