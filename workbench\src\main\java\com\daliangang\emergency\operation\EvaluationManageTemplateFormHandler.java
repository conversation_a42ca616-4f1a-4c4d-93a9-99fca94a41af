/*
 * Copyright © 2020-2035 yundingyun All rights reserved.
 */
package com.daliangang.emergency.operation;

import com.daliangang.emergency.entity.EvaluationManage;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;
import xyz.erupt.annotation.fun.OperationHandler;
import xyz.erupt.toolkit.utils.FileUtils;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.transaction.Transactional;
import java.io.*;
import java.net.URL;
import java.net.URLEncoder;
import java.util.List;

@Service
public class EvaluationManageTemplateFormHandler implements OperationHandler<EvaluationManage, Void> {

    @Override
    public String exec(List<EvaluationManage> data, Void unused, String[] param) {
//        EvaluationManage model = data.get(0);
//        String link = model.getClass().getSimpleName() + "/" + model.getId();
//        String link = "应急救援能力评估表";
        String url = "window.open('/erupt-api/download/evaluationManageTemplateForm ','_blank')";
        return url;
    }

    @RestController
    @Transactional
    public static class EvaluationManageTemplateFormController {


        @GetMapping("/erupt-api/download/evaluationManageTemplateForm")
        public void getFaRenWeiTuoShu(HttpServletRequest req, HttpServletResponse resp) throws UnsupportedEncodingException {
            //设置请求的编码格式
            req.setCharacterEncoding("UTF-8");
            //防止乱码，客户端和服务端解析编码要相同
            resp.setContentType("text/html;charset=UTF-8");
            //获取文件地址
//            String attachments = "/2023-05-10/应急救援能力评估表-5287a.doc";
            URL url = null;
            InputStream inputStream =null;
            BufferedInputStream bis = null;
            BufferedOutputStream bos=null;
            try {

                inputStream = FileUtils.readInputStream("tpl/应急能力评估不符合项记录表.docx");
                //文件输出流
                bis= new BufferedInputStream(inputStream);
                resp.setContentType("application/vnd.openxmlformats-officedocument.wordprocessingml.document");
                resp.setHeader("Content-Disposition","attachment;fileName="+ URLEncoder.encode("应急能力评估不符合项记录表" + ".docx",  "UTF-8"));
                bos= new BufferedOutputStream(resp.getOutputStream());
                int byteRead = 0;
                byte[] buffer = new byte[8192];
                while ((byteRead = bis.read(buffer, 0, 8192)) != -1) {
                    bos.write(buffer, 0, byteRead);
                }
            }catch (Exception e){
                System.out.println(e);
            }finally {
                try {
                    if(inputStream!=null){
                        inputStream.close();
                    }
                    if(bis!=null){
                        bis.close();
                    }
                    if(bos!=null){
                        bos.close();
                    }
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }

    }

}
