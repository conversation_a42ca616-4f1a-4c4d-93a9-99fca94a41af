/*
* Copyright © 2020-2035 yundingyun All rights reserved.
*/
package com.daliangang.rndpub.proxy;

import cn.hutool.json.JSONUtil;
import com.daliangang.rndpub.entity.Inspector;
import com.daliangang.rndpub.entity.Procedure;
import com.daliangang.rndpub.operation.InspectorManualSelectionHandler;
import org.springframework.stereotype.Service;
import xyz.erupt.annotation.fun.DataProxy;
import xyz.erupt.annotation.sub_erupt.PageModel;
import xyz.erupt.core.context.MetaDrill;
import xyz.erupt.jpa.dao.EruptDao;
import xyz.erupt.toolkit.redismq.RedisMQService;
import xyz.erupt.toolkit.service.EruptCacheRedis;

import javax.annotation.Resource;
import java.util.List;

@Service
public class InspectorDataProxy implements DataProxy<Inspector>, PageModel.PageModelProxy  {
    @Resource
    private EruptDao eruptDao;

    @Resource
    private EruptCacheRedis eruptCacheRedis;

    @Resource
    private RedisMQService redisMQService;
    @Resource
    private InspectorManualSelectionHandler inspectorManualSelectionHandler;
//    @Override
//    public void searchCondition(Map<String, Object> condition) {
//        condition.put("state", true);
//    }


    public String getCacheKey(){
        long drillId = MetaDrill.getDrillId();
        return Procedure.class.getSimpleName() +"-"+ Inspector.class.getSimpleName() +"-"+ drillId + ":";
    }
    @Override
    public void open() {
//        把未抽取前的数据放入缓存
        long drillId = MetaDrill.getDrillId();
        List<Inspector> list = eruptDao.queryEntityList(Inspector.class, "procedure_id = " + drillId);
        String key = this.getCacheKey();
        String str = JSONUtil.toJsonStr(list);
        redisMQService.produce(key,str);
    }

    @Override
    public void close() {
        //关闭，就还原数据
        long drillId = MetaDrill.getDrillId();
        String key = this.getCacheKey();
        String json = redisMQService.consume(key, String.class);
        List<Inspector> list = JSONUtil.toList(json, Inspector.class);
        list.forEach(item ->{
            eruptDao.mergeAndFlush(item) ;
        });
        eruptCacheRedis.getStringRedisTemplate().delete(key);
        //还原条数
        Procedure procedure = eruptDao.queryEntity(Procedure.class, "id=" + drillId);
        inspectorManualSelectionHandler.updateCountAndName(procedure);
    }
}
