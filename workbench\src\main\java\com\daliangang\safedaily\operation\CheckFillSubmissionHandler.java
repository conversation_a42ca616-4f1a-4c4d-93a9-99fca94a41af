/*
 * Copyright © 2020-2035 yundingyun All rights reserved.
 */
package com.daliangang.safedaily.operation;

import cn.hutool.json.JSONObject;
import com.daliangang.safedaily.entity.CheckFill;
import org.springframework.stereotype.Service;
import xyz.erupt.annotation.fun.OperationHandler;
import xyz.erupt.common.http.EruptRemoteUtils;
import xyz.erupt.core.module.MetaUserinfo;
import xyz.erupt.core.util.EruptSpringUtil;
import xyz.erupt.jpa.dao.EruptDao;
import xyz.erupt.toolkit.remote.RemoteProxyService;
import xyz.erupt.toolkit.utils.AssertUtils;
import xyz.erupt.toolkit.utils.NotifyUtils;

import javax.annotation.Resource;
import javax.transaction.Transactional;
import java.util.List;
import java.util.Map;

@Service
public class CheckFillSubmissionHandler implements OperationHandler<CheckFill, Void> {

    @Resource
    private RemoteProxyService remoteProxyService;

    @Resource
    private EruptDao eruptDao;

    @Override
    @Transactional
    public String exec(List<CheckFill> data, Void unused, String[] param) {
        MetaUserinfo userinfo = EruptSpringUtil.getBean(RemoteProxyService.class).getRemoteUserInfo();

        for (CheckFill checkFill : data) {
            AssertUtils.isFalse(checkFill.getSubmitted(), "请不要重复提交");
            checkFill.setSubmitted(true);
            eruptDao.merge(checkFill);

            JSONObject inputData = new JSONObject();
            inputData.set("clazz", "CheckFill");
            inputData.set("insertData",checkFill);
            EruptRemoteUtils.doPost(remoteProxyService.getMainServerUrl() + "/erupt-api/exchange/data/exchange", inputData, Map.class);
        }
        return NotifyUtils.getSuccessNotify("提交成功");
    }
}
