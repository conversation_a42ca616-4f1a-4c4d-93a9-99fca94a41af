[mysqld]
# 基本配置
default-storage-engine=INNODB
character-set-server=utf8mb4
collation-server=utf8mb4_unicode_ci
init_connect='SET NAMES utf8mb4'

# 连接配置
max_connections=1000
max_connect_errors=1000
wait_timeout=28800
interactive_timeout=28800

# 缓存配置
innodb_buffer_pool_size=256M
innodb_log_file_size=64M
innodb_log_buffer_size=16M
innodb_flush_log_at_trx_commit=2

# 安全配置
sql_mode=STRICT_TRANS_TABLES,NO_ZERO_DATE,NO_ZERO_IN_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_AUTO_CREATE_USER,NO_ENGINE_SUBSTITUTION
local_infile=0

# 时区配置
default-time-zone='+08:00'

# 日志配置
general_log=0
slow_query_log=1
slow_query_log_file=/var/lib/mysql/slow.log
long_query_time=2

[mysql]
default-character-set=utf8mb4

[client]
default-character-set=utf8mb4
