/*
* Copyright © 2020-2035 yundingyun All rights reserved.
*/
package com.daliangang.emergency.proxy;

import cn.hutool.json.JSONObject;
import com.daliangang.core.DaliangangContext;
import com.daliangang.emergency.entity.Drill;
import org.springframework.stereotype.Service;
import xyz.erupt.annotation.fun.DataProxy;
import xyz.erupt.annotation.query.Condition;
import xyz.erupt.common.http.EruptRemoteUtils;
import xyz.erupt.jpa.dao.EruptDao;
import xyz.erupt.toolkit.remote.RemoteProxyService;
import xyz.erupt.upms.model.EruptUser;
import xyz.erupt.upms.service.EruptUserService;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Map;

@Service
public class DrillDataProxy implements DataProxy<Drill> {

    @Resource
    private RemoteProxyService remoteProxyService;
    @Resource
    private EruptUserService eruptUserService;

    @Resource
    private EruptDao eruptDao;

    @Override
    public String beforeFetch(List<Condition> conditions) {
//        if (DaliangangContext.isDepartmentUser()) return "submitted=true";
        return DataProxy.super.beforeFetch(conditions);
    }

    @Override
    public void addBehavior(Drill drill) {
        drill.setFillingDate(new Date());
//        drill.setSubmitted(Boolean.FALSE);
        drill.setSubmitted(Boolean.TRUE);
    }

    @Override
    public void beforeAdd(Drill drill) {
//        List<Drill> drillList = eruptDao.queryEntityList(Drill.class, "company='" + drill.getCompany() + "'");
//        drillList = drillList.stream().filter(d -> d.getFillingDate()!=null&&FindDateUtil.isThisQuarter(d.getFillingDate())).collect(Collectors.toList());
//        if(drillList.size()>0){
//            NotifyUtils.showErrorMsg("【"+drill.getCompany()+"】本月数据已经添加，请勿重复添加！");
//        }
    }

    @Override
    public void afterAdd(Drill drill) {
        //推送数据
        JSONObject inputData = new JSONObject();
        inputData.set("clazz", "Drill");
        inputData.set("insertData",drill);
        EruptRemoteUtils.doPost(remoteProxyService.getMainServerUrl() + "/erupt-api/exchange/data/exchange", inputData, Map.class);
    }

    @Override
    public void afterUpdate(Drill drill) {
        //推送数据
        JSONObject inputData = new JSONObject();
        inputData.set("clazz", "Drill");
        inputData.set("insertData",drill);
        EruptRemoteUtils.doPost(remoteProxyService.getMainServerUrl() + "/erupt-api/exchange/data/exchange", inputData, Map.class);
    }

    @Override
    public void searchCondition(Map<String, Object> condition) {
        if(!DaliangangContext.isDepartmentUser()){
            EruptUser user = eruptUserService.getCurrentEruptUser();
            if (user != null && user.getEruptOrg() != null)
                condition.put("company", user.getEruptOrg().getCode());
        }
    }
}
