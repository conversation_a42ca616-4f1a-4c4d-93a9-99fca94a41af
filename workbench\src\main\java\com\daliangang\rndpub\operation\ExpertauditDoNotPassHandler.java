/*
* Copyright © 2020-2035 yundingyun All rights reserved.
*/
package com.daliangang.rndpub.operation;

import com.daliangang.rndpub.RndpubConst;
import com.daliangang.rndpub.entity.Expertaudit;
import org.springframework.stereotype.Service;
import xyz.erupt.annotation.fun.OperationHandler;
import xyz.erupt.jpa.dao.EruptDao;

import javax.annotation.Resource;
import javax.transaction.Transactional;
import java.util.List;

@Service
public class ExpertauditDoNotPassHandler implements OperationHandler<Expertaudit, Void> {
    @Resource
    private EruptDao eruptDao;
   @Override
   @Transactional
   public String exec(List<Expertaudit> data, Void unused, String[] param) {
       for(Expertaudit expertaudit : data){
           expertaudit.setExpertAudidtState(RndpubConst.EXPERT_STATUS_NOT_PASS);
           eruptDao.merge(expertaudit);
       }
       return null ;
	}
}