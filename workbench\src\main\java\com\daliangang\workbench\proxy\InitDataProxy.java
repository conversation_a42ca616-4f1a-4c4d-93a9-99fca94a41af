/*
 * Copyright © 2020-2035 yundingyun All rights reserved.
 */
package com.daliangang.workbench.proxy;

import cn.hutool.core.thread.ThreadUtil;
import com.daliangang.device.entity.*;
import com.daliangang.emergency.entity.*;
import com.daliangang.majorisk.entity.RiskDatabaseDepartment;
import com.daliangang.rndpub.entity.InspectionItemsManagement;
import com.daliangang.safedaily.entity.MSDS;
import com.daliangang.safedaily.entity.PortSafeWikiDir;
import com.daliangang.safedaily.entity.PortSafeWikiEntry;
import com.daliangang.safedaily.entity.StandardizationItem;
import com.daliangang.training.controller.RedisProducer;
import com.daliangang.workbench.entity.EnterpriseRelevancy;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import xyz.erupt.excel.controller.EruptExcelController;
import xyz.erupt.rider.entity.RiderApplication;
import xyz.erupt.upms.model.template.EruptRoleTemplateUserProxy;
import xyz.erupt.upms.platform.PlatformProxy;

import javax.annotation.Resource;

@Service
@Slf4j
public class InitDataProxy implements PlatformProxy {

    @Resource
    private EruptExcelController eruptExcelController;

    @Resource
    private RedisProducer redisProducer;

    @Resource
    private EruptRoleTemplateUserProxy eruptRoleTemplateUserProxy;


    //导其他系统初始化数据，拆分服务后要注释掉
    @Override
    //@Transactional
    public void init(String[] param) {
        eruptRoleTemplateUserProxy.refreshDataRange(); //暂时不刷新菜单权限

        //系统1
        eruptExcelController.importExcel(EnterpriseRelevancy.class, "public/init/01-04-企业ID与企业名称对应表.xlsx");

        //系统2
        eruptExcelController.importExcel(StandardizationItem.class, "public/init/02-01-标准化得分项.xls");
        eruptExcelController.importExcel(MSDS.class, "public/init/02-02-MSDS.xls");
        eruptExcelController.importExcel(PortSafeWikiDir.class, "public/init/02-03-港口安全知识库目录.xls");
        eruptExcelController.importExcel(PortSafeWikiEntry.class, "public/init/02-04-港口安全知识库文档.xls");

        //系统3
        //eruptExcelController.importExcel(PortArea.class, "public/init/03-01-港区管理.xls");
        eruptExcelController.importExcel(Wharf.class, "public/init/03-02-码头管理.xls");
        eruptExcelController.importExcel(Berth.class, "public/init/03-03-泊位管理.xls");
        eruptExcelController.importExcel(TankFarm.class, "public/init/03-04-罐区管理.xls");
        eruptExcelController.importExcel(Yard.class, "public/init/03-07-堆场管理.xls");
        eruptExcelController.importExcel(Warehouse.class, "public/init/03-08-仓库管理.xls");
        eruptExcelController.importExcel(LoadingDock.class, "public/init/03-09-装卸栈台管理.xls");

        //系统4
        eruptExcelController.importExcel(RiskDatabaseDepartment.class, "public/init/04-01-重大风险数据库.xls");

        //系统5
        eruptExcelController.importExcel(InspectionItemsManagement.class, "public/init/05-01-检查事项管理.xls");

        //系统7
        eruptExcelController.importExcel(Sensitivetargets.class, "public/init/07-01-敏感目标管理.xls");
        eruptExcelController.importExcel(Hospital.class, "public/init/07-02-医院管理.xls");
        eruptExcelController.importExcel(EmergencyMuster.class, "public/init/07-03-应急集合点管理.xls");
        eruptExcelController.importExcel(Rescueteam.class, "public/init/07-04-应急救援队伍管理.xls");
        eruptExcelController.importExcel(MaterialReserve.class, "public/init/07-05-应急物资储备点管理.xls");
        eruptExcelController.importExcel(EmergencyExpert.class, "public/init/07-06-应急专家管理.xls");
        eruptExcelController.importExcel(EmergencyWiki.class, "public/init/07-07-应急知识库.xls");
        eruptExcelController.importExcel(Evaluation.class, "public/init/07-08-评估表管理.xls");

        //移动端工作台
        eruptExcelController.importExcel(RiderApplication.class, "public/init/08-01-大连港移动端.xlsx");

        ThreadUtil.sleep(2000L);
        eruptExcelController.importExcel(TankGroup.class, "public/init/03-05-罐组管理.xls");
        eruptExcelController.importExcel(StorageTank.class, "public/init/03-06-储罐管理.xls");

        //一切初始化完毕以后 准备发送 清安全库数据命令
        //增加一步 清库命令

        redisProducer.cleanRedisProduce();
        //等待5秒后 继续后续操作
        try {
            Thread.sleep(10000);
            System.out.print("    线程睡眠10秒！\n");
        } catch (InterruptedException e) {
            e.printStackTrace();
        }
        //随后 发送 初始化命令
        redisProducer.addRedisProduce();

    }

    @Override
    public int sort() {
        return 3;
    }
}
