package com.daliangang.emergency.form;

import com.daliangang.emergency.entity.Rescueteam;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import xyz.erupt.annotation.EruptField;
import xyz.erupt.annotation.config.EruptSmartSkipSerialize;
import xyz.erupt.annotation.expr.ExprBool;
import xyz.erupt.annotation.sub_field.Edit;
import xyz.erupt.annotation.sub_field.EditType;
import xyz.erupt.annotation.sub_field.Readonly;
import xyz.erupt.annotation.sub_field.View;
import xyz.erupt.annotation.sub_field.sub_edit.ChoiceType;
import xyz.erupt.annotation.sub_field.sub_edit.InputType;
import xyz.erupt.annotation.sub_field.sub_edit.Search;
import xyz.erupt.toolkit.db.Comment;
import xyz.erupt.toolkit.handler.EruptDictCodeChoiceFetchHandler;
import xyz.erupt.toolkit.handler.RemoteEntityChoiceFetchHandler;
import xyz.erupt.toolkit.handler.SqlChoiceFetchHandler;
import xyz.erupt.upms.model.auth.DataAuthHandler;

import javax.persistence.Lob;

/**
 * <AUTHOR>
 * @since :2023/8/22:13:41
 */
@Data
public class RescueteamForm {
    private Long id;

    @Comment("队伍名称")
    @ApiModelProperty("队伍名称")
    private String teamName;


    private String orgCode;


    @Comment("所属单位")
    @ApiModelProperty("所属单位")
    private String ownerDepartment;

//	@EruptField(
//			views = @View(title = "企业名称"),
//			edit = @Edit(title = "企业名称",
//					readonly = @Readonly(exprHandler = DataAuthHandler.class),
//					type = EditType.CHOICE, notNull = true,
//					search = @Search,
//					choiceType = @ChoiceType(fetchHandler = RemoteEntityChoiceFetchHandler.class, fetchHandlerParams ={"main","Enterprise","org_code,name"})))
//	@Comment("企业名称")
//	@ApiModelProperty("企业名称")
//	private String company;
//	@EruptField(
//			views = @View(title = "填报单位"),
//			edit = @Edit(title = "填报单位",
//					search = @Search(vague = true),
//					readonly = @Readonly(),
//					type = EditType.INPUT, notNull = true
//			))
//	@Comment("填报单位")
//	@ApiModelProperty("填报单位")
//	private String company;

    @Comment("填报单位")
    @ApiModelProperty("填报单位")
    private String company;

//	@EruptField(
//			views = @View(title = "所属港区", ifRender = @ExprBool(exprHandler = DepartmentViewRenderHandler.class)),
//			edit = @Edit(title = "所属港区", type = EditType.CHOICE, search = @Search(vague = true), notNull = true,
//					readonly = @Readonly(exprHandler = DataAuthHandler.class),
//					choiceType = @ChoiceType(fetchHandler = RemoteEntityChoiceFetchHandler.class, fetchHandlerParams ={"device","PortArea","id,portarea_name"})))
//	@Comment("所属港区")
//	@ApiModelProperty("所属港区")
//	private String portArea;

    @Comment("所属港区")
    @ApiModelProperty("所属港区")
    private String portArea;


    @Comment("地址")
    @ApiModelProperty("地址")
    private String address;


    @Comment("队伍级别")
    @ApiModelProperty("队伍级别")
    private String teamLevel;


    @Comment("人员规模")
    @ApiModelProperty("人员规模")
    private String personnelSize;


    @Comment("装备情况")
    @ApiModelProperty("装备情况")
    private String equipmentCondition;


    @Comment("负责人")
    @ApiModelProperty("负责人")
    private String personInCharge;


    @Comment("值守电话")
    @ApiModelProperty("值守电话")
    private String contactNumber;


    @Comment("技术专长描述")
    @ApiModelProperty("技术专长描述")
    private @Lob
    String expertiseDescription;



    @Comment("地图")
    @ApiModelProperty("地图")
    @Lob private String map;
}
