package com.daliangang.safedaily.operation;

import cn.hutool.json.JSONObject;
import com.daliangang.safedaily.entity.InspectExpertDaily;
import com.daliangang.safedaily.entity.Whistleblower;
import org.springframework.stereotype.Service;
import xyz.erupt.annotation.fun.OperationHandler;
import xyz.erupt.common.http.EruptRemoteUtils;
import xyz.erupt.jpa.dao.EruptDao;
import xyz.erupt.toolkit.remote.RemoteProxyService;
import xyz.erupt.toolkit.utils.NotifyUtils;

import javax.annotation.Resource;
import javax.transaction.Transactional;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * @Title: WhistleblowerEscalationHandler
 * <AUTHOR>
 * @Package com.daliangang.safedaily.operation
 * @Date 2024/3/7 19:37
 * @description: 安全吹哨人管理上报
 */
@Service
public class WhistleblowerEscalationHandler implements OperationHandler<Whistleblower,Void> {

    @Resource
    private RemoteProxyService remoteProxyService;

    @Resource
    private EruptDao eruptDao;

    @Override
    @Transactional
    public String exec(List<Whistleblower> data, Void unused, String[] param) {
        for (Whistleblower whistleblower:data) {
            whistleblower.setSubmitted(true);
            whistleblower.setUpdateTime(LocalDateTime.now());
            eruptDao.merge(whistleblower);

            JSONObject inputData = new JSONObject();
            inputData.set("clazz", "Whistleblower");
            inputData.set("insertData",whistleblower);
            EruptRemoteUtils.doPost(remoteProxyService.getMainServerUrl() + "/erupt-api/exchange/data/exchange", inputData, Map.class);
        }

        return NotifyUtils.getSuccessNotify("上报成功！");
    }
}
