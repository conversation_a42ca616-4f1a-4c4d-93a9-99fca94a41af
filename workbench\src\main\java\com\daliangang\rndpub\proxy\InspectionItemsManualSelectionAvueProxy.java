package com.daliangang.rndpub.proxy;

import org.springframework.stereotype.Service;
import xyz.erupt.core.context.MetaDrill;
import xyz.erupt.core.util.EruptSpringUtil;
import xyz.erupt.core.view.EruptApiModel;
import xyz.erupt.jpa.dao.EruptDao;
import xyz.erupt.toolkit.db.EruptResultMap;
import xyz.erupt.tpl.avue.core.AvueProxy;

import javax.transaction.Transactional;
import java.util.List;
import java.util.Set;

@Service
public class InspectionItemsManualSelectionAvueProxy implements AvueProxy {

    @Override
    public String getCode() {
        return "InspectionItemsManualSelection";
    }

    @Override
    @Transactional
    public EruptApiModel submit(EruptResultMap params) {
        EruptDao eruptDao = EruptSpringUtil.getBean(EruptDao.class);
        String resetSql = "update tb_inspection_items set state=0 where procedure_id=" + MetaDrill.getDrillId();
        eruptDao.getJdbcTemplate().execute(resetSql);
        Set<String> keys = params.keySet();//检查事项
        for (String key : keys) {
            List<String> firstLevels = params.getAsList(key, String.class);//一级
            for (String firstLevel : firstLevels) {
                String sql = "update tb_inspection_items set state=1 where procedure_id=" + MetaDrill.getDrillId() + " and inspection_items='" + key + "' and check_first='" + firstLevel + "'";
                eruptDao.getJdbcTemplate().execute(sql);
            }
        }
        return EruptApiModel.successApi();
    }
}
