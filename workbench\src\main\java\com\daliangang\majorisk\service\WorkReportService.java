package com.daliangang.majorisk.service;

import com.daliangang.datascreen.response.WorkCondition;
import com.daliangang.device.entity.PortArea;
import com.daliangang.majorisk.entity.Maintenance;
import com.daliangang.majorisk.entity.Unload;
import com.daliangang.majorisk.entity.UnloadShip;
import com.daliangang.majorisk.operation.WordExportReportHandler;
import com.daliangang.workbench.entity.Enterprise;
import lombok.AllArgsConstructor;
import lombok.Data;
import org.springframework.stereotype.Service;
import xyz.erupt.jpa.dao.EruptDao;
import xyz.erupt.upms.service.EruptPlatformService;

import javax.annotation.Resource;
import java.sql.Date;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @Author: chongmenglin
 * @Date: 2024/12/9 13:38
 */
@Service
public class WorkReportService {
    @Resource
    private EruptDao eruptDao;
    @Resource
    private EruptPlatformService eruptPlatformService;

    /**
     * 获取给定时间的作业情况
     *
     * @param enterprises
     * @param codes
     * @param date
     * @return
     */
    public WorkCondition getWorkCondition(List<Enterprise> enterprises, List<String> codes, Date date) {
        WorkCondition workCondition = new WorkCondition();
        //1.装卸作业（装卸车+装卸船），去掉货种是"2、3、4、5、6.1、8、9"
        //装卸车作业数量
        // 查询条件
        String queryCondition = "orgCode IN :orgCodes and DATE(aeTime) >= :currentDate and DATE(abTime) <= :currentDate and submitted = '1'";
        // 执行查询
        List<Unload> unloads = eruptDao.queryEntityList(
                        Unload.class,
                        queryCondition,
                        new HashMap<String, Object>() {{
                            put("orgCodes", codes);
                            put("currentDate", date);
                        }}
                );
        workCondition.setCarNumber(unloads.size());
        OperationStats carMostActive = getMostActiveStats(enterprises, unloads, Unload::getOrgCode);
        PortArea carArea = eruptDao.queryEntity(PortArea.class, "id = :id", new HashMap<String, Object>() {{
            put("id", carMostActive.getMostActivePortArea());
        }});
        workCondition.setCarArea(carArea.getPortareaName());
        workCondition.setCarOrgan(carMostActive.getMostActiveEnterprise().getName());
        // 统计装卸车作业的货种
        String carGoodsNames = unloads.stream()
                .filter(unload -> unload.getGoodsName() != null)
                .filter(item -> filterDangerousGoods(item.getGoodsName()))  // 只在统计货种时过滤指定危险品
                // 直接按货物名称分组，每组保留重量最大的记录
                .collect(Collectors.groupingBy(
                        Unload::getGoodsName,  // 直接用货物名称分组
                        Collectors.maxBy(Comparator.comparing(Unload::getWeight, Comparator.nullsLast(Comparator.naturalOrder())))
                ))
                .values()
                .stream()
                .filter(Optional::isPresent)
                .map(Optional::get)
                .sorted(Comparator.comparing(Unload::getWeight, Comparator.nullsLast(Comparator.reverseOrder())))
                .map(Unload::getGoodsName)
                .collect(Collectors.collectingAndThen(
                        Collectors.toList(),
                        list -> {
                            if (list.size() <= 4) {
                                return String.join("，", list);
                            } else {
                                return String.join("，", list.subList(0, 4)) + "等";
                            }
                        }
                ));
        workCondition.setCarGoodsType(carGoodsNames);
        //装卸船作业数量
        // 查询条件
        queryCondition = "orgCode IN :orgCode and DATE(:currentDate) >= DATE(abTime) and DATE(:currentDate) <= DATE(aeTime) and cancelState = 0";
        // 执行查询
        List<UnloadShip> unloadShips = eruptDao.queryEntityList(
                        UnloadShip.class,
                        queryCondition,
                        new HashMap<String, Object>() {{
                            put("orgCode", codes);
                            put("currentDate", date);
                        }}
                );
        workCondition.setShipNumber(unloadShips.size());
        OperationStats shipMostActive = getMostActiveStats(enterprises, unloadShips, UnloadShip::getOrgCode);
        PortArea shipArea = eruptDao.queryEntity(PortArea.class, "id = :id", new HashMap<String, Object>() {{
            put("id", shipMostActive.getMostActivePortArea());
        }});
        workCondition.setShipArea(shipArea.getPortareaName());
        workCondition.setShipOrgan(shipMostActive.getMostActiveEnterprise().getName());
        // 统计装卸船作业的货种
        String shipGoodsNames = unloadShips.stream()
                .filter(ship -> ship.getGoodsName() != null)
                .filter(item -> filterDangerousGoods(item.getGoodsName()))  // 只在统计货种时过滤指定危险品
                // 直接按货物名称分组，每组保留重量最大的记录
                .collect(Collectors.groupingBy(
                        UnloadShip::getGoodsName,  // 直接用货物名称分组
                        Collectors.maxBy(Comparator.comparing(UnloadShip::getWeight, Comparator.nullsLast(Comparator.naturalOrder())))
                ))
                .values()
                .stream()
                .filter(Optional::isPresent)
                .map(Optional::get)
                .sorted(Comparator.comparing(UnloadShip::getWeight, Comparator.nullsLast(Comparator.reverseOrder())))
                .map(UnloadShip::getGoodsName)
                .collect(Collectors.collectingAndThen(
                        Collectors.toList(),
                        list -> {
                            if (list.size() <= 4) {
                                return String.join("，", list);
                            } else {
                                return String.join("，", list.subList(0, 4)) + "等";
                            }
                        }
                ));
        workCondition.setShipGoodsType(shipGoodsNames);
        //检维修作业数量
        queryCondition = "orgCode IN :orgCode and DATE(starDate) <= :currentDate and DATE(endDate) >= :currentDate and submitted = '1'";
        // 执行查询
        List<Maintenance> maintenances = eruptDao.queryEntityList(
                Maintenance.class,
                queryCondition,
                new HashMap<String, Object>() {{
                    put("orgCode", codes);
                    put("currentDate", date);
                }}
        );
        workCondition.setMaintenanceNumber(maintenances.size());
        OperationStats maintenanceMostActive = getMostActiveStats(enterprises, maintenances, Maintenance::getOrgCode);
        PortArea maintenanceArea = eruptDao.queryEntity(PortArea.class, "id = :id", new HashMap<String, Object>() {{
            put("id", maintenanceMostActive.getMostActivePortArea());
        }});
        workCondition.setMaintenanceArea(maintenanceArea.getPortareaName());
        workCondition.setMaintenanceOrgan(maintenanceMostActive.getMostActiveEnterprise().getName());

        //车+船装卸港区Top1
        if (!carArea.getPortareaName().equals(shipArea.getPortareaName())) {
            workCondition.setCarShipArea(concatenate("、", Arrays.asList(carArea.getPortareaName(), shipArea.getPortareaName())));
        } else {
            workCondition.setCarShipArea(carArea.getPortareaName());
        }

        // 工作重点
        // 统计所有作业的港区分布
        Map<Long, Long> totalPortAreaCount = new HashMap<>();
        Map<Long, List<Enterprise>> portAreaEnterprises = enterprises.stream()
                .collect(Collectors.groupingBy(Enterprise::getPortArea));

        // 统计三种作业的数量
        Map<String, Long> carCount = unloads.stream()
                .collect(Collectors.groupingBy(Unload::getOrgCode, Collectors.counting()));
        Map<String, Long> shipCount = unloadShips.stream()
                .collect(Collectors.groupingBy(UnloadShip::getOrgCode, Collectors.counting()));
        Map<String, Long> maintenanceCount = maintenances.stream()
                .collect(Collectors.groupingBy(Maintenance::getOrgCode, Collectors.counting()));

        // 合并所有作业数量统计
        for (Map.Entry<Long, List<Enterprise>> entry : portAreaEnterprises.entrySet()) {
            Long portAreaId = entry.getKey();
            long total = entry.getValue().stream()
                    .mapToLong(e -> carCount.getOrDefault(e.getOrgCode(), 0L) +
                            shipCount.getOrDefault(e.getOrgCode(), 0L) +
                            maintenanceCount.getOrDefault(e.getOrgCode(), 0L))
                    .sum();
            totalPortAreaCount.put(portAreaId, total);
        }

        // 找出总作业量最多的港区
        Long mostActivePortAreaId = totalPortAreaCount.entrySet().stream()
                .max(Map.Entry.comparingByValue())
                .map(Map.Entry::getKey)
                .orElse(null);

        // 设置总作业量最多的港区
        if (mostActivePortAreaId != null) {
            PortArea mostActivePortArea = eruptDao.queryEntity(PortArea.class, "id = :id",
                    new HashMap<String, Object>() {{
                        put("id", mostActivePortAreaId);
                    }});
            workCondition.setStrengthenSupervisionArea(mostActivePortArea.getPortareaName());
        }

        // 统计车船作业合并后的企业分布
        Map<String, Long> carShipCount = new HashMap<>();

        // 合并车船作业数量
        for (Enterprise enterprise : enterprises) {
            String orgCode = enterprise.getOrgCode();
            long carWorkCount = unloads.stream()
                    .filter(u -> u.getOrgCode().equals(orgCode))
                    .count();
            long shipWorkCount = unloadShips.stream()
                    .filter(s -> s.getOrgCode().equals(orgCode))
                    .count();
            long total = carWorkCount + shipWorkCount;
            if (total > 0) {
                carShipCount.put(orgCode, total);
            }
        }

        // 找出车船作业量最多的企业
//        Enterprise carShipMostActiveEnterprise = enterprises.stream()
//                .map(e -> new AbstractMap.SimpleEntry<>(e, carShipCount.getOrDefault(e.getOrgCode(), 0L)))
//                .max(Map.Entry.comparingByValue())
//                .map(Map.Entry::getKey)
//                .orElse(null);
        if (!maintenances.isEmpty()) {
            workCondition.setKeyInspectionContent(maintenanceMostActive.getMostActiveEnterprise().getName());
        } else {
            workCondition.setKeyInspectionContent("检维修作业暂未上报");
        }

        return workCondition;
    }

    /**
     * 过滤特定危险品类别组合
     *
     * @param goodsName 货物名称
     * @return true 表示保留该记录，false 表示过滤掉
     */
    private boolean filterDangerousGoods(String goodsName) {
        if (goodsName == null) {
            return true;
        }

        // 1. 清除所有空白字符
        String cleanName = goodsName.replaceAll("\\s+", "");

        // 2. 如果包含其他字符（除了数字、顿号、逗号、点、"类"字和"第"字），就保留
        if (cleanName.matches(".*[^0-9、，.类第].*")) {
            return true;
        }
        // 3. 移除"第"字（如果存在）
        cleanName = cleanName.replace("第", "");

        // 4. 提取数字
        Set<String> numbers = Arrays.stream(cleanName.split("[、，]"))
                .map(s -> s.replace("类", "").trim())
                .collect(Collectors.toSet());

        // 5. 定义目标数字集合
        String[] split = eruptPlatformService.getOption(WordExportReportHandler.NEED_FILTER_GOOD).getAsString().split(",");
        Set<String> targetNumbers = new HashSet<>(Arrays.asList(split));
        // 6. 检查是否为目标数字的子集，且至少包含6个数字
        return !(targetNumbers.containsAll(numbers) && numbers.size() >= 6);
    }

    /**
     * 作业统计结果
     */
    @Data
    @AllArgsConstructor
    private static class OperationStats {
        private Long mostActivePortArea;  // 作业最多的港区ID
        private Enterprise mostActiveEnterprise;  // 作业最多的企业
    }

    /**
     * 获取作业数量最多的港区和企业
     */
    private <T> OperationStats getMostActiveStats(List<Enterprise> enterprises,
                                                  List<T> operations,
                                                  Function<T, String> orgCodeGetter) {
        // 1. 按orgCode统计作业数量
        Map<String, Long> orgCodeCount = operations.stream()
                .collect(Collectors.groupingBy(orgCodeGetter, Collectors.counting()));

        // 2. 按portArea分组统计总作业量
        Map<Long, Long> portAreaCount = new HashMap<>();
        Map<Long, List<Enterprise>> portAreaEnterprises = enterprises.stream()
                .collect(Collectors.groupingBy(Enterprise::getPortArea));

        for (Map.Entry<Long, List<Enterprise>> entry : portAreaEnterprises.entrySet()) {
            long total = entry.getValue().stream()
                    .mapToLong(e -> orgCodeCount.getOrDefault(e.getOrgCode(), 0L))
                    .sum();
            portAreaCount.put(entry.getKey(), total);
        }

        // 3. 找出作业数量最多的港区
        Long mostActivePortArea = portAreaCount.entrySet().stream()
                .max(Map.Entry.comparingByValue())
                .map(Map.Entry::getKey)
                .orElse(null);

        // 4. 找出作业数量最多的企业
        Enterprise mostActiveEnterprise = enterprises.stream()
                .map(e -> new AbstractMap.SimpleEntry<>(e, orgCodeCount.getOrDefault(e.getOrgCode(), 0L)))
                .max(Map.Entry.comparingByValue())
                .map(Map.Entry::getKey)
                .orElse(null);

        return new OperationStats(mostActivePortArea, mostActiveEnterprise);
    }

    public static String concatenate(String delimiter, Collection<String> strings) {
        return strings == null || strings.isEmpty() ? "" :
                String.join(delimiter == null ? "" : delimiter, strings);
    }
}

