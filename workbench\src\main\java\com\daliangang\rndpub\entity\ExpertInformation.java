/*
 * Copyright © 2020-2035 yundingyun All rights reserved.
 */
package com.daliangang.rndpub.entity;

import com.daliangang.core.CompanyRenderHandler;
import com.daliangang.rndpub.operation.ExpertInformationManualSelectionHandler;
import com.daliangang.rndpub.operation.ExpertInformationSystemExtractionHandler;
import com.daliangang.rndpub.proxy.ExpertInformationDataProxy;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import xyz.erupt.annotation.Erupt;
import xyz.erupt.annotation.EruptField;
import xyz.erupt.annotation.expr.ExprBool;
import xyz.erupt.annotation.sub_erupt.PageModel;
import xyz.erupt.annotation.sub_erupt.Power;
import xyz.erupt.annotation.sub_erupt.RowOperation;
import xyz.erupt.annotation.sub_field.Edit;
import xyz.erupt.annotation.sub_field.EditType;
import xyz.erupt.annotation.sub_field.View;
import xyz.erupt.annotation.sub_field.sub_edit.BoolType;
import xyz.erupt.annotation.sub_field.sub_edit.ChoiceType;
import xyz.erupt.annotation.sub_field.sub_edit.InputType;
import xyz.erupt.annotation.sub_field.sub_edit.Search;
import xyz.erupt.jpa.model.MetaModel;
import xyz.erupt.toolkit.db.Comment;
import xyz.erupt.toolkit.handler.EruptDictCodeChoiceFetchHandler;

import javax.persistence.Entity;
import javax.persistence.ManyToOne;
import javax.persistence.Table;

@Erupt(name = "执法专家", power = @Power(add = false, delete = false, export = false, importable = true, viewDetails = true, edit = false),selectionVisible = false
        , dataProxy = ExpertInformationDataProxy.class
        , pageModel = @PageModel(flag = true,pageProxy = ExpertInformationDataProxy.class)
        , rowOperation = {
        @RowOperation(title = "自动抽取", icon = "fa fa-chain-broken", eruptClass = ExpertInformationSystemExtractionHandler.DrawExpertForm.class, operationHandler = ExpertInformationSystemExtractionHandler.class, mode = RowOperation.Mode.BUTTON),
        @RowOperation(confirm = false, title = "抽取专家", ifExpr = "item.state=='未抽取'", icon = "fa fa-hand-o-up", operationHandler = ExpertInformationManualSelectionHandler.class, operationParam = "true", mode = RowOperation.Mode.MULTI),
        @RowOperation(confirm = false, title = "放弃抽取", ifExpr = "item.state=='已抽取'", icon = "fa fa-hand-o-down", operationHandler = ExpertInformationManualSelectionHandler.class, operationParam = "false", mode = RowOperation.Mode.MULTI),
        @RowOperation(confirm = false,type = RowOperation.Type.CLOSE,title = "确认", mode = RowOperation.Mode.BUTTON),
})
@Table(name = "tb_expert_information")
@Entity
@Getter
@Setter
@Comment("执法专家")
@ApiModel("执法专家")
public class ExpertInformation extends MetaModel {

    @ManyToOne
    private Procedure procedure;


    @EruptField(
            views = @View(title = "状态", show = true, sortable = true),
            edit = @Edit(title = "状态", type = EditType.BOOLEAN, notNull = true, show = false, search = @Search,
                    boolType = @BoolType(trueText = "已抽取", falseText = "未抽取")))
    @Comment("状态")
    @ApiModelProperty("状态")
    private Boolean state;

    @EruptField(
            views = @View(title = "专家id", show = false),
            edit = @Edit(title = "专家id", type = EditType.INPUT, show = false,
                    inputType = @InputType))
    @Comment("专家id")
    @ApiModelProperty("专家id")
    private String expertId;


    @EruptField(
            views = @View(title = "专家姓名", show = true),
            edit = @Edit(title = "专家姓名", type = EditType.INPUT,
                    inputType = @InputType))
    @Comment("专家姓名")
    @ApiModelProperty("专家姓名")
    private String name;

    @EruptField(
            views = @View(title = "性别", show = true),
            edit = @Edit(title = "性别", type = EditType.BOOLEAN, notNull = true,
                    boolType = @BoolType(trueText = "男", falseText = "女")))
    @Comment("性别")
    @ApiModelProperty("性别")
    private Boolean sex;

    @EruptField(
            views = @View(title = "职称"),
            edit = @Edit(title = "职称", type = EditType.CHOICE, notNull = true,
                    choiceType = @ChoiceType(fetchHandler = EruptDictCodeChoiceFetchHandler.class, fetchHandlerParams = "postTitle")))
    @Comment("职称")
    @ApiModelProperty("职称")
    private String technical;

    @EruptField(
            views = @View(title = "企业名称"),
            edit = @Edit(title = "企业名称", search = @Search(ifRender = @ExprBool(exprHandler = CompanyRenderHandler.class)),
                    type = EditType.INPUT, notNull = true))
    @Comment("企业名称")
    @ApiModelProperty("企业名称")
    private String expertCompany;


//    @EruptField(
//            views = @View(title = "企业名称", show = true),
//            edit = @Edit(title = "企业名称", readonly = @Readonly(exprHandler = DataAuthHandler.class), type = EditType.CHOICE, notNull = true,
//                    choiceType = @ChoiceType(fetchHandler = RemoteEntityChoiceFetchHandler.class, fetchHandlerParams = {"main", "Enterprise", "org_code,name"})))
//    @Comment("企业名称")
//    @ApiModelProperty("企业名称")
//    private String expertCompany;

//    @EruptField(
//            views = @View(title = "检查范围"),
//            edit = @Edit(title = "检查范围", type = EditType.CHOICE, notNull = false,
//                    choiceType = @ChoiceType(fetchHandler = EruptDictCodeChoiceFetchHandler.class, fetchHandlerParams = "checkScope", reload = true)))

//    @EruptField(
//            views = @View(title = "检查范围", show = true),
//            edit = @Edit(title = "检查范围", type = EditType.INPUT,
//                    inputType = @InputType))
//    @Comment("检查范围")
//    @ApiModelProperty("检查范围")
//    private String checkScope;

    @EruptField(
            views = @View(title = "检查范围"),
            edit = @Edit(title = "检查范围", type = EditType.CHOICE,
                    choiceType = @ChoiceType(fetchHandler = EruptDictCodeChoiceFetchHandler.class, fetchHandlerParams = "checkScope")))
    @Comment("检查范围")
    @ApiModelProperty("检查范围")
    private String checkScope;

}
