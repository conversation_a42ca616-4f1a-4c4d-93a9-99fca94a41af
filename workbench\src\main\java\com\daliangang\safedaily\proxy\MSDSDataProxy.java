/*
 * Copyright © 2020-2035 yundingyun All rights reserved.
 */
package com.daliangang.safedaily.proxy;

import com.daliangang.safedaily.entity.MSDS;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import xyz.erupt.annotation.fun.DataProxy;
import xyz.erupt.toolkit.utils.FileUtils;

import java.util.StringTokenizer;

@Service
public class MSDSDataProxy implements DataProxy<MSDS> {
    @Override
    public void beforeAdd(MSDS msds) {
        if (StringUtils.isNotEmpty(msds.getFile())) {
            String name = FileUtils.getFilename(msds.getFile()).replace(".pdf", "");
            if (name.contains("-")) {
                StringTokenizer st = new StringTokenizer(name, "-");
                name = st.nextToken().trim();
            }
            msds.setName(name);
        }
    }

//    @Override
//    public void afterFetch(Collection<Map<String, Object>> list) {
//        List<MSDS> msdsList = EruptDaoUtils.convert(list, MSDS.class);
//        msdsList.forEach(msds -> {
//            if (msds.getFile().contains("%")) {
//                try {
//                    String file = URLEncoder.encode(msds.getFile(), "UTF-8");
//                    EruptDaoUtils.updateAfterFetch(list, msds.getId(), "file", file);
//                } catch (UnsupportedEncodingException e) {
//                    throw new RuntimeException(e);
//                }
//            }
//        });
//    }
}
