package com.daliangang.majorisk.controller;

import com.daliangang.core.DaliangangContext;
import com.daliangang.majorisk.entity.RiskDatabaseEnterprise;
import com.daliangang.majorisk.form.RiskDatabaseEnterpriseForm;
import com.daliangang.majorisk.form.WorkRiskForm;
import com.daliangang.majorisk.form.WorkRiskRequestForm;
import com.daliangang.majorisk.sql.MajoriskWorkbenchSql;
import com.google.gson.internal.LinkedTreeMap;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import xyz.erupt.core.annotation.EruptRouter;
import xyz.erupt.core.module.MetaUserinfo;
import xyz.erupt.core.view.EruptApiModel;
import xyz.erupt.jpa.dao.EruptDao;
import xyz.erupt.toolkit.remote.RemoteProxyService;
import xyz.erupt.toolkit.utils.EruptDaoUtils;
import xyz.erupt.toolkit.utils.SqlUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since :2023/4/21:13:38
 */
@RestController
public class RiskDatabaseEnterpriseController {
    @Resource
    private EruptDao eruptDao;
    @Resource
    private RemoteProxyService remoteProxyService;
    @Resource
    private MajoriskWorkbenchSql majoriskWorkbenchSql;

    /**
     * 获取重大风险数据库信息
     * @param
     * @return
     */
    @RequestMapping("erupt-api/get/riskDatabase")
    @EruptRouter(verifyType = EruptRouter.VerifyType.LOGIN)
    public EruptApiModel getPortareaName() {
        MetaUserinfo remoteUserInfo = remoteProxyService.getRemoteUserInfo();
        String orgCode = SqlUtils.wrapIn(remoteUserInfo.getAuth());

//        Enterprise enterprise = EruptDaoUtils.selectOne("select * from tb_enterprise where org_code ='" +remoteUserInfo.getOrg()+"'", Enterprise.class);
//        if (ObjectUtils.isEmpty(enterprise)) {
//            return EruptApiModel.successApi();
//        }

        List<RiskDatabaseEnterprise> riskDatabaseEnterprises = EruptDaoUtils.selectOnes("select * from tb_risk_database_enterprise where org_code " + orgCode+"", RiskDatabaseEnterprise.class);
      //  riskDatabaseEnterprises = riskDatabaseEnterprises.stream().collect(Collectors.collectingAndThen(Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(RiskDatabaseEnterprise :: getRiskName))), ArrayList::new));
        if (ObjectUtils.isNotEmpty(riskDatabaseEnterprises)) {
            List<LinkedTreeMap> list1 = new ArrayList<>();
            riskDatabaseEnterprises.forEach(v->{
                LinkedTreeMap map = new LinkedTreeMap();
                map.put("code",v.getId());
                map.put("name",v.getRiskName());
                list1.add(map);
            });
            return EruptApiModel.successApi(list1);
        }

        return EruptApiModel.successApi();
    }

    @RequestMapping("erupt-api/get/nowRiskDatabaseEnterprise")
    @EruptRouter(verifyType = EruptRouter.VerifyType.LOGIN)
    public EruptApiModel nowRiskDatabaseEnterprise() {
        //查询当前登录的用户信息
        MetaUserinfo remoteUserInfo = remoteProxyService.getRemoteUserInfo();
        List<RiskDatabaseEnterprise> riskDatabaseEnterprises=new ArrayList<>();
        //判断是政府管理员还是企业
        if(DaliangangContext.isDepartmentUser()){
            riskDatabaseEnterprises = EruptDaoUtils.selectOnes("select * from tb_risk_database_enterprise where org_code " + SqlUtils.wrapIn(remoteUserInfo.getAuth()), RiskDatabaseEnterprise.class);
//            riskDatabaseEnterprises = riskDatabaseEnterprises.stream().collect(Collectors.collectingAndThen(Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(RiskDatabaseEnterprise :: getRiskName))), ArrayList::new));
        }else{
            riskDatabaseEnterprises = EruptDaoUtils.selectOnes("select * from tb_risk_database_enterprise where org_code =" + SqlUtils.wrapStr(remoteUserInfo.getOrg()), RiskDatabaseEnterprise.class);
            riskDatabaseEnterprises = riskDatabaseEnterprises.stream().collect(Collectors.collectingAndThen(Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(RiskDatabaseEnterprise :: getRiskName))), ArrayList::new));
        }
        if (ObjectUtils.isNotEmpty(riskDatabaseEnterprises)) {
            List<LinkedTreeMap> list1 = new ArrayList<>();
            riskDatabaseEnterprises.forEach(v->{
                LinkedTreeMap map = new LinkedTreeMap();
                map.put("code",v.getId());
                map.put("name",v.getRiskName());
                list1.add(map);
            });
            return EruptApiModel.successApi(list1);
        }

        return EruptApiModel.successApi();
    }


    // 第二大屏 风险信息详情
    @PostMapping("erupt-api/get/selectWorksRiskDatabaseInfo")
   // @EruptRouter(verifyType = EruptRouter.VerifyType.LOGIN)
    public EruptApiModel selectWorksNum(@RequestBody WorkRiskRequestForm workRiskRequestForm) {
//        MetaUserinfo remoteUserInfo = remoteProxyService.getRemoteUserInfo();
//        String orgCode = SqlUtils.wrapIn(remoteUserInfo.getAuth());
        // 查询作业单元分组
        String sql = majoriskWorkbenchSql.selectWorkRiskDatabase(workRiskRequestForm.getOrgCode(),workRiskRequestForm.getRiskName());

        List<WorkRiskForm> workRiskFormList = EruptDaoUtils.selectOnes(sql, WorkRiskForm.class);
        Map<String,List<RiskDatabaseEnterpriseForm>> riskDatabaseEnterpriseMap =new HashMap<>();
        // 根据有单元查询详细数据,组装数据
        workRiskFormList.forEach(v->{
            String sq = majoriskWorkbenchSql.selectWorkRiskDatabaseInfo(workRiskRequestForm.getOrgCode(),workRiskRequestForm.getRiskName(), v.getWork(), v.getRiskRange());
            List<RiskDatabaseEnterpriseForm> riskDatabaseEnterpriseList = EruptDaoUtils.selectOnes(sq, RiskDatabaseEnterpriseForm.class);
            riskDatabaseEnterpriseMap.put(v.getWork(),riskDatabaseEnterpriseList);
        });
        return EruptApiModel.successApi(riskDatabaseEnterpriseMap);
    }

}
