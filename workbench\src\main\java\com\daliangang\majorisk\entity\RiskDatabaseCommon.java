/*
 * Copyright © 2020-2035 yundingyun All rights reserved.
 */
package com.daliangang.majorisk.entity;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import xyz.erupt.annotation.EruptField;
import xyz.erupt.annotation.expr.ExprBool;
import xyz.erupt.annotation.sub_field.Edit;
import xyz.erupt.annotation.sub_field.EditType;
import xyz.erupt.annotation.sub_field.View;
import xyz.erupt.annotation.sub_field.sub_edit.ChoiceType;
import xyz.erupt.annotation.sub_field.sub_edit.InputType;
import xyz.erupt.annotation.sub_field.sub_edit.Search;
import xyz.erupt.core.context.MetaContext;
import xyz.erupt.core.util.EruptSpringUtil;
import xyz.erupt.jpa.model.MetaModel;
import xyz.erupt.toolkit.db.Comment;
import xyz.erupt.toolkit.handler.EruptDictCodeChoiceFetchHandler;
import xyz.erupt.upms.model.EruptMenu;
import xyz.erupt.upms.service.EruptContextService;

import javax.persistence.Lob;
import javax.persistence.MappedSuperclass;
import javax.persistence.Transient;

@Getter
@Setter
@MappedSuperclass
public class RiskDatabaseCommon extends MetaModel implements ExprBool.ExprHandler {

    @EruptField(views = @View(title = "风险类型"),
            edit = @Edit(title = "风险类型", type = EditType.CHOICE, search = @Search, notNull = true,
                    choiceType = @ChoiceType(fullSpan = true, fetchHandler = EruptDictCodeChoiceFetchHandler.class, fetchHandlerParams = "riskType")))
    //choiceType = @ChoiceType(fetchHandler = SqlChoiceFetchHandler.class, fetchHandlerParams = "select '测试项','测试项'")))
    @Comment("风险类型")
    @ApiModelProperty("风险类型")
    private String riskType;

    @EruptField(views = @View(title = "风险名称"/*, ifRender = @ExprBool(exprHandler = RiskDatabaseCommon.class)*/),
            edit = @Edit(title = "风险名称" /*, ifRender = @ExprBool(exprHandler = RiskDatabaseCommon.class)*/,search = @Search(vague = true), type = EditType.INPUT,inputType = @InputType(fullSpan = true)))
    @Comment("风险名称")
    @ApiModelProperty("风险名称")
    private String riskName;

    @EruptField(views = @View(title = "风险辨识范围"), edit = @Edit(title = "风险辨识范围", type = EditType.TEXTAREA, notNull = true))
    @Comment("风险辨识范围")
    @ApiModelProperty("风险辨识范围")
    private @Lob String riskRange;

    @EruptField(views = @View(title = "作业单元"), edit = @Edit(title = "作业单元", type = EditType.TEXTAREA, notNull = true))
    @Comment("作业单元")
    @ApiModelProperty("作业单元")
    private @Lob String work;

    @EruptField(views = @View(title = "风险事件"), edit = @Edit(title = "风险事件", type = EditType.TEXTAREA, notNull = true))
    @Comment("风险事件")
    @ApiModelProperty("风险事件")
    private @Lob String event;

    @EruptField(views = @View(title = "事故类型"), edit = @Edit(title = "事故类型", type = EditType.TEXTAREA, notNull = true))
    @Comment("事故类型")
    @ApiModelProperty("事故类型")
    private @Lob String accidentType;

    @EruptField(views = @View(title = "致险因素", show = false), edit = @Edit(title = "致险因素", type = EditType.DIVIDE))
    @Transient
    @Comment("致险因素")
    @ApiModelProperty("致险因素")
    private String factorDiv;

    @EruptField(views = @View(title = "人的因素", show = false), edit = @Edit(title = "人的因素", type = EditType.TEXTAREA, notNull = true))
    @Comment("致险因素_人的因素")
    @ApiModelProperty("致险因素_人的因素")
    private @Lob String factorPerson;

    @EruptField(views = @View(title = "设备设施因素",show = false), edit = @Edit(title = "设备设施因素", type = EditType.TEXTAREA, notNull = true))
    @Comment("致险因素_设备设施因素")
    @ApiModelProperty("致险因素_设备设施因素")
    private @Lob String factorDevice;

    @EruptField(views = @View(title = "环境因素",show = false), edit = @Edit(title = "环境因素", type = EditType.TEXTAREA, notNull = true))
    @Comment("致险因素_环境因素")
    @ApiModelProperty("致险因素_环境因素")
    private @Lob String factorEnv;

    @EruptField(views = @View(title = "管理因素",show = false), edit = @Edit(title = "管理因素", type = EditType.TEXTAREA, notNull = true))
    @Comment("致险因素_管理因素")
    @ApiModelProperty("致险因素_管理因素")
    private @Lob String factorManage;

    @EruptField(views = @View(title = "风险管控措施", show = false), edit = @Edit(title = "风险管控措施", type = EditType.DIVIDE))
    @Transient
    @Comment("风险管控措施")
    @ApiModelProperty("风险管控措施")
    private String measureDivide;

    @EruptField(views = @View(title = "风险管控措施",show = false), edit = @Edit(title = "风险管控措施", type = EditType.TEXTAREA, notNull = true))
    @Comment("风险管控措施")
    @ApiModelProperty("风险管控措施")
    private @Lob String measure;

    @EruptField(views = @View(title = "变更说明", show = false), edit = @Edit(title = "变更说明", type = EditType.TEXTAREA, notNull = false,show = false))
    @Comment("变更说明")
    @ApiModelProperty("变更说明")
    private @Lob String description;

    @Override
    public boolean handler(boolean expr, String[] params) {
        if (MetaContext.getToken() != null) {
            EruptMenu menu = EruptSpringUtil.getBean(EruptContextService.class).getCurrentEruptMenu();
            if (menu != null && menu.getCode().equals(RiskDatabaseEnterprise.class.getSimpleName())) return true;
        }
        return false;
    }
}
