package com.daliangang.safedaily.sql;

import org.springframework.stereotype.Repository;

/**
 * <AUTHOR>
 * @since :2023/4/23:13:29
 */
@Repository
public class ReleaseSql {

    //
    public String selectReleaseNum (String orgCode) {

        String sql = "SELECT COUNT(*) as num  from tb_release tr  where submitted = 1 and  CONVERT(filling_date,DATE) = CURRENT_DATE()";
               sql += "and org_code "+orgCode+"";
        return sql;
    }

    public String selectReleaseNumTwo (String orgCode) {

        String sql = "SELECT COUNT(*) as num  from tb_release tr  where submitted = 1 and  CONVERT(filling_date,DATE) = CURRENT_DATE()";
        sql += " and org_code = '"+orgCode+"'";
        return sql;
    }
}
