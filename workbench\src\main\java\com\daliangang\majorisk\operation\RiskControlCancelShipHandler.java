package com.daliangang.majorisk.operation;

import com.daliangang.majorisk.entity.UnloadShip;
import com.daliangang.majorisk.form.RiskControlDockingShipForm;
import com.google.gson.Gson;
import com.google.gson.JsonObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import xyz.erupt.core.config.GsonFactory;
import xyz.erupt.jpa.dao.EruptDao;
import xyz.erupt.openapi.core.OpenApi;
import xyz.erupt.openapi.core.OpenApiHandler;
import xyz.erupt.openapi.core.OpenApiModel;
import xyz.erupt.openapi.enums.ApiStatus;
import xyz.erupt.toolkit.db.EruptResultMap;
import xyz.erupt.toolkit.utils.SqlUtils;

import javax.annotation.Resource;
import javax.transaction.Transactional;
import java.time.LocalDateTime;
import java.util.List;

@Service
@Slf4j
@OpenApi(api = "cancelLoadingShip",name = "修改装卸船撤销状态",handler = RiskControlCancelShipHandler.class)
public class RiskControlCancelShipHandler implements OpenApiHandler {

    @Resource
    private EruptDao eruptDao;

    @Transactional
    @Override
    public OpenApiModel exec(JsonObject request,String params) {
        log.info("接收撤销装卸船原始数据 -> " + request);
        // 数据转换
        Gson gson = GsonFactory.getGson();
        RiskControlDockingShipForm shipForm = gson.fromJson(request, RiskControlDockingShipForm.class);
        log.info("接收撤销装卸船处理后数据 -> " + shipForm);

        OpenApiModel openApiModel = new OpenApiModel();
        if(StringUtils.isEmpty(shipForm.getJob_id())){
            openApiModel.setStatus(ApiStatus.FAILURE);
            EruptResultMap vo = new EruptResultMap();
            log.info("撤销装卸船job_id不能为空");
            vo.put("消息", "job_id不能为空");
            openApiModel.setData(vo);
            return openApiModel ;
        }
        if(StringUtils.isEmpty(shipForm.getCancel_state())){
            openApiModel.setStatus(ApiStatus.FAILURE);
            EruptResultMap vo = new EruptResultMap();
            vo.put("消息", "撤销装卸船撤销状态不能为空");
            log.info("撤销状态不能为空");
            openApiModel.setData(vo);
            return openApiModel ;
        }
        List<UnloadShip> unloadShips = eruptDao.queryEntityList(UnloadShip.class, "job_id = " + SqlUtils.wrapStr(shipForm.getJob_id()));

        if(unloadShips == null || unloadShips.size() ==0){
            openApiModel.setStatus(ApiStatus.FAILURE);
            EruptResultMap vo = new EruptResultMap();
            vo.put("消息", "该job_id不存在");
            log.info("撤销装卸船"+shipForm.getJob_id()+"job_id不存在");
            openApiModel.setData(vo);
            return openApiModel ;
        }
        int updateNum = eruptDao.getJdbcTemplate().update("update tb_unload_ship set cancel_state = " + SqlUtils.wrapStr(shipForm.getCancel_state()) + " where job_id = " + SqlUtils.wrapStr(shipForm.getJob_id()));
        EruptResultMap vo = new EruptResultMap();
        vo.put("消息","更新了"+updateNum +"条数据");
        vo.put("返回时间", LocalDateTime.now());
        log.info("撤销装卸船更新了"+updateNum +"条数据");
        return openApiModel.setData(vo);
    }
}
