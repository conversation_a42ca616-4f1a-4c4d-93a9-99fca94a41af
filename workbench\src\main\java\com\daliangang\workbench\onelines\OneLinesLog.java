package com.daliangang.workbench.onelines;

import io.swagger.annotations.ApiModel;
import lombok.Data;
import xyz.erupt.annotation.Erupt;
import xyz.erupt.annotation.EruptField;
import xyz.erupt.annotation.config.EruptSmartSkipSerialize;
import xyz.erupt.annotation.sub_erupt.Power;
import xyz.erupt.annotation.sub_field.Edit;
import xyz.erupt.annotation.sub_field.EditType;
import xyz.erupt.annotation.sub_field.View;
import xyz.erupt.annotation.sub_field.sub_edit.DateType;
import xyz.erupt.jpa.model.BaseModel;
import xyz.erupt.toolkit.db.Comment;

import javax.persistence.Entity;
import javax.persistence.Table;
import java.time.LocalDateTime;

@Data
@Entity
@Erupt(name = "一网协同日志", dataProxy = OnlineUser.OnlineUserDataProxy.class,orderBy = "createTime desc",power = @Power(add = false, delete = false, export = true, importable = false, viewDetails = false, edit = false))
@Table(name = "tb_oneline_log")
@Comment("一网协同日志")
@ApiModel("一网协同日志")
public class OneLinesLog  extends BaseModel {

    @EruptField(
            views = @View(title = "日志内容"),
            edit = @Edit(title = "日志内容")
    )
    private String log ;

    @EruptSmartSkipSerialize
    @xyz.erupt.annotation.config.Comment("网址")
    @EruptField(
            views = @View(title = "网址"),
            edit = @Edit(title = "网址")
    )
    private String url;

    @EruptSmartSkipSerialize
    @xyz.erupt.annotation.config.Comment("创建时间")
    @EruptField(
            views = @View(title = "创建时间"),
            edit = @Edit(title = "创建时间",type = EditType.DATE,dateType = @DateType(type= DateType.Type.DATE_TIME), notNull = true)
    )
    private LocalDateTime createTime;

    @EruptField(
            views = @View(title = "跳转结果"),
            edit = @Edit(title = "跳转结果")
    )
    private String callbackResult ;

    @EruptField(
            views = @View(title = "日志结果"),
            edit = @Edit(title = "日志结果")
    )
    private String result ;
}
