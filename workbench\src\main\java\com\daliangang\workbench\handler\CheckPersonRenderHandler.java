package com.daliangang.workbench.handler;

import com.daliangang.core.DaliangangContext;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import xyz.erupt.annotation.expr.ExprBool;
import xyz.erupt.core.context.MetaContext;
import xyz.erupt.core.util.EruptSpringUtil;
import xyz.erupt.upms.model.EruptUser;
import xyz.erupt.upms.service.EruptUserService;

@Component
public class CheckPersonRenderHandler implements ExprBool.ExprHandler {
    @Override
    public boolean handler(boolean expr, String[] params) {
        if (StringUtils.isNotEmpty(MetaContext.getToken())) {
            EruptUserService eruptUserService = EruptSpringUtil.getBean(EruptUserService.class);
            EruptUser eruptUser = eruptUserService.getCurrentEruptUser();
            if (eruptUser != null && eruptUser.getIsAdmin()) {
                return true;
            }
        }
        return DaliangangContext.isDepartmentUser();
    }
}
