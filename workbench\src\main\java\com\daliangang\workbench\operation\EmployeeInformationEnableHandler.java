/*
 * Copyright © 2020-2035 yundingyun All rights reserved.
 */
package com.daliangang.workbench.operation;

import com.daliangang.workbench.entity.EmployeeInformation;
import org.springframework.stereotype.Service;
import xyz.erupt.annotation.fun.OperationHandler;
import xyz.erupt.jpa.dao.EruptDao;
import xyz.erupt.toolkit.utils.AssertUtils;
import xyz.erupt.toolkit.utils.EruptDaoUtils;
import xyz.erupt.upms.model.EruptUser;
import xyz.erupt.upms.service.EruptUserService;

import javax.annotation.Resource;
import javax.transaction.Transactional;
import java.util.List;

@Service
public class EmployeeInformationEnableHandler implements OperationHandler<EmployeeInformation, Void> {

    @Resource
    private EruptDao eruptDao;

    @Resource
    private EruptUserService eruptUserService;

    @Override
    @Transactional
    public String exec(List<EmployeeInformation> data, Void unused, String[] param) {
        for (EmployeeInformation employee : data) {
            boolean state = param[0].equals("on");
            employee.setState(state);
            eruptDao.merge(employee);

            EruptUser empUser = eruptUserService.findEruptUserByAccount(employee.getPhone());
            AssertUtils.notNull(empUser, "没有这个用户");
            String sql = "update e_upms_user set status=" + state + " where id=" + empUser.getId();
            EruptDaoUtils.updateNoForeignKeyChecks(sql);
            eruptUserService.flushEruptUserCache(empUser);
        }
        return null;
    }
}
