package com.daliangang.majorisk.config;

import com.daliangang.majorisk.entity.*;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.annotation.Order;
import xyz.erupt.core.constant.MenuTypeEnum;
import xyz.erupt.core.module.EruptModule;
import xyz.erupt.core.module.EruptModuleInvoke;
import xyz.erupt.core.module.MetaMenu;
import xyz.erupt.core.module.ModuleInfo;
import xyz.erupt.core.util.EruptSpringUtil;
import xyz.erupt.upms.service.EruptMenuService;
import xyz.erupt.upms.util.EruptMenuUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * @Author: zengwensi
 * @Description:
 * @Date: Created in 2023/3/17 15:18
 * @Modified By
 */
@Configuration
@Order
public class MajoriskConfiguration implements EruptModule, ApplicationRunner {

    static {
        EruptModuleInvoke.addEruptModule(MajoriskConfiguration.class);
    }

    @Override
    public ModuleInfo info() {
        return ModuleInfo.builder().name("daliangang-majorisk").build();
    }

    @Override
    public List<MetaMenu> initMenus() {
        EruptMenuService eruptMenuService = EruptSpringUtil.getBean(EruptMenuService.class);
        String moduleId = "majorisk";
        eruptMenuService.registerModule(moduleId, "港区重大风险监测与管控系统", 4);
        AtomicInteger rootSort = new AtomicInteger(400);
        AtomicInteger sort = new AtomicInteger();
        List<MetaMenu> menus = new ArrayList<>();

        MetaMenu jobMan = EruptMenuUtils.createRootMenu(null, "$dlg-jobMan", "作业管理", "fa fa-steam-square", rootSort, moduleId);
        menus.add(jobMan);

        menus.add(EruptMenuUtils.createMenu(UnloadShip.class, jobMan, sort, MenuTypeEnum.TABLE, "装卸船作业", "UnloadShip", moduleId, null));//装卸船作业
        menus.add(EruptMenuUtils.createMenu(Unload.class, jobMan, sort, MenuTypeEnum.TABLE, "装卸车作业", "Unload", moduleId, null));//装卸车作业
        menus.add(EruptMenuUtils.createMenu(Maintenance.class, jobMan, sort, MenuTypeEnum.TABLE, "检维修作业", "Maintenance", moduleId, null)); //检维修作业

        //menus.add(EruptMenuUtils.createMenu(ReserveReporting.class, null, rootSort, MenuTypeEnum.TABLE, "每日储量上报", "ReserveReporting", moduleId, null));//每日储量上报
        MetaMenu reserveMan = EruptMenuUtils.createRootMenu(null, "$dlg-reserveMan", "每日储量上报", "fa fa-cloud-upload", rootSort, moduleId);
        menus.add(reserveMan);
        menus.add(EruptMenuUtils.createMenu(ReserveReporting.class, reserveMan, sort, MenuTypeEnum.TABLE, "储罐每日储量上报", "ReserveReporting", moduleId, null));//装卸船作业
        menus.add(EruptMenuUtils.createMenu(CkReserveReporting.class, reserveMan, sort, MenuTypeEnum.TABLE, "仓库每日储量上报", "CkReserveReporting", moduleId, null));//装卸船作业
        menus.add(EruptMenuUtils.createMenu(DcReserveReporting.class, reserveMan, sort, MenuTypeEnum.TABLE, "堆场每日储量上报", "DcReserveReporting", moduleId, null));//装卸船作业

        MetaMenu riskDatabase = EruptMenuUtils.createRootMenu(null, "$risk-database", "重大风险数据库", "fa fa-life-saver", rootSort, moduleId);
        menus.add(riskDatabase);//重大风险数据库
        menus.add(EruptMenuUtils.createMenu(RiskDatabaseDepartment.class, riskDatabase, sort, MenuTypeEnum.TABLE, "公共数据库", "RiskDatabaseDepartment", moduleId, null));
        menus.add(EruptMenuUtils.createMenu(RiskDatabaseCompany.class, riskDatabase, sort, MenuTypeEnum.TABLE, "通用风险库", "RiskDatabaseCompany", moduleId, null));
        menus.add(EruptMenuUtils.createMenu(RiskDatabaseEnterprise.class, riskDatabase, sort, MenuTypeEnum.TABLE, "本企业风险库", "RiskDatabaseEnterprise", moduleId, null));
        menus.add(MetaMenu.createSimpleMenu("RiskMap", "重大风险分布图", "/major-risk/index.html", null, rootSort.getAndIncrement(), MenuTypeEnum.NEW_WINDOW.getCode()).icon("fa fa-area-chart").module(moduleId));

//        MetaMenu riskStats = EruptMenuUtils.createRootMenu(null, "$risk-stats", "数据统计", "fa fa-area-chart", rootSort, moduleId);
//        menus.add(riskStats);
        menus.add(MetaMenu.createSimpleMenu("riskStatsZf", "政府统计", "TSR9YULz", null, rootSort.getAndIncrement(), "bi").module(moduleId).icon("fa fa-bar-chart"));
        menus.add(MetaMenu.createSimpleMenu("riskStatsQy", "企业统计", "yaUpmw0P", null, rootSort.getAndIncrement(), "bi").module(moduleId).icon("fa fa-area-chart"));

        //五清单
        MetaMenu fiveDetail = EruptMenuUtils.createMenu(FiveDetail.class, null, rootSort, MenuTypeEnum.TABLE, "重大风险“五个清单”", "FiveDetail", moduleId, "fa fa-html5");
        menus.add(fiveDetail);//五清单
        menus.add(EruptMenuUtils.createMenu(FiveChecklistsFoundation.class, fiveDetail, sort, MenuTypeEnum.TABLE, "基础清单", "FiveChecklistsFoundation", moduleId, null).hide());
        menus.add(EruptMenuUtils.createMenu(FiveChecklistsEmergencyDisposal.class, fiveDetail, sort, MenuTypeEnum.TABLE, "应急处置", "FiveChecklistsEmergencyDisposal", moduleId, null).hide());
        menus.add(EruptMenuUtils.createMenu(FiveChecklistsMonitor.class, fiveDetail, sort, MenuTypeEnum.TABLE, "监控检测", "FiveChecklistsMonitor", moduleId, null).hide());
        menus.add(EruptMenuUtils.createMenu(FiveChecklistsPreventionAndControl.class, fiveDetail, sort, MenuTypeEnum.TABLE, "防控措施", "FiveChecklistsPreventionAndControl", moduleId, null).hide());
        menus.add(EruptMenuUtils.createMenu(FiveChecklistsResponsibility.class, fiveDetail, sort, MenuTypeEnum.TABLE, "责任分工", "FiveChecklistsResponsibility", moduleId, null).hide());

        //五清单(政府)
        MetaMenu fiveDetailzf = EruptMenuUtils.createMenu(FiveDetailZf.class, null, rootSort, MenuTypeEnum.TABLE, "重大风险管控手册", "FiveDetailZf", moduleId, "fa fa-html5");
        menus.add(fiveDetailzf);//五清单

        menus.add(EruptMenuUtils.createMenu(FiveChecklistsResponsibility.class, fiveDetailzf, sort, MenuTypeEnum.TABLE, "责任分工", "FiveChecklistsResponsibilityZf", moduleId, null).hide());


        return menus;
    }

    @Override
    public void run(ApplicationArguments args) throws Exception {
        //EruptSpringUtil.getBean(EruptExcelController.class).importExcel(RiskDatabaseDepartment.class, "public/init/03-重大风险数据库.xls");
        //EruptSpringUtil.getBean(UnloadDataProxy.class).prepare();
    }
}
