package com.daliangang.rndpub.sql;

import org.springframework.stereotype.Repository;

/**
 * <AUTHOR>
 * @since :2023/4/25:9:59
 */
@Repository
public class InspectionResultSql {
    //
    public String selectInspectionResultNum (String orgCode) {

        String sql = "select COUNT(tir.id)-count(IF( inspection_result = 'PASS' and publish_status = 1, TRUE, NULL )) as num,'未整改完成' as type from tb_inspection_results tir left join tb_procedure tp on tir.inspection_name = tp.id where    date_format(tp.inspection_date, '%Y') = YEAR(NOW())";
        sql += "   and tir.check_object "+orgCode+"";

        sql += "  UNION all select COUNT(tir.id) as num ,'已整改完成' as type from tb_inspection_results tir left join tb_procedure tp on tir.inspection_name = tp.id where  tir.inspection_result = 'PASS' and publish_status = 1 and  date_format(tp.inspection_date, '%Y') = YEAR(NOW())";
        sql += "   and tir.check_object "+orgCode+"";

        return sql;
    }

    public String selectQyYqNum(String orgCode){
        String sql= "select COUNT(tir.id) as num ,'已逾期' as type from tb_inspection_results tir left join tb_procedure tp on tir.inspection_name = tp.id where   tir.be_overdue = '1' and  date_format(tp.inspection_date, '%Y') = YEAR(NOW())";
        sql += "  and tir.check_object ='"+orgCode+"'";
        return sql;
    }

    // 统计今年本企业被检查次数
     public String selectQyCheckNum(String orgCode){
        String sql= "select COUNT(tei.id) as num ,'今年被抽查数' as `type` from tb_enterprise_information tei left join tb_procedure tp on tei .procedure_id =tp.id where date_format(tp.inspection_date, '%Y') = YEAR(NOW())";
         sql += "  and tei.org_code ='"+orgCode+"'";
        return sql;
     }

    // 统计今年产生的问题数
    public String selectQyQuesNum(String orgCode){
        String sql= "select COUNT(tir.id) as num,'今年问题数' as type from tb_inspection_results tir left join tb_procedure tp on tir.inspection_name = tp.id where    date_format(tp.inspection_date, '%Y') = YEAR(NOW())";
        sql += "  and tir.check_object ='"+orgCode+"'";
        return sql;
    }
}
