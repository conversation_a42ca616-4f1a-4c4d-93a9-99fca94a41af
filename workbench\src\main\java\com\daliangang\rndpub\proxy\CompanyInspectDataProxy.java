package com.daliangang.rndpub.proxy;

import com.daliangang.rndpub.entity.CompanyInspect;
import com.daliangang.workbench.entity.Enterprise;
import org.springframework.stereotype.Service;
import xyz.erupt.annotation.fun.DataProxy;
import xyz.erupt.annotation.query.Condition;
import xyz.erupt.jpa.dao.EruptDao;
import xyz.erupt.toolkit.utils.EruptDaoUtils;

import javax.annotation.Resource;
import javax.transaction.Transactional;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/9/23
 */
@Service
public class CompanyInspectDataProxy implements DataProxy<CompanyInspect> {

    @Resource
    private EruptDao eruptDao;

//    @Override
//    @Transactional
//    public void searchCondition(Map<String, Object> condition) {
//        //清空内容
//        String truncateSql="truncate table tb_company_inspect";
//        eruptDao.getJdbcTemplate().execute(truncateSql);
//        //查询企业列表
//        String companySql="select * from tb_enterprise";
//        List<Enterprise> enterprises = EruptDaoUtils.selectOnes(companySql, Enterprise.class);
//        enterprises.forEach(enterprise -> {
//            CompanyInspect companyInspect = new CompanyInspect();
//            //设置企业名称
//            companyInspect.setCompany(enterprise.getOrgCode());
//            //设置检查次数
//            String inspectCountSql=String.format("SELECT count( enterprise_name ) AS inspectCount FROM `tb_enterprise_information` WHERE enterprise_name =( SELECT NAME FROM tb_enterprise WHERE org_code = '%s' ) \n" +
//                    "AND procedure_id IN ( SELECT id FROM tb_procedure ) AND state=1;",enterprise.getOrgCode());
//            String inspectCount = String.valueOf(EruptDaoUtils.selectMap(inspectCountSql).get("inspectCount"));
//            companyInspect.setInspectCount(Integer.parseInt(inspectCount));
//            //设置检查问题数量
//            String inspectProblemCountSql=String.format("SELECT\n" +
//                    "\tcount( problem_description ) AS inspectProblemCount \n" +
//                    "FROM\n" +
//                    "\t`tb_inspection_results` \n" +
//                    "WHERE\n" +
//                    "\tcheck_object = '%s' and publish_status=1",enterprise.getOrgCode());
//            String inspectProblemCount = String.valueOf(EruptDaoUtils.selectMap(inspectProblemCountSql).get("inspectProblemCount"));
//            companyInspect.setInspectProblemCount(Integer.parseInt(inspectProblemCount));
//            //设置已整改问题数量
//            String rectifyProblemCountSql=String.format("SELECT\n" +
//                    "\tcount( inspection_items ) AS rectifyProblemCount \n" +
//                    "FROM\n" +
//                    "\t`tb_inspection_results` \n" +
//                    "WHERE\n" +
//                    "\tcheck_object = '%s' \n" +
//                    "\tAND inspection_result = 'BY'",enterprise.getOrgCode());
//            String rectifyProblemCount = String.valueOf(EruptDaoUtils.selectMap(rectifyProblemCountSql).get("rectifyProblemCount"));
//            companyInspect.setRectifyProblemCount(Integer.parseInt(rectifyProblemCount));
//            //设置整改率
//            String rectifyChanceSql=String.format("SELECT\n" +
//                    "\tROUND((t1.rectifyProblemCount / t2.inspectProblemCount)*100,2) AS rectifyChance \n" +
//                    "FROM\n" +
//                    "\t( SELECT count( inspection_items ) AS rectifyProblemCount FROM `tb_inspection_results` WHERE check_object = '%s' AND inspection_result = 'BY' ) t1,\n" +
//                    "\t( SELECT count( problem_description ) AS inspectProblemCount FROM `tb_inspection_results` WHERE check_object = '%s' ) t2",enterprise.getOrgCode(),enterprise.getOrgCode());
//            String rectifyChance = String.valueOf(EruptDaoUtils.selectMap(rectifyChanceSql).get("rectifyChance"));
//            companyInspect.setRectifyChance(rectifyChance.equals("null")?"0.00%":rectifyChance+"%");
//            //检查年度
//            SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
//            String formatDate = simpleDateFormat.format(new Date());
//            companyInspect.setInspectYear(formatDate.substring(0,4));
//            //插入数据
//            eruptDao.merge(companyInspect);
//        });
//    }


    @Override
    @Transactional
    public String beforeFetch(List<Condition> conditions) {
        Map<String, Object> conditionMap = conditions.stream().collect(Collectors.toMap(
                Condition::getKey,
                Condition::getValue,
                (c1, c2) -> c1
        ));
        if(conditionMap.size()>0&&conditionMap.get("inspectYear")!=null&&!conditionMap.get("inspectYear").equals("")){
            //清空内容
            String truncateSql="truncate table tb_company_inspect";
            eruptDao.getJdbcTemplate().execute(truncateSql);
            //查询企业列表
            String companySql="select * from tb_enterprise";
            List<Enterprise> enterprises = EruptDaoUtils.selectOnes(companySql, Enterprise.class);
            enterprises.forEach(enterprise -> {
                CompanyInspect companyInspect = new CompanyInspect();
                //设置企业名称
                companyInspect.setCompany(enterprise.getOrgCode());
                //设置检查次数
                String inspectCountSql=String.format("SELECT count( enterprise_name ) AS inspectCount FROM `tb_enterprise_information` WHERE enterprise_name =( SELECT NAME FROM tb_enterprise WHERE org_code = '%s' ) \n" +
                        "AND procedure_id IN ( SELECT id FROM tb_procedure WHERE YEAR ( inspection_date ) = '%s' ) AND state=1",enterprise.getOrgCode(),conditionMap.get("inspectYear"));
                String inspectCount = String.valueOf(EruptDaoUtils.selectMap(inspectCountSql).get("inspectCount"));
                companyInspect.setInspectCount(Integer.parseInt(inspectCount));
                //设置检查问题数量
                String inspectProblemCountSql=String.format("SELECT\n" +
                        "\tcount( tb_inspection_results.problem_description ) AS inspectProblemCount \n" +
                        "FROM\n" +
                        "\t`tb_inspection_results`\n" +
                        "\tINNER JOIN tb_procedure ON tb_inspection_results.inspection_name = tb_procedure.id \n" +
                        "WHERE\n" +
                        "\ttb_inspection_results.check_object = '%s' \n" +
                        "\tAND tb_inspection_results.publish_status = 1 \n" +
                        "\tAND YEAR ( tb_procedure.inspection_date ) = '%s'",enterprise.getOrgCode(),conditionMap.get("inspectYear"));
                String inspectProblemCount = String.valueOf(EruptDaoUtils.selectMap(inspectProblemCountSql).get("inspectProblemCount"));
                companyInspect.setInspectProblemCount(Integer.parseInt(inspectProblemCount));
                //设置已整改问题数量
                String rectifyProblemCountSql=String.format("SELECT\n" +
                        "\tcount( tb_inspection_results.inspection_items ) AS rectifyProblemCount \n" +
                        "FROM\n" +
                        "\t`tb_inspection_results`\n" +
                        "\tINNER JOIN tb_procedure ON tb_inspection_results.inspection_name = tb_procedure.id \n" +
                        "WHERE\n" +
                        "\ttb_inspection_results.check_object = '%s' \n" +
                        " AND publish_status =1 " +
                        "\tAND tb_inspection_results.inspection_result = 'BY' \n" +
                        "\tAND YEAR ( tb_procedure.inspection_date )= '%s'",enterprise.getOrgCode(),conditionMap.get("inspectYear"));
                String rectifyProblemCount = String.valueOf(EruptDaoUtils.selectMap(rectifyProblemCountSql).get("rectifyProblemCount"));
                companyInspect.setRectifyProblemCount(Integer.parseInt(rectifyProblemCount));
                //设置整改率
                String rectifyChanceSql=String.format("SELECT \n" +
                        " ROUND((t1.rectifyProblemCount / t2.inspectProblemCount)*100,2) AS rectifyChance\n" +
                        "FROM\n" +
                        "\t(SELECT\n" +
                        "\tcount( tb_inspection_results.inspection_items ) AS rectifyProblemCount \n" +
                        "FROM\n" +
                        "\t`tb_inspection_results`\n" +
                        "\tINNER JOIN tb_procedure ON tb_inspection_results.inspection_name = tb_procedure.id \n" +
                        "WHERE\n" +
                        "\ttb_inspection_results.check_object = '%s' \n" +
                        "\tAND tb_inspection_results.inspection_result = 'BY' \n" +
                        "\tAND YEAR ( tb_procedure.inspection_date )= '%s') t1,\n" +
                        "(SELECT\n" +
                        "\tcount( tb_inspection_results.problem_description ) AS inspectProblemCount \n" +
                        "FROM\n" +
                        "\t`tb_inspection_results`\n" +
                        "\tINNER JOIN tb_procedure ON tb_inspection_results.inspection_name = tb_procedure.id \n" +
                        "WHERE\n" +
                        "\ttb_inspection_results.check_object = '%s' \n" +
                        "\tAND tb_inspection_results.publish_status = 1 \n" +
                        "\tAND YEAR ( tb_procedure.inspection_date ) = '%s') t2",enterprise.getOrgCode(),conditionMap.get("inspectYear"),enterprise.getOrgCode(),conditionMap.get("inspectYear"));
                String rectifyChance = String.valueOf(EruptDaoUtils.selectMap(rectifyChanceSql).get("rectifyChance"));
                companyInspect.setRectifyChance(rectifyChance.equals("null")?"0.00%":rectifyChance+"%");
                //检查年度
                companyInspect.setInspectYear(String.valueOf(conditionMap.get("inspectYear")));
                //插入数据
                eruptDao.merge(companyInspect);
            });
            return null;
        }
        //清空内容
        String truncateSql="truncate table tb_company_inspect";
        eruptDao.getJdbcTemplate().execute(truncateSql);
        //查询企业列表
        String companySql="select * from tb_enterprise";
        List<Enterprise> enterprises = EruptDaoUtils.selectOnes(companySql, Enterprise.class);
        enterprises.forEach(enterprise -> {
            CompanyInspect companyInspect = new CompanyInspect();
            //设置企业名称
            companyInspect.setCompany(enterprise.getOrgCode());
            //设置检查次数
            String inspectCountSql=String.format("SELECT count( enterprise_name ) AS inspectCount FROM `tb_enterprise_information` WHERE enterprise_name =( SELECT NAME FROM tb_enterprise WHERE org_code = '%s' ) \n" +
                    "AND procedure_id IN ( SELECT id FROM tb_procedure ) AND state=1;",enterprise.getOrgCode());
            String inspectCount = String.valueOf(EruptDaoUtils.selectMap(inspectCountSql).get("inspectCount"));
            companyInspect.setInspectCount(Integer.parseInt(inspectCount));
            //设置检查问题数量
            String inspectProblemCountSql=String.format("SELECT\n" +
                    "\tcount( problem_description ) AS inspectProblemCount \n" +
                    "FROM\n" +
                    "\t`tb_inspection_results` \n" +
                    "WHERE\n" +
                    "\tcheck_object = '%s' and publish_status=1",enterprise.getOrgCode());
            String inspectProblemCount = String.valueOf(EruptDaoUtils.selectMap(inspectProblemCountSql).get("inspectProblemCount"));
            companyInspect.setInspectProblemCount(Integer.parseInt(inspectProblemCount));
            //设置已整改问题数量
            String rectifyProblemCountSql=String.format("SELECT\n" +
                    "\tcount( inspection_items ) AS rectifyProblemCount \n" +
                    "FROM\n" +
                    "\t`tb_inspection_results` \n" +
                    "WHERE\n" +
                    "\tcheck_object = '%s' \n" +
                    "\tAND inspection_result = 'BY'",enterprise.getOrgCode());
            String rectifyProblemCount = String.valueOf(EruptDaoUtils.selectMap(rectifyProblemCountSql).get("rectifyProblemCount"));
            companyInspect.setRectifyProblemCount(Integer.parseInt(rectifyProblemCount));
            //设置整改率
            String rectifyChanceSql=String.format("SELECT\n" +
                    "\tROUND((t1.rectifyProblemCount / t2.inspectProblemCount)*100,2) AS rectifyChance \n" +
                    "FROM\n" +
                    "\t( SELECT count( inspection_items ) AS rectifyProblemCount FROM `tb_inspection_results` WHERE check_object = '%s' AND inspection_result = 'BY' ) t1,\n" +
                    "\t( SELECT count( problem_description ) AS inspectProblemCount FROM `tb_inspection_results` WHERE check_object = '%s' ) t2",enterprise.getOrgCode(),enterprise.getOrgCode());
            String rectifyChance = String.valueOf(EruptDaoUtils.selectMap(rectifyChanceSql).get("rectifyChance"));
            companyInspect.setRectifyChance(rectifyChance.equals("null")?"0.00%":rectifyChance+"%");
            //检查年度
//            SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
//            String formatDate = simpleDateFormat.format(new Date());
//            companyInspect.setInspectYear(formatDate.substring(0,4));
            //插入数据
            eruptDao.merge(companyInspect);
        });
        return null;
    }
}
