package com.daliangang.device.config;

import com.daliangang.device.entity.*;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.context.annotation.Configuration;
import xyz.erupt.core.constant.MenuTypeEnum;
import xyz.erupt.core.module.EruptModule;
import xyz.erupt.core.module.EruptModuleInvoke;
import xyz.erupt.core.module.MetaMenu;
import xyz.erupt.core.module.ModuleInfo;
import xyz.erupt.core.util.EruptSpringUtil;
import xyz.erupt.mns.msg.entity.EruptMessage;
import xyz.erupt.upms.platform.EruptOption;
import xyz.erupt.upms.platform.EruptOptions;
import xyz.erupt.upms.service.EruptMenuService;
import xyz.erupt.upms.util.EruptMenuUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * @Author: zengwensi
 * @Description:
 * @Date: Created in 2023/3/17 15:18
 * @Modified By
 */
@EruptOptions(
        @EruptOption(name = EruptMessage.MNS_SENDER_SQL, value = "select name from tb_department where parent_id is null union select name from tb_enterprise ", desc = "收件人sql")
)
@Configuration
public class DeviceConfiguration implements EruptModule, ApplicationRunner {

    static {
        EruptModuleInvoke.addEruptModule(DeviceConfiguration.class);
    }

    @Override
    public ModuleInfo info() {
        return ModuleInfo.builder().name("daliangang-device").build();
    }

    @Override
    public List<MetaMenu> initMenus() {
        EruptMenuService eruptMenuService = EruptSpringUtil.getBean(EruptMenuService.class);
        String moduleId = "device";
        eruptMenuService.registerModule(moduleId, "港口设施维护管理系统", 3);
        AtomicInteger rootSort = new AtomicInteger(300);
        AtomicInteger sort = new AtomicInteger();
        List<MetaMenu> menus = new ArrayList<>();

        MetaMenu deviceMan = EruptMenuUtils.createRootMenu(null, "$dlg-deviceMan", "设施管理", "fa fa-cubes", rootSort, moduleId);
        menus.add(deviceMan);

        menus.add(EruptMenuUtils.createMenu(PortArea.class, deviceMan, sort, MenuTypeEnum.TABLE, "港区管理", "PortArea", moduleId, null));//港区管理
        menus.add(EruptMenuUtils.createMenu(Wharf.class, deviceMan, sort, MenuTypeEnum.TABLE, "码头管理", "Wharf", moduleId, null));//码头管理
        menus.add(EruptMenuUtils.createMenu(Berth.class, deviceMan, sort, MenuTypeEnum.TABLE, "泊位管理", "Berth", moduleId, null));//泊位管理
        menus.add(EruptMenuUtils.createMenu(Anchorage.class, deviceMan, sort, MenuTypeEnum.TABLE, "锚地管理", "Anchorage", moduleId, null));//锚地管理
        menus.add(EruptMenuUtils.createMenu(Arrival.class, deviceMan, sort, MenuTypeEnum.TABLE, "进港航道管理", "Arrival", moduleId, null));//进港航道管理
        menus.add(EruptMenuUtils.createMenu(Railway.class, deviceMan, sort, MenuTypeEnum.TABLE, "港区铁路管理", "Railway", moduleId, null));//港区铁路管理
        menus.add(EruptMenuUtils.createMenu(Highway.class, deviceMan, sort, MenuTypeEnum.TABLE, "疏港公路管理", "Highway", moduleId, null));//疏港公路管理
        menus.add(EruptMenuUtils.createMenu(TankFarm.class, deviceMan, sort, MenuTypeEnum.TABLE, "罐区管理", "TankFarm", moduleId, null));//罐区管理
        menus.add(EruptMenuUtils.createMenu(TankGroup.class, deviceMan, sort, MenuTypeEnum.TABLE, "罐组管理", "TankGroup", moduleId, null));//罐组管理
        menus.add(EruptMenuUtils.createMenu(StorageTank.class, deviceMan, sort, MenuTypeEnum.TABLE, "储罐管理", "StorageTank", moduleId, null)); //储罐管理
        menus.add(EruptMenuUtils.createMenu(LoadingDock.class, deviceMan, sort, MenuTypeEnum.TABLE, "装卸栈台管理", "LoadingDock", moduleId, null)); //装卸栈台管理
        menus.add(EruptMenuUtils.createMenu(Yard.class, deviceMan, sort, MenuTypeEnum.TABLE, "堆场管理", "Yard", moduleId, null));//堆场管理
        menus.add(EruptMenuUtils.createMenu(Warehouse.class, deviceMan, sort, MenuTypeEnum.TABLE, "仓库管理", "Warehouse", moduleId, null));//仓库管理
        menus.add(EruptMenuUtils.createMenu(PositionDocking.class, deviceMan, sort, MenuTypeEnum.TABLE, "第三方位置数据关联", "PositionDocking", moduleId, null));//第三方位置数据关联

        MetaMenu deviceCheckMan = EruptMenuUtils.createRootMenu(null, "$dlg-deviceCheckMan", "设施检测管理", "fa fa-cog text-blue", rootSort, moduleId);
        menus.add(deviceCheckMan);

        menus.add(EruptMenuUtils.createMenu(WharfStructureInspection.class, deviceCheckMan, sort, MenuTypeEnum.TABLE, "码头结构检测", "WharfStructureInspection", moduleId, null));//码头结构检测
        menus.add(EruptMenuUtils.createMenu(FacilityInspection.class, deviceCheckMan, sort, MenuTypeEnum.TABLE, "消防设施检测", "FacilityInspection", moduleId, null));//消防设施检测
        menus.add(EruptMenuUtils.createMenu(LightningProtection.class, deviceCheckMan, sort, MenuTypeEnum.TABLE, "防雷装置检测", "LightningProtection", moduleId, null));//防雷装置检测
        menus.add(EruptMenuUtils.createMenu(PressureVessel.class, deviceCheckMan, sort, MenuTypeEnum.TABLE, "压力容器检测", "PressureVessel", moduleId, null)); //压力容器检测
        menus.add(EruptMenuUtils.createMenu(TankInspection.class, deviceCheckMan, sort, MenuTypeEnum.TABLE, "储罐检测", "TankInspection", moduleId, null));//储罐检测
        menus.add(EruptMenuUtils.createMenu(Penstock.class, deviceCheckMan, sort, MenuTypeEnum.TABLE, "压力管道检测", "Penstock", moduleId, null));//压力管道检测
        menus.add(EruptMenuUtils.createMenu(Hose.class, deviceCheckMan, sort, MenuTypeEnum.TABLE, "输油臂（软管）检测", "Hose", moduleId, null));//输油臂（软管）检测
        menus.add(EruptMenuUtils.createMenu(PressureGauge.class, deviceCheckMan, sort, MenuTypeEnum.TABLE, "压力表检测", "PressureGauge", moduleId, null));//压力表检测
        menus.add(EruptMenuUtils.createMenu(SafetyValve.class, deviceCheckMan, sort, MenuTypeEnum.TABLE, "安全阀检测", "SafetyValve", moduleId, null)); //安全阀检测
        menus.add(EruptMenuUtils.createMenu(CombustibleGas.class, deviceCheckMan, sort, MenuTypeEnum.TABLE, "可燃气体报警器检测", "CombustibleGas", moduleId, null));//可燃气体报警器检测
        menus.add(EruptMenuUtils.createMenu(HoistingMachinery.class, deviceCheckMan, sort, MenuTypeEnum.TABLE, "起重机械检测", "HoistingMachinery", moduleId, null));//起重机械检测
        menus.add(EruptMenuUtils.createMenu(OtherTests.class, deviceCheckMan, sort, MenuTypeEnum.TABLE, "其他检测", "OtherTests", moduleId, null));//其他检测

        return menus;
    }

    @Override
    public void run(ApplicationArguments args) throws Exception {

    }
}
