/*
 Navicat Premium Data Transfer

 Source Server         : 大连港阿里测试库
 Source Server Type    : MySQL
 Source Server Version : 80027
 Source Host           : dlg-ali.yundingyun.net:30036
 Source Schema         : daliangang

 Target Server Type    : MySQL
 Target Server Version : 80027
 File Encoding         : 65001

 Date: 25/07/2023 17:45:43
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for e_bi
-- ----------------------------
DROP TABLE IF EXISTS `e_bi`;
CREATE TABLE `e_bi`  (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `create_by` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `create_time` datetime NULL DEFAULT NULL,
  `update_by` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '更新人',
  `update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  `cache_time` int NULL DEFAULT NULL COMMENT '缓存时间（秒）',
  `code` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '编码',
  `count_statement` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '总条数SQL',
  `export` bit(1) NULL DEFAULT NULL COMMENT '导出',
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '名称',
  `page_type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '分页方式',
  `refresh_time` int NULL DEFAULT NULL COMMENT '自动刷新周期（秒）',
  `remark` varchar(2000) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '报表描述',
  `sql_statement` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '取值SQL',
  `bi_group_id` bigint NULL DEFAULT NULL COMMENT '组别',
  `class_handler_id` bigint NULL DEFAULT NULL COMMENT '处理类',
  `datasource_id` bigint NULL DEFAULT NULL COMMENT '数据源',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `UKls8yyipfi4uh5dp6kkvnffr9s`(`code` ASC) USING BTREE,
  INDEX `FKkdvrjqg0be7n88kr8circjt40`(`bi_group_id` ASC) USING BTREE,
  INDEX `FKpmu0yvj13ahrp2s7nfn2nr1q3`(`class_handler_id` ASC) USING BTREE,
  INDEX `FKmmt5bs9246wwljcur1ccnkdg7`(`datasource_id` ASC) USING BTREE,
  CONSTRAINT `FKkdvrjqg0be7n88kr8circjt40` FOREIGN KEY (`bi_group_id`) REFERENCES `e_bi_group` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `FKmmt5bs9246wwljcur1ccnkdg7` FOREIGN KEY (`datasource_id`) REFERENCES `e_bi_datasource` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `FKpmu0yvj13ahrp2s7nfn2nr1q3` FOREIGN KEY (`class_handler_id`) REFERENCES `e_bi_class_handler` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 9 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '报表配置' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of e_bi
-- ----------------------------
INSERT INTO `e_bi` VALUES (1, NULL, NULL, '系统', '2023-07-14 11:34:15', 60, 'yaUpmw0P', NULL, b'1', '数据统计-企业页面', 'backend', 60, NULL, NULL, 1, 1, 1);
INSERT INTO `e_bi` VALUES (3, NULL, NULL, '系统', '2023-07-14 11:34:36', 60, 'jORjaKWC', NULL, b'1', ' 双随机-政府', 'backend', 60, NULL, NULL, 1, NULL, 2);
INSERT INTO `e_bi` VALUES (4, NULL, NULL, '系统', '2023-05-08 14:38:42', 60, 'TSR9YULz', NULL, b'1', '数据统计-政府页面', 'front', 60, NULL, NULL, 1, 2, 1);
INSERT INTO `e_bi` VALUES (5, NULL, NULL, '系统', '2023-07-14 11:35:18', 60, 'JI6pTjkM', NULL, b'1', '双随机-企业', 'backend', 60, NULL, NULL, 1, NULL, 2);
INSERT INTO `e_bi` VALUES (6, NULL, NULL, '系统', '2023-05-10 16:20:09', 60, '9wIbxCTS', NULL, b'1', '应急评估统计', 'backend', 60, NULL, NULL, 1, NULL, 3);
INSERT INTO `e_bi` VALUES (7, NULL, NULL, '系统', '2023-07-14 11:39:03', 60, 'EA2LbAQx', NULL, b'1', '从业人员-考生统计', 'backend', 60, NULL, 'SELECT\r\n    `user`.id userId,\r\n	`user`.real_name \'考生姓名\',\r\n	organ.`name` \'企业名称\',\r\n	ifnull( finishPeriod, 0 ) \'总学时\',\r\n	ifnull( shouldTrainCount, 0 ) \'应参加培训次数\',\r\n	ifnull( actualTrainCount, 0 ) \'实际参加培训次数\',\r\n	ifnull(( shouldTrainCount - actualTrainCount ), 0 ) \'缺勤次数\',\r\n	concat( ifnull( passPaperRatio, 0 ), \'%\' ) \'考试合格率\'\r\nFROM\r\n	( SELECT * FROM t_user_info WHERE is_admin = 0  and status = 1  \r\n    #REPLACESQL\r\n    ) `user`\r\n	LEFT JOIN t_organ organ ON `user`.org_code = organ.org_code\r\n	INNER JOIN ( SELECT user_id, sum( finish_period ) finishPeriod FROM t_user_course GROUP BY user_id ) userPeriod ON `user`.id = userPeriod.user_id\r\n	LEFT JOIN (\r\n		SELECT\r\n			user_id,\r\n			count(*) shouldTrainCount,\r\n			count( IF ( sta = 2, 1, NULL )) actualTrainCount \r\n		FROM\r\n			t_train_user \r\n		WHERE\r\n			STATUS = 2 \r\n		GROUP BY\r\n			user_id \r\n	) userTrain ON `user`.id = userTrain.user_id\r\n	LEFT JOIN (\r\n	SELECT\r\n		*,\r\n		round( passPaperCount * 100 / totalPaperCount, 2 ) passPaperRatio \r\n	FROM\r\n	( SELECT user_id, count( IF ( STATUS = 3, 1, NULL )) passPaperCount, count(*) totalPaperCount FROM t_user_paper GROUP BY user_id ) t \r\n	) userPaper ON `user`.id = userPaper.user_id\r\n	where 1 = 1 \r\n	#SEARCHSQL\r\n', 1, 19, 4);
INSERT INTO `e_bi` VALUES (8, NULL, NULL, '系统', '2023-07-14 11:37:36', 60, 'OWXfcRW8', NULL, b'1', '从业人员-企业统计', 'backend', 60, NULL, 'SELECT\r\n	organ.orgId ,\r\n	organ.organName \'企业名称\',\r\n	ifnull( postTrainCount, 0 )  \'岗位评估次数\',\r\n	ifnull( legalTrainCount, 0 ) \'法定培训次数\',\r\n	ifnull( selfTrainCount, 0 ) \'自组织培训次数\',\r\n	ifnull( trainUserCount, 0 ) \'总培训人次\' \r\nFROM\r\n	( SELECT id orgId, NAME organName FROM t_organ WHERE 1 = 1 \r\n	#ORGSQL\r\n	) organ\r\n	LEFT JOIN ( SELECT org_id, count(*) postTrainCount FROM t_evaluation_man WHERE 1 = 1 \r\n	#POSTTRAINSQL \r\n	GROUP BY org_id ) postTrain ON organ.orgId = postTrain.org_id\r\n	LEFT JOIN ( SELECT organ_id, count(*) legalTrainCount FROM t_train_info WHERE 1 = 1 \r\n	#LEGALTRAINSQL\r\n	GROUP BY organ_id ) legalTrain ON organ.orgId = legalTrain.organ_id\r\n	LEFT JOIN ( SELECT organ_id, count(*) selfTrainCount FROM t_train_info WHERE 1 = 1 \r\n	#SELFTRAINSQL\r\n	GROUP BY organ_id ) selfTrain ON organ.orgId = selfTrain.organ_id\r\n	LEFT JOIN (\r\n	SELECT\r\n		train.organ_id,\r\n		count(*) trainUserCount \r\n	FROM\r\n		t_train_user trainUser,\r\n		t_train_info train \r\n	WHERE\r\n		trainUser.train_id = train.id \r\n		#USERTRAINSQL\r\n	GROUP BY\r\n		train.organ_id \r\n	) trainUser ON organ.orgId = trainUser.organ_id ', 1, 20, 4);

-- ----------------------------
-- Table structure for e_bi_chart
-- ----------------------------
DROP TABLE IF EXISTS `e_bi_chart`;
CREATE TABLE `e_bi_chart`  (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `create_by` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `create_time` datetime NULL DEFAULT NULL,
  `update_by` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '更新人',
  `update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  `cache_time` int NULL DEFAULT NULL COMMENT '缓存时间（秒）',
  `chart_option` varchar(4000) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '自定义图表配置',
  `code` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '编码',
  `grid` int NULL DEFAULT NULL COMMENT '栅格数',
  `height` int NULL DEFAULT NULL COMMENT '高度(px)',
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '名称',
  `sort` int NULL DEFAULT NULL COMMENT '显示顺序',
  `sql_statement` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '图表SQL',
  `type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '图表类型',
  `bi_id` bigint NULL DEFAULT NULL,
  `bi_tpl_id` bigint NULL DEFAULT NULL COMMENT '报表模板',
  `class_handler_id` bigint NULL DEFAULT NULL COMMENT '处理类',
  `data_source_id` bigint NULL DEFAULT NULL COMMENT '数据源',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `UKmcmnjuj7y5ppitao1jck5s2xy`(`code` ASC, `bi_id` ASC) USING BTREE,
  INDEX `FK6012onu7tuk4yx1734cqa39uk`(`bi_id` ASC) USING BTREE,
  INDEX `FK4jc135g48190nvujmk88q10mw`(`bi_tpl_id` ASC) USING BTREE,
  INDEX `FKqqhuuqs4rnvt8rwjcqmiei6gq`(`class_handler_id` ASC) USING BTREE,
  INDEX `FK7kyuns0awcjn0ywf0ma2gfm3d`(`data_source_id` ASC) USING BTREE,
  CONSTRAINT `FK4jc135g48190nvujmk88q10mw` FOREIGN KEY (`bi_tpl_id`) REFERENCES `e_bi_tpl` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `FK6012onu7tuk4yx1734cqa39uk` FOREIGN KEY (`bi_id`) REFERENCES `e_bi` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `FK7kyuns0awcjn0ywf0ma2gfm3d` FOREIGN KEY (`data_source_id`) REFERENCES `e_bi_datasource` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `FKqqhuuqs4rnvt8rwjcqmiei6gq` FOREIGN KEY (`class_handler_id`) REFERENCES `e_bi_class_handler` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 37 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '图表配置' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of e_bi_chart
-- ----------------------------
INSERT INTO `e_bi_chart` VALUES (4, NULL, NULL, '系统', '2023-05-24 09:30:26', 60, NULL, 'KaFZFTPq', 24, 340, '问题类型统计', 60, 'SELECT\r\n	inspection_items 检查事项,\r\n	COUNT(*) 数量 \r\nFROM\r\n	tb_inspection_results \r\nWHERE\r\n	DATE_FORMAT( create_time, \'%Y\' )= \'#searchDate\'\r\n#REPLACESQL\r\nGROUP BY\r\n	检查事项 \r\nORDER BY\r\n	数量 DESC;', 'Column', 3, 1, 8, 2);
INSERT INTO `e_bi_chart` VALUES (5, NULL, NULL, '系统', '2023-05-24 09:27:32', 60, NULL, 'uVEtcCuN', 24, 50, '截止时间', 1, 'SELECT DATE_FORMAT(NOW(),\'%Y-%m-%d %H:%i:%S\') now', 'tpl', 3, 2, NULL, NULL);
INSERT INTO `e_bi_chart` VALUES (8, NULL, NULL, '系统', '2023-05-24 09:31:21', 60, NULL, 'OPwZKyMF', 24, 340, '执法人员检查情况', 90, 'SELECT\r\n	i.NAME,\r\n	count(*) 数量\r\nFROM\r\n	tb_inspector i,\r\n	tb_procedure p \r\nWHERE\r\n	i.procedure_id = p.id \r\nAND YEAR ( p.inspection_date ) = \'#searchDate\'\r\n #REPLACESQL\r\nGROUP BY\r\n	i.NAME', 'Line', 3, NULL, 10, 2);
INSERT INTO `e_bi_chart` VALUES (9, NULL, NULL, '系统', '2023-05-24 09:27:51', 60, NULL, 'KDrbJhLY', 8, 100, '检查次数', 10, 'SELECT\r\n	t.searchCount,\r\nIF\r\n	( searchCount > searchBeforeCount, CONCAT( \'+\', searchCount - searchBeforeCount ), searchCount - searchBeforeCount ) compare \r\nFROM\r\n	(\r\n	SELECT\r\n		count(\r\n		IF\r\n		( YEAR ( inspection_date ) = \'#searchDate\', TRUE, NULL )) searchCount,\r\n		count(\r\n		IF\r\n		( YEAR ( inspection_date ) = YEAR ( DATE_SUB( concat(\'#searchDate\',\'-01-01\'), INTERVAL 1 YEAR )), TRUE, NULL )) searchBeforeCount \r\n	FROM\r\n	tb_procedure \r\n	where 1 =1 \r\n	#REPLACESQL\r\n	) t\r\n	\r\n	\r\n	', 'tpl', 3, 1, 3, 2);
INSERT INTO `e_bi_chart` VALUES (10, NULL, NULL, '系统', '2023-07-25 17:34:28', 60, NULL, 'AKXlzZCM', 24, 120, ' 作业数量统计', 10, 'select SUM(unstart) unstart,SUM(progress) progress,SUM(finish) finish,SUM(total) total from\r\n(SELECT\r\n	count(IF( now() < ab_time, TRUE, NULL )) unstart,\r\n	count(IF( now() BETWEEN ab_time AND ae_time, TRUE, NULL )) progress ,\r\n	count(IF( now() > ae_time, TRUE, NULL )) finish,\r\n	count(*) total \r\nFROM\r\n	tb_unload_ship \r\nWHERE org_code = \'#ORGCODE\' and YEAR ( ab_time ) = \'#searchDate\'\r\n	UNION ALL\r\nSELECT \r\n  count( IF ( now() < ab_time, TRUE, NULL )) unstart,\r\n	count( IF ( now() BETWEEN ab_time AND ae_time, TRUE, NULL )) progress ,\r\n	count( IF ( now() > ae_time, TRUE, NULL )) finish ,\r\n	count(*) total \r\nFROM\r\n	tb_unload \r\nWHERE org_code = \'#ORGCODE\' and YEAR ( ab_time ) = \'#searchDate\'\r\n	UNION ALL\r\nSELECT\r\n	count( IF ( now() < star_date, TRUE, NULL )) unstart,\r\n	count( IF ( now() BETWEEN star_date AND end_date, TRUE, NULL )) progress ,\r\n	count( IF ( now() > end_date, TRUE, NULL )) finish ,\r\n	count(*) total \r\nFROM\r\n	tb_maintenance \r\nWHERE org_code = \'#ORGCODE\' and YEAR ( star_date ) = \'#searchDate\')t\r\n', 'tpl', 1, 3, 1, 1);
INSERT INTO `e_bi_chart` VALUES (11, NULL, NULL, '系统', '2023-06-07 10:45:22', 60, NULL, 'YHOhaVqe', 24, 190, '风险类型统计', 20, 'SELECT\r\n	name \'风险类型\',\r\n	sum( process ) \'现有风险数量\',\r\n	sum( finished ) \'历史风险数量\' \r\nFROM\r\n(SELECT\r\n		s3.`NAME`,\r\n		count(\r\n		IF\r\n		( now() BETWEEN s.ab_time AND s.ae_time, TRUE, NULL )) process,\r\n		count(\r\n		IF\r\n		( now() > s.ae_time, TRUE, NULL )) finished \r\nFROM\r\n		tb_unload s,\r\n		(SELECT id,risk_type FROM tb_risk_database_enterprise) s2,\r\n		(	SELECT\r\n				i.NAME NAME,\r\n				i.`code` CODE \r\n			FROM\r\n				e_dict d\r\n				INNER JOIN e_dict_item i ON d.id = i.erupt_dict_id\r\n			AND d.`code` = \'riskType\'	) s3\r\nWHERE s.risk_database=s2.id	and s2.risk_type=s3.`code`\r\n #REPLACESQL\r\nGROUP BY s3.`NAME`		\r\nUNION ALL\r\nSELECT\r\n		s3.`NAME`,\r\n		count(\r\n		IF\r\n		( now() BETWEEN s.ab_time AND s.ae_time, TRUE, NULL )) process,\r\n		count(\r\n		IF\r\n		( now() > s.ae_time, TRUE, NULL )) finished \r\nFROM\r\n		tb_unload_ship s,\r\n		(SELECT id,risk_type FROM tb_risk_database_enterprise) s2,\r\n		(	SELECT\r\n				i.NAME NAME,\r\n				i.`code` CODE \r\n			FROM\r\n				e_dict d\r\n				INNER JOIN e_dict_item i ON d.id = i.erupt_dict_id\r\n			AND d.`code` = \'riskType\'	) s3\r\nWHERE s.risk_database=s2.id	and s2.risk_type=s3.`code`\r\n #REPLACESQL\r\nGROUP BY s3.`NAME`) k		\r\nGROUP BY name', 'tpl', 1, 13, 1, 1);
INSERT INTO `e_bi_chart` VALUES (12, NULL, NULL, '系统', '2023-07-25 17:41:10', 60, NULL, 'JeSMjKGQ', 24, 190, '作业风险数量统计', 30, 'SELECT\r\n	i.NAME \'作业类型\',\r\n	count( IF ( now() BETWEEN ab_time AND ae_time, TRUE, NULL )) \'现有风险数量\',\r\n	count( IF ( now() > ae_time, TRUE, NULL )) \'历史风险数量\' \r\nFROM\r\n	e_dict d,\r\n	e_dict_item i,\r\n	tb_unload_ship t \r\nWHERE\r\n	d.id = i.erupt_dict_id \r\n	AND d.CODE = \'workTypeShip\' \r\n	AND t.work_type = i.`code` \r\n	AND org_code = \'#ORGCODE\'\r\n	AND YEAR ( t.ab_time ) = \'#searchDate\'\r\n	group by t.work_type,i.name \r\n	union all\r\n	\r\n	\r\n	SELECT\r\n	i.NAME \'作业类型\',\r\n	count( IF ( now() BETWEEN ab_time AND ae_time, TRUE, NULL )) \'现有风险数量\',\r\n	count( IF ( now() > ae_time, TRUE, NULL )) \'历史风险数量\' \r\nFROM\r\n	e_dict d,\r\n	e_dict_item i,\r\n	tb_unload t \r\nWHERE\r\n	d.id = i.erupt_dict_id \r\n	AND d.CODE = \'workType\' \r\n	AND t.work_type = i.`code` \r\n	AND org_code = \'#ORGCODE\'\r\n	AND YEAR ( t.ab_time ) = \'#searchDate\'\r\n	group by t.work_type,i.name \r\n	union all\r\n	\r\n	SELECT\r\n	i.NAME \'作业类型\',\r\n	count( IF ( now() BETWEEN star_date AND end_date, TRUE, NULL )) \'现有风险数量\' ,\r\n	count( IF ( now() > end_date, TRUE, NULL )) \'历史风险数量\' \r\nFROM\r\n	e_dict d,\r\n	e_dict_item i,\r\n	tb_maintenance t \r\nWHERE\r\n	d.id = i.erupt_dict_id \r\n	AND d.CODE = \'MaintenanceType\' \r\n	AND t.type = i.`code` \r\n	AND org_code = \'#ORGCODE\'\r\n	AND YEAR ( t.star_date ) = \'#searchDate\'\r\n	group by t.type,i.name \r\n', 'table', 1, NULL, 1, 1);
INSERT INTO `e_bi_chart` VALUES (13, NULL, NULL, '系统', '2023-06-06 11:51:03', 60, NULL, 'nZrSzkWw', 24, 190, '企业作业数量统计', 40, 'SELECT\r\n	e.NAME \'企业名称\',\r\n	ifnull( t.unstart, 0 ) \'未开始作业\',\r\n	ifnull( t.progress, 0 ) \'正在进行中作业\',\r\n	ifnull( t.finish, 0 ) \'已结束作业\',\r\n	ifnull( t.total, 0 ) \'作业总数\' \r\nFROM\r\n	tb_enterprise e\r\n	LEFT JOIN (\r\n	SELECT\r\n		org_code,\r\n		SUM( unstart ) unstart,\r\n		SUM( progress ) progress,\r\n		SUM( finish ) finish,\r\n		SUM( total ) total \r\n	FROM\r\n		(\r\n		SELECT\r\n			org_code,\r\n			count( IF ( now() < ab_time, TRUE, NULL )) unstart,\r\n			count( IF ( now() BETWEEN ab_time AND ae_time, TRUE, NULL )) progress,\r\n			count( IF ( now() > ae_time, TRUE, NULL )) finish,\r\n			count(*) total \r\n		FROM\r\n			tb_unload_ship \r\n			where 1 =1 \r\n			#REPLACESQL\r\n			\r\n		GROUP BY\r\n			org_code UNION \r\n\r\n		SELECT\r\n			org_code,\r\n			count( IF ( now() < ab_time, TRUE, NULL )) unstart,\r\n			count( IF ( now() BETWEEN ab_time AND ae_time, TRUE, NULL )) progress,\r\n			count( IF ( now() > ae_time, TRUE, NULL )) finish,\r\n			count(*) total \r\n		FROM\r\n			tb_unload \r\n			where 1 =1 \r\n			#REPLACESQL\r\n			\r\n		GROUP BY\r\n			org_code UNION \r\n		SELECT\r\n			org_code,\r\n			count( IF ( now() < star_date, TRUE, NULL )) unstart,\r\n			count( IF ( now() BETWEEN star_date AND end_date, TRUE, NULL )) progress,\r\n			count( IF ( now() > end_date, TRUE, NULL )) finish,\r\n			count(*) total \r\n		FROM\r\n			tb_maintenance \r\n			where 1 =1 \r\n			#REPLACESQL\r\n			\r\n		GROUP BY\r\n			org_code \r\n		) t \r\n	GROUP BY\r\n	org_code \r\n	) t ON e.org_code = t.org_code', 'tpl', 4, 12, 2, 1);
INSERT INTO `e_bi_chart` VALUES (14, NULL, NULL, '系统', '2023-06-06 10:51:18', 60, NULL, 'sInCzSUQ', 24, 190, '风险类型统计', 20, 'SELECT\r\n	name \'风险类型\',\r\n	sum( process ) \'现有风险数量\',\r\n	sum( finished ) \'历史风险数量\' \r\nFROM\r\n(SELECT\r\n		s3.`NAME`,\r\n		count(\r\n		IF\r\n		( now() BETWEEN s.ab_time AND s.ae_time, TRUE, NULL )) process,\r\n		count(\r\n		IF\r\n		( now() > s.ae_time, TRUE, NULL )) finished \r\nFROM\r\n		tb_unload s,\r\n		(SELECT id,risk_type FROM tb_risk_database_enterprise) s2,\r\n		(	SELECT\r\n				i.NAME NAME,\r\n				i.`code` CODE \r\n			FROM\r\n				e_dict d\r\n				INNER JOIN e_dict_item i ON d.id = i.erupt_dict_id\r\n			AND d.`code` = \'riskType\'	) s3\r\nWHERE s.risk_database=s2.id	and s2.risk_type=s3.`code`\r\n #REPLACESQL\r\nGROUP BY s3.`NAME`		\r\nUNION ALL\r\nSELECT\r\n		s3.`NAME`,\r\n		count(\r\n		IF\r\n		( now() BETWEEN s.ab_time AND s.ae_time, TRUE, NULL )) process,\r\n		count(\r\n		IF\r\n		( now() > s.ae_time, TRUE, NULL )) finished \r\nFROM\r\n		tb_unload_ship s,\r\n		(SELECT id,risk_type FROM tb_risk_database_enterprise) s2,\r\n		(	SELECT\r\n				i.NAME NAME,\r\n				i.`code` CODE \r\n			FROM\r\n				e_dict d\r\n				INNER JOIN e_dict_item i ON d.id = i.erupt_dict_id\r\n			AND d.`code` = \'riskType\'	) s3\r\nWHERE s.risk_database=s2.id	and s2.risk_type=s3.`code`\r\n #REPLACESQL\r\nGROUP BY s3.`NAME`) k		\r\nGROUP BY name', 'tpl', 4, 10, 2, 1);
INSERT INTO `e_bi_chart` VALUES (15, NULL, NULL, '系统', '2023-06-06 10:51:54', 60, NULL, 'ukIUCVWw', 24, 190, '作业风险数量统计', 30, 'SELECT\r\n	i.NAME \'作业类型\',\r\n	count( IF ( now() BETWEEN ab_time AND ae_time, TRUE, NULL )) \'现有风险数量\',\r\n	count( IF ( now() > ae_time, TRUE, NULL )) \'历史风险数量\' \r\nFROM\r\n	e_dict d,\r\n	e_dict_item i,\r\n	tb_unload_ship t \r\nWHERE\r\n	d.id = i.erupt_dict_id \r\n	AND d.CODE = \'workTypeShip\' \r\n	AND t.work_type = i.`code` \r\n	#REPLACESQL\r\n	group by t.work_type,i.name union all\r\n	\r\n	\r\n	SELECT\r\n	i.NAME \'作业类型\',\r\n	count( IF ( now() BETWEEN ab_time AND ae_time, TRUE, NULL )) \'现有风险数量\',\r\n	count( IF ( now() > ae_time, TRUE, NULL )) \'历史风险数量\' \r\nFROM\r\n	e_dict d,\r\n	e_dict_item i,\r\n	tb_unload t \r\nWHERE\r\n	d.id = i.erupt_dict_id \r\n	AND d.CODE = \'workType\' \r\n	AND t.work_type = i.`code` \r\n	#REPLACESQL\r\n	group by t.work_type,i.name union all\r\n	\r\n	SELECT\r\n	i.NAME \'作业类型\',\r\n	count( IF ( now() BETWEEN star_date AND end_date, TRUE, NULL )) \'现有风险数量\' ,\r\n	count( IF ( now() > end_date, TRUE, NULL )) \'历史风险数量\' \r\nFROM\r\n	e_dict d,\r\n	e_dict_item i,\r\n	tb_maintenance t \r\nWHERE\r\n	d.id = i.erupt_dict_id \r\n	AND d.CODE = \'MaintenanceType\' \r\n	AND t.type = i.`code` \r\n	#REPLACESQL\r\n	group by t.type,i.name \r\n', 'tpl', 4, 11, 2, 1);
INSERT INTO `e_bi_chart` VALUES (16, NULL, NULL, '系统', '2023-05-24 09:28:11', 60, NULL, 'DUynPDnp', 8, 100, '检查企业数', 20, 'SELECT\r\n	t1.searchCount,\r\nIF\r\n	( t1.searchCount > t2.searchBeforeCount, CONCAT( \''+\'', t1.searchCount - t2.searchBeforeCount ), t1.searchCount - t2.searchBeforeCount ) compare \r\nFROM\r\n	( SELECT count( DISTINCT enterprise_name ) searchCount FROM tb_enterprise_information \r\n	 WHERE  \r\n	 state = 1 \r\n	 AND procedure_id in (select id from tb_procedure where YEAR (inspection_date )= \''#searchDate\'' ) \r\n	 #REPLACESQL\r\n	) t1,\r\n	(\r\n	SELECT\r\n		count( DISTINCT enterprise_name ) searchBeforeCount \r\n	FROM\r\n		tb_enterprise_information \r\n	WHERE \r\n	\r\n		state = 1 \r\n		AND procedure_id in (select id from tb_procedure where YEAR ( inspection_date )=YEAR (\r\n		DATE_SUB( concat( \''#searchDate\'', \''-01-01\'' ), INTERVAL 1 YEAR )) )\r\n		#REPLACESQL	\r\n	) t2\r\n', 'tpl', 3, 4, 4, 2);
INSERT INTO `e_bi_chart` VALUES (17, NULL, NULL, '系统', '2023-05-24 09:29:41', 60, NULL, 'ZHDqpWlw', 8, 100, '问题总数', 30, 'SELECT\r\n	t.totalCount,\r\n	ifnull( round( t.totalCount * 100 / t1.beforeTotalCount, 2 ), 0 ) ratio \r\nFROM\r\n	( SELECT count(*) totalCount FROM tb_inspection_results WHERE YEAR ( deadline ) = \'#searchDate\' \r\n	#REPLACESQL\r\n	) t,\r\n	( SELECT count(*) beforeTotalCount FROM tb_inspection_results WHERE YEAR ( deadline ) = YEAR ( DATE_SUB( concat( \'#searchDate\', \'-01-01\' ), INTERVAL 1 YEAR )) \r\n	#REPLACESQL\r\n	) t1 ', 'tpl', 3, 5, 6, 2);
INSERT INTO `e_bi_chart` VALUES (18, NULL, NULL, '系统', '2023-05-24 09:29:21', 60, NULL, 'mjradWkK', 12, 100, '整改问题数量', 40, 'SELECT\r\n	*,\r\n	ifnull(round( finishCount * 100 / totalCount, 2 ),0) ratio \r\nFROM\r\n	(\r\n	SELECT\r\n		count( IF ( inspection_result = \'BY\', TRUE, NULL )) finishCount,\r\n		count(*) totalCount \r\n	FROM\r\n		tb_inspection_results \r\n	WHERE\r\n	YEAR ( deadline ) = \'#searchDate\' \r\n	#REPLACESQL\r\n	) t ', 'tpl', 3, 6, 5, 2);
INSERT INTO `e_bi_chart` VALUES (19, NULL, NULL, '系统', '2023-05-24 09:30:03', 60, NULL, 'geJfCjVn', 12, 100, '逾期问题数', 50, 'SELECT\r\n	t.beOverdueCount,\r\n	ifnull( round( beOverdueCount * 100 / totalCount, 2 ), 0 ) ratio \r\nFROM\r\n	(\r\n	SELECT\r\n		count(\r\n		IF\r\n		( DATE_FORMAT(deadline,\''%Y-%m-%d\'')  < DATE_FORMAT(now(),\''%Y-%m-%d\''), TRUE, NULL )) beOverdueCount,\r\n		count(*) totalCount \r\n	FROM\r\n		tb_inspection_results \r\n	WHERE\r\n		YEAR ( deadline ) = \''#searchDate\'' \r\n		and inspection_name in (select id from tb_procedure)\r\n		#REPLACESQL\r\n	) t', 'tpl', 3, 7, 7, 2);
INSERT INTO `e_bi_chart` VALUES (20, NULL, NULL, '系统', '2023-05-24 09:31:45', 60, NULL, 'SLdxoqeu', 24, 340, '执法人员年度提交的检查情况', 100, 'SELECT\r\n	i.create_by,\r\n	count(*) 数量\r\nFROM\r\n	tb_inspector i,\r\n	tb_procedure p \r\nWHERE\r\n	i.procedure_id = p.id \r\nAND YEAR ( p.inspection_date ) = \'#searchDate\'\r\n #REPLACESQL\r\nGROUP BY\r\n	i.create_by', 'Column', 3, NULL, 10, 2);
INSERT INTO `e_bi_chart` VALUES (21, NULL, NULL, '系统', '2023-05-24 09:35:04', 60, NULL, 'grgSkLkX', 24, 50, '截止时间', 1, 'SELECT DATE_FORMAT(NOW(),\'%Y-%m-%d %H:%i:%S\') now', 'tpl', 5, 2, NULL, NULL);
INSERT INTO `e_bi_chart` VALUES (22, NULL, NULL, '系统', '2023-05-24 09:35:19', 60, NULL, 'EVuIENUa', 8, 100, '检查次数', 10, 'SELECT\r\n	t.searchCount,\r\nIF\r\n	( searchCount > searchBeforeCount, CONCAT( \'+\', searchCount - searchBeforeCount ), searchCount - searchBeforeCount ) compare \r\nFROM\r\n	(\r\n	SELECT\r\n		count(\r\n		IF\r\n		( YEAR ( inspection_date ) = \'#searchDate\', TRUE, NULL )) searchCount,\r\n		count(\r\n		IF\r\n		( YEAR ( inspection_date ) = YEAR ( DATE_SUB( concat(\'#searchDate\',\'-01-01\'), INTERVAL 1 YEAR )), TRUE, NULL )) searchBeforeCount \r\n	FROM\r\n	tb_procedure \r\n	where 1 =1 \r\n	#REPLACESQL\r\n	) t\r\n	\r\n	\r\n	', 'tpl', 5, 1, 3, 2);
INSERT INTO `e_bi_chart` VALUES (23, NULL, NULL, '系统', '2023-05-24 09:35:34', 60, NULL, 'GkPHonZt', 8, 100, '问题总数', 20, 'SELECT\r\n	t.totalCount,\r\n	ifnull( round( t.totalCount * 100 / t1.beforeTotalCount, 2 ), 0 ) ratio \r\nFROM\r\n	( SELECT count(*) totalCount FROM tb_inspection_results WHERE YEAR ( deadline ) = \'#searchDate\' \r\n	#REPLACESQL\r\n	) t,\r\n	( SELECT count(*) beforeTotalCount FROM tb_inspection_results WHERE YEAR ( deadline ) = YEAR ( DATE_SUB( concat( \'#searchDate\', \'-01-01\' ), INTERVAL 1 YEAR )) \r\n	#REPLACESQL\r\n	) t1 ', 'tpl', 5, 5, 6, 2);
INSERT INTO `e_bi_chart` VALUES (24, NULL, NULL, '系统', '2023-05-24 09:35:51', 60, NULL, 'hyEjwYVl', 8, 100, '整改问题数', 30, 'SELECT\r\n	*,\r\n	ifnull(round( finishCount * 100 / totalCount, 2 ),0) ratio \r\nFROM\r\n	(\r\n	SELECT\r\n		count( IF ( inspection_result = \'BY\', TRUE, NULL )) finishCount,\r\n		count(*) totalCount \r\n	FROM\r\n		tb_inspection_results \r\n	WHERE\r\n	YEAR ( deadline ) = \'#searchDate\' \r\n	#REPLACESQL\r\n	) t ', 'tpl', 5, 9, 5, 2);
INSERT INTO `e_bi_chart` VALUES (25, NULL, NULL, '系统', '2023-05-24 09:36:11', 60, NULL, 'rtXmJCpS', 12, 100, '未整改问题数', 40, 'SELECT\r\n	*,\r\n	ifnull(round( unfinishCount * 100 / totalCount, 2 ),0) ratio \r\nFROM\r\n	(\r\n	SELECT\r\n		count( IF ( rectification_status = \'TO_BE_RECTIFIED\', TRUE, NULL )) unfinishCount,\r\n		count(*) totalCount \r\n	FROM\r\n		tb_inspection_results \r\n	WHERE\r\n	YEAR ( deadline ) = \'#searchDate\' \r\n	#REPLACESQL\r\n	) t ', 'tpl', 5, 8, 5, 2);
INSERT INTO `e_bi_chart` VALUES (26, NULL, NULL, '系统', '2023-05-24 09:36:54', 60, NULL, 'SnhWYYAO', 12, 100, '逾期问题数', 50, 'SELECT\r\n	t.beOverdueCount,\r\n	ifnull( round( beOverdueCount * 100 / totalCount, 2 ), 0 ) ratio \r\nFROM\r\n	(\r\n	SELECT\r\n		count(\r\n		IF\r\n		( DATE_FORMAT(deadline,\''%Y-%m-%d\'')  < DATE_FORMAT(now(),\''%Y-%m-%d\''), TRUE, NULL )) beOverdueCount,\r\n		count(*) totalCount \r\n	FROM\r\n		tb_inspection_results \r\n	WHERE\r\n		YEAR ( deadline ) = \''#searchDate\'' \r\n		and inspection_name in (select id from tb_procedure)\r\n		#REPLACESQL\r\n	) t ', 'tpl', 5, 7, 7, 2);
INSERT INTO `e_bi_chart` VALUES (27, NULL, NULL, '系统', '2023-05-24 09:37:15', 60, NULL, 'YgXfQuJJ', 24, 340, '问题类型统计', 60, 'SELECT\r\n	inspection_items 检查事项,\r\n	COUNT(*) 数量 \r\nFROM\r\n	tb_inspection_results \r\nWHERE\r\n	DATE_FORMAT( create_time, \'%Y\' )= \'#searchDate\'\r\n#REPLACESQL\r\nGROUP BY\r\n	检查事项 \r\nORDER BY\r\n	数量 DESC;', 'Column', 5, NULL, 8, 2);
INSERT INTO `e_bi_chart` VALUES (29, NULL, NULL, '系统', '2023-05-24 09:37:35', 60, NULL, 'rTlmKYsP', 24, 340, '检查清单', 80, 'SELECT\r\n	t2.name \''检查名称\'',\r\n	count( t1.inspection_name ) \''问题数量\'',\r\n	count(\r\n	IF\r\n	( t1.inspection_result = \''BY\'' and publish_status =1, TRUE, NULL )) \''已整改数量\'',\r\n	count(\r\n	IF\r\n	( t1.inspection_result != \''BY\'' and publish_status =1, TRUE, NULL )) \''未整改数量\'',\r\n	count(\r\n	IF\r\n	( t1.deadline < now() and publish_status =1, TRUE, NULL )) \''逾期数量\'' \r\nFROM\r\n	tb_inspection_results t1\r\n	RIGHT OUTER JOIN (\r\n	SELECT\r\n		id,\r\n	NAME \r\n	FROM\r\n		tb_procedure \r\n) t2 ON t1.inspection_name = t2.id\r\n	where YEAR ( t1.create_time )=\''#searchDate\''\r\n	and t1.check_object = \''#ORGCODE\''\r\nGROUP BY t2.`NAME`', 'table', 5, NULL, 11, 2);
INSERT INTO `e_bi_chart` VALUES (30, NULL, NULL, '系统', '2023-05-24 14:45:45', 60, NULL, 'iGqrPEVf', 8, 340, '评估企业数量', 10, 'SELECT\r\n	grade.NAME \'等级\',\r\n	count( manage.id ) \'企业数量\'\r\nFROM\r\n	tb_evaluation_grade grade\r\n	LEFT JOIN tb_evaluation_manage manage ON grade.id = manage.eval_grade_id \r\n	where 1 = 1 and manage.public_state=1\r\n	#REPLACESQL\r\nGROUP BY\r\n	grade.NAME;', 'Column', 6, NULL, 12, 3);
INSERT INTO `e_bi_chart` VALUES (31, NULL, NULL, '系统', '2023-05-24 14:41:55', 60, NULL, 'loDPUOyB', 8, 340, '企业4个维度平均值', 20, 'SELECT\r\n	t.NAME \'维度\',\r\n	ROUND(avg( s.score ),2) \'平均值\' \r\nFROM\r\n	(\r\n	SELECT\r\n		i.CODE,\r\n		i.NAME \r\n	FROM\r\n		e_dict d,\r\n		e_dict_item i \r\n	WHERE\r\n		d.CODE = \'PrincipalElement\' \r\n		AND d.id = i.erupt_dict_id \r\n	) t\r\n	LEFT JOIN ( SELECT * FROM tb_evaluation_score ) s ON t.CODE = s.NAME \r\nGROUP BY\r\n	t.NAME ', 'Radar', 6, NULL, 13, 3);
INSERT INTO `e_bi_chart` VALUES (32, NULL, NULL, '系统', '2023-05-24 15:21:29', 60, NULL, 'KvPRLezT', 8, 340, '企业12个要素平均值', 30, 'SELECT\r\n	child_name \'要素\',\r\n	round(avg( score ),2) \'平均值\' \r\nFROM\r\n	tb_evaluation_score \r\nGROUP BY\r\n	child_name;', 'Rose', 6, NULL, 14, 3);
INSERT INTO `e_bi_chart` VALUES (33, NULL, NULL, '系统', '2023-05-24 15:29:44', 60, NULL, 'QAzCgfHH', 8, 340, '不合格企业名单', 40, 'SELECT\r\n	DISTINCT enterprise.`name`  \'企业名称\'\r\nFROM\r\n	tb_evaluation_grade grade\r\n	LEFT JOIN tb_evaluation_manage manage ON grade.id = manage.eval_grade_id \r\n	INNER JOIN tb_enterprise enterprise ON  manage.company = enterprise.org_code\r\nWHERE\r\n	grade.NAME = \'不合格\' \r\n	AND manage.public_state=1\r\n	#REPLACESQL', 'table', 6, NULL, 15, 3);
INSERT INTO `e_bi_chart` VALUES (34, NULL, NULL, '系统', '2023-05-24 15:53:49', 60, NULL, 'guPlyrBH', 8, 340, 'top10企业名单', 50, 'SELECT\r\n	DISTINCT enterprise.`name`  \'企业名称\',\r\n	manage.score \'分数\'\r\nFROM\r\n  tb_evaluation_manage manage ,\r\n	tb_enterprise enterprise \r\nwhere manage.company = enterprise.org_code and\r\nmanage.public_state=1\r\n#REPLACESQL\r\norder by manage.score desc\r\nlimit 0,10', 'table', 6, NULL, 16, 3);
INSERT INTO `e_bi_chart` VALUES (35, NULL, NULL, '系统', '2023-05-24 16:22:19', 60, NULL, 'dkqYEJKj', 8, 340, '未评估企业名单', 60, 'SELECT DISTINCT enterprise.name \'企业名称\' FROM\r\n(SELECT * FROM tb_enterprise enterprise WHERE 1=1 \r\n #REPLACESQL\r\n) enterprise LEFT JOIN tb_evaluation_manage \r\nmanage ON enterprise.org_code=manage.company\r\nWHERE manage.id IS NULL;', 'table', 6, NULL, 17, 3);
INSERT INTO `e_bi_chart` VALUES (36, NULL, NULL, '系统', '2023-05-24 14:44:43', 60, '{\r\n    \"color\":\"#f00\"\r\n}', 'sdGPjQJR', 24, 340, '企业评估明细', 70, 'SELECT\r\n	enterprise.`name` \'企业名称\',\r\n	manage.score \'分数\',\r\n	sum( CASE WHEN score.child_name = \'值班值守与信息报告\' THEN ROUND(score.score*score.weight,2) END ) \'值班值守与信息报告\',\r\n	sum( CASE WHEN score.child_name = \'应急组织与职责\' THEN ROUND(score.score*score.weight,2) END ) \'应急组织与职责\',\r\n	sum( CASE WHEN score.child_name = \'应急管理制度\' THEN ROUND(score.score*score.weight,2) END ) \'应急管理制度\',\r\n	sum( CASE WHEN score.child_name = \'风险管理\' THEN ROUND(score.score*score.weight,2) END ) \'风险管理\',\r\n	sum( CASE WHEN score.child_name = \'应急预案\' THEN ROUND(score.score*score.weight,2) END ) \'应急预案\',\r\n	sum( CASE WHEN score.child_name = \'监控与预警\' THEN ROUND(score.score*score.weight,2) END ) \'监控与预警\',\r\n	sum( CASE WHEN score.child_name = \'教育培训与演练\' THEN ROUND(score.score*score.weight,2) END ) \'教育培训与演练\',\r\n	sum( CASE WHEN score.child_name = \'应急处置与救援\' THEN ROUND(score.score*score.weight,2) END ) \'应急处置与救援\',\r\n	sum( CASE WHEN score.child_name = \'应急恢复\' THEN ROUND(score.score*score.weight,2) END ) \'应急恢复\',\r\n	sum( CASE WHEN score.child_name = \'应急物资装备\' THEN ROUND(score.score*score.weight,2) END ) \'应急物资装备\',\r\n	sum( CASE WHEN score.child_name = \'应急救援队伍\' THEN ROUND(score.score*score.weight,2) END ) \'应急救援队伍\',\r\n	sum( CASE WHEN score.child_name = \'应急经费\' THEN ROUND(score.score*score.weight,2) END ) \'	应急经费\'\r\nFROM\r\n	( SELECT max( id ) maxid, company FROM tb_evaluation_manage manage WHERE manage.public_state=1 GROUP BY company ) maxMan,\r\n	tb_evaluation_manage manage,\r\n	tb_enterprise enterprise,\r\n	tb_evaluation_score score\r\nWHERE\r\n	maxMan.maxid = manage.id \r\n	AND manage.company = enterprise.org_code \r\n	AND manage.id = score.eval_id \r\n	#REPLACESQL\r\nGROUP BY\r\n	enterprise.`name`,\r\n	manage.score \r\nORDER BY\r\n	manage.score DESC; \r\n', 'table', 6, NULL, 18, 3);

-- ----------------------------
-- Table structure for e_bi_class_handler
-- ----------------------------
DROP TABLE IF EXISTS `e_bi_class_handler`;
CREATE TABLE `e_bi_class_handler`  (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `create_by` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `create_time` datetime NULL DEFAULT NULL,
  `update_by` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '更新人',
  `update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  `handler_path` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '处理类',
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '名称',
  `param` varchar(2000) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '处理类参数',
  `remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 21 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '报表处理类' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of e_bi_class_handler
-- ----------------------------
INSERT INTO `e_bi_class_handler` VALUES (1, NULL, NULL, NULL, NULL, 'com.daliangang.statistics.handler.CompanyHandler', '港区重大风险-公司处理类', NULL, '获取当前登录公司');
INSERT INTO `e_bi_class_handler` VALUES (2, NULL, NULL, '系统', '2023-05-08 14:27:34', 'com.daliangang.statistics.handler.GovernmentHandler', '港区重大风险-政府处理类', NULL, '市级：系统全部企业，企业数据为0时也要显示\n区级：自己管辖范围内的企业数据');
INSERT INTO `e_bi_class_handler` VALUES (3, NULL, NULL, '系统', '2023-05-08 16:52:31', 'com.daliangang.statistics.handler.RndpubInspectCountHandler', '双随机-检查次数处理类', NULL, '检查次数（次）：从【检查流程】获取年度内数据，一条数据算一次，同时统计上一年度的数据，做同比=当前年度数量-上一年度数量，得数可以为正数或负数');
INSERT INTO `e_bi_class_handler` VALUES (4, NULL, NULL, '系统', '2023-05-08 17:30:42', 'com.daliangang.statistics.handler.RndpubInspectCompanyHandler', '双随机-检查企业数', NULL, '检查企业数（家）：将统计的检查次数中检查对象（企业）加和去重后=企业数量，做同比=当前年度数量-上一年度数量，得数可以为正数或负数');
INSERT INTO `e_bi_class_handler` VALUES (5, NULL, NULL, '系统', '2023-05-09 10:05:23', 'com.daliangang.statistics.handler.RndpubInspectFinishCountHandler', '双随机-整改问题个数处理类', NULL, '整改问题数量（个）：从【检查结果】获取年度内数据，判断【整改结果】中【通过】的数据，显示：已经整改通过的数量/问题总数。完成率=已经整改通过的数量/问题总数*100%');
INSERT INTO `e_bi_class_handler` VALUES (6, NULL, NULL, '系统', '2023-05-09 10:27:27', 'com.daliangang.statistics.handler.RndpubInspectTotalCountHandler', '双随机-问题总数处理类', NULL, '问题总数（个）：从【检查结果】获取年度内数据，一条数据算一个，同时计算上一年度，');
INSERT INTO `e_bi_class_handler` VALUES (7, NULL, NULL, '系统', '2023-05-09 10:41:47', 'com.daliangang.statistics.handler.RndpubInspectBeoverCountHandler', '双随机-逾期问题数处理类', NULL, '逾期问题数（个）：从【检查结果】获取年度内数据，判断【逾期状态】中【已逾期】的数据，显示：已逾期数量/问题总数。逾期占比=已逾期数量/问题总数*100%');
INSERT INTO `e_bi_class_handler` VALUES (8, NULL, NULL, '系统', '2023-05-09 18:04:31', 'com.daliangang.statistics.handler.RndpubInspectQuestionTypeHandler', '双随机-问题类型统计处理类', NULL, '问题类型统计：柱形top图，问题数量从上到下，从多到少；根据【检查事项】维度统计，分别统计年度内各【检查事项】下的问题数量');
INSERT INTO `e_bi_class_handler` VALUES (9, NULL, NULL, '系统', '2023-05-09 19:10:36', 'com.daliangang.statistics.handler.RndpubInspectProblemRectificationHandler', '双随机-问题整改情况', NULL, '问题整改情况：环形图，中间显示年度内全部问题，分别计算已整改状态和未整改状态的问题占比');
INSERT INTO `e_bi_class_handler` VALUES (10, NULL, NULL, '系统', '2023-05-09 19:41:14', 'com.daliangang.statistics.handler.RndpubInspectInspectorHandler', '双随机-检查人员检查情况', NULL, '执法人员检查情况：柱形图+折线图，统计年度内，参与检查流程的全部检查人员（去重），分别统计每个检查人员年度内参与的检查流程的次数，分别统计每个检查人员年度内提交的检查结果数量');
INSERT INTO `e_bi_class_handler` VALUES (11, NULL, NULL, '系统', '2023-05-10 13:56:46', 'com.daliangang.statistics.handler.RndpubInspectChecklistHandler', '双随机-检查清单处理类', NULL, '检查清单：显示本年度内本企业参加的检查流程，数据从【检查流程】获取');
INSERT INTO `e_bi_class_handler` VALUES (12, NULL, NULL, '系统', '2023-05-10 16:23:57', 'com.daliangang.statistics.handler.EmergencyCompanyCountHandler', '危险货物-评估企业霜', NULL, '评估企业数量：根据评估等级，统计各等级下企业数量');
INSERT INTO `e_bi_class_handler` VALUES (13, NULL, NULL, '系统', '2023-05-10 17:28:53', 'com.daliangang.statistics.handler.EmergencyCompanyMainAvgHandler', ' 危险货物-企业4个维度平均值', NULL, '企业4个维度评估情况：某个主要素评估结果=该主要素企业平均分/该主要素总分；平均分=企业该主要素得分加和/企业数量；总分=100*该主要素下子要素数量');
INSERT INTO `e_bi_class_handler` VALUES (14, NULL, NULL, '系统', '2023-05-10 17:32:39', 'com.daliangang.statistics.handler.EmergencyCompanyChildAvgHandler', '危险货物-企业12个子要素平均分', NULL, '企业12个子要素平均分：某个子要素平均分=所有企业该子要素得分加和/企业数量');
INSERT INTO `e_bi_class_handler` VALUES (15, NULL, NULL, '系统', '2023-05-10 17:42:38', 'com.daliangang.statistics.handler.EmergencyUnqualifiedCompanyHandler', '危险货物-不合格企业名单', NULL, '不合格企业名单：根据评估等级判断');
INSERT INTO `e_bi_class_handler` VALUES (16, NULL, NULL, '系统', '2023-05-10 17:43:11', 'com.daliangang.statistics.handler.EmergencyTopCompanyHandler', '危险货物-Top10企业名单', NULL, 'Top10企业名单：根据评估总得分判断，展示分数前十的企业名称及分数，按分数从高到低');
INSERT INTO `e_bi_class_handler` VALUES (17, NULL, NULL, '系统', '2023-05-10 19:15:21', 'com.daliangang.statistics.handler.EmergencyNotEvaluationCompanyHandler', '危险货物-未评估企业名单', NULL, '未评估企业名单：根据有评估数据的企业对比全系统全部企业，判断哪些企业本年度未评估');
INSERT INTO `e_bi_class_handler` VALUES (18, NULL, NULL, '系统', '2023-05-10 19:17:01', 'com.daliangang.statistics.handler.EmergencyEvaluationCompanyDetailHandler', '危险货物-企业评估明细', NULL, '企业评估明细：获取本年度参与评估的企业，展示企业各子要素得分和总得分，按总得分从高到低展示');
INSERT INTO `e_bi_class_handler` VALUES (19, NULL, NULL, '系统', '2023-06-01 15:59:13', 'com.daliangang.statistics.handler.ExamineeHandler', '从业人员-考生统计', NULL, '考生统计：\n考生姓名：展示考生的名称；\n企业名称：展示该考生对应的企业名称；\n总学时：展示培训通过的总学时（未通过的不记录）；\n应参加培训次数：法定+自组织应参加的总次数；\n实际参加培训次数：法定+自组织实际参加的总次数；\n缺勤次数：展示应参加次数-实际参加次数；\n考试合格率：展示合格的考试/全部考试；\n\n这两个统计参照【企业管理 / 企业统计列表】页面做，对应的市级政府、区县政府、企业都需要做数据过滤');
INSERT INTO `e_bi_class_handler` VALUES (20, NULL, NULL, '系统', '2023-06-01 16:24:14', 'com.daliangang.statistics.handler.EnterpriseStatisticsHandler', '从业人员-企业统计', NULL, '-企业统计：\n企业名称：展示企业名称；\n岗位评估次数：企业自组织次数+政府组织次数，可在【岗位胜任力/评估统计】菜单下查询；\n法定培训次数：市级政府添加的培训次数，可在【教务管理/培训考核】菜单下查询；\n自组织培训次数：企业添加的培训次数，可在【教务管理/培训考核】菜单下查询；\n总培训人次：合计参加培训的人数；');

-- ----------------------------
-- Table structure for e_bi_column
-- ----------------------------
DROP TABLE IF EXISTS `e_bi_column`;
CREATE TABLE `e_bi_column`  (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `display` bit(1) NULL DEFAULT NULL COMMENT '是否显示',
  `drill_express` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '下钻SQL',
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '列名',
  `remark` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '描述',
  `sortable` bit(1) NULL DEFAULT NULL COMMENT '是否排序',
  `type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '类型',
  `width` int NULL DEFAULT NULL COMMENT '宽度',
  `bi_id` bigint NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `FK7ofdtmq1g18px49itii3ifita`(`bi_id` ASC) USING BTREE,
  CONSTRAINT `FK7ofdtmq1g18px49itii3ifita` FOREIGN KEY (`bi_id`) REFERENCES `e_bi` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 7 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '列配置' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of e_bi_column
-- ----------------------------
INSERT INTO `e_bi_column` VALUES (5, b'0', NULL, 'userId', NULL, b'0', 'string', NULL, 7);
INSERT INTO `e_bi_column` VALUES (6, b'0', NULL, 'orgId', NULL, b'0', 'string', NULL, 8);

-- ----------------------------
-- Table structure for e_bi_datasource
-- ----------------------------
DROP TABLE IF EXISTS `e_bi_datasource`;
CREATE TABLE `e_bi_datasource`  (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `create_by` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `create_time` datetime NULL DEFAULT NULL,
  `update_by` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '更新人',
  `update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  `code` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '编码',
  `driver` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '驱动',
  `limit_sql` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '分页语句',
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '名称',
  `password` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '密码',
  `pool_config` varchar(2000) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '连接池配置',
  `remark` varchar(2000) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '备注',
  `type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '数据库类型',
  `url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '连接字符串',
  `user_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '用户名',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `UKnl4a4ys6lomm3sxjv5nqc2aar`(`code` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 5 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '数据源管理' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of e_bi_datasource
-- ----------------------------
INSERT INTO `e_bi_datasource` VALUES (1, NULL, NULL, '系统', '2023-05-08 09:44:33', 'XEmdf62d', 'com.p6spy.engine.spy.P6SpyDriver', NULL, '港区重大风险监测与管控系统', '1234@abcD', NULL, '港区重大风险监测与管控系统 数据源管理', 'MySQL', '*********************************************************************************************************************************************************************************************************************************************', 'root');
INSERT INTO `e_bi_datasource` VALUES (2, NULL, NULL, '系统', '2023-05-08 09:52:39', 'mGYdrGeo', 'com.p6spy.engine.spy.P6SpyDriver', NULL, '港区危险货物企业“双随机、一公开”系统', '1234@abcD', NULL, '港区危险货物企业“双随机、一公开”系统 数据库配置', 'MySQL', '*********************************************************************************************************************************************************************************************************************************************', 'root');
INSERT INTO `e_bi_datasource` VALUES (3, NULL, NULL, '系统', '2023-05-10 16:26:13', 'je2yA8vC', 'com.p6spy.engine.spy.P6SpyDriver', NULL, '危险货物重大突发事件智慧管理系统', '1234@abcD', NULL, NULL, 'MySQL', '*********************************************************************************************************************************************************************************************************************************************', 'root');
INSERT INTO `e_bi_datasource` VALUES (4, NULL, NULL, '系统', '2023-06-01 10:59:56', '1iG3bN3X', 'com.p6spy.engine.spy.P6SpyDriver', NULL, '	从业人员一体化精准管理系统', '1234@abcD', NULL, NULL, 'MySQL', '*****************************************************************************************************************************************************************************************************************************', 'root');

-- ----------------------------
-- Table structure for e_bi_dimension
-- ----------------------------
DROP TABLE IF EXISTS `e_bi_dimension`;
CREATE TABLE `e_bi_dimension`  (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `code` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '维度编码',
  `default_value` varchar(2000) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '默认值',
  `not_null` bit(1) NULL DEFAULT NULL COMMENT '是否必填',
  `sort` int NULL DEFAULT NULL COMMENT '显示顺序',
  `title` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '名称',
  `type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '维度类型',
  `bi_id` bigint NULL DEFAULT NULL,
  `bi_dimension_reference_id` bigint NULL DEFAULT NULL COMMENT '参照维度',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `UK178xcwr6hc4s2q6radi3drlun`(`code` ASC, `bi_id` ASC) USING BTREE,
  INDEX `FKtak80kcehioa5lt77llqh8c2a`(`bi_id` ASC) USING BTREE,
  INDEX `FKwpl79gqh3v35vr87ihk842lx`(`bi_dimension_reference_id` ASC) USING BTREE,
  CONSTRAINT `FKtak80kcehioa5lt77llqh8c2a` FOREIGN KEY (`bi_id`) REFERENCES `e_bi` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `FKwpl79gqh3v35vr87ihk842lx` FOREIGN KEY (`bi_dimension_reference_id`) REFERENCES `e_bi_dimension_reference` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 7 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '查询维度' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of e_bi_dimension
-- ----------------------------
INSERT INTO `e_bi_dimension` VALUES (1, 'date', 'new Date().getFullYear()+\'\'', b'0', 30, '年度', 'YEAR', 3, NULL);
INSERT INTO `e_bi_dimension` VALUES (2, 'yearSearch', 'new Date().getFullYear()+\'\'', b'0', NULL, '年度查询', 'YEAR', 5, NULL);
INSERT INTO `e_bi_dimension` VALUES (3, 'date', 'new Date().getFullYear() + \'\'', b'0', NULL, '年度', 'YEAR', 1, NULL);
INSERT INTO `e_bi_dimension` VALUES (4, 'name', NULL, b'0', 1, '考生姓名', 'INPUT', 7, NULL);
INSERT INTO `e_bi_dimension` VALUES (5, 'companyName', NULL, b'0', 2, '企业名称', 'INPUT', 7, NULL);
INSERT INTO `e_bi_dimension` VALUES (6, 'company', NULL, b'0', NULL, '企业名称', 'INPUT', 8, NULL);

-- ----------------------------
-- Table structure for e_bi_dimension_reference
-- ----------------------------
DROP TABLE IF EXISTS `e_bi_dimension_reference`;
CREATE TABLE `e_bi_dimension_reference`  (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `create_by` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `create_time` datetime NULL DEFAULT NULL,
  `update_by` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '更新人',
  `update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '名称',
  `ref_sql` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '参照SQL',
  `class_handler_id` bigint NULL DEFAULT NULL COMMENT '处理类',
  `data_source_id` bigint NULL DEFAULT NULL COMMENT '数据源',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `FKo1gd6tkp2vcujvfx74n0uj63g`(`class_handler_id` ASC) USING BTREE,
  INDEX `FKghw1afiw0ms1p2arc49syayg3`(`data_source_id` ASC) USING BTREE,
  CONSTRAINT `FKghw1afiw0ms1p2arc49syayg3` FOREIGN KEY (`data_source_id`) REFERENCES `e_bi_datasource` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `FKo1gd6tkp2vcujvfx74n0uj63g` FOREIGN KEY (`class_handler_id`) REFERENCES `e_bi_class_handler` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 2 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '参照维度' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of e_bi_dimension_reference
-- ----------------------------

-- ----------------------------
-- Table structure for e_bi_function
-- ----------------------------
DROP TABLE IF EXISTS `e_bi_function`;
CREATE TABLE `e_bi_function`  (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `create_by` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `create_time` datetime NULL DEFAULT NULL,
  `update_by` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '更新人',
  `update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  `code` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '编码',
  `js_function` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '函数表达式',
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '名称',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `UKl4tkhdjrgmmkubp454rkxd2gs`(`code` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 2 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '函数管理' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of e_bi_function
-- ----------------------------
INSERT INTO `e_bi_function` VALUES (1, NULL, NULL, NULL, NULL, 'BI_FUN', '', 'BI_FUN');

-- ----------------------------
-- Table structure for e_bi_group
-- ----------------------------
DROP TABLE IF EXISTS `e_bi_group`;
CREATE TABLE `e_bi_group`  (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `create_by` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `create_time` datetime NULL DEFAULT NULL,
  `update_by` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '更新人',
  `update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  `code` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '编码',
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '组别名称',
  `remark` varchar(5000) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '备注',
  `parent_id` bigint NULL DEFAULT NULL COMMENT '上级组别',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `UKmgb9fx4s6cv7ixhtxh7yg4x3y`(`code` ASC) USING BTREE,
  INDEX `FK1p2nlcuafd5xb4bu4he0ft7p`(`parent_id` ASC) USING BTREE,
  CONSTRAINT `FK1p2nlcuafd5xb4bu4he0ft7p` FOREIGN KEY (`parent_id`) REFERENCES `e_bi_group` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 2 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '分组管理' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of e_bi_group
-- ----------------------------
INSERT INTO `e_bi_group` VALUES (1, NULL, NULL, NULL, NULL, 'default', '默认分组', NULL, NULL);

-- ----------------------------
-- Table structure for e_bi_history
-- ----------------------------
DROP TABLE IF EXISTS `e_bi_history`;
CREATE TABLE `e_bi_history`  (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `after_sql_statement` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '修改后表达式',
  `bi_id` bigint NULL DEFAULT NULL,
  `mark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '来源',
  `operate_by` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '操作人',
  `operate_time` datetime NULL DEFAULT NULL COMMENT '操作时间',
  `sql_statement` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '修改前表达式',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `IDXfhl3a0gyqqa21ejanx90hmuyg`(`bi_id` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 84 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '修改记录' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of e_bi_history
-- ----------------------------
INSERT INTO `e_bi_history` VALUES (1, 'select ${ __request__}', 1, '权重大于60', 'erupt', '2023-05-05 09:49:26', 'select code,name from e_upms_post where weight >60');
INSERT INTO `e_bi_history` VALUES (2, 'select code,name from e_upms_post where weight >60', 1, '权重大于60', 'erupt', '2023-05-05 10:44:13', 'select ${ __request__}');
INSERT INTO `e_bi_history` VALUES (3, 'select count(*) \'count\' from e_upms_post', 3, '数据统计', 'erupt', '2023-05-05 11:37:52', 'select count(*) from e_upms_post');
INSERT INTO `e_bi_history` VALUES (4, 'select name,count(*) \'count\' from e_upms_post group by name', 3, '数据统计', 'erupt', '2023-05-05 13:41:58', 'select count(*) \'count\' from e_upms_post');
INSERT INTO `e_bi_history` VALUES (5, '\r\nselect \'经营资质与文件\' name ,10 count  union select \'教育培训\' name,25 count union select \'双重预防机制\' name,5 count', 3, '数据统计', 'erupt', '2023-05-05 13:44:43', 'select name,count(*) \'count\' from e_upms_post group by name');
INSERT INTO `e_bi_history` VALUES (6, '\r\nselect  \'已整改问题\' name ,10 count union  select \'未整改问题\' name  ,5 count', 3, '问题整改', 'erupt', '2023-05-05 13:49:11', 'select 10 \'已整改问题\',5 \'未整改问题\'');
INSERT INTO `e_bi_history` VALUES (7, '\r\nselect \'2022年3月4号检查\' as\'检查名称\'  , 10 \'问题数量\', 1 \'已整改数量\',8 \'未整改数量\',3 \'逾期数量\' union \r\nselect \'2023年5月4号检查\'  as \'检查名称\'  , 20 \'问题数量\', 6 \'已整改数量\',14 \'未整改数量\',9 \'逾期数量\'', 3, '检查清单', 'erupt', '2023-05-05 13:57:54', '\r\nselect \'2022年3月4号检查\' \'检查名称\'  , 10 \'问题数量\', 1 \'已整改数量\',8 \'未整改数量\',3 \'逾期数量\' union \r\nselect \'2023年5月4号检查\' \'检查名称\'  , 20 \'问题数量\', 6 \'已整改数量\',14 \'未整改数量\',9 \'逾期数量\'');
INSERT INTO `e_bi_history` VALUES (8, '\r\nselect \'2022年3月4号检查\' as\'检查名称\'  , 10 as \'问题数量\', 1  as\'已整改数量\',8  as\'未整改数量\',3  as\'逾期数量\' union \r\nselect \'2023年5月4号检查\'  as \'检查名称\'  , 20  as\'问题数量\', 6  as\'已整改数量\',14  as\'未整改数量\',9  as\'逾期数量\'', 3, '检查清单', 'erupt', '2023-05-05 13:58:38', '\r\nselect \'2022年3月4号检查\' as\'检查名称\'  , 10 \'问题数量\', 1 \'已整改数量\',8 \'未整改数量\',3 \'逾期数量\' union \r\nselect \'2023年5月4号检查\'  as \'检查名称\'  , 20 \'问题数量\', 6 \'已整改数量\',14 \'未整改数量\',9 \'逾期数量\'');
INSERT INTO `e_bi_history` VALUES (9, 'select \'2022年3月4号检查\' as\'检查名称\'  , 10 as \'问题数量\', 1  as\'已整改数量\',8  as\'未整改数量\',3  as\'逾期数量\' union \r\nselect \'2023年5月4号检查\'  as \'检查名称\'  , 20  as\'问题数量\', 6  as\'已整改数量\',14  as\'未整改数量\',9  as\'逾期数量\'', 3, '检查清单', 'erupt', '2023-05-05 13:59:06', '\r\nselect \'2022年3月4号检查\' as\'检查名称\'  , 10 as \'问题数量\', 1  as\'已整改数量\',8  as\'未整改数量\',3  as\'逾期数量\' union \r\nselect \'2023年5月4号检查\'  as \'检查名称\'  , 20  as\'问题数量\', 6  as\'已整改数量\',14  as\'未整改数量\',9  as\'逾期数量\'');
INSERT INTO `e_bi_history` VALUES (10, 'select 16 as count', 3, '数据统计', 'erupt', '2023-05-05 14:17:09', 'select 1');
INSERT INTO `e_bi_history` VALUES (11, 'select code,name from e_upms_post where 1=1', 1, '全部权重', 'erupt', '2023-05-08 10:10:12', 'select code,name from e_upms_post');
INSERT INTO `e_bi_history` VALUES (12, 'select count(*) from e_upms_post where 1=1', 1, ' 作业数量统计', 'erupt', '2023-05-08 10:14:19', 'select count(*) from e_upms_post');
INSERT INTO `e_bi_history` VALUES (13, 'select count(*) count from e_upms_post where 1=1', 1, ' 作业数量统计', 'erupt', '2023-05-08 10:18:41', 'select count(*) from e_upms_post where 1=1');
INSERT INTO `e_bi_history` VALUES (14, 'SELECT\r\n	count(IF( now() < ab_time, TRUE, NULL )) unstart,\r\n	count(IF( now() BETWEEN ab_time AND ae_time, TRUE, NULL )) progress ,\r\n	count(IF( now() > ae_time, TRUE, NULL )) finish,\r\n	count(*) total \r\nFROM\r\n	tb_unload_ship \r\nWHERE org_code = \'#{ORGCODE}\' \r\n	UNION ALL\r\nSELECT \r\n  count( IF ( now() < ab_time, TRUE, NULL )) unstart,\r\n	count( IF ( now() BETWEEN ab_time AND ae_time, TRUE, NULL )) progress ,\r\n	count( IF ( now() > ae_time, TRUE, NULL )) finish ,\r\n	count(*) total \r\nFROM\r\n	tb_unload \r\nWHERE org_code = \'#{ORGCODE}\' \r\n	UNION ALL\r\nSELECT\r\n	count( IF ( now() < star_date, TRUE, NULL )) unstart,\r\n	count( IF ( now() BETWEEN star_date AND end_date, TRUE, NULL )) progress ,\r\n	count( IF ( now() > end_date, TRUE, NULL )) finish ,\r\n	count(*) total \r\nFROM\r\n	tb_maintenance \r\nWHERE org_code = \'#{ORGCODE}\' ', 1, ' 作业数量统计', 'erupt', '2023-05-08 10:54:38', 'select count(*) count from e_upms_post where 1=1');
INSERT INTO `e_bi_history` VALUES (15, 'SELECT\r\n	count(IF( now() < ab_time, TRUE, NULL )) unstart,\r\n	count(IF( now() BETWEEN ab_time AND ae_time, TRUE, NULL )) progress ,\r\n	count(IF( now() > ae_time, TRUE, NULL )) finish,\r\n	count(*) total \r\nFROM\r\n	tb_unload_ship \r\nWHERE org_code = \'#ORGCODE\' \r\n	UNION ALL\r\nSELECT \r\n  count( IF ( now() < ab_time, TRUE, NULL )) unstart,\r\n	count( IF ( now() BETWEEN ab_time AND ae_time, TRUE, NULL )) progress ,\r\n	count( IF ( now() > ae_time, TRUE, NULL )) finish ,\r\n	count(*) total \r\nFROM\r\n	tb_unload \r\nWHERE org_code = \'#ORGCODE\' \r\n	UNION ALL\r\nSELECT\r\n	count( IF ( now() < star_date, TRUE, NULL )) unstart,\r\n	count( IF ( now() BETWEEN star_date AND end_date, TRUE, NULL )) progress ,\r\n	count( IF ( now() > end_date, TRUE, NULL )) finish ,\r\n	count(*) total \r\nFROM\r\n	tb_maintenance \r\nWHERE org_code = \'#ORGCODE\' \r\n	\r\n', 1, ' 作业数量统计', 'erupt', '2023-05-08 10:56:08', 'SELECT\r\n	count(IF( now() < ab_time, TRUE, NULL )) unstart,\r\n	count(IF( now() BETWEEN ab_time AND ae_time, TRUE, NULL )) progress ,\r\n	count(IF( now() > ae_time, TRUE, NULL )) finish,\r\n	count(*) total \r\nFROM\r\n	tb_unload_ship \r\nWHERE org_code = \'#{ORGCODE}\' \r\n	UNION ALL\r\nSELECT \r\n  count( IF ( now() < ab_time, TRUE, NULL )) unstart,\r\n	count( IF ( now() BETWEEN ab_time AND ae_time, TRUE, NULL )) progress ,\r\n	count( IF ( now() > ae_time, TRUE, NULL )) finish ,\r\n	count(*) total \r\nFROM\r\n	tb_unload \r\nWHERE org_code = \'#{ORGCODE}\' \r\n	UNION ALL\r\nSELECT\r\n	count( IF ( now() < star_date, TRUE, NULL )) unstart,\r\n	count( IF ( now() BETWEEN star_date AND end_date, TRUE, NULL )) progress ,\r\n	count( IF ( now() > end_date, TRUE, NULL )) finish ,\r\n	count(*) total \r\nFROM\r\n	tb_maintenance \r\nWHERE org_code = \'#{ORGCODE}\' ');
INSERT INTO `e_bi_history` VALUES (16, 'select SUM(unstart) unstart,SUM(progress) progress,SUM(finish) finish,SUM(total) total from\r\n(SELECT\r\n	count(IF( now() < ab_time, TRUE, NULL )) unstart,\r\n	count(IF( now() BETWEEN ab_time AND ae_time, TRUE, NULL )) progress ,\r\n	count(IF( now() > ae_time, TRUE, NULL )) finish,\r\n	count(*) total \r\nFROM\r\n	tb_unload_ship \r\nWHERE org_code = \'#ORGCODE\' \r\n	UNION ALL\r\nSELECT \r\n  count( IF ( now() < ab_time, TRUE, NULL )) unstart,\r\n	count( IF ( now() BETWEEN ab_time AND ae_time, TRUE, NULL )) progress ,\r\n	count( IF ( now() > ae_time, TRUE, NULL )) finish ,\r\n	count(*) total \r\nFROM\r\n	tb_unload \r\nWHERE org_code = \'#ORGCODE\' \r\n	UNION ALL\r\nSELECT\r\n	count( IF ( now() < star_date, TRUE, NULL )) unstart,\r\n	count( IF ( now() BETWEEN star_date AND end_date, TRUE, NULL )) progress ,\r\n	count( IF ( now() > end_date, TRUE, NULL )) finish ,\r\n	count(*) total \r\nFROM\r\n	tb_maintenance \r\nWHERE org_code = \'#ORGCODE\' )t\r\n', 1, ' 作业数量统计', 'erupt', '2023-05-08 11:17:08', 'SELECT\r\n	count(IF( now() < ab_time, TRUE, NULL )) unstart,\r\n	count(IF( now() BETWEEN ab_time AND ae_time, TRUE, NULL )) progress ,\r\n	count(IF( now() > ae_time, TRUE, NULL )) finish,\r\n	count(*) total \r\nFROM\r\n	tb_unload_ship \r\nWHERE org_code = \'#ORGCODE\' \r\n	UNION ALL\r\nSELECT \r\n  count( IF ( now() < ab_time, TRUE, NULL )) unstart,\r\n	count( IF ( now() BETWEEN ab_time AND ae_time, TRUE, NULL )) progress ,\r\n	count( IF ( now() > ae_time, TRUE, NULL )) finish ,\r\n	count(*) total \r\nFROM\r\n	tb_unload \r\nWHERE org_code = \'#ORGCODE\' \r\n	UNION ALL\r\nSELECT\r\n	count( IF ( now() < star_date, TRUE, NULL )) unstart,\r\n	count( IF ( now() BETWEEN star_date AND end_date, TRUE, NULL )) progress ,\r\n	count( IF ( now() > end_date, TRUE, NULL )) finish ,\r\n	count(*) total \r\nFROM\r\n	tb_maintenance \r\nWHERE org_code = \'#ORGCODE\' \r\n	\r\n');
INSERT INTO `e_bi_history` VALUES (17, 'SELECT\r\n	risk_database \'风险类型\',\r\n	sum( process ) \'现有风险数量\',\r\n	sum( finished ) \'历史风险数量\' \r\nFROM\r\n	(\r\n	SELECT\r\n		risk_database,\r\n		count(IF( now() BETWEEN ab_time AND ae_time, TRUE, NULL )) process,\r\n		count(IF( now() > ae_time, TRUE, NULL )) finished \r\n	FROM\r\n		tb_unload_ship \r\n	WHERE org_code = \'#ORGCODE\' \r\n	GROUP BY\r\n		risk_database UNION ALL\r\n	SELECT\r\n		risk_database,\r\n		count(IF( now() BETWEEN ab_time AND ae_time, TRUE, NULL )) process,\r\n		count(IF( now() > ae_time, TRUE, NULL )) \'finished\' \r\n	FROM\r\n		tb_unload \r\n	WHERE org_code = \'#ORGCODE\' \r\n	GROUP BY\r\n		risk_database \r\n	) t \r\nGROUP BY\r\n	risk_database', 1, '风险类型统计', 'erupt', '2023-05-08 11:46:06', 'select 16 as count');
INSERT INTO `e_bi_history` VALUES (18, 'SELECT\r\n	i.NAME \'作业类型\',\r\n	count( IF ( now() BETWEEN ab_time AND ae_time, TRUE, NULL )) \'现有风险数量\',\r\n	count( IF ( now() > ae_time, TRUE, NULL )) \'历史风险数量\' \r\nFROM\r\n	e_dict d,\r\n	e_dict_item i,\r\n	tb_unload_ship t \r\nWHERE\r\n	d.id = i.erupt_dict_id \r\n	AND d.CODE = \'workTypeShip\' \r\n	AND t.work_type = i.`code` \r\n	AND org_code = \'#ORGCODE\'\r\n	group by t.work_type,i.name union all\r\n	\r\n	\r\n	SELECT\r\n	i.NAME \'作业类型\',\r\n	count( IF ( now() BETWEEN ab_time AND ae_time, TRUE, NULL )) \'现有风险数量\',\r\n	count( IF ( now() > ae_time, TRUE, NULL )) \'历史风险数量\' \r\nFROM\r\n	e_dict d,\r\n	e_dict_item i,\r\n	tb_unload t \r\nWHERE\r\n	d.id = i.erupt_dict_id \r\n	AND d.CODE = \'workType\' \r\n	AND t.work_type = i.`code` \r\n	AND org_code = \'#ORGCODE\'\r\n	group by t.work_type,i.name union all\r\n	\r\n	SELECT\r\n	i.NAME \'作业类型\',\r\n	count( IF ( now() BETWEEN star_date AND end_date, TRUE, NULL )) \'现有风险数量\' ,\r\n	count( IF ( now() > end_date, TRUE, NULL )) \'历史风险数量\' \r\nFROM\r\n	e_dict d,\r\n	e_dict_item i,\r\n	tb_maintenance t \r\nWHERE\r\n	d.id = i.erupt_dict_id \r\n	AND d.CODE = \'MaintenanceType\' \r\n	AND t.type = i.`code` \r\n	AND org_code = \'#ORGCODE\'\r\n	group by t.type,i.name \r\n', 1, '作业风险数量统计', 'erupt', '2023-05-08 14:01:58', 'select \'作业类型\',\r\n\r\n\r\n\'现有风险数量\',\r\n\r\n\r\n\'历史风险数量\'');
INSERT INTO `e_bi_history` VALUES (19, 'SELECT\r\n	t.searchCount,\r\nIF\r\n	( searchCount > searchBeforeCount, CONCAT( \'+\', searchCount - searchBeforeCount ), searchCount - searchBeforeCount ) compare \r\nFROM\r\n	(\r\n	SELECT\r\n		count(\r\n		IF\r\n		( YEAR ( inspection_date ) = \'#searchDate\', TRUE, NULL )) searchCount,\r\n		count(\r\n		IF\r\n		( YEAR ( inspection_date ) = YEAR ( DATE_SUB( concat(\'#searchDate\',\'-01-01\'), INTERVAL 1 YEAR )), TRUE, NULL )) searchBeforeCount \r\n	FROM\r\n	tb_procedure \r\n	) t\r\n	\r\n	\r\n	', 3, '卡片-检查次数', 'erupt', '2023-05-08 16:43:52', 'select 16 as count');
INSERT INTO `e_bi_history` VALUES (20, 'SELECT\r\n	searchCount,\r\nIF( searchCount > searchBeforeCount,\r\n		CONCAT( \'+\', searchCount - searchBeforeCount ), searchCount - searchBeforeCount ) compare \r\nFROM\r\n	(\r\n	SELECT\r\n		count( DISTINCT enterprise_id ) searchCount \r\n	FROM\r\n		tb_procedure p,\r\n		tb_enterprise_information e \r\n	WHERE\r\n		p.id = e.procedure_id \r\n		AND YEAR ( inspection_date ) = \'#searchDate\' \r\n	) t,\r\n	(\r\n	SELECT\r\n		count( DISTINCT enterprise_id ) searchBeforeCount \r\n	FROM\r\n		tb_procedure p,\r\n		tb_enterprise_information e \r\n	WHERE\r\n		p.id = e.procedure_id \r\n	AND YEAR ( inspection_date ) = \'2022\' \r\n	) t1', 3, '卡片-检查企业数', 'erupt', '2023-05-08 17:27:00', 'select 16 as count');
INSERT INTO `e_bi_history` VALUES (21, 'SELECT\r\n	searchCount,\r\nIF( searchCount > searchBeforeCount,\r\n		CONCAT( \'+\', searchCount - searchBeforeCount ), searchCount - searchBeforeCount ) compare \r\nFROM\r\n	(\r\n	SELECT\r\n		count( DISTINCT enterprise_id ) searchCount \r\n	FROM\r\n		tb_procedure p,\r\n		tb_enterprise_information e \r\n	WHERE\r\n		p.id = e.procedure_id \r\n		AND YEAR ( inspection_date ) = \'#searchDate\' \r\n	) t,\r\n	(\r\n	SELECT\r\n		count( DISTINCT enterprise_id ) searchBeforeCount \r\n	FROM\r\n		tb_procedure p,\r\n		tb_enterprise_information e \r\n	WHERE\r\n		p.id = e.procedure_id \r\n	AND YEAR ( inspection_date ) = YEAR ( DATE_SUB( concat(\'#searchDate\' ,\'-01-01\'), INTERVAL 1 YEAR ))\r\n	) t1', 3, '卡片-检查企业数', 'erupt', '2023-05-08 17:28:21', 'SELECT\r\n	searchCount,\r\nIF( searchCount > searchBeforeCount,\r\n		CONCAT( \'+\', searchCount - searchBeforeCount ), searchCount - searchBeforeCount ) compare \r\nFROM\r\n	(\r\n	SELECT\r\n		count( DISTINCT enterprise_id ) searchCount \r\n	FROM\r\n		tb_procedure p,\r\n		tb_enterprise_information e \r\n	WHERE\r\n		p.id = e.procedure_id \r\n		AND YEAR ( inspection_date ) = \'#searchDate\' \r\n	) t,\r\n	(\r\n	SELECT\r\n		count( DISTINCT enterprise_id ) searchBeforeCount \r\n	FROM\r\n		tb_procedure p,\r\n		tb_enterprise_information e \r\n	WHERE\r\n		p.id = e.procedure_id \r\n	AND YEAR ( inspection_date ) = \'2022\' \r\n	) t1');
INSERT INTO `e_bi_history` VALUES (22, 'SELECT\r\n	*,\r\n	round( finishCount * 100 / totalCount, 2 ) ratio \r\nFROM\r\n	(\r\n	SELECT\r\n		count( IF ( inspection_result = \'BY\', TRUE, NULL )) finishCount,\r\n		count(*) totalCount \r\n	FROM\r\n		tb_inspection_results \r\n	WHERE\r\n	YEAR ( deadline ) = \'#searchDate\' \r\n	) t ', 3, '卡片-整改问题数量', 'erupt', '2023-05-09 10:02:13', 'select 16 as count');
INSERT INTO `e_bi_history` VALUES (23, 'SELECT\r\n	*,\r\n	ifnull(round( finishCount * 100 / totalCount, 2 ),0) ratio \r\nFROM\r\n	(\r\n	SELECT\r\n		count( IF ( inspection_result = \'BY\', TRUE, NULL )) finishCount,\r\n		count(*) totalCount \r\n	FROM\r\n		tb_inspection_results \r\n	WHERE\r\n	YEAR ( deadline ) = \'#searchDate\' \r\n	) t ', 3, '卡片-整改问题数量', 'erupt', '2023-05-09 10:15:00', 'SELECT\r\n	*,\r\n	round( finishCount * 100 / totalCount, 2 ) ratio \r\nFROM\r\n	(\r\n	SELECT\r\n		count( IF ( inspection_result = \'BY\', TRUE, NULL )) finishCount,\r\n		count(*) totalCount \r\n	FROM\r\n		tb_inspection_results \r\n	WHERE\r\n	YEAR ( deadline ) = \'#searchDate\' \r\n	) t ');
INSERT INTO `e_bi_history` VALUES (24, 'SELECT\r\n	t.totalCount,\r\n	ifnull( round( t.totalCount * 100 / t1.beforeTotalCount, 2 ), 0 ) ratio \r\nFROM\r\n	( SELECT count(*) totalCount FROM tb_inspection_results WHERE YEAR ( deadline ) = \'2023\' ) t,\r\n	( SELECT count(*) beforeTotalCount FROM tb_inspection_results WHERE YEAR ( deadline ) = YEAR ( DATE_SUB( concat( \'2023\', \'-01-01\' ), INTERVAL 1 YEAR )) ) t1 ', 3, '卡片-问题总数', 'erupt', '2023-05-09 10:25:54', 'select 16 as count');
INSERT INTO `e_bi_history` VALUES (25, 'SELECT\r\n	t.totalCount,\r\n	ifnull( round( t.totalCount * 100 / t1.beforeTotalCount, 2 ), 0 ) ratio \r\nFROM\r\n	( SELECT count(*) totalCount FROM tb_inspection_results WHERE YEAR ( deadline ) = \'#searchDate\' ) t,\r\n	( SELECT count(*) beforeTotalCount FROM tb_inspection_results WHERE YEAR ( deadline ) = YEAR ( DATE_SUB( concat( \'#searchDate\', \'-01-01\' ), INTERVAL 1 YEAR )) ) t1 ', 3, '卡片-问题总数', 'erupt', '2023-05-09 10:26:16', 'SELECT\r\n	t.totalCount,\r\n	ifnull( round( t.totalCount * 100 / t1.beforeTotalCount, 2 ), 0 ) ratio \r\nFROM\r\n	( SELECT count(*) totalCount FROM tb_inspection_results WHERE YEAR ( deadline ) = \'2023\' ) t,\r\n	( SELECT count(*) beforeTotalCount FROM tb_inspection_results WHERE YEAR ( deadline ) = YEAR ( DATE_SUB( concat( \'2023\', \'-01-01\' ), INTERVAL 1 YEAR )) ) t1 ');
INSERT INTO `e_bi_history` VALUES (26, 'SELECT\r\n	t.beOverdueCount,\r\n	ifnull( round( beOverdueCount * 100 / totalCount, 2 ), 0 ) ratio \r\nFROM\r\n	(\r\n	SELECT\r\n		count(\r\n		IF\r\n		( deadline > now(), TRUE, NULL )) beOverdueCount,\r\n		count(*) totalCount \r\n	FROM\r\n		tb_inspection_results \r\n	WHERE\r\n		YEAR ( deadline ) = \'#searchDate\' \r\n	) t ', 3, '卡片-逾期问题数', 'erupt', '2023-05-09 10:39:15', 'select 16 as count');
INSERT INTO `e_bi_history` VALUES (27, 'SELECT\r\n	i.inspection_items,\r\n	count(*) c \r\nFROM\r\n	tb_inspection_items i,\r\n	tb_procedure p \r\nWHERE\r\n	i.procedure_id = p.id \r\nAND YEAR ( inspection_date ) = \'#searchDate\' \r\nGROUP BY\r\n	i.inspection_items \r\nORDER BY\r\n	c DESC', 3, '问题类型统计', 'erupt', '2023-05-09 18:04:22', '\r\nselect \'经营资质与文件\' name ,10 count  union select \'教育培训\' name,25 count union select \'双重预防机制\' name,5 count');
INSERT INTO `e_bi_history` VALUES (28, 'SELECT \r\n  \'已整改问题\' name,\r\n( SELECT count( IF ( inspection_result = \'BY\', TRUE, NULL )) finishCount FROM tb_inspection_results WHERE YEAR ( deadline ) = \'#searchDate\' ) count \r\nUNION ALL\r\nSELECT\r\n	\'未整改问题\' name,\r\n	( SELECT count( IF ( inspection_result != \'BY\', TRUE, NULL )) notFinishCount FROM tb_inspection_results WHERE YEAR ( deadline ) = \'#searchDate\' ) count', 3, '问题整改情况', 'erupt', '2023-05-09 18:31:41', '\r\nselect  \'已整改问题\' name ,10 count union  select \'未整改问题\' name  ,5 count');
INSERT INTO `e_bi_history` VALUES (29, 'SELECT\r\n	i.NAME,\r\n	count(*) \r\nFROM\r\n	tb_inspector i,\r\n	tb_procedure p \r\nWHERE\r\n	i.procedure_id = p.id \r\nAND YEAR ( deadline ) = \'#searchDate\'\r\nGROUP BY\r\n	i.NAME', 3, '执法人员检查情况', 'erupt', '2023-05-09 19:36:53', '\r\nselect \'曾某\' name ,10 count  union select \'赵xx\' name,25 count union select \'柳x\' name,5 count');
INSERT INTO `e_bi_history` VALUES (30, 'SELECT\r\n	i.NAME,\r\n	count(*) \r\nFROM\r\n	tb_inspector i,\r\n	tb_procedure p \r\nWHERE\r\n	i.procedure_id = p.id \r\nAND YEAR ( p.inspection_date ) = \'#searchDate\'\r\nGROUP BY\r\n	i.NAME', 3, '执法人员检查情况', 'erupt', '2023-05-09 19:43:06', 'SELECT\r\n	i.NAME,\r\n	count(*) \r\nFROM\r\n	tb_inspector i,\r\n	tb_procedure p \r\nWHERE\r\n	i.procedure_id = p.id \r\nAND YEAR ( deadline ) = \'#searchDate\'\r\nGROUP BY\r\n	i.NAME');
INSERT INTO `e_bi_history` VALUES (31, '\r\n	SELECT\r\n	  p.name \'检查名称\',\r\n	  count(*) \'问题数量\' ,\r\n		count( IF ( inspection_result = \'BY\', TRUE, NULL )) \'已整改数量\',\r\n		count( IF ( inspection_result != \'BY\', TRUE, NULL )) \'未整改数量\',\r\n		count( IF ( r.deadline > now(), TRUE, NULL )) \'逾期数量\'\r\n		\r\n	FROM\r\n		tb_inspection_results r ,tb_procedure p\r\n		\r\n	WHERE\r\n	YEAR ( r.deadline ) = \'#searchDate\' \r\n	and p.id = r.inspection_name\r\n	\r\n	group by p.id,p.name\r\n	', 3, '检查清单', 'erupt', '2023-05-10 13:52:09', 'select \'2022年3月4号检查\' as\'检查名称\'  , 10 as \'问题数量\', 1  as\'已整改数量\',8  as\'未整改数量\',3  as\'逾期数量\' union \r\nselect \'2023年5月4号检查\'  as \'检查名称\'  , 20  as\'问题数量\', 6  as\'已整改数量\',14  as\'未整改数量\',9  as\'逾期数量\'');
INSERT INTO `e_bi_history` VALUES (32, '\r\n	SELECT\r\n	  p.name \'检查名称\',\r\n	  count(*) \'问题数量\' ,\r\n		count( IF ( inspection_result = \'BY\', TRUE, NULL )) \'已整改数量\',\r\n		count( IF ( inspection_result != \'BY\', TRUE, NULL )) \'未整改数量\',\r\n		count( IF ( r.deadline > now(), TRUE, NULL )) \'逾期数量\'\r\n		\r\n	FROM\r\n		tb_inspection_results r ,tb_procedure p\r\n		\r\n	WHERE\r\n	YEAR ( r.deadline ) = \'#searchDate\' \r\n	and p.id = r.inspection_name\r\n	and r.org_code = \'#ORGCODE\' \r\n	\r\n	group by p.id,p.name\r\n	', 3, '检查清单', 'erupt', '2023-05-10 14:09:15', '\r\n	SELECT\r\n	  p.name \'检查名称\',\r\n	  count(*) \'问题数量\' ,\r\n		count( IF ( inspection_result = \'BY\', TRUE, NULL )) \'已整改数量\',\r\n		count( IF ( inspection_result != \'BY\', TRUE, NULL )) \'未整改数量\',\r\n		count( IF ( r.deadline > now(), TRUE, NULL )) \'逾期数量\'\r\n		\r\n	FROM\r\n		tb_inspection_results r ,tb_procedure p\r\n		\r\n	WHERE\r\n	YEAR ( r.deadline ) = \'#searchDate\' \r\n	and p.id = r.inspection_name\r\n	\r\n	group by p.id,p.name\r\n	');
INSERT INTO `e_bi_history` VALUES (33, 'SELECT\r\n	*,\r\n	ifnull(round( unFinishCount * 100 / totalCount, 2 ),0) ratio \r\nFROM\r\n	(\r\n	SELECT\r\n		count( IF ( inspection_result != \'BY\', TRUE, NULL )) unFinishCount,\r\n		count(*) totalCount \r\n	FROM\r\n		tb_inspection_results \r\n	WHERE\r\n	YEAR ( deadline ) = \'#searchDate\' \r\n	) t ', 5, '卡片-未整改问题数', 'erupt', '2023-05-10 15:15:01', 'SELECT\r\n	*,\r\n	ifnull(round( finishCount * 100 / totalCount, 2 ),0) ratio \r\nFROM\r\n	(\r\n	SELECT\r\n		count( IF ( inspection_result != \'BY\', TRUE, NULL )) unFinishCount,\r\n		count(*) totalCount \r\n	FROM\r\n		tb_inspection_results \r\n	WHERE\r\n	YEAR ( deadline ) = \'#searchDate\' \r\n	) t ');
INSERT INTO `e_bi_history` VALUES (34, 'SELECT\r\n	*,\r\n	ifnull(round( unfinishCount * 100 / totalCount, 2 ),0) ratio \r\nFROM\r\n	(\r\n	SELECT\r\n		count( IF ( inspection_result != \'BY\', TRUE, NULL )) unfinishCount,\r\n		count(*) totalCount \r\n	FROM\r\n		tb_inspection_results \r\n	WHERE\r\n	YEAR ( deadline ) = \'#searchDate\' \r\n	) t ', 5, '卡片-未整改问题数', 'erupt', '2023-05-10 15:15:52', 'SELECT\r\n	*,\r\n	ifnull(round( unFinishCount * 100 / totalCount, 2 ),0) ratio \r\nFROM\r\n	(\r\n	SELECT\r\n		count( IF ( inspection_result != \'BY\', TRUE, NULL )) unFinishCount,\r\n		count(*) totalCount \r\n	FROM\r\n		tb_inspection_results \r\n	WHERE\r\n	YEAR ( deadline ) = \'#searchDate\' \r\n	) t ');
INSERT INTO `e_bi_history` VALUES (35, 'SELECT\r\n	name \'风险类型\',\r\n	sum( process ) \'现有风险数量\',\r\n	sum( finished ) \'历史风险数量\' \r\nFROM\r\n(SELECT\r\n		t.name,\r\n		count(IF( now() BETWEEN s.ab_time AND s.ae_time, TRUE, NULL )) process,\r\n		count(IF( now() > s.ae_time, TRUE, NULL )) finished \r\n	FROM\r\n		tb_unload s INNER JOIN	\r\n(SELECT i.name name,i.`code` code FROM e_dict d,e_dict_item i WHERE d.id=i.erupt_dict_id AND d.`code`=\'riskType\')t\r\nON s.risk_database=t.`code`\r\nWHERE org_code = \'#ORGCODE\' \r\nGROUP BY t.`name` \r\nUNION ALL\r\nSELECT\r\n		t.name,\r\n		count(IF( now() BETWEEN s.ab_time AND s.ae_time, TRUE, NULL )) process,\r\n		count(IF( now() > s.ae_time, TRUE, NULL )) finished \r\n	FROM\r\n		tb_unload_ship s INNER JOIN	\r\n(SELECT i.name name,i.`code` code FROM e_dict d,e_dict_item i WHERE d.id=i.erupt_dict_id AND d.`code`=\'riskType\')t\r\nON s.risk_database=t.`code`\r\nWHERE org_code = \'#ORGCODE\' \r\nGROUP BY t.`name`) t\r\nGROUP BY name', 1, '风险类型统计', 'erupt', '2023-05-24 09:26:20', 'SELECT\r\n	risk_database \'风险类型\',\r\n	sum( process ) \'现有风险数量\',\r\n	sum( finished ) \'历史风险数量\' \r\nFROM\r\n	(\r\n	SELECT\r\n		risk_database,\r\n		count(IF( now() BETWEEN ab_time AND ae_time, TRUE, NULL )) process,\r\n		count(IF( now() > ae_time, TRUE, NULL )) finished \r\n	FROM\r\n		tb_unload_ship \r\n	WHERE org_code = \'#ORGCODE\' \r\n	GROUP BY\r\n		risk_database UNION ALL\r\n	SELECT\r\n		risk_database,\r\n		count(IF( now() BETWEEN ab_time AND ae_time, TRUE, NULL )) process,\r\n		count(IF( now() > ae_time, TRUE, NULL )) \'finished\' \r\n	FROM\r\n		tb_unload \r\n	WHERE org_code = \'#ORGCODE\' \r\n	GROUP BY\r\n		risk_database \r\n	) t \r\nGROUP BY\r\n	risk_database');
INSERT INTO `e_bi_history` VALUES (36, 'SELECT DATE_FORMAT(NOW(),\'%Y-%m-%d %H:%i:%S\') now', 3, '截止时间', 'erupt', '2023-05-24 09:27:32', 'select 1');
INSERT INTO `e_bi_history` VALUES (37, 'SELECT\r\n	t.searchCount,\r\nIF\r\n	( searchCount > searchBeforeCount, CONCAT( \'+\', searchCount - searchBeforeCount ), searchCount - searchBeforeCount ) compare \r\nFROM\r\n	(\r\n	SELECT\r\n		count(\r\n		IF\r\n		( YEAR ( inspection_date ) = \'#searchDate\', TRUE, NULL )) searchCount,\r\n		count(\r\n		IF\r\n		( YEAR ( inspection_date ) = YEAR ( DATE_SUB( concat(\'#searchDate\',\'-01-01\'), INTERVAL 1 YEAR )), TRUE, NULL )) searchBeforeCount \r\n	FROM\r\n	tb_procedure \r\n	where 1 =1 \r\n	#REPLACESQL\r\n	) t\r\n	\r\n	\r\n	', 3, '检查次数', 'erupt', '2023-05-24 09:27:51', 'SELECT\r\n	t.searchCount,\r\nIF\r\n	( searchCount > searchBeforeCount, CONCAT( \'+\', searchCount - searchBeforeCount ), searchCount - searchBeforeCount ) compare \r\nFROM\r\n	(\r\n	SELECT\r\n		count(\r\n		IF\r\n		( YEAR ( inspection_date ) = \'#searchDate\', TRUE, NULL )) searchCount,\r\n		count(\r\n		IF\r\n		( YEAR ( inspection_date ) = YEAR ( DATE_SUB( concat(\'#searchDate\',\'-01-01\'), INTERVAL 1 YEAR )), TRUE, NULL )) searchBeforeCount \r\n	FROM\r\n	tb_procedure \r\n	) t\r\n	\r\n	\r\n	');
INSERT INTO `e_bi_history` VALUES (38, 'SELECT\r\n	searchCount,\r\nIF\r\n	( searchCount > searchBeforeCount, CONCAT( \'+\', searchCount - searchBeforeCount ), searchCount - searchBeforeCount ) compare \r\nFROM\r\n	( SELECT count( DISTINCT check_object ) AS searchCount FROM tb_inspection_results WHERE YEAR ( create_time )= \'#searchDate\'  #REPLACESQL) AS t1,\r\n(SELECT\r\n	count( DISTINCT check_object ) AS searchBeforeCount \r\nFROM\r\n	tb_inspection_results \r\nWHERE\r\n	YEAR ( create_time ) = YEAR (\r\n	DATE_SUB( concat( \'#searchDate\' , \'-01-01\' ), INTERVAL 1 YEAR )) #REPLACESQL) t2', 3, '检查企业数', 'erupt', '2023-05-24 09:28:11', 'SELECT\r\n	searchCount,\r\nIF( searchCount > searchBeforeCount,\r\n		CONCAT( \'+\', searchCount - searchBeforeCount ), searchCount - searchBeforeCount ) compare \r\nFROM\r\n	(\r\n	SELECT\r\n		count( DISTINCT enterprise_id ) searchCount \r\n	FROM\r\n		tb_procedure p,\r\n		tb_enterprise_information e \r\n	WHERE\r\n		p.id = e.procedure_id \r\n		AND YEAR ( inspection_date ) = \'#searchDate\' \r\n	) t,\r\n	(\r\n	SELECT\r\n		count( DISTINCT enterprise_id ) searchBeforeCount \r\n	FROM\r\n		tb_procedure p,\r\n		tb_enterprise_information e \r\n	WHERE\r\n		p.id = e.procedure_id \r\n	AND YEAR ( inspection_date ) = YEAR ( DATE_SUB( concat(\'#searchDate\' ,\'-01-01\'), INTERVAL 1 YEAR ))\r\n	) t1');
INSERT INTO `e_bi_history` VALUES (39, 'SELECT\r\n	t.totalCount,\r\n	ifnull( round( t.totalCount * 100 / t1.beforeTotalCount, 2 ), 0 ) ratio \r\nFROM\r\n	( SELECT count(*) totalCount FROM tb_inspection_results WHERE YEAR ( deadline ) = \'#searchDate\' \r\n	#REPLACESQL\r\n	) t,\r\n	( SELECT count(*) beforeTotalCount FROM tb_inspection_results WHERE YEAR ( deadline ) = YEAR ( DATE_SUB( concat( \'#searchDate\', \'-01-01\' ), INTERVAL 1 YEAR )) \r\n	#REPLACESQL\r\n	) t1 ', 3, '问题总数', 'erupt', '2023-05-24 09:28:31', 'SELECT\r\n	t.totalCount,\r\n	ifnull( round( t.totalCount * 100 / t1.beforeTotalCount, 2 ), 0 ) ratio \r\nFROM\r\n	( SELECT count(*) totalCount FROM tb_inspection_results WHERE YEAR ( deadline ) = \'#searchDate\' ) t,\r\n	( SELECT count(*) beforeTotalCount FROM tb_inspection_results WHERE YEAR ( deadline ) = YEAR ( DATE_SUB( concat( \'#searchDate\', \'-01-01\' ), INTERVAL 1 YEAR )) ) t1 ');
INSERT INTO `e_bi_history` VALUES (40, 'SELECT\r\n	*,\r\n	ifnull(round( finishCount * 100 / totalCount, 2 ),0) ratio \r\nFROM\r\n	(\r\n	SELECT\r\n		count( IF ( inspection_result = \'BY\', TRUE, NULL )) finishCount,\r\n		count(*) totalCount \r\n	FROM\r\n		tb_inspection_results \r\n	WHERE\r\n	YEAR ( deadline ) = \'#searchDate\' \r\n	#REPLACESQL\r\n	) t ', 3, '整改问题数量', 'erupt', '2023-05-24 09:29:21', 'SELECT\r\n	*,\r\n	ifnull(round( finishCount * 100 / totalCount, 2 ),0) ratio \r\nFROM\r\n	(\r\n	SELECT\r\n		count( IF ( inspection_result = \'BY\', TRUE, NULL )) finishCount,\r\n		count(*) totalCount \r\n	FROM\r\n		tb_inspection_results \r\n	WHERE\r\n	YEAR ( deadline ) = \'#searchDate\' \r\n	) t ');
INSERT INTO `e_bi_history` VALUES (41, 'SELECT\r\n	t.beOverdueCount,\r\n	ifnull( round( beOverdueCount * 100 / totalCount, 2 ), 0 ) ratio \r\nFROM\r\n	(\r\n	SELECT\r\n		count(\r\n		IF\r\n		( deadline < now(), TRUE, NULL )) beOverdueCount,\r\n		count(*) totalCount \r\n	FROM\r\n		tb_inspection_results \r\n	WHERE\r\n		YEAR ( deadline ) = \'#searchDate\' \r\n		#REPLACESQL\r\n	) t ', 3, '逾期问题数', 'erupt', '2023-05-24 09:30:03', 'SELECT\r\n	t.beOverdueCount,\r\n	ifnull( round( beOverdueCount * 100 / totalCount, 2 ), 0 ) ratio \r\nFROM\r\n	(\r\n	SELECT\r\n		count(\r\n		IF\r\n		( deadline > now(), TRUE, NULL )) beOverdueCount,\r\n		count(*) totalCount \r\n	FROM\r\n		tb_inspection_results \r\n	WHERE\r\n		YEAR ( deadline ) = \'#searchDate\' \r\n	) t ');
INSERT INTO `e_bi_history` VALUES (42, 'SELECT\r\n	inspection_items 检查事项,\r\n	COUNT(*) 数量 \r\nFROM\r\n	tb_inspection_results \r\nWHERE\r\n	DATE_FORMAT( create_time, \'%Y\' )= \'#searchDate\'\r\n#REPLACESQL\r\nGROUP BY\r\n	检查事项 \r\nORDER BY\r\n	数量 DESC;', 3, '问题类型统计', 'erupt', '2023-05-24 09:30:26', 'SELECT\r\n	i.inspection_items,\r\n	count(*) c \r\nFROM\r\n	tb_inspection_items i,\r\n	tb_procedure p \r\nWHERE\r\n	i.procedure_id = p.id \r\nAND YEAR ( inspection_date ) = \'#searchDate\' \r\nGROUP BY\r\n	i.inspection_items \r\nORDER BY\r\n	c DESC');
INSERT INTO `e_bi_history` VALUES (43, 'SELECT\r\n	i.NAME,\r\n	count(*) 数量\r\nFROM\r\n	tb_inspector i,\r\n	tb_procedure p \r\nWHERE\r\n	i.procedure_id = p.id \r\nAND YEAR ( p.inspection_date ) = \'#searchDate\'\r\n #REPLACESQL\r\nGROUP BY\r\n	i.NAME', 3, '执法人员检查情况', 'erupt', '2023-05-24 09:31:04', 'SELECT\r\n	i.NAME,\r\n	count(*) \r\nFROM\r\n	tb_inspector i,\r\n	tb_procedure p \r\nWHERE\r\n	i.procedure_id = p.id \r\nAND YEAR ( p.inspection_date ) = \'#searchDate\'\r\nGROUP BY\r\n	i.NAME');
INSERT INTO `e_bi_history` VALUES (44, 'SELECT\r\n	i.create_by,\r\n	count(*) 数量\r\nFROM\r\n	tb_inspector i,\r\n	tb_procedure p \r\nWHERE\r\n	i.procedure_id = p.id \r\nAND YEAR ( p.inspection_date ) = \'#searchDate\'\r\n #REPLACESQL\r\nGROUP BY\r\n	i.create_by', 3, '执法人员年度提交的检查情况', 'erupt', '2023-05-24 09:31:45', 'SELECT\r\n	i.create_by,\r\n	count(*) \r\nFROM\r\n	tb_inspector i,\r\n	tb_procedure p \r\nWHERE\r\n	i.procedure_id = p.id \r\nAND YEAR ( p.inspection_date ) = \'#searchDate\'\r\nGROUP BY\r\n	i.create_by');
INSERT INTO `e_bi_history` VALUES (45, 'SELECT\r\n	name \'风险类型\',\r\n	sum( process ) \'现有风险数量\',\r\n	sum( finished ) \'历史风险数量\' \r\nFROM\r\n(SELECT\r\n		t.name,\r\n		count(IF( now() BETWEEN s.ab_time AND s.ae_time, TRUE, NULL )) process,\r\n		count(IF( now() > s.ae_time, TRUE, NULL )) finished \r\n	FROM\r\n		tb_unload s INNER JOIN	\r\n(SELECT i.name name,i.`code` code FROM e_dict d,e_dict_item i WHERE d.id=i.erupt_dict_id AND d.`code`=\'riskType\')t\r\nON s.risk_database=t.`code`\r\nWHERE 1=1\r\n#REPLACESQL\r\nGROUP BY t.`name` \r\nUNION ALL\r\nSELECT\r\n		t.name,\r\n		count(IF( now() BETWEEN s.ab_time AND s.ae_time, TRUE, NULL )) process,\r\n		count(IF( now() > s.ae_time, TRUE, NULL )) finished \r\n	FROM\r\n		tb_unload_ship s INNER JOIN	\r\n(SELECT i.name name,i.`code` code FROM e_dict d,e_dict_item i WHERE d.id=i.erupt_dict_id AND d.`code`=\'riskType\')t\r\nON s.risk_database=t.`code`\r\nWHERE 1=1\r\n#REPLACESQL\r\nGROUP BY t.`name`) t\r\nGROUP BY name\r\n', 4, '风险类型统计', 'erupt', '2023-05-24 09:32:21', 'SELECT\r\n	risk_database \'风险类型\',\r\n	sum( process ) \'现有风险数量\',\r\n	sum( finished ) \'历史风险数量\' \r\nFROM\r\n	(\r\n	SELECT\r\n		risk_database,\r\n		count(IF( now() BETWEEN ab_time AND ae_time, TRUE, NULL )) process,\r\n		count(IF( now() > ae_time, TRUE, NULL )) finished \r\n	FROM\r\n		tb_unload_ship \r\n	-- WHERE org_code = \'#ORGCODE\' \r\n	GROUP BY\r\n		risk_database UNION ALL\r\n	SELECT\r\n		risk_database,\r\n		count(IF( now() BETWEEN ab_time AND ae_time, TRUE, NULL )) process,\r\n		count(IF( now() > ae_time, TRUE, NULL )) \'finished\' \r\n	FROM\r\n		tb_unload \r\n	-- WHERE org_code = \'#ORGCODE\' \r\n	GROUP BY\r\n		risk_database \r\n	) t \r\nGROUP BY\r\n	risk_database');
INSERT INTO `e_bi_history` VALUES (46, 'SELECT\r\n	i.NAME \'作业类型\',\r\n	count( IF ( now() BETWEEN ab_time AND ae_time, TRUE, NULL )) \'现有风险数量\',\r\n	count( IF ( now() > ae_time, TRUE, NULL )) \'历史风险数量\' \r\nFROM\r\n	e_dict d,\r\n	e_dict_item i,\r\n	tb_unload_ship t \r\nWHERE\r\n	d.id = i.erupt_dict_id \r\n	AND d.CODE = \'workTypeShip\' \r\n	AND t.work_type = i.`code` \r\n	#REPLACESQL\r\n	group by t.work_type,i.name union all\r\n	\r\n	\r\n	SELECT\r\n	i.NAME \'作业类型\',\r\n	count( IF ( now() BETWEEN ab_time AND ae_time, TRUE, NULL )) \'现有风险数量\',\r\n	count( IF ( now() > ae_time, TRUE, NULL )) \'历史风险数量\' \r\nFROM\r\n	e_dict d,\r\n	e_dict_item i,\r\n	tb_unload t \r\nWHERE\r\n	d.id = i.erupt_dict_id \r\n	AND d.CODE = \'workType\' \r\n	AND t.work_type = i.`code` \r\n	#REPLACESQL\r\n	group by t.work_type,i.name union all\r\n	\r\n	SELECT\r\n	i.NAME \'作业类型\',\r\n	count( IF ( now() BETWEEN star_date AND end_date, TRUE, NULL )) \'现有风险数量\' ,\r\n	count( IF ( now() > end_date, TRUE, NULL )) \'历史风险数量\' \r\nFROM\r\n	e_dict d,\r\n	e_dict_item i,\r\n	tb_maintenance t \r\nWHERE\r\n	d.id = i.erupt_dict_id \r\n	AND d.CODE = \'MaintenanceType\' \r\n	AND t.type = i.`code` \r\n	#REPLACESQL\r\n	group by t.type,i.name \r\n', 4, '企业作业数量统计', 'erupt', '2023-05-24 09:33:00', 'SELECT\r\n	e.NAME \'企业名称\',\r\n	ifnull( t.unstart, 0 ) \'未开始作业\',\r\n	ifnull( t.progress, 0 ) \'正在进行中作业\',\r\n	ifnull( t.finish, 0 ) \'已结束作业\',\r\n	ifnull( t.total, 0 ) \'作业总数\' \r\nFROM\r\n	tb_enterprise e\r\n	LEFT JOIN (\r\n	SELECT\r\n		org_code,\r\n		SUM( unstart ) unstart,\r\n		SUM( progress ) progress,\r\n		SUM( finish ) finish,\r\n		SUM( total ) total \r\n	FROM\r\n		(\r\n		SELECT\r\n			org_code,\r\n			count( IF ( now() < ab_time, TRUE, NULL )) unstart,\r\n			count( IF ( now() BETWEEN ab_time AND ae_time, TRUE, NULL )) progress,\r\n			count( IF ( now() > ae_time, TRUE, NULL )) finish,\r\n			count(*) total \r\n		FROM\r\n			tb_unload_ship \r\n			-- WHERE org_code = \'#ORGCODE\'\r\n			\r\n		GROUP BY\r\n			org_code UNION ALL\r\n		SELECT\r\n			org_code,\r\n			count( IF ( now() < ab_time, TRUE, NULL )) unstart,\r\n			count( IF ( now() BETWEEN ab_time AND ae_time, TRUE, NULL )) progress,\r\n			count( IF ( now() > ae_time, TRUE, NULL )) finish,\r\n			count(*) total \r\n		FROM\r\n			tb_unload \r\n			-- WHERE org_code = \'#ORGCODE\'\r\n			\r\n		GROUP BY\r\n			org_code UNION ALL\r\n		SELECT\r\n			org_code,\r\n			count( IF ( now() < star_date, TRUE, NULL )) unstart,\r\n			count( IF ( now() BETWEEN star_date AND end_date, TRUE, NULL )) progress,\r\n			count( IF ( now() > end_date, TRUE, NULL )) finish,\r\n			count(*) total \r\n		FROM\r\n			tb_maintenance \r\n			-- WHERE org_code = \'#ORGCODE\'\r\n			\r\n		GROUP BY\r\n			org_code \r\n		) t \r\n	GROUP BY\r\n	org_code \r\n	) t ON e.org_code = t.org_code');
INSERT INTO `e_bi_history` VALUES (47, 'SELECT\r\n	e.NAME \'企业名称\',\r\n	ifnull( t.unstart, 0 ) \'未开始作业\',\r\n	ifnull( t.progress, 0 ) \'正在进行中作业\',\r\n	ifnull( t.finish, 0 ) \'已结束作业\',\r\n	ifnull( t.total, 0 ) \'作业总数\' \r\nFROM\r\n	tb_enterprise e\r\n	LEFT JOIN (\r\n	SELECT\r\n		org_code,\r\n		SUM( unstart ) unstart,\r\n		SUM( progress ) progress,\r\n		SUM( finish ) finish,\r\n		SUM( total ) total \r\n	FROM\r\n		(\r\n		SELECT\r\n			org_code,\r\n			count( IF ( now() < ab_time, TRUE, NULL )) unstart,\r\n			count( IF ( now() BETWEEN ab_time AND ae_time, TRUE, NULL )) progress,\r\n			count( IF ( now() > ae_time, TRUE, NULL )) finish,\r\n			count(*) total \r\n		FROM\r\n			tb_unload_ship \r\n			where 1 =1 \r\n			#REPLACESQL\r\n			\r\n		GROUP BY\r\n			org_code UNION \r\n\r\n		SELECT\r\n			org_code,\r\n			count( IF ( now() < ab_time, TRUE, NULL )) unstart,\r\n			count( IF ( now() BETWEEN ab_time AND ae_time, TRUE, NULL )) progress,\r\n			count( IF ( now() > ae_time, TRUE, NULL )) finish,\r\n			count(*) total \r\n		FROM\r\n			tb_unload \r\n			where 1 =1 \r\n			#REPLACESQL\r\n			\r\n		GROUP BY\r\n			org_code UNION \r\n		SELECT\r\n			org_code,\r\n			count( IF ( now() < star_date, TRUE, NULL )) unstart,\r\n			count( IF ( now() BETWEEN star_date AND end_date, TRUE, NULL )) progress,\r\n			count( IF ( now() > end_date, TRUE, NULL )) finish,\r\n			count(*) total \r\n		FROM\r\n			tb_maintenance \r\n			where 1 =1 \r\n			#REPLACESQL\r\n			\r\n		GROUP BY\r\n			org_code \r\n		) t \r\n	GROUP BY\r\n	org_code \r\n	) t ON e.org_code = t.org_code', 4, '企业作业数量统计', 'erupt', '2023-05-24 09:33:24', 'SELECT\r\n	i.NAME \'作业类型\',\r\n	count( IF ( now() BETWEEN ab_time AND ae_time, TRUE, NULL )) \'现有风险数量\',\r\n	count( IF ( now() > ae_time, TRUE, NULL )) \'历史风险数量\' \r\nFROM\r\n	e_dict d,\r\n	e_dict_item i,\r\n	tb_unload_ship t \r\nWHERE\r\n	d.id = i.erupt_dict_id \r\n	AND d.CODE = \'workTypeShip\' \r\n	AND t.work_type = i.`code` \r\n	#REPLACESQL\r\n	group by t.work_type,i.name union all\r\n	\r\n	\r\n	SELECT\r\n	i.NAME \'作业类型\',\r\n	count( IF ( now() BETWEEN ab_time AND ae_time, TRUE, NULL )) \'现有风险数量\',\r\n	count( IF ( now() > ae_time, TRUE, NULL )) \'历史风险数量\' \r\nFROM\r\n	e_dict d,\r\n	e_dict_item i,\r\n	tb_unload t \r\nWHERE\r\n	d.id = i.erupt_dict_id \r\n	AND d.CODE = \'workType\' \r\n	AND t.work_type = i.`code` \r\n	#REPLACESQL\r\n	group by t.work_type,i.name union all\r\n	\r\n	SELECT\r\n	i.NAME \'作业类型\',\r\n	count( IF ( now() BETWEEN star_date AND end_date, TRUE, NULL )) \'现有风险数量\' ,\r\n	count( IF ( now() > end_date, TRUE, NULL )) \'历史风险数量\' \r\nFROM\r\n	e_dict d,\r\n	e_dict_item i,\r\n	tb_maintenance t \r\nWHERE\r\n	d.id = i.erupt_dict_id \r\n	AND d.CODE = \'MaintenanceType\' \r\n	AND t.type = i.`code` \r\n	#REPLACESQL\r\n	group by t.type,i.name \r\n');
INSERT INTO `e_bi_history` VALUES (48, 'SELECT\r\n	i.NAME \'作业类型\',\r\n	count( IF ( now() BETWEEN ab_time AND ae_time, TRUE, NULL )) \'现有风险数量\',\r\n	count( IF ( now() > ae_time, TRUE, NULL )) \'历史风险数量\' \r\nFROM\r\n	e_dict d,\r\n	e_dict_item i,\r\n	tb_unload_ship t \r\nWHERE\r\n	d.id = i.erupt_dict_id \r\n	AND d.CODE = \'workTypeShip\' \r\n	AND t.work_type = i.`code` \r\n	#REPLACESQL\r\n	group by t.work_type,i.name union all\r\n	\r\n	\r\n	SELECT\r\n	i.NAME \'作业类型\',\r\n	count( IF ( now() BETWEEN ab_time AND ae_time, TRUE, NULL )) \'现有风险数量\',\r\n	count( IF ( now() > ae_time, TRUE, NULL )) \'历史风险数量\' \r\nFROM\r\n	e_dict d,\r\n	e_dict_item i,\r\n	tb_unload t \r\nWHERE\r\n	d.id = i.erupt_dict_id \r\n	AND d.CODE = \'workType\' \r\n	AND t.work_type = i.`code` \r\n	#REPLACESQL\r\n	group by t.work_type,i.name union all\r\n	\r\n	SELECT\r\n	i.NAME \'作业类型\',\r\n	count( IF ( now() BETWEEN star_date AND end_date, TRUE, NULL )) \'现有风险数量\' ,\r\n	count( IF ( now() > end_date, TRUE, NULL )) \'历史风险数量\' \r\nFROM\r\n	e_dict d,\r\n	e_dict_item i,\r\n	tb_maintenance t \r\nWHERE\r\n	d.id = i.erupt_dict_id \r\n	AND d.CODE = \'MaintenanceType\' \r\n	AND t.type = i.`code` \r\n	#REPLACESQL\r\n	group by t.type,i.name \r\n', 4, '作业风险数量统计', 'erupt', '2023-05-24 09:34:31', 'SELECT\r\n	i.NAME \'作业类型\',\r\n	count( IF ( now() BETWEEN ab_time AND ae_time, TRUE, NULL )) \'现有风险数量\',\r\n	count( IF ( now() > ae_time, TRUE, NULL )) \'历史风险数量\' \r\nFROM\r\n	e_dict d,\r\n	e_dict_item i,\r\n	tb_unload_ship t \r\nWHERE\r\n	d.id = i.erupt_dict_id \r\n	AND d.CODE = \'workTypeShip\' \r\n	AND t.work_type = i.`code` \r\n	-- AND org_code = \'#ORGCODE\'\r\n	group by t.work_type,i.name union all\r\n	\r\n	\r\n	SELECT\r\n	i.NAME \'作业类型\',\r\n	count( IF ( now() BETWEEN ab_time AND ae_time, TRUE, NULL )) \'现有风险数量\',\r\n	count( IF ( now() > ae_time, TRUE, NULL )) \'历史风险数量\' \r\nFROM\r\n	e_dict d,\r\n	e_dict_item i,\r\n	tb_unload t \r\nWHERE\r\n	d.id = i.erupt_dict_id \r\n	AND d.CODE = \'workType\' \r\n	AND t.work_type = i.`code` \r\n	-- AND org_code = \'#ORGCODE\'\r\n	group by t.work_type,i.name union all\r\n	\r\n	SELECT\r\n	i.NAME \'作业类型\',\r\n	count( IF ( now() BETWEEN star_date AND end_date, TRUE, NULL )) \'现有风险数量\' ,\r\n	count( IF ( now() > end_date, TRUE, NULL )) \'历史风险数量\' \r\nFROM\r\n	e_dict d,\r\n	e_dict_item i,\r\n	tb_maintenance t \r\nWHERE\r\n	d.id = i.erupt_dict_id \r\n	AND d.CODE = \'MaintenanceType\' \r\n	AND t.type = i.`code` \r\n	-- AND org_code = \'#ORGCODE\'\r\n	group by t.type,i.name \r\n');
INSERT INTO `e_bi_history` VALUES (49, 'SELECT DATE_FORMAT(NOW(),\'%Y-%m-%d %H:%i:%S\') now', 5, '截止时间', 'erupt', '2023-05-24 09:35:04', 'select 1');
INSERT INTO `e_bi_history` VALUES (50, 'SELECT\r\n	t.searchCount,\r\nIF\r\n	( searchCount > searchBeforeCount, CONCAT( \'+\', searchCount - searchBeforeCount ), searchCount - searchBeforeCount ) compare \r\nFROM\r\n	(\r\n	SELECT\r\n		count(\r\n		IF\r\n		( YEAR ( inspection_date ) = \'#searchDate\', TRUE, NULL )) searchCount,\r\n		count(\r\n		IF\r\n		( YEAR ( inspection_date ) = YEAR ( DATE_SUB( concat(\'#searchDate\',\'-01-01\'), INTERVAL 1 YEAR )), TRUE, NULL )) searchBeforeCount \r\n	FROM\r\n	tb_procedure \r\n	where 1 =1 \r\n	#REPLACESQL\r\n	) t\r\n	\r\n	\r\n	', 5, '检查次数', 'erupt', '2023-05-24 09:35:19', 'SELECT\r\n	t.searchCount,\r\nIF\r\n	( searchCount > searchBeforeCount, CONCAT( \'+\', searchCount - searchBeforeCount ), searchCount - searchBeforeCount ) compare \r\nFROM\r\n	(\r\n	SELECT\r\n		count(\r\n		IF\r\n		( YEAR ( inspection_date ) = \'#searchDate\', TRUE, NULL )) searchCount,\r\n		count(\r\n		IF\r\n		( YEAR ( inspection_date ) = YEAR ( DATE_SUB( concat(\'#searchDate\',\'-01-01\'), INTERVAL 1 YEAR )), TRUE, NULL )) searchBeforeCount \r\n	FROM\r\n	tb_procedure \r\n	) t\r\n	\r\n	\r\n	');
INSERT INTO `e_bi_history` VALUES (51, 'SELECT\r\n	t.totalCount,\r\n	ifnull( round( t.totalCount * 100 / t1.beforeTotalCount, 2 ), 0 ) ratio \r\nFROM\r\n	( SELECT count(*) totalCount FROM tb_inspection_results WHERE YEAR ( deadline ) = \'#searchDate\' \r\n	#REPLACESQL\r\n	) t,\r\n	( SELECT count(*) beforeTotalCount FROM tb_inspection_results WHERE YEAR ( deadline ) = YEAR ( DATE_SUB( concat( \'#searchDate\', \'-01-01\' ), INTERVAL 1 YEAR )) \r\n	#REPLACESQL\r\n	) t1 ', 5, '问题总数', 'erupt', '2023-05-24 09:35:34', 'SELECT\r\n	t.totalCount,\r\n	ifnull( round( t.totalCount * 100 / t1.beforeTotalCount, 2 ), 0 ) ratio \r\nFROM\r\n	( SELECT count(*) totalCount FROM tb_inspection_results WHERE YEAR ( deadline ) = \'#searchDate\' ) t,\r\n	( SELECT count(*) beforeTotalCount FROM tb_inspection_results WHERE YEAR ( deadline ) = YEAR ( DATE_SUB( concat( \'#searchDate\', \'-01-01\' ), INTERVAL 1 YEAR )) ) t1 ');
INSERT INTO `e_bi_history` VALUES (52, 'SELECT\r\n	*,\r\n	ifnull(round( finishCount * 100 / totalCount, 2 ),0) ratio \r\nFROM\r\n	(\r\n	SELECT\r\n		count( IF ( inspection_result = \'BY\', TRUE, NULL )) finishCount,\r\n		count(*) totalCount \r\n	FROM\r\n		tb_inspection_results \r\n	WHERE\r\n	YEAR ( deadline ) = \'#searchDate\' \r\n	#REPLACESQL\r\n	) t ', 5, '整改问题数', 'erupt', '2023-05-24 09:35:51', 'SELECT\r\n	*,\r\n	ifnull(round( finishCount * 100 / totalCount, 2 ),0) ratio \r\nFROM\r\n	(\r\n	SELECT\r\n		count( IF ( inspection_result = \'BY\', TRUE, NULL )) finishCount,\r\n		count(*) totalCount \r\n	FROM\r\n		tb_inspection_results \r\n	WHERE\r\n	YEAR ( deadline ) = \'#searchDate\' \r\n	) t ');
INSERT INTO `e_bi_history` VALUES (53, 'SELECT\r\n	*,\r\n	ifnull(round( unfinishCount * 100 / totalCount, 2 ),0) ratio \r\nFROM\r\n	(\r\n	SELECT\r\n		count( IF ( inspection_result != \'BY\', TRUE, NULL )) unfinishCount,\r\n		count(*) totalCount \r\n	FROM\r\n		tb_inspection_results \r\n	WHERE\r\n	YEAR ( deadline ) = \'#searchDate\' \r\n	#REPLACESQL\r\n	) t ', 5, '未整改问题数', 'erupt', '2023-05-24 09:36:11', 'SELECT\r\n	*,\r\n	ifnull(round( unfinishCount * 100 / totalCount, 2 ),0) ratio \r\nFROM\r\n	(\r\n	SELECT\r\n		count( IF ( inspection_result != \'BY\', TRUE, NULL )) unfinishCount,\r\n		count(*) totalCount \r\n	FROM\r\n		tb_inspection_results \r\n	WHERE\r\n	YEAR ( deadline ) = \'#searchDate\' \r\n	) t ');
INSERT INTO `e_bi_history` VALUES (54, 'SELECT\r\n	t.beOverdueCount,\r\n	ifnull( round( beOverdueCount * 100 / totalCount, 2 ), 0 ) ratio \r\nFROM\r\n	(\r\n	SELECT\r\n		count(\r\n		IF\r\n		( deadline < now(), TRUE, NULL )) beOverdueCount,\r\n		count(*) totalCount \r\n	FROM\r\n		tb_inspection_results \r\n	WHERE\r\n		YEAR ( deadline ) = \'#searchDate\' \r\n		#REPLACESQL\r\n	) t ', 5, '逾期问题数', 'erupt', '2023-05-24 09:36:54', 'SELECT\r\n	t.beOverdueCount,\r\n	ifnull( round( beOverdueCount * 100 / totalCount, 2 ), 0 ) ratio \r\nFROM\r\n	(\r\n	SELECT\r\n		count(\r\n		IF\r\n		( deadline > now(), TRUE, NULL )) beOverdueCount,\r\n		count(*) totalCount \r\n	FROM\r\n		tb_inspection_results \r\n	WHERE\r\n		YEAR ( deadline ) = \'#searchDate\' \r\n	) t ');
INSERT INTO `e_bi_history` VALUES (55, 'SELECT\r\n	inspection_items 检查事项,\r\n	COUNT(*) 数量 \r\nFROM\r\n	tb_inspection_results \r\nWHERE\r\n	DATE_FORMAT( create_time, \'%Y\' )= \'#searchDate\'\r\n#REPLACESQL\r\nGROUP BY\r\n	检查事项 \r\nORDER BY\r\n	数量 DESC;', 5, '问题类型统计', 'erupt', '2023-05-24 09:37:15', 'SELECT\r\n	i.inspection_items,\r\n	count(*) c \r\nFROM\r\n	tb_inspection_items i,\r\n	tb_procedure p \r\nWHERE\r\n	i.procedure_id = p.id \r\nAND YEAR ( inspection_date ) = \'#searchDate\' \r\nGROUP BY\r\n	i.inspection_items \r\nORDER BY\r\n	c DESC');
INSERT INTO `e_bi_history` VALUES (56, 'SELECT\r\n	t2.name \'检查名称\',\r\n	count( t1.inspection_name ) \'问题数量\',\r\n	count(\r\n	IF\r\n	( t1.inspection_result = \'BY\', TRUE, NULL )) \'已整改数量\',\r\n	count(\r\n	IF\r\n	( t1.inspection_result != \'BY\', TRUE, NULL )) \'未整改数量\',\r\n	count(\r\n	IF\r\n	( t1.deadline < now(), TRUE, NULL )) \'逾期数量\' \r\nFROM\r\n	tb_inspection_results t1\r\n	RIGHT OUTER JOIN (\r\n	SELECT\r\n		id,\r\n	NAME \r\n	FROM\r\n		tb_procedure \r\n	WHERE\r\n	enterprises LIKE CONCAT( \'%\',( SELECT DISTINCT `name` FROM tb_enterprise WHERE org_code = \'#ORGCODE\' ), \'%\' )) t2 ON t1.inspection_name = t2.id\r\n	where YEAR ( t1.create_time )=\'#searchDate\'\r\nGROUP BY t2.`NAME`', 5, '检查清单', 'erupt', '2023-05-24 09:37:35', '\r\n	SELECT\r\n	  p.name \'检查名称\',\r\n	  count(*) \'问题数量\' ,\r\n		count( IF ( inspection_result = \'BY\', TRUE, NULL )) \'已整改数量\',\r\n		count( IF ( inspection_result != \'BY\', TRUE, NULL )) \'未整改数量\',\r\n		count( IF ( r.deadline > now(), TRUE, NULL )) \'逾期数量\'\r\n		\r\n	FROM\r\n		tb_inspection_results r ,tb_procedure p\r\n		\r\n	WHERE\r\n	YEAR ( r.deadline ) = \'#searchDate\' \r\n	and p.id = r.inspection_name\r\n	and r.org_code = \'#ORGCODE\' \r\n	\r\n	group by p.id,p.name\r\n	');
INSERT INTO `e_bi_history` VALUES (57, 'SELECT\r\n	enterprise.`name` \'企业名称\',\r\n	manage.score \'分数\',\r\n	sum( CASE WHEN score.child_name = \'值班值守与信息报告\' THEN ROUND(score.score*score.weight,2) END ) \'值班值守与信息报告\',\r\n	sum( CASE WHEN score.child_name = \'应急组织与职责\' THEN ROUND(score.score*score.weight,2) END ) \'应急组织与职责\',\r\n	sum( CASE WHEN score.child_name = \'应急管理制度\' THEN ROUND(score.score*score.weight,2) END ) \'应急管理制度\',\r\n	sum( CASE WHEN score.child_name = \'风险管理\' THEN ROUND(score.score*score.weight,2) END ) \'风险管理\',\r\n	sum( CASE WHEN score.child_name = \'应急预案\' THEN ROUND(score.score*score.weight,2) END ) \'应急预案\',\r\n	sum( CASE WHEN score.child_name = \'监控与预警\' THEN ROUND(score.score*score.weight,2) END ) \'监控与预警\',\r\n	sum( CASE WHEN score.child_name = \'教育培训与演练\' THEN ROUND(score.score*score.weight,2) END ) \'教育培训与演练\',\r\n	sum( CASE WHEN score.child_name = \'应急处置与救援\' THEN ROUND(score.score*score.weight,2) END ) \'应急处置与救援\',\r\n	sum( CASE WHEN score.child_name = \'应急恢复\' THEN ROUND(score.score*score.weight,2) END ) \'应急恢复\',\r\n	sum( CASE WHEN score.child_name = \'应急物资装备\' THEN ROUND(score.score*score.weight,2) END ) \'应急物资装备\',\r\n	sum( CASE WHEN score.child_name = \'应急救援队伍\' THEN ROUND(score.score*score.weight,2) END ) \'应急救援队伍\',\r\n	sum( CASE WHEN score.child_name = \'应急经费\' THEN ROUND(score.score*score.weight,2) END ) \'	应急经费\'\r\nFROM\r\n	( SELECT max( id ) maxid, company FROM tb_evaluation_manage manage WHERE manage.public_state=true GROUP BY company ) maxMan,\r\n	tb_evaluation_manage manage,\r\n	tb_enterprise enterprise,\r\n	tb_evaluation_score score\r\nWHERE\r\n	maxMan.maxid = manage.id \r\n	AND manage.company = enterprise.org_code \r\n	AND manage.id = score.eval_id \r\n	 AND enterprise.org_code  in(\'89b6efb5\')\r\nGROUP BY\r\n	enterprise.`name`,\r\n	manage.score \r\nORDER BY\r\n	manage.score DESC; \r\n', 6, '企业评估明细', 'erupt', '2023-05-24 14:40:47', 'SELECT\r\n	enterprise.`name` \'企业名称\',\r\n	sum( CASE WHEN score.child_name = \'应急组织与职责\' THEN score.score END ) \'应急组织与职责\',\r\n	sum( CASE WHEN score.child_name = \'应急管理制度\' THEN score.score END ) \'应急管理制度\',\r\n	sum( CASE WHEN score.child_name = \'风险管理\' THEN score.score END ) \'风险管理\',\r\n	sum( CASE WHEN score.child_name = \'应急预案\' THEN score.score END ) \'应急预案\',\r\n	sum( CASE WHEN score.child_name = \'监控与预警\' THEN score.score END ) \'监控与预警\',\r\n	sum( CASE WHEN score.child_name = \'教育培训与演练\' THEN score.score END ) \'教育培训与演练\',\r\n	sum( CASE WHEN score.child_name = \'值班值守与信息报告\' THEN score.score END ) \'值班值守与信息报告\',\r\n	sum( CASE WHEN score.child_name = \'应急处置与救援\' THEN score.score END ) \'应急处置与救援\',\r\n	sum( CASE WHEN score.child_name = \'应急恢复\' THEN score.score END ) \'应急恢复\',\r\n	sum( CASE WHEN score.child_name = \'应急物资装备\' THEN score.score END ) \'应急物资装备\',\r\n	sum( CASE WHEN score.child_name = \'应急救援队伍\' THEN score.score END ) \'应急救援队伍\',\r\n	sum( CASE WHEN score.child_name = \'应急经费\' THEN score.score END ) \'	应急经费\',\r\n	manage.score \'分数\' \r\nFROM\r\n	( SELECT max( id ) maxid, org_code FROM tb_evaluation_manage manage GROUP BY org_code ) maxMan,\r\n	tb_evaluation_manage manage,\r\n	tb_enterprise enterprise,\r\n	tb_evaluation_score score \r\nWHERE\r\n	maxMan.maxid = manage.id \r\n	AND manage.company = enterprise.org_code \r\n	AND manage.id = score.eval_id \r\nGROUP BY\r\n	enterprise.`name`,\r\n	manage.score \r\nORDER BY\r\n	manage.score DESC;');
INSERT INTO `e_bi_history` VALUES (58, 'SELECT\r\n	t.NAME \'维度\',\r\n	ROUND(avg( s.score ),2) \'平均值\' \r\nFROM\r\n	(\r\n	SELECT\r\n		i.CODE,\r\n		i.NAME \r\n	FROM\r\n		e_dict d,\r\n		e_dict_item i \r\n	WHERE\r\n		d.CODE = \'PrincipalElement\' \r\n		AND d.id = i.erupt_dict_id \r\n	) t\r\n	LEFT JOIN ( SELECT * FROM tb_evaluation_score ) s ON t.CODE = s.NAME \r\nGROUP BY\r\n	t.NAME ', 6, '企业4个维度平均值', 'erupt', '2023-05-24 14:41:55', 'SELECT\r\n	t.NAME \'维度\',\r\n	avg( s.score ) \'平均值\' \r\nFROM\r\n	(\r\n	SELECT\r\n		i.CODE,\r\n		i.NAME \r\n	FROM\r\n		e_dict d,\r\n		e_dict_item i \r\n	WHERE\r\n		d.CODE = \'PrincipalElement\' \r\n		AND d.id = i.erupt_dict_id \r\n	) t\r\n	LEFT JOIN ( SELECT * FROM tb_evaluation_score ) s ON t.CODE = s.NAME \r\nGROUP BY\r\n	t.NAME ');
INSERT INTO `e_bi_history` VALUES (59, 'SELECT\r\n	child_name \'要素\',\r\n	round(avg( score ),2) \'平均值\' \r\nFROM\r\n	tb_evaluation_score \r\nGROUP BY\r\n	child_name;', 6, '企业12个要素平均值', 'erupt', '2023-05-24 14:42:24', 'SELECT\r\n	child_name \'要素\',\r\n	avg( score ) \'平均值\' \r\nFROM\r\n	tb_evaluation_score \r\nGROUP BY\r\n	child_name;');
INSERT INTO `e_bi_history` VALUES (60, 'SELECT\r\n	enterprise.`name` \'企业名称\',\r\n	manage.score \'分数\',\r\n	sum( CASE WHEN score.child_name = \'值班值守与信息报告\' THEN ROUND(score.score*score.weight,2) END ) \'值班值守与信息报告\',\r\n	sum( CASE WHEN score.child_name = \'应急组织与职责\' THEN ROUND(score.score*score.weight,2) END ) \'应急组织与职责\',\r\n	sum( CASE WHEN score.child_name = \'应急管理制度\' THEN ROUND(score.score*score.weight,2) END ) \'应急管理制度\',\r\n	sum( CASE WHEN score.child_name = \'风险管理\' THEN ROUND(score.score*score.weight,2) END ) \'风险管理\',\r\n	sum( CASE WHEN score.child_name = \'应急预案\' THEN ROUND(score.score*score.weight,2) END ) \'应急预案\',\r\n	sum( CASE WHEN score.child_name = \'监控与预警\' THEN ROUND(score.score*score.weight,2) END ) \'监控与预警\',\r\n	sum( CASE WHEN score.child_name = \'教育培训与演练\' THEN ROUND(score.score*score.weight,2) END ) \'教育培训与演练\',\r\n	sum( CASE WHEN score.child_name = \'应急处置与救援\' THEN ROUND(score.score*score.weight,2) END ) \'应急处置与救援\',\r\n	sum( CASE WHEN score.child_name = \'应急恢复\' THEN ROUND(score.score*score.weight,2) END ) \'应急恢复\',\r\n	sum( CASE WHEN score.child_name = \'应急物资装备\' THEN ROUND(score.score*score.weight,2) END ) \'应急物资装备\',\r\n	sum( CASE WHEN score.child_name = \'应急救援队伍\' THEN ROUND(score.score*score.weight,2) END ) \'应急救援队伍\',\r\n	sum( CASE WHEN score.child_name = \'应急经费\' THEN ROUND(score.score*score.weight,2) END ) \'	应急经费\'\r\nFROM\r\n	( SELECT max( id ) maxid, company FROM tb_evaluation_manage manage WHERE manage.public_state=1 GROUP BY company ) maxMan,\r\n	tb_evaluation_manage manage,\r\n	tb_enterprise enterprise,\r\n	tb_evaluation_score score\r\nWHERE\r\n	maxMan.maxid = manage.id \r\n	AND manage.company = enterprise.org_code \r\n	AND manage.id = score.eval_id \r\n	#REPLACESQL\r\nGROUP BY\r\n	enterprise.`name`,\r\n	manage.score \r\nORDER BY\r\n	manage.score DESC; \r\n', 6, '企业评估明细', 'erupt', '2023-05-24 14:44:43', 'SELECT\r\n	enterprise.`name` \'企业名称\',\r\n	manage.score \'分数\',\r\n	sum( CASE WHEN score.child_name = \'值班值守与信息报告\' THEN ROUND(score.score*score.weight,2) END ) \'值班值守与信息报告\',\r\n	sum( CASE WHEN score.child_name = \'应急组织与职责\' THEN ROUND(score.score*score.weight,2) END ) \'应急组织与职责\',\r\n	sum( CASE WHEN score.child_name = \'应急管理制度\' THEN ROUND(score.score*score.weight,2) END ) \'应急管理制度\',\r\n	sum( CASE WHEN score.child_name = \'风险管理\' THEN ROUND(score.score*score.weight,2) END ) \'风险管理\',\r\n	sum( CASE WHEN score.child_name = \'应急预案\' THEN ROUND(score.score*score.weight,2) END ) \'应急预案\',\r\n	sum( CASE WHEN score.child_name = \'监控与预警\' THEN ROUND(score.score*score.weight,2) END ) \'监控与预警\',\r\n	sum( CASE WHEN score.child_name = \'教育培训与演练\' THEN ROUND(score.score*score.weight,2) END ) \'教育培训与演练\',\r\n	sum( CASE WHEN score.child_name = \'应急处置与救援\' THEN ROUND(score.score*score.weight,2) END ) \'应急处置与救援\',\r\n	sum( CASE WHEN score.child_name = \'应急恢复\' THEN ROUND(score.score*score.weight,2) END ) \'应急恢复\',\r\n	sum( CASE WHEN score.child_name = \'应急物资装备\' THEN ROUND(score.score*score.weight,2) END ) \'应急物资装备\',\r\n	sum( CASE WHEN score.child_name = \'应急救援队伍\' THEN ROUND(score.score*score.weight,2) END ) \'应急救援队伍\',\r\n	sum( CASE WHEN score.child_name = \'应急经费\' THEN ROUND(score.score*score.weight,2) END ) \'	应急经费\'\r\nFROM\r\n	( SELECT max( id ) maxid, company FROM tb_evaluation_manage manage WHERE manage.public_state=true GROUP BY company ) maxMan,\r\n	tb_evaluation_manage manage,\r\n	tb_enterprise enterprise,\r\n	tb_evaluation_score score\r\nWHERE\r\n	maxMan.maxid = manage.id \r\n	AND manage.company = enterprise.org_code \r\n	AND manage.id = score.eval_id \r\n	 AND enterprise.org_code  in(\'89b6efb5\')\r\nGROUP BY\r\n	enterprise.`name`,\r\n	manage.score \r\nORDER BY\r\n	manage.score DESC; \r\n');
INSERT INTO `e_bi_history` VALUES (61, 'SELECT\r\n	grade.NAME \'等级\',\r\n	count( manage.id ) \'企业数量\'\r\nFROM\r\n	tb_evaluation_grade grade\r\n	LEFT JOIN tb_evaluation_manage manage ON grade.id = manage.eval_grade_id \r\n	where 1 = 1 and manage.public_state=1\r\n	#REPLACESQL\r\nGROUP BY\r\n	grade.NAME;', 6, '评估企业数量', 'erupt', '2023-05-24 14:45:45', 'SELECT\r\n	grade.NAME \'等级\',\r\n	count( manage.id ) \'企业数量\'\r\nFROM\r\n	tb_evaluation_grade grade\r\n	LEFT JOIN tb_evaluation_manage manage ON grade.id = manage.eval_grade_id \r\nGROUP BY\r\n	grade.NAME;');
INSERT INTO `e_bi_history` VALUES (62, 'SELECT\r\n	enterprise.`name`  \'企业名称\'\r\nFROM\r\n	tb_evaluation_grade grade\r\n	LEFT JOIN tb_evaluation_manage manage ON grade.id = manage.eval_grade_id \r\n	INNER JOIN tb_enterprise enterprise ON  manage.company = enterprise.org_code\r\nWHERE\r\n	grade.NAME = \'不合格\' \r\n	AND manage.public_state=1\r\n	#REPLACESQL', 6, '不合格企业名单', 'erupt', '2023-05-24 14:50:54', 'SELECT\r\n	enterprise.`name`  \'企业名称\'\r\nFROM\r\n	tb_evaluation_grade grade\r\n	LEFT JOIN tb_evaluation_manage manage ON grade.id = manage.eval_grade_id,\r\n	tb_enterprise enterprise \r\nWHERE\r\n	grade.NAME = \'不合格\' \r\n	AND manage.company = enterprise.org_code');
INSERT INTO `e_bi_history` VALUES (63, 'SELECT\r\n	enterprise.`name`  \'企业名称\',\r\n	manage.score \'分数\'\r\nFROM\r\n  tb_evaluation_manage manage ,\r\n	tb_enterprise enterprise \r\nwhere manage.company = enterprise.org_code and manage.public_state=1\r\norder by score desc\r\nlimit 10', 6, 'top10企业名单', 'erupt', '2023-05-24 14:51:24', 'SELECT\r\n	enterprise.`name`  \'企业名称\',\r\n	manage.score \'分数\'\r\nFROM\r\n  tb_evaluation_manage manage ,\r\n	tb_enterprise enterprise \r\nwhere manage.company = enterprise.org_code\r\norder by score desc\r\nlimit 10');
INSERT INTO `e_bi_history` VALUES (64, 'SELECT\r\n	enterprise.`name` \'企业名称\' \r\nFROM\r\n	tb_enterprise enterprise\r\n	LEFT JOIN tb_evaluation_manage manage ON enterprise.org_code = manage.company \r\nWHERE\r\n	manage.id IS NULL\r\n	ADN manage.public_state=1;', 6, '未评估企业名单', 'erupt', '2023-05-24 14:52:42', 'SELECT\r\n	enterprise.`name` \'企业名称\' \r\nFROM\r\n	tb_enterprise enterprise\r\n	LEFT JOIN tb_evaluation_manage manage ON enterprise.org_code = manage.company \r\nWHERE\r\n	manage.id IS NULL;');
INSERT INTO `e_bi_history` VALUES (65, 'SELECT\r\n	enterprise.`name` \'企业名称\' \r\nFROM\r\n	tb_enterprise enterprise\r\n	LEFT JOIN tb_evaluation_manage manage ON enterprise.org_code = manage.company \r\nWHERE\r\n	manage.id IS NULL\r\n	AND manage.public_state=1;', 6, '未评估企业名单', 'erupt', '2023-05-24 14:56:16', 'SELECT\r\n	enterprise.`name` \'企业名称\' \r\nFROM\r\n	tb_enterprise enterprise\r\n	LEFT JOIN tb_evaluation_manage manage ON enterprise.org_code = manage.company \r\nWHERE\r\n	manage.id IS NULL\r\n	ADN manage.public_state=1;');
INSERT INTO `e_bi_history` VALUES (66, 'SELECT\r\n	enterprise.`name` \'企业名称\' \r\nFROM\r\n	tb_enterprise enterprise\r\n	LEFT JOIN tb_evaluation_manage manage ON enterprise.org_code = manage.company \r\nWHERE\r\n	manage.id IS NULL\r\n	AND manage.public_state=1\r\n	#REPLACESQL', 6, '未评估企业名单', 'erupt', '2023-05-24 15:07:26', 'SELECT\r\n	enterprise.`name` \'企业名称\' \r\nFROM\r\n	tb_enterprise enterprise\r\n	LEFT JOIN tb_evaluation_manage manage ON enterprise.org_code = manage.company \r\nWHERE\r\n	manage.id IS NULL\r\n	AND manage.public_state=1;');
INSERT INTO `e_bi_history` VALUES (67, 'SELECT\r\n	enterprise.`name`  \'企业名称\',\r\n	manage.score \'分数\'\r\nFROM\r\n  tb_evaluation_manage manage ,\r\n	tb_enterprise enterprise \r\nwhere manage.company = enterprise.org_code and manage.public_state=1\r\n#REPLACESQL\r\norder by score desc\r\nlimit 10', 6, 'top10企业名单', 'erupt', '2023-05-24 15:20:22', 'SELECT\r\n	enterprise.`name`  \'企业名称\',\r\n	manage.score \'分数\'\r\nFROM\r\n  tb_evaluation_manage manage ,\r\n	tb_enterprise enterprise \r\nwhere manage.company = enterprise.org_code and manage.public_state=1\r\norder by score desc\r\nlimit 10');
INSERT INTO `e_bi_history` VALUES (68, 'SELECT\r\n	child_name \'要素\',\r\n	round(avg( score ),2) \'平均值\' \r\nFROM\r\n	tb_evaluation_score \r\nWHERE 1=1\r\n	#REPLACESQL\r\nGROUP BY\r\n	child_name;', 6, '企业12个要素平均值', 'erupt', '2023-05-24 15:21:02', 'SELECT\r\n	child_name \'要素\',\r\n	round(avg( score ),2) \'平均值\' \r\nFROM\r\n	tb_evaluation_score \r\nGROUP BY\r\n	child_name;');
INSERT INTO `e_bi_history` VALUES (69, 'SELECT\r\n	child_name \'要素\',\r\n	round(avg( score ),2) \'平均值\' \r\nFROM\r\n	tb_evaluation_score \r\nGROUP BY\r\n	child_name;', 6, '企业12个要素平均值', 'erupt', '2023-05-24 15:21:29', 'SELECT\r\n	child_name \'要素\',\r\n	round(avg( score ),2) \'平均值\' \r\nFROM\r\n	tb_evaluation_score \r\nWHERE 1=1\r\n	#REPLACESQL\r\nGROUP BY\r\n	child_name;');
INSERT INTO `e_bi_history` VALUES (70, 'SELECT\r\n	DISTINCT enterprise.`name`  \'企业名称\'\r\nFROM\r\n	tb_evaluation_grade grade\r\n	LEFT JOIN tb_evaluation_manage manage ON grade.id = manage.eval_grade_id \r\n	INNER JOIN tb_enterprise enterprise ON  manage.company = enterprise.org_code\r\nWHERE\r\n	grade.NAME = \'不合格\' \r\n	AND manage.public_state=1\r\n	#REPLACESQL', 6, '不合格企业名单', 'erupt', '2023-05-24 15:29:44', 'SELECT\r\n	enterprise.`name`  \'企业名称\'\r\nFROM\r\n	tb_evaluation_grade grade\r\n	LEFT JOIN tb_evaluation_manage manage ON grade.id = manage.eval_grade_id \r\n	INNER JOIN tb_enterprise enterprise ON  manage.company = enterprise.org_code\r\nWHERE\r\n	grade.NAME = \'不合格\' \r\n	AND manage.public_state=1\r\n	#REPLACESQL');
INSERT INTO `e_bi_history` VALUES (71, 'SELECT\r\n	enterprise.`name`  \'企业名称\',\r\n	manage.score \'分数\'\r\nFROM\r\n  tb_evaluation_manage manage ,\r\n	tb_enterprise enterprise \r\nwhere manage.company = enterprise.org_code and manage.public_state=1\r\n#REPLACESQL\r\norder by manage.score desc\r\nlimit 10', 6, 'top10企业名单', 'erupt', '2023-05-24 15:34:07', 'SELECT\r\n	enterprise.`name`  \'企业名称\',\r\n	manage.score \'分数\'\r\nFROM\r\n  tb_evaluation_manage manage ,\r\n	tb_enterprise enterprise \r\nwhere manage.company = enterprise.org_code and manage.public_state=1\r\n#REPLACESQL\r\norder by score desc\r\nlimit 10');
INSERT INTO `e_bi_history` VALUES (72, 'SELECT\r\n	enterprise.`name`  \'企业名称\',\r\n	manage.score \'分数\'\r\nFROM\r\n  tb_evaluation_manage manage ,\r\n	tb_enterprise enterprise \r\nwhere manage.company = enterprise.org_code and manage.public_state=1\r\n#REPLACESQL\r\nlimit 10\r\norder by manage.score desc', 6, 'top10企业名单', 'erupt', '2023-05-24 15:35:15', 'SELECT\r\n	enterprise.`name`  \'企业名称\',\r\n	manage.score \'分数\'\r\nFROM\r\n  tb_evaluation_manage manage ,\r\n	tb_enterprise enterprise \r\nwhere manage.company = enterprise.org_code and manage.public_state=1\r\n#REPLACESQL\r\norder by manage.score desc\r\nlimit 10');
INSERT INTO `e_bi_history` VALUES (73, 'SELECT\r\n	enterprise.`name`  \'企业名称\',\r\n	manage.score \'分数\'\r\nFROM\r\n  tb_evaluation_manage manage ,\r\n	tb_enterprise enterprise \r\nwhere manage.company = enterprise.org_code and manage.public_state=1\r\n#REPLACESQL\r\norder by manage.score desc\r\nlimit 0,10', 6, 'top10企业名单', 'erupt', '2023-05-24 15:41:56', 'SELECT\r\n	enterprise.`name`  \'企业名称\',\r\n	manage.score \'分数\'\r\nFROM\r\n  tb_evaluation_manage manage ,\r\n	tb_enterprise enterprise \r\nwhere manage.company = enterprise.org_code and manage.public_state=1\r\n#REPLACESQL\r\nlimit 10\r\norder by manage.score desc');
INSERT INTO `e_bi_history` VALUES (74, 'SELECT\r\n	DISTINCT enterprise.`name`  \'企业名称\',\r\n	manage.score \'分数\'\r\nFROM\r\n  tb_evaluation_manage manage ,\r\n	tb_enterprise enterprise \r\nwhere manage.company = enterprise.org_code and manage.public_state=1\r\n#REPLACESQL\r\norder by manage.score desc\r\nlimit 0,10', 6, 'top10企业名单', 'erupt', '2023-05-24 15:42:15', 'SELECT\r\n	enterprise.`name`  \'企业名称\',\r\n	manage.score \'分数\'\r\nFROM\r\n  tb_evaluation_manage manage ,\r\n	tb_enterprise enterprise \r\nwhere manage.company = enterprise.org_code and manage.public_state=1\r\n#REPLACESQL\r\norder by manage.score desc\r\nlimit 0,10');
INSERT INTO `e_bi_history` VALUES (75, 'SELECT\r\n	DISTINCT enterprise.`name`  \'企业名称\',\r\n	manage.score \'分数\'\r\nFROM\r\n  tb_evaluation_manage manage ,\r\n	tb_enterprise enterprise \r\nwhere manage.company = enterprise.org_code and\r\nmanage.public_state=1\r\n#REPLACESQL\r\norder by manage.score desc\r\nlimit 0,10', 6, 'top10企业名单', 'erupt', '2023-05-24 15:53:49', 'SELECT\r\n	DISTINCT enterprise.`name`  \'企业名称\',\r\n	manage.score \'分数\'\r\nFROM\r\n  tb_evaluation_manage manage ,\r\n	tb_enterprise enterprise \r\nwhere manage.company = enterprise.org_code and manage.public_state=1\r\n#REPLACESQL\r\norder by manage.score desc\r\nlimit 0,10');
INSERT INTO `e_bi_history` VALUES (76, 'SELECT DISTINCT enterprise.name FROM\r\n(SELECT * FROM tb_enterprise  WHERE #REPLACESQL\r\n) enterprise RIGHT JOIN tb_evaluation_manage \r\nmanage ON enterprise.org_code=manage.company\r\nWHERE manage.id IS NULL;', 6, '未评估企业名单', 'erupt', '2023-05-24 16:02:16', 'SELECT\r\n	enterprise.`name` \'企业名称\' \r\nFROM\r\n	tb_enterprise enterprise\r\n	LEFT JOIN tb_evaluation_manage manage ON enterprise.org_code = manage.company \r\nWHERE\r\n	manage.id IS NULL\r\n	AND manage.public_state=1\r\n	#REPLACESQL');
INSERT INTO `e_bi_history` VALUES (77, 'SELECT DISTINCT enterprise.name FROM\r\n(SELECT * FROM tb_enterprise enterprise WHERE 1=1 \r\n #REPLACESQL\r\n) enterprise RIGHT JOIN tb_evaluation_manage \r\nmanage ON enterprise.org_code=manage.company\r\nWHERE manage.id IS NULL;', 6, '未评估企业名单', 'erupt', '2023-05-24 16:05:07', 'SELECT DISTINCT enterprise.name FROM\r\n(SELECT * FROM tb_enterprise  WHERE #REPLACESQL\r\n) enterprise RIGHT JOIN tb_evaluation_manage \r\nmanage ON enterprise.org_code=manage.company\r\nWHERE manage.id IS NULL;');
INSERT INTO `e_bi_history` VALUES (78, 'SELECT DISTINCT enterprise.name FROM\r\n(SELECT * FROM tb_enterprise enterprise WHERE 1=1 \r\n #REPLACESQL\r\n) enterprise LEFT JOIN tb_evaluation_manage \r\nmanage ON enterprise.org_code=manage.company\r\nWHERE manage.id IS NULL;', 6, '未评估企业名单', 'erupt', '2023-05-24 16:18:06', 'SELECT DISTINCT enterprise.name FROM\r\n(SELECT * FROM tb_enterprise enterprise WHERE 1=1 \r\n #REPLACESQL\r\n) enterprise RIGHT JOIN tb_evaluation_manage \r\nmanage ON enterprise.org_code=manage.company\r\nWHERE manage.id IS NULL;');
INSERT INTO `e_bi_history` VALUES (79, 'SELECT DISTINCT enterprise.name \'企业名称\' FROM\r\n(SELECT * FROM tb_enterprise enterprise WHERE 1=1 \r\n #REPLACESQL\r\n) enterprise LEFT JOIN tb_evaluation_manage \r\nmanage ON enterprise.org_code=manage.company\r\nWHERE manage.id IS NULL;', 6, '未评估企业名单', 'erupt', '2023-05-24 16:22:19', 'SELECT DISTINCT enterprise.name FROM\r\n(SELECT * FROM tb_enterprise enterprise WHERE 1=1 \r\n #REPLACESQL\r\n) enterprise LEFT JOIN tb_evaluation_manage \r\nmanage ON enterprise.org_code=manage.company\r\nWHERE manage.id IS NULL;');
INSERT INTO `e_bi_history` VALUES (80, 'SELECT\r\n	name \'风险类型\',\r\n	sum( process ) \'现有风险数量\',\r\n	sum( finished ) \'历史风险数量\' \r\nFROM\r\n(SELECT\r\n		s3.`NAME`,\r\n		count(\r\n		IF\r\n		( now() BETWEEN s.ab_time AND s.ae_time, TRUE, NULL )) process,\r\n		count(\r\n		IF\r\n		( now() > s.ae_time, TRUE, NULL )) finished \r\nFROM\r\n		tb_unload s,\r\n		(SELECT id,risk_type FROM tb_risk_database_enterprise) s2,\r\n		(	SELECT\r\n				i.NAME NAME,\r\n				i.`code` CODE \r\n			FROM\r\n				e_dict d\r\n				INNER JOIN e_dict_item i ON d.id = i.erupt_dict_id\r\n			AND d.`code` = \'riskType\'	) s3\r\nWHERE s.risk_database=s2.id	and s2.risk_type=s3.`code`\r\n #REPLACESQL\r\nGROUP BY s3.`NAME`		\r\nUNION ALL\r\nSELECT\r\n		s3.`NAME`,\r\n		count(\r\n		IF\r\n		( now() BETWEEN s.ab_time AND s.ae_time, TRUE, NULL )) process,\r\n		count(\r\n		IF\r\n		( now() > s.ae_time, TRUE, NULL )) finished \r\nFROM\r\n		tb_unload_ship s,\r\n		(SELECT id,risk_type FROM tb_risk_database_enterprise) s2,\r\n		(	SELECT\r\n				i.NAME NAME,\r\n				i.`code` CODE \r\n			FROM\r\n				e_dict d\r\n				INNER JOIN e_dict_item i ON d.id = i.erupt_dict_id\r\n			AND d.`code` = \'riskType\'	) s3\r\nWHERE s.risk_database=s2.id	and s2.risk_type=s3.`code`\r\n #REPLACESQL\r\nGROUP BY s3.`NAME`) k		\r\nGROUP BY name\r\n', 4, '风险类型统计', 'erupt', '2023-05-24 19:07:06', 'SELECT\r\n	name \'风险类型\',\r\n	sum( process ) \'现有风险数量\',\r\n	sum( finished ) \'历史风险数量\' \r\nFROM\r\n(SELECT\r\n		t.name,\r\n		count(IF( now() BETWEEN s.ab_time AND s.ae_time, TRUE, NULL )) process,\r\n		count(IF( now() > s.ae_time, TRUE, NULL )) finished \r\n	FROM\r\n		tb_unload s INNER JOIN	\r\n(SELECT i.name name,i.`code` code FROM e_dict d,e_dict_item i WHERE d.id=i.erupt_dict_id AND d.`code`=\'riskType\')t\r\nON s.risk_database=t.`code`\r\nWHERE 1=1\r\n#REPLACESQL\r\nGROUP BY t.`name` \r\nUNION ALL\r\nSELECT\r\n		t.name,\r\n		count(IF( now() BETWEEN s.ab_time AND s.ae_time, TRUE, NULL )) process,\r\n		count(IF( now() > s.ae_time, TRUE, NULL )) finished \r\n	FROM\r\n		tb_unload_ship s INNER JOIN	\r\n(SELECT i.name name,i.`code` code FROM e_dict d,e_dict_item i WHERE d.id=i.erupt_dict_id AND d.`code`=\'riskType\')t\r\nON s.risk_database=t.`code`\r\nWHERE 1=1\r\n#REPLACESQL\r\nGROUP BY t.`name`) t\r\nGROUP BY name\r\n');
INSERT INTO `e_bi_history` VALUES (81, 'SELECT\r\n	name \'风险类型\',\r\n	sum( process ) \'现有风险数量\',\r\n	sum( finished ) \'历史风险数量\' \r\nFROM\r\n(SELECT\r\n		s3.`NAME`,\r\n		count(\r\n		IF\r\n		( now() BETWEEN s.ab_time AND s.ae_time, TRUE, NULL )) process,\r\n		count(\r\n		IF\r\n		( now() > s.ae_time, TRUE, NULL )) finished \r\nFROM\r\n		tb_unload s,\r\n		(SELECT id,risk_type FROM tb_risk_database_enterprise) s2,\r\n		(	SELECT\r\n				i.NAME NAME,\r\n				i.`code` CODE \r\n			FROM\r\n				e_dict d\r\n				INNER JOIN e_dict_item i ON d.id = i.erupt_dict_id\r\n			AND d.`code` = \'riskType\'	) s3\r\nWHERE s.risk_database=s2.id	and s2.risk_type=s3.`code`\r\n #REPLACESQL\r\nGROUP BY s3.`NAME`		\r\nUNION ALL\r\nSELECT\r\n		s3.`NAME`,\r\n		count(\r\n		IF\r\n		( now() BETWEEN s.ab_time AND s.ae_time, TRUE, NULL )) process,\r\n		count(\r\n		IF\r\n		( now() > s.ae_time, TRUE, NULL )) finished \r\nFROM\r\n		tb_unload_ship s,\r\n		(SELECT id,risk_type FROM tb_risk_database_enterprise) s2,\r\n		(	SELECT\r\n				i.NAME NAME,\r\n				i.`code` CODE \r\n			FROM\r\n				e_dict d\r\n				INNER JOIN e_dict_item i ON d.id = i.erupt_dict_id\r\n			AND d.`code` = \'riskType\'	) s3\r\nWHERE s.risk_database=s2.id	and s2.risk_type=s3.`code`\r\n #REPLACESQL\r\nGROUP BY s3.`NAME`) k		\r\nGROUP BY name', 1, '风险类型统计', 'erupt', '2023-05-24 19:07:38', 'SELECT\r\n	name \'风险类型\',\r\n	sum( process ) \'现有风险数量\',\r\n	sum( finished ) \'历史风险数量\' \r\nFROM\r\n(SELECT\r\n		t.name,\r\n		count(IF( now() BETWEEN s.ab_time AND s.ae_time, TRUE, NULL )) process,\r\n		count(IF( now() > s.ae_time, TRUE, NULL )) finished \r\n	FROM\r\n		tb_unload s INNER JOIN	\r\n(SELECT i.name name,i.`code` code FROM e_dict d,e_dict_item i WHERE d.id=i.erupt_dict_id AND d.`code`=\'riskType\')t\r\nON s.risk_database=t.`code`\r\nWHERE org_code = \'#ORGCODE\' \r\nGROUP BY t.`name` \r\nUNION ALL\r\nSELECT\r\n		t.name,\r\n		count(IF( now() BETWEEN s.ab_time AND s.ae_time, TRUE, NULL )) process,\r\n		count(IF( now() > s.ae_time, TRUE, NULL )) finished \r\n	FROM\r\n		tb_unload_ship s INNER JOIN	\r\n(SELECT i.name name,i.`code` code FROM e_dict d,e_dict_item i WHERE d.id=i.erupt_dict_id AND d.`code`=\'riskType\')t\r\nON s.risk_database=t.`code`\r\nWHERE org_code = \'#ORGCODE\' \r\nGROUP BY t.`name`) t\r\nGROUP BY name');
INSERT INTO `e_bi_history` VALUES (82, 'SELECT\r\n	name \'风险类型\',\r\n	sum( process ) \'现有风险数量\',\r\n	sum( finished ) \'历史风险数量\' \r\nFROM\r\n(SELECT\r\n		s3.`NAME`,\r\n		count(\r\n		IF\r\n		( now() BETWEEN s.ab_time AND s.ae_time, TRUE, NULL )) process,\r\n		count(\r\n		IF\r\n		( now() > s.ae_time, TRUE, NULL )) finished \r\nFROM\r\n		tb_unload s,\r\n		(SELECT id,risk_type FROM tb_risk_database_enterprise) s2,\r\n		(	SELECT\r\n				i.NAME NAME,\r\n				i.`code` CODE \r\n			FROM\r\n				e_dict d\r\n				INNER JOIN e_dict_item i ON d.id = i.erupt_dict_id\r\n			AND d.`code` = \'riskType\'	) s3\r\nWHERE s.risk_database=s2.id	and s2.risk_type=s3.`code`\r\n #REPLACESQL\r\nGROUP BY s3.`NAME`		\r\nUNION ALL\r\nSELECT\r\n		s3.`NAME`,\r\n		count(\r\n		IF\r\n		( now() BETWEEN s.ab_time AND s.ae_time, TRUE, NULL )) process,\r\n		count(\r\n		IF\r\n		( now() > s.ae_time, TRUE, NULL )) finished \r\nFROM\r\n		tb_unload_ship s,\r\n		(SELECT id,risk_type FROM tb_risk_database_enterprise) s2,\r\n		(	SELECT\r\n				i.NAME NAME,\r\n				i.`code` CODE \r\n			FROM\r\n				e_dict d\r\n				INNER JOIN e_dict_item i ON d.id = i.erupt_dict_id\r\n			AND d.`code` = \'riskType\'	) s3\r\nWHERE s.risk_database=s2.id	and s2.risk_type=s3.`code`\r\n #REPLACESQL\r\nGROUP BY s3.`NAME`) k		\r\nGROUP BY name', 4, '风险类型统计', 'erupt', '2023-05-24 19:07:58', 'SELECT\r\n	name \'风险类型\',\r\n	sum( process ) \'现有风险数量\',\r\n	sum( finished ) \'历史风险数量\' \r\nFROM\r\n(SELECT\r\n		s3.`NAME`,\r\n		count(\r\n		IF\r\n		( now() BETWEEN s.ab_time AND s.ae_time, TRUE, NULL )) process,\r\n		count(\r\n		IF\r\n		( now() > s.ae_time, TRUE, NULL )) finished \r\nFROM\r\n		tb_unload s,\r\n		(SELECT id,risk_type FROM tb_risk_database_enterprise) s2,\r\n		(	SELECT\r\n				i.NAME NAME,\r\n				i.`code` CODE \r\n			FROM\r\n				e_dict d\r\n				INNER JOIN e_dict_item i ON d.id = i.erupt_dict_id\r\n			AND d.`code` = \'riskType\'	) s3\r\nWHERE s.risk_database=s2.id	and s2.risk_type=s3.`code`\r\n #REPLACESQL\r\nGROUP BY s3.`NAME`		\r\nUNION ALL\r\nSELECT\r\n		s3.`NAME`,\r\n		count(\r\n		IF\r\n		( now() BETWEEN s.ab_time AND s.ae_time, TRUE, NULL )) process,\r\n		count(\r\n		IF\r\n		( now() > s.ae_time, TRUE, NULL )) finished \r\nFROM\r\n		tb_unload_ship s,\r\n		(SELECT id,risk_type FROM tb_risk_database_enterprise) s2,\r\n		(	SELECT\r\n				i.NAME NAME,\r\n				i.`code` CODE \r\n			FROM\r\n				e_dict d\r\n				INNER JOIN e_dict_item i ON d.id = i.erupt_dict_id\r\n			AND d.`code` = \'riskType\'	) s3\r\nWHERE s.risk_database=s2.id	and s2.risk_type=s3.`code`\r\n #REPLACESQL\r\nGROUP BY s3.`NAME`) k		\r\nGROUP BY name\r\n');
INSERT INTO `e_bi_history` VALUES (83, 'select SUM(unstart) unstart,SUM(progress) progress,SUM(finish) finish,SUM(total) total from\r\n(SELECT\r\n	count(IF( now() < ab_time, TRUE, NULL )) unstart,\r\n	count(IF( now() BETWEEN ab_time AND ae_time, TRUE, NULL )) progress ,\r\n	count(IF( now() > ae_time, TRUE, NULL )) finish,\r\n	count(*) total \r\nFROM\r\n	tb_unload_ship \r\nWHERE org_code = \'#ORGCODE\' and YEAR ( ab_time ) = \'#searchDate\'\r\n	UNION ALL\r\nSELECT \r\n  count( IF ( now() < ab_time, TRUE, NULL )) unstart,\r\n	count( IF ( now() BETWEEN ab_time AND ae_time, TRUE, NULL )) progress ,\r\n	count( IF ( now() > ae_time, TRUE, NULL )) finish ,\r\n	count(*) total \r\nFROM\r\n	tb_unload \r\nWHERE org_code = \'#ORGCODE\' and YEAR ( ab_time ) = \'#searchDate\'\r\n	UNION ALL\r\nSELECT\r\n	count( IF ( now() < star_date, TRUE, NULL )) unstart,\r\n	count( IF ( now() BETWEEN star_date AND end_date, TRUE, NULL )) progress ,\r\n	count( IF ( now() > end_date, TRUE, NULL )) finish ,\r\n	count(*) total \r\nFROM\r\n	tb_maintenance \r\nWHERE org_code = \'#ORGCODE\' and YEAR ( ab_time ) = \'#searchDate\')t\r\n', 1, ' 作业数量统计', 'erupt', '2023-07-25 17:28:27', 'select SUM(unstart) unstart,SUM(progress) progress,SUM(finish) finish,SUM(total) total from\r\n(SELECT\r\n	count(IF( now() < ab_time, TRUE, NULL )) unstart,\r\n	count(IF( now() BETWEEN ab_time AND ae_time, TRUE, NULL )) progress ,\r\n	count(IF( now() > ae_time, TRUE, NULL )) finish,\r\n	count(*) total \r\nFROM\r\n	tb_unload_ship \r\nWHERE org_code = \'#ORGCODE\' \r\n	UNION ALL\r\nSELECT \r\n  count( IF ( now() < ab_time, TRUE, NULL )) unstart,\r\n	count( IF ( now() BETWEEN ab_time AND ae_time, TRUE, NULL )) progress ,\r\n	count( IF ( now() > ae_time, TRUE, NULL )) finish ,\r\n	count(*) total \r\nFROM\r\n	tb_unload \r\nWHERE org_code = \'#ORGCODE\' \r\n	UNION ALL\r\nSELECT\r\n	count( IF ( now() < star_date, TRUE, NULL )) unstart,\r\n	count( IF ( now() BETWEEN star_date AND end_date, TRUE, NULL )) progress ,\r\n	count( IF ( now() > end_date, TRUE, NULL )) finish ,\r\n	count(*) total \r\nFROM\r\n	tb_maintenance \r\nWHERE org_code = \'#ORGCODE\' )t\r\n');
INSERT INTO `e_bi_history` VALUES (84, 'select SUM(unstart) unstart,SUM(progress) progress,SUM(finish) finish,SUM(total) total from\r\n(SELECT\r\n	count(IF( now() < ab_time, TRUE, NULL )) unstart,\r\n	count(IF( now() BETWEEN ab_time AND ae_time, TRUE, NULL )) progress ,\r\n	count(IF( now() > ae_time, TRUE, NULL )) finish,\r\n	count(*) total \r\nFROM\r\n	tb_unload_ship \r\nWHERE org_code = \'#ORGCODE\' and YEAR ( ab_time ) = \'#searchDate\'\r\n	UNION ALL\r\nSELECT \r\n  count( IF ( now() < ab_time, TRUE, NULL )) unstart,\r\n	count( IF ( now() BETWEEN ab_time AND ae_time, TRUE, NULL )) progress ,\r\n	count( IF ( now() > ae_time, TRUE, NULL )) finish ,\r\n	count(*) total \r\nFROM\r\n	tb_unload \r\nWHERE org_code = \'#ORGCODE\' and YEAR ( ab_time ) = \'#searchDate\'\r\n	UNION ALL\r\nSELECT\r\n	count( IF ( now() < star_date, TRUE, NULL )) unstart,\r\n	count( IF ( now() BETWEEN star_date AND end_date, TRUE, NULL )) progress ,\r\n	count( IF ( now() > end_date, TRUE, NULL )) finish ,\r\n	count(*) total \r\nFROM\r\n	tb_maintenance \r\nWHERE org_code = \'#ORGCODE\' and YEAR ( star_date ) = \'#searchDate\')t\r\n', 1, ' 作业数量统计', 'erupt', '2023-07-25 17:34:28', 'select SUM(unstart) unstart,SUM(progress) progress,SUM(finish) finish,SUM(total) total from\r\n(SELECT\r\n	count(IF( now() < ab_time, TRUE, NULL )) unstart,\r\n	count(IF( now() BETWEEN ab_time AND ae_time, TRUE, NULL )) progress ,\r\n	count(IF( now() > ae_time, TRUE, NULL )) finish,\r\n	count(*) total \r\nFROM\r\n	tb_unload_ship \r\nWHERE org_code = \'#ORGCODE\' and YEAR ( ab_time ) = \'#searchDate\'\r\n	UNION ALL\r\nSELECT \r\n  count( IF ( now() < ab_time, TRUE, NULL )) unstart,\r\n	count( IF ( now() BETWEEN ab_time AND ae_time, TRUE, NULL )) progress ,\r\n	count( IF ( now() > ae_time, TRUE, NULL )) finish ,\r\n	count(*) total \r\nFROM\r\n	tb_unload \r\nWHERE org_code = \'#ORGCODE\' and YEAR ( ab_time ) = \'#searchDate\'\r\n	UNION ALL\r\nSELECT\r\n	count( IF ( now() < star_date, TRUE, NULL )) unstart,\r\n	count( IF ( now() BETWEEN star_date AND end_date, TRUE, NULL )) progress ,\r\n	count( IF ( now() > end_date, TRUE, NULL )) finish ,\r\n	count(*) total \r\nFROM\r\n	tb_maintenance \r\nWHERE org_code = \'#ORGCODE\' and YEAR ( ab_time ) = \'#searchDate\')t\r\n');
INSERT INTO `e_bi_history` VALUES (85, 'SELECT\r\n	i.NAME \'作业类型\',\r\n	count( IF ( now() BETWEEN ab_time AND ae_time, TRUE, NULL )) \'现有风险数量\',\r\n	count( IF ( now() > ae_time, TRUE, NULL )) \'历史风险数量\' \r\nFROM\r\n	e_dict d,\r\n	e_dict_item i,\r\n	tb_unload_ship t \r\nWHERE\r\n	d.id = i.erupt_dict_id \r\n	AND d.CODE = \'workTypeShip\' \r\n	AND t.work_type = i.`code` \r\n	AND org_code = \'#ORGCODE\'\r\n	AND YEAR ( t.ab_time ) = \'#searchDate\'\r\n	group by t.work_type,i.name \r\n	union all\r\n	\r\n	\r\n	SELECT\r\n	i.NAME \'作业类型\',\r\n	count( IF ( now() BETWEEN ab_time AND ae_time, TRUE, NULL )) \'现有风险数量\',\r\n	count( IF ( now() > ae_time, TRUE, NULL )) \'历史风险数量\' \r\nFROM\r\n	e_dict d,\r\n	e_dict_item i,\r\n	tb_unload t \r\nWHERE\r\n	d.id = i.erupt_dict_id \r\n	AND d.CODE = \'workType\' \r\n	AND t.work_type = i.`code` \r\n	AND org_code = \'#ORGCODE\'\r\n	AND YEAR ( t.ab_time ) = \'#searchDate\'\r\n	group by t.work_type,i.name \r\n	union all\r\n	\r\n	SELECT\r\n	i.NAME \'作业类型\',\r\n	count( IF ( now() BETWEEN star_date AND end_date, TRUE, NULL )) \'现有风险数量\' ,\r\n	count( IF ( now() > end_date, TRUE, NULL )) \'历史风险数量\' \r\nFROM\r\n	e_dict d,\r\n	e_dict_item i,\r\n	tb_maintenance t \r\nWHERE\r\n	d.id = i.erupt_dict_id \r\n	AND d.CODE = \'MaintenanceType\' \r\n	AND t.type = i.`code` \r\n	AND org_code = \'#ORGCODE\'\r\n	AND YEAR ( t.star_date ) = \'#searchDate\'\r\n	group by t.type,i.name \r\n', 1, '作业风险数量统计', 'erupt', '2023-07-25 17:41:10', 'SELECT\r\n	i.NAME \'作业类型\',\r\n	count( IF ( now() BETWEEN ab_time AND ae_time, TRUE, NULL )) \'现有风险数量\',\r\n	count( IF ( now() > ae_time, TRUE, NULL )) \'历史风险数量\' \r\nFROM\r\n	e_dict d,\r\n	e_dict_item i,\r\n	tb_unload_ship t \r\nWHERE\r\n	d.id = i.erupt_dict_id \r\n	AND d.CODE = \'workTypeShip\' \r\n	AND t.work_type = i.`code` \r\n	AND org_code = \'#ORGCODE\'\r\n	group by t.work_type,i.name union all\r\n	\r\n	\r\n	SELECT\r\n	i.NAME \'作业类型\',\r\n	count( IF ( now() BETWEEN ab_time AND ae_time, TRUE, NULL )) \'现有风险数量\',\r\n	count( IF ( now() > ae_time, TRUE, NULL )) \'历史风险数量\' \r\nFROM\r\n	e_dict d,\r\n	e_dict_item i,\r\n	tb_unload t \r\nWHERE\r\n	d.id = i.erupt_dict_id \r\n	AND d.CODE = \'workType\' \r\n	AND t.work_type = i.`code` \r\n	AND org_code = \'#ORGCODE\'\r\n	group by t.work_type,i.name union all\r\n	\r\n	SELECT\r\n	i.NAME \'作业类型\',\r\n	count( IF ( now() BETWEEN star_date AND end_date, TRUE, NULL )) \'现有风险数量\' ,\r\n	count( IF ( now() > end_date, TRUE, NULL )) \'历史风险数量\' \r\nFROM\r\n	e_dict d,\r\n	e_dict_item i,\r\n	tb_maintenance t \r\nWHERE\r\n	d.id = i.erupt_dict_id \r\n	AND d.CODE = \'MaintenanceType\' \r\n	AND t.type = i.`code` \r\n	AND org_code = \'#ORGCODE\'\r\n	group by t.type,i.name \r\n');

-- ----------------------------
-- Table structure for e_bi_tpl
-- ----------------------------
DROP TABLE IF EXISTS `e_bi_tpl`;
CREATE TABLE `e_bi_tpl`  (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `create_by` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `create_time` datetime NULL DEFAULT NULL,
  `update_by` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '更新人',
  `update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  `code` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '编码',
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '名称',
  `path` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '路径',
  `tpl` varchar(4000) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '模板',
  `type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '资源类型',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `UKkqhrgyi70vv5d5mrv3h4sdwun`(`code` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 14 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '组件模板' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of e_bi_tpl
-- ----------------------------
INSERT INTO `e_bi_tpl` VALUES (1, NULL, NULL, '系统', '2023-05-23 17:27:06', 'VAc60Ym6', '双随机-检查次数', NULL, '\r\n<#list data as d>\r\n<div><span>${d.searchCount}</span></div>\r\n<div>相比上一年 ${d.compare}</div>\r\n</#list>\r\n<style>\r\n  div{font-size:14px; line-height:36px;padding:0 8px}\r\n  span{\r\n    display:block;\r\n    color:#3f51b5;\r\n    font-size:20px;\r\n    font-weight:bold;\r\n  }\r\n</style>', 'online');
INSERT INTO `e_bi_tpl` VALUES (2, NULL, NULL, '系统', '2023-05-30 13:36:53', 'FNWr39NU', '双随机-统计数据截止时间', NULL, '<#list data as d>\r\n<p>统计数据截止：${d.now}</p>\r\n</#list>\r\n<style>\r\n    p{\r\n        font-size:14px;\r\n        color:#333;\r\n        padding:0 5px;\r\n    }\r\n</style>', 'online');
INSERT INTO `e_bi_tpl` VALUES (3, NULL, NULL, '系统', '2023-05-23 16:18:16', 'uUVRioVT', '港区重大风险-企业作业数量统计', NULL, '\r\n<#list data as d>\r\n<div>\r\n    未开始\r\n    <span>${d.unstart}</span>\r\n</div>\r\n<div>\r\n    正在进行中\r\n    <span> ${d.progress}</span>\r\n</div>\r\n<div>\r\n    已结束\r\n    <span> ${d.finish}</span>\r\n</div>\r\n<div>\r\n    作业总数\r\n    <span> ${d.total}</span>\r\n</div>\r\n\r\n   \r\n</#list>\r\n<style>\r\ndiv{\r\n    width:24%;\r\n    display:inline-block;\r\n    font-size:14px;\r\n    text-align:center;\r\n    line-height:44px;\r\n}\r\nspan{\r\n    display:block;\r\n    font-size:36px;\r\n    color:red;\r\n}\r\n</style>', 'online');
INSERT INTO `e_bi_tpl` VALUES (4, NULL, NULL, '系统', '2023-05-23 17:32:24', 'oPmJlWWC', '双随机-检查企业数', NULL, '\r\n<div>\r\n<span>\r\n<#list data as d>\r\n   ${d.searchCount}\r\n</#list>\r\n</span>\r\n</div>\r\n<div>\r\n相比上一年\r\n<#list data as d>\r\n   ${d.compare}\r\n</#list>\r\n</div>\r\n<style>\r\n  div{font-size:14px; line-height:36px;padding:0 8px}\r\n  span{\r\n    display:block;\r\n    color:#3f51b5;\r\n    font-size:20px;\r\n    font-weight:bold;\r\n  }\r\n</style>', 'online');
INSERT INTO `e_bi_tpl` VALUES (5, NULL, NULL, '系统', '2023-05-23 17:32:40', '9lvbRQPy', '双随机-问题个数', NULL, '\r\n<div>\r\n    \r\n        <span>\r\n<#list data as d>\r\n   ${d.totalCount}\r\n</#list>\r\n</span>\r\n\r\n相比上一年\r\n<#list data as d>\r\n   ${d.ratio}%\r\n</#list>\r\n</div>\r\n<style>\r\n  div{font-size:14px; line-height:36px;padding:0 8px;}\r\n  span{\r\n    display:block;\r\n    color:#3f51b5;\r\n    font-size:20px;\r\n    font-weight:bold;\r\n  }\r\n</style>', 'online');
INSERT INTO `e_bi_tpl` VALUES (6, NULL, NULL, '系统', '2023-05-23 17:32:49', 'FCECBWit', '双随机-整改问题数量-政府', NULL, '\r\n<div>\r\n<span>\r\n<#list data as d>\r\n   ${d.finishCount}/${d.totalCount}\r\n</#list>\r\n</span>\r\n</div>\r\n<div>\r\n完成率\r\n<#list data as d>\r\n   ${d.ratio}%\r\n</#list>\r\n</div>\r\n<style>\r\n  div{font-size:14px; line-height:36px;padding:0 8px;}\r\n  span{\r\n    display:block;\r\n    color:#3f51b5;\r\n    font-size:20px;\r\n    font-weight:bold;\r\n  }\r\n</style>', 'online');
INSERT INTO `e_bi_tpl` VALUES (7, NULL, NULL, '系统', '2023-05-23 17:32:55', 'wpiI5DgE', '双随机-逾期问题数', NULL, '\r\n\r\n<div>\r\n    \r\n    <span>\r\n<#list data as d>\r\n   ${d.beOverdueCount} </span>\r\n   逾期占比\r\n   ${d.ratio} %\r\n</#list>\r\n</div>\r\n<style>\r\n  div{font-size:14px; line-height:36px;padding:0 8px;}\r\n  span{\r\n    display:block;\r\n    color:#3f51b5;\r\n    font-size:20px;\r\n    font-weight:bold;\r\n  }\r\n</style>', 'online');
INSERT INTO `e_bi_tpl` VALUES (8, NULL, NULL, '系统', '2023-05-23 17:33:02', 'Q8ySeWC2', '双随机-未整改问题数-企业', NULL, '\r\n<div>\r\n<span>\r\n<#list data as d>\r\n   ${d.unfinishCount}\r\n</#list>\r\n</span>\r\n\r\n未完成率\r\n<#list data as d>\r\n   ${d.ratio}%\r\n</#list>\r\n</div>\r\n<style>\r\n  div{font-size:14px; line-height:36px;padding:0 8px;}\r\n  span{\r\n    display:block;\r\n    color:#3f51b5;\r\n    font-size:20px;\r\n    font-weight:bold;\r\n  }\r\n</style>', 'online');
INSERT INTO `e_bi_tpl` VALUES (9, NULL, NULL, '系统', '2023-05-23 17:33:09', 'raDiMrhS', '双随机-整改问题数-企业', NULL, '\r\n<div>\r\n  \r\n<span>\r\n<#list data as d>\r\n   ${d.finishCount}\r\n</#list>\r\n</span>\r\n\r\n\r\n完成率\r\n<#list data as d>\r\n   ${d.ratio}%\r\n</#list>\r\n</div>\r\n<style>\r\n  div{font-size:14px; line-height:36px;padding:0 8px;}\r\n  span{\r\n    display:block;\r\n    color:#3f51b5;\r\n    font-size:20px;\r\n    font-weight:bold;\r\n  }\r\n</style>', 'online');
INSERT INTO `e_bi_tpl` VALUES (10, NULL, NULL, '系统', '2023-06-07 10:36:41', 'bFQKBipk', '重大风险-风险类型', NULL, '<div style=\"width:100%; position:sticky;top:0;background-color:#FFFFFF;margin-bottom:85px; margin-top:-100px;height:50px\">\r\n<button  onclick=\"exportEvent()\" class=\"button button2\">导出</button>\r\n</div>\r\n<table>\r\n    <tr clss=\"header\">\r\n        <th width=\"50px\" text-align=\"center\">序号</th>\r\n        <th>风险类型</th>\r\n        <th>历史风险数量</th>\r\n        <th>现有风险数量</th>\r\n        \r\n    </tr>\r\n<#list data as d>\r\n    <tr>\r\n        <td class=\"index\">${d_index+1}</td>\r\n        <td class=\"index\">${d[\'风险类型\']}</td>\r\n        <td class=\"index\">${d[\'历史风险数量\']}</td>\r\n        <td class=\"index\">${d[\'现有风险数量\']}</td>\r\n        \r\n    </tr>\r\n</#list>\r\n</table>\r\n<script type=\"text/javascript\">\r\n  const token = JSON.parse(window.localStorage.getItem(\'_token\')).token\r\n  const domain = window.parent.eruptSiteConfig.riderDomain\r\n    function exportEvent(){\r\n        window.open(domain+\'/erupt-api/typeOfRisk?token=\'+token)\r\n    }\r\n</script>\r\n<style>\r\n\r\ntable{\r\n    width: 100%;\r\n    border-collapse:collapse;\r\n}\r\ntable caption{\r\n    margin: 1em 0;\r\n}\r\nth,td{\r\n   border-bottom:0.2px solid #DCDCDC;\r\n   padding: 8px;\r\n   color: rgba(0, 0, 0, 0.65);\r\n   font-size: 14px;\r\n   line-height: 1;\r\n   text-align: center;\r\n}\r\ntable thead tr{\r\n      transition: all 0.3s, height 0s;\r\n     border-bottom: 1px solid #e8e8e8;\r\n}\r\n\r\ntable tfoot tr td{\r\n    text-align: right;\r\n    padding-right: 20px;\r\n}\r\n .index{\r\n          text-align:center;\r\n}\r\n.button {\r\n    font-family: inherit;\r\n    background-color: #595959; /* Green */\r\n    border: none;\r\n    padding: 6px 25px;\r\n    text-align: center;\r\n    text-decoration: none;\r\n    display: inline-block;\r\n    font-size: 14px;\r\n    margin: 0 2px;\r\n    -webkit-transition-duration: 0.4s; /* Safari */\r\n    transition-duration: 0.4s;\r\n    cursor: pointer;\r\n}\r\n.button2 {\r\n    background-color: white; \r\n    border: 1px solid #D9D9D9;\r\n    border-radius:5px;\r\n    color: #595959;\r\n}\r\n \r\n.button2:hover {\r\n     border: 1px solid  #4DB5FF;\r\n     color:#4DB5FF;\r\n}\r\n</style>', 'online');
INSERT INTO `e_bi_tpl` VALUES (11, NULL, NULL, '系统', '2023-06-07 10:38:36', 'nWTTnUKo', '重大风险-作业风险', NULL, '<div style=\"width:100%; position:sticky;top:0;background-color:#FFFFFF;margin-bottom:85px; margin-top:-100px;height:50px\">\r\n<button  onclick=\"exportEvent()\" class=\"button button2\">导出</button>\r\n</div>\r\n<table>\r\n    <tr>\r\n        <th width=\"50px\" text-align=\"center\">序号</th>\r\n        <th>作业类型</th>\r\n        <th>历史风险数量</th>\r\n        <th>现有风险数量</th>\r\n        \r\n        \r\n        \r\n    </tr>\r\n<#list data as d>\r\n    <tr>\r\n        <td class=\"index\">${d_index+1}</td>\r\n        <td class=\"index\">${d[\'作业类型\']}</td>\r\n        <td class=\"index\">${d[\'历史风险数量\']}</td>\r\n        <td class=\"index\">${d[\'现有风险数量\']}</td>\r\n        \r\n        \r\n        \r\n    </tr>\r\n</#list>\r\n</table>\r\n<script type=\"text/javascript\">\r\n  const token = JSON.parse(window.localStorage.getItem(\'_token\')).token\r\n  const domain = window.parent.eruptSiteConfig.riderDomain\r\n    function exportEvent(){\r\n                window.open( domain+\'/erupt-api/operationalRisk?token=\'+token) \r\n    }\r\n</script>\r\n\r\n<style>\r\n\r\ntable{\r\n    width: 100%;\r\n    border-collapse:collapse;\r\n}\r\ntable caption{\r\n    font-weight: bold;\r\n    margin: 1em 0;\r\n}\r\nth,td{\r\n   border-bottom:0.2px solid #DCDCDC;\r\n   padding: 8px;\r\n   color: rgba(0, 0, 0, 0.65);\r\n   font-size: 14px;\r\n   line-height: 1;\r\n   text-align: center;\r\n}\r\ntable thead tr{\r\n      transition: all 0.3s, height 0s;\r\n     border-bottom: 1px solid #e8e8e8;\r\n}\r\n\r\ntable tfoot tr td{\r\n    text-align: right;\r\n    padding-right: 20px;\r\n}\r\n .index{\r\n          text-align:center;\r\n}\r\n.button {\r\n    font-family: inherit;\r\n    background-color: #595959; /* Green */\r\n    border: none;\r\n    padding: 6px 25px;\r\n    text-align: center;\r\n    text-decoration: none;\r\n    display: inline-block;\r\n    font-size: 14px;\r\n    margin: 0 2px;\r\n    -webkit-transition-duration: 0.4s; /* Safari */\r\n    transition-duration: 0.4s;\r\n    cursor: pointer;\r\n}\r\n.button2 {\r\n    background-color: white; \r\n    border: 1px solid #D9D9D9;\r\n    border-radius:5px;\r\n    color: #595959;\r\n}\r\n \r\n.button2:hover {\r\n     border: 1px solid  #4DB5FF;\r\n     color:#4DB5FF;\r\n}\r\n</style>', 'online');
INSERT INTO `e_bi_tpl` VALUES (12, NULL, NULL, '系统', '2023-06-07 10:41:32', 'AmJdN8cK', '重大风险-企业作业', NULL, '<div style=\"width:100%; position:sticky;top:0;background-color:#FFFFFF;margin-bottom:85px; margin-top:-100px;height:50px\">\r\n<button  onclick=\"exportEvent()\" class=\"button button2\">导出</button>\r\n</div>\r\n<table>\r\n    <tr>\r\n        <th width=\"50px\" text-align=\"center\">序号</th>\r\n        <th>企业名称</th>\r\n        <th>作业总数</th>\r\n        <th>已结束作业</th>\r\n        <th>未开始作业</th>\r\n        <th>正在进行中作业</th>\r\n    </tr>\r\n<#list data as d>\r\n    <tr>\r\n        <td class=\"index\">${d_index+1}</td>\r\n        <td class=\"index\">${d[\'企业名称\']}</td>\r\n        <td class=\"index\">${d[\'作业总数\']}</td>\r\n        <td class=\"index\">${d[\'已结束作业\']}</td>\r\n        <td class=\"index\">${d[\'未开始作业\']}</td>\r\n        <td class=\"index\">${d[\'正在进行中作业\']}</td>\r\n    </tr>\r\n</#list>\r\n</table>\r\n<script type=\"text/javascript\">\r\n const token = JSON.parse(window.localStorage.getItem(\'_token\')).token\r\n  const domain = window.parent.eruptSiteConfig.riderDomain\r\n    function exportEvent(){\r\n                window.open(domain+\'/erupt-api/businessOperations?token=\'+token) \r\n    }\r\n</script>\r\n<style>\r\ntable{\r\n    width: 100%;\r\n    border-collapse:collapse;\r\n}\r\nth,td{\r\n border-bottom:0.2px solid #DCDCDC;\r\n padding: 8px;\r\n color: rgba(0, 0, 0, 0.65);\r\nfont-size: 14px;\r\nline-height: 1;\r\ntext-align: center;\r\n}\r\ntable thead tr{\r\n      transition: all 0.3s, height 0s;\r\n     border-bottom: 1px solid #e8e8e8;\r\n}\r\ntable tfoot tr td{text-align: right;padding-right: 20px;}\r\n .index{text-align:center;}\r\n.button {\r\n    font-family: inherit;\r\n    background-color: #595959; /* Green */\r\n    border: none;\r\n    padding: 6px 25px;\r\n    text-align: center;\r\n    text-decoration: none;\r\n    display: inline-block;\r\n    font-size: 14px;\r\n    margin: 0 2px;\r\n    -webkit-transition-duration: 0.4s; /* Safari */\r\n    transition-duration: 0.4s;\r\n    cursor: pointer;\r\n}\r\n.button2 {\r\n    background-color: white; \r\n    border: 1px solid #D9D9D9;\r\n    border-radius:5px;\r\n    color: #595959;\r\n}\r\n \r\n.button2:hover {\r\n     border: 1px solid  #4DB5FF;\r\n     color:#4DB5FF;\r\n}\r\n</style>', 'online');
INSERT INTO `e_bi_tpl` VALUES (13, NULL, NULL, '系统', '2023-06-07 10:46:53', 'lOYY4ava', '重大风险-企业-风险类型统计', NULL, '<table>\r\n    <tr clss=\"header\">\r\n        <th width=\"50px\" text-align=\"center\">序号</th>\r\n        <th>风险类型</th>\r\n        <th>历史风险数量</th>\r\n        <th>现有风险数量</th>\r\n        \r\n    </tr>\r\n<#list data as d>\r\n    <tr>\r\n        <td class=\"index\">${d_index+1}</td>\r\n        <td class=\"index\">${d[\'风险类型\']}</td>\r\n        <td class=\"index\">${d[\'历史风险数量\']}</td>\r\n        <td class=\"index\">${d[\'现有风险数量\']}</td>\r\n        \r\n    </tr>\r\n</#list>\r\n</table>\r\n\r\n<style>\r\n\r\ntable{\r\n    width: 100%;\r\n    border-collapse:collapse;\r\n}\r\ntable caption{\r\n    margin: 1em 0;\r\n}\r\nth,td{\r\n   border-bottom:0.2px solid #DCDCDC;\r\n   padding: 8px;\r\n   color: rgba(0, 0, 0, 0.65);\r\n   font-size: 14px;\r\n   line-height: 1;\r\n   text-align: center;\r\n}\r\ntable thead tr{\r\n      transition: all 0.3s, height 0s;\r\n     border-bottom: 1px solid #e8e8e8;\r\n}\r\n\r\ntable tfoot tr td{\r\n    text-align: right;\r\n    padding-right: 20px;\r\n}\r\n .index{\r\n          text-align:center;\r\n}\r\n.button {\r\n    font-family: inherit;\r\n    background-color: #595959; /* Green */\r\n    border: none;\r\n    padding: 6px 25px;\r\n    text-align: center;\r\n    text-decoration: none;\r\n    display: inline-block;\r\n    font-size: 14px;\r\n    margin: 0 2px;\r\n    -webkit-transition-duration: 0.4s; /* Safari */\r\n    transition-duration: 0.4s;\r\n    cursor: pointer;\r\n}\r\n.button2 {\r\n    background-color: white; \r\n    border: 1px solid #D9D9D9;\r\n    border-radius:5px;\r\n    color: #595959;\r\n}\r\n \r\n.button2:hover {\r\n     border: 1px solid  #4DB5FF;\r\n     color:#4DB5FF;\r\n}\r\n</style>', 'online');

SET FOREIGN_KEY_CHECKS = 1;
