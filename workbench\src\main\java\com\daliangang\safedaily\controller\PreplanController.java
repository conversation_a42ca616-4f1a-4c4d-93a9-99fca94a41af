package com.daliangang.safedaily.controller;

import com.daliangang.emergency.form.DrillForm;
import com.daliangang.safedaily.sql.PreplanSql;
import com.daliangang.workbench.form.EmployeeCertificateForm;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import xyz.erupt.core.annotation.EruptRouter;
import xyz.erupt.core.module.MetaUserinfo;
import xyz.erupt.core.view.EruptApiModel;
import xyz.erupt.toolkit.remote.RemoteProxyService;
import xyz.erupt.toolkit.utils.EruptDaoUtils;
import xyz.erupt.toolkit.utils.SqlUtils;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @since :2023/4/24:15:44
 */
@RestController
public class PreplanController {
    @Resource
    private RemoteProxyService remoteProxyService;
    @Resource
    private PreplanSql preplanSql;

    //
    @RequestMapping("erupt-api/get/selectPreplanNum")
    @EruptRouter(verifyType = EruptRouter.VerifyType.LOGIN)
    public EruptApiModel selectPreplanNum() {
        MetaUserinfo remoteUserInfo = remoteProxyService.getRemoteUserInfo();
        String orgCode = SqlUtils.wrapIn(remoteUserInfo.getAuth());
        String sql = preplanSql.selectPreplanNum(orgCode);
        EmployeeCertificateForm employeeCertificateForm = EruptDaoUtils.selectOne(sql, EmployeeCertificateForm.class);
        return EruptApiModel.successApi(employeeCertificateForm);
    }
}
