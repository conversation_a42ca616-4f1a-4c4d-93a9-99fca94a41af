/*
 * Copyright © 2020-2035 yundingyun All rights reserved.
 */
package com.daliangang.workbench.entity;

import com.daliangang.workbench.proxy.EmployeeInformationDataProxy;
import io.swagger.annotations.ApiModel;
import lombok.Getter;
import lombok.Setter;
import xyz.erupt.annotation.Erupt;
import xyz.erupt.annotation.sub_erupt.Power;
import xyz.erupt.toolkit.db.Comment;

import javax.persistence.Entity;
import javax.persistence.Table;

@Erupt(name = "员工管理-检查人员", closeTreeView = true, authVerify = false, power = @Power(importable = true, powerHandler = EmployeeView.class),
        dataProxy = EmployeeInformationDataProxy.class
        , rowOperation = {
//        @RowOperation(confirm = false, title = "启用", icon = "fa fa-toggle-off", ifExpr = "item.state=='禁用'", ifExprBehavior = RowOperation.IfExprBehavior.HIDE, operationHandler = EmployeeInformationEnableHandler.class, operationParam = "on", mode = RowOperation.Mode.MULTI),
//        @RowOperation(confirm = false, title = "禁用", icon = "fa fa-toggle-on", ifExpr = "item.state=='启用'", ifExprBehavior = RowOperation.IfExprBehavior.HIDE, operationHandler = EmployeeInformationEnableHandler.class, operationParam = "off", mode = RowOperation.Mode.MULTI),
//        @RowOperation(title = "重置密码", icon = "fa fa-key", operationHandler = EmployeeInformationResetPasswordHandler.class, mode = RowOperation.Mode.SINGLE),
})
@Table(name = "tb_employee_information")
@Entity
@Getter
@Setter
@Comment("员工管理-检查人员")
@ApiModel("员工管理-检查人员")
public class EmployeeCheckPerson extends EmployeeView {

//    @EruptField(
//            edit = @Edit(title = "员工证书", type = EditType.TAB_TABLE_ADD
//                    /*showBy = @ShowBy(dependField = "checkPerson", expr = "value==false")*/))
//    @OneToMany(cascade = CascadeType.ALL, orphanRemoval = true)
//    @OrderBy
//    @JoinColumn(name = "employee_information_id")
//    @Comment("员工证书")
//    @ApiModelProperty("员工证书")
//    private Set<EmployeeCertificate> certificates;

}
