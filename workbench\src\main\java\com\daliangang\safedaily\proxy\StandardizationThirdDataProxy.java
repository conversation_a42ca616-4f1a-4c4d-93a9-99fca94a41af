/*
 * Copyright © 2020-2035 yundingyun All rights reserved.
 */
package com.daliangang.safedaily.proxy;

import com.daliangang.core.DaliangangContext;
import com.daliangang.safedaily.entity.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import xyz.erupt.annotation.fun.DataProxy;
import xyz.erupt.annotation.query.Condition;
import xyz.erupt.toolkit.utils.AssertUtils;
import xyz.erupt.toolkit.utils.EruptDaoUtils;
import xyz.erupt.toolkit.utils.NotifyUtils;

import javax.transaction.Transactional;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Service
@Slf4j
public class StandardizationThirdDataProxy implements DataProxy<StandardizationThird> {

 /*   @Override
    public void addBehavior(StandardizationThird standardization) {
//        List<StandardizationScoreThird> scores = new ArrayList<>();
//        List<StandardizationItem> items = EruptDaoUtils.selectOnes("select * from tb_standardization_item order by series_id", StandardizationItem.class);
//        for (StandardizationItem item : items) {
//            StandardizationScoreThird score = EruptDaoUtils.cast(item, StandardizationScoreThird.class);
//            score.setScoreStandard(new BigDecimal(0));
//            score.setScore(new BigDecimal(0));
//            score.setId((long) item.getSeriesId());
//            scores.add(score);
//        }
//        standardization.setScores(scores);
    }*/

    @Override
    @Transactional
    public void beforeAdd(StandardizationThird standardization) {
        validNumber(standardization);
        standardization.setSubmitted(false);
    }

    @Override
    @Transactional
    public void beforeUpdate(StandardizationThird standardization) {
        standardization.getScores().forEach(v->{
            v.setStandardizationThird(standardization);
        });
        validNumber(standardization);
        /*if (standardization.getSubmitted()) {
            NotifyUtils.showErrorMsg("报告单已经上报，请勿修改！");
        }*/
    }

    @Override
    public String beforeFetch(List<Condition> conditions) {
        //只看比第一年晚两年的数据
        Calendar calendar = Calendar.getInstance();
        String year = String.valueOf(calendar.get(Calendar.YEAR));
        return " year <= " + year;
        //if (DaliangangContext.isDepartmentUser()) return "submitted=true";
    }

    /*@Override
    public void beforeDelete(StandardizationThird standardization) {
        if (standardization.getSubmitted()) {
            NotifyUtils.showErrorMsg("报告单已经上报，请勿删除！");
        }
    }*/

//    @Override
//    public void afterFetch(Collection<Map<String, Object>> list) {
//        Calendar calendar=Calendar.getInstance();
//        List<Standardization> standardizations = EruptDaoUtils.convert(list, Standardization.class);
//        for (Standardization standardization : standardizations) {
//            Date evaluationTime = standardization.getEffectiveDate();
//            Date now = new Date(System.currentTimeMillis());
//            calendar.setTime(evaluationTime);
//            calendar.add(Calendar.DATE,-30);
//            java.util.Date beforeMonth = calendar.getTime();
//            //预案状态:正常、即将到期、已逾期
//            if(now.after(evaluationTime)){
//                //已逾期
//                standardization.setStateStr(TplUtils.addColor("已逾期", "red"));
//            }else {
//                //到期前30天为即将逾期，否则为正常
//                if(now.before(beforeMonth)){
//                    standardization.setStateStr(TplUtils.addColor("正常", "green"));
//                }else{
//                    standardization.setStateStr(TplUtils.addColor("即将逾期", "blue"));
//                }
//            }
//            EruptDaoUtils.updateAfterFetch(list, standardization.getId(), "stateStr", standardization.getStateStr());
//        }
//
//    }


    @Transactional
    public void validNumber(StandardizationThird target) {
        //计算总得分
        double scoreGot = 0;
        //应得总分
        double totalScore = 0;
        //判断小数点后2位的数字的正则表达式
        Pattern pattern = Pattern.compile("^(([1-9]{1}\\d*)|([0]{1}))(\\.(\\d){0,2})?$");
        //遍历每个分数
        for (StandardizationScoreThird score : target.getScores()) {
            Matcher match = pattern.matcher(score.getScore().toString());
            AssertUtils.isTrue(match.matches(), score.getStandard() + "需为整数或小数点2位小数");
            //将得分相加
            scoreGot += score.getScore().doubleValue();
            //将选中的应得分相加
            totalScore += score.getScoreStandard().doubleValue();
        }

//        String scoreSql = "select sum(score_standard) as total from tb_standardization_item";
//        EruptDao eruptDao = EruptSpringUtil.getBean(EruptDao.class);
//        Map<String, Object> resultMap = eruptDao.getJdbcTemplate().queryForMap(scoreSql);
//        String strScore = resultMap.get("total") + "";
//        double scoreStandard = StringUtils.isEmpty(strScore) ? 0D : Double.parseDouble(String.valueOf(strScore));

        //总分计算=实际得分/应得分数*1000
        double scoreFinal = totalScore > 0 ? (scoreGot / totalScore) * 1000 : 0;
        log.info("总得分 -> " + scoreGot + ", 应得分数 -> " + totalScore + ", 实际得分 -> " + scoreFinal);
        //AssertUtils.isTrue(scoreFinal <= 200, "总得分不能超过200");
        target.setFinalScore((int)scoreGot);
        target.setGateScore((int) totalScore);
        target.setMostTotalScore(String.format("%.2f", new BigDecimal(scoreFinal)));
    }

    //重写导入方法
//    @Override
//    public void excelImport(Object workbook) {
//        Workbook wb = (Workbook) workbook;
//        Sheet sheet = wb.getSheetAt(0);
//        for (int i = sheet.getFirstRowNum() + 1; i <= sheet.getLastRowNum(); i++) {
//            //获取每行数据
//            Row row = sheet.getRow(i);
//            System.out.println(row);
//            //获取每一列数据
//            //年度
//            double year = row.getCell(0).getNumericCellValue();
//            //企业名称
//            String company = row.getCell(1).getStringCellValue();
//            String companySql="select * from tb_enterprise where name='"+company+"'";
//            Enterprise enterprise = EruptDaoUtils.selectOne(companySql, Enterprise.class);
//            //安全标准化等级
//            String grade = row.getCell(2).getStringCellValue();
//            //发证日期
//            String issueDateStr = row.getCell(3).getCellStyle().getDataFormatString();
//            Date issueDate = strToDate(issueDateStr);
//            //有效期至
//            String effectiveDateStr = row.getCell(4).getCellStyle().getDataFormatString();
//            Date effectiveDate = strToDate(effectiveDateStr);
//            //总得分
//            String finalScore = row.getCell(5).getStringCellValue();
//            //应得总分
//            String gateScoreStr = row.getCell(6).getStringCellValue();
//            Integer gateScore = Integer.valueOf(gateScoreStr);
//            String insertSql=String.format("insert into tb_standardization(year,company,grade,issue_date,effective_date,final_score,gate_score,standard_file,self_evaluation) values(%s,%s,%s,%s,%s,%s,%s,%s,%s)",
//                    year, enterprise.getOrgCode(),grade,issueDate,effectiveDate,finalScore,gateScore,"","");
//            EruptDao eruptDao = EruptDaoUtils.getEruptDao();
//            eruptDao.getJdbcTemplate().execute(insertSql);
//        }
//    }
//
//    public static java.util.Date strToDate(String strDate) {
//        String str = strDate;
//        SimpleDateFormat format = new SimpleDateFormat("yyyy/MM/dd");
//        java.util.Date d = null;
//        try {
//            d = format.parse(str);
//        } catch (Exception e) {
//            e.printStackTrace();
//        }
//        java.util.Date date = new java.util.Date(d.getTime());
//        return date;
//    }
}
