package com.daliangang.workbench.form;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import xyz.erupt.annotation.Erupt;
import xyz.erupt.annotation.EruptField;
import xyz.erupt.annotation.sub_erupt.Power;
import xyz.erupt.annotation.sub_field.Edit;
import xyz.erupt.annotation.sub_field.EditType;
import xyz.erupt.annotation.sub_field.Readonly;
import xyz.erupt.annotation.sub_field.View;
import xyz.erupt.annotation.sub_field.sub_edit.InputType;
import xyz.erupt.jpa.model.BaseModel;
import xyz.erupt.toolkit.db.Comment;

@Erupt(name = "风险分布图", power = @Power(add = false, delete = false, export = false, importable = true, viewDetails = false, edit = false)
        , rowOperation = {})

@Getter
@Setter
@Comment("风险分布图")
@ApiModel("风险分布图")
public class RiskProfileFrom extends BaseModel {

    @EruptField(
            views = @View(title = "总数",show = false),
            edit = @Edit(title = "总数", type = EditType.INPUT,readonly = @Readonly,
                    inputType = @InputType))
    @Comment("总数")
    @ApiModelProperty("总数")
    private String total;

    @EruptField(
            views = @View(title = "过期数",show = false),
            edit = @Edit(title = "过期数", type = EditType.INPUT,readonly = @Readonly,
                    inputType = @InputType))
    @Comment("过期数")
    @ApiModelProperty("过期数")
    private String exceed;

    @EruptField(
            views = @View(title = "统计类型",show = false),
            edit = @Edit(title = "统计类型", type = EditType.INPUT,readonly = @Readonly,
                    inputType = @InputType))
    @Comment("统计类型")
    @ApiModelProperty("统计类型")
    private String code;
}
