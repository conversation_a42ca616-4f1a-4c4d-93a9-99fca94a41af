package com.daliangang.workbench.patch;


import com.daliangang.core.DaliangangContext;
import com.daliangang.device.entity.StorageTank;
import com.daliangang.device.operation.PositionDockingHttpHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import xyz.erupt.devtools.patch.PlatformPatchHandler;
import xyz.erupt.jpa.dao.EruptDao;
import xyz.erupt.toolkit.utils.SqlUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

@Service
@Slf4j
public class DeviceAddPatch implements PlatformPatchHandler {

    @Override
    public String getPatchId() {
        return "1.11.7.6-patch-01";
    }

    @Override
    public String getName() {
        return "需要推到风控的数据,有经纬度";
    }

    @Override
    public String getDesc() {
        return "1.查询未推送过去的数据<br>2.查询调用风控位置新增接口<br>3.目前只有储罐才需要用该补丁";
    }

    @Resource
    private EruptDao eruptDao;

    @Resource
    private PositionDockingHttpHandler positionDockingHttpHandler;

    @Override
    public boolean execute() {
        ArrayList<Long> ids = new ArrayList<>();
        ids.add(345L);
        ids.add(346L);
        ids.add(347L);
        ids.add(348L);
        ids.add(349L);
        ids.add(376L);
        ids.add(379L);
        ids.add(382L);
        ids.add(384L);
        ids.add(385L);
        ids.add(386L);
        ids.add(387L);
        ids.add(388L);

        List<StorageTank> storageTanks = eruptDao.queryEntityList(StorageTank.class, "id " + SqlUtils.wrapIn(ids));
        for (StorageTank storageTank : storageTanks) {
            // 推送系统x新增的数据
            String token = DaliangangContext.getToken();
            positionDockingHttpHandler.AddHttpDocking(storageTank, "1", storageTank.getOrgCode(),token);
        }
        return true;
    }
}
