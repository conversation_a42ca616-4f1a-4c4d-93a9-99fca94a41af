package com.daliangang.safedaily.entity;

import com.daliangang.rndpub.proxy.AuditcertificateDataProxy;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import xyz.erupt.annotation.Erupt;
import xyz.erupt.annotation.EruptField;
import xyz.erupt.annotation.sub_erupt.Power;
import xyz.erupt.annotation.sub_field.Edit;
import xyz.erupt.annotation.sub_field.EditType;
import xyz.erupt.annotation.sub_field.View;
import xyz.erupt.annotation.sub_field.sub_edit.ChoiceType;
import xyz.erupt.annotation.sub_field.sub_edit.DateType;
import xyz.erupt.annotation.sub_field.sub_edit.InputType;
import xyz.erupt.annotation.sub_field.sub_edit.Search;
import xyz.erupt.toolkit.db.Comment;
import xyz.erupt.toolkit.handler.EruptDictCodeChoiceFetchHandler;
import xyz.erupt.upms.model.auth.DataAuthModel;
import xyz.erupt.upms.model.auth.OrgCodeRender;

import javax.persistence.Entity;
import javax.persistence.Table;

/**
 * @Author: zengwensi
 * @Description:
 * @Date: Created in 2023/3/16 14:11
 * @Modified By
 */
@Erupt(name = "危险源信息", power = @Power(add = true, delete = true, export = false, importable = true, viewDetails = true, edit = true)
        , dataProxy = AuditcertificateDataProxy.class
        , rowOperation = {})
@Table(name = "tb_preplan_data")
@Entity
@Getter
@Setter
@Comment("审核资质证书")
@ApiModel("审核资质证书")
public class PrePlanDangerSource extends DataAuthModel {

    @EruptField(
            views = @View(title = "重大危险源名称"),
            edit = @Edit(title = "重大危险源名称", type = EditType.INPUT,notNull = true,
                    inputType = @InputType))
    @Comment("重大危险源名称")
    @ApiModelProperty("重大危险源名称")
    private String dangerName;

    @EruptField(
            views = @View(title = "重大危险源等级"),
            edit = @Edit(title = "重大危险源等级", type = EditType.CHOICE, notNull = true,
                    choiceType = @ChoiceType(fetchHandler = EruptDictCodeChoiceFetchHandler.class, fetchHandlerParams = "dangerType")))
    @Comment("重大危险源等级")
    @ApiModelProperty("重大危险源等级")
    private String dangerType;

    @EruptField(
            views = @View(title = "备案时间"),
            edit = @Edit(title = "备案时间", type = EditType.DATE, notNull = true,
                    dateType = @DateType))
    @Comment("备案时间")
    @ApiModelProperty("备案时间")
    private java.util.Date recordTime;




}
