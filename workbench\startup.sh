image=registry.cn-wulanchabu.aliyuncs.com/ydy-lowcode/daliangang-workbench
version=********
app=dlg-workbench
docker stop $app
docker rm $app
docker rmi $image:$version
docker login --username=candycloud registry.cn-wulanchabu.aliyuncs.com --password=1234@abcD
docker run -d --name=$app \
-p 8080:8080 \
-p 4000:4000 \
-e MYSQL_HOST=************* \
-e MYSQL_PORT=30036 \
-e REDIS_HOST=************* \
-e REDIS_PWD=1234@abcD \
-e REDIS_PORT=30079 \
-e ERUPT_DOMAIN=http://************:8080 \
-e ERUPT_TITLE=大连港-外网测试站 \
-e ERUPT_FILE_DOMAIN=http://************:30900/test \
$image:$version
