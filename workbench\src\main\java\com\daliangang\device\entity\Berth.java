/*
 * Copyright © 2020-2035 yundingyun All rights reserved.
 */
package com.daliangang.device.entity;

import com.daliangang.core.CompanyRenderHandler;
import com.daliangang.device.proxy.BerthDataProxy;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import xyz.erupt.annotation.Erupt;
import xyz.erupt.annotation.EruptField;
import xyz.erupt.annotation.expr.ExprBool;
import xyz.erupt.annotation.sub_erupt.Power;
import xyz.erupt.annotation.sub_field.Edit;
import xyz.erupt.annotation.sub_field.EditType;
import xyz.erupt.annotation.sub_field.Readonly;
import xyz.erupt.annotation.sub_field.View;
import xyz.erupt.annotation.sub_field.sub_edit.ChoiceType;
import xyz.erupt.annotation.sub_field.sub_edit.InputType;
import xyz.erupt.annotation.sub_field.sub_edit.Search;
import xyz.erupt.toolkit.db.Comment;
import xyz.erupt.toolkit.handler.RemoteCallChoiceFetchHandler;
import xyz.erupt.toolkit.handler.RemoteEntityChoiceFetchHandler;
import xyz.erupt.toolkit.handler.SqlChoiceFetchHandler;
import xyz.erupt.upms.model.auth.DataAuthHandler;
import xyz.erupt.upms.model.auth.DataAuthModel;

import javax.persistence.Entity;
import javax.persistence.Lob;
import javax.persistence.Table;

@Erupt(name = "泊位管理", importTruncate = true,
        power = @Power(add = true, delete = true, export = false, importable = true, viewDetails = true, edit = true)
        , dataProxy = BerthDataProxy.class
        , rowOperation = {})
@Table(name = "tb_berth")
@Entity
@Getter
@Setter
@Comment("泊位管理")
@ApiModel("泊位管理")
public class Berth extends DataAuthModel {

    @EruptField(
            views = @View(title = "企业名称",width = "250px"),
            edit = @Edit(title = "企业名称",search = @Search(ifRender = @ExprBool(exprHandler = CompanyRenderHandler.class) ), type = EditType.CHOICE, notNull = true, readonly = @Readonly(exprHandler = DataAuthHandler.class),
                    choiceType = @ChoiceType(fullSpan = true,fetchHandler = RemoteEntityChoiceFetchHandler.class, fetchHandlerParams = {"main", "Enterprise", "org_code,name"})))
    @Comment("企业名称")
    @ApiModelProperty("企业名称")
    private String company;


    @EruptField(
            views = @View(title = "泊位名称",width = "150px"),
            edit = @Edit(title = "泊位名称", type = EditType.INPUT, search = @Search(vague = true), notNull = true,
                    inputType = @InputType))
    @Comment("泊位名称")
    @ApiModelProperty("泊位名称")
    private String name;

//    @EruptField(
//            views = @View(title = "所属码头"),
//            edit = @Edit(title = "所属码头", type = EditType.REFERENCE_TREE,  notNull = true,
//                    referenceTreeType = @ReferenceTreeType(type = ReferenceTreeType.Type.CHOICE, dependField = "company", dependColumn = "company"),
//                    choiceType = @ChoiceType(fetchHandler = SqlChoiceFetchHandler.class, fetchHandlerParams = {" select id as code,name as name from tb_wharf ", "5000", "and name is not null"})))
//    @Comment("所属码头")
//    @ApiModelProperty("所属码头")
//    private String wharf;
    @EruptField(
            views = @View(title = "所属码头",width = "100px"),
            edit = @Edit(title = "所属码头",
                    search = @Search,
                    type = EditType.CHOICE,  notNull = true,
                    choiceType = @ChoiceType(fetchHandler = RemoteCallChoiceFetchHandler.class, fetchHandlerParams = {"main", "erupt-api/get/wharfName"})))
    @Comment("所属码头")
    @ApiModelProperty("所属码头")
    private String wharf;

//    @EruptField(
//            views = @View(title = "所属码头",show = false),
//            edit = @Edit(title = "所属码头", type = EditType.CHOICE, search = @Search, notNull = false, show = false,
//                    choiceType = @ChoiceType(fetchHandler = RemoteCallChoiceFetchHandler.class, fetchHandlerParams = {"main", "erupt-api/get/wharfName"})))
//    @Transient
//    private String wharf1;

//	@EruptField(
//		views = @View(title = "所属港区"),
//		edit = @Edit(title = "所属港区", type = EditType.CHOICE, search = @Search(vague = true), notNull = true,
//		choiceType = @ChoiceType(fetchHandler = RemoteCallChoiceFetchHandler.class, fetchHandlerParams ={"main","erupt-api/get/portareaName"})))
//
//	@Comment("所属港区")
//	@ApiModelProperty("所属港区")
//	private String portArea;

    @EruptField(
            views = @View(title = "所属港区",width = "100px"),
            edit = @Edit(title = "所属港区", type = EditType.CHOICE, notNull = true, readonly = @Readonly,search = @Search,
                    choiceType = @ChoiceType(fetchHandler = SqlChoiceFetchHandler.class, fetchHandlerParams = {"select id,portarea_name from tb_port_area"})))
    @Comment("所属港区")
    @ApiModelProperty("所属港区")
    private String portArea;

    @EruptField(
            views = @View(title = "长度",width = "150px",show = false),
            edit = @Edit(title = "长度", type = EditType.INPUT,
                    inputType = @InputType))
    @Comment("泊位名称")
    @ApiModelProperty("泊位名称")
    private String length;

    @EruptField(
            views = @View(title = "用途",width = "150px",show = false),
            edit = @Edit(title = "用途", type = EditType.TEXTAREA,
                    inputType = @InputType))
    @Comment("用途")
    @ApiModelProperty("用途")
    @Lob
    private String useBerth;

    @EruptField(
            views = @View(title = "设计靠泊能力",width = "150px",show = false),
            edit = @Edit(title = "设计靠泊能力", type = EditType.TEXTAREA,
                    inputType = @InputType))
    @Comment("设计靠泊能力")
    @ApiModelProperty("设计靠泊能力")
    @Lob
    private String berthingCapacity;


//	@EruptField(
//			views = @View(title = "经纬度范围", show = false),
//			edit = @Edit(title = "经纬度范围", type = EditType.INPUT, show = false, notNull = false,
//					inputType = @InputType))
//	@Comment("经纬度范围")
//	@ApiModelProperty("经纬度范围")
//	private String longitudeAndLatitude;
//
//	@EruptField(
//			views = @View(title = "经度", show = false),
//			edit = @Edit(title = "经度", type = EditType.INPUT, show = false,
//					inputType = @InputType))
//	@Comment("经度")
//	@ApiModelProperty("经度")
//	private String longitude;
//
//	@EruptField(
//			views = @View(title = "纬度", show = false),
//			edit = @Edit(title = "纬度", type = EditType.INPUT, show = false,
//					inputType = @InputType))
//	@Comment("纬度")
//	@ApiModelProperty("纬度")
//	private String latitude;

    @EruptField(
            views = @View(title = "地图", show = false),
            edit = @Edit(title = "地图", type = EditType.MAP, show = true,notNull = true))
    @Comment("地图")
    @ApiModelProperty("地图")
    @Lob
    private String map;

    @EruptField(
            views = @View(title = "三方系统位置id",show = false),
            edit = @Edit(title = "三方系统位置id", type = EditType.INPUT,show = false,
                    inputType = @InputType))
    @Comment("三方系统位置id")
    @ApiModelProperty("三方系统位置id")
    private String positionId;

}
