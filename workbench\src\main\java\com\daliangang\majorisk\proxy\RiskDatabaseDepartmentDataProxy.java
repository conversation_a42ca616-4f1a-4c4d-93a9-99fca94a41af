/*
 * Copyright © 2020-2035 yundingyun All rights reserved.
 */
package com.daliangang.majorisk.proxy;

import com.daliangang.core.DaliangangContext;
import com.daliangang.majorisk.entity.RiskDatabaseCompany;
import com.daliangang.majorisk.entity.RiskDatabaseDepartment;
import org.springframework.stereotype.Service;
import xyz.erupt.annotation.fun.DataProxy;
import xyz.erupt.annotation.query.Condition;

import javax.transaction.Transactional;
import java.util.List;

@Service
public class RiskDatabaseDepartmentDataProxy implements DataProxy<RiskDatabaseDepartment> {
    @Override
    public void addBehavior(RiskDatabaseDepartment database) {
        database.setState(false);
    }

    @Override
    public String beforeFetch(List<Condition> conditions) {
        if (DaliangangContext.isDepartmentUser()) return null;
        return "state=true";
    }

    @Override
    @Transactional
    public void excelImport(Object workbook) {
//        EruptDaoUtils.truncate(RiskDatabaseDepartment.class);
    }
}
