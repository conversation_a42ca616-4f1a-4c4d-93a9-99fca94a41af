/*
 * Copyright © 2020-2035 yundingyun All rights reserved.
 */
package com.daliangang.rndpub.entity;

import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import xyz.erupt.annotation.EruptField;
import xyz.erupt.annotation.expr.ExprBool;
import xyz.erupt.annotation.sub_field.Edit;
import xyz.erupt.annotation.sub_field.EditType;
import xyz.erupt.annotation.sub_field.Readonly;
import xyz.erupt.annotation.sub_field.View;
import xyz.erupt.annotation.sub_field.sub_edit.*;
import xyz.erupt.core.util.EruptSpringUtil;
import xyz.erupt.toolkit.db.Comment;
import xyz.erupt.toolkit.handler.EruptDictCodeChoiceFetchHandler;
import xyz.erupt.toolkit.handler.RemoteCallChoiceFetchHandler;
import xyz.erupt.toolkit.handler.RemoteEntityChoiceFetchHandler;
import xyz.erupt.toolkit.handler.SqlChoiceFetchHandler;
import xyz.erupt.upms.model.EruptMenu;
import xyz.erupt.upms.model.auth.DataAuthHandler;
import xyz.erupt.upms.model.auth.DataAuthModel;
import xyz.erupt.upms.service.EruptContextService;

import javax.persistence.Lob;
import javax.persistence.MappedSuperclass;
import javax.persistence.Transient;
import java.util.Date;

@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@MappedSuperclass
public class Inspection extends DataAuthModel implements ExprBool.ExprHandler {

    @EruptField(
            views = @View(title = "检查对象",show = false),
            edit = @Edit(title = "检查对象", type = EditType.CHOICE, notNull = false, readonly = @Readonly(exprHandler = DataAuthHandler.class),
                    show = false,
                    search = @Search,
                    choiceType = @ChoiceType(fullSpan = true,reload = true,fetchHandler = RemoteEntityChoiceFetchHandler.class, fetchHandlerParams = {"main", "Enterprise", "org_code,name"})))
    @Transient
    private String company;


    @EruptField(
            views = @View(title = "检查名称"),
            edit = @Edit(title = "检查名称", type = EditType.CHOICE, notNull = true,
                    choiceType = @ChoiceType(anewFetch = true ,fetchHandler = RemoteCallChoiceFetchHandler.class, fetchHandlerParams ={"main","erupt-api/get/getInspectionName"})
            ))
    @Comment("检查名称")
    @ApiModelProperty("检查名称")
    private String inspectionName;

    @EruptField(
            views = @View(title = "检查对象"),
            edit = @Edit(title = "检查对象", type = EditType.REFERENCE_TREE, notNull = true,
                    referenceTreeType = @ReferenceTreeType(type = ReferenceTreeType.Type.CHOICE, dependField = "inspectionName", dependColumn = "procedure_id"),
                    choiceType = @ChoiceType(fetchHandler = SqlChoiceFetchHandler.class,
                            fetchHandlerParams = {"select org_code,enterprise_name from tb_enterprise_information", "5000", "and state=1"})))
    @Comment("检查对象")
    @ApiModelProperty("检查对象")
    private String checkObject;

    @EruptField(
            views = @View(title = "检查事项"),
            edit = @Edit(title = "检查事项", type = EditType.REFERENCE_TREE, notNull = true,
                    referenceTreeType = @ReferenceTreeType(type = ReferenceTreeType.Type.CHOICE,dependField = "",dependColumn = ""),
                    choiceType = @ChoiceType(fetchHandler = SqlChoiceFetchHandler.class,
                            fetchHandlerParams = {"select distinct inspection_items from tb_inspection_items_management", "5000", "and 1=1"})))
    @Comment("检查事项")
    @ApiModelProperty("检查事项")
    private String inspectionItems;

    @EruptField(
            views = @View(title = "检查内容（一级）"),
            edit = @Edit(title = "检查内容（一级）", type = EditType.REFERENCE_TREE, notNull = true,
                    referenceTreeType = @ReferenceTreeType(type = ReferenceTreeType.Type.CHOICE, dependField = "inspectionItems", dependColumn = "inspection_items"),
                    choiceType = @ChoiceType(fetchHandler = SqlChoiceFetchHandler.class,
                            fetchHandlerParams = {"select distinct check_first from tb_inspection_items_management", "5000", "and 1=1"})))
	@Comment("检查内容（一级）")
	@ApiModelProperty("检查内容（一级）")
	private String inspectionContent;

    @EruptField(
            views = @View(title = "问题描述"),
            edit = @Edit(title = "问题描述", type = EditType.TEXTAREA,  notNull = true))
    @Comment("问题描述")
    @ApiModelProperty("问题描述")
    private @Lob String problemDescription;


    @EruptField(
            views = @View(title = "检查依据"),
            edit = @Edit(title = "检查依据", type = EditType.TEXTAREA, notNull = true,
                    inputType = @InputType))
    @Comment("检查依据")
    @ApiModelProperty("检查依据")
    @Lob
    private String inspectionBasis;

    @EruptField(
            views = @View(title = "检查依据附件名称", show = false),
            edit = @Edit(title = "检查依据附件名称", type = EditType.ATTACHMENT,
                    attachmentType = @AttachmentType(type = AttachmentType.Type.BASE, maxLimit = 1, fileTypes = {".pdf", ".doc", ".docx", ".xlsx", ".png", ".jpg", ".xls", ".ppt", ".pptx", ".zip", ".rar"})))
    @Comment("检查依据附件名称")
    @ApiModelProperty("检查依据附件名称")
    private String inspectionBasisFile;

    @EruptField(
            views = @View(title = "整改建议"),
            edit = @Edit(title = "整改建议", type = EditType.TEXTAREA, notNull = true,
                    inputType = @InputType))
    @Comment("整改建议")
    @ApiModelProperty("整改建议")
    @Lob
    private String proposal;

    @EruptField(
            views = @View(title = "整改建议附件名称", show = false),
            edit = @Edit(title = "整改建议附件名称", type = EditType.ATTACHMENT,
                    attachmentType = @AttachmentType(type = AttachmentType.Type.BASE, maxLimit = 1, fileTypes = {".pdf", ".doc", ".docx", ".xlsx", ".png", ".jpg", ".xls", ".ppt", ".pptx", ".zip", ".rar"})))
    @Comment("整改建议附件名称")
    @ApiModelProperty("整改建议附件名称")
    private String proposalFile;

    @EruptField(
            views = @View(title = "整改结果", show = true),
            edit = @Edit(title = "整改结果", type = EditType.CHOICE, notNull = false, show = false,search = @Search,
                    choiceType = @ChoiceType(fetchHandler = EruptDictCodeChoiceFetchHandler.class, fetchHandlerParams = "result")))
    @Comment("整改结果")
    @ApiModelProperty("整改结果")
    private String inspectionResult;

    @EruptField(
            views = @View(title = "整改截止时间（年月日）"),
            edit = @Edit(title = "整改截止时间（年月日）", type = EditType.DATE,notNull = true,
                    dateType = @DateType))
    @Comment("整改截止时间（年月日）")
    @ApiModelProperty("整改截止时间（年月日）")
    private Date deadline;

    @EruptField(
            views = @View(title = "是否逾期", show = true),
            edit = @Edit(title = "是否逾期", type = EditType.BOOLEAN, notNull = false, show = false,search = @Search(vague = true),
                    boolType = @BoolType(trueText = "未逾期", falseText = "已逾期")))
    @Comment("是否逾期")
    @ApiModelProperty("是否逾期")
    private Boolean beOverdue;

    @EruptField(
            views = @View(title = "提交人", ifRender = @ExprBool(exprHandler= Inspection.class)),
            edit = @Edit(title = "提交人", type = EditType.INPUT, notNull = false, show = false,readonly = @Readonly))
    @Comment("提交人")
    @ApiModelProperty("提交人")
    private String submitPerson;

    @EruptField(
            views = @View(title = "复查人姓名", ifRender = @ExprBool(exprHandler= Inspection.class)),
            edit = @Edit(title = "复查人姓名", type = EditType.INPUT, notNull = false, show = false,readonly = @Readonly))
    @Comment("复查人姓名")
    @ApiModelProperty("复查人姓名")
    private String reviewer;

    @EruptField(
            views = @View(title = "发布状态", show = true),
            edit = @Edit(title = "发布状态",search = @Search(vague = true),
                    type = EditType.BOOLEAN,notNull = false, show = false,boolType = @BoolType(trueText = "已发布", falseText = "未发布")))
    @Comment("发布状态")
    @ApiModelProperty("发布状态")
    private Boolean publishStatus;


//    @OneToMany(cascade = CascadeType.ALL, orphanRemoval = true)
//    @JoinColumn(name = "rectifys_id")
//    @EruptField(
//            views = @View(title = "整改内容",show = false),
//            edit = @Edit(
//                    title = "整改内容", type = EditType.TAB_TABLE_ADD, readonly = @Readonly(add = true, edit = true)
//            )
//    )
//    private Set<Rectify> rectifys;
//
//    public Set<Rectify> getRectifysOrDefault(){
//        if(rectifys == null) rectifys = new HashSet<>();
//        return this.rectifys;
//    }


    @Override
    public boolean handler(boolean expr, String[] params) {

        EruptMenu menu = EruptSpringUtil.getBean(EruptContextService.class).getCurrentEruptMenu() ;
        if (menu != null && menu.getValue().equals(InspectionResultsView.class.getSimpleName())) {
            return true;
        }else {
            return false ;
        }
    }
}
