package com.daliangang.datascreen.response;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @Author: chong<PERSON>glin
 * @Date: 2024/11/13 14:06
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder(toBuilder = true)
public class AmountCountResponse {
    private String theme;
    private String derscription;
    private List<amountContent> content;

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @Builder(toBuilder = true)
    public static class amountContent {
        private String title;
        private String count;
        private String unit;
        private String code;
    }
}
