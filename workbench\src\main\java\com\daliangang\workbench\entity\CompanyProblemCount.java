package com.daliangang.workbench.entity;

import com.daliangang.workbench.proxy.CompanyProblemCountProxy;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import xyz.erupt.annotation.Erupt;
import xyz.erupt.annotation.EruptField;
import xyz.erupt.annotation.sub_erupt.Power;
import xyz.erupt.annotation.sub_erupt.RowOperation;
import xyz.erupt.annotation.sub_field.Edit;
import xyz.erupt.annotation.sub_field.EditType;
import xyz.erupt.annotation.sub_field.View;
import xyz.erupt.annotation.sub_field.ViewType;
import xyz.erupt.annotation.sub_field.sub_edit.HtmlType;
import xyz.erupt.jpa.model.BaseModel;
import xyz.erupt.toolkit.db.Comment;
import xyz.erupt.upms.handler.GotoPageHandler;

import javax.persistence.Entity;
import javax.persistence.Lob;
import javax.persistence.Table;

@Erupt(name = "企业问题清单数量",
        power = @Power(add = false,edit = false,delete = false,viewDetails = false),
        dataProxy = {CompanyProblemCountProxy.class},
        rowOperation = {
                @RowOperation(title = "企业问题清单", icon = "fa fa-mail-reply",
                        confirm=false,
                        operationParam="CompanyProblemList",
                        operationHandler = GotoPageHandler.class,
                        mode = RowOperation.Mode.BUTTON),
        })
@Table(name = "tb_company_problem_count")
@Entity
@Getter
@Setter
@Comment("企业问题清单数量")
@ApiModel("企业问题清单数量")

public class CompanyProblemCount extends BaseModel {

    @EruptField(
            views = @View(title = "菜单名称",show = true,width = "30%"),
            edit = @Edit(title = "菜单名称", type = EditType.INPUT, notNull = true))
    @Comment("菜单名称")
    @ApiModelProperty("菜单名称")
    private String menu;

    @Lob
    @EruptField(
            views = @View(title = "未上报或已逾期",width = "60%" ,show = true,type = ViewType.HTML,htmlType = @HtmlType(showIcon = false,isOpenDialog = false)),
            edit = @Edit(title = "未上报或已逾期", type = EditType.INPUT, notNull = true))
    @Comment("未上报或已逾期")
    @ApiModelProperty("未上报或已逾期")
    private String num;
}
