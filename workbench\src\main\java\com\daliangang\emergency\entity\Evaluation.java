/*
 * Copyright © 2020-2035 yundingyun All rights reserved.
 */
package com.daliangang.emergency.entity;

import com.daliangang.emergency.proxy.EvaluationDataProxy;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import org.springframework.stereotype.Service;
import xyz.erupt.annotation.Erupt;
import xyz.erupt.annotation.EruptField;
import xyz.erupt.annotation.fun.OperationHandler;
import xyz.erupt.annotation.sub_erupt.Power;
import xyz.erupt.annotation.sub_erupt.RowOperation;
import xyz.erupt.annotation.sub_field.Edit;
import xyz.erupt.annotation.sub_field.EditType;
import xyz.erupt.annotation.sub_field.View;
import xyz.erupt.annotation.sub_field.sub_edit.ChoiceType;
import xyz.erupt.annotation.sub_field.sub_edit.InputType;
import xyz.erupt.annotation.sub_field.sub_edit.Search;
import xyz.erupt.jpa.dao.EruptDao;
import xyz.erupt.jpa.model.BaseModel;
import xyz.erupt.toolkit.db.Comment;
import xyz.erupt.toolkit.utils.EruptDaoUtils;
import xyz.erupt.toolkit.utils.NotifyUtils;
import xyz.erupt.toolkit.utils.SqlUtils;
import xyz.erupt.upms.dict.Dict;
import xyz.erupt.upms.dict.DictItem;
import xyz.erupt.upms.dict.Dicts;
import xyz.erupt.upms.handler.DictCodeChoiceFetchHandler;
import xyz.erupt.upms.model.EruptDict;
import xyz.erupt.upms.model.EruptDictItem;

import javax.annotation.Resource;
import javax.persistence.Entity;
import javax.persistence.Table;
import javax.transaction.Transactional;
import java.util.List;
import java.util.UUID;

@Erupt(name = "评估表管理", importTruncate = true,
        power = @Power(add = true, delete = true, export = false, importable = true, viewDetails = true, edit = true)
        , dataProxy = EvaluationDataProxy.class
        , rowOperation = {
        @RowOperation(title = "添加主要素", icon = "fa fa-plus-square", eruptClass = Evaluation.PrincipalElementAddForm.class, operationHandler = Evaluation.PrincipalElementAddHandler.class, mode = RowOperation.Mode.BUTTON),
        @RowOperation(title = "删除主要素", icon = "fa fa-minus-square", eruptClass = Evaluation.PrincipalElementDelForm.class, operationHandler = Evaluation.PrincipalElementDelHandler.class, mode = RowOperation.Mode.BUTTON),

})
@Table(name = "tb_evaluation")
@Entity
@Getter
@Setter
@Comment("评估表管理")
@ApiModel("评估表管理")
@Dicts({
        @Dict(code = "PrincipalElement", name = "主要素", value = {
                @DictItem(code = "ORGANIZATION_SYSTEM", value = "组织体制"),
                @DictItem(code = "LAW_BASIS", value = "法律基础"),
                @DictItem(code = "GUARANTEE_SYSTEM", value = "保障系统"),
                @DictItem(code = "RUN_MECHANISM", value = "运行机制")
        })
})
public class Evaluation extends EvaluationCommon {


    @Service
    public static class PrincipalElementAddHandler implements OperationHandler<Evaluation, PrincipalElementAddForm> {

        @Resource
        private EruptDao eruptDao;

        @Override
        @Transactional
        public String exec(List<Evaluation> data, PrincipalElementAddForm form, String[] param) {
            EruptDict dict = eruptDao.queryEntity(EruptDict.class, "code=" + SqlUtils.wrapStr("PrincipalElement"));
            String sql = "select * from e_dict_item where erupt_dict_id=" + dict.getId() + " and name=" + SqlUtils.wrapStr(form.element);
            EruptDictItem item = EruptDaoUtils.selectOne(sql, EruptDictItem.class);
            if (item != null) NotifyUtils.showErrorNotify("该主要素已存在[" + form.element + "]");
            item = new EruptDictItem();
            item.setEruptDict(dict);
            item.setName(form.getElement());
            item.setCode(UUID.randomUUID().toString());
            eruptDao.persist(item);
            return null;
        }
    }

    @Service
    public static class PrincipalElementDelHandler implements OperationHandler<Evaluation, PrincipalElementDelForm> {
        @Resource
        private EruptDao eruptDao;

        @Override
        @Transactional
        public String exec(List<Evaluation> data, PrincipalElementDelForm form, String[] param) {
            EruptDict dict = eruptDao.queryEntity(EruptDict.class, "code=" + SqlUtils.wrapStr("PrincipalElement"));
            String sql = "select * from e_dict_item where erupt_dict_id=" + dict.getId() + " and code=" + SqlUtils.wrapStr(form.element);
            EruptDictItem item = EruptDaoUtils.selectOne(sql, EruptDictItem.class);
            if (item == null) NotifyUtils.showErrorMsg("该主要素不存在[" + form.element + "]");
            sql = "delete from e_dict_item where id=" + item.getId();
            EruptDaoUtils.updateNoForeignKeyChecks(sql);
            return null;
        }
    }

    @Erupt(name = "添加主要素")
    @Data
    public static class PrincipalElementAddForm extends BaseModel {
        @EruptField(
                edit = @Edit(title = "主要素名称", type = EditType.INPUT, notNull = true, inputType = @InputType))
        private String element;
    }

    @Erupt(name = "删除主要素")
    @Data
    public static class PrincipalElementDelForm extends BaseModel {
        @EruptField(
                views = @View(title = "主要素名称"),
                edit = @Edit(title = "选择要删除的主要素", type = EditType.CHOICE, search = @Search, notNull = true,
                        choiceType = @ChoiceType(fetchHandler = DictCodeChoiceFetchHandler.class, fetchHandlerParams = "PrincipalElement")))
        private String element;
    }

}
