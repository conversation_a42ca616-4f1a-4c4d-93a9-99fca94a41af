package com.daliangang.majorisk.entity;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.ObjectUtils;
import xyz.erupt.annotation.Erupt;
import xyz.erupt.annotation.EruptField;
import xyz.erupt.annotation.config.Comment;
import xyz.erupt.annotation.config.EruptSmartSkipSerialize;
import xyz.erupt.annotation.expr.ExprBool;
import xyz.erupt.annotation.fun.DataProxy;
import xyz.erupt.annotation.query.Condition;
import xyz.erupt.annotation.sub_erupt.Power;
import xyz.erupt.annotation.sub_field.Edit;
import xyz.erupt.annotation.sub_field.EditType;
import xyz.erupt.annotation.sub_field.View;
import xyz.erupt.annotation.sub_field.sub_edit.ChoiceType;
import xyz.erupt.annotation.sub_field.sub_edit.InputType;
import xyz.erupt.annotation.sub_field.sub_edit.Search;
import xyz.erupt.core.util.EruptSpringUtil;
import xyz.erupt.toolkit.handler.EruptDictCodeChoiceFetchHandler;
import xyz.erupt.toolkit.utils.EruptCompUtils;
import xyz.erupt.toolkit.utils.EruptDaoUtils;
import xyz.erupt.toolkit.utils.NotifyUtils;
import xyz.erupt.upms.model.EruptOrg;
import xyz.erupt.upms.model.auth.DataAuthHandler;
import xyz.erupt.upms.model.auth.DataAuthModel;
import xyz.erupt.upms.service.EruptUserService;

import javax.persistence.Entity;
import javax.persistence.Lob;
import javax.persistence.Table;
import javax.persistence.Transient;
import java.util.List;

@Erupt(name = "企业风险数据库", power = @Power(add = true, delete = true, export = true, importable = true, viewDetails = true, edit = true),
        dataProxy = RiskDatabaseEnterprise.class,
        rowOperation = {
        })
@Table(name = "tb_risk_database_enterprise")
@Entity
@Getter
@Setter
//@PreDataProxy(DataAuthProxy.class)
public class RiskDatabaseEnterprise extends DataAuthModel implements DataProxy<RiskDatabaseEnterprise> {

    @EruptField(views = @View(title = "风险类型"),
            edit = @Edit(title = "风险类型", type = EditType.CHOICE, search = @Search, notNull = true,
                    choiceType = @ChoiceType(fullSpan = true, fetchHandler = EruptDictCodeChoiceFetchHandler.class, fetchHandlerParams = "riskType",reload = true)))
    //choiceType = @ChoiceType(fetchHandler = SqlChoiceFetchHandler.class, fetchHandlerParams = "select '测试项','测试项'")))
    @xyz.erupt.toolkit.db.Comment("风险类型")
    @ApiModelProperty("风险类型")
    private String riskType;

    @EruptField(views = @View(title = "风险名称"/*, ifRender = @ExprBool(exprHandler = RiskDatabaseCommon.class)*/),
            edit = @Edit(title = "风险名称" /*, ifRender = @ExprBool(exprHandler = RiskDatabaseCommon.class)*/,search = @Search(vague = true), type = EditType.INPUT,inputType = @InputType(fullSpan = true)))
    @xyz.erupt.toolkit.db.Comment("风险名称")
    @ApiModelProperty("风险名称")
    private String riskName;

    @EruptField(views = @View(title = "风险辨识范围"), edit = @Edit(title = "风险辨识范围", type = EditType.TEXTAREA, notNull = true))
    @xyz.erupt.toolkit.db.Comment("风险辨识范围")
    @ApiModelProperty("风险辨识范围")
    private @Lob
    String riskRange;

    @EruptField(views = @View(title = "作业单元"), edit = @Edit(title = "作业单元", type = EditType.TEXTAREA, notNull = true))
    @xyz.erupt.toolkit.db.Comment("作业单元")
    @ApiModelProperty("作业单元")
    private @Lob String work;

    @EruptField(views = @View(title = "风险事件"), edit = @Edit(title = "风险事件", type = EditType.TEXTAREA, notNull = true))
    @xyz.erupt.toolkit.db.Comment("风险事件")
    @ApiModelProperty("风险事件")
    private @Lob String event;

    @EruptField(views = @View(title = "事故类型"), edit = @Edit(title = "事故类型", type = EditType.TEXTAREA, notNull = true))
    @xyz.erupt.toolkit.db.Comment("事故类型")
    @ApiModelProperty("事故类型")
    private @Lob String accidentType;

    @EruptField(views = @View(title = "致险因素", show = false), edit = @Edit(title = "致险因素", type = EditType.DIVIDE,show = true))
    @Transient
    @xyz.erupt.toolkit.db.Comment("致险因素")
    @ApiModelProperty("致险因素")
    private String factorDiv;

    @EruptField(views = @View(title = "致险因素<br>人的因素",show = false), edit = @Edit(title = "人的因素", type = EditType.TEXTAREA, show = true))
    @xyz.erupt.toolkit.db.Comment("致险因素_人的因素")
    @ApiModelProperty("致险因素_人的因素")
    private @Lob String factorPerson;

    @EruptField(views = @View(title = "致险因素<br>设备设施因素",show = false), edit = @Edit(title = "设备设施因素", type = EditType.TEXTAREA, show = true))
    @xyz.erupt.toolkit.db.Comment("致险因素_设备设施因素")
    @ApiModelProperty("致险因素_设备设施因素")
    private @Lob String factorDevice;

    @EruptField(views = @View(title = "致险因素<br>环境因素",show = false), edit = @Edit(title = "环境因素", type = EditType.TEXTAREA, show = true))
    @xyz.erupt.toolkit.db.Comment("致险因素_环境因素")
    @ApiModelProperty("致险因素_环境因素")
    private @Lob String factorEnv;

    @EruptField(views = @View(title = "致险因素<br>管理因素",show = false), edit = @Edit(title = "管理因素", type = EditType.TEXTAREA, show = true))
    @xyz.erupt.toolkit.db.Comment("致险因素_管理因素")
    @ApiModelProperty("致险因素_管理因素")
    private @Lob String factorManage;

    @EruptField(views = @View(title = "风险管控措施", show = false), edit = @Edit(title = "风险管控措施", type = EditType.DIVIDE))
    @Transient
    @xyz.erupt.toolkit.db.Comment("风险管控措施")
    @ApiModelProperty("风险管控措施")
    private String measureDivide;

    @EruptField(views = @View(title = "风险管控措施",show = false), edit = @Edit(title = "风险管控措施", type = EditType.TEXTAREA, notNull = true))
    @xyz.erupt.toolkit.db.Comment("风险管控措施")
    @ApiModelProperty("风险管控措施")
    private @Lob String measure;

    @EruptField(views = @View(title = "变更说明", show = false), edit = @Edit(title = "变更说明", type = EditType.TEXTAREA, notNull = false,show = false))
    @xyz.erupt.toolkit.db.Comment("变更说明")
    @ApiModelProperty("变更说明")
    private @Lob String description;


    @EruptField(
            views = @View(title = "公共风险编号", show = false),
            edit = @Edit(title = "公共风险编号", show = false, type = EditType.INPUT))
    @Comment("公共风险编号")
    @ApiModelProperty("公共风险编号")
    private Long commonRiskId;

    @EruptField(views = @View(title = "组织编码", show = false,
            ifRender = @ExprBool(exprHandler = DataAuthHandler.class)),
            edit = @Edit(title = "组织编码", show = false))
    @EruptSmartSkipSerialize
    private String orgCode;

    @Override
    public void beforeAdd(RiskDatabaseEnterprise riskDatabaseEnterprise) {
        riskDatabaseEnterprise.setRiskName(riskDatabaseEnterprise.getRiskName().trim());
        RiskDatabaseEnterprise riskDatabaseEnterprise1 = EruptDaoUtils.selectOne("select * from tb_risk_database_enterprise where  org_code='" + riskDatabaseEnterprise.getOrgCode() + "' and risk_name ='" + riskDatabaseEnterprise.getRiskName() + "'", RiskDatabaseEnterprise.class);
        if (ObjectUtils.isNotEmpty(riskDatabaseEnterprise1)) {
            NotifyUtils.showErrorMsg("风险名称已存在！");
        }

        EruptUserService eruptUserService = EruptSpringUtil.getBean(EruptUserService.class);
        EruptOrg org = eruptUserService.getCurrentEruptUser().getEruptOrg();
        riskDatabaseEnterprise.setOrgCode(org == null ? null : org.getCode());
    }

    @Override
    public void addBehavior(RiskDatabaseEnterprise riskDatabaseEnterprise) {
        if (ObjectUtils.isNotEmpty(riskDatabaseEnterprise.getRiskType())) {
            String choiceValue = EruptCompUtils.getChoiceValue(riskDatabaseEnterprise.getRiskType());
            riskDatabaseEnterprise.setRiskName(choiceValue);
        }
    }

    @Override
    public String beforeFetch(List<Condition> conditions) {
//        EruptRoleTemplateUserService eruptUserService = EruptSpringUtil.getBean(EruptRoleTemplateUserService.class);
//        EruptRoleTemplateUser roleTemplateUser = eruptUserService.queryByAccount(MetaContext.getUser().getAccount());
//        conditions.add(new Condition("orgCode", roleTemplateUser.initOrgCode(), QueryExpression.EQ));
        return null;
    }
}
