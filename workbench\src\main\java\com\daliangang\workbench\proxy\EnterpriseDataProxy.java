/*
 * Copyright © 2020-2035 yundingyun All rights reserved.
 */
package com.daliangang.workbench.proxy;

import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONObject;
import com.daliangang.core.DaliangangContext;
import com.daliangang.training.models.OrganSyncEntity;
import com.daliangang.workbench.entity.Department;
import com.daliangang.workbench.entity.Enterprise;
import com.daliangang.workbench.form.MapFroms;
import com.daliangang.workbench.init.InitConst;
import com.daliangang.workbench.service.PlatformService;
import com.google.gson.Gson;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import xyz.erupt.annotation.fun.DataProxy;
import xyz.erupt.annotation.query.Condition;
import xyz.erupt.common.http.EruptRemoteUtils;
import xyz.erupt.core.config.GsonFactory;
import xyz.erupt.core.context.MetaContext;
import xyz.erupt.core.util.EruptSpringUtil;
import xyz.erupt.jpa.dao.EruptDao;
import xyz.erupt.toolkit.redismq.RedisMQConst;
import xyz.erupt.toolkit.redismq.RedisMQService;
import xyz.erupt.toolkit.remote.RemoteProxyService;
import xyz.erupt.toolkit.utils.AliTransUtils;
import xyz.erupt.toolkit.utils.EruptDaoUtils;
import xyz.erupt.toolkit.utils.NotifyUtils;
import xyz.erupt.toolkit.utils.SqlUtils;
import xyz.erupt.upms.model.EruptOrg;
import xyz.erupt.upms.model.EruptRole;
import xyz.erupt.upms.model.EruptUser;
import xyz.erupt.upms.model.template.EruptRoleTemplate;
import xyz.erupt.upms.platform.EruptOption;
import xyz.erupt.upms.platform.EruptOptions;
import xyz.erupt.upms.service.EruptPlatformService;
import xyz.erupt.upms.service.EruptUserService;

import javax.annotation.Resource;
import javax.transaction.Transactional;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;

@Service
@Slf4j
@EruptOptions(
        {
                @EruptOption(name = EnterpriseDataProxy.SEARCH_ADDR_TIANDITU, value = "https://api.tianditu.gov.cn/geocoder?ds={\"keyWord\":'{keyword}'}&tk=9698c1ee1cf22d427ec08802e7acd4a0", desc = "天地图根据注册地址获取数据接口，这里的{keyword}一定不能换，代码里写死了"),
        }

)
public class EnterpriseDataProxy implements DataProxy<Enterprise> {

    public static final String SEARCH_ADDR_TIANDITU = "SEARCH_ADDR_TIANDITU";

    @Resource
    private PlatformService platformService;

    @Resource
    private EruptDao eruptDao;

    @Resource
    private EruptUserService eruptUserService;

    @Resource
    private RedisMQService redisMQService;

    @Resource
    private RemoteProxyService remoteProxyService;
    @Resource
    private EruptPlatformService eruptPlatformService;

    @Override
    public Enterprise queryDataById(long id) {
        Enterprise enterprise = eruptDao.queryEntity(Enterprise.class, "id=" + id);
        MetaContext.setAttribute(ENTERPRISE_ORG_CODE, enterprise.getOrgCode());
        return enterprise;
    }

    @Override
    public void addBehavior(Enterprise enterprise) {
        enterprise.setState(true);
        EruptRoleTemplate role = eruptDao.queryEntity(EruptRoleTemplate.class, "base_role='103'");
        enterprise.setRole(role);
        //查找当前用户的主管部门
        EruptUser eruptUser = eruptUserService.getCurrentEruptUser();
        if (eruptUser.getEruptOrg() != null) {
            Department department = eruptDao.queryEntity(Department.class, "erupt_org_id=" + eruptUser.getEruptOrg().getId());
            enterprise.setImDepartment(department);
        }
    }

    public static final String ENTERPRISE_ORG_CODE = "ENTERPRISE_ORG_CODE";

    @Override
    public void editBehavior(Enterprise enterprise) {
        MetaContext.setAttribute(ENTERPRISE_ORG_CODE, enterprise.getOrgCode());
    }

    @Override
    @Transactional
    public void afterAdd(Enterprise enterprise) {
        //同步创建平台用户，企业用户要复制角色
        platformService.afterAdd(StringUtils.isEmpty(enterprise.getAdministrator()) ? AliTransUtils.CN2EN(enterprise.getName()) : enterprise.getAdministrator(),
                enterprise.getName(), enterprise.getInitOrgCode(),enterprise.getPhone(),
                true, this.getEruptOrg(enterprise),
                enterprise.getRole(),
                EruptPlatformService.getInitPassWord(),
                InitConst.HOMEPAGE_ENTERPRISE, false);

        //更新企业自己的orgCode
        EruptOrg org = eruptDao.queryEntity(EruptOrg.class, "name=" + SqlUtils.wrapStr(enterprise.getName()));
        enterprise.setOrgCode(org.getCode());
        if (enterprise.getCertificates() != null) {
            enterprise.getCertificates().forEach(cert -> {
                cert.setCompany(enterprise.getOrgCode());
                eruptDao.merge(cert);

            });

            JSONObject inputData = new JSONObject();
            inputData.set("clazz", "EnterpriseCertificate");
            inputData.set("insertData", enterprise.getCertificates());
            EruptRemoteUtils.doPost(remoteProxyService.getMainServerUrl() + "/erupt-api/exchange/data/exchange", inputData, Map.class);
        }
        eruptDao.merge(enterprise);

        //企业创建成功后，将其加入所属上级的数据权限组织树里
        //查询admin
        String adminSql="select * from e_upms_user where account='admin'";
        EruptUser adminEruptUser = EruptDaoUtils.selectOne(adminSql, EruptUser.class);
        String sql = "select * from e_upms_user where account in (select account from e_upms_role_template_user where erupt_org_id=(select erupt_org_id from tb_department where id=(select im_department_id from tb_enterprise where name='" + enterprise.getName() + "')))";
        List<EruptUser> eruptUsers = EruptDaoUtils.selectOnes(sql, EruptUser.class);
        eruptUsers.add(adminEruptUser);
        List<EruptRole> updateRoles = new ArrayList<>();
        List<EruptUser> updateUsers = new ArrayList<>();
        for (EruptUser eruptUser_ : eruptUsers) {
            EruptUser eruptUser = eruptUserService.getEruptUser(eruptUser_.getId());
            eruptUser.getRoles().forEach(role -> {
                if (role.getCode().equals("101") || //admin是共享角色
                        (!role.getCode().equals("101") && role.getCode().startsWith("10") && role.getCode().contains("."))) {//其他是隔离角色
                    if (!role.getDepts().contains(org)) {
                        role.getDepts().add(org);
                        updateRoles.add(role);
                        updateUsers.add(eruptUser);
                    }
                }
            });
        }

        for (EruptRole role : updateRoles) {
            eruptDao.merge(role);
        }
        for (EruptUser eruptUser : updateUsers) {
            eruptDao.merge(eruptUser);
            eruptUserService.flushEruptUserCache(eruptUser);
        }
    }

    @Transactional
    public void addEnterpriseIntoAdminDepts(Enterprise enterprise) {
        String sql = "select * from e_upms_role where code like '101%' or code like '108%'";
        List<EruptRole> roles = EruptDaoUtils.selectOnes(sql, EruptRole.class);
        EruptOrg org = eruptDao.queryEntity(EruptOrg.class, "code='" + enterprise.getOrgCode() + "'");
        roles.forEach(role_ -> {
            EruptRole role = eruptDao.queryEntity(EruptRole.class, "id=" + role_.getId());
            if (!role.getDepts().contains(org)) {
                role.getDepts().add(org);
                eruptDao.merge(role);
            }
        });
    }

    public EruptOrg getEruptOrg(Enterprise enterprise) {
        Department dept = eruptDao.queryEntity(Department.class, "id=" + enterprise.getImDepartment().getId());
        return EruptSpringUtil.getBean(EruptDao.class).queryEntity(EruptOrg.class, "id=" + dept.getEruptOrgId());
    }

    @Override
    public String beforeFetch(List<Condition> conditions) {
        if (EruptSpringUtil.getBean(EruptUserService.class).getCurrentEruptUser().getIsAdmin()) return null;

//        if (ObjectUtils.isNotEmpty(conditions)) {
//         Condition level = conditions.stream().filter(v -> "portArea".equals(v.getKey())).findAny().orElse(null);
//            String customerManager = "";
//            if  (ObjectUtils.isNotEmpty(level)) {
//                customerManager = level.getValue().toString();
//                Condition customerAuthority = new Condition("", "", QueryExpression.IN);
//                customerAuthority.setKey("portArea");
//                customerAuthority.setValue(level.getValue());
//                conditions.add(customerAuthority);
//                conditions.remove(level);
//            }
//
//        }


        if (DaliangangContext.isEnterpriseUser())
            return "Enterprise.administrator = " + SqlUtils.wrapStr(MetaContext.getUser().getAccount());
        return null;
    }

    @Override
    @Transactional
    public void afterUpdate(Enterprise enterprise) {
        EruptOrg org = eruptDao.queryEntity(EruptOrg.class, "code ='" + enterprise.getOrgCode() + "'");
        platformService.afterUpdate(enterprise.getAdministrator(),
                enterprise.getName(), enterprise.getPhone(),org, enterprise.getRole());

        if (enterprise.getCertificates() != null) {
            enterprise.getCertificates().forEach(cert -> {
                cert.setCompany(enterprise.getOrgCode());
                eruptDao.merge(cert);
            });

            JSONObject inputData = new JSONObject();
            inputData.set("clazz", "EnterpriseCertificate");
            inputData.set("insertData", enterprise.getCertificates());
            EruptRemoteUtils.doPost(remoteProxyService.getMainServerUrl() + "/erupt-api/exchange/data/exchange", inputData, Map.class);
        }


        //同步新增企业机构
        OrganSyncEntity organSync = OrganSyncEntity.builder()
                .orgId(org.getId())
                .orgName(org.getName())
                .orgType(2) //若无组织父级 则为市 否则为区县
                .parentOrgCode(Objects.isNull(org.getParentOrg()) ? null : org.getParentOrg().getCode())
                .orgCode(org.getCode())
                .updateFlag(true)
                .delFlag(false)
                .build();
        redisMQService.produce(RedisMQConst.DEFAULT_TOPIC + "_ORGAN", organSync);
    }

    @Override
    @Transactional
    public void afterDelete(Enterprise enterprise) {


        EruptOrg org = this.getEruptOrg(enterprise);
        //todo 同步新增企业机构
        OrganSyncEntity organSync = OrganSyncEntity.builder()
                .orgId(org.getId())
                .orgName(org.getName())
                .orgType(2) //若无组织父级 则为市 否则为区县
//                .parentOrgId(Objects.isNull(org.getParentOrg())? null : org.getParentOrg().getId())
                .orgCode(org.getCode())
                .updateFlag(true)
                .delFlag(true)
                .build();
        redisMQService.produce(RedisMQConst.DEFAULT_TOPIC + "_ORGAN", organSync);

        platformService.afterDelete(enterprise.getAdministrator());

    }

    @Override
    public void excelImport(Object workbook) {
//        EruptDaoUtils.truncate(Enterprise.class);
    }

    @Override
    public void beforeAdd(Enterprise enterprise) {
        String administrator = enterprise.getAdministrator();
        String selectSql = "select * from tb_enterprise where administrator=" + SqlUtils.wrapStr(administrator);
        Enterprise existEnterprise = EruptDaoUtils.selectOne(selectSql, Enterprise.class);
        if (existEnterprise != null) {
            NotifyUtils.showErrorDialog("该管理员用户名已存在，请重新输入！");
        }
        String result = null;
        try {
            result = district(enterprise.getRegisteredAddress());
            Gson gson = GsonFactory.getGson();
            MapFroms mapFroms = gson.fromJson(result, MapFroms.class);
            enterprise.setMap(gson.toJson(mapFroms));
            enterprise.setState(true);
        } catch (Exception ex) {
            log.error("企业地图信息添加失败 -> " + enterprise.getName() + ", " + enterprise.getRegisteredAddress() + ", " + result);
            //ex.printStackTrace();
        }
    }

    @Override
    public void beforeUpdate(Enterprise enterprise) {
        String administrator = enterprise.getAdministrator();
        String selectSql = "select * from tb_enterprise where administrator=" + SqlUtils.wrapStr(administrator);
        Enterprise existEnterprise = EruptDaoUtils.selectOne(selectSql, Enterprise.class);
        if (existEnterprise != null && !existEnterprise.getId().equals(enterprise.getId())) {
            NotifyUtils.showErrorDialog("该管理员用户名已存在，请重新输入！");
        }
        String result = district(enterprise.getRegisteredAddress());
        try {
            Gson gson = GsonFactory.getGson();
            MapFroms mapFroms = gson.fromJson(result, MapFroms.class);
            enterprise.setMap(gson.toJson(mapFroms));
        } catch (Exception ex) {
            log.error("企业地图信息添加失败 -> " + enterprise.getName() + ", " + enterprise.getRegisteredAddress() + ", " + result);
        }
    }

    public String district(String registeredAddress) {
        eruptDao.getEntityManager().clear();
        String url = eruptPlatformService.getOption(EnterpriseDataProxy.SEARCH_ADDR_TIANDITU).getAsString();
//        String url = "https://api.tianditu.gov.cn/geocoder?ds={\"keyWord\":'" + registeredAddress + "'}&tk=9698c1ee1cf22d427ec08802e7acd4a0";
        if(StringUtils.isNotEmpty(registeredAddress)){
            url = url.replaceAll("\\{keyword}", registeredAddress);
            String result = HttpUtil.get(url);
            return result;
        }
        return "";

    }

    @Override
    public void beforeDelete(Enterprise enterprise) {
        if(enterprise.getId()!=null){
            NotifyUtils.showErrorDialog("该企业不可随意删除，请联系管理员！");
        }
    }
}
