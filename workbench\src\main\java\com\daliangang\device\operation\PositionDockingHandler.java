package com.daliangang.device.operation;

import com.daliangang.device.entity.*;
import com.daliangang.device.form.PositionDockingForm;
import com.google.gson.Gson;
import com.google.gson.JsonObject;
import com.google.gson.internal.LinkedTreeMap;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.stereotype.Service;
import xyz.erupt.core.config.GsonFactory;
import xyz.erupt.core.util.EruptSpringUtil;
import xyz.erupt.jpa.dao.EruptDao;
import xyz.erupt.openapi.core.OpenApi;
import xyz.erupt.openapi.core.OpenApiHandler;
import xyz.erupt.openapi.core.OpenApiModel;
import xyz.erupt.toolkit.db.EruptResultMap;
import xyz.erupt.toolkit.remote.RemoteProxyService;
import xyz.erupt.toolkit.remote.RemoteQuery;
import xyz.erupt.toolkit.utils.EruptDaoUtils;
import xyz.erupt.toolkit.utils.SqlUtils;

import javax.annotation.Resource;
import javax.transaction.Transactional;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @since :2023/5/17:16:42
 */
@Service
@Slf4j
@Transactional
@OpenApi(api = "positionDocking",name = "对接作业地点",handler = PositionDockingHandler.class)
public class PositionDockingHandler implements OpenApiHandler {
    @Resource
    EruptDao eruptDao;
    @Resource
    private RemoteProxyService remoteProxyService;

    @Override

    public OpenApiModel exec(JsonObject request,String params) {

        log.info("接收作业地点原始数据 -> " + request);
        // 数据转换
        Gson gson = GsonFactory.getGson();
        PositionDockingForm positionDockingForm = gson.fromJson(request, PositionDockingForm.class);
        log.info("接收作业地点处理后数据 -> " + positionDockingForm);
       // String listToJsonString = gson.toJson(positionDockingForm.getAddress());
        String listToJsonString = "";
        // 处理风控数据中经纬度数据
        // 判断是否为圆
        if (positionDockingForm.getAddress().get(0).getRy() != 0.0) {
            Map mapY = new HashMap();
            mapY.put("type","circle");
            mapY.put("lng",positionDockingForm.getAddress().get(0).getHt().get(0).getLng());
            mapY.put("lat",positionDockingForm.getAddress().get(0).getHt().get(0).getLat());
            mapY.put("radius",positionDockingForm.getAddress().get(0).getRy());
            listToJsonString = gson.toJson(mapY);
        } else {
            Map mapD = new HashMap();
            mapD.put("type","polygon");
            mapD.put("points",positionDockingForm.getAddress().get(0).getHt());
            listToJsonString = gson.toJson(mapD);
        }

        positionDockingForm.setMap(listToJsonString);
        // 获取关联企业code
        String orgCode ="";
        List<LinkedTreeMap> linkedTreeMaps = remoteProxyService.queryEntity("EnterpriseRelevancy", RemoteQuery.builder().condition("sm_id", positionDockingForm.getCompanyId()));
        if (ObjectUtils.isNotEmpty(linkedTreeMaps)) {
             orgCode = String.valueOf(linkedTreeMaps.get(0).get("orgCode"));
        }

        // 查询关联表数据没有新增对应表，有则按id修改
        PositionDocking positionDocking = eruptDao.queryEntity(PositionDocking.class, "position_id=" + SqlUtils.wrapStr(positionDockingForm.getId()));

        if (ObjectUtils.isEmpty(positionDocking) && positionDockingForm.getDeleteState().equals("0")) {  // 空新增

            switch (positionDockingForm.getType()) {
                case "1":   //储罐
                    // 新增主表
                    StorageTank storageTank = EruptSpringUtil.getBean(StorageTank.class);
                    storageTank.setCompany(orgCode);
                    storageTank.setOrgCode(orgCode);
                    storageTank.setName(positionDockingForm.getName());
                    storageTank.setMap(positionDockingForm.getMap());
                    StorageTank merge = eruptDao.merge(storageTank);
                    // 新增关系表
                    PositionDocking positionDocking1 = EruptSpringUtil.getBean(PositionDocking.class);
                    positionDocking1.setBId(merge.getId());
                   positionDocking1.setPositionType(positionDockingForm.getType());
                  // positionDocking.setPositionName(positionDockingForm.getName());
                   positionDocking1.setPositionId(positionDockingForm.getId());
                 //  positionDocking.setMap(positionDockingForm.getAddress());
                   eruptDao.merge(positionDocking1);

                    break;
                case "2":  //泊位
                    // 新增主表
                    Berth berth = EruptSpringUtil.getBean(Berth.class);
                    berth.setCompany(orgCode);
                    berth.setOrgCode(orgCode);
                    berth.setName(positionDockingForm.getName());
                    berth.setMap(positionDockingForm.getMap());
                    Berth merge1 = eruptDao.merge(berth);
                    // 新增关系表
                    PositionDocking positionDocking2 = EruptSpringUtil.getBean(PositionDocking.class);

                    positionDocking2.setBId(merge1.getId());
                    positionDocking2.setPositionType(positionDockingForm.getType());
                   // positionDocking.setPositionName(positionDockingForm.getName());
                    positionDocking2.setPositionId(positionDockingForm.getId());
                  //  positionDocking.setMap(positionDockingForm.getAddress());
                    eruptDao.merge(positionDocking2);
                    break;
                case "3": // 栈台
                    // 新增主表
                    LoadingDock loadingDock = EruptSpringUtil.getBean(LoadingDock.class);
                    loadingDock.setCompany(orgCode);
                    loadingDock.setOrgCode(orgCode);
                    loadingDock.setName(positionDockingForm.getName());
                    loadingDock.setMap(positionDockingForm.getMap());
                    LoadingDock merge2 = eruptDao.merge(loadingDock);
                    // 新增关系表
                    PositionDocking positionDocking3 = EruptSpringUtil.getBean(PositionDocking.class);
                    positionDocking3.setBId(merge2.getId());
                    positionDocking3.setPositionType(positionDockingForm.getType());
                   // positionDocking.setPositionName(positionDockingForm.getName());
                    positionDocking3.setPositionId(positionDockingForm.getId());
                   // positionDocking.setMap(positionDockingForm.getAddress());
                    eruptDao.merge(positionDocking3);
                    break;
                case "5": // 堆场
                    // 新增主表
                    Yard yard = EruptSpringUtil.getBean(Yard.class);
                    yard.setCompany(orgCode);
                    yard.setOrgCode(orgCode);
                    yard.setName(positionDockingForm.getName());
                    yard.setMap(positionDockingForm.getMap());
                    Yard merge3 = eruptDao.merge(yard);
                    // 新增关系表
                    PositionDocking positionDocking4 = EruptSpringUtil.getBean(PositionDocking.class);

                    positionDocking4.setBId(merge3.getId());
                    positionDocking4.setPositionType(positionDockingForm.getType());
                   // positionDocking.setPositionName(positionDockingForm.getName());
                    positionDocking4.setPositionId(positionDockingForm.getId());
                  //  positionDocking.setMap(positionDockingForm.getAddress());
                    eruptDao.merge(positionDocking4);
                    break;
                default:  // 仓库/棚库
                    // 新增主表
                    Warehouse warehouse = EruptSpringUtil.getBean(Warehouse.class);
                    warehouse.setCompany(orgCode);
                    warehouse.setOrgCode(orgCode);
                    warehouse.setName(positionDockingForm.getName());
                    warehouse.setMap(positionDockingForm.getMap());
                    Warehouse merge4 = eruptDao.merge(warehouse);
                    // 新增关系表
                    PositionDocking positionDocking6 = EruptSpringUtil.getBean(PositionDocking.class);

                    positionDocking6.setBId(merge4.getId());
                    positionDocking6.setPositionType(positionDockingForm.getType());
                   // positionDocking.setPositionName(positionDockingForm.getName());
                    positionDocking6.setPositionId(positionDockingForm.getId());
                  //  positionDocking.setMap(positionDockingForm.getAddress());
                    eruptDao.merge(positionDocking6);

            }

        } else if (ObjectUtils.isNotEmpty(positionDocking) && positionDockingForm.getDeleteState().equals("0")) {  // 修改
            switch (positionDockingForm.getType()) {
                case "1":   //储罐
                    // 修改主表
                    StorageTank storageTank = EruptSpringUtil.getBean(StorageTank.class);
                    storageTank.setId(positionDocking.getBId());
                    storageTank.setCompany(orgCode);
                    storageTank.setOrgCode(orgCode);
                    storageTank.setName(positionDockingForm.getName());
                    storageTank.setMap(positionDockingForm.getMap());
                    StorageTank merge = eruptDao.merge(storageTank);
                    // 修改关系表
                    positionDocking.setPositionType(positionDockingForm.getType());
//                    positionDocking.setPositionName(positionDockingForm.getName());
//                    positionDocking.setMap(positionDockingForm.getAddress());
                    eruptDao.merge(positionDocking);

                    break;
                case "2":  //泊位
                    // 修改主表
                    Berth berth = EruptSpringUtil.getBean(Berth.class);
                    berth.setId(positionDocking.getBId());
                    berth.setCompany(orgCode);
                    berth.setOrgCode(orgCode);
                    berth.setName(positionDockingForm.getName());
                    berth.setMap(positionDockingForm.getMap());
                    Berth merge1 = eruptDao.merge(berth);
                    // 修改关系表

                    positionDocking.setPositionType(positionDockingForm.getType());
//                    positionDocking.setPositionName(positionDockingForm.getName());
//                    positionDocking.setMap(positionDockingForm.getAddress());
                    eruptDao.merge(positionDocking);
                    break;
                case "3": // 栈台
                    // 修改主表
                    LoadingDock loadingDock = EruptSpringUtil.getBean(LoadingDock.class);
                    loadingDock.setId(positionDocking.getBId());
                    loadingDock.setCompany(orgCode);
                    loadingDock.setOrgCode(orgCode);
                    loadingDock.setName(positionDockingForm.getName());
                    loadingDock.setMap(positionDockingForm.getMap());
                    LoadingDock merge2 = eruptDao.merge(loadingDock);
                    // 修改关系表

                    positionDocking.setPositionType(positionDockingForm.getType());
//                    positionDocking.setPositionName(positionDockingForm.getName());
//                    positionDocking.setMap(positionDockingForm.getAddress());
                    eruptDao.merge(positionDocking);
                    break;
                case "5": // 堆场
                    // 修改主表
                    Yard yard = EruptSpringUtil.getBean(Yard.class);
                    yard.setId(positionDocking.getBId());
                    yard.setCompany(orgCode);
                    yard.setOrgCode(orgCode);
                    yard.setName(positionDockingForm.getName());
                    yard.setMap(positionDockingForm.getMap());
                    Yard merge3 = eruptDao.merge(yard);
                    // 修改关系表
                    positionDocking.setPositionType(positionDockingForm.getType());
//                    positionDocking.setPositionName(positionDockingForm.getName());
//                    positionDocking.setMap(positionDockingForm.getAddress());
                    eruptDao.merge(positionDocking);
                    break;
                default:  // 仓库/棚库
                    // 修改主表
                    Warehouse warehouse = EruptSpringUtil.getBean(Warehouse.class);
                    warehouse.setId(positionDocking.getBId());
                    warehouse.setCompany(orgCode);
                    warehouse.setOrgCode(orgCode);
                    warehouse.setName(positionDockingForm.getName());
                    warehouse.setMap(positionDockingForm.getMap());
                    Warehouse merge4 = eruptDao.merge(warehouse);
                    // 修改关系表
                    positionDocking.setPositionType(positionDockingForm.getType());
//                    positionDocking.setPositionName(positionDockingForm.getName());
//                    positionDocking.setMap(positionDockingForm.getAddress());
                    eruptDao.merge(positionDocking);
            }

        } else if (ObjectUtils.isNotEmpty(positionDocking) && positionDockingForm.getDeleteState().equals("1")) {  //删除
            switch (positionDockingForm.getType()) {
                case "1":   //储罐
                    // 删除主表
//                    StorageTank storageTank = EruptSpringUtil.getBean(StorageTank.class);
//                    storageTank.setId(positionDocking.getBId());
                    String sql = "delete from tb_storage_tank where id=" + positionDocking.getBId();
                    EruptDaoUtils.updateNoForeignKeyChecks(sql);
                    // 删除关系表
                    eruptDao.delete(positionDocking);

                    break;
                case "2":  //泊位
                    // 删除主表
//                    Berth berth = EruptSpringUtil.getBean(Berth.class);
//                    berth.setId(positionDocking.getBId());
                    String sql1 = "delete from tb_berth where id=" + positionDocking.getBId();
                    EruptDaoUtils.updateNoForeignKeyChecks(sql1);
                    // 删除关系表
                    eruptDao.delete(positionDocking);
                    break;
                case "3": // 栈台
                    // 删除主表
//                    LoadingDock loadingDock = EruptSpringUtil.getBean(LoadingDock.class);
//                    loadingDock.setId(positionDocking.getBId());
                    String sql2 = "delete from tb_loading_dock where id=" + positionDocking.getBId();
                    EruptDaoUtils.updateNoForeignKeyChecks(sql2);
                    // 删除关系表
                    eruptDao.delete(positionDocking);
                    break;
                case "5": // 堆场
                    // 删除主表
//                    Yard yard = EruptSpringUtil.getBean(Yard.class);
//                    yard.setId(positionDocking.getBId());
                    String sql3 = "delete from tb_yard where id=" + positionDocking.getBId();
                    EruptDaoUtils.updateNoForeignKeyChecks(sql3);
                    // 删除关系表
                    eruptDao.delete(positionDocking);
                    break;
                default:  // 仓库/棚库
                    // 删除主表
                    String sql4 = "delete from tb_warehouse where id=" + positionDocking.getBId();
                    EruptDaoUtils.updateNoForeignKeyChecks(sql4);
                    // 删除关系表
                    eruptDao.delete(positionDocking);
            }
        }
        EruptResultMap vo = new EruptResultMap();
        //vo.put("id", merge.getId());
        vo.put("返回时间", LocalDateTime.now());
        return new OpenApiModel().setData(vo);

    }
}
