/*
* Copyright © 2020-2035 yundingyun All rights reserved.
*/
package com.daliangang.safedaily.operation;

import com.daliangang.safedaily.entity.PlanManagement;
import lombok.Data;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import xyz.erupt.annotation.Erupt;
import xyz.erupt.annotation.fun.OperationHandler;
import xyz.erupt.core.annotation.EruptRouter;
import xyz.erupt.core.util.EruptSpringUtil;
import xyz.erupt.jpa.dao.EruptDao;
import xyz.erupt.toolkit.db.EruptResultMap;
import xyz.erupt.toolkit.utils.EruptDaoUtils;

import javax.annotation.Resource;
import javax.transaction.Transactional;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;

@Service
public class PlanManagementFilingAgainHandler implements OperationHandler<PlanManagement, Void> {


    @Data
//    @SuperBuilder
    @Erupt(name = "再次备案")
    public static class PlanManagementAgain extends PlanManagement {


    }

    @RestController
    @Transactional
    public static class PlanManagementAgainController {
        @RequestMapping("erupt-api/data/PlanManagement/operator/again")
        @EruptRouter(verifyType = EruptRouter.VerifyType.LOGIN)
        public String again(@RequestBody EruptResultMap reuquest) {

            PlanManagement form = (PlanManagement) reuquest.getAs("param", PlanManagement.class);
            return EruptSpringUtil.getBean(PlanManagementFilingAgainHandler.class).exec(Arrays.asList(form), null, null);
        }

        @RequestMapping("/erupt-api/data/PlanManagementAgain/{id}")
        @EruptRouter(verifyType = EruptRouter.VerifyType.LOGIN)
        public PlanManagementFilingAgainHandler.PlanManagementAgain initValue(@PathVariable("id") Long id) {
            PlanManagementAgain plan=  EruptDaoUtils.selectOne("select * from tb_plan_management where id=" + id, PlanManagementAgain.class);
            assert plan != null;
            plan.setCompany(plan.getOrgCode());
            return plan ;
        }
    }

    @Resource
    private EruptDao eruptDao;
   @Override
   @Transactional
   public String exec(List<PlanManagement> data, Void unused, String[] param) {
       for(PlanManagement plan : data){
           PlanManagement planClone = plan.deepClone();
           planClone.setId(null);
           planClone.setSubmitted(false);
           planClone.setCreateTime(LocalDateTime.now());
           planClone.setUpdateBy(null);
           planClone.setUpdateTime(null);
           eruptDao.persist(planClone);
       }
       return null;
	}



}