package com.daliangang.workbench.proxy;

import com.daliangang.core.DaliangangContext;
import com.daliangang.workbench.entity.Enterprise;
import com.daliangang.workbench.entity.EnterpriseCertificateImport;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.stereotype.Service;
import xyz.erupt.annotation.fun.DataProxy;
import xyz.erupt.jpa.dao.EruptDao;
import xyz.erupt.toolkit.utils.AssertUtils;
import xyz.erupt.toolkit.utils.EruptDaoUtils;
import xyz.erupt.toolkit.utils.NotifyUtils;
import xyz.erupt.toolkit.utils.SqlUtils;
import xyz.erupt.upms.model.EruptUser;
import xyz.erupt.upms.service.EruptUserService;

import javax.annotation.Resource;
import java.util.List;
import java.util.StringTokenizer;

@Service
public class EnterpriseCertificateImportDataProxy implements DataProxy<EnterpriseCertificateImport> {

    @Resource
    private EruptUserService eruptUserService;

    @Resource
    private EruptDao eruptDao;

    @Resource
    private EnterpriseCertificateDataProxy enterpriseCertificateDataProxy ;

    @Override
    public void beforeAdd(EnterpriseCertificateImport enterpriseCertificateImport) {
        //获取当前登录的用户
        EruptUser eruptUser = eruptUserService.getCurrentEruptUser();
        if(!DaliangangContext.isDepartmentUser()){
            if(StringUtils.isNotEmpty(enterpriseCertificateImport.getCompany())&&StringUtils.isNotEmpty(eruptUser.getEruptOrg().getCode())&&!enterpriseCertificateImport.getCompany().equals(eruptUser.getEruptOrg().getCode())){
                NotifyUtils.showErrorDialog("编号为【"+enterpriseCertificateImport.getCertificateNo()+"】的企业附征所属企业与当前企业不一致，请核对后重新导入！");
            }
        }

        //附证编号唯一值判断
//        String selectSql="select * from tb_enterprise_certificate where certificate_no="+SqlUtils.wrapStr(enterpriseCertificateImport.getCertificateNo());
//        List<EnterpriseCertificate> certificates = EruptDaoUtils.selectOnes(selectSql, EnterpriseCertificate.class);
//        if(certificates.size()>0){
//            NotifyUtils.showErrorDialog("附证编号【"+enterpriseCertificateImport.getCertificateNo()+"】已存在，请换一个！");
//        }
        //企业用户
        if(DaliangangContext.isDepartmentUser()){
            if ( StringUtils.isEmpty(enterpriseCertificateImport.getCompany())) {
                NotifyUtils.showWarnMsg("企业名称必填");
            }
        }else{
            if ( StringUtils.isEmpty(enterpriseCertificateImport.getCompany())) {
                enterpriseCertificateImport.setCompany(eruptUser.getEruptOrg().getCode());
            }
        }

    }

    @Override
    public void excelImport(Object workbook) {
        Workbook wb = (Workbook) workbook;
        Sheet sheet = wb.getSheetAt(0);
        for (int i = sheet.getFirstRowNum(); i <= sheet.getLastRowNum(); i++) {
            Row row = sheet.getRow(i);
            Cell cell = row.getCell(1);
            AssertUtils.notNull(cell, "附证编号不可为空");
            String certNo = "";
            switch (cell.getCellType()) {
                case STRING:
                    certNo = cell.getStringCellValue();
                    break;
                case NUMERIC:
                    certNo = cell.getNumericCellValue() + "";
                    break;
            }
            if (StringUtils.isEmpty(certNo)) NotifyUtils.showErrorMsg("附证编号不可为空");
//            EnterpriseCertificate certificate = eruptDao.queryEntity(EnterpriseCertificate.class, "certificate_no=" + SqlUtils.wrapStr(certNo));
//            AssertUtils.isNull(certificate, "附证编号已存在 -> " + certNo);
//            EruptUser eruptUser = eruptUserService.getCurrentEruptUser();
//            row.createCell(row.getLastCellNum()+1).setCellValue(eruptUser.getEruptOrg().getCode());
        }
    }

    @Override
    public void afterAdd(EnterpriseCertificateImport enterpriseCertificateImport) {
        String orgCode = enterpriseCertificateImport.getCompany();
        Enterprise enterprise = eruptDao.queryEntity(Enterprise.class, "org_code=" + SqlUtils.wrapStr(orgCode));
        AssertUtils.notNull(enterprise, "没有这个企业 -> " + orgCode);
        String updateSql = "update tb_enterprise_certificate set enterprise_id=" + enterprise.getId() + " where company=" + SqlUtils.wrapStr(orgCode);
        EruptDaoUtils.updateNoForeignKeyChecks(updateSql);
        //同步到一体化平台
        enterpriseCertificateDataProxy.syncEnterpriseCertificateData(enterpriseCertificateImport);
    }

    @Override
    public void afterExcelImport(Object workbook, List list) {
//        list.forEach(obj -> {
//            JsonObject vo = (JsonObject) obj;
//            String orgCode = vo.get("company").getAsString();
//            Enterprise enterprise = eruptDao.queryEntity(Enterprise.class, "org_code=" + SqlUtils.wrapStr(orgCode));
//            AssertUtils.notNull(enterprise, "没有这个企业 -> " + orgCode);
//            String updateSql = "update tb_enterprise_certificate set enterprise_id=" + enterprise.getId() + " where certificate_no=" + SqlUtils.wrapStr(vo.get("certificateNo").getAsString());
//            EruptDaoUtils.updateNoForeignKeyChecks(updateSql);
//        });
    }

    @Override
    public void excelExport(Object workbook) {
        Workbook wb = (Workbook) workbook;
        Sheet sheet = wb.getSheetAt(0);
        Row title = sheet.getRow(0);
        int index = -1;
        for (int i = title.getFirstCellNum(); i <= title.getLastCellNum(); i++) {
            Cell cell = title.getCell(i);
            if (cell.getStringCellValue().equals("状态")) {
                index = i;
                break;
            }
        }
        if (index < 0) return;
        for (int i = 1; i <= sheet.getLastRowNum(); i++) {
            Row row = sheet.getRow(i);
            Cell cell = row.getCell(index);
            String value = cell.getStringCellValue();
            value = value.replace("<span class='e-tag'>", "");
            value = value.replace("</font></span>", "");
            StringTokenizer st = new StringTokenizer(value, "'>");
            st.nextToken();
            st.nextToken();
            value = st.nextToken();
            cell.setCellValue(value);
        }
    }
}
