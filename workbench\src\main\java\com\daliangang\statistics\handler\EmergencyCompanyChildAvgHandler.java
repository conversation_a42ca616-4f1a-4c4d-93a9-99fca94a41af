package com.daliangang.statistics.handler;

import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.stereotype.Component;
import xyz.erupt.bi.fun.EruptBiHandler;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * 企业12个子要素平均值
 * @Author: z<PERSON><PERSON><PERSON>
 * @Description:
 * @Date: Created in 2023/5/10 17:25
 * @Modified By
 */
@Component
public class EmergencyCompanyChildAvgHandler implements EruptBiHandler {

    @Resource
    private CommonHandler commonHandler;
    @Override
    public String exprHandler(String param, Map<String, Object> condition, String expr) {
        //权限
        expr = commonHandler.exprHandlerAuthority(param,condition,expr);

        return expr ;
    }

    @Override
    public void resultHandler(String param, Map<String, Object> condition, List<Map<String, Object>> result) {

    }

    @Override
    public void exportHandler(String param, Map<String, Object> condition, Workbook workbook) {

    }
}
