/*
 * Copyright © 2020-2035 yundingyun All rights reserved.
 */
package com.daliangang.safedaily.proxy;

import com.daliangang.core.DaliangangContext;
import com.daliangang.safedaily.entity.DailyInspection;
import org.springframework.stereotype.Service;
import xyz.erupt.annotation.fun.DataProxy;
import xyz.erupt.annotation.query.Condition;
import xyz.erupt.core.util.DateUtil;

import java.util.Date;
import java.util.List;
import java.util.Map;

@Service
public class DailyInspectionDataProxy implements DataProxy<DailyInspection> {
    @Override
    public void addBehavior(DailyInspection dailyInspection) {
        dailyInspection.setInspectionTime(new Date(System.currentTimeMillis()));
    }

    @Override
    public String beforeFetch(List<Condition> conditions) {
        if (DaliangangContext.isDepartmentUser()) return "submitted=true";
        return null;
    }
    @Override
    public void searchCondition(Map<String, Object> condition) {
        condition.put("inspectionTime", DateUtil.getFormatDate(new java.util.Date(), DateUtil.DATE));
    }
}
