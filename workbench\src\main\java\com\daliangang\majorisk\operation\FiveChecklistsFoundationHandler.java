package com.daliangang.majorisk.operation;

import com.daliangang.majorisk.entity.FiveChecklistsFoundation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import xyz.erupt.annotation.fun.OperationHandler;
import xyz.erupt.core.annotation.EruptRouter;
import xyz.erupt.core.util.EruptSpringUtil;
import xyz.erupt.jpa.dao.EruptDao;
import xyz.erupt.toolkit.db.EruptResultMap;
import xyz.erupt.toolkit.utils.EruptCompUtils;
import xyz.erupt.toolkit.utils.EruptDaoUtils;
import xyz.erupt.toolkit.utils.NotifyUtils;

import javax.annotation.Resource;
import javax.transaction.Transactional;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @since :2023/4/4:11:14
 */
@Service
@Slf4j
public class FiveChecklistsFoundationHandler implements OperationHandler<FiveChecklistsFoundation, FiveChecklistsFoundation> {

    @Resource
    private EruptDao eruptDao;

    @Override
    @Transactional
    public String exec(List<FiveChecklistsFoundation> data, FiveChecklistsFoundation fiveChecklistsFoundation, String[] param) {
        fiveChecklistsFoundation.setId(data.get(0).getId());
        eruptDao.merge(fiveChecklistsFoundation);
        return NotifyUtils.getSuccessNotify("保存成功！");

    }

    public FiveChecklistsFoundation detail(Long id) {
        FiveChecklistsFoundationController messageController = EruptSpringUtil.getBean(FiveChecklistsFoundationController.class);
        FiveChecklistsFoundation fiveChecklistsFoundation = messageController.initValue(id);
        return fiveChecklistsFoundation;
    }


    @RestController
    @Transactional
    public static class FiveChecklistsFoundationController {

        @RequestMapping("erupt-api/data/FiveDetail/operator/foundation")
        @EruptRouter(verifyType = EruptRouter.VerifyType.LOGIN)
        public String recreate(@RequestBody EruptResultMap reuquest) {
            FiveChecklistsFoundation foundation = (FiveChecklistsFoundation) reuquest.getAs("param", FiveChecklistsFoundation.class);
            List list = new ArrayList();
            list.add(foundation);
            return EruptSpringUtil.getBean(FiveChecklistsFoundationHandler.class).exec(list, foundation, null);
        }

        @RequestMapping("/erupt-api/data/FiveChecklistsFoundation/{id}")
        @EruptRouter(verifyType = EruptRouter.VerifyType.LOGIN)
        public FiveChecklistsFoundation initValue(@PathVariable("id") Long id) {
            EruptResultMap fiveDetail = EruptDaoUtils.selectOne("select * from tb_five_detail where id=" + id, EruptResultMap.class);
            FiveChecklistsFoundation fiveChecklistsFoundation = EruptDaoUtils.selectOne("select * from tb_fivechecklists_foundation where id=" + fiveDetail.getInt("five_checklists_foundation_id"), FiveChecklistsFoundation.class);
            return fiveChecklistsFoundation;
        }
    }

}
