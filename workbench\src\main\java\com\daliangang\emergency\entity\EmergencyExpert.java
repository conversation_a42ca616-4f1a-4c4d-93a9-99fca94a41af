/*
 * Copyright © 2020-2035 yundingyun All rights reserved.
 */
package com.daliangang.emergency.entity;

import com.daliangang.emergency.proxy.EmergencyExpertDataProxy;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import xyz.erupt.annotation.Erupt;
import xyz.erupt.annotation.EruptField;
import xyz.erupt.annotation.sub_erupt.Power;
import xyz.erupt.annotation.sub_field.Edit;
import xyz.erupt.annotation.sub_field.EditType;
import xyz.erupt.annotation.sub_field.View;
import xyz.erupt.annotation.sub_field.sub_edit.InputType;
import xyz.erupt.annotation.sub_field.sub_edit.Search;
import xyz.erupt.jpa.model.MetaModel;
import xyz.erupt.toolkit.db.Comment;

import javax.persistence.Entity;
import javax.persistence.Table;

@Erupt(name = "应急专家管理", importTruncate = true, power = @Power(add = true, delete = true, export = false, importable = true, viewDetails = true, edit = true)
        , dataProxy = EmergencyExpertDataProxy.class
        , rowOperation = {})
@Table(name = "tb_emergency_expert")
@Entity
@Getter
@Setter
@Comment("应急专家管理")
@ApiModel("应急专家管理")
public class EmergencyExpert extends MetaModel {
    @EruptField(
            views = @View(title = "专家姓名"),
            edit = @Edit(title = "专家姓名", type = EditType.INPUT, search = @Search(vague = true), notNull = true,
                    inputType = @InputType))
    @Comment("专家姓名")
    @ApiModelProperty("专家姓名")
    private String nameOfExpert;

    @EruptField(
            views = @View(title = "工作领域"),
            edit = @Edit(title = "工作领域", type = EditType.INPUT, search = @Search(vague = true), notNull = true,
                    inputType = @InputType))
    @Comment("工作领域")
    @ApiModelProperty("工作领域")
    private String workArea;

    @EruptField(
            views = @View(title = "职务/职称"),
            edit = @Edit(title = "职务/职称", type = EditType.INPUT, notNull = true,
                    inputType = @InputType))
    @Comment("职务/职称")
    @ApiModelProperty("职务/职称")
    private String post;

    @EruptField(
            views = @View(title = "工作单位",width = "300px"),
            edit = @Edit(title = "工作单位", type = EditType.INPUT,
                    inputType = @InputType))
    @Comment("工作单位")
    @ApiModelProperty("工作单位")
    private String workUnit;

    @EruptField(
            views = @View(title = "联系电话"),
            edit = @Edit(title = "联系电话", type = EditType.INPUT, notNull = true,
                    inputType = @InputType))
    @Comment("联系电话")
    @ApiModelProperty("联系电话")
    private String contactNumber;

    //	@EruptField(
//		views = @View(title = "专业特长"),
//		edit = @Edit(title = "专业特长", type = EditType.CHOICE, search = @Search(vague = true), notNull = true,
//		choiceType = @ChoiceType(fetchHandler = EruptDictCodeChoiceFetchHandler.class, fetchHandlerParams = "type")))
//	@Comment("专业特长")
//	@ApiModelProperty("专业特长")
//	private String professionalExpertise;
    @EruptField(
            views = @View(title = "专业特长"),
            edit = @Edit(
                    title = "专业特长",
                    type = EditType.INPUT,
                    search = @Search(vague = true),
                    notNull = true,
                    inputType = @InputType
            )
    )
    @Comment("专业特长")
    @ApiModelProperty("专业特长")
    private String professionalExpertise;

}
