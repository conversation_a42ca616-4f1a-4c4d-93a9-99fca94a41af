package com.daliangang.emergency.operation;

import com.daliangang.emergency.entity.EmergencyFlowChart;
import org.springframework.stereotype.Service;
import xyz.erupt.annotation.fun.OperationHandler;

import java.util.List;

/**
 * <AUTHOR>
 * @since :2023/4/26:11:44
 */
@Service
public class EmergencyFlowChartHandler implements OperationHandler<EmergencyFlowChart, Void> {

    @Override
    public String exec(List<EmergencyFlowChart> data, Void aVoid, String[] param) {
        return null;
    }
}
