package com.daliangang.device.controller;

import com.daliangang.core.DaliangangContext;
import com.daliangang.device.entity.TankFarm;
import com.google.gson.internal.LinkedTreeMap;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import xyz.erupt.core.annotation.EruptRouter;
import xyz.erupt.core.module.MetaUserinfo;
import xyz.erupt.core.view.EruptApiModel;
import xyz.erupt.jpa.dao.EruptDao;
import xyz.erupt.toolkit.remote.RemoteProxyService;
import xyz.erupt.toolkit.utils.EruptDaoUtils;
import xyz.erupt.toolkit.utils.SqlUtils;
import xyz.erupt.upms.service.EruptUserService;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @since :2023/4/18:13:46
 */
@RestController
public class TankFarmController {
    @Resource
    private EruptDao eruptDao;
    @Resource
    private RemoteProxyService remoteProxyService;
    @Resource
    private EruptUserService eruptUserService;

    /**
     * 获取企业罐区管理
     *
     * @param
     * @return
     */
    @RequestMapping("erupt-api/get/tankFarm")
    @EruptRouter(verifyType = EruptRouter.VerifyType.LOGIN)
    public EruptApiModel getTankFarm() {
        MetaUserinfo remoteUserInfo = remoteProxyService.getRemoteUserInfo();
        ArrayList<LinkedTreeMap> result = new ArrayList<>();
        List<TankFarm> wharves=new ArrayList<>();
        if(DaliangangContext.isDepartmentUser()){
            //查全部
            List<String> auth = remoteUserInfo.getAuth();
            wharves = EruptDaoUtils.selectOnes("select * from tb_tank_farm where org_code " + SqlUtils.wrapIn(auth), TankFarm.class);
        }else {
            wharves = EruptDaoUtils.selectOnes("select * from tb_tank_farm where org_code="+SqlUtils.wrapStr(remoteUserInfo.getOrg()), TankFarm.class);
        }
        if (ObjectUtils.isNotEmpty(wharves)) {
            wharves.forEach(v -> {
                LinkedTreeMap map = new LinkedTreeMap();
                map.put("code", v.getId());
                map.put("name", v.getTankFarm());
                result.add(map);
            });
        }
        return EruptApiModel.successApi(result);
//        List<TankFarm> tankFarms = new ArrayList<>();
//        //当前登录人是政府
//        if (DaliangangContext.isDepartmentUser()) {
//            List<String> auth = eruptUserService.getUserAuth();
//            if (auth != null && !auth.isEmpty()) {
//                //tankFarms = EruptDaoUtils.selectOnes("select * from tb_tank_farm where org_code " + SqlUtils.wrapIn(auth), TankFarm.class);
//                tankFarms = this.queryTankFarms(SqlUtils.wrapIn(auth));
//            }
//        } else if (DaliangangContext.isEnterpriseUser()) {
//            //当前登录人是企业
//            MetaUserinfo remoteUserInfo = remoteProxyService.getRemoteUserInfo();
//            //tankFarms = EruptDaoUtils.selectOnes("select * from tb_tank_farm where org_code='" + remoteUserInfo.getOrg() + "'", TankFarm.class);
//            tankFarms = this.queryTankFarms("=" + SqlUtils.wrapStr(remoteUserInfo.getOrg()));
//        }
//
//        if (ObjectUtils.isNotEmpty(tankFarms)) {
//            List<LinkedTreeMap> list1 = new ArrayList<>();
//            tankFarms.forEach(v -> {
//                LinkedTreeMap map = new LinkedTreeMap();
//                map.put("code", v.getId());
//                map.put("name", v.getTankFarm());
//                list1.add(map);
//            });
//
//            return EruptApiModel.successApi(list1);
//        }
//        return EruptApiModel.successApi();
    }

//    private List<TankFarm> queryTankFarms(String orgCode) {
//        List<TankFarm> tankFarms = EruptDaoUtils.selectOnes("select * from tb_tank_farm where org_code " + orgCode, TankFarm.class);
//        if (tankFarms.isEmpty()) {
//            tankFarms = EruptDaoUtils.selectOnes("select * from tb_tank_farm", TankFarm.class);
//        }
//        return tankFarms;
//    }


}
