/*
 * Copyright © 2020-2035 yundingyun All rights reserved.
 */
package com.daliangang.safedaily.proxy;

import cn.hutool.core.util.ObjectUtil;
import com.daliangang.core.DaliangangContext;
import com.daliangang.rndpub.entity.Expertaudit;
import com.daliangang.safedaily.entity.Report;
import lombok.SneakyThrows;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import xyz.erupt.annotation.fun.DataProxy;
import xyz.erupt.annotation.query.Condition;
import xyz.erupt.core.module.MetaUserinfo;
import xyz.erupt.core.util.DateUtil;
import xyz.erupt.jpa.dao.EruptDao;
import xyz.erupt.toolkit.remote.RemoteProxyService;
import xyz.erupt.toolkit.utils.EruptDaoUtils;
import xyz.erupt.toolkit.utils.NotifyUtils;

import javax.annotation.Resource;
import javax.transaction.Transactional;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.util.*;

@Service
public class ReportDataProxy implements DataProxy<Report> {

    @Resource
    private RemoteProxyService remoteProxyService;
    @Resource
    private EruptDao eruptDao;

    @Override
    public void addBehavior(Report report) {
        MetaUserinfo remoteUserInfo = remoteProxyService.getRemoteUserInfo();
        //默认回显董事长
        Report report1 = EruptDaoUtils.selectOne("select * FROM tb_report where org_code ='"+remoteUserInfo.getOrg()+"' ORDER BY id DESC LIMIT 0,1", Report.class);
        if (ObjectUtils.isNotEmpty(report1)) {
//            report.setReporter(report1.getReporter());
            report.setResPerson(report1.getResPerson());
        }

        report.setCompany(remoteUserInfo.getOrg());
        report.setContent(Report.DEFAULT_CONTENT);


    }

    @Override
    public String beforeFetch(List<Condition> conditions) {
        if (DaliangangContext.isDepartmentUser()) return "submitted=true";
        return null;
    }

    @Transactional
    @Override
    public void afterFetch(Collection<Map<String, Object>> list) {
        //转化成相应的对象
        List<Report> reportList = EruptDaoUtils.convert(list, Report.class);
        ArrayList<Report> updateReports = new ArrayList<>();
        reportList.forEach(report -> {
            if(report.getModifyTime()==null||report.getModifyTime().equals("")){
                if(report.getCreateTime()!=null){
                    ZoneId zoneId = ZoneId.systemDefault();
                    ZonedDateTime zonedDateTime = report.getCreateTime().atZone(zoneId);
                    Date from = Date.from(zonedDateTime.toInstant());
                    report.setModifyTime(DateUtil.getFormatDate(from, DateUtil.YEAR_MONTH));
                }else{
                    report.setModifyTime(DateUtil.getFormatDate(new java.util.Date(), DateUtil.YEAR_MONTH));
                }
                updateReports.add(report);
            }

        });
        //更改数据库中的数据
        updateReports.forEach(report -> {
            eruptDao.merge(report);
        });

    }

    @Override
    @SneakyThrows
    public void beforeAdd(Report report) {
        Report report1 = EruptDaoUtils.selectOne("select * FROM tb_report where org_code ='"+report.getOrgCode()+"' ORDER BY id DESC LIMIT 0,1", Report.class);
//        String time = getTime(report.getMouth());


//        if (ObjectUtils.isNotEmpty(report1)) {
//            if (report1.getMouth().equals(time)) {
//                NotifyUtils.showErrorMsg("当月已经添加，请勿重复添加！");
//            }
//            report.setMouth(time);
//        }
        report.setIsReport(false);
        report.setSubmitted(false);
        report.setModifyTime(DateUtil.getFormatDate(new java.util.Date(), DateUtil.YEAR_MONTH));
    }

    @Override
    public void beforeUpdate(Report report) {
        if (report.getSubmitted()) {
            NotifyUtils.showErrorMsg("报告单已经上报，请勿修改！");
        }
        report.setModifyTime(DateUtil.getFormatDate(new java.util.Date(), DateUtil.YEAR_MONTH));
    }


    @Override
    public void beforeDelete(Report report) {
        if (report.getSubmitted()) {
            NotifyUtils.showErrorMsg("报告单已经上报，请勿删除！");
        }
    }


    //判断 并转换时间格式 ditNumber = 43607.4166666667
    public static String getTime(String ditNumber) {
        if (!StringUtils.isNumeric(ditNumber)) return ditNumber;

        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM");
        //如果是数字 小于0则 返回
        BigDecimal bd = new BigDecimal(ditNumber);
        int days = bd.intValue();//天数
        int mills = (int) Math.round(bd.subtract(new BigDecimal(days)).doubleValue() * 24 * 3600);

        //获取时间
        Calendar c = Calendar.getInstance();
        c.set(1900, 0, 1);
        c.add(Calendar.DATE, days - 2);
        int hour = mills / 3600;
        int minute = (mills - hour * 3600) / 60;
        int second = mills - hour * 3600 - minute * 60;
        c.set(Calendar.HOUR_OF_DAY, hour);
        c.set(Calendar.MINUTE, minute);
        c.set(Calendar.SECOND, second);

        return dateFormat.format(c.getTime());


    }

    @Override
    public void searchCondition(Map<String, Object> condition) {
        condition.put("modifyTime", DateUtil.getFormatDate(new java.util.Date(), DateUtil.YEAR_MONTH));
    }


}
