/*
* Copyright © 2020-2035 yundingyun All rights reserved.
*/
package com.daliangang.safedaily.proxy;

import com.daliangang.core.DaliangangContext;
import com.daliangang.safedaily.entity.Preplan;
import org.springframework.stereotype.Service;
import xyz.erupt.annotation.fun.DataProxy;
import xyz.erupt.annotation.query.Condition;
import xyz.erupt.toolkit.db.EruptResultMap;
import xyz.erupt.toolkit.utils.EruptDaoUtils;
import xyz.erupt.upms.service.EruptUserService;

import javax.annotation.Resource;
import java.util.Collection;
import java.util.List;
import java.util.Map;

@Service
public class PreplanDataProxy implements DataProxy<Preplan> {

    @Resource
    private EruptUserService eruptUserService;

    @Override
    public String beforeFetch(List<Condition> conditions) {
        if (DaliangangContext.isDepartmentUser()) return "submitted=true";
        return null;
    }

    @Override
    public void afterFetch(Collection<Map<String, Object>> list) {
        List<Preplan> preplans = EruptDaoUtils.convert(list, Preplan.class);
        for (Preplan preplan : preplans) {
            String countSql="select count(*) as dangerNum from tb_preplan_data where data_source_id="+preplan.getId();
            EruptResultMap eruptResultMap = EruptDaoUtils.selectMap(countSql);
            String dangerNum = String.valueOf(eruptResultMap.get("dangerNum"));
            preplan.setDangerNum(dangerNum);
            EruptDaoUtils.updateAfterFetch(list, preplan.getId(), "dangerNum", preplan.getDangerNum());
        }
    }

    @Override
    public void beforeAdd(Preplan preplan) {
        preplan.setSubmitted(true);
    }

    @Override
    public void beforeUpdate(Preplan preplan) {
//        if (preplan.getSubmitted()) {
//            NotifyUtils.showErrorMsg("报告单已经上报，请勿修改！");
//        }
        preplan.setSubmitted(true);

    }

    @Override
    public void beforeDelete(Preplan preplan) {
//        if (preplan.getSubmitted()) {
//            NotifyUtils.showErrorMsg("报告单已经上报，不可删除！");
//        }
//        preplan.setSubmitted(false);
    }

//    @Override
//    public void searchCondition(Map<String, Object> condition) {
//        if(!DaliangangContext.isDepartmentUser()){
//            EruptUser user = eruptUserService.getCurrentEruptUser();
//            if (user != null && user.getEruptOrg() != null)
//                condition.put("company", user.getEruptOrg().getCode());
//        }
//    }
}
