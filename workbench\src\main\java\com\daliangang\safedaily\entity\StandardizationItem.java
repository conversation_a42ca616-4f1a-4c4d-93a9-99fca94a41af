package com.daliangang.safedaily.entity;

import cn.hutool.json.JSONObject;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import xyz.erupt.annotation.Erupt;
import xyz.erupt.annotation.EruptField;
import xyz.erupt.annotation.expr.ExprBool;
import xyz.erupt.annotation.fun.DataProxy;
import xyz.erupt.annotation.sub_erupt.Power;
import xyz.erupt.annotation.sub_field.Edit;
import xyz.erupt.annotation.sub_field.EditType;
import xyz.erupt.annotation.sub_field.Readonly;
import xyz.erupt.annotation.sub_field.View;
import xyz.erupt.annotation.sub_field.sub_edit.InputType;
import xyz.erupt.annotation.sub_field.sub_edit.NumberType;
import xyz.erupt.common.http.EruptRemoteUtils;
import xyz.erupt.core.util.EruptSpringUtil;
import xyz.erupt.jpa.model.MetaModel;
import xyz.erupt.toolkit.db.Comment;
import xyz.erupt.toolkit.remote.RemoteProxyService;
import xyz.erupt.upms.util.EruptMenuUtils;

import javax.persistence.Entity;
import javax.persistence.Table;
import java.math.BigDecimal;
import java.util.Map;

@Erupt(name = "标准化得分项", importTruncate = true, power = @Power(importable = true), orderBy = "seriesId", dataProxy = StandardizationItem.class)
@Table(name = "tb_standardization_item")
@Entity
@Getter
@Setter
@Comment("标准化得分项")
@ApiModel("标准化得分项")
public class StandardizationItem extends MetaModel implements ExprBool.ExprHandler, Readonly.ReadonlyHandler, DataProxy<StandardizationItem> {

    //序号、评价项目、应得分数、实际得分、得分占比

    @EruptField(views = @View(title = "序号",width = "200px"), edit = @Edit(title = "序号", readonly = @Readonly(add = false,edit = true), type = EditType.NUMBER))
    @Comment("序号")
    @ApiModelProperty("序号")
    private Integer seriesId;

    @EruptField(views = @View(title = "评价项目",width = "300px"), edit = @Edit(title = "评价项目", readonly = @Readonly(exprHandler = StandardizationItem.class), type = EditType.INPUT, inputType = @InputType))
    @Comment("评价项目")
    @ApiModelProperty("评价项目")
    private String standard;

    @EruptField(views = @View(title = "标准分数",width = "200px"),
            edit = @Edit(title = "标准分数", type = EditType.NUMBER, numberType = @NumberType()))
    @Comment("标准分数")
    @ApiModelProperty("标准分数")
    private BigDecimal tandardsScore;

    @EruptField(views = @View(title = "应得分数",show = false), edit = @Edit(title = "应得分数", type = EditType.NUMBER, show = false,numberType = @NumberType(min = 0, max = 100)))
    @Comment("应得分数")
    @ApiModelProperty("应得分数")
    private BigDecimal scoreStandard;

    @EruptField(views = @View(title = "得分占比", ifRender = @ExprBool(exprHandler = StandardizationItem.class),show = false),
            edit = @Edit(title = "得分占比", show = false,ifRender = @ExprBool(exprHandler = StandardizationItem.class), readonly = @Readonly(exprHandler = StandardizationItem.class), type = EditType.NUMBER, numberType = @NumberType(min = 0, max = 100)))
    @Comment("得分占比")
    @ApiModelProperty("得分占比")
    private BigDecimal scoreProportion;

    @Override
    public void excelImport(Object workbook) {
//        EruptDaoUtils.truncate(StandardizationItem.class);
    }

    @Override
    public boolean add(boolean add, String[] params) {
        return false;
    }

    @Override
    public boolean edit(boolean edit, String[] params) {
        return EruptMenuUtils.isEruptMenu(Standardization.class) || EruptMenuUtils.isEruptMenu(StandardizationSenond.class) || EruptMenuUtils.isEruptMenu(StandardizationThird.class);
    }

    @Override
    public boolean handler(boolean expr, String[] params) {
        return EruptMenuUtils.isEruptMenu(StandardizationItem.class);
    }

    @Override
    public void afterAdd(StandardizationItem standardizationItem) {

        RemoteProxyService remoteProxyService = EruptSpringUtil.getBean(RemoteProxyService.class);
        JSONObject inputData = new JSONObject();
        inputData.set("clazz", "StandardizationItem");
        inputData.set("insertData",standardizationItem);
        EruptRemoteUtils.doPost(remoteProxyService.getMainServerUrl() + "/erupt-api/exchange/data/exchange", inputData, Map.class);
    }

    @Override
    public void afterUpdate(StandardizationItem standardizationItem) {
        RemoteProxyService remoteProxyService = EruptSpringUtil.getBean(RemoteProxyService.class);
        JSONObject inputData = new JSONObject();
        inputData.set("clazz", "StandardizationItem");
        inputData.set("insertData",standardizationItem);
        EruptRemoteUtils.doPost(remoteProxyService.getMainServerUrl() + "/erupt-api/exchange/data/exchange", inputData, Map.class);
    }
}
