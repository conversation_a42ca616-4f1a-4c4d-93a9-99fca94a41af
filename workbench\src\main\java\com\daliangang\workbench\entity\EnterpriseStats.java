/*
 * Copyright © 2020-2035 yundingyun All rights reserved.
 */
package com.daliangang.workbench.entity;

import com.daliangang.workbench.proxy.EnterpriseStatsDataProxy;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import xyz.erupt.annotation.Erupt;
import xyz.erupt.annotation.EruptField;
import xyz.erupt.annotation.sub_erupt.Power;
import xyz.erupt.annotation.sub_field.Edit;
import xyz.erupt.annotation.sub_field.EditType;
import xyz.erupt.annotation.sub_field.Readonly;
import xyz.erupt.annotation.sub_field.View;
import xyz.erupt.annotation.sub_field.sub_edit.ChoiceType;
import xyz.erupt.annotation.sub_field.sub_edit.InputType;
import xyz.erupt.annotation.sub_field.sub_edit.Search;
import xyz.erupt.toolkit.db.Comment;
import xyz.erupt.toolkit.handler.RemoteEntityChoiceFetchHandler;
import xyz.erupt.upms.model.auth.DataAuthHandler;
import xyz.erupt.upms.model.auth.DataAuthModel;

import javax.persistence.Entity;
import javax.persistence.Table;
import javax.persistence.Transient;

@Erupt(name = "企业统计",power = @Power(add = false, delete = false, export = false, importable = false, viewDetails = false, edit = false),
        dataProxy = EnterpriseStatsDataProxy.class)
@Table(name = "tb_enterprise_stats")
@Entity
@Getter
@Setter
@Comment("企业统计")
@ApiModel("企业统计")
public class EnterpriseStats extends DataAuthModel {

    @EruptField(
            views = @View(title = "企业名称"),
            edit = @Edit(title = "企业名称", readonly = @Readonly(exprHandler = DataAuthHandler.class), type = EditType.CHOICE, notNull = true,search = @Search(),
                    choiceType = @ChoiceType(fullSpan = true,fetchHandler = RemoteEntityChoiceFetchHandler.class, fetchHandlerParams = {"main", "Enterprise", "org_code,name"})))
    @Comment("企业名称")
    @ApiModelProperty("企业名称")
    @Transient
    private String company;

    @EruptField(
            views = @View(title = "泊位数量"),
            edit = @Edit(title = "泊位数量", type = EditType.INPUT,
                    inputType = @InputType))
    @Comment("泊位数量")
    @ApiModelProperty("泊位数量")
    private String berthNum;

    @EruptField(
            views = @View(title = "罐区数量"),
            edit = @Edit(title = "罐区数量", type = EditType.INPUT,
                    inputType = @InputType))
    @Comment("罐区数量")
    @ApiModelProperty("罐区数量")
    private String tankFarmNum;

    @EruptField(
            views = @View(title = "罐组数量"),
            edit = @Edit(title = "罐组数量", type = EditType.INPUT,
                    inputType = @InputType))
    @Comment("罐组数量")
    @ApiModelProperty("罐组数量")
    private String tankGroupNum;



    @EruptField(
            views = @View(title = "储罐数量"),
            edit = @Edit(title = "储罐数量", type = EditType.INPUT,
                    inputType = @InputType))
    @Comment("储罐数量")
    @ApiModelProperty("储罐数量")
    private String storageTankNum;

    @EruptField(
            views = @View(title = "储罐容积(m³)"),
            edit = @Edit(title = "储罐容积(m³)", type = EditType.INPUT,
                    inputType = @InputType))
    @Comment("储罐容积(m³)")
    @ApiModelProperty("储罐容积(m³)")
    private String storageTankVolume;

    @EruptField(
            views = @View(title = "港经证有效期"),
            edit = @Edit(title = "港经证有效期", type = EditType.INPUT,
                    inputType = @InputType))
    @Comment("港经证有效期")
    @ApiModelProperty("港经证有效期")
    private String hongKongCertificateValidity;

    @EruptField(
            views = @View(title = "附证有效期"),
            edit = @Edit(title = "附证有效期", type = EditType.INPUT,
                    inputType = @InputType))
    @Comment("附证有效期")
    @ApiModelProperty("附证有效期")
    private String collateralValidity;

    @EruptField(
            views = @View(title = "重大风险源备案"),
            edit = @Edit(title = "重大风险源备案", type = EditType.INPUT,
                    inputType = @InputType))
    @Comment("重大风险源备案")
    @ApiModelProperty("重大风险源备案")
    private String riskSourceFilingNum;

    @EruptField(
            views = @View(title = "预案备案"),
            edit = @Edit(title = "预案备案", type = EditType.INPUT,
                    inputType = @InputType))
    @Comment("预案备案")
    @ApiModelProperty("预案备案")
    private String recordOfPlanNum;

    @EruptField(
            views = @View(title = "标准化达标等级"),
            edit = @Edit(title = "标准化达标等级", type = EditType.INPUT,
                    inputType = @InputType))
    @Comment("标准化达标等级")
    @ApiModelProperty("标准化达标等级")
    private String standardization;

    @EruptField(
            views = @View(title = "安全评价备案"),
            edit = @Edit(title = "安全评价备案", type = EditType.INPUT,
                    inputType = @InputType))
    @Comment("安全评价备案")
    @ApiModelProperty("安全评价备案")
    private String safetyEvaluationRecord;

    @EruptField(
            views = @View(title = "港口设施保安"),
            edit = @Edit(title = "港口设施保安", type = EditType.INPUT,
                    inputType = @InputType))
    @Comment("港口设施保安")
    @ApiModelProperty("港口设施保安")
    private String portFacilitySecurity;

    @EruptField(
            views = @View(title = "水路运输从业资格证"),
            edit = @Edit(title = "水路运输从业资格证", type = EditType.INPUT,
                    inputType = @InputType))
    @Comment("水路运输从业资格证")
    @ApiModelProperty("水路运输从业资格证")
    private String waterTransport;

    @EruptField(
            views = @View(title = "特种设备操作证书"),
            edit = @Edit(title = "特种设备操作证书", type = EditType.INPUT,
                    inputType = @InputType))
    @Comment("特种设备操作证书")
    @ApiModelProperty("特种设备操作证书")
    private String specialEquipment;

    @EruptField(
            views = @View(title = "特种作业操作证书"),
            edit = @Edit(title = "特种作业操作证书", type = EditType.INPUT,
                    inputType = @InputType))
    @Comment("特种作业操作证书")
    @ApiModelProperty("特种作业操作证书")
    private String specialOperation;

    @EruptField(
            views = @View(title = "其他证书"),
            edit = @Edit(title = "其他证书", type = EditType.INPUT,
                    inputType = @InputType))
    @Comment("其他证书")
    @ApiModelProperty("其他证书")
    private String otherCertificates;


}
