/*
 * Copyright © 2020-2035 yundingyun All rights reserved.
 */
package com.daliangang.rndpub.proxy;

import cn.hutool.core.collection.CollectionUtil;
import com.daliangang.rndpub.entity.CheckPerson;
import com.daliangang.workbench.entity.Enterprise;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import xyz.erupt.annotation.fun.DataProxy;
import xyz.erupt.annotation.query.Condition;
import xyz.erupt.core.config.GsonFactory;
import xyz.erupt.core.view.Page;
import xyz.erupt.jpa.dao.EruptDao;
import xyz.erupt.toolkit.db.EruptResultMap;
import xyz.erupt.toolkit.remote.RemoteProxyService;
import xyz.erupt.toolkit.utils.EruptDaoUtils;
import xyz.erupt.toolkit.utils.SqlUtils;
import xyz.erupt.upms.model.EruptOrg;

import javax.annotation.Resource;
import javax.transaction.Transactional;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Service
@Slf4j
public class CheckpersonDataProxy implements DataProxy<CheckPerson> {
    @Resource
    private RemoteProxyService remoteProxyService;

    @Resource
    private EruptDao eruptDao;

    @Override
    @Transactional
    public void searchCondition(Map<String, Object> condition) {
        this.syncCheckPersonData();
    }


    @Override
    public String beforeFetch(List<Condition> conditions) {
        String returnStr = "checkPerson=true";

        return returnStr;
    }


    @Transactional
    public void syncCheckPersonData() {
//        Page page = remoteProxyService.getEruptData("EmployeeCheckPerson", 1, Short.MAX_VALUE, "name");
//        if(CollectionUtil.isEmpty(page.getList())){
//            return  ;
//        }
        //EruptDaoUtils.updateNoForeignKeyChecks("TRUNCATE tb_checkperson");
//        List<EruptResultMap> checkpersonList = EruptDaoUtils.convert(page.getList(), EruptResultMap.class);

        //直接查库(因不拆服务了，直接查库较快）
        String employeeSql = "select * from tb_employee_information order by name";
        List<EruptResultMap> checkpersonList = EruptDaoUtils.selectOnes(employeeSql, EruptResultMap.class);
        List<Long> names = new ArrayList<>();
        for (EruptResultMap resultMap : checkpersonList) {
            CheckPerson checkperson = EruptDaoUtils.cast(resultMap, CheckPerson.class);
            log.info("checkperson -> " + GsonFactory.getGson().toJson(resultMap));
            checkperson.setSex(resultMap.getBool("sex"));
            //checkperson.setState(resultMap.getString("state").equals("启用"));
            checkperson.setCheckPerson(resultMap.getBool("check_person"));
            EruptOrg ent = EruptDaoUtils.selectOne("select * from e_upms_org where code='" + resultMap.getString("company") + "'", EruptOrg.class);
            checkperson.setBelongCompany(ent.getName());
//            CheckPerson person = eruptDao.queryEntity(CheckPerson.class, "name='" + checkperson.getName() + "' limit 1");
            CheckPerson person = null;
            List<CheckPerson> checkPersonList = eruptDao.queryEntityList(CheckPerson.class, "name='" + checkperson.getName() + "'");
            if (!checkPersonList.isEmpty()) {
                person = checkPersonList.get(0);
            }
            if (person == null) {
                person = checkperson;
                person.setState(false);
                //这里先新增，再修改id，因为此时表里面无该条数据的id，用merge无效，用persist会报错（因为自增id，且这里又给赋id）
                Long id = person.getId();
                person.setId(null);
                eruptDao.persist(person);
                eruptDao.getJdbcTemplate().execute(String.format("update tb_checkperson set id = %s where id = %s", id, person.getId()));
                names.add(id);

            } else {
                long id = person.getId();
                boolean state = person.getState();
                person = EruptDaoUtils.cast(checkperson, CheckPerson.class);
                person.setId(id);
                person.setState(state);
                eruptDao.merge(person);
                names.add(person.getId());
            }


        }
        if (names.size() > 0)
            EruptDaoUtils.updateNoForeignKeyChecks("delete from tb_checkperson where id not " + SqlUtils.wrapIn(names));
    }
}
