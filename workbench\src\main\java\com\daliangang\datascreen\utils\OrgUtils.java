package com.daliangang.datascreen.utils;

import com.daliangang.workbench.entity.Enterprise;
import org.jetbrains.annotations.NotNull;
import xyz.erupt.core.exception.EruptApiErrorTip;
import xyz.erupt.core.util.EruptSpringUtil;
import xyz.erupt.jpa.dao.EruptDao;
import xyz.erupt.toolkit.utils.EruptDaoUtils;
import xyz.erupt.upms.model.EruptUser;
import xyz.erupt.upms.service.EruptUserService;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @Author: chongmenglin
 * @Date: 2024/11/13 15:49
 */
public class OrgUtils {
    private static final EruptUserService eruptUserService;
    private static final EruptDao eruptDao;

    // 静态初始化块，初始化静态变量
    static {
        eruptUserService = EruptSpringUtil.getBean(EruptUserService.class);
        eruptDao = EruptDaoUtils.getEruptDao();
    }

    /**
     * 获取未禁用企业code值
     *
     * @return
     */
    public static List<Enterprise> getStartUpOrgCode() {
        // 获取本级及下级的 orgCode 列表
        List<String> auth = eruptUserService.getUserAuth();
        // 创建参数映射
        Map<String, Object> params = new HashMap<>();
        params.put("auth", auth);
        // 从数据库中查询所有符合条件的 Enterprise 对象
        List<Enterprise> enterprises = eruptDao.queryEntityList(
                Enterprise.class,
                "state != '0' AND orgCode IN (:auth)",
                params
        );
        // 从符合条件的 Enterprise 列表中提取 orgCode 字段并返回
        return enterprises;
    }

    public static List<String> getDeptCodes(String orgCode) {
        if(orgCode != null) {
            return Collections.singletonList(orgCode);
        }
        // 获取本级及下级的 orgCode 列表
        List<String> auth = eruptUserService.getUserAuth();

        // 从数据库中查询所有符合条件的 Enterprise 对象，并提取 orgCode 列表
        Set<String> codes = eruptDao.queryEntityList(
                        Enterprise.class,
                        "state = '0'"
                ).stream()
                .map(Enterprise::getOrgCode)
                .collect(Collectors.toSet());

        // 返回 auth 中不包含 codes 的元素
        return auth.stream()
                .filter(code -> !codes.contains(code))
                .collect(Collectors.toList());
    }


    /**
     * 获取未禁用企业code值
     *
     * @return
     */
    public static List<Enterprise> getStartUpOrgCode(EruptUser user) {
        // 获取本级及下级的 orgCode 列表
        List<String> auth = eruptUserService.getUserAuth(user);
        // 创建参数映射
        Map<String, Object> params = new HashMap<>();
        params.put("auth", auth);
        // 从数据库中查询所有符合条件的 Enterprise 对象
        List<Enterprise> enterprises = eruptDao.queryEntityList(
                Enterprise.class,
                "state != '0' AND orgCode IN (:auth)",
                params
        );
        // 从符合条件的 Enterprise 列表中提取 orgCode 字段并返回
        return enterprises;
    }

    public static String getCompanyName(String orgCode, List<Enterprise> enterprises) {
        return enterprises.stream()
                .filter(e -> e.getOrgCode().equals(orgCode))
                .map(Enterprise::getName)
                .findFirst()
                .orElse("未知");
    }

    public static String getCompanyName(String orgCode) {
        Enterprise enterprise = eruptDao.queryEntity(Enterprise.class, "orgCode = '" + orgCode + "'");
        return enterprise == null? "未知" : enterprise.getName();
    }

    public static List<String> getCompanyNamesByBatchQuery(List<String> orgCodes) {
        // 拼接 SQL 查询条件
        String queryCondition = "orgCode IN :orgCodes";
        List<Enterprise> enterprises = eruptDao.queryEntityList(
                Enterprise.class,
                queryCondition,
                new HashMap<String, Object>() {{
                    put("orgCodes", orgCodes);
                }}
        );

        // 创建 orgCode -> companyName 的映射
        Map<String, String> orgCodeToNameMap = enterprises.stream()
                .collect(Collectors.toMap(Enterprise::getOrgCode, Enterprise::getName));

        // 根据 orgCodes 返回对应的名称集合
        return orgCodes.stream()
                .map(orgCode -> orgCodeToNameMap.getOrDefault(orgCode, "未知"))
                .collect(Collectors.toList());
    }


    public static @NotNull List<String> getCodes(String orgCode) {
        List<Enterprise> enterprises = OrgUtils.getStartUpOrgCode();
        List<String> codes = enterprises.stream()
                .map(Enterprise::getOrgCode)
                .collect(Collectors.toList());

        // 如果 orgCode 为 null，直接返回所有企业代码
        if (orgCode == null) {
            return codes;
        }

        // 如果 orgCode 不在 codes 中，抛出异常
        if (!codes.contains(orgCode)) {
            throw new EruptApiErrorTip("您没有权限查看该企业信息");
        }

        // 如果 orgCode 存在，返回它的单个值
        return Collections.singletonList(orgCode);
    }

    public static @NotNull List<String> getCodes() {
        List<Enterprise> enterprises = OrgUtils.getStartUpOrgCode();
        return enterprises.stream()
                .map(Enterprise::getOrgCode)
                .collect(Collectors.toList());

    }
}
