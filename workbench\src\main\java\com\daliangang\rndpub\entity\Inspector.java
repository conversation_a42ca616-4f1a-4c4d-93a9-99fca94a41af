/*
 * Copyright © 2020-2035 yundingyun All rights reserved.
 */
package com.daliangang.rndpub.entity;

import com.daliangang.core.CompanyRenderHandler;
import com.daliangang.rndpub.operation.InspectorManualSelectionHandler;
import com.daliangang.rndpub.operation.InspectorSystemExtractionHandler;
import com.daliangang.rndpub.proxy.InspectorDataProxy;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import xyz.erupt.annotation.Erupt;
import xyz.erupt.annotation.EruptField;
import xyz.erupt.annotation.expr.ExprBool;
import xyz.erupt.annotation.sub_erupt.PageModel;
import xyz.erupt.annotation.sub_erupt.Power;
import xyz.erupt.annotation.sub_erupt.RowOperation;
import xyz.erupt.annotation.sub_field.Edit;
import xyz.erupt.annotation.sub_field.EditType;
import xyz.erupt.annotation.sub_field.Readonly;
import xyz.erupt.annotation.sub_field.View;
import xyz.erupt.annotation.sub_field.sub_edit.BoolType;
import xyz.erupt.annotation.sub_field.sub_edit.InputType;
import xyz.erupt.annotation.sub_field.sub_edit.Search;
import xyz.erupt.annotation.sub_field.sub_edit.TagsType;
import xyz.erupt.toolkit.db.Comment;
import xyz.erupt.toolkit.handler.SqlTagFetchHandler;
import xyz.erupt.upms.model.auth.DataAuthModel;

import javax.persistence.Entity;
import javax.persistence.ManyToOne;
import javax.persistence.Table;

@Erupt(name = "检查人员", power = @Power(add = false, delete = false, export = false, importable = true, viewDetails = true, edit = true),selectionVisible = false
        , dataProxy = InspectorDataProxy.class
        , pageModel = @PageModel(flag = true,pageProxy = InspectorDataProxy.class)
        , rowOperation = {
        @RowOperation(title = "自动抽取", icon = "fa fa-chain-broken", eruptClass = InspectorManualSelectionHandler.DrawForm.class, operationHandler = InspectorManualSelectionHandler.class, mode = RowOperation.Mode.BUTTON),
        @RowOperation(confirm = false, title = "抽取检查人员", ifExpr = "item.state=='未抽取'", icon = "fa fa-hand-o-up", operationHandler = InspectorSystemExtractionHandler.class, operationParam = "true", mode = RowOperation.Mode.MULTI),
        @RowOperation(confirm = false, title = "放弃抽取", ifExpr = "item.state=='已抽取'", icon = "fa fa-hand-o-down", operationHandler = InspectorSystemExtractionHandler.class, operationParam = "false", mode = RowOperation.Mode.MULTI),
        @RowOperation(confirm = false,type = RowOperation.Type.CLOSE,title = "确认", mode = RowOperation.Mode.BUTTON)
})
@Table(name = "tb_inspector")
@Entity
@Getter
@Setter
@Comment("检查人员")
@ApiModel("检查人员")
public class Inspector extends DataAuthModel {

    @ManyToOne
    private Procedure procedure;

    @EruptField(
            views = @View(title = "状态", show = true, sortable = true),
            edit = @Edit(title = "状态", type = EditType.BOOLEAN, notNull = true, show = false, search = @Search(vague = true),
                    boolType = @BoolType(trueText = "已抽取", falseText = "未抽取")))
    @Comment("状态")
    @ApiModelProperty("状态")
    private Boolean state;

    @EruptField(
            views = @View(title = "检查人员ID", show = false),
            edit = @Edit(title = "检查人员ID", type = EditType.INPUT, show = false,
                    inputType = @InputType))
    @Comment("检查人员ID")
    @ApiModelProperty("检查人员ID")
    private String InspectorId;

    @EruptField(
            views = @View(title = "检查人员姓名", show = true),
            edit = @Edit(title = "检查人员姓名", type = EditType.TAGS, notNull = true,
                    tagsType = @TagsType(allowExtension = false, fetchHandler = SqlTagFetchHandler.class, fetchHandlerParams = "select '测试项','测试项'")))
    @Comment("检查人员姓名")
    @ApiModelProperty("检查人员姓名")
    private String name;

    @EruptField(
            views = @View(title = "性别", show = true),
            edit = @Edit(title = "性别", type = EditType.BOOLEAN, notNull = true,
                    boolType = @BoolType(trueText = "男", falseText = "女")))
    @Comment("性别")
    @ApiModelProperty("性别")
    private Boolean sex;

    @EruptField(
            views = @View(title = "手机号", show = true),
            edit = @Edit(title = "手机号", type = EditType.INPUT, notNull = true,
                    inputType = @InputType))
    @Comment("手机号")
    @ApiModelProperty("手机号")
    private String phone;

    @EruptField(
            views = @View(title = "执法证号",show = false),
            edit = @Edit(title = "执法证号", type = EditType.INPUT,show = false,
                    inputType = @InputType))
    @Comment("执法证号")
    @ApiModelProperty("执法证号")
    private String certificateNo;

//    @EruptField(
//            views = @View(title = "企业名称", show = true),
//            edit = @Edit(title = "企业名称", readonly = @Readonly(exprHandler = DataAuthHandler.class), type = EditType.CHOICE, notNull = true,
//                    choiceType = @ChoiceType(fetchHandler = RemoteEntityChoiceFetchHandler.class, fetchHandlerParams = {"main", "Enterprise", "org_code,name"})))
//    @Comment("企业名称")
//    @ApiModelProperty("企业名称")
//    private String company;


    @EruptField(
            views = @View(title = "企业名称"),
            edit = @Edit(title = "企业名称", search = @Search(ifRender = @ExprBool(exprHandler = CompanyRenderHandler.class)),
                    readonly = @Readonly, type = EditType.INPUT))
    @Comment("企业名称")
    @ApiModelProperty("企业名称")
    private String company;

    @EruptField(
            views = @View(title = "检查范围", show = true),
            edit = @Edit(title = "检查范围", type = EditType.INPUT, notNull = true,
                    inputType = @InputType))
    @Comment("检查范围")
    @ApiModelProperty("检查范围")
    private String checkScope;

}
