package com.daliangang.safedaily.controller;

import com.daliangang.safedaily.form.NumForm;
import com.daliangang.safedaily.sql.CheckFillSql;
import com.daliangang.workbench.form.EmployeeCertificateForm;
import com.google.gson.internal.LinkedTreeMap;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import xyz.erupt.core.annotation.EruptRouter;
import xyz.erupt.core.module.MetaUserinfo;
import xyz.erupt.core.view.EruptApiModel;
import xyz.erupt.toolkit.remote.RemoteProxyService;
import xyz.erupt.toolkit.remote.RemoteQuery;
import xyz.erupt.toolkit.utils.EruptDaoUtils;
import xyz.erupt.toolkit.utils.SqlUtils;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @since :2023/4/23:11:47
 */
@RestController
public class CheckFillController {

    @Resource
    private RemoteProxyService remoteProxyService;
    @Resource
    private CheckFillSql checkFillSql;

    // 统计上报
    @RequestMapping("erupt-api/get/selectCheckFillNum")
    @EruptRouter(verifyType = EruptRouter.VerifyType.LOGIN)
    public EruptApiModel selectCheckFillNum() {
        MetaUserinfo remoteUserInfo = remoteProxyService.getRemoteUserInfo();
        String orgCode = SqlUtils.wrapIn(remoteUserInfo.getAuth());
        String sql = checkFillSql.selectCheckFillNum(orgCode);
        NumForm employeeCertificateForm = EruptDaoUtils.selectOne(sql, NumForm.class);
        return EruptApiModel.successApi(employeeCertificateForm);
    }

}
