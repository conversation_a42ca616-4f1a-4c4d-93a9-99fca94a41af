/*
* Copyright © 2020-2035 yundingyun All rights reserved.
*/
package com.daliangang.emergency.proxy;

import cn.hutool.json.JSONObject;
import lombok.*;
import org.springframework.stereotype.*;
import xyz.erupt.annotation.fun.*;
import com.daliangang.emergency.entity.*;
import xyz.erupt.common.http.EruptRemoteUtils;
import xyz.erupt.toolkit.remote.RemoteProxyService;

import javax.annotation.*;
import java.util.Map;

@Service
public class EmergencyExpertDataProxy implements DataProxy<EmergencyExpert> {

    @Resource
    private RemoteProxyService remoteProxyService;


    @Override
    public void afterAdd(EmergencyExpert emergencyExpert) {
        //推送数据
        JSONObject inputData = new JSONObject();
        inputData.set("clazz", "EmergencyExpert");
        inputData.set("insertData",emergencyExpert);
        EruptRemoteUtils.doPost(remoteProxyService.getMainServerUrl() + "/erupt-api/exchange/data/exchange", inputData, Map.class);
    }

    @Override
    public void afterUpdate(EmergencyExpert emergencyExpert) {
        //推送数据
        JSONObject inputData = new JSONObject();
        inputData.set("clazz", "EmergencyExpert");
        inputData.set("insertData",emergencyExpert);
        EruptRemoteUtils.doPost(remoteProxyService.getMainServerUrl() + "/erupt-api/exchange/data/exchange", inputData, Map.class);
    }
}
