package com.daliangang.workbench.patch;

import com.daliangang.workbench.entity.EmployeeInformation;
import com.daliangang.workbench.proxy.EmployeeInformationDataProxy;
import com.daliangang.workbench.proxy.InitMenuProxy;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import xyz.erupt.core.config.GsonFactory;
import xyz.erupt.designer.entity.handler.MenuDefaultRoleHandler;
import xyz.erupt.designer.entity.model.ModelMenu;
import xyz.erupt.devtools.patch.PlatformPatchHandler;
import xyz.erupt.excel.controller.EruptExcelController;
import xyz.erupt.jpa.dao.EruptDao;
import xyz.erupt.toolkit.db.EruptResultMap;
import xyz.erupt.toolkit.utils.EruptDaoUtils;
import xyz.erupt.upms.model.EruptMenu;
import xyz.erupt.upms.model.EruptRole;
import xyz.erupt.upms.model.EruptUser;
import xyz.erupt.upms.model.EruptUserByRoleView;
import xyz.erupt.upms.model.template.EruptRoleTemplate;
import xyz.erupt.upms.model.template.EruptRoleTemplateUser;
import xyz.erupt.upms.model.template.EruptRoleTemplateUserProxy;
import xyz.erupt.upms.service.EruptUserService;

import javax.annotation.Resource;
import javax.transaction.Transactional;
import java.util.Arrays;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

@Service
@Slf4j
public class EmployeeRoleMenuPatch implements PlatformPatchHandler {

    @Override
    public String getPatchId() {
        return "1.11.7.3-patch-01";
    }

    @Override
    public String getName() {
        return "员工初始角色与菜单补丁";
    }

    @Override
    public String getDesc() {
        return "1.修复自建公共角色错误设置为未共享的问题<br>" +
                "2.新增市级员工/区县员工基础数据<br>" +
                "3.重新初始化员工菜单信息";
    }

    @Resource
    private EruptDao eruptDao;

    @Resource
    private EruptUserService eruptUserService;

    @Resource
    private EruptExcelController eruptExcelController;

    @Resource
    private MenuDefaultRoleHandler menuDefaultRoleHandler;

    @Override
    @Transactional
    public boolean execute() {
        //执行数据修复补丁
        EruptDaoUtils.executeSql("public/patch/" + this.getPatchId() + ".sql");

        //所有员工数据缓存重置一下
        List<EruptUser> eruptUsers = eruptDao.queryEntityList(EruptUser.class);
        eruptUsers.forEach(eruptUser -> {
            eruptUserService.flushEruptUserCache(eruptUser);
        });

        //新增基准角色
        this.createBaseRole("108", "市级员工", 8, true, "GOV");
        this.createBaseRole("109", "区县员工", 9, true, "AREA");

        //新增角色模板
        this.createRoleTemplate("员工", "104");
        this.createRoleTemplate("市级员工", "108");
        this.createRoleTemplate("区县员工", "109");

        //重新导入菜单
        eruptExcelController.importExcel(ModelMenu.class, "public/init/01-00-菜单设置.xls");
        //List<ModelMenu> menus = eruptDao.queryEntityList(ModelMenu.class);
        menuDefaultRoleHandler.setTruncate(false);
        menuDefaultRoleHandler.exec(null, null, new String[]{"all"});

        //修正原来将企业自建角色设置为非共享的问题
        String fixCompanySql = "update e_upms_role set share=1 where name in( select name from tb_employee_role)";
        EruptDaoUtils.updateNoForeignKeyChecks(fixCompanySql);

        return true;
    }

    @Transactional
    public void createRoleTemplate(String name, String baseRole) {
        EruptRoleTemplate template = eruptDao.queryEntity(EruptRoleTemplate.class, "name='" + name + "'");
        if (template != null) {
            if (!template.isDuplicateRole()) {
                template.setDuplicateRole(true);
                eruptDao.merge(template);
            }
        } else {
            template = new EruptRoleTemplate();
            template.setName(name);
            template.setBaseRole(baseRole);
            template.setDataRange("LOWERS");
            template.setDuplicateRole(true);
            eruptDao.persist(template);
        }
    }

    //创建基准角色
    @Transactional
    public void createBaseRole(String code, String name, int sort, boolean share, String ident) {
        EruptRole exist = eruptDao.queryEntity(EruptRole.class, "code='" + code + "'");
        if (exist != null) return;
        EruptRole role = new EruptRole();
        role.setCode(code);
        role.setName(name);
        role.setSort(sort);
        role.setShare(share);
        role.setIdent(ident);
        role.setReserved(true);
        eruptDao.persist(role);
    }
}
