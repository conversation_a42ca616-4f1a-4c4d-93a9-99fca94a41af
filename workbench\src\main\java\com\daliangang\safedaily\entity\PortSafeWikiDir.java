package com.daliangang.safedaily.entity;

import com.daliangang.safedaily.operation.PortSafeWikiBatchUploadHandler;
import lombok.Data;
import xyz.erupt.annotation.Erupt;
import xyz.erupt.annotation.EruptField;
import xyz.erupt.annotation.fun.DataProxy;
import xyz.erupt.annotation.sub_erupt.Power;
import xyz.erupt.annotation.sub_erupt.RowOperation;
import xyz.erupt.annotation.sub_erupt.Tree;
import xyz.erupt.annotation.sub_field.Edit;
import xyz.erupt.annotation.sub_field.EditType;
import xyz.erupt.annotation.sub_field.View;
import xyz.erupt.annotation.sub_field.sub_edit.ReferenceTreeType;
import xyz.erupt.annotation.sub_field.sub_edit.Search;
import xyz.erupt.jpa.model.MetaModelVo;

import javax.persistence.Entity;
import javax.persistence.ManyToOne;
import javax.persistence.Table;

@Entity
@Table(name = "tb_port_safe_wiki_dir")
@Erupt(name = "港口安全知识库目录", importTruncate = true, power = @Power(importable = true),
        tree = @Tree(pid = "parent.id"), rowOperation = {
        @RowOperation(title = "批量上传", icon = "fa fa-upload", operationHandler = PortSafeWikiBatchUploadHandler.class, eruptClass = PortSafeWikiBatchUploadHandler.PortSafeWikiBatchUploadForm.class, mode = RowOperation.Mode.BUTTON)
})
@Data
public class PortSafeWikiDir extends MetaModelVo implements DataProxy<PortSafeWikiDir> {

    @EruptField(views = @View(title = "法规库分类",width = "200px"), edit = @Edit(title = "法规库分类", search = @Search(vague = true)))
    private String name;

//    @EruptField(views = @View(title = "目录名称"), edit = @Edit(title = "目录名称", notNull = true))
//    private String name;

    @ManyToOne
    @EruptField(edit = @Edit(title = "上级目录", type = EditType.REFERENCE_TREE, referenceTreeType = @ReferenceTreeType(pid = "parent.id")))
    private PortSafeWikiDir parent;

}
