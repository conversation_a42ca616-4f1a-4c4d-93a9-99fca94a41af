package com.daliangang.majorisk.form;

import lombok.Data;

/**
 * <AUTHOR>
 * @since :2023/5/16:18:31
 */
@Data
public class RiskControlDockingShipForm {

    //企业名称
    private String company;
    //企业id
    private String companyId;
    //作业名称
    private String name;
    //作业id
    private String job_id;
    //计划开始时
    private String begin_time;
    //计划结束时
    private String end_time;
    //实际开始时
    private String ab_time;
    //实际结束时
    private String ae_time;
    //货种名称
    private String goods_name;
    //总重（吨）
    private String weight;
    //作业⽅式
    private String work_mode;
    //作业类型
    private String work_type;
    //作业状态
    private String work_state;
    //⻛险数据库
    private String risk_database;
    //船名
    private String ship;
    //作业地点id
    private String g_id;
    //作业地点类
    private String g_type;
    //0-不撤销   1-撤销
    private String cancel_state ;
}
