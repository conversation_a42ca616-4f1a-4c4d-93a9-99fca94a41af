/*
* Copyright © 2020-2035 yundingyun All rights reserved.
*/
package com.daliangang.majorisk.operation;

import com.daliangang.safedaily.entity.Release;
import org.springframework.stereotype.*;
import xyz.erupt.annotation.fun.*;

import java.time.LocalDateTime;
import java.util.*;
import com.daliangang.majorisk.entity.*;
import xyz.erupt.jpa.dao.EruptDao;
import xyz.erupt.toolkit.utils.NotifyUtils;

import javax.annotation.Resource;
import javax.transaction.Transactional;

@Service
public class UnloadDangerousGoodsDeclarationSystemHandler implements OperationHandler<UnloadShip, Void> {

    @Resource
    private EruptDao eruptDao;
   @Override
   @Transactional
   public String exec(List<UnloadShip> data, Void unused, String[] param) {

       String url = "window.open('http://**************/dpos/','_blank')";
       return url;
   }
}
