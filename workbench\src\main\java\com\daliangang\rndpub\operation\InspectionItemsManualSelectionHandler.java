/*
 * Copyright © 2020-2035 yundingyun All rights reserved.
 */
package com.daliangang.rndpub.operation;

import cn.hutool.json.JSONUtil;
import com.daliangang.rndpub.entity.EnterpriseInformation;
import com.daliangang.rndpub.entity.InspectionItems;
import org.springframework.stereotype.Service;
import xyz.erupt.annotation.sub_erupt.Tpl;
import xyz.erupt.core.context.MetaDrill;
import xyz.erupt.toolkit.db.EruptResultMap;
import xyz.erupt.toolkit.utils.EruptDaoUtils;
import xyz.erupt.toolkit.utils.NotifyUtils;
import xyz.erupt.toolkit.utils.SqlUtils;
import xyz.erupt.tpl.avue.core.AvueForm;
import xyz.erupt.tpl.avue.core.component.AvueCheckBox;
import xyz.erupt.tpl.avue.core.component.AvueProp;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Service
public class InspectionItemsManualSelectionHandler implements Tpl.TplHandler {


    @Override
    public void bindTplData(Map<String, Object> binding, String[] params) {

//        //获取企业信息
//        String enterpriseSql = "select enterprise_name,type from tb_enterprise_information where procedure_id=" + MetaDrill.getDrillId() + " and state=1";
//        List<EnterpriseInformation> enterpriseList = EruptDaoUtils.selectOnes(enterpriseSql, EnterpriseInformation.class);
//        if (enterpriseList.size() == 0) {
//            NotifyUtils.showErrorMsg("请先抽取企业");
//        }

        AvueForm form = AvueForm.builder().code("InspectionItemsManualSelection").build();
        String sql = "select distinct inspection_items from tb_inspection_items_management";
        List<EruptResultMap> types = EruptDaoUtils.selectOnes(sql, EruptResultMap.class);
        for (EruptResultMap ro : types) {
            AvueCheckBox checkBox = AvueCheckBox.builder()
                    .label(ro.getString("inspection_items"))
                    .prop(ro.getString("inspection_items"))
                    .labelWidth("240").row(true)
                    .build();
            //提取一级内容
            String firstSql = "select distinct check_first as label,check_first as value from tb_inspection_items_management where inspection_items=" + SqlUtils.wrapStr(checkBox.getProp());
            List<AvueProp> typeList = EruptDaoUtils.selectOnes(firstSql, AvueProp.class);
            for (AvueProp type : typeList) {
                checkBox.getDicData().add(type);
            }

            //看一下哪些被选中了
            String selectSql = "select distinct check_first from tb_inspection_items where procedure_id=" + MetaDrill.getDrillId() + " and state=1 and inspection_items=" + SqlUtils.wrapStr(checkBox.getProp());
            List<InspectionItems> items = EruptDaoUtils.selectOnes(selectSql, InspectionItems.class);
            List<String> selections = new ArrayList<>();
            for (InspectionItems item : items) {
                selections.add(item.getCheckFirst());
            }
            checkBox.setValue(selections);

            form.getColumn().add(checkBox);
        }
        binding.put("avueForm", JSONUtil.toJsonStr(form));
    }
}
