/*
* Copyright © 2020-2035 yundingyun All rights reserved.
*/
package com.daliangang.safedaily.operation;

import com.daliangang.safedaily.entity.StandardizationSenond;
import com.daliangang.safedaily.entity.StandardizationThird;
import org.springframework.stereotype.Service;
import xyz.erupt.annotation.fun.OperationHandler;
import xyz.erupt.jpa.dao.EruptDao;
import xyz.erupt.toolkit.utils.NotifyUtils;

import javax.annotation.Resource;
import javax.transaction.Transactional;
import java.util.Calendar;
import java.util.List;

@Service
public class StandardizationEscalationThirdHandler implements OperationHandler<StandardizationThird, Void> {
    @Resource
    EruptDao eruptDao;

   @Override
   @Transactional
   public String exec(List<StandardizationThird> data, Void unused, String[] param) {
       data.forEach(v->{
           Calendar calendar = Calendar.getInstance();
           String year = String.valueOf(calendar.get(Calendar.YEAR));
           if (!v.getYear().equals(year)) {
               NotifyUtils.showErrorMsg("未到提交年份！");
           }
           //  v.setIsReport(true);
           v.setSubmitted(true);
          // v.setIsReport(true);
           eruptDao.merge(v);
       });
       return NotifyUtils.getSuccessNotify("上报成功！");
	}
}
