package com.daliangang.workbench.controller;

import com.daliangang.workbench.entity.EmployeeSelfInfo;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;
import xyz.erupt.core.annotation.EruptRouter;
import xyz.erupt.core.view.TreeModel;
import xyz.erupt.jpa.dao.EruptDao;
import xyz.erupt.toolkit.utils.SqlUtils;
import xyz.erupt.upms.model.EruptUser;
import xyz.erupt.upms.service.EruptUserService;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.Collection;


@RestController
public class EmployeeSelfInfoController {

    @Resource
    private EruptDao eruptDao;
    @Resource
    private EruptUserService eruptUserService;

    //获取员工信息初始化数据
    @GetMapping("/erupt-api/data/tree/EmployeeSelfInfo")
    @EruptRouter(authIndex = 2, verifyType = EruptRouter.VerifyType.ERUPT)
    public Collection<TreeModel> initEruptValue() {

        EruptUser eruptUser = eruptUserService.getCurrentEruptUser();
        EmployeeSelfInfo employeeSelfInfo = eruptDao.queryEntity(EmployeeSelfInfo.class, "phone = " + SqlUtils.wrapStr(eruptUser.getAccount()));
        TreeModel treeModel = new TreeModel(employeeSelfInfo != null ? employeeSelfInfo.getId() : 0,
                employeeSelfInfo != null ? employeeSelfInfo.getName() : "", null);
        return Arrays.asList(treeModel);
    }
}
