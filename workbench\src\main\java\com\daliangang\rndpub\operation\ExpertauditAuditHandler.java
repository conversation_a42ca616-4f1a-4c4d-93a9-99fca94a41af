/*
* Copyright © 2020-2035 yundingyun All rights reserved.
*/
package com.daliangang.rndpub.operation;

import org.springframework.stereotype.*;
import xyz.erupt.annotation.fun.*;
import java.util.*;
import com.daliangang.rndpub.entity.*;

@Service
public class ExpertauditAuditHandler implements OperationHandler<Expertaudit, Void> {
   @Override
   public String exec(List<Expertaudit> data, Void unused, String[] param) {
       return null;
	}
}