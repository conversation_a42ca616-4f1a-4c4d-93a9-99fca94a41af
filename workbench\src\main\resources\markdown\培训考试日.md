# OfflineTraining

本文档用于说明 培训考试日 的创建与更新。

# 1 创建 培训考试日 对象

请求方式：POST<br>
请求地址：/erupt-api/open-api/data/OfflineTraining

## 1.1 Body参数
参数说明：以JSON格式在Body体内提交

|  参数  |  类型  |  是否必填  |  说明  |  备注  |
| --- | --- | --- | --- | --- |
|  company  |  String  |  是  |  单位名称  |       |
|  trainName  |  String  |  是  |  培训名称  |       |
|  trainBegin  |  String  |  是  |  培训开始时间  |       |
|  trainEnd  |  String  |  是  |  培训结束时间  |       |
|  trainContent  |  String  |  是  |  培训内容  |       |
|  trainImage  |  String  |  是  |  培训封面  |       |
|  trainAddress  |  String  |  是  |  培训地址  |       |
|  trainMethod  |  String  |  是  |  培训方式  |       |
|  trainInstitutions  |  String  |  是  |  培训机构  |       |
|  trainContactName  |  String  |  是  |  培训联系人  |       |
|  trainContactPhone  |  String  |  是  |  联系电话  |       |
|  instructorProfile  |  String  |  是  |  讲师简介  |       |
|  admissionMaterials  |  String  |  是  |  参培资料  |       |
|  trainDepartments  |  String  |  是  |  组织部门  |       |
|  sheetForTrainer  |  String  |  是  |  参培人员签到表  |       |
|  fillingDate  |  Date  |  是  |  上报时间  |       |
|  submitted  |  Boolean  |  是  |  上报状态  |       |
|  orgCode  |  String  |  否  |  组织编码  |       |
|  id  |  Long  |  否  |    |       |
## 1.2 返回值
返回值说明：以JSON格式返回

|  参数  |  类型  |  说明  |  备注  |
| --- | --- | --- | --- |
| status  | 状态   |  SUCCESS/ERROR |  |
| promptWay  | 类型   |  MESSAGE，表示返回类型为消息 |   |
| message  | 消息内容   |  报错信息（如果发生错误） |   |
| data  | 携带数据   |  请求返回的数据（如果有数据）|   |
| errorIntercept  | 保留字段   |    |   |
