package com.daliangang.device.controller;

import com.daliangang.core.DaliangangContext;
import com.daliangang.device.entity.StorageTank;
import com.daliangang.device.entity.Warehouse;
import com.daliangang.device.entity.Yard;
import com.google.gson.internal.LinkedTreeMap;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import xyz.erupt.core.annotation.EruptRouter;
import xyz.erupt.core.module.MetaUserinfo;
import xyz.erupt.core.view.EruptApiModel;
import xyz.erupt.jpa.dao.EruptDao;
import xyz.erupt.toolkit.remote.RemoteProxyService;
import xyz.erupt.toolkit.utils.EruptDaoUtils;
import xyz.erupt.toolkit.utils.SqlUtils;

import javax.annotation.Resource;
import javax.transaction.Transactional;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @since :2023/3/30:13:20
 */
@RestController
public class StorageTankController {

    @Resource
   private EruptDao eruptDao;
    @Resource
   private RemoteProxyService remoteProxyService;

    /**
     * 获取企业附征货种名称
     * @param
     * @return
     */
    @RequestMapping("erupt-api/get/tankName")
    @EruptRouter(verifyType = EruptRouter.VerifyType.LOGIN)
    public EruptApiModel getGoodsType() {
        MetaUserinfo remoteUserInfo = remoteProxyService.getRemoteUserInfo();

//        Enterprise enterprise = EruptDaoUtils.selectOne("select * from tb_enterprise where org_code ='" +remoteUserInfo.getOrg()+"'", Enterprise.class);
//        if (ObjectUtils.isEmpty(enterprise)) {
//            return EruptApiModel.successApi();
//        }
        if(DaliangangContext.isDepartmentUser()){
            List<StorageTank> storageTanks = EruptDaoUtils.selectOnes("select * from tb_storage_tank ", StorageTank.class);
            if (ObjectUtils.isNotEmpty(storageTanks)) {
                List<LinkedTreeMap> list1 = new ArrayList<>();
                storageTanks.forEach(v->{
                    LinkedTreeMap map = new LinkedTreeMap();
                    map.put("code",v.getId());
                    map.put("name",v.getName());
                    list1.add(map);
                });

                return EruptApiModel.successApi(list1);
            }
        }
        List<StorageTank> storageTanks = EruptDaoUtils.selectOnes("select * from tb_storage_tank where org_code='" + remoteUserInfo.getOrg()+"'", StorageTank.class);
        if (ObjectUtils.isNotEmpty(storageTanks)) {
            List<LinkedTreeMap> list1 = new ArrayList<>();
            storageTanks.forEach(v->{
                LinkedTreeMap map = new LinkedTreeMap();
                map.put("code",v.getId());
                map.put("name",v.getName());
                list1.add(map);
            });

            return EruptApiModel.successApi(list1);
        }

        return EruptApiModel.successApi();
    }


    // 查询企业范围仓库
    @RequestMapping("erupt-api/get/selectWarehouseInfo")
    @EruptRouter(verifyType = EruptRouter.VerifyType.LOGIN)
    @Transactional
    public EruptApiModel selectWarehouseInfo() {
        MetaUserinfo remoteUserInfo = remoteProxyService.getRemoteUserInfo();
        String orgCode1 = SqlUtils.wrapIn(remoteUserInfo.getAuth());
        // 仓库数据
        List<Warehouse> Warehouse = EruptDaoUtils.selectOnes("select tw.id as id,tw.map as map,company,name  from tb_warehouse tw where org_code " + orgCode1 , Warehouse.class);
        return EruptApiModel.successApi(Warehouse);
    }

    // 查询企业范围堆场
    @RequestMapping("erupt-api/get/selectYardInfo")
    @EruptRouter(verifyType = EruptRouter.VerifyType.LOGIN)
    public EruptApiModel selectYardInfo() {
        MetaUserinfo remoteUserInfo = remoteProxyService.getRemoteUserInfo();
        String orgCode1 = SqlUtils.wrapIn(remoteUserInfo.getAuth());
        // 仓库数据
        List<Yard> yards = EruptDaoUtils.selectOnes("select tw.id as id,tw.map as map,company,name  from tb_yard tw where org_code " + orgCode1 , Yard.class);
        return EruptApiModel.successApi(yards);
    }

    // 查询企业范围储罐
    @RequestMapping("erupt-api/get/selectStorageTankInfo")
    @EruptRouter(verifyType = EruptRouter.VerifyType.LOGIN)
    public EruptApiModel selectStorageTankInfo() {
        MetaUserinfo remoteUserInfo = remoteProxyService.getRemoteUserInfo();
        String orgCode1 = SqlUtils.wrapIn(remoteUserInfo.getAuth());
        // 仓库数据
        List<StorageTank> storageTanks = EruptDaoUtils.selectOnes("select tw.id as id,tw.map as map,company,name from tb_storage_tank tw where org_code " + orgCode1 , StorageTank.class);
        return EruptApiModel.successApi(storageTanks);
    }

}
