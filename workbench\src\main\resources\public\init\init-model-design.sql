/*
 Navicat Premium Data Transfer

 Source Server         : localhost
 Source Server Type    : MySQL
 Source Server Version : 80027
 Source Host           : localhost:3306
 Source Schema         : erupt

 Target Server Type    : MySQL
 Target Server Version : 80027
 File Encoding         : 65001

 Date: 20/04/2023 13:56:12
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for s_model_button
-- ----------------------------
DROP TABLE IF EXISTS `s_model_button`;
CREATE TABLE `s_model_button` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `enabled` bit(1) NOT NULL COMMENT '是否可用',
  `text` varchar(255) DEFAULT NULL COMMENT '按钮文本',
  `type` varchar(255) DEFAULT NULL COMMENT '按钮类型',
  `class_id` bigint DEFAULT NULL,
  <PERSON><PERSON><PERSON><PERSON> KEY (`id`),
  <PERSON><PERSON><PERSON> `FKciixpyspxdbl2pg6uq81i0xt3` (`class_id`),
  CONSTRAINT `FKciixpyspxdbl2pg6uq81i0xt3` FOREIGN KEY (`class_id`) REFERENCES `s_model_class` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=517 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='权限按钮';

-- ----------------------------
-- Records of s_model_button
-- ----------------------------
BEGIN;
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (1, b'1', '新增主管部门', 'ADD', 1);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (2, b'1', '删除', 'DELETE', 1);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (3, b'1', '修改', 'UPDATE', 1);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (4, b'1', '详情', 'QUERY', 1);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (5, b'0', '导入', 'IMPORT', 1);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (6, b'0', '导出', 'EXPORT', 1);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (7, b'1', '新增', 'ADD', 2);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (8, b'1', '删除', 'DELETE', 2);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (9, b'1', '修改', 'UPDATE', 2);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (10, b'1', '详情', 'QUERY', 2);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (11, b'1', '导入企业', 'IMPORT', 2);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (12, b'1', '导出企业', 'EXPORT', 2);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (13, b'1', '新增', 'ADD', 3);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (14, b'1', '删除', 'DELETE', 3);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (15, b'1', '修改', 'UPDATE', 3);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (16, b'1', '详情', 'QUERY', 3);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (17, b'1', '导入', 'IMPORT', 3);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (18, b'1', '导出', 'EXPORT', 3);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (19, b'1', '新增', 'ADD', 4);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (20, b'1', '删除', 'DELETE', 4);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (21, b'1', '修改', 'UPDATE', 4);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (22, b'1', '详情', 'QUERY', 4);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (23, b'0', '导入', 'IMPORT', 4);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (24, b'0', '导出', 'EXPORT', 4);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (25, b'1', '新增', 'ADD', 5);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (26, b'1', '删除', 'DELETE', 5);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (27, b'1', '修改', 'UPDATE', 5);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (28, b'1', '详情', 'QUERY', 5);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (29, b'1', '导入', 'IMPORT', 5);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (30, b'0', '导出', 'EXPORT', 5);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (31, b'1', '新增', 'ADD', 6);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (32, b'1', '删除', 'DELETE', 6);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (33, b'1', '修改', 'UPDATE', 6);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (34, b'1', '详情', 'QUERY', 6);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (35, b'1', '导入', 'IMPORT', 6);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (36, b'0', '导出', 'EXPORT', 6);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (37, b'1', '新增', 'ADD', 7);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (38, b'1', '删除', 'DELETE', 7);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (39, b'1', '修改', 'UPDATE', 7);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (40, b'1', '详情', 'QUERY', 7);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (41, b'0', '导入', 'IMPORT', 7);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (42, b'0', '导出', 'EXPORT', 7);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (43, b'1', '新增', 'ADD', 8);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (44, b'1', '删除', 'DELETE', 8);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (45, b'1', '修改', 'UPDATE', 8);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (46, b'1', '详情', 'QUERY', 8);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (47, b'0', '导入', 'IMPORT', 8);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (48, b'0', '导出', 'EXPORT', 8);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (49, b'1', '新增', 'ADD', 9);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (50, b'1', '删除', 'DELETE', 9);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (51, b'1', '修改', 'UPDATE', 9);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (52, b'1', '详情', 'QUERY', 9);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (53, b'0', '导入', 'IMPORT', 9);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (54, b'0', '导出', 'EXPORT', 9);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (55, b'1', '新增', 'ADD', 10);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (56, b'1', '删除', 'DELETE', 10);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (57, b'1', '修改', 'UPDATE', 10);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (58, b'1', '详情', 'QUERY', 10);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (59, b'1', '导入', 'IMPORT', 10);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (60, b'0', '导出', 'EXPORT', 10);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (61, b'1', '新增', 'ADD', 11);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (62, b'1', '删除', 'DELETE', 11);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (63, b'1', '修改', 'UPDATE', 11);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (64, b'1', '详情', 'QUERY', 11);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (65, b'0', '导入', 'IMPORT', 11);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (66, b'0', '导出', 'EXPORT', 11);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (67, b'1', '新增', 'ADD', 12);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (68, b'1', '删除', 'DELETE', 12);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (69, b'1', '修改', 'UPDATE', 12);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (70, b'1', '详情', 'QUERY', 12);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (71, b'0', '导入', 'IMPORT', 12);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (72, b'0', '导出', 'EXPORT', 12);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (73, b'1', '新增', 'ADD', 13);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (74, b'1', '删除', 'DELETE', 13);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (75, b'1', '修改', 'UPDATE', 13);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (76, b'1', '详情', 'QUERY', 13);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (77, b'0', '导入', 'IMPORT', 13);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (78, b'0', '导出', 'EXPORT', 13);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (79, b'1', '新增', 'ADD', 14);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (80, b'1', '删除', 'DELETE', 14);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (81, b'1', '修改', 'UPDATE', 14);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (82, b'1', '详情', 'QUERY', 14);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (83, b'0', '导入', 'IMPORT', 14);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (84, b'0', '导出', 'EXPORT', 14);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (85, b'1', '新增', 'ADD', 15);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (86, b'1', '删除', 'DELETE', 15);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (87, b'1', '修改', 'UPDATE', 15);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (88, b'1', '详情', 'QUERY', 15);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (89, b'0', '导入', 'IMPORT', 15);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (90, b'0', '导出', 'EXPORT', 15);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (91, b'1', '新增', 'ADD', 16);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (92, b'1', '删除', 'DELETE', 16);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (93, b'1', '修改', 'UPDATE', 16);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (94, b'1', '详情', 'QUERY', 16);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (95, b'0', '导入', 'IMPORT', 16);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (96, b'0', '导出', 'EXPORT', 16);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (97, b'1', '新增', 'ADD', 17);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (98, b'1', '删除', 'DELETE', 17);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (99, b'1', '修改', 'UPDATE', 17);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (100, b'1', '详情', 'QUERY', 17);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (101, b'1', '导入', 'IMPORT', 17);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (102, b'0', '导出', 'EXPORT', 17);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (103, b'1', '新增', 'ADD', 18);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (104, b'1', '删除', 'DELETE', 18);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (105, b'1', '修改', 'UPDATE', 18);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (106, b'1', '详情', 'QUERY', 18);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (107, b'0', '导入', 'IMPORT', 18);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (108, b'0', '导出', 'EXPORT', 18);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (109, b'1', '新增', 'ADD', 19);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (110, b'1', '删除', 'DELETE', 19);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (111, b'1', '修改', 'UPDATE', 19);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (112, b'1', '详情', 'QUERY', 19);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (113, b'0', '导入', 'IMPORT', 19);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (114, b'0', '导出', 'EXPORT', 19);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (115, b'1', '新增', 'ADD', 20);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (116, b'1', '删除', 'DELETE', 20);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (117, b'1', '修改', 'UPDATE', 20);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (118, b'1', '详情', 'QUERY', 20);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (119, b'0', '导入', 'IMPORT', 20);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (120, b'0', '导出', 'EXPORT', 20);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (121, b'1', '新增', 'ADD', 21);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (122, b'1', '删除', 'DELETE', 21);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (123, b'1', '修改', 'UPDATE', 21);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (124, b'1', '详情', 'QUERY', 21);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (125, b'0', '导入', 'IMPORT', 21);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (126, b'0', '导出', 'EXPORT', 21);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (127, b'1', '新增', 'ADD', 22);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (128, b'1', '删除', 'DELETE', 22);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (129, b'1', '修改', 'UPDATE', 22);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (130, b'1', '详情', 'QUERY', 22);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (131, b'0', '导入', 'IMPORT', 22);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (132, b'0', '导出', 'EXPORT', 22);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (133, b'1', '新增', 'ADD', 23);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (134, b'1', '删除', 'DELETE', 23);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (135, b'1', '修改', 'UPDATE', 23);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (136, b'1', '详情', 'QUERY', 23);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (137, b'1', '导入', 'IMPORT', 23);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (138, b'0', '导出', 'EXPORT', 23);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (139, b'1', '新增', 'ADD', 24);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (140, b'1', '删除', 'DELETE', 24);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (141, b'1', '修改', 'UPDATE', 24);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (142, b'1', '详情', 'QUERY', 24);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (143, b'0', '导入', 'IMPORT', 24);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (144, b'0', '导出', 'EXPORT', 24);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (145, b'1', '新增', 'ADD', 25);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (146, b'1', '删除', 'DELETE', 25);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (147, b'1', '修改', 'UPDATE', 25);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (148, b'1', '详情', 'QUERY', 25);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (149, b'0', '导入', 'IMPORT', 25);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (150, b'0', '导出', 'EXPORT', 25);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (151, b'1', '新增', 'ADD', 26);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (152, b'1', '删除', 'DELETE', 26);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (153, b'1', '修改', 'UPDATE', 26);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (154, b'1', '详情', 'QUERY', 26);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (155, b'0', '导入', 'IMPORT', 26);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (156, b'0', '导出', 'EXPORT', 26);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (157, b'1', '新增', 'ADD', 27);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (158, b'1', '删除', 'DELETE', 27);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (159, b'1', '修改', 'UPDATE', 27);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (160, b'1', '详情', 'QUERY', 27);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (161, b'0', '导入', 'IMPORT', 27);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (162, b'0', '导出', 'EXPORT', 27);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (163, b'1', '新增', 'ADD', 28);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (164, b'1', '删除', 'DELETE', 28);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (165, b'1', '修改', 'UPDATE', 28);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (166, b'1', '详情', 'QUERY', 28);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (167, b'0', '导入', 'IMPORT', 28);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (168, b'0', '导出', 'EXPORT', 28);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (169, b'1', '新增', 'ADD', 29);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (170, b'1', '删除', 'DELETE', 29);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (171, b'1', '修改', 'UPDATE', 29);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (172, b'1', '详情', 'QUERY', 29);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (173, b'0', '导入', 'IMPORT', 29);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (174, b'0', '导出', 'EXPORT', 29);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (175, b'1', '新增', 'ADD', 30);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (176, b'1', '删除', 'DELETE', 30);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (177, b'1', '修改', 'UPDATE', 30);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (178, b'1', '详情', 'QUERY', 30);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (179, b'0', '导入', 'IMPORT', 30);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (180, b'0', '导出', 'EXPORT', 30);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (181, b'1', '新增', 'ADD', 31);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (182, b'1', '删除', 'DELETE', 31);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (183, b'1', '修改', 'UPDATE', 31);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (184, b'1', '详情', 'QUERY', 31);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (185, b'0', '导入', 'IMPORT', 31);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (186, b'0', '导出', 'EXPORT', 31);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (187, b'1', '新增', 'ADD', 32);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (188, b'1', '删除', 'DELETE', 32);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (189, b'1', '修改', 'UPDATE', 32);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (190, b'1', '详情', 'QUERY', 32);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (191, b'0', '导入', 'IMPORT', 32);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (192, b'0', '导出', 'EXPORT', 32);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (193, b'1', '新增', 'ADD', 33);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (194, b'1', '删除', 'DELETE', 33);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (195, b'1', '修改', 'UPDATE', 33);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (196, b'1', '详情', 'QUERY', 33);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (197, b'0', '导入', 'IMPORT', 33);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (198, b'0', '导出', 'EXPORT', 33);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (199, b'1', '新增', 'ADD', 34);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (200, b'1', '删除', 'DELETE', 34);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (201, b'1', '修改', 'UPDATE', 34);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (202, b'1', '详情', 'QUERY', 34);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (203, b'0', '导入', 'IMPORT', 34);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (204, b'0', '导出', 'EXPORT', 34);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (205, b'1', '新增', 'ADD', 35);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (206, b'1', '删除', 'DELETE', 35);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (207, b'1', '修改', 'UPDATE', 35);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (208, b'1', '详情', 'QUERY', 35);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (209, b'0', '导入', 'IMPORT', 35);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (210, b'0', '导出', 'EXPORT', 35);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (211, b'1', '新增', 'ADD', 36);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (212, b'1', '删除', 'DELETE', 36);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (213, b'1', '修改', 'UPDATE', 36);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (214, b'1', '详情', 'QUERY', 36);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (215, b'0', '导入', 'IMPORT', 36);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (216, b'0', '导出', 'EXPORT', 36);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (217, b'1', '新增', 'ADD', 37);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (218, b'1', '删除', 'DELETE', 37);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (219, b'1', '修改', 'UPDATE', 37);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (220, b'1', '详情', 'QUERY', 37);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (221, b'0', '导入', 'IMPORT', 37);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (222, b'0', '导出', 'EXPORT', 37);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (223, b'1', '新增', 'ADD', 38);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (224, b'1', '删除', 'DELETE', 38);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (225, b'1', '修改', 'UPDATE', 38);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (226, b'1', '详情', 'QUERY', 38);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (227, b'0', '导入', 'IMPORT', 38);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (228, b'0', '导出', 'EXPORT', 38);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (229, b'1', '新增', 'ADD', 39);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (230, b'1', '删除', 'DELETE', 39);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (231, b'1', '修改', 'UPDATE', 39);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (232, b'1', '详情', 'QUERY', 39);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (233, b'0', '导入', 'IMPORT', 39);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (234, b'0', '导出', 'EXPORT', 39);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (235, b'1', '新增', 'ADD', 40);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (236, b'1', '删除', 'DELETE', 40);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (237, b'1', '修改', 'UPDATE', 40);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (238, b'1', '详情', 'QUERY', 40);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (239, b'0', '导入', 'IMPORT', 40);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (240, b'0', '导出', 'EXPORT', 40);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (241, b'1', '新增', 'ADD', 41);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (242, b'1', '删除', 'DELETE', 41);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (243, b'1', '修改', 'UPDATE', 41);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (244, b'1', '详情', 'QUERY', 41);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (245, b'0', '导入', 'IMPORT', 41);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (246, b'0', '导出', 'EXPORT', 41);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (247, b'1', '新增', 'ADD', 42);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (248, b'1', '删除', 'DELETE', 42);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (249, b'1', '修改', 'UPDATE', 42);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (250, b'1', '详情', 'QUERY', 42);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (251, b'0', '导入', 'IMPORT', 42);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (252, b'0', '导出', 'EXPORT', 42);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (253, b'1', '新增', 'ADD', 43);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (254, b'1', '删除', 'DELETE', 43);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (255, b'1', '修改', 'UPDATE', 43);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (256, b'1', '详情', 'QUERY', 43);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (257, b'0', '导入', 'IMPORT', 43);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (258, b'0', '导出', 'EXPORT', 43);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (259, b'1', '新增', 'ADD', 44);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (260, b'1', '删除', 'DELETE', 44);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (261, b'1', '修改', 'UPDATE', 44);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (262, b'1', '详情', 'QUERY', 44);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (263, b'0', '导入', 'IMPORT', 44);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (264, b'0', '导出', 'EXPORT', 44);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (265, b'1', '新增', 'ADD', 45);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (266, b'1', '删除', 'DELETE', 45);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (267, b'1', '修改', 'UPDATE', 45);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (268, b'1', '详情', 'QUERY', 45);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (269, b'0', '导入', 'IMPORT', 45);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (270, b'0', '导出', 'EXPORT', 45);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (271, b'1', '新增', 'ADD', 46);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (272, b'1', '删除', 'DELETE', 46);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (273, b'1', '修改', 'UPDATE', 46);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (274, b'1', '详情', 'QUERY', 46);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (275, b'0', '导入', 'IMPORT', 46);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (276, b'0', '导出', 'EXPORT', 46);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (277, b'1', '新增', 'ADD', 47);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (278, b'1', '删除', 'DELETE', 47);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (279, b'1', '修改', 'UPDATE', 47);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (280, b'1', '详情', 'QUERY', 47);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (281, b'0', '导入', 'IMPORT', 47);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (282, b'0', '导出', 'EXPORT', 47);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (283, b'1', '新增', 'ADD', 48);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (284, b'1', '删除', 'DELETE', 48);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (285, b'1', '修改', 'UPDATE', 48);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (286, b'1', '详情', 'QUERY', 48);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (287, b'0', '导入', 'IMPORT', 48);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (288, b'0', '导出', 'EXPORT', 48);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (289, b'1', '新增', 'ADD', 49);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (290, b'1', '删除', 'DELETE', 49);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (291, b'1', '修改', 'UPDATE', 49);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (292, b'1', '详情', 'QUERY', 49);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (293, b'0', '导入', 'IMPORT', 49);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (294, b'0', '导出', 'EXPORT', 49);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (295, b'0', '新增', 'ADD', 50);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (296, b'0', '删除', 'DELETE', 50);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (297, b'1', '修改', 'UPDATE', 50);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (298, b'1', '详情', 'QUERY', 50);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (299, b'0', '导入', 'IMPORT', 50);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (300, b'0', '导出', 'EXPORT', 50);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (301, b'1', '新增', 'ADD', 51);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (302, b'1', '删除', 'DELETE', 51);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (303, b'1', '修改', 'UPDATE', 51);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (304, b'1', '详情', 'QUERY', 51);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (305, b'0', '导入', 'IMPORT', 51);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (306, b'0', '导出', 'EXPORT', 51);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (307, b'1', '新增', 'ADD', 52);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (308, b'1', '删除', 'DELETE', 52);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (309, b'1', '修改', 'UPDATE', 52);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (310, b'1', '详情', 'QUERY', 52);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (311, b'0', '导入', 'IMPORT', 52);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (312, b'0', '导出', 'EXPORT', 52);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (313, b'1', '新增', 'ADD', 53);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (314, b'1', '删除', 'DELETE', 53);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (315, b'1', '修改', 'UPDATE', 53);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (316, b'1', '详情', 'QUERY', 53);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (317, b'0', '导入', 'IMPORT', 53);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (318, b'0', '导出', 'EXPORT', 53);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (319, b'1', '新增', 'ADD', 54);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (320, b'1', '删除', 'DELETE', 54);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (321, b'1', '修改', 'UPDATE', 54);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (322, b'1', '详情', 'QUERY', 54);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (323, b'0', '导入', 'IMPORT', 54);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (324, b'0', '导出', 'EXPORT', 54);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (325, b'1', '新增', 'ADD', 55);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (326, b'1', '删除', 'DELETE', 55);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (327, b'1', '修改', 'UPDATE', 55);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (328, b'1', '详情', 'QUERY', 55);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (329, b'0', '导入', 'IMPORT', 55);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (330, b'0', '导出', 'EXPORT', 55);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (331, b'1', '新增', 'ADD', 56);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (332, b'1', '删除', 'DELETE', 56);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (333, b'1', '修改', 'UPDATE', 56);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (334, b'1', '详情', 'QUERY', 56);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (335, b'1', '导入', 'IMPORT', 56);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (336, b'1', '导出', 'EXPORT', 56);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (337, b'1', '新增', 'ADD', 57);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (338, b'1', '删除', 'DELETE', 57);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (339, b'1', '修改', 'UPDATE', 57);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (340, b'1', '详情', 'QUERY', 57);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (341, b'0', '导入', 'IMPORT', 57);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (342, b'0', '导出', 'EXPORT', 57);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (343, b'1', '新增', 'ADD', 58);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (344, b'1', '删除', 'DELETE', 58);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (345, b'1', '修改', 'UPDATE', 58);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (346, b'1', '详情', 'QUERY', 58);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (347, b'1', '导入', 'IMPORT', 58);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (348, b'0', '导出', 'EXPORT', 58);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (349, b'1', '新增', 'ADD', 59);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (350, b'1', '删除', 'DELETE', 59);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (351, b'1', '修改', 'UPDATE', 59);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (352, b'1', '详情', 'QUERY', 59);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (353, b'1', '导入', 'IMPORT', 59);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (354, b'0', '导出', 'EXPORT', 59);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (355, b'1', '新增', 'ADD', 60);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (356, b'1', '删除', 'DELETE', 60);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (357, b'1', '修改', 'UPDATE', 60);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (358, b'1', '详情', 'QUERY', 60);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (359, b'1', '导入', 'IMPORT', 60);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (360, b'0', '导出', 'EXPORT', 60);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (361, b'1', '新增', 'ADD', 61);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (362, b'1', '删除', 'DELETE', 61);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (363, b'1', '修改', 'UPDATE', 61);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (364, b'1', '详情', 'QUERY', 61);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (365, b'1', '导入', 'IMPORT', 61);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (366, b'0', '导出', 'EXPORT', 61);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (367, b'1', '新增', 'ADD', 62);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (368, b'1', '删除', 'DELETE', 62);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (369, b'1', '修改', 'UPDATE', 62);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (370, b'1', '详情', 'QUERY', 62);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (371, b'1', '导入', 'IMPORT', 62);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (372, b'0', '导出', 'EXPORT', 62);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (373, b'1', '新增', 'ADD', 63);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (374, b'1', '删除', 'DELETE', 63);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (375, b'1', '修改', 'UPDATE', 63);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (376, b'1', '详情', 'QUERY', 63);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (377, b'1', '导入', 'IMPORT', 63);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (378, b'0', '导出', 'EXPORT', 63);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (379, b'1', '新增', 'ADD', 64);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (380, b'1', '删除', 'DELETE', 64);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (381, b'1', '修改', 'UPDATE', 64);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (382, b'1', '详情', 'QUERY', 64);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (383, b'0', '导入', 'IMPORT', 64);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (384, b'0', '导出', 'EXPORT', 64);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (385, b'1', '新增', 'ADD', 65);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (386, b'1', '删除', 'DELETE', 65);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (387, b'1', '修改', 'UPDATE', 65);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (388, b'1', '详情', 'QUERY', 65);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (389, b'0', '导入', 'IMPORT', 65);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (390, b'0', '导出', 'EXPORT', 65);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (391, b'1', '新增', 'ADD', 66);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (392, b'1', '删除', 'DELETE', 66);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (393, b'1', '修改', 'UPDATE', 66);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (394, b'1', '详情', 'QUERY', 66);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (395, b'0', '导入', 'IMPORT', 66);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (396, b'0', '导出', 'EXPORT', 66);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (397, b'1', '新增', 'ADD', 67);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (398, b'1', '删除', 'DELETE', 67);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (399, b'1', '修改', 'UPDATE', 67);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (400, b'1', '详情', 'QUERY', 67);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (401, b'0', '导入', 'IMPORT', 67);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (402, b'0', '导出', 'EXPORT', 67);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (403, b'1', '新增', 'ADD', 68);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (404, b'1', '删除', 'DELETE', 68);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (405, b'1', '修改', 'UPDATE', 68);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (406, b'1', '详情', 'QUERY', 68);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (407, b'0', '导入', 'IMPORT', 68);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (408, b'0', '导出', 'EXPORT', 68);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (409, b'1', '新增', 'ADD', 69);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (410, b'1', '删除', 'DELETE', 69);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (411, b'1', '修改', 'UPDATE', 69);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (412, b'1', '详情', 'QUERY', 69);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (413, b'1', '导入', 'IMPORT', 69);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (414, b'0', '导出', 'EXPORT', 69);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (415, b'1', '新增', 'ADD', 70);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (416, b'1', '删除', 'DELETE', 70);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (417, b'1', '修改', 'UPDATE', 70);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (418, b'1', '详情', 'QUERY', 70);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (419, b'1', '导入', 'IMPORT', 70);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (420, b'1', '导出', 'EXPORT', 70);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (421, b'1', '新增', 'ADD', 71);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (422, b'1', '删除', 'DELETE', 71);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (423, b'1', '修改', 'UPDATE', 71);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (424, b'1', '详情', 'QUERY', 71);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (425, b'0', '导入', 'IMPORT', 71);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (426, b'0', '导出', 'EXPORT', 71);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (427, b'1', '新增', 'ADD', 72);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (428, b'1', '删除', 'DELETE', 72);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (429, b'1', '修改', 'UPDATE', 72);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (430, b'1', '详情', 'QUERY', 72);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (431, b'0', '导入', 'IMPORT', 72);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (432, b'0', '导出', 'EXPORT', 72);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (433, b'1', '新增', 'ADD', 73);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (434, b'1', '删除', 'DELETE', 73);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (435, b'1', '修改', 'UPDATE', 73);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (436, b'1', '详情', 'QUERY', 73);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (437, b'1', '导入', 'IMPORT', 73);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (438, b'0', '导出', 'EXPORT', 73);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (439, b'1', '新增', 'ADD', 74);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (440, b'1', '删除', 'DELETE', 74);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (441, b'1', '修改', 'UPDATE', 74);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (442, b'1', '详情', 'QUERY', 74);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (443, b'1', '导入', 'IMPORT', 74);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (444, b'0', '导出', 'EXPORT', 74);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (445, b'1', '新增', 'ADD', 75);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (446, b'1', '删除', 'DELETE', 75);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (447, b'1', '修改', 'UPDATE', 75);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (448, b'1', '详情', 'QUERY', 75);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (449, b'0', '导入', 'IMPORT', 75);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (450, b'0', '导出', 'EXPORT', 75);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (451, b'1', '新增', 'ADD', 76);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (452, b'1', '删除', 'DELETE', 76);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (453, b'1', '修改', 'UPDATE', 76);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (454, b'1', '详情', 'QUERY', 76);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (455, b'0', '导入', 'IMPORT', 76);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (456, b'0', '导出', 'EXPORT', 76);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (457, b'1', '新增', 'ADD', 77);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (458, b'1', '删除', 'DELETE', 77);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (459, b'1', '修改', 'UPDATE', 77);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (460, b'1', '详情', 'QUERY', 77);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (461, b'0', '导入', 'IMPORT', 77);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (462, b'0', '导出', 'EXPORT', 77);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (463, b'1', '新增', 'ADD', 78);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (464, b'1', '删除', 'DELETE', 78);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (465, b'1', '修改', 'UPDATE', 78);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (466, b'1', '详情', 'QUERY', 78);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (467, b'0', '导入', 'IMPORT', 78);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (468, b'0', '导出', 'EXPORT', 78);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (469, b'1', '新增', 'ADD', 79);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (470, b'1', '删除', 'DELETE', 79);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (471, b'1', '修改', 'UPDATE', 79);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (472, b'1', '详情', 'QUERY', 79);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (473, b'0', '导入', 'IMPORT', 79);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (474, b'0', '导出', 'EXPORT', 79);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (475, b'1', '新增', 'ADD', 80);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (476, b'1', '删除', 'DELETE', 80);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (477, b'1', '修改', 'UPDATE', 80);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (478, b'1', '详情', 'QUERY', 80);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (479, b'0', '导入', 'IMPORT', 80);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (480, b'0', '导出', 'EXPORT', 80);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (481, b'1', '新增', 'ADD', 81);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (482, b'1', '删除', 'DELETE', 81);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (483, b'1', '修改', 'UPDATE', 81);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (484, b'1', '详情', 'QUERY', 81);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (485, b'0', '导入', 'IMPORT', 81);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (486, b'0', '导出', 'EXPORT', 81);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (487, b'1', '新增', 'ADD', 82);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (488, b'1', '删除', 'DELETE', 82);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (489, b'1', '修改', 'UPDATE', 82);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (490, b'1', '详情', 'QUERY', 82);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (491, b'0', '导入', 'IMPORT', 82);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (492, b'0', '导出', 'EXPORT', 82);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (493, b'1', '新增', 'ADD', 83);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (494, b'1', '删除', 'DELETE', 83);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (495, b'1', '修改', 'UPDATE', 83);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (496, b'1', '详情', 'QUERY', 83);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (497, b'0', '导入', 'IMPORT', 83);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (498, b'0', '导出', 'EXPORT', 83);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (499, b'1', '新增', 'ADD', 84);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (500, b'1', '删除', 'DELETE', 84);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (501, b'1', '修改', 'UPDATE', 84);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (502, b'1', '详情', 'QUERY', 84);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (503, b'1', '导入', 'IMPORT', 84);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (504, b'0', '导出', 'EXPORT', 84);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (505, b'0', '新增', 'ADD', 85);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (506, b'0', '删除', 'DELETE', 85);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (507, b'1', '修改', 'UPDATE', 85);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (508, b'1', '详情', 'QUERY', 85);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (509, b'0', '导入', 'IMPORT', 85);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (510, b'0', '导出', 'EXPORT', 85);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (511, b'1', '新增', 'ADD', 86);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (512, b'1', '删除', 'DELETE', 86);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (513, b'1', '修改', 'UPDATE', 86);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (514, b'1', '详情', 'QUERY', 86);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (515, b'0', '导入', 'IMPORT', 86);
INSERT INTO `s_model_button` (`id`, `enabled`, `text`, `type`, `class_id`) VALUES (516, b'0', '导出', 'EXPORT', 86);
COMMIT;

-- ----------------------------
-- Table structure for s_model_class
-- ----------------------------
DROP TABLE IF EXISTS `s_model_class`;
CREATE TABLE `s_model_class` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `create_by` varchar(255) DEFAULT NULL,
  `create_time` datetime DEFAULT NULL,
  `update_by` varchar(255) DEFAULT NULL,
  `update_time` datetime DEFAULT NULL,
  `class_name` varchar(255) DEFAULT NULL COMMENT '实体类名',
  `indexes` varchar(255) DEFAULT NULL COMMENT '索引键',
  `menu` varchar(255) DEFAULT NULL COMMENT '当前菜单',
  `module` varchar(255) DEFAULT NULL COMMENT '模块',
  `name` varchar(255) DEFAULT NULL COMMENT '名称',
  `package_name` varchar(255) DEFAULT NULL COMMENT '包名',
  `page_button1` varchar(255) DEFAULT NULL COMMENT '页按钮1',
  `page_button2` varchar(255) DEFAULT NULL COMMENT '页按钮2',
  `page_button3` varchar(255) DEFAULT NULL COMMENT '页按钮3',
  `page_button4` varchar(255) DEFAULT NULL COMMENT '页按钮4',
  `parent_menu` varchar(255) DEFAULT NULL COMMENT '父菜单',
  `remark` varchar(2000) DEFAULT NULL COMMENT '简介',
  `source` varchar(255) DEFAULT NULL COMMENT '来源',
  `table_name` varchar(255) DEFAULT NULL COMMENT '表名',
  `uniques` varchar(255) DEFAULT NULL COMMENT '唯一键',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=87 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='模型管理';

-- ----------------------------
-- Records of s_model_class
-- ----------------------------
BEGIN;
INSERT INTO `s_model_class` (`id`, `create_by`, `create_time`, `update_by`, `update_time`, `class_name`, `indexes`, `menu`, `module`, `name`, `package_name`, `page_button1`, `page_button2`, `page_button3`, `page_button4`, `parent_menu`, `remark`, `source`, `table_name`, `uniques`) VALUES (1, NULL, NULL, NULL, NULL, 'Department', '', '主管部门|Tree', 'workbench', '主管部门', 'com.daliangang.workbench', '', '', '', '', '企业管理', '企业管理下主管部门', '1危险货物港区重大安全风险管控平台门户', 'tb_department', 'name');
INSERT INTO `s_model_class` (`id`, `create_by`, `create_time`, `update_by`, `update_time`, `class_name`, `indexes`, `menu`, `module`, `name`, `package_name`, `page_button1`, `page_button2`, `page_button3`, `page_button4`, `parent_menu`, `remark`, `source`, `table_name`, `uniques`) VALUES (2, NULL, NULL, NULL, NULL, 'Enterprise', '', '企业列表', 'workbench', '企业信息', 'com.daliangang.workbench', '导入附证|fa-arrow-down', '导出附证|fa-arrow-up', '同步货种信息|fa-compress ', '同步港区信息|fa-compress ', '企业管理', '企业管理下企业列表', '1危险货物港区重大安全风险管控平台门户', 'tb_enterprise', 'name');
INSERT INTO `s_model_class` (`id`, `create_by`, `create_time`, `update_by`, `update_time`, `class_name`, `indexes`, `menu`, `module`, `name`, `package_name`, `page_button1`, `page_button2`, `page_button3`, `page_button4`, `parent_menu`, `remark`, `source`, `table_name`, `uniques`) VALUES (3, NULL, NULL, NULL, NULL, 'EnterpriseCertificate', '', 'HIDDEN', 'workbench', '企业附证', 'com.daliangang.workbench', '', '', '', '', '企业管理', '企业列表附证', '1危险货物港区重大安全风险管控平台门户', 'tb_enterprise_certificate', '');
INSERT INTO `s_model_class` (`id`, `create_by`, `create_time`, `update_by`, `update_time`, `class_name`, `indexes`, `menu`, `module`, `name`, `package_name`, `page_button1`, `page_button2`, `page_button3`, `page_button4`, `parent_menu`, `remark`, `source`, `table_name`, `uniques`) VALUES (4, NULL, NULL, NULL, NULL, 'Job', '', '岗位管理', 'workbench', '岗位信息', 'com.daliangang.workbench', '', '', '', '', '企业管理', '企业管理下岗位管理', '1危险货物港区重大安全风险管控平台门户', 'tb_job', '');
INSERT INTO `s_model_class` (`id`, `create_by`, `create_time`, `update_by`, `update_time`, `class_name`, `indexes`, `menu`, `module`, `name`, `package_name`, `page_button1`, `page_button2`, `page_button3`, `page_button4`, `parent_menu`, `remark`, `source`, `table_name`, `uniques`) VALUES (5, NULL, NULL, NULL, NULL, 'EmployeeInformation', '', '员工信息', 'workbench', '员工信息', 'com.daliangang.workbench', '', '', '', '', '企业管理', '企业管理下员工信息', '1危险货物港区重大安全风险管控平台门户', 'tb_employee_information', '');
INSERT INTO `s_model_class` (`id`, `create_by`, `create_time`, `update_by`, `update_time`, `class_name`, `indexes`, `menu`, `module`, `name`, `package_name`, `page_button1`, `page_button2`, `page_button3`, `page_button4`, `parent_menu`, `remark`, `source`, `table_name`, `uniques`) VALUES (6, NULL, NULL, NULL, NULL, 'EmployeeCertificate', '', 'HIDDEN', 'workbench', '员工证书', 'com.daliangang.workbench', '附件延期|fa-history', '', '', '', '企业管理', '企业管理下员工证书', '1危险货物港区重大安全风险管控平台门户', 'tb_employee_certificate', '');
INSERT INTO `s_model_class` (`id`, `create_by`, `create_time`, `update_by`, `update_time`, `class_name`, `indexes`, `menu`, `module`, `name`, `package_name`, `page_button1`, `page_button2`, `page_button3`, `page_button4`, `parent_menu`, `remark`, `source`, `table_name`, `uniques`) VALUES (7, NULL, NULL, NULL, NULL, 'ContentManagement', '', '内容管理', 'workbench', '内容管理', 'com.daliangang.workbench', '', '', '', '', '门户管理', '门户管理下内容管理', '1危险货物港区重大安全风险管控平台门户', 'tb_content_management', '');
INSERT INTO `s_model_class` (`id`, `create_by`, `create_time`, `update_by`, `update_time`, `class_name`, `indexes`, `menu`, `module`, `name`, `package_name`, `page_button1`, `page_button2`, `page_button3`, `page_button4`, `parent_menu`, `remark`, `source`, `table_name`, `uniques`) VALUES (8, NULL, NULL, NULL, NULL, 'CheckFill', '', '安全生产责任制', 'safe-daily', '检查填报', 'com.daliangang.safedaily', '', '', '', '', '四项机制', '四项机制下检查填报', '2港口危险货物安全日常业务监管系统', 'tb_check_fill', '');
INSERT INTO `s_model_class` (`id`, `create_by`, `create_time`, `update_by`, `update_time`, `class_name`, `indexes`, `menu`, `module`, `name`, `package_name`, `page_button1`, `page_button2`, `page_button3`, `page_button4`, `parent_menu`, `remark`, `source`, `table_name`, `uniques`) VALUES (9, NULL, NULL, NULL, NULL, 'Release', '', '安全承诺公告', 'safe-daily', '发布公告', 'com.daliangang.safedaily', '', '', '', '', '安全承诺公告管理|四项机制', '四项机制下发布公告', '2港口危险货物安全日常业务监管系统', 'tb_release', '');
INSERT INTO `s_model_class` (`id`, `create_by`, `create_time`, `update_by`, `update_time`, `class_name`, `indexes`, `menu`, `module`, `name`, `package_name`, `page_button1`, `page_button2`, `page_button3`, `page_button4`, `parent_menu`, `remark`, `source`, `table_name`, `uniques`) VALUES (10, NULL, NULL, NULL, NULL, 'Report', '', '可靠性报告单', 'safe-daily', '可靠性报告单', 'com.daliangang.safedaily', '', '', '', '', '四项机制', '四项机制下可靠性报告单', '2港口危险货物安全日常业务监管系统', 'tb_report', '');
INSERT INTO `s_model_class` (`id`, `create_by`, `create_time`, `update_by`, `update_time`, `class_name`, `indexes`, `menu`, `module`, `name`, `package_name`, `page_button1`, `page_button2`, `page_button3`, `page_button4`, `parent_menu`, `remark`, `source`, `table_name`, `uniques`) VALUES (11, NULL, NULL, NULL, NULL, 'DailyInspection', '', '日检查', 'safe-daily', '日检查', 'com.daliangang.safedaily', '', '', '', '', '安全检查管理', '检查管理下日检查', '2港口危险货物安全日常业务监管系统', 'tb_daily_inspection', '');
INSERT INTO `s_model_class` (`id`, `create_by`, `create_time`, `update_by`, `update_time`, `class_name`, `indexes`, `menu`, `module`, `name`, `package_name`, `page_button1`, `page_button2`, `page_button3`, `page_button4`, `parent_menu`, `remark`, `source`, `table_name`, `uniques`) VALUES (12, NULL, NULL, NULL, NULL, 'WeeklyReport', '', '周报告', 'safe-daily', '周报告', 'com.daliangang.safedaily', '', '', '', '', '安全检查管理', '检查管理下周报告', '2港口危险货物安全日常业务监管系统', 'tb_weekly_report', '');
INSERT INTO `s_model_class` (`id`, `create_by`, `create_time`, `update_by`, `update_time`, `class_name`, `indexes`, `menu`, `module`, `name`, `package_name`, `page_button1`, `page_button2`, `page_button3`, `page_button4`, `parent_menu`, `remark`, `source`, `table_name`, `uniques`) VALUES (13, NULL, NULL, NULL, NULL, 'MonthlyScheduling', '', '月调度', 'safe-daily', '月调度', 'com.daliangang.safedaily', '', '', '', '', '安全检查管理', '检查管理下月调度', '2港口危险货物安全日常业务监管系统', 'tb_monthly_scheduling', '');
INSERT INTO `s_model_class` (`id`, `create_by`, `create_time`, `update_by`, `update_time`, `class_name`, `indexes`, `menu`, `module`, `name`, `package_name`, `page_button1`, `page_button2`, `page_button3`, `page_button4`, `parent_menu`, `remark`, `source`, `table_name`, `uniques`) VALUES (14, NULL, NULL, NULL, NULL, 'PlanManagement', '', '预案管理', 'safe-daily', '预案管理', 'com.daliangang.safedaily', '', '', '', '', '备案管理', '备案管理下预案管理', '2港口危险货物安全日常业务监管系统', 'tb_plan_management', '');
INSERT INTO `s_model_class` (`id`, `create_by`, `create_time`, `update_by`, `update_time`, `class_name`, `indexes`, `menu`, `module`, `name`, `package_name`, `page_button1`, `page_button2`, `page_button3`, `page_button4`, `parent_menu`, `remark`, `source`, `table_name`, `uniques`) VALUES (15, NULL, NULL, NULL, NULL, 'Preplan', '', '重大危险源备案', 'safe-daily', '重大危险源备案', 'com.daliangang.safedaily', '', '', '', '', '备案管理', '备案管理下重大危险源备案', '2港口危险货物安全日常业务监管系统', 'tb_preplan', '');
INSERT INTO `s_model_class` (`id`, `create_by`, `create_time`, `update_by`, `update_time`, `class_name`, `indexes`, `menu`, `module`, `name`, `package_name`, `page_button1`, `page_button2`, `page_button3`, `page_button4`, `parent_menu`, `remark`, `source`, `table_name`, `uniques`) VALUES (16, NULL, NULL, NULL, NULL, 'Standardization', '', '港口企业安全生产标准化管理', 'safe-daily', '港口企业安全生产标准化管理', 'com.daliangang.safedaily', '', '', '', '', '港口企业安全生产标准化', '港口企业安全生产标准化管理', '2港口危险货物安全日常业务监管系统', 'tb_standardization', '');
INSERT INTO `s_model_class` (`id`, `create_by`, `create_time`, `update_by`, `update_time`, `class_name`, `indexes`, `menu`, `module`, `name`, `package_name`, `page_button1`, `page_button2`, `page_button3`, `page_button4`, `parent_menu`, `remark`, `source`, `table_name`, `uniques`) VALUES (17, NULL, NULL, NULL, NULL, 'EvaluationInformation', '', '评价表信息', 'safe-daily', '评价表信息', 'com.daliangang.safedaily', '', '', '', '', '港口企业安全生产标准化', '港口企业安全生产标准化管理下评价表信息', '2港口危险货物安全日常业务监管系统', 'tb_evaluation_information', '');
INSERT INTO `s_model_class` (`id`, `create_by`, `create_time`, `update_by`, `update_time`, `class_name`, `indexes`, `menu`, `module`, `name`, `package_name`, `page_button1`, `page_button2`, `page_button3`, `page_button4`, `parent_menu`, `remark`, `source`, `table_name`, `uniques`) VALUES (18, NULL, NULL, NULL, NULL, 'PortRecord', '', '港口安全评价备案管理', 'safe-daily', '港口安全评价备案管理', 'com.daliangang.safedaily', '', '', '', '', '备案管理', '备案管理下港口安全评价备案管理', '2港口危险货物安全日常业务监管系统', 'tb_port_record', '');
INSERT INTO `s_model_class` (`id`, `create_by`, `create_time`, `update_by`, `update_time`, `class_name`, `indexes`, `menu`, `module`, `name`, `package_name`, `page_button1`, `page_button2`, `page_button3`, `page_button4`, `parent_menu`, `remark`, `source`, `table_name`, `uniques`) VALUES (19, NULL, NULL, NULL, NULL, 'Record', '', '安全条件审查', 'safe-daily', '安全条件审查', 'com.daliangang.safedaily', '', '', '', '', '备案管理', '备案管理下安全条件审查', '2港口危险货物安全日常业务监管系统', 'tb_record', '');
INSERT INTO `s_model_class` (`id`, `create_by`, `create_time`, `update_by`, `update_time`, `class_name`, `indexes`, `menu`, `module`, `name`, `package_name`, `page_button1`, `page_button2`, `page_button3`, `page_button4`, `parent_menu`, `remark`, `source`, `table_name`, `uniques`) VALUES (20, NULL, NULL, NULL, NULL, 'Design', '', '设施设计审查', 'safe-daily', '设施设计审查', 'com.daliangang.safedaily', '', '', '', '', '备案管理', '备案管理下设施设计审查', '2港口危险货物安全日常业务监管系统', 'tb_design', '');
INSERT INTO `s_model_class` (`id`, `create_by`, `create_time`, `update_by`, `update_time`, `class_name`, `indexes`, `menu`, `module`, `name`, `package_name`, `page_button1`, `page_button2`, `page_button3`, `page_button4`, `parent_menu`, `remark`, `source`, `table_name`, `uniques`) VALUES (21, NULL, NULL, NULL, NULL, 'Security', '', '港口设施保安备案管理', 'safe-daily', '港口设施保安备案管理', 'com.daliangang.safedaily', '', '', '', '', '备案管理', '备案管理下港口设施保安备案管理', '2港口危险货物安全日常业务监管系统', 'tb_security', '');
INSERT INTO `s_model_class` (`id`, `create_by`, `create_time`, `update_by`, `update_time`, `class_name`, `indexes`, `menu`, `module`, `name`, `package_name`, `page_button1`, `page_button2`, `page_button3`, `page_button4`, `parent_menu`, `remark`, `source`, `table_name`, `uniques`) VALUES (22, NULL, NULL, NULL, NULL, 'QualificationManage', '', '作业资质年度检查管理', 'safe-daily', '作业资质年度检查管理', 'com.daliangang.safedaily', '', '', '', '', '备案管理', '备案管理下作业资质年度检查管理', '2港口危险货物安全日常业务监管系统', 'tb_qualification_manage', '');
INSERT INTO `s_model_class` (`id`, `create_by`, `create_time`, `update_by`, `update_time`, `class_name`, `indexes`, `menu`, `module`, `name`, `package_name`, `page_button1`, `page_button2`, `page_button3`, `page_button4`, `parent_menu`, `remark`, `source`, `table_name`, `uniques`) VALUES (23, NULL, NULL, NULL, NULL, 'Knowledge', '', '知识库', 'safe-daily', '港口安全知识库分类', 'com.daliangang.safedaily', '', '', '', '', '', '港口安全知识库分类', '2港口危险货物安全日常业务监管系统', 'tb_knowledge', '');
INSERT INTO `s_model_class` (`id`, `create_by`, `create_time`, `update_by`, `update_time`, `class_name`, `indexes`, `menu`, `module`, `name`, `package_name`, `page_button1`, `page_button2`, `page_button3`, `page_button4`, `parent_menu`, `remark`, `source`, `table_name`, `uniques`) VALUES (24, NULL, NULL, NULL, NULL, 'MSDS', '', 'MSDS', 'safe-daily', 'MSDS', 'com.daliangang.safedaily', '', '', '', '', '', 'MSDS', '2港口危险货物安全日常业务监管系统', 'tb_m_s_d_s', '');
INSERT INTO `s_model_class` (`id`, `create_by`, `create_time`, `update_by`, `update_time`, `class_name`, `indexes`, `menu`, `module`, `name`, `package_name`, `page_button1`, `page_button2`, `page_button3`, `page_button4`, `parent_menu`, `remark`, `source`, `table_name`, `uniques`) VALUES (25, NULL, NULL, NULL, NULL, 'PortArea', '', '港区管理', 'device', '港区管理', 'com.daliangang.device', '', '', '', '', '设施管理', '港区管理', '3.港口设施维护管理系统', 'tb_port_area', '');
INSERT INTO `s_model_class` (`id`, `create_by`, `create_time`, `update_by`, `update_time`, `class_name`, `indexes`, `menu`, `module`, `name`, `package_name`, `page_button1`, `page_button2`, `page_button3`, `page_button4`, `parent_menu`, `remark`, `source`, `table_name`, `uniques`) VALUES (26, NULL, NULL, NULL, NULL, 'Wharf', '', '码头管理', 'device', '码头管理', 'com.daliangang.device', '', '', '', '', '设施管理', '码头管理', '3.港口设施维护管理系统', 'tb_wharf', '');
INSERT INTO `s_model_class` (`id`, `create_by`, `create_time`, `update_by`, `update_time`, `class_name`, `indexes`, `menu`, `module`, `name`, `package_name`, `page_button1`, `page_button2`, `page_button3`, `page_button4`, `parent_menu`, `remark`, `source`, `table_name`, `uniques`) VALUES (27, NULL, NULL, NULL, NULL, 'Berth', '', '泊位管理', 'device', '泊位管理', 'com.daliangang.device', '', '', '', '', '设施管理', '泊位管理', '3.港口设施维护管理系统', 'tb_berth', '');
INSERT INTO `s_model_class` (`id`, `create_by`, `create_time`, `update_by`, `update_time`, `class_name`, `indexes`, `menu`, `module`, `name`, `package_name`, `page_button1`, `page_button2`, `page_button3`, `page_button4`, `parent_menu`, `remark`, `source`, `table_name`, `uniques`) VALUES (28, NULL, NULL, NULL, NULL, 'Anchorage', '', '锚地管理', 'device', '锚地管理', 'com.daliangang.device', '', '', '', '', '设施管理', '锚地管理', '3.港口设施维护管理系统', 'tb_anchorage', '');
INSERT INTO `s_model_class` (`id`, `create_by`, `create_time`, `update_by`, `update_time`, `class_name`, `indexes`, `menu`, `module`, `name`, `package_name`, `page_button1`, `page_button2`, `page_button3`, `page_button4`, `parent_menu`, `remark`, `source`, `table_name`, `uniques`) VALUES (29, NULL, NULL, NULL, NULL, 'Arrival', '', '进港航道管理', 'device', '进港航道管理', 'com.daliangang.device', '', '', '', '', '设施管理', '进港航道管理', '3.港口设施维护管理系统', 'tb_arrival', '');
INSERT INTO `s_model_class` (`id`, `create_by`, `create_time`, `update_by`, `update_time`, `class_name`, `indexes`, `menu`, `module`, `name`, `package_name`, `page_button1`, `page_button2`, `page_button3`, `page_button4`, `parent_menu`, `remark`, `source`, `table_name`, `uniques`) VALUES (30, NULL, NULL, NULL, NULL, 'Railway', '', '港区铁路管理', 'device', '港区铁路管理', 'com.daliangang.device', '', '', '', '', '设施管理', '港区铁路管理', '3.港口设施维护管理系统', 'tb_railway', '');
INSERT INTO `s_model_class` (`id`, `create_by`, `create_time`, `update_by`, `update_time`, `class_name`, `indexes`, `menu`, `module`, `name`, `package_name`, `page_button1`, `page_button2`, `page_button3`, `page_button4`, `parent_menu`, `remark`, `source`, `table_name`, `uniques`) VALUES (31, NULL, NULL, NULL, NULL, 'Highway', '', '疏港公路管理', 'device', '疏港公路管理', 'com.daliangang.device', '', '', '', '', '设施管理', '疏港公路管理', '3.港口设施维护管理系统', 'tb_highway', '');
INSERT INTO `s_model_class` (`id`, `create_by`, `create_time`, `update_by`, `update_time`, `class_name`, `indexes`, `menu`, `module`, `name`, `package_name`, `page_button1`, `page_button2`, `page_button3`, `page_button4`, `parent_menu`, `remark`, `source`, `table_name`, `uniques`) VALUES (32, NULL, NULL, NULL, NULL, 'TankFarm', '', '罐区管理', 'device', '罐区管理', 'com.daliangang.device', '', '', '', '', '设施管理', '罐区管理', '3.港口设施维护管理系统', 'tb_tank_farm', '');
INSERT INTO `s_model_class` (`id`, `create_by`, `create_time`, `update_by`, `update_time`, `class_name`, `indexes`, `menu`, `module`, `name`, `package_name`, `page_button1`, `page_button2`, `page_button3`, `page_button4`, `parent_menu`, `remark`, `source`, `table_name`, `uniques`) VALUES (33, NULL, NULL, NULL, NULL, 'TankGroup', '', '罐组管理', 'device', '罐组管理', 'com.daliangang.device', '', '', '', '', '设施管理', '罐组管理', '3.港口设施维护管理系统', 'tb_tank_group', '');
INSERT INTO `s_model_class` (`id`, `create_by`, `create_time`, `update_by`, `update_time`, `class_name`, `indexes`, `menu`, `module`, `name`, `package_name`, `page_button1`, `page_button2`, `page_button3`, `page_button4`, `parent_menu`, `remark`, `source`, `table_name`, `uniques`) VALUES (34, NULL, NULL, NULL, NULL, 'StorageTank', '', '储罐管理', 'device', '储罐管理', 'com.daliangang.device', '', '', '', '', '设施管理', '储罐管理', '3.港口设施维护管理系统', 'tb_storage_tank', '');
INSERT INTO `s_model_class` (`id`, `create_by`, `create_time`, `update_by`, `update_time`, `class_name`, `indexes`, `menu`, `module`, `name`, `package_name`, `page_button1`, `page_button2`, `page_button3`, `page_button4`, `parent_menu`, `remark`, `source`, `table_name`, `uniques`) VALUES (35, NULL, NULL, NULL, NULL, 'LoadingDock', '', '装卸栈台管理', 'device', '装卸栈台管理', 'com.daliangang.device', '', '', '', '', '设施管理', '装卸栈台管理', '3.港口设施维护管理系统', 'tb_loading_dock', '');
INSERT INTO `s_model_class` (`id`, `create_by`, `create_time`, `update_by`, `update_time`, `class_name`, `indexes`, `menu`, `module`, `name`, `package_name`, `page_button1`, `page_button2`, `page_button3`, `page_button4`, `parent_menu`, `remark`, `source`, `table_name`, `uniques`) VALUES (36, NULL, NULL, NULL, NULL, 'Yard', '', '堆场管理', 'device', '堆场管理', 'com.daliangang.device', '', '', '', '', '设施管理', '堆场管理', '3.港口设施维护管理系统', 'tb_yard', '');
INSERT INTO `s_model_class` (`id`, `create_by`, `create_time`, `update_by`, `update_time`, `class_name`, `indexes`, `menu`, `module`, `name`, `package_name`, `page_button1`, `page_button2`, `page_button3`, `page_button4`, `parent_menu`, `remark`, `source`, `table_name`, `uniques`) VALUES (37, NULL, NULL, NULL, NULL, 'Warehouse', '', '仓库管理', 'device', '仓库管理', 'com.daliangang.device', '', '', '', '', '设施管理', '仓库管理', '3.港口设施维护管理系统', 'tb_warehouse', '');
INSERT INTO `s_model_class` (`id`, `create_by`, `create_time`, `update_by`, `update_time`, `class_name`, `indexes`, `menu`, `module`, `name`, `package_name`, `page_button1`, `page_button2`, `page_button3`, `page_button4`, `parent_menu`, `remark`, `source`, `table_name`, `uniques`) VALUES (38, NULL, NULL, NULL, NULL, 'WharfStructureInspection', '', '码头结构检测', 'device', '码头结构检测', 'com.daliangang.device', '', '', '', '', '设备设施检测信息管理', '码头结构检测', '3.港口设施维护管理系统', 'tb_wharf_structure_inspection', '');
INSERT INTO `s_model_class` (`id`, `create_by`, `create_time`, `update_by`, `update_time`, `class_name`, `indexes`, `menu`, `module`, `name`, `package_name`, `page_button1`, `page_button2`, `page_button3`, `page_button4`, `parent_menu`, `remark`, `source`, `table_name`, `uniques`) VALUES (39, NULL, NULL, NULL, NULL, 'FacilityInspection', '', '消防设施检测', 'device', '消防设施检测', 'com.daliangang.device', '', '', '', '', '设备设施检测信息管理', '消防设施检测', '3.港口设施维护管理系统', 'tb_facility_inspection', '');
INSERT INTO `s_model_class` (`id`, `create_by`, `create_time`, `update_by`, `update_time`, `class_name`, `indexes`, `menu`, `module`, `name`, `package_name`, `page_button1`, `page_button2`, `page_button3`, `page_button4`, `parent_menu`, `remark`, `source`, `table_name`, `uniques`) VALUES (40, NULL, NULL, NULL, NULL, 'LightningProtection', '', '防雷装置检测', 'device', '防雷装置检测', 'com.daliangang.device', '', '', '', '', '设备设施检测信息管理', '防雷装置检测', '3.港口设施维护管理系统', 'tb_lightning_protection', '');
INSERT INTO `s_model_class` (`id`, `create_by`, `create_time`, `update_by`, `update_time`, `class_name`, `indexes`, `menu`, `module`, `name`, `package_name`, `page_button1`, `page_button2`, `page_button3`, `page_button4`, `parent_menu`, `remark`, `source`, `table_name`, `uniques`) VALUES (41, NULL, NULL, NULL, NULL, 'PressureVessel', '', '压力容器检测', 'device', '压力容器检测', 'com.daliangang.device', '', '', '', '', '设备设施检测信息管理', '压力容器检测', '3.港口设施维护管理系统', 'tb_pressure_vessel', '');
INSERT INTO `s_model_class` (`id`, `create_by`, `create_time`, `update_by`, `update_time`, `class_name`, `indexes`, `menu`, `module`, `name`, `package_name`, `page_button1`, `page_button2`, `page_button3`, `page_button4`, `parent_menu`, `remark`, `source`, `table_name`, `uniques`) VALUES (42, NULL, NULL, NULL, NULL, 'TankInspection', '', '储罐检测', 'device', '储罐检测', 'com.daliangang.device', '', '', '', '', '设备设施检测信息管理', '储罐检测', '3.港口设施维护管理系统', 'tb_tank_inspection', '');
INSERT INTO `s_model_class` (`id`, `create_by`, `create_time`, `update_by`, `update_time`, `class_name`, `indexes`, `menu`, `module`, `name`, `package_name`, `page_button1`, `page_button2`, `page_button3`, `page_button4`, `parent_menu`, `remark`, `source`, `table_name`, `uniques`) VALUES (43, NULL, NULL, NULL, NULL, 'Penstock', '', '压力管道检测', 'device', '压力管道检测', 'com.daliangang.device', '', '', '', '', '设备设施检测信息管理', '压力管道检测', '3.港口设施维护管理系统', 'tb_penstock', '');
INSERT INTO `s_model_class` (`id`, `create_by`, `create_time`, `update_by`, `update_time`, `class_name`, `indexes`, `menu`, `module`, `name`, `package_name`, `page_button1`, `page_button2`, `page_button3`, `page_button4`, `parent_menu`, `remark`, `source`, `table_name`, `uniques`) VALUES (44, NULL, NULL, NULL, NULL, 'Hose', '', '输油臂（软管）检测', 'device', '输油臂（软管）检测', 'com.daliangang.device', '', '', '', '', '设备设施检测信息管理', '输油臂（软管）检测', '3.港口设施维护管理系统', 'tb_hose', '');
INSERT INTO `s_model_class` (`id`, `create_by`, `create_time`, `update_by`, `update_time`, `class_name`, `indexes`, `menu`, `module`, `name`, `package_name`, `page_button1`, `page_button2`, `page_button3`, `page_button4`, `parent_menu`, `remark`, `source`, `table_name`, `uniques`) VALUES (45, NULL, NULL, NULL, NULL, 'PressureGauge', '', '压力表检测', 'device', '压力表检测', 'com.daliangang.device', '', '', '', '', '设备设施检测信息管理', '压力表检测', '3.港口设施维护管理系统', 'tb_pressure_gauge', '');
INSERT INTO `s_model_class` (`id`, `create_by`, `create_time`, `update_by`, `update_time`, `class_name`, `indexes`, `menu`, `module`, `name`, `package_name`, `page_button1`, `page_button2`, `page_button3`, `page_button4`, `parent_menu`, `remark`, `source`, `table_name`, `uniques`) VALUES (46, NULL, NULL, NULL, NULL, 'SafetyValve', '', '安全阀检测', 'device', '安全阀检测', 'com.daliangang.device', '', '', '', '', '设备设施检测信息管理', '安全阀检测', '3.港口设施维护管理系统', 'tb_safety_valve', '');
INSERT INTO `s_model_class` (`id`, `create_by`, `create_time`, `update_by`, `update_time`, `class_name`, `indexes`, `menu`, `module`, `name`, `package_name`, `page_button1`, `page_button2`, `page_button3`, `page_button4`, `parent_menu`, `remark`, `source`, `table_name`, `uniques`) VALUES (47, NULL, NULL, NULL, NULL, 'CombustibleGas', '', '可燃气体报警器检测', 'device', '可燃气体报警器检测', 'com.daliangang.device', '', '', '', '', '设备设施检测信息管理', '可燃气体报警器检测', '3.港口设施维护管理系统', 'tb_combustible_gas', '');
INSERT INTO `s_model_class` (`id`, `create_by`, `create_time`, `update_by`, `update_time`, `class_name`, `indexes`, `menu`, `module`, `name`, `package_name`, `page_button1`, `page_button2`, `page_button3`, `page_button4`, `parent_menu`, `remark`, `source`, `table_name`, `uniques`) VALUES (48, NULL, NULL, NULL, NULL, 'HoistingMachinery', '', '起重机械检测', 'device', '起重机械检测', 'com.daliangang.device', '', '', '', '', '设备设施检测信息管理', '起重机械检测', '3.港口设施维护管理系统', 'tb_hoisting_machinery', '');
INSERT INTO `s_model_class` (`id`, `create_by`, `create_time`, `update_by`, `update_time`, `class_name`, `indexes`, `menu`, `module`, `name`, `package_name`, `page_button1`, `page_button2`, `page_button3`, `page_button4`, `parent_menu`, `remark`, `source`, `table_name`, `uniques`) VALUES (49, NULL, NULL, NULL, NULL, 'OtherTests', '', '其他检测', 'device', '其他检测', 'com.daliangang.device', '', '', '', '', '设备设施检测信息管理', '其他检测', '3.港口设施维护管理系统', 'tb_other_tests', '');
INSERT INTO `s_model_class` (`id`, `create_by`, `create_time`, `update_by`, `update_time`, `class_name`, `indexes`, `menu`, `module`, `name`, `package_name`, `page_button1`, `page_button2`, `page_button3`, `page_button4`, `parent_menu`, `remark`, `source`, `table_name`, `uniques`) VALUES (50, NULL, NULL, NULL, NULL, 'Unload', '', '装卸船作业', 'major-risk', '装卸船作业', 'com.daliangang.majorisk', '危货申报系统| Example ofcloud-upload', '', '', '', '作业管理', '作业管理下装卸作业', '4港区重大风险监测与管控系统', 'tb_unload', '');
INSERT INTO `s_model_class` (`id`, `create_by`, `create_time`, `update_by`, `update_time`, `class_name`, `indexes`, `menu`, `module`, `name`, `package_name`, `page_button1`, `page_button2`, `page_button3`, `page_button4`, `parent_menu`, `remark`, `source`, `table_name`, `uniques`) VALUES (51, NULL, NULL, NULL, NULL, 'CarUnload', '', '装卸车作业', 'major-risk', '装卸车作业', 'com.daliangang.majorisk', '', '', '', '', '作业管理', '作业管理下装卸作业', '4港区重大风险监测与管控系统', 'tb_car_unload', '');
INSERT INTO `s_model_class` (`id`, `create_by`, `create_time`, `update_by`, `update_time`, `class_name`, `indexes`, `menu`, `module`, `name`, `package_name`, `page_button1`, `page_button2`, `page_button3`, `page_button4`, `parent_menu`, `remark`, `source`, `table_name`, `uniques`) VALUES (52, NULL, NULL, NULL, NULL, 'Maintenance', '', '检维修作业', 'major-risk', '检维修作业', 'com.daliangang.majorisk', '上报| Example ofcloud-upload', '', '', '', '作业管理', '作业管理下检维修作业', '4港区重大风险监测与管控系统', 'tb_maintenance', '');
INSERT INTO `s_model_class` (`id`, `create_by`, `create_time`, `update_by`, `update_time`, `class_name`, `indexes`, `menu`, `module`, `name`, `package_name`, `page_button1`, `page_button2`, `page_button3`, `page_button4`, `parent_menu`, `remark`, `source`, `table_name`, `uniques`) VALUES (53, NULL, NULL, NULL, NULL, 'ReserveReporting', '', '储罐每日储量上报', 'major-risk', '储罐每日储量上报', 'com.daliangang.majorisk', '上报| Example ofcloud-upload', '', '', '', '每日储量上报', '作业管理下每日储量上报', '4港区重大风险监测与管控系统', 'tb_reserve_reporting', '');
INSERT INTO `s_model_class` (`id`, `create_by`, `create_time`, `update_by`, `update_time`, `class_name`, `indexes`, `menu`, `module`, `name`, `package_name`, `page_button1`, `page_button2`, `page_button3`, `page_button4`, `parent_menu`, `remark`, `source`, `table_name`, `uniques`) VALUES (54, NULL, NULL, NULL, NULL, 'DcReserveReporting', '', '堆场每日储量上报', 'major-risk', '堆场每日储量上报', 'com.daliangang.majorisk', '上报| Example ofcloud-upload', '', '', '', '每日储量上报', '作业管理下每日储量上报', '4港区重大风险监测与管控系统', 'tb_dc_reserve_reporting', '');
INSERT INTO `s_model_class` (`id`, `create_by`, `create_time`, `update_by`, `update_time`, `class_name`, `indexes`, `menu`, `module`, `name`, `package_name`, `page_button1`, `page_button2`, `page_button3`, `page_button4`, `parent_menu`, `remark`, `source`, `table_name`, `uniques`) VALUES (55, NULL, NULL, NULL, NULL, 'CkReserveReporting', '', '仓库每日储量上报', 'major-risk', '仓库每日储量上报', 'com.daliangang.majorisk', '上报| Example ofcloud-upload', '', '', '', '每日储量上报', '作业管理下每日储量上报', '4港区重大风险监测与管控系统', 'tb_ck_reserve_reporting', '');
INSERT INTO `s_model_class` (`id`, `create_by`, `create_time`, `update_by`, `update_time`, `class_name`, `indexes`, `menu`, `module`, `name`, `package_name`, `page_button1`, `page_button2`, `page_button3`, `page_button4`, `parent_menu`, `remark`, `source`, `table_name`, `uniques`) VALUES (56, NULL, NULL, NULL, NULL, 'Database', '', '重大风险数据库', 'major-risk', '重大风险数据库', 'com.daliangang.majorisk', '添加分类|fa-address-book', '发布全部|fa-paper-plane', '', '', '', '重大风险数据库', '4港区重大风险监测与管控系统', 'tb_database', '');
INSERT INTO `s_model_class` (`id`, `create_by`, `create_time`, `update_by`, `update_time`, `class_name`, `indexes`, `menu`, `module`, `name`, `package_name`, `page_button1`, `page_button2`, `page_button3`, `page_button4`, `parent_menu`, `remark`, `source`, `table_name`, `uniques`) VALUES (57, NULL, NULL, NULL, NULL, 'Checkperson', '', '检查人员', 'rnd-public', '检查人员管理', 'com.daliangang.rndpub', '', '', '', '', '检查管理', '检查人员管理', '5.港区危险货物企业“双随机、一公开”系统', 'tb_checkperson', '');
INSERT INTO `s_model_class` (`id`, `create_by`, `create_time`, `update_by`, `update_time`, `class_name`, `indexes`, `menu`, `module`, `name`, `package_name`, `page_button1`, `page_button2`, `page_button3`, `page_button4`, `parent_menu`, `remark`, `source`, `table_name`, `uniques`) VALUES (58, NULL, NULL, NULL, NULL, 'Expert', '', '专家列表', 'rnd-public', '专家列表', 'com.daliangang.rndpub', '复制申请二维码|fa-copy', '', '', '', '专家管理|检查管理', '专家列表', '5.港区危险货物企业“双随机、一公开”系统', 'tb_expert', '');
INSERT INTO `s_model_class` (`id`, `create_by`, `create_time`, `update_by`, `update_time`, `class_name`, `indexes`, `menu`, `module`, `name`, `package_name`, `page_button1`, `page_button2`, `page_button3`, `page_button4`, `parent_menu`, `remark`, `source`, `table_name`, `uniques`) VALUES (59, NULL, NULL, NULL, NULL, 'Qualification', '', 'HIDDEN', 'rnd-public', '资质证书', 'com.daliangang.rndpub', '', '', '', '', '专家管理|检查管理', '专家列表下资质证书', '5.港区危险货物企业“双随机、一公开”系统', 'tb_qualification', '');
INSERT INTO `s_model_class` (`id`, `create_by`, `create_time`, `update_by`, `update_time`, `class_name`, `indexes`, `menu`, `module`, `name`, `package_name`, `page_button1`, `page_button2`, `page_button3`, `page_button4`, `parent_menu`, `remark`, `source`, `table_name`, `uniques`) VALUES (60, NULL, NULL, NULL, NULL, 'LawenForcement', '', 'HIDDEN', 'rnd-public', '执法信息', 'com.daliangang.rndpub', '', '', '', '', '专家管理|检查管理', '专家列表下执法信息', '5.港区危险货物企业“双随机、一公开”系统', 'tb_lawen_forcement', '');
INSERT INTO `s_model_class` (`id`, `create_by`, `create_time`, `update_by`, `update_time`, `class_name`, `indexes`, `menu`, `module`, `name`, `package_name`, `page_button1`, `page_button2`, `page_button3`, `page_button4`, `parent_menu`, `remark`, `source`, `table_name`, `uniques`) VALUES (61, NULL, NULL, NULL, NULL, 'Expertaudit', '', '专家审核', 'rnd-public', '专家审核', 'com.daliangang.rndpub', '', '', '', '', '专家审核管理|检查管理', '专家审核', '5.港区危险货物企业“双随机、一公开”系统', 'tb_expertaudit', '');
INSERT INTO `s_model_class` (`id`, `create_by`, `create_time`, `update_by`, `update_time`, `class_name`, `indexes`, `menu`, `module`, `name`, `package_name`, `page_button1`, `page_button2`, `page_button3`, `page_button4`, `parent_menu`, `remark`, `source`, `table_name`, `uniques`) VALUES (62, NULL, NULL, NULL, NULL, 'Auditcertificate', '', 'HIDDEN', 'rnd-public', '审核资质证书', 'com.daliangang.rndpub', '', '', '', '', '专家审核管理|检查管理', '专家审核下资质证书', '5.港区危险货物企业“双随机、一公开”系统', 'tb_auditcertificate', '');
INSERT INTO `s_model_class` (`id`, `create_by`, `create_time`, `update_by`, `update_time`, `class_name`, `indexes`, `menu`, `module`, `name`, `package_name`, `page_button1`, `page_button2`, `page_button3`, `page_button4`, `parent_menu`, `remark`, `source`, `table_name`, `uniques`) VALUES (63, NULL, NULL, NULL, NULL, 'InspectionItemsManagement', '', '检查事项管理', 'rnd-public', '检查事项管理', 'com.daliangang.rndpub', '新增分类|fa-address-book', '', '', '', '专家审核管理|检查管理', '检查事项管理', '5.港区危险货物企业“双随机、一公开”系统', 'tb_inspection_items_management', '');
INSERT INTO `s_model_class` (`id`, `create_by`, `create_time`, `update_by`, `update_time`, `class_name`, `indexes`, `menu`, `module`, `name`, `package_name`, `page_button1`, `page_button2`, `page_button3`, `page_button4`, `parent_menu`, `remark`, `source`, `table_name`, `uniques`) VALUES (64, NULL, NULL, NULL, NULL, 'Procedure', '', '检查流程', 'rnd-public', '检查流程', 'com.daliangang.rndpub', '', '', '', '', '检查流程管理|检查管理', '检查流程', '5.港区危险货物企业“双随机、一公开”系统', 'tb_procedure', '');
INSERT INTO `s_model_class` (`id`, `create_by`, `create_time`, `update_by`, `update_time`, `class_name`, `indexes`, `menu`, `module`, `name`, `package_name`, `page_button1`, `page_button2`, `page_button3`, `page_button4`, `parent_menu`, `remark`, `source`, `table_name`, `uniques`) VALUES (65, NULL, NULL, NULL, NULL, 'EnterpriseInformation', '', 'HIDDEN', 'rnd-public', '检查对象', 'com.daliangang.rndpub', '', '', '', '', '检查流程管理|检查管理', '检查流程下检查对象', '5.港区危险货物企业“双随机、一公开”系统', 'tb_enterprise_information', '');
INSERT INTO `s_model_class` (`id`, `create_by`, `create_time`, `update_by`, `update_time`, `class_name`, `indexes`, `menu`, `module`, `name`, `package_name`, `page_button1`, `page_button2`, `page_button3`, `page_button4`, `parent_menu`, `remark`, `source`, `table_name`, `uniques`) VALUES (66, NULL, NULL, NULL, NULL, 'ExpertInformation', '', 'HIDDEN', 'rnd-public', '执法专家', 'com.daliangang.rndpub', '', '', '', '', '检查流程管理|检查管理', '检查流程下执法专家', '5.港区危险货物企业“双随机、一公开”系统', 'tb_expert_information', '');
INSERT INTO `s_model_class` (`id`, `create_by`, `create_time`, `update_by`, `update_time`, `class_name`, `indexes`, `menu`, `module`, `name`, `package_name`, `page_button1`, `page_button2`, `page_button3`, `page_button4`, `parent_menu`, `remark`, `source`, `table_name`, `uniques`) VALUES (67, NULL, NULL, NULL, NULL, 'Inspector', '', 'HIDDEN', 'rnd-public', '检查人员', 'com.daliangang.rndpub', '', '', '', '', '检查流程管理|检查管理', '检查流程下检查人员', '5.港区危险货物企业“双随机、一公开”系统', 'tb_inspector', '');
INSERT INTO `s_model_class` (`id`, `create_by`, `create_time`, `update_by`, `update_time`, `class_name`, `indexes`, `menu`, `module`, `name`, `package_name`, `page_button1`, `page_button2`, `page_button3`, `page_button4`, `parent_menu`, `remark`, `source`, `table_name`, `uniques`) VALUES (68, NULL, NULL, NULL, NULL, 'InspectionItems', '', 'HIDDEN', 'rnd-public', '检查事项', 'com.daliangang.rndpub', '', '', '', '', '检查流程管理|检查管理', '检查流程下检查事项', '5.港区危险货物企业“双随机、一公开”系统', 'tb_inspection_items', '');
INSERT INTO `s_model_class` (`id`, `create_by`, `create_time`, `update_by`, `update_time`, `class_name`, `indexes`, `menu`, `module`, `name`, `package_name`, `page_button1`, `page_button2`, `page_button3`, `page_button4`, `parent_menu`, `remark`, `source`, `table_name`, `uniques`) VALUES (69, NULL, NULL, NULL, NULL, 'InspectionrResults', '', '检查结果', 'rnd-public', '检查结果', 'com.daliangang.rndpub', '', '', '', '', '检查管理', '检查结果', '5.港区危险货物企业“双随机、一公开”系统', 'tb_inspectionr_results', '');
INSERT INTO `s_model_class` (`id`, `create_by`, `create_time`, `update_by`, `update_time`, `class_name`, `indexes`, `menu`, `module`, `name`, `package_name`, `page_button1`, `page_button2`, `page_button3`, `page_button4`, `parent_menu`, `remark`, `source`, `table_name`, `uniques`) VALUES (70, NULL, NULL, NULL, NULL, 'Rectification', '', '整改管理', 'rnd-public', '整改管理', 'com.daliangang.rndpub', '', '', '', '', '检查管理', '整改管理', '5.港区危险货物企业“双随机、一公开”系统', 'tb_rectification', '');
INSERT INTO `s_model_class` (`id`, `create_by`, `create_time`, `update_by`, `update_time`, `class_name`, `indexes`, `menu`, `module`, `name`, `package_name`, `page_button1`, `page_button2`, `page_button3`, `page_button4`, `parent_menu`, `remark`, `source`, `table_name`, `uniques`) VALUES (71, NULL, NULL, NULL, NULL, 'CaseBase', '', '案例库管理', 'emergency-event', '案例库管理', 'com.daliangang.emergency', '', '', '', '', '', '案例库管理', '7.危险货物重大突发事件智慧管理系统', 'tb_case_base', '');
INSERT INTO `s_model_class` (`id`, `create_by`, `create_time`, `update_by`, `update_time`, `class_name`, `indexes`, `menu`, `module`, `name`, `package_name`, `page_button1`, `page_button2`, `page_button3`, `page_button4`, `parent_menu`, `remark`, `source`, `table_name`, `uniques`) VALUES (72, NULL, NULL, NULL, NULL, 'Drill', '', '演练管理', 'emergency-event', '演练管理', 'com.daliangang.emergency', '', '', '', '', '', '演练管理', '7.危险货物重大突发事件智慧管理系统', 'tb_drill', '');
INSERT INTO `s_model_class` (`id`, `create_by`, `create_time`, `update_by`, `update_time`, `class_name`, `indexes`, `menu`, `module`, `name`, `package_name`, `page_button1`, `page_button2`, `page_button3`, `page_button4`, `parent_menu`, `remark`, `source`, `table_name`, `uniques`) VALUES (73, NULL, NULL, NULL, NULL, 'EmergencyDuty', '', '应急值守管理', 'emergency-event', '应急值守管理', 'com.daliangang.emergency', '', '', '', '', '', '应急值守管理', '7.危险货物重大突发事件智慧管理系统', 'tb_emergency_duty', '');
INSERT INTO `s_model_class` (`id`, `create_by`, `create_time`, `update_by`, `update_time`, `class_name`, `indexes`, `menu`, `module`, `name`, `package_name`, `page_button1`, `page_button2`, `page_button3`, `page_button4`, `parent_menu`, `remark`, `source`, `table_name`, `uniques`) VALUES (74, NULL, NULL, NULL, NULL, 'KnowledgeBase', '', '应急知识库管理', 'emergency-event', '应急知识库管理', 'com.daliangang.emergency', '', '', '', '', '', '应急知识库管理', '7.危险货物重大突发事件智慧管理系统', 'tb_knowledge_base', '');
INSERT INTO `s_model_class` (`id`, `create_by`, `create_time`, `update_by`, `update_time`, `class_name`, `indexes`, `menu`, `module`, `name`, `package_name`, `page_button1`, `page_button2`, `page_button3`, `page_button4`, `parent_menu`, `remark`, `source`, `table_name`, `uniques`) VALUES (75, NULL, NULL, NULL, NULL, 'Rescueteam', '', '应急救援队伍管理', 'emergency-event', '应急救援队伍管理', 'com.daliangang.emergency', '', '', '', '', '应急资源管理', '应急救援队伍管理', '7.危险货物重大突发事件智慧管理系统', 'tb_rescueteam', '');
INSERT INTO `s_model_class` (`id`, `create_by`, `create_time`, `update_by`, `update_time`, `class_name`, `indexes`, `menu`, `module`, `name`, `package_name`, `page_button1`, `page_button2`, `page_button3`, `page_button4`, `parent_menu`, `remark`, `source`, `table_name`, `uniques`) VALUES (76, NULL, NULL, NULL, NULL, 'Sensitivetargets', '', '敏感目标管理', 'emergency-event', '敏感目标管理', 'com.daliangang.emergency', '', '', '', '', '应急资源管理', '敏感目标管理', '7.危险货物重大突发事件智慧管理系统', 'tb_sensitivetargets', '');
INSERT INTO `s_model_class` (`id`, `create_by`, `create_time`, `update_by`, `update_time`, `class_name`, `indexes`, `menu`, `module`, `name`, `package_name`, `page_button1`, `page_button2`, `page_button3`, `page_button4`, `parent_menu`, `remark`, `source`, `table_name`, `uniques`) VALUES (77, NULL, NULL, NULL, NULL, 'EmergencyMuster', '', '应急集合点管理', 'emergency-event', '应急集合点管理', 'com.daliangang.emergency', '', '', '', '', '应急资源管理', '应急集合点管理', '7.危险货物重大突发事件智慧管理系统', 'tb_emergency_muster', '');
INSERT INTO `s_model_class` (`id`, `create_by`, `create_time`, `update_by`, `update_time`, `class_name`, `indexes`, `menu`, `module`, `name`, `package_name`, `page_button1`, `page_button2`, `page_button3`, `page_button4`, `parent_menu`, `remark`, `source`, `table_name`, `uniques`) VALUES (78, NULL, NULL, NULL, NULL, 'Hospital', '', '医院管理', 'emergency-event', '医院管理', 'com.daliangang.emergency', '', '', '', '', '应急资源管理', '医院管理', '7.危险货物重大突发事件智慧管理系统', 'tb_hospital', '');
INSERT INTO `s_model_class` (`id`, `create_by`, `create_time`, `update_by`, `update_time`, `class_name`, `indexes`, `menu`, `module`, `name`, `package_name`, `page_button1`, `page_button2`, `page_button3`, `page_button4`, `parent_menu`, `remark`, `source`, `table_name`, `uniques`) VALUES (79, NULL, NULL, NULL, NULL, 'EmergencyPool', '', '事故应急池管理', 'emergency-event', '事故应急池管理', 'com.daliangang.emergency', '', '', '', '', '应急资源管理', '事故应急池管理', '7.危险货物重大突发事件智慧管理系统', 'tb_emergency_pool', '');
INSERT INTO `s_model_class` (`id`, `create_by`, `create_time`, `update_by`, `update_time`, `class_name`, `indexes`, `menu`, `module`, `name`, `package_name`, `page_button1`, `page_button2`, `page_button3`, `page_button4`, `parent_menu`, `remark`, `source`, `table_name`, `uniques`) VALUES (80, NULL, NULL, NULL, NULL, 'MaterialReserve', '', '应急物资储备点管理', 'emergency-event', '应急物资储备点管理', 'com.daliangang.emergency', '', '', '', '', '应急资源管理', '应急物资储备点管理', '7.危险货物重大突发事件智慧管理系统', 'tb_material_reserve', '');
INSERT INTO `s_model_class` (`id`, `create_by`, `create_time`, `update_by`, `update_time`, `class_name`, `indexes`, `menu`, `module`, `name`, `package_name`, `page_button1`, `page_button2`, `page_button3`, `page_button4`, `parent_menu`, `remark`, `source`, `table_name`, `uniques`) VALUES (81, NULL, NULL, NULL, NULL, 'EmergencyExpert', '', '应急专家管理', 'emergency-event', '应急专家管理', 'com.daliangang.emergency', '', '', '', '', '应急资源管理', '应急专家管理', '7.危险货物重大突发事件智慧管理系统', 'tb_emergency_expert', '');
INSERT INTO `s_model_class` (`id`, `create_by`, `create_time`, `update_by`, `update_time`, `class_name`, `indexes`, `menu`, `module`, `name`, `package_name`, `page_button1`, `page_button2`, `page_button3`, `page_button4`, `parent_menu`, `remark`, `source`, `table_name`, `uniques`) VALUES (82, NULL, NULL, NULL, NULL, 'Rescueassessment', '', '应急救援评估', 'emergency-event', '应急救援评估', 'com.daliangang.emergency', '', '', '', '', '', '应急救援评估', '7.危险货物重大突发事件智慧管理系统', 'tb_rescueassessment', '');
INSERT INTO `s_model_class` (`id`, `create_by`, `create_time`, `update_by`, `update_time`, `class_name`, `indexes`, `menu`, `module`, `name`, `package_name`, `page_button1`, `page_button2`, `page_button3`, `page_button4`, `parent_menu`, `remark`, `source`, `table_name`, `uniques`) VALUES (83, NULL, NULL, NULL, NULL, 'PrincipalElement', '', '主要素管理', 'emergency-event', '主要素管理', 'com.daliangang.emergency', '', '', '', '', '应急救援能力评估', '应急体系及能力评估下主要素管理', '7.危险货物重大突发事件智慧管理系统', 'tb_principal_element', '');
INSERT INTO `s_model_class` (`id`, `create_by`, `create_time`, `update_by`, `update_time`, `class_name`, `indexes`, `menu`, `module`, `name`, `package_name`, `page_button1`, `page_button2`, `page_button3`, `page_button4`, `parent_menu`, `remark`, `source`, `table_name`, `uniques`) VALUES (84, NULL, NULL, NULL, NULL, 'Evaluation', '', '评估表管理', 'emergency-event', '评估表管理', 'com.daliangang.emergency', '', '', '', '', '应急救援能力评估', '应急体系及能力评估下评估表管理', '7.危险货物重大突发事件智慧管理系统', 'tb_evaluation', '');
INSERT INTO `s_model_class` (`id`, `create_by`, `create_time`, `update_by`, `update_time`, `class_name`, `indexes`, `menu`, `module`, `name`, `package_name`, `page_button1`, `page_button2`, `page_button3`, `page_button4`, `parent_menu`, `remark`, `source`, `table_name`, `uniques`) VALUES (85, NULL, NULL, NULL, NULL, 'Grade', '', '评估等级管理', 'emergency-event', '评估等级管理', 'com.daliangang.emergency', '', '', '', '', '应急救援能力评估', '应急体系及能力评估下评估等级管理', '7.危险货物重大突发事件智慧管理系统', 'tb_grade', '');
INSERT INTO `s_model_class` (`id`, `create_by`, `create_time`, `update_by`, `update_time`, `class_name`, `indexes`, `menu`, `module`, `name`, `package_name`, `page_button1`, `page_button2`, `page_button3`, `page_button4`, `parent_menu`, `remark`, `source`, `table_name`, `uniques`) VALUES (86, NULL, NULL, NULL, NULL, 'EvaluationManage', '', '评估管理', 'emergency-event', '评估管理', 'com.daliangang.emergency', '', '', '', '', '应急救援能力评估', '应急体系及能力评估下评估管理', '7.危险货物重大突发事件智慧管理系统', 'tb_evaluation_manage', '');
COMMIT;

-- ----------------------------
-- Table structure for s_model_field
-- ----------------------------
DROP TABLE IF EXISTS `s_model_field`;
CREATE TABLE `s_model_field` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `check_submit` varchar(255) DEFAULT NULL COMMENT '提交检查',
  `check_update` bit(1) NOT NULL COMMENT '更新检查',
  `edit_show` bit(1) NOT NULL COMMENT '编辑显示',
  `field_name` varchar(255) DEFAULT NULL COMMENT '字段名',
  `link_class` varchar(255) DEFAULT NULL COMMENT '备注信息',
  `not_null` bit(1) DEFAULT NULL COMMENT '是否必填',
  `query` bit(1) DEFAULT NULL COMMENT '查询项',
  `read_only` varchar(255) DEFAULT NULL COMMENT '是否只读',
  `row_button1` varchar(255) DEFAULT NULL COMMENT '行按钮1',
  `row_button2` varchar(255) DEFAULT NULL COMMENT '行按钮2',
  `row_button3` varchar(255) DEFAULT NULL COMMENT '行按钮3',
  `row_button4` varchar(255) DEFAULT NULL COMMENT '行按钮4',
  `show_name` varchar(255) DEFAULT NULL COMMENT '显示名称',
  `sort` int DEFAULT NULL COMMENT '序号',
  `sortable` bit(1) DEFAULT NULL COMMENT '字段排序',
  `type` varchar(255) DEFAULT NULL COMMENT '编辑类型',
  `view_show` bit(1) NOT NULL COMMENT '列表显示',
  `class_id` bigint DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `FK920ykn5wk467sh81kiyv2hyxg` (`class_id`),
  CONSTRAINT `FK920ykn5wk467sh81kiyv2hyxg` FOREIGN KEY (`class_id`) REFERENCES `s_model_class` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=749 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='字段管理';

-- ----------------------------
-- Records of s_model_field
-- ----------------------------
BEGIN;
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (1, '', b'0', b'1', 'name', '', b'1', b'1', '', '', '', '', '', '主管部门名称', 1, b'1', 'INPUT', b'1', 1);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (2, '', b'0', b'1', 'portArea', '鲶鱼湾港区|北良港区|甘井子港区|大窑湾港区|大孤山南港区|旅顺港区|瓦房店港区|长兴岛港区|长海县', b'1', b'1', '', '', '', '', '', '所属港区', 2, b'0', 'CHOICE', b'1', 1);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (3, '', b'0', b'1', 'parent', 'Department', b'0', b'0', '', '', '', '', '', '上级部门', 3, b'0', 'REFERENCE_TREE', b'1', 1);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (4, '', b'0', b'1', 'name', '', b'1', b'1', 'FALSE|TRUE', '启用|fa-toggle-on', '重置密码|fa-key', '禁用|fa-toggle-on', '', '企业名称', 1, b'0', 'INPUT', b'1', 2);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (5, '', b'0', b'1', 'administrator', '', b'1', b'0', 'FALSE|TRUE', '', '', '', '', '管理员用户名', 2, b'0', 'INPUT', b'1', 2);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (6, '', b'0', b'1', 'type', '油品|集装箱|其他', b'1', b'1', 'FALSE|TRUE', '', '', '', '', '企业类别', 3, b'0', 'CHOICE', b'1', 2);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (7, '', b'0', b'1', 'role', 'xyz.erupt.template.model.EruptRoleTemplate', b'1', b'0', '', '', '', '', '', '选择角色', 4, b'0', 'REFERENCE_TREE', b'1', 2);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (8, '', b'0', b'1', 'portArea', '鲶鱼湾港区|北良港区|甘井子港区|大窑湾港区|大孤山南港区|旅顺港区|瓦房店港区|长兴岛港区|长海县', b'1', b'1', '', '', '', '', '', '所属港区', 5, b'0', 'CHOICE', b'1', 2);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (9, '', b'0', b'1', 'imDepartment', 'Department', b'1', b'0', '', '', '', '', '', '主管部门', 6, b'0', 'REFERENCE_TREE', b'1', 2);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (10, '', b'0', b'1', 'representative', '', b'0', b'0', '', '', '', '', '', '法定代表人', 7, b'0', 'INPUT', b'1', 2);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (11, '', b'0', b'1', 'socialCode', '', b'0', b'0', '', '', '', '', '', '统一社会信用代码', 8, b'0', 'INPUT', b'1', 2);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (12, '', b'0', b'1', 'termOperation', '', b'0', b'0', '', '', '', '', '', '营业期限', 9, b'0', 'INPUT', b'1', 2);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (13, '', b'0', b'1', 'registeredAddress', '', b'0', b'0', '', '', '', '', '', '注册地址', 10, b'0', 'INPUT', b'1', 2);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (14, '', b'0', b'1', 'businessScope', '', b'0', b'0', '', '', '', '', '', '经营范围', 11, b'0', 'INPUT', b'1', 2);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (15, '', b'0', b'1', 'portNo', '', b'1', b'0', '', '', '', '', '', '港经证编号', 12, b'0', 'INPUT', b'1', 2);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (16, '', b'0', b'1', 'validityPeriod', '', b'1', b'0', '', '', '', '', '', '有效期限', 13, b'0', 'DATE', b'1', 2);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (17, '', b'0', b'1', 'certificates', 'EnterpriseCertificate', b'1', b'0', '', '', '', '', '', '企业附证', 14, b'0', 'TAB_TABLE_ADD', b'1', 2);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (18, '', b'0', b'1', 'certificateNo', '', b'1', b'0', '', '', '', '', '', '附证编号', 1, b'0', 'INPUT', b'1', 3);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (19, '', b'0', b'1', 'expiredState', '正常|即将逾期|已过期', b'1', b'0', '', '', '', '', '', '状态', 2, b'0', 'BOOLEAN', b'1', 3);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (20, '', b'0', b'1', 'workRange', '', b'1', b'0', '', '', '', '', '', '作业范围', 3, b'0', 'INPUT', b'1', 3);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (21, '', b'0', b'1', 'operationMode', '', b'1', b'0', '', '', '', '', '', '作业方式', 4, b'0', 'INPUT', b'1', 3);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (22, '', b'0', b'1', 'goodsType', '', b'1', b'0', '', '', '', '', '', '货种名称', 5, b'0', 'TAGS', b'1', 3);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (23, '', b'0', b'1', 'issueDate', '', b'1', b'0', '', '', '', '', '', '发证日期', 6, b'0', 'DATE', b'1', 3);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (24, '', b'0', b'1', 'issuingAuthority', '', b'1', b'0', '', '', '', '', '', '发证机关', 7, b'0', 'INPUT', b'1', 3);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (25, '', b'0', b'1', 'expirationTime', '', b'1', b'0', '', '', '', '', '', '到期时间', 8, b'0', 'DATE', b'1', 3);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (26, '', b'0', b'1', 'title', '', b'1', b'1', '', '', '', '', '', '岗位名称', 1, b'0', 'INPUT', b'1', 4);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (27, '', b'0', b'1', 'name', '', b'1', b'1', '', '启用|fa-toggle-on', '重置密码|fa-key', '禁用|fa-toggle-on', '', '姓名', 1, b'0', 'INPUT', b'1', 5);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (28, '', b'0', b'1', 'external', '外部员工|内部员工', b'1', b'0', '', '', '', '', '', '是否外部员工', 2, b'0', 'BOOLEAN', b'1', 5);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (29, '', b'0', b'0', 'company', 'select id,name from tb_enterprise', b'1', b'0', '', '', '', '', '', '企业名称', 3, b'0', 'CHOICE', b'0', 5);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (30, '', b'0', b'1', 'department', '', b'1', b'1', '', '', '', '', '', '部门', 4, b'0', 'INPUT', b'1', 5);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (31, '', b'0', b'1', 'jobTitle', '', b'1', b'0', '', '', '', '', '', '培训岗位', 5, b'0', 'CHOICE', b'1', 5);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (32, '', b'0', b'1', 'noJobTitle', '', b'1', b'0', '', '', '', '', '', '非培训岗位', 6, b'0', 'TAGS', b'1', 5);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (33, '', b'0', b'1', 'sex', '男|女', b'1', b'0', '', '', '', '', '', '性别', 7, b'0', 'BOOLEAN', b'1', 5);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (34, '', b'0', b'1', 'birthday', '', b'1', b'0', '', '', '', '', '', '出生日期', 8, b'0', 'DATE', b'1', 5);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (35, '', b'0', b'1', 'phone', '', b'1', b'0', '', '', '', '', '', '手机号', 9, b'0', 'INPUT', b'1', 5);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (36, '', b'0', b'1', 'educational', '小学|初中|高中|专科教育|本科教育|硕士研究生|博士研究生', b'1', b'0', '', '', '', '', '', '学历', 10, b'0', 'CHOICE', b'1', 5);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (37, '', b'0', b'1', 'major', '', b'1', b'0', '', '', '', '', '', '专业', 11, b'0', 'INPUT', b'1', 5);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (38, '', b'0', b'1', 'professional', '', b'1', b'1', '', '', '', '', '', '职称', 12, b'0', 'INPUT', b'1', 5);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (39, '', b'0', b'1', 'seniority', '', b'1', b'0', '', '', '', '', '', '工作年限', 13, b'0', 'NUMBER', b'1', 5);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (40, '', b'0', b'1', 'checkPerson', '', b'1', b'0', '', '', '', '', '', '是否检查人员', 14, b'0', 'BOOLEAN', b'1', 5);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (41, '', b'0', b'1', 'checkScope', '油品|集装箱|其他', b'1', b'0', '', '', '', '', '', '检查范围', 15, b'0', 'TAGS', b'1', 5);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (42, '', b'0', b'1', 'certificateNo', '', b'1', b'0', '', '', '', '', '', '执法证号', 16, b'0', 'INPUT', b'1', 5);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (43, '', b'0', b'1', 'EvadeEnterprise', 'select name from tb_enterprise', b'1', b'0', '', '', '', '', '', '规避企业', 17, b'0', 'TAGS', b'1', 5);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (44, '', b'0', b'1', 'certificates', 'EmployeeCertificate', b'1', b'0', '', '', '', '', '', '员工证书', 18, b'0', 'TAB_TABLE_ADD', b'1', 5);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (45, '', b'0', b'1', 'certificateName', '', b'1', b'0', '', '', '', '', '', '资质证书名称', 1, b'0', 'INPUT', b'1', 6);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (46, '', b'0', b'1', 'certificateType', '水路运输从业资格证|特种设备操作证书|特种作业操作证书|其他证书', b'1', b'0', '', '', '', '', '', '证书类型', 2, b'0', 'INPUT', b'1', 6);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (47, '', b'0', b'1', 'validityPeriod', '', b'1', b'0', '', '', '', '', '', '有效期至', 3, b'0', 'DATE', b'1', 6);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (48, '', b'0', b'1', 'certificateFile', '50|image|jpg/bmp/png', b'1', b'0', '', '', '', '', '', '资质证书附件', 4, b'0', 'ATTACHMENT', b'1', 6);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (49, '', b'0', b'1', 'title', '', b'1', b'1', '', '', '', '', '', '标题', 1, b'0', 'INPUT', b'1', 7);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (50, '', b'0', b'1', 'content', '', b'1', b'0', '', '', '', '', '', '内容', 2, b'0', 'TEXTAREA', b'1', 7);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (51, '', b'0', b'0', 'deployTime', '', b'1', b'0', '', '', '', '', '', '发布时间', 3, b'1', 'DATE_TIME', b'0', 7);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (52, '', b'0', b'1', 'year', '', b'1', b'1', 'FALSE|TRUE', '提交|fa-send-o', '', '', '', '年度', 1, b'1', 'YEAR', b'1', 8);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (53, 'year|1', b'0', b'0', 'submitted', '', b'1', b'0', '', '', '', '', '', '状态', 2, b'0', 'BOOLEAN', b'0', 8);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (54, '', b'0', b'1', 'company', 'remote.entity=main|Enterprise|administrator,name', b'1', b'0', 'TRUE', NULL, NULL, NULL, NULL, '企业名字', 3, b'0', 'CHOICE', b'1', 8);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (55, '', b'0', b'1', 'responseiFile', '1|base|pdf/doc/docx/xlsx/png/jpg/xls/ppt/pptx/zip/rar', b'1', b'0', '', NULL, NULL, NULL, NULL, '责任制文件上传附件', 4, b'0', 'ATTACHMENT', b'1', 8);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (56, '', b'0', b'1', 'responseiCopy', '20|base|pdf/doc/docx/xlsx/png/jpg/xls/ppt/pptx/zip/rar', b'1', b'0', '', NULL, NULL, NULL, NULL, '责任制书扫描件', 5, b'0', 'ATTACHMENT', b'1', 8);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (57, '', b'0', b'1', 'berthNo', '', b'1', b'1', '', '上报|fa-arrow-circle-o-up', '', '', '', '危险货物泊位数量', 1, b'0', 'INPUT', b'0', 9);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (58, '', b'0', b'1', 'controTanknum', '', b'1', b'0', '', '', '', '', '', '处于安全运行状态储罐数量', 2, b'0', 'INPUT', b'0', 9);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (59, '', b'0', b'1', 'controRisknum', '', b'1', b'0', NULL, '', '', '', '', '处于安全可控状态动火作业、受限空间作业风险数量', 3, b'0', 'INPUT', b'0', 9);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (60, '', b'0', b'1', 'wareHouseNo', '', b'1', b'0', '', '', '', '', '', '处于安全运行状态危险货物堆场（库房）', 4, b'0', 'INPUT', b'0', 9);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (61, '', b'0', b'1', 'riskNo', '', b'1', b'0', '', '', '', '', '', '处于安全可控状态动火作业风险数量', 5, b'0', 'INPUT', b'0', 9);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (62, '', b'0', b'1', 'resPerson', '', b'1', b'1', '', '', '', '', '', '董事长或总经理', 6, b'0', 'INPUT', b'1', 9);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (63, '', b'0', b'1', 'company', 'remote.entity=main|Enterprise|administrator,name', b'1', b'0', 'TRUE', '', '', '', '', '企业名称', 7, b'0', 'CHOICE', b'1', 9);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (64, 'createTime|1', b'0', b'0', 'submitted', '', b'1', b'0', '', '', '', '', '', '状态', 8, b'0', 'BOOLEAN', b'0', 9);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (65, '', b'0', b'1', 'company', 'remote.entity=main|Enterprise|administrator,name', b'1', b'0', 'TRUE', '上报|fa-arrow-circle-o-up', '', '', '', '企业名称', 1, b'0', 'CHOICE', b'1', 10);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (66, 'mouth|1', b'0', b'0', 'submitted', '', b'1', b'0', '', '', '', '', '', '状态', 2, b'0', 'BOOLEAN', b'0', 10);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (67, NULL, b'0', b'1', 'mouth', '', b'1', b'0', NULL, '', '', '', '', '月份', 3, b'0', 'MONTH', b'1', 10);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (68, NULL, b'0', b'1', 'securityriskNo', '', b'1', b'0', NULL, '', '', '', '', '安全风险数量', 4, b'0', 'INPUT', b'0', 10);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (69, NULL, b'0', b'1', 'redriskNo', '', b'1', b'0', NULL, '', '', '', '', '红色风险数量', 5, b'0', 'INPUT', b'0', 10);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (70, NULL, b'0', b'1', 'orariskNo', '', b'1', b'0', NULL, '', '', '', '', '橙色风险数量', 6, b'0', 'INPUT', b'0', 10);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (71, NULL, b'0', b'1', 'yellowriskNo', '', b'1', b'0', NULL, '', '', '', '', '黄色风险数量', 7, b'0', 'INPUT', b'0', 10);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (72, NULL, b'0', b'1', 'buleriskNo', '', b'1', b'0', NULL, '', '', '', '', '蓝色风险数量', 8, b'0', 'INPUT', b'0', 10);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (73, NULL, b'0', b'0', 'redDivide', '', b'0', b'0', '', '', '', '', '', '红色风险', 9, b'0', 'DIVIDE', b'0', 10);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (74, NULL, b'0', b'1', 'redworkNo', '', b'1', b'0', '', '', '', '', '', '红色风险中生产作业数量', 10, b'0', 'INPUT', b'0', 10);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (75, NULL, b'0', b'1', 'rwmeasureNo', '', b'1', b'0', '', '', '', '', '', '作业采取措施数量', 11, b'0', 'INPUT', b'0', 10);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (76, NULL, b'0', b'1', 'redsperiskNo', '', b'1', b'0', '', '', '', '', '', '特殊风险数量', 12, b'0', 'INPUT', b'0', 10);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (77, NULL, b'0', b'1', 'rriskmeasureNo', '', b'1', b'0', '', '', '', '', '', '风险采取措施数量', 13, b'0', 'INPUT', b'0', 10);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (78, NULL, b'0', b'0', 'orDivide', '', b'0', b'0', '', '', '', '', '', '橙色风险', 14, b'0', 'DIVIDE', b'0', 10);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (79, NULL, b'0', b'1', 'oraworkNo', '', b'1', b'0', '', '', '', '', '', '橙色风险中生产作业数量', 15, b'0', 'INPUT', b'0', 10);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (80, NULL, b'0', b'1', 'owmeasureNo', '', b'1', b'0', '', '', '', '', '', '作业采取措施数量', 16, b'0', 'INPUT', b'0', 10);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (81, NULL, b'0', b'1', 'orasperiskNo', '', b'1', b'0', '', '', '', '', '', '特殊风险数量', 17, b'0', 'INPUT', b'0', 10);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (82, NULL, b'0', b'1', 'oriskmeasureNo', '', b'1', b'0', '', '', '', '', '', '风险采取措施数量', 18, b'0', 'INPUT', b'0', 10);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (83, NULL, b'0', b'0', 'yelDivide', '', b'0', b'0', '', '', '', '', '', '黄色风险', 19, b'0', 'DIVIDE', b'0', 10);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (84, NULL, b'0', b'1', 'yelworkNo', '', b'1', b'0', '', '', '', '', '', '黄色风险中生产作业数量', 20, b'0', 'INPUT', b'0', 10);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (85, NULL, b'0', b'1', 'ywmeasureNo', '', b'1', b'0', '', '', '', '', '', '作业采取措施数量', 21, b'0', 'INPUT', b'0', 10);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (86, NULL, b'0', b'1', 'yelsperiskNo', '', b'1', b'0', '', '', '', '', '', '特殊风险数量', 22, b'0', 'INPUT', b'0', 10);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (87, NULL, b'0', b'1', 'yriskmeasureNo', '', b'1', b'0', '', '', '', '', '', '风险采取措施数量', 23, b'0', 'INPUT', b'0', 10);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (88, NULL, b'0', b'0', 'blueDivide', '', b'0', b'0', NULL, '', '', '', '', '蓝色风险', 24, b'0', 'DIVIDE', b'0', 10);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (89, NULL, b'0', b'1', 'blueworkNo', '', b'1', b'0', NULL, '', '', '', '', '蓝色风险中生产作业数量', 25, b'0', 'INPUT', b'0', 10);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (90, NULL, b'0', b'1', 'bwmeasureNo', '', b'1', b'0', NULL, '', '', '', '', '作业采取措施数量', 26, b'0', 'INPUT', b'0', 10);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (91, NULL, b'0', b'1', 'belsperiskNo', '', b'1', b'0', NULL, '', '', '', '', '特殊风险数量', 27, b'0', 'INPUT', b'0', 10);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (92, NULL, b'0', b'1', 'briskmeasureNo', '', b'1', b'0', NULL, '', '', '', '', '风险采取措施数量', 28, b'0', 'INPUT', b'0', 10);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (93, NULL, b'0', b'1', 'reporter', '', b'1', b'1', NULL, '', '', '', '', '报告人', 29, b'0', 'INPUT', b'1', 10);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (94, NULL, b'0', b'0', 'jobTitle', '', b'1', b'0', NULL, '', '', '', '', '岗位', 30, b'0', 'INPUT', b'0', 10);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (95, NULL, b'0', b'0', 'isReport', '是|否', b'1', b'0', NULL, NULL, NULL, NULL, NULL, '是否上报', 31, b'0', 'BOOLEAN', b'0', 10);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (96, '', b'0', b'1', 'company', 'remote.entity=main|Enterprise|administrator,name', b'1', b'0', 'TRUE', '', '', '', '', '企业名称', 1, b'0', 'CHOICE', b'1', 11);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (97, '', b'0', b'1', 'inspector', '', b'1', b'0', '', '', '', '', '', '检查人员', 2, b'0', 'INPUT', b'1', 11);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (98, '', b'0', b'1', 'jobTitle', '', b'1', b'0', '', '', '', '', '', '岗位', 3, b'0', 'INPUT', b'1', 11);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (99, 'inspectionTime|1', b'0', b'0', 'submitted', '', b'1', b'0', '', '', '', '', '', '状态', 4, b'0', 'BOOLEAN', b'0', 11);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (100, '', b'0', b'1', 'inspectionTime', '', b'1', b'0', '', '', '', '', '', '检查时间', 5, b'0', 'DATE', b'1', 11);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (101, '', b'0', b'1', 'inspectionResult', '', b'1', b'0', '', '', '', '', '', '检查结果', 6, b'0', 'INPUT', b'1', 11);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (102, '', b'0', b'1', 'file', '20|base|pdf/doc/docx/xlsx/png/jpg/xls/ppt/pptx/zip/rar', b'1', b'0', '', '', '', '', '', '上传附件', 7, b'0', 'ATTACHMENT', b'1', 11);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (103, '', b'0', b'1', 'jobTitle', '', b'1', b'0', '', '', '', '', '', '岗位', 1, b'0', 'INPUT', b'1', 12);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (104, '', b'0', b'1', 'jobContent', '', b'1', b'0', '', '', '', '', '', '报告内容', 2, b'0', 'INPUT', b'1', 12);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (105, 'inspectionTime|1', b'0', b'0', 'submitted', '', b'1', b'0', '', '', '', '', '', '状态', 3, b'0', 'BOOLEAN', b'0', 12);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (106, '', b'0', b'1', 'inspectionTime', '', b'1', b'0', '', '', '', '', '', '检查时间', 4, b'0', 'DATE', b'1', 12);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (107, '', b'0', b'1', 'file', '20|base|pdf/doc/docx/xlsx/png/jpg/xls/ppt/pptx/zip/rar', b'1', b'0', '', '', '', '', '', '上传附件', 5, b'0', 'ATTACHMENT', b'1', 12);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (108, '', b'0', b'1', 'jobTitle', '', b'1', b'0', '', '', '', '', '', '岗位', 1, b'0', 'INPUT', b'1', 13);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (109, '', b'0', b'1', 'file', '20|base|pdf/doc/docx/xlsx/png/jpg/xls/ppt/pptx/zip/rar', b'1', b'0', '', '', '', '', '', '上传附件', 2, b'0', 'ATTACHMENT', b'1', 13);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (110, 'createTime|1', b'0', b'0', 'submitted', '', b'1', b'0', '', '', '', '', '', '状态', 3, b'0', 'BOOLEAN', b'0', 13);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (111, '', b'1', b'1', 'preplanName', '', b'1', b'1', '', '上报|fa-arrow-circle-o-up', '再次备案|fa-window-restore', '', '', '预案名称', 1, b'0', 'INPUT', b'1', 14);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (112, '', b'1', b'1', 'company', 'remote.entity=main|Enterprise|administrator,name', b'1', b'0', 'TRUE', '', '', '', '', '企业名称', 2, b'0', 'CHOICE', b'1', 14);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (113, '', b'1', b'1', 'preplanType', '综合预案|专项预案|现场处置方案', b'1', b'1', '', '', '', '', '', '预案类型', 3, b'0', 'CHOICE', b'1', 14);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (114, '', b'1', b'0', 'isRecord', '是|否', b'1', b'1', '', '', '', '', '', '是否备案', 4, b'0', 'BOOLEAN', b'1', 14);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (115, '', b'1', b'1', 'evaluationTime', '', b'1', b'0', '', '', '', '', '', '评估时间', 5, b'0', 'DATE', b'1', 14);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (116, '', b'1', b'1', 'recordTime', '', b'1', b'0', '', '', '', '', '', '备案时间', 6, b'0', 'DATE', b'1', 14);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (117, '', b'1', b'0', 'preplanState', '', b'1', b'1', '', '', '', '', '', '预案状态', 7, b'0', 'INPUT', b'1', 14);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (118, '', b'1', b'1', 'contact', '', b'1', b'1', '', '', '', '', '', '联系人', 8, b'0', 'INPUT', b'1', 14);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (119, '', b'1', b'1', 'contactTel', '', b'1', b'0', '', '', '', '', '', '联系方式', 9, b'0', 'INPUT', b'1', 14);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (120, '', b'1', b'0', 'fileDivide', '', b'0', b'0', '', '', '', '', '', '上传附件', 10, b'0', 'DIVIDE', b'0', 14);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (121, '', b'1', b'1', 'emergency', '20|base|pdf/doc/docx/xlsx/png/jpg/xls/ppt/pptx/zip/rar', b'1', b'0', '', '', '', '', '', '应急预案', 11, b'0', 'ATTACHMENT', b'1', 14);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (122, '', b'1', b'1', 'emerOpinion', '20|base|pdf/doc/docx/xlsx/png/jpg/xls/ppt/pptx/zip/rar', b'1', b'0', '', '', '', '', '', '应急预案评审意见', 12, b'0', 'ATTACHMENT', b'1', 14);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (123, '', b'1', b'1', 'riskIden', '20|base|pdf/doc/docx/xlsx/png/jpg/xls/ppt/pptx/zip/rar', b'1', b'0', '', '', '', '', '', '事故风险辨识', 13, b'0', 'ATTACHMENT', b'1', 14);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (124, '', b'1', b'1', 'reportEva', '20|base|pdf/doc/docx/xlsx/png/jpg/xls/ppt/pptx/zip/rar', b'1', b'0', '', '', '', '', '', '评估报告', 14, b'0', 'ATTACHMENT', b'1', 14);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (125, '', b'1', b'1', 'emerList', '20|base|pdf/doc/docx/xlsx/png/jpg/xls/ppt/pptx/zip/rar', b'1', b'0', '', '', '', '', '', '应急资源调查清单', 15, b'0', 'ATTACHMENT', b'1', 14);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (126, '', b'1', b'1', 'emerTable', '20|base|pdf/doc/docx/xlsx/png/jpg/xls/ppt/pptx/zip/rar', b'1', b'0', '', '', '', '', '', '应急预案备案登记表（备案回执）', 16, b'0', 'ATTACHMENT', b'1', 14);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (127, '', b'0', b'1', 'otherFile', '20|base|pdf/doc/docx/xlsx/png/jpg/xls/ppt/pptx/zip/rar', b'1', b'0', '', '', '', '', '', '其他文件/资料', 17, b'0', 'ATTACHMENT', b'1', 14);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (128, '', b'0', b'0', 'isReport', '是|否', b'1', b'0', '', '', '', '', '', '是否上报', 18, b'0', 'BOOLEAN', b'0', 14);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (129, 'recordTime|1', b'0', b'0', 'submitted', '是|否', b'1', b'0', '', '', '', '', '', '状态', 19, b'0', 'BOOLEAN', b'0', 14);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (130, '', b'0', b'1', 'company', 'remote.entity=main|Enterprise|administrator,name', b'1', b'0', 'TRUE', '上报|fa-arrow-circle-o-up', '', '', '', '企业名称', 1, b'0', 'CHOICE', b'1', 15);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (131, NULL, b'0', b'1', 'dangerName', '', b'1', b'1', NULL, NULL, NULL, NULL, NULL, '重大危险源名称', 2, b'0', 'INPUT', b'1', 15);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (132, NULL, b'0', b'1', 'dangerType', '一级|二级|三级|四级', b'1', b'0', NULL, NULL, NULL, NULL, NULL, '重大危险源等级', 3, b'0', 'CHOICE', b'1', 15);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (133, NULL, b'0', b'1', 'recordTime', '', b'1', b'0', NULL, NULL, NULL, NULL, NULL, '备案时间', 4, b'0', 'DATE', b'1', 15);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (134, NULL, b'0', b'0', 'fileDivide', '', b'0', b'0', NULL, NULL, NULL, NULL, NULL, '上传附件', 5, b'0', 'DIVIDE', b'0', 15);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (135, NULL, b'0', b'1', 'identification', '20|base|pdf/doc/docx/xlsx/png/jpg/xls/ppt/pptx/zip/rar', b'1', b'0', NULL, NULL, NULL, NULL, NULL, '辨识、分级记录', 6, b'0', 'ATTACHMENT', b'0', 15);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (136, NULL, b'0', b'1', 'dangerFile', '20|base|pdf/doc/docx/xlsx/png/jpg/xls/ppt/pptx/zip/rar', b'1', b'0', NULL, NULL, NULL, NULL, NULL, '港口重大危险源基本特征表', 7, b'0', 'ATTACHMENT', b'0', 15);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (137, NULL, b'0', b'1', 'dangerBook', '20|base|pdf/doc/docx/xlsx/png/jpg/xls/ppt/pptx/zip/rar', b'1', b'0', NULL, NULL, NULL, NULL, NULL, '危险货物安全技术说明书', 8, b'0', 'ATTACHMENT', b'0', 15);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (138, NULL, b'0', b'1', 'listFile', '20|base|pdf/doc/docx/xlsx/png/jpg/xls/ppt/pptx/zip/rar', b'1', b'0', NULL, NULL, NULL, NULL, NULL, '区域位置图、平面布置图、工艺流程图和主要设备一览表', 9, b'0', 'ATTACHMENT', b'0', 15);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (139, NULL, b'0', b'1', 'regulationFile', '20|base|pdf/doc/docx/xlsx/png/jpg/xls/ppt/pptx/zip/rar', b'1', b'0', NULL, NULL, NULL, NULL, NULL, '港口重大危险源安全管理制度及安全操作规程', 10, b'0', 'ATTACHMENT', b'0', 15);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (140, NULL, b'0', b'1', 'resultFile', '20|base|pdf/doc/docx/xlsx/png/jpg/xls/ppt/pptx/zip/rar', b'1', b'0', NULL, NULL, NULL, NULL, NULL, '安全监测监控系统、措施说明、检测、检验结果', 11, b'0', 'ATTACHMENT', b'0', 15);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (141, NULL, b'0', b'1', 'preplanFile', '20|base|pdf/doc/docx/xlsx/png/jpg/xls/ppt/pptx/zip/rar', b'1', b'0', NULL, NULL, NULL, NULL, NULL, '港口重大危险源事故应急预案', 12, b'0', 'ATTACHMENT', b'0', 15);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (142, NULL, b'0', b'1', 'reportFile', '20|base|pdf/doc/docx/xlsx/png/jpg/xls/ppt/pptx/zip/rar', b'1', b'0', NULL, NULL, NULL, NULL, NULL, '安全评估报告或安全评价报告', 13, b'0', 'ATTACHMENT', b'0', 15);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (143, NULL, b'0', b'1', 'warningfile', '20|base|pdf/doc/docx/xlsx/png/jpg/xls/ppt/pptx/zip/rar', b'1', b'0', NULL, NULL, NULL, NULL, NULL, '港口重大危险源场所安全警示标志的设置情况', 14, b'0', 'ATTACHMENT', b'0', 15);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (144, NULL, b'0', b'1', 'otherFile', '20|base|pdf/doc/docx/xlsx/png/jpg/xls/ppt/pptx/zip/rar', b'1', b'0', NULL, NULL, NULL, NULL, NULL, '其他文件、资料', 15, b'0', 'ATTACHMENT', b'0', 15);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (145, NULL, b'0', b'0', 'isReport', '是|否', b'1', b'0', NULL, NULL, NULL, NULL, NULL, '是否上报', 16, b'0', 'BOOLEAN', b'1', 15);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (146, '', b'0', b'1', 'company', 'remote.entity=main|Enterprise|administrator,name', b'1', b'0', 'TRUE', '上报|fa-arrow-circle-o-up', '自评|fa-star-o', '', '', '企业名称', 1, b'0', 'CHOICE', b'1', 16);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (147, NULL, b'1', b'1', 'grade', '一级|二级', b'1', b'0', NULL, NULL, NULL, NULL, NULL, '安全标准化等级', 2, b'0', 'CHOICE', b'1', 16);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (148, NULL, b'1', b'1', 'issueDate', '', b'1', b'0', NULL, NULL, NULL, NULL, NULL, '发证日期', 3, b'0', 'DATE', b'1', 16);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (149, NULL, b'1', b'1', 'effectiveDate', '', b'1', b'1', NULL, NULL, NULL, NULL, NULL, '有效期至', 4, b'0', 'DATE', b'1', 16);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (150, NULL, b'1', b'1', 'expiredState', '正常|即将逾期|已过期', b'1', b'0', NULL, NULL, NULL, NULL, NULL, '状态', 5, b'0', 'CHOICE', b'1', 16);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (151, NULL, b'1', b'0', 'fileDivide', '', b'0', b'0', NULL, NULL, NULL, NULL, NULL, '上传附件', 6, b'0', 'DIVIDE', b'0', 16);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (152, NULL, b'1', b'1', 'standardFile', '20|base|pdf/doc/docx/xlsx/png/jpg/xls/ppt/pptx/zip/rar', b'1', b'0', NULL, NULL, NULL, NULL, NULL, '安全标准化证书', 7, b'0', 'ATTACHMENT', b'0', 16);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (153, NULL, b'1', b'1', 'selfEvaluation', '20|base|pdf/doc/docx/xlsx/png/jpg/xls/ppt/pptx/zip/rar', b'1', b'0', NULL, NULL, NULL, NULL, NULL, '自评报告', 8, b'0', 'ATTACHMENT', b'0', 16);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (154, NULL, b'1', b'1', 'otherFile', '20|base|pdf/doc/docx/xlsx/png/jpg/xls/ppt/pptx/zip/rar', b'0', b'0', NULL, NULL, NULL, NULL, NULL, '其他文件、资料', 9, b'0', 'ATTACHMENT', b'0', 16);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (155, NULL, b'0', b'0', 'isReport', '是|否', b'1', b'0', NULL, NULL, NULL, NULL, NULL, '是否上报', 10, b'0', 'BOOLEAN', b'1', 16);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (156, 'year|1', b'1', b'0', 'submitted', '', b'1', b'0', '', '', '', '', '', '状态', 11, b'0', 'BOOLEAN', b'0', 16);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (157, NULL, b'0', b'1', 'category', '', b'1', b'0', NULL, '', '', '', '', '评价类目', 1, b'0', 'INPUT', b'0', 17);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (158, NULL, b'0', b'1', 'project', '', b'1', b'0', NULL, NULL, NULL, NULL, NULL, '评价项目', 2, b'0', 'INPUT', b'0', 17);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (159, NULL, b'0', b'1', 'interpretation', '', b'1', b'0', NULL, NULL, NULL, NULL, NULL, '释义', 3, b'0', 'INPUT', b'0', 17);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (160, NULL, b'0', b'1', 'method', '', b'1', b'0', NULL, NULL, NULL, NULL, NULL, '评价方法', 4, b'0', 'INPUT', b'0', 17);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (161, NULL, b'0', b'1', 'score', '', b'1', b'0', NULL, NULL, NULL, NULL, NULL, '标准分值', 5, b'0', 'INPUT', b'0', 17);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (162, NULL, b'0', b'1', 'standard', '', b'1', b'0', NULL, NULL, NULL, NULL, NULL, '评价标准', 6, b'0', 'INPUT', b'0', 17);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (163, NULL, b'0', b'1', 'isInvolve', '是|否', b'1', b'0', NULL, NULL, NULL, NULL, NULL, '是否涉及', 7, b'0', 'BOOLEAN', b'0', 17);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (164, NULL, b'0', b'1', 'myScore', '', b'1', b'0', NULL, NULL, NULL, NULL, NULL, '得分', 8, b'0', 'INPUT', b'0', 17);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (165, '', b'0', b'1', 'company', 'remote.entity=main|Enterprise|administrator,name', b'1', b'0', 'TRUE', '上报|fa-arrow-circle-o-up', '', '', '', '企业名称', 1, b'0', 'CHOICE', b'1', 18);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (166, '', b'0', b'1', 'grade', '', b'1', b'0', '', NULL, NULL, NULL, NULL, '项目名称', 2, b'0', 'INPUT', b'1', 18);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (167, '', b'0', b'1', 'issueDate', '', b'1', b'0', '', NULL, NULL, NULL, NULL, '项目概况', 3, b'0', 'INPUT', b'1', 18);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (168, '', b'0', b'1', 'effectiveDate', '', b'1', b'0', '', NULL, NULL, NULL, NULL, '备案编号', 4, b'0', 'INPUT', b'1', 18);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (169, '', b'0', b'1', 'state', '', b'1', b'0', '', NULL, NULL, NULL, NULL, '备案有效期至', 5, b'0', 'INPUT', b'1', 18);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (170, '', b'0', b'1', 'expiredState', '正常|即将逾期|已过期', b'1', b'0', '', NULL, NULL, NULL, NULL, '状态', 6, b'0', 'CHOICE', b'1', 18);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (171, '', b'0', b'0', 'fileDivide', '', b'0', b'0', '', NULL, NULL, NULL, NULL, '上传附件', 7, b'0', 'DIVIDE', b'0', 18);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (172, '', b'0', b'1', 'selfEvaluation', '20|base|pdf/doc/docx/xlsx/png/jpg/xls/ppt/pptx/zip/rar', b'1', b'0', '', NULL, NULL, NULL, NULL, '评价报告', 8, b'0', 'ATTACHMENT', b'0', 18);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (173, '', b'0', b'1', 'otherFile', '20|base|pdf/doc/docx/xlsx/png/jpg/xls/ppt/pptx/zip/rar', b'1', b'0', '', NULL, NULL, NULL, NULL, '其他文件、资料', 9, b'0', 'ATTACHMENT', b'1', 18);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (174, '', b'0', b'0', 'isReport', '是|否', b'1', b'0', '', NULL, NULL, NULL, NULL, '是否上报', 10, b'0', 'BOOLEAN', b'0', 18);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (175, '', b'0', b'1', 'company', 'remote.entity=main|Enterprise|administrator,name', b'1', b'0', 'TRUE', '上报|fa-arrow-circle-o-up', '', '', '', '企业名称', 1, b'0', 'CHOICE', b'1', 19);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (176, NULL, b'0', b'1', 'grade', '', b'1', b'0', NULL, NULL, NULL, NULL, NULL, '项目名称', 2, b'0', 'INPUT', b'1', 19);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (177, NULL, b'0', b'1', 'issueDate', '', b'1', b'0', NULL, NULL, NULL, NULL, NULL, '项目概况', 3, b'0', 'INPUT', b'1', 19);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (178, NULL, b'0', b'0', 'fileDivide', '', b'0', b'0', NULL, NULL, NULL, NULL, NULL, '上传附件', 4, b'0', 'DIVIDE', b'0', 19);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (179, NULL, b'0', b'1', 'application', '20|base|pdf/doc/docx/xlsx/png/jpg/xls/ppt/pptx/zip/rar', b'1', b'0', NULL, NULL, NULL, NULL, NULL, '港口建设项目安全条件审查申请书', 5, b'0', 'ATTACHMENT', b'0', 19);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (180, NULL, b'0', b'1', 'conditionReport', '20|base|pdf/doc/docx/xlsx/png/jpg/xls/ppt/pptx/zip/rar', b'1', b'0', NULL, NULL, NULL, NULL, NULL, '港口建设项目安全条件论证报告', 6, b'0', 'ATTACHMENT', b'0', 19);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (181, NULL, b'0', b'1', 'evaluation', '20|base|pdf/doc/docx/xlsx/png/jpg/xls/ppt/pptx/zip/rar', b'1', b'0', NULL, NULL, NULL, NULL, NULL, '港口建设项目安全预评价报告', 7, b'0', 'ATTACHMENT', b'0', 19);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (182, NULL, b'0', b'1', 'review', '20|base|pdf/doc/docx/xlsx/png/jpg/xls/ppt/pptx/zip/rar', b'1', b'0', NULL, NULL, NULL, NULL, NULL, '港口建设项目安全条件审查意见书', 8, b'0', 'ATTACHMENT', b'0', 19);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (183, NULL, b'0', b'1', 'otherFile', '20|base|pdf/doc/docx/xlsx/png/jpg/xls/ppt/pptx/zip/rar', b'0', b'0', NULL, NULL, NULL, NULL, NULL, '其他文件、资料', 9, b'0', 'ATTACHMENT', b'0', 19);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (184, NULL, b'0', b'0', 'isReport', '是|否', b'1', b'0', NULL, NULL, NULL, NULL, NULL, '是否上报', 10, b'0', 'BOOLEAN', b'0', 19);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (185, '', b'0', b'1', 'company', 'remote.entity=main|Enterprise|administrator,name', b'1', b'0', 'TRUE', '上报|fa-arrow-circle-o-up', '', '', '', '企业名称', 1, b'0', 'CHOICE', b'1', 20);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (186, NULL, b'0', b'1', 'grade', '', b'1', b'0', NULL, NULL, NULL, NULL, NULL, '项目名称', 2, b'0', 'INPUT', b'1', 20);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (187, NULL, b'0', b'1', 'issueDate', '', b'1', b'0', NULL, NULL, NULL, NULL, NULL, '项目概况', 3, b'0', 'INPUT', b'1', 20);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (188, NULL, b'0', b'0', 'fileDivide', '', b'0', b'0', NULL, NULL, NULL, NULL, NULL, '上传附件', 4, b'0', 'DIVIDE', b'0', 20);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (189, NULL, b'0', b'1', 'design', '20|base|pdf/doc/docx/xlsx/png/jpg/xls/ppt/pptx/zip/rar', b'1', b'0', NULL, NULL, NULL, NULL, NULL, '港口建设项目安全设施设计审查申请书及文件', 5, b'0', 'ATTACHMENT', b'0', 20);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (190, NULL, b'0', b'1', 'review', '20|base|pdf/doc/docx/xlsx/png/jpg/xls/ppt/pptx/zip/rar', b'1', b'0', NULL, NULL, NULL, NULL, NULL, '港口建设项目安全条件审查意见书', 6, b'0', 'ATTACHMENT', b'0', 20);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (191, NULL, b'0', b'1', 'qualification', '20|base|pdf/doc/docx/xlsx/png/jpg/xls/ppt/pptx/zip/rar', b'1', b'0', NULL, NULL, NULL, NULL, NULL, '设计单位的设计资质证明文件', 7, b'0', 'ATTACHMENT', b'0', 20);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (192, NULL, b'0', b'1', 'facility', '20|base|pdf/doc/docx/xlsx/png/jpg/xls/ppt/pptx/zip/rar', b'1', b'0', NULL, NULL, NULL, NULL, NULL, '港口建设项目安全设施设计专篇', 8, b'0', 'ATTACHMENT', b'0', 20);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (193, NULL, b'0', b'1', 'approve', '20|base|pdf/doc/docx/xlsx/png/jpg/xls/ppt/pptx/zip/rar', b'1', b'0', NULL, NULL, NULL, NULL, NULL, '港口建设项目批准（或核准、备案）文件和选址意见书（或规划许可证或土地使用证）', 9, b'0', 'ATTACHMENT', b'0', 20);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (194, NULL, b'0', b'1', 'opinion', '20|base|pdf/doc/docx/xlsx/png/jpg/xls/ppt/pptx/zip/rar', b'1', b'0', NULL, NULL, NULL, NULL, NULL, '港口建设项目安全设施设计审查意见书', 10, b'0', 'ATTACHMENT', b'0', 20);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (195, NULL, b'0', b'1', 'otherFile', '20|base|pdf/doc/docx/xlsx/png/jpg/xls/ppt/pptx/zip/rar', b'0', b'0', NULL, NULL, NULL, NULL, NULL, '其他文件、资料', 11, b'0', 'ATTACHMENT', b'0', 20);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (196, NULL, b'0', b'0', 'isReport', '是|否', b'1', b'0', NULL, NULL, NULL, NULL, NULL, '是否上报', 12, b'0', 'BOOLEAN', b'0', 20);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (197, NULL, b'0', b'0', 'name', '', b'1', b'0', NULL, '上报|fa-arrow-circle-o-up', '', '', '', '港口设施名称', 1, b'0', 'INPUT', b'0', 21);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (198, '', b'0', b'1', 'company', 'remote.entity=main|Enterprise|administrator,name', b'1', b'0', 'TRUE', NULL, NULL, NULL, NULL, '企业名称', 2, b'0', 'CHOICE', b'1', 21);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (199, NULL, b'0', b'1', 'adress', '', b'1', b'0', NULL, NULL, NULL, NULL, NULL, '港口设施地址', 3, b'0', 'INPUT', b'1', 21);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (200, NULL, b'0', b'1', 'date', '', b'1', b'0', NULL, NULL, NULL, NULL, NULL, '签发日期', 4, b'0', 'DATE', b'1', 21);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (201, NULL, b'0', b'1', 'effectiveDate', '', b'1', b'1', NULL, NULL, NULL, NULL, NULL, '有效期至', 5, b'0', 'DATE', b'1', 21);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (202, NULL, b'0', b'1', 'expiredState', '正常|即将逾期|已过期', b'1', b'0', NULL, NULL, NULL, NULL, NULL, '状态', 6, b'0', 'CHOICE', b'1', 21);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (203, NULL, b'0', b'0', 'fileDivide', '', b'0', b'0', NULL, NULL, NULL, NULL, NULL, '上传附件', 7, b'0', 'DIVIDE', b'0', 21);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (204, NULL, b'0', b'1', 'report', '20|base|pdf/doc/docx/xlsx/png/jpg/xls/ppt/pptx/zip/rar', b'1', b'0', NULL, NULL, NULL, NULL, NULL, '港口设施保安评估报告', 8, b'0', 'ATTACHMENT', b'0', 21);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (205, NULL, b'0', b'1', 'plan', '20|base|pdf/doc/docx/xlsx/png/jpg/xls/ppt/pptx/zip/rar', b'1', b'0', NULL, NULL, NULL, NULL, NULL, '港口设施保安计划', 9, b'0', 'ATTACHMENT', b'0', 21);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (206, NULL, b'0', b'1', 'certificate', '20|base|pdf/doc/docx/xlsx/png/jpg/xls/ppt/pptx/zip/rar', b'1', b'0', NULL, NULL, NULL, NULL, NULL, '港口设施保安符合证书', 10, b'0', 'ATTACHMENT', b'0', 21);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (207, NULL, b'0', b'1', 'reportFile', '20|base|pdf/doc/docx/xlsx/png/jpg/xls/ppt/pptx/zip/rar', b'1', b'0', NULL, NULL, NULL, NULL, NULL, '年度核查报告', 11, b'0', 'ATTACHMENT', b'0', 21);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (208, NULL, b'0', b'1', 'other', '20|base|pdf/doc/docx/xlsx/png/jpg/xls/ppt/pptx/zip/rar', b'0', b'0', NULL, NULL, NULL, NULL, NULL, '其他文件、资料', 12, b'0', 'ATTACHMENT', b'0', 21);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (209, NULL, b'0', b'0', 'isReport', '是|否', b'1', b'0', NULL, NULL, NULL, NULL, NULL, '是否上报', 13, b'0', 'BOOLEAN', b'0', 21);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (210, NULL, b'0', b'0', 'number', '', b'1', b'0', NULL, '上报|fa-arrow-circle-o-up', '上传附证|fa-arrow-circle-o-up', '', '', '附证编号', 1, b'0', 'INPUT', b'0', 22);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (211, '', b'0', b'1', 'company', 'remote.entity=main|Enterprise|administrator,name', b'1', b'0', 'TRUE', NULL, NULL, NULL, NULL, '企业名称', 2, b'0', 'CHOICE', b'1', 22);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (212, NULL, b'0', b'1', 'businessScope', '', b'1', b'0', NULL, NULL, NULL, NULL, NULL, '经营范围', 3, b'0', 'INPUT', b'1', 22);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (213, NULL, b'0', b'1', 'work', '', b'1', b'0', NULL, NULL, NULL, NULL, NULL, '作业方式', 4, b'0', 'INPUT', b'1', 22);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (214, NULL, b'0', b'1', 'date', '', b'1', b'0', NULL, NULL, NULL, NULL, NULL, '发证日期', 5, b'0', 'DATE', b'1', 22);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (215, NULL, b'0', b'1', 'effectiveDate', '', b'1', b'0', NULL, NULL, NULL, NULL, NULL, '有效期至', 6, b'0', 'INPUT', b'1', 22);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (216, NULL, b'0', b'1', 'expiredState', '正常|即将逾期|已过期', b'1', b'0', NULL, NULL, NULL, NULL, NULL, '状态', 7, b'0', 'CHOICE', b'1', 22);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (217, NULL, b'0', b'1', 'checkDate', '', b'1', b'0', NULL, NULL, NULL, NULL, NULL, '年度审验日期', 8, b'0', 'DATE', b'1', 22);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (218, NULL, b'0', b'1', 'file', '20|base|pdf/doc/docx/xlsx/png/jpg/xls/ppt/pptx/zip/rar', b'1', b'0', NULL, NULL, NULL, NULL, NULL, '港口危险货物作业附证（加注年审意见并盖章）', 9, b'0', 'ATTACHMENT', b'0', 22);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (219, NULL, b'0', b'0', 'isReport', '是|否', b'1', b'0', NULL, NULL, NULL, NULL, NULL, '是否上报', 10, b'0', 'BOOLEAN', b'0', 22);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (220, NULL, b'0', b'1', 'superior', '', b'1', b'0', NULL, '', '', '', '', '上级目录名称', 1, b'0', 'INPUT', b'1', 23);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (221, NULL, b'0', b'1', 'directory', '', b'1', b'0', NULL, NULL, NULL, NULL, NULL, '目录名称', 2, b'0', 'INPUT', b'1', 23);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (222, NULL, b'0', b'1', 'file', '20|base|pdf/doc/docx/xlsx/png/jpg/xls/ppt/pptx/zip/rar', b'1', b'1', NULL, NULL, NULL, NULL, NULL, '附件名称', 3, b'0', 'ATTACHMENT', b'1', 23);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (223, NULL, b'0', b'1', 'name', '', b'1', b'1', '', '', '', '', '', '货种名称', 1, b'0', 'INPUT', b'1', 24);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (224, NULL, b'0', b'1', 'file', '1|base|pdf/doc/docx', b'1', b'0', '', '', '', '', '', '货种文件', 2, b'0', 'ATTACHMENT', b'1', 24);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (225, NULL, b'0', b'1', 'portareaName', '', b'1', b'1', NULL, '', '', '', '', '港区名称', 1, b'0', 'INPUT', b'1', 25);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (226, NULL, b'0', b'1', 'totalArea', '', b'1', b'0', NULL, NULL, NULL, NULL, NULL, '合计面积（万平方米）', 2, b'0', 'INPUT', b'1', 25);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (227, NULL, b'0', b'1', 'landArea', '', b'1', b'0', NULL, NULL, NULL, NULL, NULL, '陆域面积（万平方米）', 3, b'0', 'INPUT', b'1', 25);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (228, NULL, b'0', b'1', 'waterArea', '', b'1', b'0', NULL, NULL, NULL, NULL, NULL, '水域面积（万平方米）', 4, b'0', 'INPUT', b'1', 25);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (229, NULL, b'0', b'1', 'shorelineLength', '', b'1', b'0', NULL, NULL, NULL, NULL, NULL, '港口生产已使用自然岸线长度（米）', 5, b'0', 'INPUT', b'1', 25);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (230, NULL, b'0', b'0', 'longitudeAndLatitude', '', b'1', b'0', NULL, NULL, NULL, NULL, NULL, '经纬度范围', 6, b'0', 'INPUT', b'0', 25);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (231, NULL, b'0', b'0', 'longitude', '', b'0', b'0', NULL, NULL, NULL, NULL, NULL, '经度', 7, b'0', 'INPUT', b'0', 25);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (232, NULL, b'0', b'0', 'latitude', '', b'0', b'0', NULL, NULL, NULL, NULL, NULL, '纬度', 8, b'0', 'INPUT', b'0', 25);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (233, NULL, b'0', b'0', 'map', NULL, b'0', b'0', NULL, NULL, NULL, NULL, NULL, '地图', 9, b'0', 'MAP', b'0', 25);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (234, '', b'0', b'1', 'wharfName', '', b'1', b'1', '', '', '', '', '', '码头名称', 1, b'0', 'INPUT', b'1', 26);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (235, NULL, b'0', b'1', 'terminalUnit', '', b'1', b'0', NULL, NULL, NULL, NULL, NULL, '港口企业或码头单位', 2, b'0', 'INPUT', b'1', 26);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (236, NULL, b'0', b'1', 'portArea', '', b'1', b'1', '', NULL, NULL, NULL, NULL, '所属港区', 3, b'0', 'CHOICE', b'1', 26);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (237, NULL, b'0', b'1', 'company', 'remote.entity=main|Enterprise|administrator,name', b'1', b'0', NULL, NULL, NULL, NULL, NULL, '企业名称', 4, b'0', 'CHOICE', b'1', 26);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (238, NULL, b'0', b'0', 'longitudeAndLatitude', '', b'1', b'0', NULL, NULL, NULL, NULL, NULL, '经纬度范围', 5, b'0', 'INPUT', b'0', 26);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (239, NULL, b'0', b'0', 'longitude', '', b'0', b'0', NULL, NULL, NULL, NULL, NULL, '经度', 6, b'0', 'INPUT', b'0', 26);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (240, NULL, b'0', b'0', 'latitude', '', b'0', b'0', NULL, NULL, NULL, NULL, NULL, '纬度', 7, b'0', 'INPUT', b'0', 26);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (241, NULL, b'0', b'0', 'map', NULL, b'0', b'0', NULL, NULL, NULL, NULL, NULL, '地图', 8, b'0', 'MAP', b'0', 26);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (242, NULL, b'0', b'1', 'berthName', '', b'1', b'1', NULL, '', '', '', '', '泊位名称', 1, b'0', 'INPUT', b'1', 27);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (243, NULL, b'0', b'1', 'wharf', '', b'1', b'1', NULL, NULL, NULL, NULL, NULL, '所属码头', 2, b'0', 'CHOICE', b'1', 27);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (244, NULL, b'0', b'1', 'portArea', '', b'1', b'1', NULL, NULL, NULL, NULL, NULL, '所属港区', 3, b'0', 'CHOICE', b'1', 27);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (245, NULL, b'0', b'1', 'company', 'remote.entity=main|Enterprise|administrator,name', b'1', b'0', NULL, NULL, NULL, NULL, NULL, '企业名称', 4, b'0', 'CHOICE', b'1', 27);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (246, NULL, b'0', b'0', 'longitudeAndLatitude', '', b'1', b'0', NULL, NULL, NULL, NULL, NULL, '经纬度范围', 5, b'0', 'INPUT', b'0', 27);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (247, NULL, b'0', b'0', 'longitude', '', b'0', b'0', NULL, NULL, NULL, NULL, NULL, '经度', 6, b'0', 'INPUT', b'0', 27);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (248, NULL, b'0', b'0', 'latitude', '', b'0', b'0', NULL, NULL, NULL, NULL, NULL, '纬度', 7, b'0', 'INPUT', b'0', 27);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (249, NULL, b'0', b'0', 'map', NULL, b'0', b'0', NULL, NULL, NULL, NULL, NULL, '地图', 8, b'0', 'MAP', b'0', 27);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (250, NULL, b'0', b'1', 'anchorage', '', b'1', b'1', NULL, '', '', '', '', '锚地名称', 1, b'0', 'INPUT', b'1', 28);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (251, NULL, b'0', b'1', 'categoryOfAnchorage', '', b'1', b'0', NULL, NULL, NULL, NULL, NULL, '锚地类别', 2, b'0', 'INPUT', b'1', 28);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (252, NULL, b'0', b'1', 'anchoragePosition', '', b'1', b'0', NULL, NULL, NULL, NULL, NULL, '锚地位置', 3, b'0', 'INPUT', b'1', 28);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (253, NULL, b'0', b'1', 'anchorageDepth', '', b'1', b'0', NULL, NULL, NULL, NULL, NULL, '锚地水深', 4, b'0', 'INPUT', b'1', 28);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (254, NULL, b'0', b'1', 'anchorageArea', '', b'1', b'0', NULL, NULL, NULL, NULL, NULL, '锚地面积（万平方米）', 5, b'0', 'INPUT', b'1', 28);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (255, NULL, b'0', b'1', 'mooringBuoy', '', b'1', b'0', NULL, NULL, NULL, NULL, NULL, '系船浮筒个数（个）', 6, b'0', 'INPUT', b'1', 28);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (256, NULL, b'0', b'1', 'tonnage', '', b'1', b'0', NULL, NULL, NULL, NULL, NULL, '锚泊能力_吨级', 7, b'0', 'INPUT', b'1', 28);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (257, NULL, b'0', b'1', 'number', '', b'1', b'0', NULL, NULL, NULL, NULL, NULL, '锚泊能力_艘数', 8, b'0', 'INPUT', b'1', 28);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (258, NULL, b'0', b'0', 'longitudeAndLatitude', '', b'1', b'0', NULL, NULL, NULL, NULL, NULL, '经纬度', 9, b'0', 'INPUT', b'0', 28);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (259, NULL, b'0', b'0', 'longitude', '', b'0', b'0', NULL, NULL, NULL, NULL, NULL, '经度', 10, b'0', 'INPUT', b'0', 28);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (260, NULL, b'0', b'0', 'latitude', '', b'0', b'0', NULL, NULL, NULL, NULL, NULL, '纬度', 11, b'0', 'INPUT', b'0', 28);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (261, NULL, b'0', b'0', 'map', NULL, b'0', b'0', NULL, NULL, NULL, NULL, NULL, '地图', 12, b'0', 'MAP', b'0', 28);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (262, NULL, b'0', b'1', 'approachChannel', '', b'1', b'1', NULL, '', '', '', '', '进港航道名称', 1, b'0', 'INPUT', b'1', 29);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (263, NULL, b'0', b'0', 'longitudeAndLatitude', NULL, b'1', b'0', NULL, NULL, NULL, NULL, NULL, '经纬度', 2, b'0', 'INPUT', b'0', 29);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (264, NULL, b'0', b'0', 'longitude', NULL, b'0', b'0', NULL, NULL, NULL, NULL, NULL, '经度', 3, b'0', 'INPUT', b'0', 29);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (265, NULL, b'0', b'0', 'latitude', NULL, b'0', b'0', NULL, NULL, NULL, NULL, NULL, '纬度', 4, b'0', 'INPUT', b'0', 29);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (266, NULL, b'0', b'0', 'map', NULL, b'0', b'0', NULL, NULL, NULL, NULL, NULL, '地图', 5, b'0', 'MAP', b'0', 29);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (267, NULL, b'0', b'1', 'railwayName', '', b'1', b'1', NULL, '', '', '', '', '铁路名称', 1, b'0', 'INPUT', b'1', 30);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (268, NULL, b'0', b'1', 'constructionTime', '', b'1', b'0', NULL, NULL, NULL, NULL, NULL, '修建时间', 2, b'0', 'DATE', b'1', 30);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (269, NULL, b'0', b'1', 'productionTime', '', b'1', b'0', NULL, NULL, NULL, NULL, NULL, '投产时间', 3, b'0', 'DATE', b'1', 30);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (270, NULL, b'0', b'1', 'assetOwnership', '', b'1', b'0', NULL, NULL, NULL, NULL, NULL, '资产归属单位', 4, b'0', 'INPUT', b'1', 30);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (271, NULL, b'0', b'1', 'assetManagement', '', b'1', b'0', NULL, NULL, NULL, NULL, NULL, '资产管理单位', 5, b'0', 'INPUT', b'1', 30);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (272, NULL, b'0', b'1', 'railwayGrade', '', b'1', b'0', NULL, NULL, NULL, NULL, NULL, '铁路等级', 6, b'0', 'CHOICE', b'1', 30);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (273, NULL, b'0', b'1', 'functionType', '', b'1', b'0', NULL, NULL, NULL, NULL, NULL, '功能类型', 7, b'0', 'INPUT', b'1', 30);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (274, NULL, b'0', b'1', 'trackType', '', b'1', b'0', NULL, NULL, NULL, NULL, NULL, '轨道类型', 8, b'0', 'INPUT', b'1', 30);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (275, NULL, b'0', b'0', 'longitudeAndLatitude', '', b'1', b'0', NULL, NULL, NULL, NULL, NULL, '经纬度', 9, b'0', 'INPUT', b'0', 30);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (276, NULL, b'0', b'0', 'longitude', '', b'0', b'0', NULL, NULL, NULL, NULL, NULL, '经度', 10, b'0', 'INPUT', b'0', 30);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (277, NULL, b'0', b'0', 'latitude', '', b'0', b'0', NULL, NULL, NULL, NULL, NULL, '纬度', 11, b'0', 'INPUT', b'0', 30);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (278, NULL, b'0', b'0', 'map', NULL, b'0', b'0', NULL, NULL, NULL, NULL, NULL, '地图', 12, b'0', 'MAP', b'0', 30);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (279, NULL, b'0', b'1', 'highwayName', '', b'1', b'1', NULL, '', '', '', '', '公路名称', 1, b'0', 'INPUT', b'1', 31);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (280, NULL, b'0', b'1', 'travelRestriction', '', b'1', b'0', NULL, NULL, NULL, NULL, NULL, '限行信息', 2, b'0', 'INPUT', b'1', 31);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (281, NULL, b'0', b'1', 'heightLimit', '', b'1', b'0', NULL, NULL, NULL, NULL, NULL, '限高信息', 3, b'0', 'INPUT', b'1', 31);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (282, NULL, b'0', b'1', 'speedLimit', '', b'1', b'0', NULL, NULL, NULL, NULL, NULL, '限速信息', 4, b'0', 'INPUT', b'1', 31);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (283, NULL, b'0', b'0', 'longitudeAndLatitude', NULL, b'1', b'0', NULL, NULL, NULL, NULL, NULL, '经纬度', 5, b'0', 'INPUT', b'0', 31);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (284, NULL, b'0', b'0', 'longitude', NULL, b'0', b'0', NULL, NULL, NULL, NULL, NULL, '经度', 6, b'0', 'INPUT', b'0', 31);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (285, NULL, b'0', b'0', 'latitude', NULL, b'0', b'0', NULL, NULL, NULL, NULL, NULL, '纬度', 7, b'0', 'INPUT', b'0', 31);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (286, NULL, b'0', b'0', 'map', NULL, b'0', b'0', NULL, NULL, NULL, NULL, NULL, '地图', 8, b'0', 'MAP', b'0', 31);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (287, NULL, b'0', b'1', 'tankFarm', '', b'1', b'1', NULL, '', '', '', '', '罐区名称', 1, b'0', 'INPUT', b'1', 32);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (288, NULL, b'0', b'1', 'company', 'remote.entity=main|Enterprise|administrator,name', b'1', b'0', NULL, NULL, NULL, NULL, NULL, '企业名称', 2, b'0', 'CHOICE', b'1', 32);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (289, NULL, b'0', b'1', 'portArea', '', b'1', b'1', NULL, NULL, NULL, NULL, NULL, '所属港区', 3, b'0', 'CHOICE', b'1', 32);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (290, NULL, b'0', b'0', 'longitudeAndLatitude', '', b'1', b'0', NULL, NULL, NULL, NULL, NULL, '经纬度', 4, b'0', 'INPUT', b'0', 32);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (291, NULL, b'0', b'0', 'longitude', '', b'0', b'0', NULL, NULL, NULL, NULL, NULL, '经度', 5, b'0', 'INPUT', b'0', 32);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (292, NULL, b'0', b'0', 'latitude', '', b'0', b'0', NULL, NULL, NULL, NULL, NULL, '纬度', 6, b'0', 'INPUT', b'0', 32);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (293, NULL, b'0', b'0', 'map', NULL, b'0', b'0', NULL, NULL, NULL, NULL, NULL, '地图', 7, b'0', 'MAP', b'0', 32);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (294, NULL, b'0', b'1', 'tankgroup', '', b'1', b'1', NULL, '', '', '', '', '罐组名称', 1, b'0', 'INPUT', b'1', 33);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (295, NULL, b'0', b'1', 'tankFarm', '', b'1', b'1', NULL, NULL, NULL, NULL, NULL, '所属罐区', 2, b'0', 'CHOICE', b'1', 33);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (296, NULL, b'0', b'1', 'company', 'remote.entity=main|Enterprise|administrator,name', b'1', b'0', NULL, NULL, NULL, NULL, NULL, '企业名称', 3, b'0', 'CHOICE', b'1', 33);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (297, NULL, b'0', b'1', 'portArea', '', b'1', b'1', NULL, NULL, NULL, NULL, NULL, '所属港区', 4, b'0', 'CHOICE', b'1', 33);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (298, NULL, b'0', b'1', 'longitudeAndLatitude', '', b'0', b'0', NULL, NULL, NULL, NULL, NULL, '经纬度', 5, b'0', 'INPUT', b'1', 33);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (299, NULL, b'0', b'0', 'longitude', '', b'0', b'0', NULL, NULL, NULL, NULL, NULL, '经度', 6, b'0', 'INPUT', b'0', 33);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (300, NULL, b'0', b'0', 'latitude', '', b'0', b'0', NULL, NULL, NULL, NULL, NULL, '纬度', 7, b'0', 'INPUT', b'0', 33);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (301, NULL, b'0', b'0', 'map', NULL, b'0', b'0', NULL, NULL, NULL, NULL, NULL, '地图', 8, b'0', 'MAP', b'0', 33);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (302, NULL, b'0', b'1', 'tankName', '', b'1', b'1', NULL, '', '', '', '', '储罐名称', 1, b'0', 'INPUT', b'1', 34);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (303, NULL, b'0', b'1', 'tankFarm', '', b'1', b'1', NULL, NULL, NULL, NULL, NULL, '所属罐区', 2, b'0', 'CHOICE', b'1', 34);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (304, NULL, b'0', b'1', 'tankGroup', '', b'1', b'1', NULL, NULL, NULL, NULL, NULL, '所属罐组', 3, b'0', 'CHOICE', b'1', 34);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (305, NULL, b'0', b'1', 'company', 'remote.entity=main|Enterprise|administrator,name', b'1', b'0', NULL, NULL, NULL, NULL, NULL, '企业名称', 4, b'0', 'CHOICE', b'1', 34);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (306, NULL, b'0', b'1', 'portArea', '', b'1', b'1', NULL, NULL, NULL, NULL, NULL, '所属港区', 5, b'0', 'CHOICE', b'1', 34);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (307, NULL, b'0', b'1', 'tankShape', '', b'1', b'0', NULL, NULL, NULL, NULL, NULL, '储罐形状', 6, b'0', 'INPUT', b'1', 34);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (308, NULL, b'0', b'1', 'tankType', '', b'1', b'0', NULL, NULL, NULL, NULL, NULL, '储罐形式', 7, b'0', 'INPUT', b'1', 34);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (309, NULL, b'0', b'1', 'tankMaterial', '', b'1', b'0', NULL, NULL, NULL, NULL, NULL, '储罐材质', 8, b'0', 'INPUT', b'1', 34);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (310, NULL, b'0', b'1', 'existingGoods', '', b'1', b'0', NULL, NULL, NULL, NULL, NULL, '现存货物名称', 9, b'0', 'INPUT', b'1', 34);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (311, NULL, b'0', b'1', 'inventoryable', '', b'1', b'0', NULL, NULL, NULL, NULL, NULL, '可存货类', 10, b'0', 'INPUT', b'1', 34);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (312, NULL, b'0', b'1', 'volume', '', b'1', b'0', NULL, NULL, NULL, NULL, NULL, '容积（m3)', 11, b'0', 'INPUT', b'1', 34);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (313, NULL, b'0', b'1', 'designPressure', '', b'1', b'0', NULL, NULL, NULL, NULL, NULL, '设计压力', 12, b'0', 'INPUT', b'1', 34);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (314, NULL, b'0', b'1', 'designTemperature', '', b'1', b'0', NULL, NULL, NULL, NULL, NULL, '设计温度', 13, b'0', 'INPUT', b'1', 34);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (315, NULL, b'0', b'1', 'yearBuilt', '', b'1', b'0', NULL, NULL, NULL, NULL, NULL, '建成年份', 14, b'0', 'YEAR', b'1', 34);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (316, NULL, b'0', b'1', 'serviceLife', '', b'1', b'0', NULL, NULL, NULL, NULL, NULL, '设计使用寿命', 15, b'0', 'INPUT', b'1', 34);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (317, NULL, b'0', b'1', 'safetyDirector', '', b'1', b'0', NULL, NULL, NULL, NULL, NULL, '安全负责人姓名', 16, b'0', 'INPUT', b'1', 34);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (318, NULL, b'0', b'1', 'contactNumber', '', b'1', b'0', NULL, NULL, NULL, NULL, NULL, '联系电话', 17, b'0', 'INPUT', b'1', 34);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (319, NULL, b'0', b'1', 'longitudeAndLatitude', '', b'0', b'0', NULL, NULL, NULL, NULL, NULL, '经纬度', 18, b'0', 'INPUT', b'1', 34);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (320, NULL, b'0', b'0', 'longitude', '', b'0', b'0', NULL, NULL, NULL, NULL, NULL, '经度', 19, b'0', 'INPUT', b'0', 34);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (321, NULL, b'0', b'0', 'latitude', '', b'0', b'0', NULL, NULL, NULL, NULL, NULL, '纬度', 20, b'0', 'INPUT', b'0', 34);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (322, NULL, b'0', b'0', 'map', NULL, b'0', b'0', NULL, NULL, NULL, NULL, NULL, '地图', 21, b'0', 'MAP', b'0', 34);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (323, NULL, b'0', b'1', 'stackName', '', b'1', b'1', NULL, '', '', '', '', '栈台名称', 1, b'0', 'INPUT', b'1', 35);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (324, NULL, b'0', b'1', 'company', 'remote.entity=main|Enterprise|administrator,name', b'1', b'0', NULL, NULL, NULL, NULL, NULL, '企业名称', 2, b'0', 'CHOICE', b'1', 35);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (325, NULL, b'0', b'1', 'portArea', '', b'1', b'1', NULL, NULL, NULL, NULL, NULL, '所属港区', 3, b'0', 'CHOICE', b'1', 35);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (326, NULL, b'0', b'1', 'numberOfVehicles', '', b'1', b'0', NULL, NULL, NULL, NULL, NULL, '车台数', 4, b'0', 'INPUT', b'1', 35);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (327, NULL, b'0', b'1', 'numberOfLanes', '', b'1', b'0', NULL, NULL, NULL, NULL, NULL, '车道数', 5, b'0', 'INPUT', b'1', 35);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (328, NULL, b'0', b'1', 'operationTime', '', b'1', b'0', NULL, NULL, NULL, NULL, NULL, '投用时间', 6, b'0', 'DATE', b'1', 35);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (329, NULL, b'0', b'1', 'contacts', '', b'1', b'0', NULL, NULL, NULL, NULL, NULL, '联系人', 7, b'0', 'INPUT', b'1', 35);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (330, NULL, b'0', b'1', 'contactNumber', '', b'1', b'0', NULL, NULL, NULL, NULL, NULL, '联系电话', 8, b'0', 'INPUT', b'1', 35);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (331, NULL, b'0', b'0', 'longitudeAndLatitude', '', b'1', b'0', NULL, NULL, NULL, NULL, NULL, '经纬度', 9, b'0', 'INPUT', b'0', 35);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (332, NULL, b'0', b'0', 'longitude', '', b'0', b'0', NULL, NULL, NULL, NULL, NULL, '经度', 10, b'0', 'INPUT', b'0', 35);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (333, NULL, b'0', b'0', 'latitude', '', b'0', b'0', NULL, NULL, NULL, NULL, NULL, '纬度', 11, b'0', 'INPUT', b'0', 35);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (334, NULL, b'0', b'0', 'map', NULL, b'0', b'0', NULL, NULL, NULL, NULL, NULL, '地图', 12, b'0', 'MAP', b'0', 35);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (335, NULL, b'0', b'1', 'yardName', '', b'1', b'1', NULL, '', '', '', '', '堆场名称', 1, b'0', 'INPUT', b'1', 36);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (336, NULL, b'0', b'1', 'company', 'remote.entity=main|Enterprise|administrator,name', b'1', b'0', NULL, NULL, NULL, NULL, NULL, '企业名称', 2, b'0', 'CHOICE', b'1', 36);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (337, NULL, b'0', b'1', 'Area', '', b'1', b'0', NULL, NULL, NULL, NULL, NULL, '面积', 3, b'0', 'INPUT', b'1', 36);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (338, NULL, b'0', b'1', 'contacts', '', b'1', b'0', NULL, NULL, NULL, NULL, NULL, '联系人', 4, b'0', 'INPUT', b'1', 36);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (339, NULL, b'0', b'1', 'contactNumber', '', b'1', b'0', NULL, NULL, NULL, NULL, NULL, '联系电话', 5, b'0', 'INPUT', b'1', 36);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (340, NULL, b'0', b'0', 'longitudeAndLatitude', '', b'1', b'0', NULL, NULL, NULL, NULL, NULL, '经纬度', 6, b'0', 'INPUT', b'0', 36);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (341, NULL, b'0', b'0', 'longitude', '', b'0', b'0', NULL, NULL, NULL, NULL, NULL, '经度', 7, b'0', 'INPUT', b'0', 36);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (342, NULL, b'0', b'0', 'latitude', '', b'0', b'0', NULL, NULL, NULL, NULL, NULL, '纬度', 8, b'0', 'INPUT', b'0', 36);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (343, NULL, b'0', b'0', 'map', NULL, b'0', b'0', NULL, NULL, NULL, NULL, NULL, '地图', 9, b'0', 'MAP', b'0', 36);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (344, NULL, b'0', b'1', 'warehouseName', '', b'1', b'1', NULL, '', '', '', '', '仓库名称', 1, b'0', 'INPUT', b'1', 37);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (345, NULL, b'0', b'1', 'company', 'remote.entity=main|Enterprise|administrator,name', b'1', b'0', NULL, NULL, NULL, NULL, NULL, '企业名称', 2, b'0', 'CHOICE', b'1', 37);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (346, NULL, b'0', b'1', 'Area', '', b'1', b'0', NULL, NULL, NULL, NULL, NULL, '面积', 3, b'0', 'INPUT', b'1', 37);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (347, NULL, b'0', b'1', 'contacts', '', b'1', b'0', NULL, NULL, NULL, NULL, NULL, '联系人', 4, b'0', 'INPUT', b'1', 37);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (348, NULL, b'0', b'1', 'contactNumber', '', b'1', b'0', NULL, NULL, NULL, NULL, NULL, '联系电话', 5, b'0', 'INPUT', b'1', 37);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (349, NULL, b'0', b'0', 'longitudeAndLatitude', NULL, b'1', b'0', NULL, NULL, NULL, NULL, NULL, '经纬度', 6, b'0', 'INPUT', b'0', 37);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (350, NULL, b'0', b'0', 'longitude', NULL, b'0', b'0', NULL, NULL, NULL, NULL, NULL, '经度', 7, b'0', 'INPUT', b'0', 37);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (351, NULL, b'0', b'0', 'latitude', NULL, b'0', b'0', NULL, NULL, NULL, NULL, NULL, '纬度', 8, b'0', 'INPUT', b'0', 37);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (352, NULL, b'0', b'0', 'map', NULL, b'0', b'0', NULL, NULL, NULL, NULL, NULL, '地图', 9, b'0', 'MAP', b'0', 37);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (353, '', b'0', b'1', 'testReportName', '', b'1', b'1', NULL, '', '', '', '', '检测报告名称', 1, b'0', 'INPUT', b'1', 38);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (354, NULL, b'0', b'1', 'grade', '一类（好）|二类（较好）|三类（较差）|四类（差）|五类（危险）', b'1', b'0', NULL, '', '', '', '', '技术状态等级', 2, b'0', 'CHOICE', b'1', 38);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (355, NULL, b'0', b'1', 'validityPeriod', '', b'1', b'0', NULL, NULL, NULL, NULL, NULL, '检测报告有效期限', 3, b'0', 'DATE', b'1', 38);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (356, NULL, b'0', b'1', 'expiredState', '正常|即将逾期|已过期', b'1', b'0', NULL, NULL, NULL, NULL, NULL, '状态', 4, b'0', 'CHOICE', b'1', 38);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (357, NULL, b'0', b'1', 'detectionRange', '', b'1', b'0', NULL, NULL, NULL, NULL, NULL, '检测报告检测范围', 5, b'0', 'CHOICE', b'1', 38);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (358, NULL, b'0', b'1', 'description', '', b'0', b'0', NULL, NULL, NULL, NULL, NULL, '说明', 6, b'0', 'INPUT', b'1', 38);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (359, NULL, b'0', b'1', 'reportAttachments', '20|base|pdf/png/jpg', b'1', b'0', NULL, NULL, NULL, NULL, NULL, '检测报告附件', 7, b'0', 'ATTACHMENT', b'1', 38);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (360, '', b'0', b'1', 'testReportName', '', b'1', b'1', NULL, '', '', '', '', '检测报告名称', 1, b'0', 'INPUT', b'1', 39);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (361, NULL, b'0', b'1', 'validityPeriod', '', b'1', b'0', NULL, NULL, NULL, NULL, NULL, '检测报告有效期限', 2, b'0', 'DATE', b'1', 39);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (362, NULL, b'0', b'1', 'expiredState', '正常|即将逾期|已过期', b'1', b'0', NULL, NULL, NULL, NULL, NULL, '状态', 3, b'0', 'CHOICE', b'1', 39);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (363, NULL, b'0', b'1', 'detectionRange', '罐组|泊位|其他', b'1', b'0', NULL, NULL, NULL, NULL, NULL, '检测报告检测范围', 4, b'0', 'CHOICE', b'1', 39);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (364, NULL, b'0', b'1', 'description', '', b'0', b'0', NULL, NULL, NULL, NULL, NULL, '说明', 5, b'0', 'INPUT', b'1', 39);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (365, NULL, b'0', b'1', 'reportAttachments', '20|base|pdf/doc/docx/xlsx/png/jpg/xls/ppt/pptx/zip/rar', b'1', b'0', NULL, NULL, NULL, NULL, NULL, '检测报告附件', 6, b'0', 'ATTACHMENT', b'1', 39);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (366, '', b'0', b'1', 'company', 'remote.entity=main|Enterprise|administrator,name', b'1', b'0', 'TRUE', NULL, NULL, NULL, NULL, '企业名称', 7, b'0', 'CHOICE', b'1', 39);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (367, '', b'0', b'1', 'testReportName', '', b'1', b'1', NULL, '', '', '', '', '检测报告名称', 1, b'0', 'INPUT', b'1', 40);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (368, NULL, b'0', b'1', 'validityPeriod', '', b'1', b'0', NULL, NULL, NULL, NULL, NULL, '检测报告有效期限', 2, b'0', 'DATE', b'1', 40);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (369, NULL, b'0', b'1', 'expiredState', '正常|即将逾期|已过期', b'1', b'0', NULL, NULL, NULL, NULL, NULL, '状态', 3, b'0', 'CHOICE', b'1', 40);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (370, NULL, b'0', b'1', 'detectionRange', '罐组|泊位|其他', b'1', b'0', NULL, NULL, NULL, NULL, NULL, '检测报告检测范围', 4, b'0', 'CHOICE', b'1', 40);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (371, NULL, b'0', b'1', 'description', '', b'0', b'0', NULL, NULL, NULL, NULL, NULL, '说明', 5, b'0', 'INPUT', b'1', 40);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (372, NULL, b'0', b'1', 'reportAttachments', '20|base|pdf/doc/docx/xlsx/png/jpg/xls/ppt/pptx/zip/rar', b'1', b'0', NULL, NULL, NULL, NULL, NULL, '检测报告附件', 6, b'0', 'ATTACHMENT', b'1', 40);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (373, NULL, b'0', b'1', 'company', 'remote.entity=main|Enterprise|administrator,name', b'1', b'0', 'TRUE', NULL, NULL, NULL, NULL, '企业名称', 7, b'0', 'CHOICE', b'1', 40);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (374, '', b'0', b'1', 'testReportName', '', b'1', b'1', NULL, '', '', '', '', '检测报告名称', 1, b'0', 'INPUT', b'1', 41);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (375, NULL, b'0', b'1', 'validityPeriod', '', b'1', b'0', NULL, NULL, NULL, NULL, NULL, '检测报告有效期限', 2, b'0', 'DATE', b'1', 41);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (376, NULL, b'0', b'1', 'expiredState', '正常|即将逾期|已过期', b'1', b'0', NULL, NULL, NULL, NULL, NULL, '状态', 3, b'0', 'CHOICE', b'1', 41);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (377, NULL, b'0', b'1', 'detectionRange', '罐组|泊位|其他', b'1', b'0', NULL, NULL, NULL, NULL, NULL, '检测报告检测范围', 4, b'0', 'CHOICE', b'1', 41);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (378, NULL, b'0', b'1', 'description', '', b'0', b'0', NULL, NULL, NULL, NULL, NULL, '说明', 5, b'0', 'INPUT', b'1', 41);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (379, NULL, b'0', b'1', 'reportAttachments', '20|base|pdf/doc/docx/xlsx/png/jpg/xls/ppt/pptx/zip/rar', b'1', b'0', NULL, NULL, NULL, NULL, NULL, '检测报告附件', 6, b'0', 'ATTACHMENT', b'1', 41);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (380, NULL, b'0', b'1', 'company', 'remote.entity=main|Enterprise|administrator,name', b'1', b'0', 'TRUE', NULL, NULL, NULL, NULL, '企业名称', 7, b'0', 'CHOICE', b'1', 41);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (381, '', b'0', b'1', 'testReportName', '', b'1', b'1', NULL, '', '', '', '', '检测报告名称', 1, b'0', 'INPUT', b'1', 42);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (382, NULL, b'0', b'1', 'validityPeriod', '', b'1', b'0', NULL, NULL, NULL, NULL, NULL, '检测报告有效期限', 2, b'0', 'DATE', b'1', 42);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (383, NULL, b'0', b'1', 'expiredState', '正常|即将逾期|已过期', b'1', b'0', NULL, NULL, NULL, NULL, NULL, '状态', 3, b'0', 'CHOICE', b'1', 42);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (384, NULL, b'0', b'1', 'detectionRange', '罐组|泊位|其他', b'1', b'0', NULL, NULL, NULL, NULL, NULL, '检测报告检测范围', 4, b'0', 'CHOICE', b'1', 42);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (385, NULL, b'0', b'1', 'description', '', b'0', b'0', NULL, NULL, NULL, NULL, NULL, '说明', 5, b'0', 'INPUT', b'1', 42);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (386, NULL, b'0', b'1', 'reportAttachments', '20|base|pdf/doc/docx/xlsx/png/jpg/xls/ppt/pptx/zip/rar', b'1', b'0', NULL, NULL, NULL, NULL, NULL, '检测报告附件', 6, b'0', 'ATTACHMENT', b'1', 42);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (387, NULL, b'0', b'1', 'company', 'remote.entity=main|Enterprise|administrator,name', b'1', b'0', 'TRUE', NULL, NULL, NULL, NULL, '企业名称', 7, b'0', 'CHOICE', b'1', 42);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (388, '', b'0', b'1', 'testReportName', '', b'1', b'1', NULL, '', '', '', '', '检测报告名称', 1, b'0', 'INPUT', b'1', 43);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (389, NULL, b'0', b'1', 'validityPeriod', '', b'1', b'0', NULL, NULL, NULL, NULL, NULL, '检测报告有效期限', 2, b'0', 'DATE', b'1', 43);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (390, NULL, b'0', b'1', 'expiredState', '正常|即将逾期|已过期', b'1', b'0', NULL, NULL, NULL, NULL, NULL, '状态', 3, b'0', 'CHOICE', b'1', 43);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (391, NULL, b'0', b'1', 'detectionRange', '罐组|泊位|其他', b'1', b'0', NULL, NULL, NULL, NULL, NULL, '检测报告检测范围', 4, b'0', 'CHOICE', b'1', 43);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (392, NULL, b'0', b'1', 'description', '', b'0', b'0', NULL, NULL, NULL, NULL, NULL, '说明', 5, b'0', 'INPUT', b'1', 43);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (393, NULL, b'0', b'1', 'reportAttachments', '20|base|pdf/doc/docx/xlsx/png/jpg/xls/ppt/pptx/zip/rar', b'1', b'0', NULL, NULL, NULL, NULL, NULL, '检测报告附件', 6, b'0', 'ATTACHMENT', b'1', 43);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (394, NULL, b'0', b'1', 'company', 'remote.entity=main|Enterprise|administrator,name', b'1', b'0', 'TRUE', NULL, NULL, NULL, NULL, '企业名称', 7, b'0', 'CHOICE', b'1', 43);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (395, '', b'0', b'1', 'testReportName', '', b'1', b'1', NULL, '', '', '', '', '检测报告名称', 1, b'0', 'INPUT', b'1', 44);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (396, NULL, b'0', b'1', 'validityPeriod', '', b'1', b'0', NULL, NULL, NULL, NULL, NULL, '检测报告有效期限', 2, b'0', 'DATE', b'1', 44);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (397, NULL, b'0', b'1', 'expiredState', '正常|即将逾期|已过期', b'1', b'0', NULL, NULL, NULL, NULL, NULL, '状态', 3, b'0', 'CHOICE', b'1', 44);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (398, NULL, b'0', b'1', 'detectionRange', '罐组|泊位|其他', b'1', b'0', NULL, NULL, NULL, NULL, NULL, '检测报告检测范围', 4, b'0', 'CHOICE', b'1', 44);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (399, NULL, b'0', b'1', 'description', '', b'0', b'0', NULL, NULL, NULL, NULL, NULL, '说明', 5, b'0', 'INPUT', b'1', 44);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (400, NULL, b'0', b'1', 'reportAttachments', '20|base|pdf/doc/docx/xlsx/png/jpg/xls/ppt/pptx/zip/rar', b'1', b'0', NULL, NULL, NULL, NULL, NULL, '检测报告附件', 6, b'0', 'ATTACHMENT', b'1', 44);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (401, NULL, b'0', b'1', 'company', 'remote.entity=main|Enterprise|administrator,name', b'1', b'0', 'TRUE', NULL, NULL, NULL, NULL, '企业名称', 7, b'0', 'CHOICE', b'1', 44);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (402, '', b'0', b'1', 'testReportName', '', b'1', b'1', NULL, '', '', '', '', '检测报告名称', 1, b'0', 'INPUT', b'1', 45);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (403, NULL, b'0', b'1', 'validityPeriod', '', b'1', b'0', NULL, NULL, NULL, NULL, NULL, '检测报告有效期限', 2, b'0', 'DATE', b'1', 45);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (404, NULL, b'0', b'1', 'expiredState', '正常|即将逾期|已过期', b'1', b'0', NULL, NULL, NULL, NULL, NULL, '状态', 3, b'0', 'CHOICE', b'1', 45);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (405, NULL, b'0', b'1', 'detectionRange', '罐组|泊位|其他', b'1', b'0', NULL, NULL, NULL, NULL, NULL, '检测报告检测范围', 4, b'0', 'CHOICE', b'1', 45);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (406, NULL, b'0', b'1', 'description', '', b'0', b'0', NULL, NULL, NULL, NULL, NULL, '说明', 5, b'0', 'INPUT', b'1', 45);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (407, NULL, b'0', b'1', 'reportAttachments', '20|base|pdf/doc/docx/xlsx/png/jpg/xls/ppt/pptx/zip/rar', b'1', b'0', NULL, NULL, NULL, NULL, NULL, '检测报告附件', 6, b'0', 'ATTACHMENT', b'1', 45);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (408, NULL, b'0', b'1', 'company', 'remote.entity=main|Enterprise|administrator,name', b'1', b'0', 'TRUE', NULL, NULL, NULL, NULL, '企业名称', 7, b'0', 'CHOICE', b'1', 45);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (409, '', b'0', b'1', 'testReportName', '', b'1', b'1', NULL, '', '', '', '', '检测报告名称', 1, b'0', 'INPUT', b'1', 46);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (410, NULL, b'0', b'1', 'validityPeriod', '', b'1', b'0', NULL, NULL, NULL, NULL, NULL, '检测报告有效期限', 2, b'0', 'DATE', b'1', 46);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (411, NULL, b'0', b'1', 'expiredState', '正常|即将逾期|已过期', b'1', b'0', NULL, NULL, NULL, NULL, NULL, '状态', 3, b'0', 'CHOICE', b'1', 46);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (412, NULL, b'0', b'1', 'detectionRange', '罐组|泊位|其他', b'1', b'0', NULL, NULL, NULL, NULL, NULL, '检测报告检测范围', 4, b'0', 'CHOICE', b'1', 46);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (413, NULL, b'0', b'1', 'description', '', b'0', b'0', NULL, NULL, NULL, NULL, NULL, '说明', 5, b'0', 'INPUT', b'1', 46);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (414, NULL, b'0', b'1', 'reportAttachments', '20|base|pdf/doc/docx/xlsx/png/jpg/xls/ppt/pptx/zip/rar', b'1', b'0', NULL, NULL, NULL, NULL, NULL, '检测报告附件', 6, b'0', 'ATTACHMENT', b'1', 46);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (415, NULL, b'0', b'1', 'company', 'remote.entity=main|Enterprise|administrator,name', b'1', b'0', 'TRUE', NULL, NULL, NULL, NULL, '企业名称', 7, b'0', 'CHOICE', b'1', 46);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (416, '', b'0', b'1', 'testReportName', '', b'1', b'1', NULL, '', '', '', '', '检测报告名称', 1, b'0', 'INPUT', b'1', 47);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (417, NULL, b'0', b'1', 'validityPeriod', '', b'1', b'0', NULL, NULL, NULL, NULL, NULL, '检测报告有效期限', 2, b'0', 'DATE', b'1', 47);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (418, NULL, b'0', b'1', 'expiredState', '正常|即将逾期|已过期', b'1', b'0', NULL, NULL, NULL, NULL, NULL, '状态', 3, b'0', 'CHOICE', b'1', 47);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (419, NULL, b'0', b'1', 'detectionRange', '罐组|泊位|其他', b'1', b'0', NULL, NULL, NULL, NULL, NULL, '检测报告检测范围', 4, b'0', 'CHOICE', b'1', 47);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (420, NULL, b'0', b'1', 'description', '', b'0', b'0', NULL, NULL, NULL, NULL, NULL, '说明', 5, b'0', 'INPUT', b'1', 47);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (421, NULL, b'0', b'1', 'reportAttachments', '20|base|pdf/doc/docx/xlsx/png/jpg/xls/ppt/pptx/zip/rar', b'1', b'0', NULL, NULL, NULL, NULL, NULL, '检测报告附件', 6, b'0', 'ATTACHMENT', b'1', 47);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (422, NULL, b'0', b'1', 'company', 'remote.entity=main|Enterprise|administrator,name', b'1', b'0', 'TRUE', NULL, NULL, NULL, NULL, '企业名称', 7, b'0', 'CHOICE', b'1', 47);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (423, '', b'0', b'1', 'testReportName', '', b'1', b'1', NULL, '', '', '', '', '检测报告名称', 1, b'0', 'INPUT', b'1', 48);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (424, NULL, b'0', b'1', 'validityPeriod', '', b'1', b'0', NULL, NULL, NULL, NULL, NULL, '检测报告有效期限', 2, b'0', 'DATE', b'1', 48);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (425, NULL, b'0', b'1', 'expiredState', '正常|即将逾期|已过期', b'1', b'0', NULL, NULL, NULL, NULL, NULL, '状态', 3, b'0', 'CHOICE', b'1', 48);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (426, NULL, b'0', b'1', 'detectionRange', '罐组|泊位|其他', b'1', b'0', NULL, NULL, NULL, NULL, NULL, '检测报告检测范围', 4, b'0', 'CHOICE', b'1', 48);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (427, NULL, b'0', b'1', 'description', '', b'0', b'0', NULL, NULL, NULL, NULL, NULL, '说明', 5, b'0', 'INPUT', b'1', 48);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (428, NULL, b'0', b'1', 'reportAttachments', '20|base|pdf/doc/docx/xlsx/png/jpg/xls/ppt/pptx/zip/rar', b'1', b'0', NULL, NULL, NULL, NULL, NULL, '检测报告附件', 6, b'0', 'ATTACHMENT', b'1', 48);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (429, NULL, b'0', b'1', 'company', 'remote.entity=main|Enterprise|administrator,name', b'1', b'0', 'TRUE', NULL, NULL, NULL, NULL, '企业名称', 7, b'0', 'CHOICE', b'1', 48);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (430, '', b'0', b'1', 'testReportName', '', b'1', b'1', NULL, '', '', '', '', '检测报告名称', 1, b'0', 'INPUT', b'1', 49);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (431, NULL, b'0', b'1', 'validityPeriod', '', b'1', b'0', NULL, '', '', '', '', '检测报告有效期限', 2, b'0', 'DATE', b'1', 49);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (432, NULL, b'0', b'1', 'expiredState', '正常|即将逾期|已过期', b'1', b'0', NULL, '', '', '', '', '状态', 3, b'0', 'CHOICE', b'1', 49);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (433, NULL, b'0', b'1', 'detectionRange', '罐组|泊位|其他', b'1', b'0', NULL, '', '', '', '', '检测报告检测范围', 4, b'0', 'CHOICE', b'1', 49);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (434, NULL, b'0', b'1', 'description', '', b'0', b'0', NULL, '', '', '', '', '说明', 5, b'0', 'INPUT', b'1', 49);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (435, NULL, b'0', b'1', 'reportAttachments', '20|base|pdf/doc/docx/xlsx/png/jpg/xls/ppt/pptx/zip/rar', b'1', b'0', NULL, '', '', '', '', '检测报告附件', 6, b'0', 'ATTACHMENT', b'1', 49);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (436, NULL, b'0', b'1', 'company', 'remote.entity=main|Enterprise|administrator,name', b'1', b'0', 'TRUE', NULL, NULL, NULL, NULL, '企业名称', 7, b'0', 'CHOICE', b'1', 49);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (437, '', b'0', b'1', 'company', 'remote.entity=main|Enterprise|administrator,name', b'1', b'0', 'TRUE', '', '', '', '', '企业名称', 1, b'0', 'CHOICE', b'1', 50);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (438, NULL, b'0', b'1', 'name', '', b'1', b'0', NULL, '', '', '', '', '作业名称', 2, b'0', 'INPUT', b'1', 50);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (439, NULL, b'0', b'1', 'beginTime', '', b'1', b'1', NULL, '', '', '', '', '计划开始时间', 3, b'0', 'DATE', b'0', 50);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (440, NULL, b'0', b'1', 'endTime', '', b'1', b'0', NULL, NULL, NULL, NULL, NULL, '计划结束时间', 4, b'0', 'DATE', b'0', 50);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (441, NULL, b'0', b'1', 'abTime', '', b'1', b'0', NULL, NULL, NULL, NULL, NULL, '实际开始时间', 5, b'0', 'DATE', b'1', 50);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (442, NULL, b'0', b'1', 'aeTime', '', b'1', b'0', NULL, NULL, NULL, NULL, NULL, '实际结束时间', 6, b'0', 'DATE', b'1', 50);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (443, NULL, b'0', b'1', 'goodsName', '', b'1', b'0', NULL, NULL, NULL, NULL, NULL, '货种名称', 7, b'0', 'INPUT', b'0', 50);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (444, NULL, b'0', b'1', 'weight', '', b'1', b'0', NULL, NULL, NULL, NULL, NULL, '总重（吨）', 8, b'0', 'NUMBER', b'0', 50);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (445, NULL, b'0', b'1', 'workMode', '', b'1', b'0', NULL, NULL, NULL, NULL, NULL, '作业方式', 9, b'0', 'INPUT', b'1', 50);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (446, NULL, b'0', b'1', 'workType', '装卸船', b'1', b'1', NULL, NULL, NULL, NULL, NULL, '作业类型', 10, b'0', 'INPUT', b'1', 50);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (447, NULL, b'0', b'0', 'workState', '', b'1', b'1', NULL, NULL, NULL, NULL, NULL, '作业状态', 11, b'0', 'CHOICE', b'1', 50);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (448, NULL, b'0', b'1', 'riskDatabase', '', b'1', b'0', NULL, NULL, NULL, NULL, NULL, '风险数据库', 12, b'0', 'CHOICE', b'0', 50);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (449, NULL, b'0', b'1', 'ship', '', b'0', b'0', NULL, NULL, NULL, NULL, NULL, '船名', 13, b'0', 'INPUT', b'0', 50);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (450, NULL, b'0', b'1', 'address', '', b'0', b'0', NULL, NULL, NULL, NULL, NULL, '作业地点', 14, b'0', 'CHOICE', b'0', 50);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (451, NULL, b'0', b'0', 'addressLongitude', '', b'1', b'0', NULL, NULL, NULL, NULL, NULL, '作业地点_经度', 15, b'0', 'NUMBER', b'0', 50);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (452, '', b'0', b'1', 'company', 'remote.entity=main|Enterprise|administrator,name', b'1', b'0', 'TRUE', '', '', '', '', '企业名称', 1, b'0', 'CHOICE', b'1', 51);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (453, NULL, b'0', b'1', 'name', '', b'1', b'0', NULL, '', '', '', '', '作业名称', 2, b'0', 'INPUT', b'1', 51);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (454, NULL, b'0', b'1', 'beginTime', '', b'1', b'1', NULL, '', '', '', '', '计划开始时间', 3, b'0', 'DATE', b'0', 51);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (455, NULL, b'0', b'1', 'endTime', '', b'1', b'0', NULL, NULL, NULL, NULL, NULL, '计划结束时间', 4, b'0', 'DATE', b'0', 51);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (456, NULL, b'0', b'1', 'abTime', '', b'1', b'0', NULL, NULL, NULL, NULL, NULL, '实际开始时间', 5, b'0', 'DATE', b'1', 51);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (457, NULL, b'0', b'1', 'aeTime', '', b'1', b'0', NULL, NULL, NULL, NULL, NULL, '实际结束时间', 6, b'0', 'DATE', b'1', 51);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (458, NULL, b'0', b'1', 'goodsName', '', b'1', b'0', NULL, NULL, NULL, NULL, NULL, '货种名称', 7, b'0', 'INPUT', b'0', 51);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (459, NULL, b'0', b'1', 'weight', '', b'1', b'0', NULL, NULL, NULL, NULL, NULL, '总重（吨）', 8, b'0', 'NUMBER', b'0', 51);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (460, NULL, b'0', b'1', 'workMode', '', b'1', b'0', NULL, NULL, NULL, NULL, NULL, '作业方式', 9, b'0', 'INPUT', b'1', 51);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (461, NULL, b'0', b'1', 'workType', '火车|汽车', b'1', b'1', NULL, NULL, NULL, NULL, NULL, '作业类型', 10, b'0', 'CHOICE', b'1', 51);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (462, NULL, b'0', b'0', 'workState', '', b'1', b'1', NULL, NULL, NULL, NULL, NULL, '作业状态', 11, b'0', 'CHOICE', b'1', 51);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (463, NULL, b'0', b'1', 'riskDatabase', '', b'1', b'0', NULL, NULL, NULL, NULL, NULL, '风险数据库', 12, b'0', 'CHOICE', b'0', 51);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (464, NULL, b'0', b'1', 'address', '', b'0', b'0', NULL, NULL, NULL, NULL, NULL, '作业地点', 13, b'0', 'CHOICE', b'0', 51);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (465, NULL, b'0', b'0', 'addressLongitude', '', b'1', b'0', NULL, NULL, NULL, NULL, NULL, '作业地点_经度', 14, b'0', 'NUMBER', b'0', 51);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (466, NULL, b'0', b'0', 'addressLatitude', '', b'1', b'0', NULL, NULL, NULL, NULL, NULL, '作业地点_纬度', 15, b'0', 'NUMBER', b'0', 51);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (467, NULL, b'0', b'1', 'licence', '', b'0', b'0', NULL, NULL, NULL, NULL, NULL, '车牌号', 16, b'0', 'INPUT', b'0', 51);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (468, NULL, b'0', b'1', 'driver', '', b'0', b'0', NULL, NULL, NULL, NULL, NULL, '驾驶员', 17, b'0', 'INPUT', b'0', 51);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (469, NULL, b'0', b'1', 'escort', '', b'0', b'0', NULL, NULL, NULL, NULL, NULL, '押运员', 18, b'0', 'INPUT', b'0', 51);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (470, '', b'0', b'1', 'company', 'remote.entity=main|Enterprise|administrator,name', b'1', b'0', 'TRUE', '', '', '', '', '企业名称', 1, b'0', 'CHOICE', b'1', 52);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (471, NULL, b'0', b'1', 'workType', '', b'1', b'1', NULL, NULL, NULL, NULL, NULL, '作业类型', 2, b'0', 'CHOICE', b'1', 52);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (472, NULL, b'0', b'1', 'starDate', '', b'1', b'0', NULL, NULL, NULL, NULL, NULL, '开始时间', 3, b'0', 'DATE_TIME', b'1', 52);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (473, NULL, b'0', b'1', 'endDate', '', b'1', b'0', NULL, NULL, NULL, NULL, NULL, '结束时间', 4, b'0', 'DATE_TIME', b'1', 52);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (474, NULL, b'0', b'1', 'address', '', b'0', b'0', NULL, NULL, NULL, NULL, NULL, '作业地点', 5, b'0', 'CHOICE', b'0', 52);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (475, NULL, b'0', b'0', 'addressLongitude', '', b'1', b'0', NULL, NULL, NULL, NULL, NULL, '作业地点_经度', 6, b'0', 'NUMBER', b'0', 52);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (476, NULL, b'0', b'0', 'addressLatitude', '', b'1', b'0', NULL, NULL, NULL, NULL, NULL, '作业地点_纬度', 7, b'0', 'NUMBER', b'0', 52);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (477, NULL, b'0', b'1', 'file', '20|base|pdf/doc/docx/xlsx/png/jpg/xls/ppt/pptx/zip/rar', b'1', b'0', NULL, NULL, NULL, NULL, NULL, '作业票', 8, b'0', 'ATTACHMENT', b'0', 52);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (478, NULL, b'0', b'0', 'state', '未开始|已开始|已结束', b'1', b'1', NULL, NULL, NULL, NULL, NULL, '状态', 9, b'0', 'CHOICE', b'1', 52);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (479, NULL, b'0', b'1', 'workName', NULL, b'1', b'1', NULL, NULL, NULL, NULL, NULL, '作业名称', 10, b'0', 'INPUT', b'1', 52);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (480, '', b'0', b'1', 'company', 'remote.entity=main|Enterprise|administrator,name', b'1', b'0', 'TRUE', '', '', '', '', '企业名称', 1, b'0', 'CHOICE', b'1', 53);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (481, NULL, b'0', b'1', 'number', '', b'1', b'0', NULL, NULL, NULL, NULL, NULL, '储罐编号', 2, b'0', 'CHOICE', b'1', 53);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (482, NULL, b'0', b'1', 'workType', '储罐储存', b'1', b'1', NULL, NULL, NULL, NULL, NULL, '作业类型', 3, b'0', 'INPUT', b'1', 53);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (483, NULL, b'0', b'0', 'yardType', '', b'1', b'0', NULL, NULL, NULL, NULL, NULL, '现存货种', 4, b'0', 'CHOICE', b'1', 53);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (484, NULL, b'0', b'0', 'now', '', b'1', b'0', NULL, NULL, NULL, NULL, NULL, '当前存储量', 5, b'0', 'INPUT', b'1', 53);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (485, NULL, b'0', b'1', 'longitude', NULL, b'0', b'0', NULL, NULL, NULL, NULL, NULL, '经度_位置', 6, b'0', 'NUMBER', b'0', 53);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (486, NULL, b'0', b'1', 'latitude', NULL, b'0', b'0', NULL, NULL, NULL, NULL, NULL, '纬度_位置', 7, b'0', 'NUMBER', b'0', 53);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (487, NULL, b'0', b'1', 'yardNum', NULL, b'1', b'0', NULL, NULL, NULL, NULL, NULL, '堆场/仓库编号', 8, b'0', 'CHOICE', b'0', 53);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (488, NULL, b'0', b'1', 'yardNow', NULL, b'1', b'0', NULL, NULL, NULL, NULL, NULL, '货种储存情况', 9, b'0', 'INPUT', b'0', 53);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (489, '', b'0', b'1', 'company', 'remote.entity=main|Enterprise|administrator,name', b'1', b'0', 'TRUE', '', '', '', '', '企业名称', 1, b'0', 'CHOICE', b'1', 54);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (490, NULL, b'0', b'1', 'number', '', b'1', b'0', NULL, NULL, NULL, NULL, NULL, '储罐编号', 2, b'0', 'CHOICE', b'1', 54);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (491, NULL, b'0', b'1', 'workType', '堆场储存', b'1', b'1', NULL, NULL, NULL, NULL, NULL, '作业类型', 3, b'0', 'INPUT', b'1', 54);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (492, NULL, b'0', b'0', 'yardType', '', b'1', b'0', NULL, NULL, NULL, NULL, NULL, '现存货种', 4, b'0', 'CHOICE', b'1', 54);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (493, NULL, b'0', b'0', 'now', '', b'1', b'0', NULL, NULL, NULL, NULL, NULL, '当前存储量', 5, b'0', 'INPUT', b'1', 54);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (494, NULL, b'0', b'1', 'longitude', NULL, b'0', b'0', NULL, NULL, NULL, NULL, NULL, '经度_位置', 6, b'0', 'NUMBER', b'0', 54);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (495, NULL, b'0', b'1', 'latitude', NULL, b'0', b'0', NULL, NULL, NULL, NULL, NULL, '纬度_位置', 7, b'0', 'NUMBER', b'0', 54);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (496, NULL, b'0', b'1', 'yardNum', NULL, b'1', b'0', NULL, NULL, NULL, NULL, NULL, '堆场/仓库编号', 8, b'0', 'CHOICE', b'0', 54);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (497, NULL, b'0', b'1', 'yardNow', NULL, b'1', b'0', NULL, NULL, NULL, NULL, NULL, '货种储存情况', 9, b'0', 'INPUT', b'0', 54);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (498, '', b'0', b'1', 'company', 'remote.entity=main|Enterprise|administrator,name', b'1', b'0', 'TRUE', '', '', '', '', '企业名称', 1, b'0', 'CHOICE', b'1', 55);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (499, NULL, b'0', b'1', 'number', '', b'1', b'0', NULL, NULL, NULL, NULL, NULL, '储罐编号', 2, b'0', 'CHOICE', b'1', 55);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (500, NULL, b'0', b'1', 'workType', '仓库储存', b'1', b'1', NULL, NULL, NULL, NULL, NULL, '作业类型', 3, b'0', 'INPUT', b'1', 55);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (501, NULL, b'0', b'0', 'yardType', '', b'1', b'0', NULL, NULL, NULL, NULL, NULL, '现存货种', 4, b'0', 'CHOICE', b'1', 55);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (502, NULL, b'0', b'0', 'now', '', b'1', b'0', NULL, NULL, NULL, NULL, NULL, '当前存储量', 5, b'0', 'INPUT', b'1', 55);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (503, NULL, b'0', b'1', 'longitude', NULL, b'0', b'0', NULL, NULL, NULL, NULL, NULL, '经度_位置', 6, b'0', 'NUMBER', b'0', 55);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (504, NULL, b'0', b'1', 'latitude', NULL, b'0', b'0', NULL, NULL, NULL, NULL, NULL, '纬度_位置', 7, b'0', 'NUMBER', b'0', 55);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (505, NULL, b'0', b'1', 'yardNum', NULL, b'1', b'0', NULL, NULL, NULL, NULL, NULL, '堆场/仓库编号', 8, b'0', 'CHOICE', b'0', 55);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (506, NULL, b'0', b'1', 'yardNow', NULL, b'1', b'0', NULL, NULL, NULL, NULL, NULL, '货种储存情况', 9, b'0', 'INPUT', b'0', 55);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (507, NULL, b'0', b'1', 'riskType', '港口企业危险货物罐区泄漏中毒、火灾爆炸风险|港口企业危险货物堆场仓库泄漏中毒、火灾爆炸风险|港口企业危险货物码头装卸作业泄漏中毒、火灾爆炸风险|港口企业危险货物罐区检维修作业中毒窒息、火灾爆炸风险|港口企业液体危险货物装、卸车作业中毒窒息、火灾爆炸风险', b'1', b'1', NULL, '', '', '', '', '风险类型', 1, b'0', 'CHOICE', b'1', 56);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (508, NULL, b'0', b'1', 'riskRange', '', b'1', b'0', NULL, '', '', '', '', '风险辨识范围', 2, b'0', 'TEXTAREA', b'1', 56);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (509, NULL, b'0', b'1', 'work', '', b'1', b'0', NULL, NULL, NULL, NULL, NULL, '作业单元', 3, b'0', 'TEXTAREA', b'1', 56);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (510, NULL, b'0', b'1', 'event', '', b'1', b'0', NULL, NULL, NULL, NULL, NULL, '风险事件', 4, b'0', 'TEXTAREA', b'1', 56);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (511, NULL, b'0', b'0', 'fileDivide', '', b'0', b'0', NULL, NULL, NULL, NULL, NULL, '致险因素', 5, b'0', 'DIVIDE', b'0', 56);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (512, NULL, b'0', b'1', 'factorPerson', '', b'1', b'0', NULL, NULL, NULL, NULL, NULL, '致险因素_人的因素', 6, b'0', 'TEXTAREA', b'1', 56);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (513, NULL, b'0', b'1', 'factorDevice', '', b'1', b'0', NULL, NULL, NULL, NULL, NULL, '致险因素_设备设施因素', 7, b'0', 'TEXTAREA', b'1', 56);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (514, NULL, b'0', b'1', 'factorEnv', '', b'1', b'0', NULL, NULL, NULL, NULL, NULL, '致险因素_环境因素', 8, b'0', 'TEXTAREA', b'1', 56);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (515, NULL, b'0', b'1', 'factorManage', '', b'1', b'0', NULL, NULL, NULL, NULL, NULL, '致险因素_管理因素', 9, b'0', 'TEXTAREA', b'1', 56);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (516, NULL, b'0', b'1', 'measure', '', b'1', b'0', NULL, NULL, NULL, NULL, NULL, '风险管控措施', 10, b'0', 'TEXTAREA', b'1', 56);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (517, NULL, b'0', b'1', 'description', '', b'1', b'0', NULL, NULL, NULL, NULL, NULL, '变更说明', 11, b'0', 'TEXTAREA', b'1', 56);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (518, '', b'0', b'1', 'name', '', b'1', b'1', NULL, '启用|fa-toggle-off', '禁用|fa-toggle-on', '', '', '姓名', 1, b'0', 'INPUT', b'1', 57);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (519, NULL, b'0', b'1', 'sex', '男|女', b'1', b'1', 'TRUE', '', '', '', '', '性别', 2, b'0', 'BOOLEAN', b'1', 57);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (520, NULL, b'0', b'1', 'tel', '', b'1', b'0', 'TRUE', '', '', '', '', '手机号', 3, b'0', 'INPUT', b'1', 57);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (521, NULL, b'0', b'1', 'company', 'remote.entity=main|Enterprise|administrator,name', b'1', b'0', 'TRUE', NULL, NULL, NULL, NULL, '企业名称', 4, b'0', 'CHOICE', b'1', 57);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (522, NULL, b'0', b'1', 'checkScope', '集装箱|油品|其他', b'1', b'0', NULL, NULL, NULL, NULL, NULL, '检查范围', 5, b'0', 'CHOICE', b'1', 57);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (523, NULL, b'0', b'1', 'stateBy', '已启用|已禁用|未启用', b'1', b'0', NULL, NULL, NULL, NULL, NULL, '状态', 6, b'0', 'CHOICE', b'1', 57);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (524, NULL, b'0', b'1', 'number', '', b'1', b'0', NULL, NULL, NULL, NULL, NULL, '执法证号', 7, b'0', 'INPUT', b'1', 57);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (525, NULL, b'0', b'1', 'evadeCompany', '', b'1', b'0', NULL, NULL, NULL, NULL, NULL, '规避企业', 8, b'0', 'CHOICE', b'1', 57);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (526, NULL, b'0', b'1', 'name', '', b'1', b'1', NULL, '启用|fa-toggle-off', '禁用|fa-toggle-on', '专家二维码|fa-qrcode', '审核|fa-check-square-o', '姓名', 1, b'0', 'INPUT', b'1', 58);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (527, NULL, b'0', b'1', 'sex', '男|女', b'1', b'1', NULL, NULL, NULL, NULL, NULL, '性别', 2, b'0', 'BOOLEAN', b'1', 58);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (528, NULL, b'0', b'1', 'date', '', b'1', b'0', NULL, NULL, NULL, NULL, NULL, '出生日期', 3, b'0', 'DATE', b'1', 58);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (529, NULL, b'0', b'1', 'educational', '', b'1', b'0', NULL, NULL, NULL, NULL, NULL, '学历', 4, b'0', 'CHOICE', b'1', 58);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (530, NULL, b'0', b'1', 'checkScope', '集装箱|油品|其他', b'1', b'0', NULL, NULL, NULL, NULL, NULL, '检查范围', 5, b'0', 'CHOICE', b'1', 58);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (531, NULL, b'0', b'1', 'category', '', b'1', b'0', NULL, NULL, NULL, NULL, NULL, '专业类别', 6, b'0', 'INPUT', b'1', 58);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (532, NULL, b'0', b'1', 'technical', '正高级工程师|高级工程师|工程师|助理工程师|技术员', b'1', b'0', NULL, NULL, NULL, NULL, NULL, '职称', 7, b'0', 'CHOICE', b'1', 58);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (533, NULL, b'0', b'1', 'company', 'remote.entity=main|Enterprise|administrator,name', b'1', b'0', NULL, NULL, NULL, NULL, NULL, '企业名称', 8, b'0', 'CHOICE', b'1', 58);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (534, NULL, b'0', b'1', 'evadeCompany', '', b'1', b'0', NULL, NULL, NULL, NULL, NULL, '规避企业', 9, b'0', 'CHOICE', b'1', 58);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (535, NULL, b'0', b'1', 'file', 'Qualification', b'1', b'0', NULL, NULL, NULL, NULL, NULL, '资质证书', 10, b'0', 'TAB_TABLE_ADD', b'1', 58);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (536, NULL, b'0', b'1', 'law', 'LawenForcement', b'1', b'0', NULL, NULL, NULL, NULL, NULL, '执法信息', 11, b'0', 'TAB_TABLE_ADD', b'1', 58);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (537, NULL, b'0', b'1', 'auditState', '未审核|未通过|已通过', b'1', b'0', NULL, NULL, NULL, NULL, NULL, '状态', 12, b'0', 'CHOICE', b'1', 58);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (538, NULL, b'0', b'1', 'name', '', b'1', b'0', NULL, '', '', '', '', '证书名称', 1, b'0', 'INPUT', b'1', 59);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (539, NULL, b'0', b'1', 'file', '1|base|pdf/doc/xlsx', b'1', b'0', NULL, NULL, NULL, NULL, NULL, '附件名称', 2, b'0', 'ATTACHMENT', b'1', 59);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (540, NULL, b'0', b'1', 'nameOfExpert', '', b'1', b'1', NULL, NULL, NULL, NULL, NULL, '专家姓名', 3, b'0', 'CHOICE', b'1', 59);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (541, NULL, b'0', b'1', 'checkScope', '', b'1', b'0', NULL, '', '', '', '', '检查范围', 1, b'0', 'CHOICE', b'1', 60);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (542, NULL, b'0', b'1', 'evade', 'remote.entity=main|Enterprise|administrator,name', b'1', b'0', NULL, NULL, NULL, NULL, NULL, '规避企业', 2, b'0', 'CHOICE', b'1', 60);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (543, NULL, b'0', b'1', 'nameOfExpert', NULL, b'1', b'1', NULL, NULL, NULL, NULL, NULL, '专家姓名', 3, b'0', 'CHOICE', b'1', 60);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (544, NULL, b'0', b'1', 'name', '', b'1', b'1', NULL, '审核|fa-check', '通过|fa-check', '不通过|fa-times', '', '专家姓名', 1, b'0', 'INPUT', b'1', 61);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (545, NULL, b'0', b'1', 'sex', '男|女', b'1', b'1', NULL, NULL, NULL, NULL, NULL, '性别', 2, b'0', 'BOOLEAN', b'1', 61);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (546, NULL, b'0', b'1', 'date', '', b'1', b'0', NULL, NULL, NULL, NULL, NULL, '出生日期', 3, b'0', 'DATE', b'1', 61);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (547, NULL, b'0', b'1', 'educational', '', b'1', b'0', NULL, NULL, NULL, NULL, NULL, '学历', 4, b'0', 'CHOICE', b'1', 61);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (548, NULL, b'0', b'1', 'category', '', b'1', b'0', NULL, NULL, NULL, NULL, NULL, '专业类别', 5, b'0', 'INPUT', b'1', 61);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (549, NULL, b'0', b'1', 'technical', '', b'1', b'0', NULL, NULL, NULL, NULL, NULL, '职称', 6, b'0', 'CHOICE', b'1', 61);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (550, NULL, b'0', b'1', 'company', 'remote.entity=main|Enterprise|administrator,name', b'1', b'0', NULL, NULL, NULL, NULL, NULL, '企业名称', 7, b'0', 'CHOICE', b'1', 61);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (551, NULL, b'0', b'1', 'creationTime', '', b'1', b'0', NULL, NULL, NULL, NULL, NULL, '提交时间', 8, b'0', 'DATE_TIME', b'1', 61);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (552, NULL, b'0', b'1', 'workYear', NULL, b'1', b'0', NULL, NULL, NULL, NULL, NULL, '工作年限', 9, b'0', 'NUMBER', b'1', 61);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (553, NULL, b'0', b'1', 'checkScope', '集装箱|油品|其他', b'1', b'0', NULL, NULL, NULL, NULL, NULL, '检查范围', 10, b'0', 'CHOICE', b'1', 61);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (554, NULL, b'0', b'1', 'audit', '通过|驳回', b'1', b'0', NULL, NULL, NULL, NULL, NULL, '审核结果', 11, b'0', 'CHOICE', b'1', 61);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (555, NULL, b'0', b'1', 'reason', '', b'0', b'0', NULL, NULL, NULL, NULL, NULL, '驳回原因', 12, b'0', 'TEXTAREA', b'1', 61);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (556, NULL, b'0', b'1', 'file', 'Auditcertificate', b'1', b'0', NULL, NULL, NULL, NULL, NULL, '资质证书', 13, b'0', 'TAB_TABLE_ADD', b'1', 61);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (557, NULL, b'0', b'1', 'name', '', b'1', b'0', NULL, '', '', '', '', '证书名称', 1, b'0', 'INPUT', b'1', 62);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (558, NULL, b'0', b'1', 'file', '20|base|pdf/doc/docx/xlsx/png/jpg/xls/ppt/pptx/zip/rar', b'1', b'0', NULL, NULL, NULL, NULL, NULL, '附件名称', 2, b'0', 'ATTACHMENT', b'1', 62);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (559, NULL, b'0', b'1', 'nameOfExpert', NULL, b'1', b'1', NULL, NULL, NULL, NULL, NULL, '专家姓名', 3, b'0', 'CHOICE', b'1', 62);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (560, NULL, b'0', b'1', 'inspectionItems', '', b'1', b'0', NULL, NULL, '', '', '', '检查事项', 1, b'0', 'INPUT', b'1', 63);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (561, NULL, b'0', b'1', 'checkFirst', '', b'1', b'0', NULL, NULL, NULL, NULL, NULL, '检查内容一级', 2, b'0', 'INPUT', b'1', 63);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (562, NULL, b'0', b'1', 'checkSecond', '', b'1', b'0', NULL, NULL, NULL, NULL, NULL, '检查内容二级', 3, b'0', 'TEXTAREA', b'1', 63);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (563, NULL, b'0', b'0', 'basis', '', b'1', b'0', NULL, NULL, NULL, NULL, NULL, '检查依据', 4, b'0', 'TEXTAREA', b'0', 63);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (564, NULL, b'0', b'0', 'method', '', b'1', b'0', NULL, NULL, NULL, NULL, NULL, '检查方法', 5, b'0', 'TEXTAREA', b'0', 63);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (565, NULL, b'0', b'0', 'standard', '', b'1', b'0', NULL, NULL, NULL, NULL, NULL, '检查标准', 6, b'0', 'TEXTAREA', b'0', 63);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (566, NULL, b'0', b'1', 'enterpriseType', '全类型|油气化工码头和罐区|集装箱|固体散装危险货物|客滚危险货物|其他', b'1', b'0', NULL, NULL, NULL, NULL, NULL, '适用企业类型', 7, b'0', 'CHOICE', b'1', 63);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (567, NULL, b'0', b'1', 'inspectionName', '', b'1', b'1', NULL, '导出检查事项|fa-arrow-down', '', '', '', '检查名称', 1, b'0', 'INPUT', b'0', 64);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (568, NULL, b'0', b'1', 'numberOfEnterprises', '', b'1', b'0', NULL, NULL, NULL, NULL, NULL, '检查企业数量', 2, b'0', 'NUMBER', b'0', 64);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (569, NULL, b'0', b'1', 'enterprises', 'remote.entity=main|Enterprise|administrator,name', b'1', b'1', NULL, NULL, NULL, NULL, NULL, '抽查企业', 3, b'0', 'CHOICE', b'0', 64);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (570, NULL, b'0', b'1', 'inspectionDate', '', b'1', b'1', NULL, NULL, NULL, NULL, NULL, '检查日期', 4, b'0', 'DATE', b'0', 64);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (571, NULL, b'0', b'1', 'inspectionExpert', '', b'1', b'0', NULL, NULL, NULL, NULL, NULL, '检查专家人数', 5, b'0', 'INPUT', b'0', 64);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (572, NULL, b'0', b'1', 'expert', '', b'1', b'1', NULL, NULL, NULL, NULL, NULL, '检查专家', 6, b'0', 'INPUT', b'0', 64);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (573, NULL, b'0', b'1', 'inspectorsNo', '', b'1', b'0', NULL, NULL, NULL, NULL, NULL, '检查人员数量', 7, b'0', 'NUMBER', b'0', 64);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (574, NULL, b'0', b'1', 'inspectors', NULL, b'1', b'1', NULL, NULL, NULL, NULL, NULL, '检查人员', 8, b'0', 'INPUT', b'0', 64);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (575, NULL, b'0', b'1', 'inspectionItems', '', b'1', b'0', NULL, NULL, NULL, NULL, NULL, '检查项目', 9, b'0', 'INPUT', b'0', 64);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (576, NULL, b'0', b'1', 'numberOfQuestions', '', b'1', b'0', NULL, NULL, NULL, NULL, NULL, '问题数量', 10, b'0', 'NUMBER', b'0', 64);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (577, NULL, b'0', b'1', 'rectified', '', b'1', b'0', NULL, NULL, NULL, NULL, NULL, '已整改数量', 11, b'0', 'NUMBER', b'0', 64);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (578, NULL, b'0', b'1', 'notRectified', '', b'1', b'0', NULL, NULL, NULL, NULL, NULL, '未整改数量', 12, b'0', 'NUMBER', b'0', 64);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (579, NULL, b'0', b'1', 'overdue', '', b'1', b'0', NULL, NULL, NULL, NULL, NULL, '逾期数', 13, b'0', 'NUMBER', b'0', 64);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (580, NULL, b'0', b'1', 'enterprise', 'EnterpriseInformation', b'1', b'0', NULL, NULL, NULL, NULL, NULL, '企业信息', 14, b'0', 'TAB_TABLE_ADD', b'0', 64);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (581, NULL, b'0', b'1', 'expertInfo', 'ExpertInformation', b'1', b'0', NULL, NULL, NULL, NULL, NULL, '专家信息', 15, b'0', 'TAB_TABLE_ADD', b'0', 64);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (582, NULL, b'0', b'1', 'inspectorsInfo', 'Inspector', b'1', b'0', NULL, NULL, NULL, NULL, NULL, '检查人员信息', 16, b'0', 'TAB_TABLE_ADD', b'0', 64);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (583, NULL, b'0', b'1', 'inspectionItemsInfo', 'InspectionItems', b'1', b'0', NULL, NULL, NULL, NULL, NULL, '检查事项信息', 17, b'0', 'TAB_TABLE_ADD', b'0', 64);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (584, NULL, b'0', b'1', 'enterpriseName', 'remote.entity=main|Enterprise|administrator,name', b'1', b'0', NULL, '手动选择|fa-hand-o-up', '系统抽取|fa-random', '', '', '企业名称', 1, b'0', 'TAGS', b'0', 65);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (585, NULL, b'0', b'1', 'legalRepresentative', '', b'1', b'0', NULL, NULL, NULL, NULL, NULL, '法定代表人', 2, b'0', 'INPUT', b'0', 65);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (586, NULL, b'0', b'1', 'competentDepartment', '', b'1', b'0', NULL, NULL, NULL, NULL, NULL, '主管部门', 3, b'0', 'INPUT', b'0', 65);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (587, NULL, b'0', b'1', 'category', '', b'1', b'0', NULL, NULL, NULL, NULL, NULL, '企业类别', 4, b'0', 'CHOICE', b'0', 65);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (588, NULL, b'0', b'1', 'portArea', '', b'1', b'0', NULL, NULL, NULL, NULL, NULL, '所属港区', 5, b'0', 'CHOICE', b'0', 65);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (589, NULL, b'0', b'1', 'lastSpotCheck', '', b'1', b'0', NULL, NULL, NULL, NULL, NULL, '上次抽查时间', 6, b'0', 'DATE', b'0', 65);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (590, NULL, b'0', b'1', 'nameOfExpert', '', b'0', b'0', NULL, '手动选择|fa-hand-o-up', '系统抽取|fa-random', '', '', '专家姓名', 1, b'0', 'INPUT', b'0', 66);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (591, NULL, b'0', b'1', 'sex', '男|女', b'1', b'0', NULL, NULL, NULL, NULL, NULL, '性别', 2, b'0', 'BOOLEAN', b'0', 66);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (592, NULL, b'0', b'1', 'post', '', b'1', b'0', NULL, NULL, NULL, NULL, NULL, '职称', 3, b'0', 'INPUT', b'0', 66);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (593, NULL, b'0', b'1', 'company', 'remote.entity=main|Enterprise|administrator,name', b'1', b'0', NULL, NULL, NULL, NULL, NULL, '企业名称', 4, b'0', 'CHOICE', b'0', 66);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (594, NULL, b'0', b'1', 'inspectionScope', '', b'1', b'0', NULL, NULL, NULL, NULL, NULL, '检查范围', 5, b'0', 'INPUT', b'0', 66);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (595, NULL, b'0', b'1', 'nameOfInspector', '', b'1', b'0', NULL, '手动选择|fa-hand-o-up', '系统抽取|fa-random', '', '', '检查人员姓名', 1, b'0', 'TAGS', b'0', 67);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (596, NULL, b'0', b'1', 'sex', '男|女', b'1', b'0', NULL, NULL, NULL, NULL, NULL, '性别', 2, b'0', 'BOOLEAN', b'0', 67);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (597, NULL, b'0', b'1', 'phone', '', b'1', b'0', NULL, NULL, NULL, NULL, NULL, '手机号', 3, b'0', 'INPUT', b'0', 67);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (598, NULL, b'0', b'1', 'company', 'remote.entity=main|Enterprise|administrator,name', b'1', b'0', NULL, NULL, NULL, NULL, NULL, '企业名称', 4, b'0', 'CHOICE', b'0', 67);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (599, NULL, b'0', b'1', 'inspectionScope', '', b'1', b'0', NULL, NULL, NULL, NULL, NULL, '检查范围', 5, b'0', 'INPUT', b'0', 67);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (600, NULL, b'0', b'1', 'inspectionItems', '', b'1', b'0', NULL, '手动选择|fa-hand-o-up', '系统抽取|fa-random', '', '', '检查事项', 1, b'0', 'TAGS', b'0', 68);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (601, NULL, b'0', b'1', 'firstLevel', '', b'1', b'0', NULL, NULL, NULL, NULL, NULL, '检查内容（一级）', 2, b'0', 'INPUT', b'0', 68);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (602, NULL, b'0', b'1', 'secondLevel', '', b'1', b'0', NULL, NULL, NULL, NULL, NULL, '检查内容（二级）', 3, b'0', 'INPUT', b'0', 68);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (603, NULL, b'0', b'1', 'inspectionBasis', '', b'1', b'0', NULL, NULL, NULL, NULL, NULL, '检查依据', 4, b'0', 'TEXTAREA', b'0', 68);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (604, NULL, b'0', b'0', 'number', '', b'1', b'0', NULL, '复查|fa-pause', '', '', '', '序号', 1, b'0', 'INPUT', b'0', 69);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (605, NULL, b'0', b'1', 'inspectionName', '', b'1', b'0', NULL, NULL, NULL, NULL, NULL, '检查名称', 2, b'0', 'CHOICE', b'1', 69);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (606, NULL, b'0', b'1', 'checkObject', '', b'1', b'0', NULL, NULL, NULL, NULL, NULL, '检查对象', 3, b'0', 'CHOICE', b'1', 69);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (607, NULL, b'0', b'1', 'inspectionItems', '', b'1', b'0', NULL, NULL, NULL, NULL, NULL, '检查事项', 4, b'0', 'CHOICE', b'1', 69);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (608, NULL, b'0', b'1', 'inspectionContent', '', b'1', b'0', NULL, NULL, NULL, NULL, NULL, '检查内容（一级）', 5, b'0', 'CHOICE', b'1', 69);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (609, NULL, b'0', b'1', 'problemDescription', '', b'1', b'0', NULL, NULL, NULL, NULL, NULL, '问题描述', 6, b'0', 'TEXTAREA', b'1', 69);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (610, NULL, b'0', b'1', 'isDanger', '是|否', b'1', b'0', NULL, NULL, NULL, NULL, NULL, '是否重大隐患', 7, b'0', 'BOOLEAN', b'1', 69);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (611, NULL, b'0', b'1', 'inspectionBasis', '', b'1', b'0', NULL, NULL, NULL, NULL, NULL, '检查依据', 8, b'0', 'INPUT', b'1', 69);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (612, NULL, b'0', b'1', 'proposal', '', b'1', b'0', NULL, NULL, NULL, NULL, NULL, '整改建议', 9, b'0', 'INPUT', b'1', 69);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (613, NULL, b'0', b'1', 'deadline', '', b'1', b'0', NULL, NULL, NULL, NULL, NULL, '整改截止时间（年月日）', 10, b'0', 'DATE', b'1', 69);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (614, NULL, b'0', b'1', 'enterpriseName', 'remote.entity=main|Enterprise|administrator,name', b'1', b'0', NULL, '整改|fa-pause', '', '', '', '企业名称', 1, b'0', 'CHOICE', b'0', 70);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (615, NULL, b'0', b'1', 'inspectionName', '', b'1', b'1', NULL, NULL, NULL, NULL, NULL, '检查名称', 2, b'0', 'CHOICE', b'1', 70);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (616, NULL, b'0', b'1', 'inspectionItems', '', b'1', b'0', NULL, NULL, NULL, NULL, NULL, '检查项目', 3, b'0', 'INPUT', b'1', 70);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (617, NULL, b'0', b'1', 'inspectionContent', '', b'1', b'0', NULL, NULL, NULL, NULL, NULL, '检查内容', 4, b'0', 'INPUT', b'1', 70);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (618, NULL, b'0', b'1', 'problemDescription', '', b'1', b'0', NULL, NULL, NULL, NULL, NULL, '问题描述', 5, b'0', 'INPUT', b'1', 70);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (619, NULL, b'0', b'1', 'inspectionBasis', '', b'1', b'0', NULL, NULL, NULL, NULL, NULL, '检查依据', 6, b'0', 'INPUT', b'0', 70);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (620, NULL, b'0', b'1', 'rectificationSuggestions', '', b'1', b'0', NULL, NULL, NULL, NULL, NULL, '整改建议', 7, b'0', 'INPUT', b'0', 70);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (621, NULL, b'0', b'1', 'deadline', '', b'1', b'0', NULL, NULL, NULL, NULL, NULL, '整改截止时间', 8, b'0', 'DATE', b'1', 70);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (622, NULL, b'0', b'1', 'description', '', b'1', b'0', NULL, NULL, NULL, NULL, NULL, '整改说明', 9, b'0', 'TEXTAREA', b'0', 70);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (623, NULL, b'0', b'1', 'supportingMaterials', '20|base|pdf/doc/xlsx/png/jpg/word/ppt/zip/rar', b'1', b'0', NULL, NULL, NULL, NULL, NULL, '整改证明材料', 10, b'0', 'ATTACHMENT', b'0', 70);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (624, NULL, b'0', b'1', 'rectificationStatus', '未整改|已整改', b'1', b'0', NULL, NULL, NULL, NULL, NULL, '整改状态', 11, b'0', 'BOOLEAN', b'1', 70);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (625, NULL, b'0', b'1', 'result', '待整改|待审核|通过|不通过', b'1', b'0', NULL, NULL, NULL, NULL, NULL, '整改结果', 12, b'0', 'CHOICE', b'1', 70);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (626, NULL, b'0', b'1', 'rectificationTime', '', b'1', b'0', NULL, NULL, NULL, NULL, NULL, '整改时间', 13, b'0', 'DATE', b'1', 70);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (627, NULL, b'0', b'1', 'beOverdue', '未逾期|已逾期', b'1', b'0', NULL, NULL, NULL, NULL, NULL, '是否逾期', 14, b'0', 'BOOLEAN', b'1', 70);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (628, NULL, b'0', b'1', 'rectificationResults', '通过|不通过', b'1', b'1', NULL, NULL, NULL, NULL, NULL, '整改结果', 15, b'0', 'BOOLEAN', b'1', 70);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (629, '', b'0', b'1', 'accidentName', '', b'1', b'1', '', '', '', '', '', '事故名称', 1, b'0', 'INPUT', b'1', 71);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (630, NULL, b'0', b'1', 'timeOfOccurrence', '', b'1', b'1', NULL, '', '', '', '', '发生时间', 2, b'0', 'DATE', b'1', 71);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (631, NULL, b'0', b'1', 'place', '', b'1', b'1', NULL, '', '', '', '', '事故地点（国家+地区）', 3, b'0', 'INPUT', b'1', 71);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (632, NULL, b'0', b'1', 'accidentType', '物体打击|车辆伤害|机械伤害|起重伤害|触电|淹溺|灼烫|火灾|高处坠落|坍塌|容器爆炸|其他爆炸|中毒和窒息|其他伤害|泄漏|涨压|溢油|设备设施损坏', b'1', b'1', NULL, NULL, NULL, NULL, NULL, '事故类型', 4, b'0', 'CHOICE', b'1', 71);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (633, NULL, b'0', b'1', 'accidentProcess', '', b'1', b'0', NULL, NULL, NULL, NULL, NULL, '事故过程及处置救援（限1000字）', 5, b'0', 'TEXTAREA', b'0', 71);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (634, NULL, b'0', b'1', 'accidentConsequence', '', b'1', b'0', NULL, NULL, NULL, NULL, NULL, '事故后果（限300字）', 6, b'0', 'TEXTAREA', b'0', 71);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (635, NULL, b'0', b'1', 'accidentCause', '', b'1', b'0', NULL, NULL, NULL, NULL, NULL, '事故直接原因（300字）', 7, b'0', 'TEXTAREA', b'0', 71);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (636, NULL, b'0', b'1', 'uploadReport', '20|base|pdf/doc/docx/xlsx/png/jpg/xls/ppt/pptx/zip/rar', b'1', b'0', NULL, NULL, NULL, NULL, NULL, '事故调查报告', 8, b'0', 'ATTACHMENT', b'0', 71);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (637, '', b'0', b'1', 'accidentName', '', b'1', b'1', '', '', '', '', '', '演练名称', 1, b'0', 'INPUT', b'1', 72);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (638, NULL, b'0', b'1', 'timeOfOccurrence', 'remote.entity=main|Enterprise|administrator,name', b'1', b'0', NULL, NULL, NULL, NULL, NULL, '演练部门', 2, b'0', 'CHOICE', b'1', 72);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (639, NULL, b'0', b'1', 'place', '', b'1', b'1', NULL, NULL, NULL, NULL, NULL, '演练日期', 3, b'0', 'DATE', b'1', 72);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (640, NULL, b'0', b'1', 'accidentType', '桌面演练|实战演练|单项演练|综合演练', b'1', b'1', NULL, NULL, NULL, NULL, NULL, '演练分类', 4, b'0', 'TAGS', b'1', 72);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (641, NULL, b'0', b'1', 'accidentLevel', '20|image|png/jpg/bmp', b'1', b'0', NULL, NULL, NULL, NULL, NULL, '现场照片', 5, b'0', 'ATTACHMENT', b'0', 72);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (642, NULL, b'0', b'1', 'keyWord', '20|base|pdf/doc/docx/xlsx/png/jpg/xls/ppt/pptx/zip/rar', b'1', b'0', NULL, NULL, NULL, NULL, NULL, '应急演练报告', 6, b'0', 'ATTACHMENT', b'0', 72);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (643, NULL, b'0', b'1', 'accidentProcess', '20|base|pdf/doc/docx/xlsx/png/jpg/xls/ppt/pptx/zip/rar', b'1', b'0', NULL, NULL, NULL, NULL, NULL, '签到表', 7, b'0', 'ATTACHMENT', b'0', 72);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (644, NULL, b'0', b'1', 'accidentConsequence', '20|base|pdf/doc/docx/xlsx/png/jpg/xls/ppt/pptx/zip/rar', b'1', b'0', NULL, NULL, NULL, NULL, NULL, '其他附件', 8, b'0', 'ATTACHMENT', b'0', 72);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (645, NULL, b'0', b'0', 'company', NULL, b'1', b'1', NULL, NULL, NULL, NULL, NULL, '企业', 9, b'0', 'INPUT', b'0', 72);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (646, '', b'0', b'1', 'dutyTime', '', b'1', b'0', '', '', '', '', '', '值守时间（年月日时分）', 1, b'0', 'DATE_TIME', b'1', 73);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (647, NULL, b'0', b'1', 'personOnDuty', '', b'1', b'0', NULL, NULL, NULL, NULL, NULL, '值守人', 2, b'0', 'INPUT', b'1', 73);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (648, NULL, b'0', b'1', 'tel', '', b'1', b'0', NULL, NULL, NULL, NULL, NULL, '联系方式', 3, b'0', 'INPUT', b'1', 73);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (649, NULL, b'0', b'1', 'company', 'remote.entity=main|Enterprise|administrator,name', b'1', b'0', NULL, NULL, NULL, NULL, NULL, '所属单位', 4, b'0', 'CHOICE', b'1', 73);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (650, '', b'0', b'1', 'parentDirectory', '', b'1', b'0', '', '新增分类|fa-address-book', '', '', '', '上级目录名称', 1, b'0', 'CHOICE', b'1', 74);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (651, NULL, b'0', b'1', 'directory', '', b'1', b'0', NULL, NULL, NULL, NULL, NULL, '目录名称', 2, b'0', 'CHOICE', b'1', 74);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (652, NULL, b'0', b'1', 'nameOfGoods', '', b'1', b'0', NULL, NULL, NULL, NULL, NULL, '货种名称', 3, b'0', 'CHOICE', b'1', 74);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (653, NULL, b'0', b'1', 'accidentType', '物体打击|车辆伤害|机械伤害|起重伤害|触电|淹溺|灼烫|火灾|高处坠落|坍塌|容器爆炸|其他爆炸|中毒和窒息|其他伤害|泄漏|涨压|溢油|设备设施损坏', b'1', b'0', NULL, NULL, NULL, NULL, NULL, '事故类型', 4, b'0', 'CHOICE', b'1', 74);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (654, NULL, b'0', b'1', 'IMDG', '', b'1', b'0', NULL, NULL, NULL, NULL, NULL, 'IMDG中的运输名称', 5, b'0', 'INPUT', b'1', 74);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (655, NULL, b'0', b'1', 'UN', '', b'1', b'0', NULL, NULL, NULL, NULL, NULL, 'UN号', 6, b'0', 'INPUT', b'1', 74);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (656, NULL, b'0', b'1', 'potentialDanger', '火灾或者爆炸|健康', b'1', b'0', NULL, NULL, NULL, NULL, NULL, '潜在危险', 7, b'0', 'BOOLEAN', b'1', 74);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (657, NULL, b'0', b'1', 'publicSafety', '通用|防护用品|疏散', b'1', b'0', NULL, NULL, NULL, NULL, NULL, '公众安全', 8, b'0', 'CHOICE', b'1', 74);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (658, NULL, b'0', b'1', 'emergency', '通用|轻微火灾|重大火灾|槽罐|卡车或拖车货物着火|溢出或泄漏|大量泄漏|急救', b'1', b'0', NULL, NULL, NULL, NULL, NULL, '应急响应', 9, b'0', 'CHOICE', b'1', 74);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (659, '', b'0', b'1', 'teamName', '', b'1', b'1', '', '', '', '', '', '队伍名称', 1, b'0', 'INPUT', b'1', 75);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (660, NULL, b'0', b'1', 'company', 'remote.entity=main|Enterprise|administrator,name', b'1', b'0', NULL, NULL, NULL, NULL, NULL, '所属单位', 2, b'0', 'CHOICE', b'1', 75);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (661, NULL, b'0', b'1', 'address', '', b'1', b'0', NULL, NULL, NULL, NULL, NULL, '地址', 3, b'0', 'INPUT', b'1', 75);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (662, NULL, b'0', b'1', 'personnelSize', '', b'1', b'0', NULL, NULL, NULL, NULL, NULL, '人员规模', 4, b'0', 'INPUT', b'1', 75);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (663, NULL, b'0', b'1', 'equipmentCondition', '', b'1', b'0', NULL, NULL, NULL, NULL, NULL, '装备情况', 5, b'0', 'INPUT', b'1', 75);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (664, NULL, b'0', b'1', 'personInCharge', '', b'1', b'0', NULL, NULL, NULL, NULL, NULL, '负责人', 6, b'0', 'INPUT', b'1', 75);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (665, NULL, b'0', b'1', 'contactNumber', '', b'1', b'0', NULL, NULL, NULL, NULL, NULL, '值守电话', 7, b'0', 'INPUT', b'1', 75);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (666, NULL, b'0', b'1', 'expertiseDescription', '', b'1', b'0', NULL, NULL, NULL, NULL, NULL, '技术专长描述', 8, b'0', 'TEXTAREA', b'0', 75);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (667, NULL, b'0', b'1', 'longitude', '', b'1', b'0', NULL, NULL, NULL, NULL, NULL, '经度', 9, b'0', 'NUMBER', b'0', 75);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (668, NULL, b'0', b'1', 'latitude', '', b'1', b'0', NULL, NULL, NULL, NULL, NULL, '纬度', 10, b'0', 'NUMBER', b'0', 75);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (669, NULL, b'0', b'0', 'map', NULL, b'0', b'0', NULL, NULL, NULL, NULL, NULL, '地图', 11, b'0', 'MAP', b'0', 75);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (670, '', b'0', b'1', 'name', '', b'1', b'1', '', '', '', '', '', '名称', 1, b'0', 'INPUT', b'1', 76);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (671, NULL, b'0', b'1', 'population', '', b'1', b'0', NULL, NULL, NULL, NULL, NULL, '人口（约）', 2, b'0', 'INPUT', b'1', 76);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (672, NULL, b'0', b'1', 'longitude', '', b'1', b'0', NULL, NULL, NULL, NULL, NULL, '经度', 3, b'0', 'NUMBER', b'0', 76);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (673, NULL, b'0', b'1', 'latitude', '', b'1', b'0', NULL, NULL, NULL, NULL, NULL, '纬度', 4, b'0', 'NUMBER', b'0', 76);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (674, NULL, b'0', b'0', 'map', NULL, b'0', b'0', NULL, NULL, NULL, NULL, NULL, '地图', 5, b'0', 'MAP', b'0', 76);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (675, '', b'0', b'1', 'name', '', b'1', b'1', '', '', '', '', '', '名称', 1, b'0', 'INPUT', b'1', 77);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (676, NULL, b'0', b'1', 'company', 'remote.entity=main|Enterprise|administrator,name', b'1', b'0', NULL, NULL, NULL, NULL, NULL, '企业名称', 2, b'0', 'CHOICE', b'1', 77);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (677, NULL, b'0', b'1', 'area', '', b'1', b'0', NULL, NULL, NULL, NULL, NULL, '面积（平方米）', 3, b'0', 'NUMBER', b'1', 77);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (678, NULL, b'0', b'1', 'numberOfPeople', '', b'1', b'0', NULL, NULL, NULL, NULL, NULL, '大约集合人数', 4, b'0', 'NUMBER', b'1', 77);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (679, NULL, b'0', b'1', 'parkingVehicles', '', b'1', b'0', NULL, NULL, NULL, NULL, NULL, '大约停放车辆数', 5, b'0', 'NUMBER', b'1', 77);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (680, NULL, b'0', b'1', 'longitude', '', b'1', b'0', NULL, NULL, NULL, NULL, NULL, '经度', 6, b'0', 'NUMBER', b'0', 77);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (681, NULL, b'0', b'1', 'latitude', '', b'1', b'0', NULL, NULL, NULL, NULL, NULL, '纬度', 7, b'0', 'NUMBER', b'0', 77);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (682, NULL, b'0', b'0', 'map', NULL, b'0', b'0', NULL, NULL, NULL, NULL, NULL, '地图', 8, b'0', 'MAP', b'0', 77);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (683, '', b'0', b'1', 'hospitalName', '', b'1', b'1', '', '', '', '', '', '医院名称', 1, b'0', 'INPUT', b'1', 78);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (684, NULL, b'0', b'1', 'hospitalLevel', '', b'1', b'1', NULL, NULL, NULL, NULL, NULL, '医院级别', 2, b'0', 'INPUT', b'1', 78);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (685, NULL, b'0', b'1', 'property', '公立|私立', b'1', b'1', NULL, NULL, NULL, NULL, NULL, '医院性质', 3, b'0', 'BOOLEAN', b'1', 78);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (686, NULL, b'0', b'1', 'contactNumber', '', b'1', b'0', NULL, NULL, NULL, NULL, NULL, '联系电话', 4, b'0', 'NUMBER', b'1', 78);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (687, NULL, b'0', b'1', 'scale', '', b'1', b'0', NULL, NULL, NULL, NULL, NULL, '规模与专长', 5, b'0', 'TEXTAREA', b'1', 78);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (688, NULL, b'0', b'1', 'address', '', b'1', b'0', NULL, NULL, NULL, NULL, NULL, '地址', 6, b'0', 'INPUT', b'1', 78);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (689, NULL, b'0', b'1', 'longitude', '', b'1', b'0', NULL, NULL, NULL, NULL, NULL, '经度', 7, b'0', 'NUMBER', b'0', 78);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (690, NULL, b'0', b'1', 'latitude', '', b'1', b'0', NULL, NULL, NULL, NULL, NULL, '纬度', 8, b'0', 'NUMBER', b'0', 78);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (691, NULL, b'0', b'0', 'map', NULL, b'0', b'0', NULL, NULL, NULL, NULL, NULL, '地图', 9, b'0', 'MAP', b'0', 78);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (692, '', b'0', b'1', 'accidentPool', '', b'1', b'1', '', '', '', '', '', '事故池名称或编号', 1, b'0', 'INPUT', b'1', 79);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (693, NULL, b'0', b'1', 'company', 'remote.entity=main|Enterprise|administrator,name', b'1', b'0', NULL, NULL, NULL, NULL, NULL, '企业名称', 2, b'0', 'CHOICE', b'1', 79);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (694, NULL, b'0', b'1', 'area', '', b'1', b'0', NULL, NULL, NULL, NULL, NULL, '容积（m3)', 3, b'0', 'NUMBER', b'1', 79);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (695, NULL, b'0', b'1', 'remarks', '', b'1', b'0', NULL, NULL, NULL, NULL, NULL, '备注', 4, b'0', 'TEXTAREA', b'1', 79);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (696, NULL, b'0', b'1', 'longitude', '', b'1', b'0', NULL, NULL, NULL, NULL, NULL, '经度', 5, b'0', 'NUMBER', b'0', 79);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (697, NULL, b'0', b'1', 'latitude', '', b'1', b'0', NULL, NULL, NULL, NULL, NULL, '纬度', 6, b'0', 'NUMBER', b'0', 79);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (698, NULL, b'0', b'0', 'map', NULL, b'0', b'0', NULL, NULL, NULL, NULL, NULL, '地图', 7, b'0', 'MAP', b'0', 79);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (699, '', b'0', b'1', 'materialReserve', '', b'1', b'1', '', '', '', '', '', '应急物资储备点名称', 1, b'0', 'INPUT', b'1', 80);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (700, NULL, b'0', b'1', 'company', 'remote.entity=main|Enterprise|administrator,name', b'1', b'0', NULL, NULL, NULL, NULL, NULL, '企业名称', 2, b'0', 'CHOICE', b'1', 80);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (701, NULL, b'0', b'1', 'materialType', '抢险救援车|侦检器材|警戒器材|灭火器材|通信器材|救生物资|破拆器材|堵漏器材|输转物资|洗消物资|排烟照明器材|其他物资', b'1', b'0', NULL, NULL, NULL, NULL, NULL, '物资类型', 3, b'0', 'TAGS', b'1', 80);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (702, NULL, b'0', b'1', 'contacts', '', b'1', b'0', NULL, NULL, NULL, NULL, NULL, '联系人', 4, b'0', 'INPUT', b'1', 80);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (703, NULL, b'0', b'1', 'contactNumber', '', b'1', b'0', NULL, NULL, NULL, NULL, NULL, '联系电话', 5, b'0', 'INPUT', b'1', 80);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (704, NULL, b'0', b'1', 'longitude', '', b'1', b'0', NULL, NULL, NULL, NULL, NULL, '经度', 6, b'0', 'NUMBER', b'0', 80);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (705, NULL, b'0', b'1', 'latitude', '', b'1', b'0', NULL, NULL, NULL, NULL, NULL, '纬度', 7, b'0', 'NUMBER', b'0', 80);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (706, NULL, b'0', b'0', 'map', NULL, b'0', b'0', NULL, NULL, NULL, NULL, NULL, '地图', 8, b'0', 'MAP', b'0', 80);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (707, '', b'0', b'1', 'nameOfExpert', '', b'1', b'1', '', '', '', '', '', '专家姓名', 1, b'0', 'INPUT', b'1', 81);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (708, NULL, b'0', b'1', 'sex', '男|女', b'1', b'0', NULL, NULL, NULL, NULL, NULL, '性别', 2, b'0', 'BOOLEAN', b'1', 81);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (709, NULL, b'0', b'1', 'post', '', b'1', b'0', NULL, NULL, NULL, NULL, NULL, '职务/职称', 3, b'0', 'INPUT', b'1', 81);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (710, NULL, b'0', b'1', 'workUnit', '', b'0', b'0', NULL, NULL, NULL, NULL, NULL, '工作单位', 4, b'0', 'INPUT', b'1', 81);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (711, NULL, b'0', b'1', 'contactNumber', '', b'1', b'0', NULL, NULL, NULL, NULL, NULL, '联系电话', 5, b'0', 'INPUT', b'1', 81);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (712, NULL, b'0', b'1', 'workArea', '', b'1', b'1', NULL, NULL, NULL, NULL, NULL, '工作领域', 6, b'0', 'INPUT', b'1', 81);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (713, NULL, b'0', b'1', 'professionalExpertise', '', b'1', b'1', NULL, NULL, NULL, NULL, NULL, '专业特长', 7, b'0', 'INPUT', b'1', 81);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (714, '', b'0', b'1', 'company', 'remote.entity=main|Enterprise|administrator,name', b'1', b'0', '', '', '', '', '', '企业名称', 1, b'0', 'CHOICE', b'1', 82);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (715, NULL, b'0', b'1', 'name', '', b'1', b'0', NULL, NULL, NULL, NULL, NULL, '事故名称', 2, b'0', 'INPUT', b'1', 82);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (716, NULL, b'0', b'1', 'time', '', b'1', b'0', NULL, NULL, NULL, NULL, NULL, '事故发生时间', 3, b'0', 'DATE', b'1', 82);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (717, NULL, b'0', b'1', 'address', '', b'1', b'0', NULL, NULL, NULL, NULL, NULL, '事故发生点', 4, b'0', 'INPUT', b'1', 82);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (718, NULL, b'0', b'1', 'file', '20|base|pdf/doc/docx/xlsx/png/jpg/xls/ppt/pptx/zip/rar', b'1', b'0', NULL, NULL, NULL, NULL, NULL, '应急救援评估附件', 5, b'0', 'ATTACHMENT', b'1', 82);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (719, '', b'0', b'0', 'num', NULL, b'1', b'0', '', '', '', '', '', '序号', 1, b'0', 'INPUT', b'0', 83);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (720, NULL, b'0', b'1', 'name', '', b'1', b'1', NULL, NULL, NULL, NULL, NULL, '主要素名称', 2, b'0', 'INPUT', b'1', 83);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (721, '', b'0', b'0', 'num', '', b'1', b'0', '', '', '', '', '', '序号', 1, b'0', 'INPUT', b'0', 84);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (722, NULL, b'0', b'1', 'name', '', b'1', b'1', NULL, NULL, NULL, NULL, NULL, '所属主要素', 2, b'0', 'CHOICE', b'1', 84);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (723, NULL, b'0', b'1', 'childName', '', b'1', b'0', NULL, NULL, NULL, NULL, NULL, '子要素名称', 3, b'0', 'INPUT', b'1', 84);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (724, NULL, b'0', b'1', 'score', '', b'1', b'0', NULL, NULL, NULL, NULL, NULL, '分数', 4, b'0', 'NUMBER', b'1', 84);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (725, NULL, b'0', b'1', 'weight', '', b'1', b'0', NULL, NULL, NULL, NULL, NULL, '权重', 5, b'0', 'NUMBER', b'1', 84);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (726, NULL, b'0', b'1', 'project', '', b'1', b'0', NULL, NULL, NULL, NULL, NULL, '项目', 6, b'0', 'INPUT', b'0', 84);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (727, NULL, b'0', b'1', 'content', '', b'1', b'0', NULL, NULL, NULL, NULL, NULL, '内容', 7, b'0', 'INPUT', b'0', 84);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (728, NULL, b'0', b'1', 'basis', '', b'1', b'0', NULL, NULL, NULL, NULL, NULL, '依据', 8, b'0', 'INPUT', b'0', 84);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (729, NULL, b'0', b'1', 'method', '', b'1', b'0', NULL, NULL, NULL, NULL, NULL, '评估办法', 9, b'0', 'INPUT', b'0', 84);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (730, NULL, b'0', b'1', 'rules', '', b'1', b'0', NULL, NULL, NULL, NULL, NULL, '评估细则', 10, b'0', 'INPUT', b'0', 84);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (731, NULL, b'0', b'1', 'childScore', '', b'1', b'0', NULL, NULL, NULL, NULL, NULL, '得分', 11, b'0', 'NUMBER', b'0', 84);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (732, '', b'0', b'1', 'excellentMinScore', NULL, b'1', b'0', '', '', '', '', '', '优良最低分数线', 1, b'0', 'NUMBER', b'1', 85);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (733, NULL, b'0', b'1', 'excellentMaxScore', NULL, b'1', b'0', NULL, '', '', '', '', '优良最高分数线', 2, b'0', 'NUMBER', b'1', 85);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (734, NULL, b'0', b'1', 'qualifiedMinScore', '', b'1', b'0', NULL, NULL, NULL, NULL, NULL, '合格最低分数线', 3, b'0', 'NUMBER', b'1', 85);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (735, NULL, b'0', b'1', 'qualifiedMaxScore', '', b'1', b'0', NULL, NULL, NULL, NULL, NULL, '合格最高分数线', 4, b'0', 'NUMBER', b'1', 85);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (736, NULL, b'0', b'1', 'unQqualifiedMinScore', '', b'1', b'0', NULL, NULL, NULL, NULL, NULL, '不合格最低分数线', 5, b'0', 'NUMBER', b'1', 85);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (737, NULL, b'0', b'1', 'unQqualifiedMaxScore', '', b'1', b'0', NULL, NULL, NULL, NULL, NULL, '不合格最高分数线', 6, b'0', 'NUMBER', b'1', 85);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (738, NULL, b'0', b'0', 'num', '', b'1', b'0', NULL, '发布|fa-paper-plane', '评估表|fa-server', '', '', '序号', 1, b'0', 'INPUT', b'0', 86);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (739, NULL, b'0', b'1', 'evaDate', '', b'1', b'0', NULL, NULL, NULL, NULL, NULL, '评估时间', 2, b'0', 'INPUT', b'1', 86);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (740, NULL, b'0', b'1', 'evaGroup', '', b'1', b'0', NULL, NULL, NULL, NULL, NULL, '评估小组组长', 3, b'0', 'INPUT', b'0', 86);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (741, NULL, b'0', b'1', 'evaDeputy', '', b'1', b'0', NULL, NULL, NULL, NULL, NULL, '评估小组副组长', 4, b'0', 'INPUT', b'0', 86);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (742, NULL, b'0', b'1', 'evaMember', '', b'1', b'0', NULL, NULL, NULL, NULL, NULL, '评估小组成员', 5, b'0', 'TEXTAREA', b'0', 86);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (743, NULL, b'0', b'1', 'company', 'remote.entity=main|Enterprise|administrator,name', b'1', b'0', NULL, NULL, NULL, NULL, NULL, '企业名称', 6, b'0', 'CHOICE', b'1', 86);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (744, NULL, b'0', b'1', 'evaluationForm', '20|base|pdf/doc/docx/xlsx/png/jpg/xls/ppt/pptx/zip/rar', b'1', b'0', NULL, NULL, NULL, NULL, NULL, '上传评估表', 7, b'0', 'ATTACHMENT', b'0', 86);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (745, NULL, b'0', b'1', 'notConform', '20|base|pdf/doc/docx/xlsx/png/jpg/xls/ppt/pptx/zip/rar', b'1', b'0', NULL, NULL, NULL, NULL, NULL, '上传不符合项记录表', 8, b'0', 'ATTACHMENT', b'0', 86);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (746, NULL, b'0', b'0', 'grade', '', b'1', b'1', NULL, NULL, NULL, NULL, NULL, '评估等级', 9, b'0', 'DATE', b'1', 86);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (747, NULL, b'0', b'0', 'evaPeople', '', b'1', b'0', NULL, NULL, NULL, NULL, NULL, '评估人', 10, b'0', 'INPUT', b'1', 86);
INSERT INTO `s_model_field` (`id`, `check_submit`, `check_update`, `edit_show`, `field_name`, `link_class`, `not_null`, `query`, `read_only`, `row_button1`, `row_button2`, `row_button3`, `row_button4`, `show_name`, `sort`, `sortable`, `type`, `view_show`, `class_id`) VALUES (748, NULL, b'0', b'0', 'evaEnd', NULL, b'1', b'0', NULL, NULL, NULL, NULL, NULL, '评估结果', 11, b'0', 'INPUT', b'1', 86);
COMMIT;

-- ----------------------------
-- Table structure for s_module_info
-- ----------------------------
DROP TABLE IF EXISTS `s_module_info`;
CREATE TABLE `s_module_info` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `data_model` varchar(255) DEFAULT NULL COMMENT '数据模型',
  `description` varchar(255) DEFAULT NULL COMMENT '模块描述',
  `menu_enabled` bit(1) NOT NULL COMMENT '菜单状态',
  `module_id` varchar(255) DEFAULT NULL COMMENT '模块编码',
  `module_name` varchar(255) DEFAULT NULL COMMENT '模块名称',
  `node_name` varchar(255) DEFAULT NULL COMMENT '节点名称',
  `package_name` varchar(255) DEFAULT NULL COMMENT '基准包名',
  `sort` int DEFAULT NULL COMMENT '序号',
  `source` varchar(255) DEFAULT NULL COMMENT '来源文件',
  PRIMARY KEY (`id`),
  UNIQUE KEY `UK_pwcnwaob608b5ckoevb15bjdk` (`module_id`)
) ENGINE=InnoDB AUTO_INCREMENT=13 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='模块管理';

-- ----------------------------
-- Records of s_module_info
-- ----------------------------
BEGIN;
INSERT INTO `s_module_info` (`id`, `data_model`, `description`, `menu_enabled`, `module_id`, `module_name`, `node_name`, `package_name`, `sort`, `source`) VALUES (1, 'DATA_AUTH_MAIN', NULL, b'1', 'workbench', 'workbench', 'workbench', 'com.daliangang.workbench', 1, '1危险货物港区重大安全风险管控平台门户');
INSERT INTO `s_module_info` (`id`, `data_model`, `description`, `menu_enabled`, `module_id`, `module_name`, `node_name`, `package_name`, `sort`, `source`) VALUES (8, 'DATA_AUTH_NODE', NULL, b'1', 'safe-daily', 'safe-daily', 'sm', 'com.daliangang.safedaily', 2, '2港口危险货物安全日常业务监管系统');
INSERT INTO `s_module_info` (`id`, `data_model`, `description`, `menu_enabled`, `module_id`, `module_name`, `node_name`, `package_name`, `sort`, `source`) VALUES (9, 'DATA_AUTH_NODE', NULL, b'1', 'device', 'device-manten', 'device', 'com.daliangang.device', 3, '3.港口设施维护管理系统');
INSERT INTO `s_module_info` (`id`, `data_model`, `description`, `menu_enabled`, `module_id`, `module_name`, `node_name`, `package_name`, `sort`, `source`) VALUES (10, 'DATA_AUTH_NODE', NULL, b'1', 'major-risk', 'major-risk', 'risk', 'com.daliangang.majorisk', 4, '4港区重大风险监测与管控系统');
INSERT INTO `s_module_info` (`id`, `data_model`, `description`, `menu_enabled`, `module_id`, `module_name`, `node_name`, `package_name`, `sort`, `source`) VALUES (11, 'DATA_AUTH_NODE', NULL, b'1', 'rnd-public', 'rnd-public', 'rndpub', 'com.daliangang.rndpub', 5, '5.港区危险货物企业“双随机、一公开”系统');
INSERT INTO `s_module_info` (`id`, `data_model`, `description`, `menu_enabled`, `module_id`, `module_name`, `node_name`, `package_name`, `sort`, `source`) VALUES (12, 'DATA_AUTH_NODE', NULL, b'1', 'emergency-event', 'emergency-event', 'emergency', 'com.daliangang.emergency', 7, '7.危险货物重大突发事件智慧管理系统');
COMMIT;

SET FOREIGN_KEY_CHECKS = 1;
