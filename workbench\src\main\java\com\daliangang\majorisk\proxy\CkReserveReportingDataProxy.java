/*
* Copyright © 2020-2035 yundingyun All rights reserved.
*/
package com.daliangang.majorisk.proxy;

import com.daliangang.core.DaliangangContext;
import com.daliangang.majorisk.entity.CkReserveReporting;
import org.springframework.stereotype.Service;
import xyz.erupt.annotation.fun.DataProxy;
import xyz.erupt.upms.model.EruptUser;
import xyz.erupt.upms.service.EruptUserService;

import javax.annotation.Resource;
import java.util.Date;
import java.util.Map;

@Service
public class CkReserveReportingDataProxy implements DataProxy<CkReserveReporting> {
    @Resource
    private EruptUserService eruptUserService;

    @Override
    public void beforeAdd(CkReserveReporting ckReserveReporting) {
        ckReserveReporting.setModifyTime(new Date());
    }

    @Override
    public void beforeUpdate(CkReserveReporting ckReserveReporting) {
        ckReserveReporting.setModifyTime(new Date());
    }

//    @Override
//    public void searchCondition(Map<String, Object> condition) {
//        if(!DaliangangContext.isDepartmentUser()){
//            EruptUser user = eruptUserService.getCurrentEruptUser();
//            if (user != null && user.getEruptOrg() != null)
//                condition.put("company", user.getEruptOrg().getCode());
//        }
//
//    }
}
