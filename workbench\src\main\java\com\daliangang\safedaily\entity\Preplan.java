/*
 * Copyright © 2020-2035 yundingyun All rights reserved.
 */
package com.daliangang.safedaily.entity;

import com.daliangang.core.CompanyRenderHandler;
import com.daliangang.safedaily.proxy.PreplanDataProxy;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import xyz.erupt.annotation.Erupt;
import xyz.erupt.annotation.EruptField;
import xyz.erupt.annotation.expr.ExprBool;
import xyz.erupt.annotation.sub_erupt.Power;
import xyz.erupt.annotation.sub_erupt.RowOperation;
import xyz.erupt.annotation.sub_field.Edit;
import xyz.erupt.annotation.sub_field.EditType;
import xyz.erupt.annotation.sub_field.Readonly;
import xyz.erupt.annotation.sub_field.View;
import xyz.erupt.annotation.sub_field.sub_edit.AttachmentType;
import xyz.erupt.annotation.sub_field.sub_edit.BoolType;
import xyz.erupt.annotation.sub_field.sub_edit.ChoiceType;
import xyz.erupt.annotation.sub_field.sub_edit.Search;
import xyz.erupt.toolkit.attachment.AttachmentDownloadHandler;
import xyz.erupt.toolkit.db.Comment;
import xyz.erupt.toolkit.handler.RemoteEntityChoiceFetchHandler;
import xyz.erupt.upms.model.auth.DataAuthHandler;
import xyz.erupt.upms.model.auth.DataAuthModel;
import xyz.erupt.upms.model.auth.OrgCodeRender;

import javax.persistence.*;
import java.util.Set;

@Erupt(name = "重大危险源备案", power = @Power(add = true, delete = true, export = false, importable = true, viewDetails = true, edit = true)
        , dataProxy = PreplanDataProxy.class
        , rowOperation = {
//        @RowOperation(title = "删除", icon = "fa fa-trash-o", operationHandler = DelOperationHandler.class, ifExpr = "item.submitted=='未上报'", ifExprBehavior = RowOperation.IfExprBehavior.HIDE, mode = RowOperation.Mode.SINGLE),
//        @RowOperation(title = "上报", icon = "fa fa-arrow-circle-o-up", operationHandler = PreplanEscalationHandler.class, ifExpr = "item.submitted=='未上报'", mode = RowOperation.Mode.SINGLE),
        @RowOperation(title = "下载附件", icon = "fa fa-download", operationHandler = AttachmentDownloadHandler.class, mode = RowOperation.Mode.SINGLE)
})
@Table(name = "tb_preplan")
@Entity
@Getter
@Setter
@Comment("重大危险源备案")
@ApiModel("重大危险源备案")
@ToString
@OrgCodeRender(render = false)
public class Preplan extends DataAuthModel {

    @EruptField(
            views = @View(title = "企业名称",width = "400px"),
            edit = @Edit(title = "企业名称", search = @Search(ifRender = @ExprBool(exprHandler = CompanyRenderHandler.class)),readonly = @Readonly(exprHandler = DataAuthHandler.class), type = EditType.CHOICE, notNull = true,
                    choiceType = @ChoiceType(fullSpan = true,fetchHandler = RemoteEntityChoiceFetchHandler.class, fetchHandlerParams = {"main", "Enterprise", "org_code,name"})))
    @Comment("企业名称")
    @ApiModelProperty("企业名称")
    private String company;

    @EruptField(
            views = @View(title = "重大危险源数量",width = "200px"),
            edit = @Edit(title = "重大危险源数量",
                    readonly = @Readonly,
                    type = EditType.INPUT,
                    show = false
            )
    )
    @Comment("重大危险源数量")
    @ApiModelProperty("重大危险源数量")
    private String dangerNum;

    @EruptField(
            views = @View(title = "上报状态",width = "200px", show = false),
            edit = @Edit(title = "上报状态", type = EditType.BOOLEAN, show = false, /*notNull = true, */
                    boolType = @BoolType(trueText = "已上报", falseText = "未上报")))
    @Comment("上报状态")
    @ApiModelProperty("上报状态")
    private Boolean submitted;

    @EruptField(
            views = @View(title = "附件", show = false),
            edit = @Edit(title = "附件", type = EditType.ATTACHMENT, notNull = true,
                    attachmentType = @AttachmentType(type = AttachmentType.Type.BASE,
                            tipMsg = "<t><font color='green'>辨识、分级记录，港口重大危险源基本特征表，危险货物安全技术说明书，区域位置图、平面布置图、工艺流程图和主要设备一览表，港口重大危险源安全管理制度及安全操作规程，安全监测监控系统、措施说明、检测、检验结果，港口重大危险源事故应急预案，安全评估报告或安全评价报告，港口重大危险源场所安全警示标志的设置情况，其他文件、资料</font>",
                            maxLimit = 20, fileTypes = {".pdf", ".doc", ".docx", ".xlsx", ".png", ".jpg", ".xls", ".ppt", ".pptx", ".zip", ".rar"})))
    @Comment("附件")
    @ApiModelProperty("附件")
    @Lob
    private String uploadFile;


//	@EruptField(
//			views = @View(title = "危险源信息", show = false),
//			edit = @Edit(title = "危险源信息", type = EditType.DIVIDE))
//	@Transient
//	@Comment("危险源信息")
//	@ApiModelProperty("危险源信息")
//	private String dataDivide;


    @EruptField(
            edit = @Edit(title = "危险源信息", type = EditType.TAB_TABLE_ADD, notNull = true))
    @OneToMany(cascade = CascadeType.ALL, orphanRemoval = true)
    @OrderBy
    @JoinColumn(name = "data_source_id")
    @Comment("危险源信息")
    @ApiModelProperty("危险源信息")
    private Set<PrePlanDangerSource> dangerSources;


}
