/*
 * Copyright © 2020-2035 yundingyun All rights reserved.
 */
package com.daliangang.majorisk.entity;

import com.daliangang.core.CompanyRenderHandler;
import com.daliangang.core.DaliangangContext;
import com.daliangang.device.entity.*;
import com.daliangang.majorisk.operation.UnloadDangerousGoodsDeclarationSystemHandler;
import com.daliangang.majorisk.proxy.UnloadShipDataProxy;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import xyz.erupt.annotation.Erupt;
import xyz.erupt.annotation.EruptField;
import xyz.erupt.annotation.expr.ExprBool;
import xyz.erupt.annotation.fun.FilterHandler;
import xyz.erupt.annotation.sub_erupt.Filter;
import xyz.erupt.annotation.sub_erupt.Power;
import xyz.erupt.annotation.sub_erupt.RowOperation;
import xyz.erupt.annotation.sub_field.Edit;
import xyz.erupt.annotation.sub_field.EditType;
import xyz.erupt.annotation.sub_field.Readonly;
import xyz.erupt.annotation.sub_field.View;
import xyz.erupt.annotation.sub_field.sub_edit.*;
import xyz.erupt.core.context.MetaContext;
import xyz.erupt.core.util.EruptSpringUtil;
import xyz.erupt.toolkit.db.Comment;
import xyz.erupt.toolkit.handler.EruptDictCodeChoiceFetchHandler;
import xyz.erupt.toolkit.handler.RemoteCallChoiceFetchHandler;
import xyz.erupt.toolkit.handler.RemoteEntityChoiceFetchHandler;
import xyz.erupt.toolkit.service.EruptCacheRedis;
import xyz.erupt.upms.model.EruptUser;
import xyz.erupt.upms.model.auth.DataAuthHandler;
import xyz.erupt.upms.model.auth.DataAuthModel;
import xyz.erupt.upms.service.EruptUserService;

import javax.persistence.*;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.Set;

@Erupt(name = "装卸船作业", power = @Power(add = true, delete = false, export = false, importable = true, viewDetails = true, edit = true)
        , dataProxy = UnloadShipDataProxy.class,
        orderBy = "UnloadShip.abTime desc",
        rowOperation = {
                @RowOperation(title = "危货申报系统", icon = "fa Example ofcloud-upload", show = @ExprBool(exprHandler = UnloadShip.class), operationHandler = UnloadDangerousGoodsDeclarationSystemHandler.class, mode = RowOperation.Mode.BUTTON)
        })
@Table(name = "tb_unload_ship")
@Entity
@Getter
@Setter
@Comment("装卸船作业")
@ApiModel("装卸船作业")
public class UnloadShip extends DataAuthModel implements FilterHandler, ExprBool.ExprHandler {
    @EruptField(
            views = @View(title = "企业名称"),
            edit = @Edit(title = "企业名称", search = @Search(ifRender = @ExprBool(exprHandler = CompanyRenderHandler.class)),
                    readonly = @Readonly(exprHandler = DataAuthHandler.class), type = EditType.CHOICE, notNull = true,
                    choiceType = @ChoiceType(fullSpan = true,fetchHandler = RemoteEntityChoiceFetchHandler.class, fetchHandlerParams = {"main", "Enterprise", "org_code,name"})))
    @Comment("企业名称")
    @ApiModelProperty("企业名称")
    private String company;

    @EruptField(
            views = @View(title = "作业名称"),
            edit = @Edit(title = "作业名称", type = EditType.INPUT, notNull = true,
                    inputType = @InputType))
    @Comment("作业名称")
    @ApiModelProperty("作业名称")
    private String name;

    @EruptField(
            views = @View(title = "计划开始时间"),
            edit = @Edit(title = "计划开始时间", type = EditType.DATE, search = @Search(vague = true), notNull = true,readonly = @Readonly,
                    dateType = @DateType(type = DateType.Type.DATE_TIME)))
    @Comment("计划开始时间")
    @ApiModelProperty("计划开始时间")
    private Date beginTime;

    @EruptField(
            views = @View(title = "计划结束时间", show = false),
            edit = @Edit(title = "计划结束时间", type = EditType.DATE, notNull = true,readonly = @Readonly,
                    dateType = @DateType(type = DateType.Type.DATE_TIME)))
    @Comment("计划结束时间")
    @ApiModelProperty("计划结束时间")
    private Date endTime;

    @EruptField(
            views = @View(title = "实际开始时间"),
            edit = @Edit(title = "实际开始时间", type = EditType.DATE, notNull = true,
                    dateType = @DateType(type = DateType.Type.DATE_TIME)))
    @Comment("实际开始时间")
    @ApiModelProperty("实际开始时间")
    private LocalDateTime abTime;

    @EruptField(
            views = @View(title = "实际结束时间"),
            edit = @Edit(title = "实际结束时间", type = EditType.DATE, notNull = true,
                    dateType = @DateType(type = DateType.Type.DATE_TIME)))
    @Comment("实际结束时间")
    @ApiModelProperty("实际结束时间")
    private LocalDateTime aeTime;

    @EruptField(
            views = @View(title = "货种名称", show = false),
            edit = @Edit(title = "货种名称", type = EditType.INPUT, notNull = true,readonly = @Readonly,
                    inputType = @InputType))
    @Comment("货种名称")
    @ApiModelProperty("货种名称")
    private String goodsName;

    @EruptField(
            views = @View(title = "总重（吨）", show = false),
            edit = @Edit(title = "总重（吨）", type = EditType.INPUT, notNull = true,readonly = @Readonly,
                    inputType = @InputType))
    @Comment("总重（吨）")
    @ApiModelProperty("总重（吨）")
    private Double weight;

    @EruptField(
            views = @View(title = "作业方式"),
            edit = @Edit(title = "作业方式", type = EditType.INPUT, notNull = true,readonly = @Readonly,
                    inputType = @InputType))
    @Comment("作业方式")
    @ApiModelProperty("作业方式")
    private String workMode;

    @EruptField(
            views = @View(title = "作业类型"),
            edit = @Edit(title = "作业类型", type = EditType.CHOICE, notNull = true,readonly = @Readonly,
                    choiceType = @ChoiceType(fetchHandler = EruptDictCodeChoiceFetchHandler.class, fetchHandlerParams = "workTypeShip")))
    @Comment("作业类型")
    @ApiModelProperty("作业类型")
    private String workType;

    @EruptField(
            views = @View(title = "作业状态"),
            edit = @Edit(title = "作业状态", type = EditType.INPUT, show = false,
                    inputType = @InputType))
    //choiceType = @ChoiceType(fetchHandler = SqlChoiceFetchHandler.class, fetchHandlerParams = "select '测试项','测试项'")))
    @Comment("作业状态")
    @ApiModelProperty("作业状态")
    private String workState;

    @EruptField(
            views = @View(title = "作业风险", show = false),
            edit = @Edit(title = "作业风险", type = EditType.CHOICE, notNull = true,
                    choiceType = @ChoiceType(reload = true,fullSpan = true, fetchHandler = RemoteCallChoiceFetchHandler.class, fetchHandlerParams = {"main", "erupt-api/get/nowRiskDatabaseEnterprise"})
            )
    )
    @Comment("作业风险")
    @ApiModelProperty("作业风险")
    private String riskDatabase;

    @EruptField(
            views = @View(title = "船名", show = false),
            edit = @Edit(title = "船名", type = EditType.INPUT,
                    inputType = @InputType))
    @Comment("船名")
    @ApiModelProperty("船名")
    private String ship;

    @EruptField(
            views = @View(title = "作业地点", show = false),
            edit = @Edit(title = "作业地点", type = EditType.INPUT, show = false,
                    inputType = @InputType))
    @Comment("作业地点")
    @ApiModelProperty("作业地点")
    private String address;

//	@EruptField(
//		views = @View(title = "作业地点", show = false),
//		edit = @Edit(title = "作业地点", type = EditType.CHOICE,
//		choiceType = @ChoiceType(fetchHandler = SqlChoiceFetchHandler.class, fetchHandlerParams = "select '测试项','测试项'")))
//	@Comment("作业地点")
//	@ApiModelProperty("作业地点")
//	private String address;

    @EruptField(
            views = @View(title = "作业地点id", show = false),
            edit = @Edit(title = "作业地点id", type = EditType.INPUT, show = false,
                    inputType = @InputType))
    @Comment("作业地点id")
    @ApiModelProperty("作业地点id")
    private String gId;

    @EruptField(
            views = @View(title = "作业地点类型", show = false),
            edit = @Edit(title = "作业地点类型", type = EditType.INPUT, show = false,
                    inputType = @InputType))
    @Comment("作业地点类型")
    @ApiModelProperty("作业地点类型")
    private String gType;

    @EruptField(
            views = @View(title = "作业id", show = false),
            edit = @Edit(title = "作业id", type = EditType.INPUT, show = false,
                    inputType = @InputType))
    @Comment("作业id")
    @ApiModelProperty("作业id")
    private String jobId;

    @EruptField(
            views = @View(title = "撤销状态", show = false),
            edit = @Edit(title = "撤销状态", type = EditType.INPUT, show = false))
    @Comment("撤销状态")
    @ApiModelProperty("撤销状态")
    private String cancelState;

//	@EruptField(
//		views = @View(title = "车牌号", show = false),
//		edit = @Edit(title = "车牌号", type = EditType.INPUT,
//		inputType = @InputType))
//	@Comment("车牌号")
//	@ApiModelProperty("车牌号")
//	private String licence;
//
//	@EruptField(
//		views = @View(title = "驾驶员", show = false),
//		edit = @Edit(title = "驾驶员", type = EditType.INPUT,
//		inputType = @InputType))
//	@Comment("驾驶员")
//	@ApiModelProperty("驾驶员")
//	private String driver;
//
//	@EruptField(
//		views = @View(title = "押运员", show = false),
//		edit = @Edit(title = "押运员", type = EditType.INPUT,
//		inputType = @InputType))
//	@Comment("押运员")
//	@ApiModelProperty("押运员")
//	private String escort;
    @EruptField(
        views = @View(title = "位置信息", show = false),
        edit = @Edit(title = "位置信息", type = EditType.TAGS,sceneBy=@SceneShowBy(enable=true,add = false,edit = false,view = true)))
    @Transient
    private String position;


    @ManyToMany(fetch = FetchType.EAGER)
    @EruptField(views = @View(title = "储罐"),
            edit = @Edit(title = "储罐",sceneBy=@SceneShowBy(enable=true,add = true,edit = true,view = false), filter = @Filter(conditionHandler = UnloadShip.class, params = "TANK"), type = EditType.TAB_TREE))
    @JoinTable(
            name = "tb_unload_ship_tanks",
            joinColumns = @JoinColumn(name = "unload_id", referencedColumnName = "id"),
            inverseJoinColumns = @JoinColumn(name = "tanks_id", referencedColumnName = "id"))
    private Set<StorageTank> tanks;

    @ManyToMany(fetch = FetchType.EAGER)
    @EruptField(views = @View(title = "堆场"),
            edit = @Edit(title = "堆场" ,sceneBy=@SceneShowBy(enable=true,add = true,edit = true,view = false),filter = @Filter(conditionHandler = UnloadShip.class, params = "YARD"), type = EditType.TAB_TREE))
    @JoinTable(
            name = "tb_unload_ship_yards",
            joinColumns = @JoinColumn(name = "unload_id", referencedColumnName = "id"),
            inverseJoinColumns = @JoinColumn(name = "yards_id", referencedColumnName = "id"))
    private Set<Yard> yards;

    @ManyToMany(fetch = FetchType.EAGER)
    @JoinTable(
            name = "tb_unload_ship_wares",
            joinColumns = @JoinColumn(name = "unload_id", referencedColumnName = "id"),
            inverseJoinColumns = @JoinColumn(name = "wares_id", referencedColumnName = "id"))
    @EruptField(views = @View(title = "仓库"),
            edit = @Edit(title = "仓库",sceneBy=@SceneShowBy(enable=true,add = true,edit = true,view = false),filter = @Filter(conditionHandler = UnloadShip.class, params = "WARE"), type = EditType.TAB_TREE))
    private Set<Warehouse> wares;

    @ManyToMany(fetch = FetchType.EAGER)
    @JoinTable(
            name = "tb_unload_ship_berths",
            joinColumns = @JoinColumn(name = "unload_id", referencedColumnName = "id"),
            inverseJoinColumns = @JoinColumn(name = "berths_id", referencedColumnName = "id"))
    @EruptField(views = @View(title = "泊位"),
            edit = @Edit(title = "泊位",sceneBy=@SceneShowBy(enable=true,add = true,edit = true,view = false),
                    filter = @Filter(conditionHandler = UnloadShip.class, params = "BERTH"),
                    type = EditType.TAB_TREE))
    private Set<Berth> berths;

    @ManyToMany(fetch = FetchType.EAGER)
    @EruptField(views = @View(title = "栈台"),
            edit = @Edit(title = "栈台",sceneBy=@SceneShowBy(enable=true,add = true,edit = true,view = false),filter = @Filter(conditionHandler = UnloadShip.class, params = "WHARF"), type = EditType.TAB_TREE))
    @JoinTable(
            name = "tb_unload_ship_wharfs",
            joinColumns = @JoinColumn(name = "unload_id", referencedColumnName = "id"),
            inverseJoinColumns = @JoinColumn(name = "wharfs_id", referencedColumnName = "id"))
    private Set<LoadingDock> wharfs;

    public static final String ATTR_UNLOAD_SHIP = "UNLOAD_SHIP";

    @Override
    public String filter(String condition, String[] params) {
        try {
            Thread.sleep(500);
        } catch (InterruptedException e) {
            throw new RuntimeException(e);
        }
        if (DaliangangContext.isDepartmentUser()) {
            EruptCacheRedis eruptCacheRedis = EruptSpringUtil.getBean(EruptCacheRedis.class);
            String orgCode = eruptCacheRedis.getStringRedisTemplate().opsForValue().get(UnloadShip.ATTR_UNLOAD_SHIP);
//            Object obj = MetaContext.getAttribute(ATTR_UNLOAD_SHIP);
            if (orgCode != null) {
                return "org_code='" + orgCode + "'";
            }
        }else{
            String orgCode = null;
            EruptUserService eruptUserService = EruptSpringUtil.getBean(EruptUserService.class);
            if (MetaContext.getUser() != null) {
                EruptUser eruptUser = eruptUserService.getEruptUser(Long.parseLong(MetaContext.getUser().getUid()));
                orgCode = eruptUser != null && eruptUser.getEruptOrg() != null ? eruptUser.getEruptOrg().getCode() : null;
            }
            //WorkAddressType type = WorkAddressType.valueOf(params[0]);
            return "org_code='" + orgCode + "'";
        }
        return null ;

    }

    @Override
    public boolean handler(boolean expr, String[] params) {

        return !DaliangangContext.isDepartmentUser();
    }
}
