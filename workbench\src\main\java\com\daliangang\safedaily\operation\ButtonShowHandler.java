package com.daliangang.safedaily.operation;

import com.daliangang.core.DaliangangContext;
import xyz.erupt.annotation.expr.ExprBool;

/**
 * @Title: ButtonShowHandler
 * <AUTHOR>
 * @Package com.daliangang.safedaily.operation
 * @Date 2024/3/7 17:02
 * @description: 上报按钮是否显示
 */
public class ButtonShowHandler implements ExprBool.ExprHandler{
    @Override
    public boolean handler(boolean expr, String[] params) {
        /*判断当前用户是否为政府用户*/
        return !DaliangangContext.isDepartmentUser();
    }
}
