package com.daliangang.workbench.service;

import cn.hutool.core.collection.CollectionUtil;
import com.daliangang.core.DaliangangContext;
import com.daliangang.training.models.PostSyncEntity;
import com.daliangang.workbench.entity.EmployeeInformation;
import com.daliangang.workbench.entity.EruptRoleTemplatePost;
import lombok.Builder;
import lombok.Data;
import org.springframework.stereotype.Service;
import xyz.erupt.annotation.fun.DataProxy;
import xyz.erupt.jpa.dao.EruptDao;
import xyz.erupt.toolkit.redismq.RedisMQConst;
import xyz.erupt.toolkit.redismq.RedisMQService;
import xyz.erupt.toolkit.service.EruptCacheRedis;
import xyz.erupt.toolkit.utils.EruptDaoUtils;
import xyz.erupt.toolkit.utils.NotifyUtils;
import xyz.erupt.toolkit.utils.SqlUtils;
import xyz.erupt.upms.model.EruptUser;
import xyz.erupt.upms.service.EruptUserService;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * @Author: zengwensi
 * @Description:
 * @Date: Created in 2023/3/31 10:16
 * @Modified By
 */
@Service
public class EruptRoleTemplatePostService implements DataProxy<EruptRoleTemplatePost> {

    @Resource
    EruptDao eruptDao;

    @Resource
    EruptUserService eruptUserService;

    @Resource
    private RedisMQService redisMQService;

//    private Class<? extends DataProxy> prepareHandler;

//    public void registerPrepareHandler(Class<? extends DataProxy> handler) {
//        this.prepareHandler = handler;
//    }

    @Override
    public void beforeAdd(EruptRoleTemplatePost eruptRoleTemplatePost) {
//        if (prepareHandler != null) EruptSpringUtil.getBean(prepareHandler).beforeAdd(eruptRoleTemplatePost);
    }

    @Override
    public void afterAdd(EruptRoleTemplatePost eruptRoleTemplatePost) {
//        if (prepareHandler != null) EruptSpringUtil.getBean(prepareHandler).afterAdd(eruptRoleTemplatePost);
        PostSyncEntity postSync = PostSyncEntity.builder()
                .code(eruptRoleTemplatePost.getCode())
                .orgCode(eruptRoleTemplatePost.getOrgCode())
                .id(eruptRoleTemplatePost.getId())
                .name(eruptRoleTemplatePost.getName())
                .weight(eruptRoleTemplatePost.getWeight())
                .reserved(eruptRoleTemplatePost.getReserved())
                .delFlag(false)
                .updateFlag(false)
                .type(eruptRoleTemplatePost.getType())
                .build();
        redisMQService.produce(RedisMQConst.DEFAULT_TOPIC + "_POST", postSync);

    }

//    @Override
//    public void beforeUpdate(EruptRoleTemplatePost eruptRoleTemplatePost) {
//        if (prepareHandler != null) EruptSpringUtil.getBean(prepareHandler).beforeUpdate(eruptRoleTemplatePost);
//    }

    @Override
    public void afterUpdate(EruptRoleTemplatePost eruptRoleTemplatePost) {
//        if (prepareHandler != null) EruptSpringUtil.getBean(prepareHandler).afterUpdate(eruptRoleTemplatePost);

        PostSyncEntity postSync = PostSyncEntity.builder()
                .code(eruptRoleTemplatePost.getCode())
                .orgCode(eruptRoleTemplatePost.getOrgCode())
                .id(eruptRoleTemplatePost.getId())
                .name(eruptRoleTemplatePost.getName())
                .weight(eruptRoleTemplatePost.getWeight())
                .reserved(eruptRoleTemplatePost.getReserved())
                .delFlag(false)
                .updateFlag(true)
                .type(eruptRoleTemplatePost.getType())
                .build();
        redisMQService.produce(RedisMQConst.DEFAULT_TOPIC + "_POST", postSync);
    }


    @Override
    public void beforeDelete(EruptRoleTemplatePost eruptRoleTemplatePost) {
        String selectSql = "select * from tb_employee_information where job_title like CONCAT('%','" + eruptRoleTemplatePost.getName() + "','%')";
        List<EmployeeInformation> employeeInformationList = EruptDaoUtils.selectOnes(selectSql, EmployeeInformation.class);
        if (CollectionUtil.isNotEmpty(employeeInformationList)) {
            NotifyUtils.showErrorDialog("当前岗位下有员工数据,不能删除!");
        }
    }

    @Override
    public void afterDelete(EruptRoleTemplatePost eruptRoleTemplatePost) {
//        if (prepareHandler != null) EruptSpringUtil.getBean(prepareHandler).afterDelete(eruptRoleTemplatePost);
        PostSyncEntity postSync = PostSyncEntity.builder()
                .code(eruptRoleTemplatePost.getCode())
                .orgCode(eruptRoleTemplatePost.getOrgCode())
                .id(eruptRoleTemplatePost.getId())
                .name(eruptRoleTemplatePost.getName())
                .delFlag(true)
                .updateFlag(true)
                .type(eruptRoleTemplatePost.getType())
                .build();
        redisMQService.produce(RedisMQConst.DEFAULT_TOPIC + "_POST", postSync);
    }

    @Resource
    private EruptCacheRedis eruptCacheRedis;

    @Data
    @Builder
    public static class EruptRoleTemplatePostQueryVo {
        List<EruptRoleTemplatePost> list;
    }

    public List<EruptRoleTemplatePost> queryPost(String exclusive) {
        EruptUser currentEruptUser = eruptUserService.getCurrentEruptUser();
        String whereSql = "select * from e_upms_post_template where 1=1 ";//" 1=1 ";
        if (!currentEruptUser.getIsAdmin()) {
            //if (StringUtils.isNotEmpty(exclusive)) whereSql += " and exclusive=" + exclusive;
            if (currentEruptUser.getEruptOrg() == null) return new ArrayList<>();
            //String orgId = currentEruptUser.getEruptOrg().getCode();//获取当前登录人的公司code
            //政府：不显示初始化7个岗位，只展示自己创建的
            //企业：显示7个初始化岗位+自己创建的
            String sql = "  org_code =" + SqlUtils.wrapStr(currentEruptUser.getEruptOrg().getCode());
            if (DaliangangContext.isDepartmentUser()) {
                whereSql += " and reserved = 0  ";
                whereSql += " and " + sql;
            } else {
//                whereSql += " and (reserved = 1  or " + sql + ")";
                whereSql += " and " + sql;

            }


        }
        //eruptDao.getEntityManager().clear();
        //return eruptDao.queryEntityList(EruptRoleTemplatePost.class,whereSql);
        return EruptDaoUtils.selectOnes(whereSql, EruptRoleTemplatePost.class);
    }

    public List<EruptRoleTemplatePost> queryExclusivePostCount(String[] postCode) {
        //获取当前登录人的公司code
        EruptUser currentEruptUser = eruptUserService.getCurrentEruptUser();
        String whereSql = "select * from e_upms_post_template";
        whereSql += " where exclusive=" + Boolean.TRUE;
        if (currentEruptUser.getEruptOrg() != null) {
            //Long orgId = currentEruptUser.getEruptOrg().getId();
            whereSql += " and org_code='" + currentEruptUser.getEruptOrg().getCode() + "'";
        }
        whereSql += " and code" + SqlUtils.wrapIn(postCode);
        return EruptDaoUtils.selectOnes(whereSql, EruptRoleTemplatePost.class);

    }
}
