package com.daliangang.emergency.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import xyz.erupt.annotation.Erupt;
import xyz.erupt.annotation.EruptField;
import xyz.erupt.annotation.sub_erupt.Power;
import xyz.erupt.annotation.sub_field.Edit;
import xyz.erupt.annotation.sub_field.EditType;
import xyz.erupt.annotation.sub_field.View;
import xyz.erupt.annotation.sub_field.sub_edit.AttachmentType;
import xyz.erupt.toolkit.db.Comment;
import xyz.erupt.upms.model.auth.DataAuthModel;

import javax.persistence.Entity;
import javax.persistence.Table;

/**
 * <AUTHOR>
 * @since :2023/4/26:11:29
 */
@Erupt(name = "应急流程图", power = @Power(add = true, delete = true, export = false, importable = true, viewDetails = true, edit = true)
        , rowOperation = {})
@Table(name = "tb_emergency_flowChart")
@Entity
@Getter
@Setter
@Comment("应急流程图")
@ApiModel("应急流程图")
public class EmergencyFlowChart extends DataAuthModel {
    @EruptField(
            views = @View(title = "附件"),
            edit = @Edit(title = "附件", type = EditType.ATTACHMENT, //notNull = true,
                    attachmentType = @AttachmentType(type = AttachmentType.Type.IMAGE, maxLimit = 100, fileTypes = {".jpg", ".bmp", ".png"})))
    @Comment("附件")
    @ApiModelProperty("附件")
    private String file;
}
