package com.daliangang.rndpub.operation;

import com.daliangang.rndpub.entity.InspectionResults;
import org.springframework.stereotype.Service;
import xyz.erupt.annotation.fun.OperationHandler;
import xyz.erupt.jpa.dao.EruptDao;

import javax.annotation.Resource;
import java.util.List;

@Service
public class InspectionResultPublishHandler implements OperationHandler<InspectionResults, Void> {
    @Resource
    private EruptDao eruptDao;

    @Override
    public String exec(List<InspectionResults> data, Void unused, String[] param) {
        for(InspectionResults inspectionResults : data){
            Long id = inspectionResults.getId();
            String updateSql = "update tb_inspection_results set publish_status = 1 where publish_status = 0 and id = "+id;
            eruptDao.getJdbcTemplate().update(updateSql);
        }

        return null ;
    }
}
