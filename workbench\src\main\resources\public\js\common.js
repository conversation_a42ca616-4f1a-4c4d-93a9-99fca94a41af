
var common={
	
	data(){
		return {
url:'http://192.168.1.72:9098/',
 token :JSON.parse(window.localStorage._token).token,
header:{
    'Content-Type': 'application/json;charset=UTF-8', // 配置请求头
    token: JSON.parse(window.localStorage._token).token
  }
		}
	},
	methods:{
		// 获取当前登录政府  企业
		getAccount(){
			let child=this.list.filter(item=>item.code=='RiskDatabaseDepartment')
			this.$http({
				url:`${window.parent.eruptSiteConfig.domain}/erupt-api/get/isDepartmentUser`,
				methods:'GET',
			headers:this.header}).then(
				res => {
					if(res.data.data){
						child[0].url='/#/build/table/RiskDatabaseDepartment'
						child[0].code='RiskDatabaseDepartment'
					}else{
						child[0].url='/#/build/table/RiskDatabaseEnterprise'
						child[0].code='RiskDatabaseEnterprise'

					}
				})
		},
		goTo(item){
			this.$http({
				url:`${window.parent.eruptSiteConfig.domain}/erupt-api/erupt-permission/checkEruptPermission?menuValue=${item.code}`,
				methods:'GET',
			headers:this.header
		}).then(
				res => {
					if(res.data){
			parent.window.open(item.url,item.blank?'_blank':'_self')
					}else{
					this.$alert('暂无权限', '提示',);
					}
				})
		},

	},
	create(){
	}
}