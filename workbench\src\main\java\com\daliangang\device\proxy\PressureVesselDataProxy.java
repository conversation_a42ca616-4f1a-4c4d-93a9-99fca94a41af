package com.daliangang.device.proxy;

import com.daliangang.device.entity.PressureVessel;
import com.daliangang.device.entity.WharfStructureInspection;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import xyz.erupt.annotation.fun.DataProxy;
import xyz.erupt.annotation.query.Condition;
import xyz.erupt.toolkit.colorfont.ColorStateTimeFontDataProxy;

import javax.annotation.Resource;
import java.util.List;
@Service
@Slf4j
public class PressureVesselDataProxy implements DataProxy<PressureVessel> {

    @Resource
    private ColorStateTimeFontDataProxy colorStateTimeFontDataProxy ;
    @Override
    public void beforeAdd(PressureVessel pressureVessel) {
//        if (pressureVessel.getDetectionRange().contains("其他")) {
//            if(pressureVessel.getDescription()==null){
//                NotifyUtils.showErrorMsg("说明不能为空!");
//            }
//            if(pressureVessel.getDescription().length()>15){
//                NotifyUtils.showErrorMsg("说明应在15字以内!");
//            }
//        }
        pressureVessel.setCheckStatus(pressureVessel.getState());
    }

    @Override
    public void beforeUpdate(PressureVessel pressureVessel) {
//        if (pressureVessel.getDetectionRange().contains("其他")) {
//            if(pressureVessel.getDescription()==null){
//                NotifyUtils.showErrorMsg("说明不能为空!");
//            }
//            if(pressureVessel.getDescription().length()>15){
//                NotifyUtils.showErrorMsg("说明应在15字以内!");
//            }
//        }
    }

    @Override
    public String beforeFetch(List<Condition> conditions) {
        String sql = " 1= 1 " ;
        sql = sql + colorStateTimeFontDataProxy.searchByState(WharfStructureInspection.class,"checkStatus" ,conditions);
        return sql;
    }
}
