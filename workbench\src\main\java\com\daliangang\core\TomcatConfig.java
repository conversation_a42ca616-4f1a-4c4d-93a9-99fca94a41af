package com.daliangang.core;

//import org.apache.catalina.Context;
//import org.apache.tomcat.util.descriptor.web.SecurityCollection;
//import org.apache.tomcat.util.descriptor.web.SecurityConstraint;
import org.springframework.boot.web.embedded.tomcat.TomcatServletWebServerFactory;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class TomcatConfig {

//    @Bean
//    public TomcatServletWebServerFactory servletContainer() {
//        TomcatServletWebServerFactory tomcatServletContainerFactory = new TomcatServletWebServerFactory() {
//            @Override
//            protected void postProcessContext(Context context) {
//                SecurityConstraint constraint = new SecurityConstraint();
//                SecurityCollection collection = new SecurityCollection();
//                //http方法
//                collection.addMethod("PUT");
//                collection.addMethod("DELETE");
//                collection.addMethod("HEAD");
//                collection.addMethod("OPTIONS");
//                collection.addMethod("TRACE");
//                //url匹配表达式
//                collection.addPattern("/*");
//                constraint.addCollection(collection);
//                constraint.setAuthConstraint(true);
//                context.addConstraint(constraint);
//
//                //设置使用httpOnly
//                context.setUseHttpOnly(true);
//            }
//        };
//        return tomcatServletContainerFactory;
//    }

}
