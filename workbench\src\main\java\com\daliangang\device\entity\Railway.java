/*
 * Copyright © 2020-2035 yundingyun All rights reserved.
 */
package com.daliangang.device.entity;

import com.daliangang.device.proxy.RailwayDataProxy;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import xyz.erupt.annotation.Erupt;
import xyz.erupt.annotation.EruptField;
import xyz.erupt.annotation.sub_erupt.Power;
import xyz.erupt.annotation.sub_field.Edit;
import xyz.erupt.annotation.sub_field.EditType;
import xyz.erupt.annotation.sub_field.View;
import xyz.erupt.annotation.sub_field.sub_edit.ChoiceType;
import xyz.erupt.annotation.sub_field.sub_edit.DateType;
import xyz.erupt.annotation.sub_field.sub_edit.InputType;
import xyz.erupt.annotation.sub_field.sub_edit.Search;
import xyz.erupt.jpa.model.MetaModel;
import xyz.erupt.toolkit.db.Comment;
import xyz.erupt.upms.dict.Dict;
import xyz.erupt.upms.dict.DictItem;
import xyz.erupt.upms.dict.Dicts;
import xyz.erupt.upms.handler.DictChoiceFetchHandler;

import javax.persistence.Entity;
import javax.persistence.Lob;
import javax.persistence.Table;

@Erupt(name = "港区铁路管理", power = @Power(add = true, delete = true, export = false, importable = true, viewDetails = true, edit = true)
        , dataProxy = RailwayDataProxy.class
        , rowOperation = {})
@Table(name = "tb_railway")
@Entity
@Getter
@Setter
@Comment("港区铁路管理")
@ApiModel("港区铁路管理")
@Dicts(
        @Dict(code = "railwayLevel", name = "铁路等级", value = {
                //铁路等级字典项：Ⅰ级、Ⅱ级、Ⅲ级、IV级
                @DictItem(code = "RAILWAY_LV_I", value = "Ⅰ级"),
                @DictItem(code = "RAILWAY_LV_II", value = "ⅠI级"),
                @DictItem(code = "RAILWAY_LV_III", value = "ⅠII级"),
                @DictItem(code = "RAILWAY_LV_IV", value = "ⅠV级")
        })
)
public class Railway extends MetaModel {
    @EruptField(
            views = @View(title = "铁路名称"),
            edit = @Edit(title = "铁路名称", type = EditType.INPUT, search = @Search(vague = true), notNull = true,
                    inputType = @InputType))
    @Comment("铁路名称")
    @ApiModelProperty("铁路名称")
    private String railwayName;

    @EruptField(
            views = @View(title = "修建时间"),
            edit = @Edit(title = "修建时间", type = EditType.DATE, notNull = true,
                    dateType = @DateType))
    @Comment("修建时间")
    @ApiModelProperty("修建时间")
    private java.util.Date constructionTime;

    @EruptField(
            views = @View(title = "投产时间"),
            edit = @Edit(title = "投产时间", type = EditType.DATE, notNull = true,
                    dateType = @DateType))
    @Comment("投产时间")
    @ApiModelProperty("投产时间")
    private java.util.Date productionTime;

    @EruptField(
            views = @View(title = "资产归属单位"),
            edit = @Edit(title = "资产归属单位", type = EditType.INPUT, notNull = true,
                    inputType = @InputType))
    @Comment("资产归属单位")
    @ApiModelProperty("资产归属单位")
    private String assetOwnership;

    @EruptField(
            views = @View(title = "资产管理单位"),
            edit = @Edit(title = "资产管理单位", type = EditType.INPUT, notNull = true,
                    inputType = @InputType))
    @Comment("资产管理单位")
    @ApiModelProperty("资产管理单位")
    private String assetManagement;

    @EruptField(
            views = @View(title = "铁路等级"),
            edit = @Edit(title = "铁路等级", type = EditType.CHOICE, notNull = true,
                    choiceType = @ChoiceType(fetchHandler = DictChoiceFetchHandler.class, fetchHandlerParams = "railwayLevel")))
    @Comment("铁路等级")
    @ApiModelProperty("铁路等级")
    private String railwayGrade;

    @EruptField(
            views = @View(title = "功能类型"),
            edit = @Edit(title = "功能类型", type = EditType.INPUT, notNull = true,
                    inputType = @InputType))
    @Comment("功能类型")
    @ApiModelProperty("功能类型")
    private String functionType;

    @EruptField(
            views = @View(title = "轨道类型"),
            edit = @Edit(title = "轨道类型", type = EditType.INPUT, notNull = true,
                    inputType = @InputType))
    @Comment("轨道类型")
    @ApiModelProperty("轨道类型")
    private String trackType;

//	@EruptField(
//		views = @View(title = "经纬度", show = false),
//		edit = @Edit(title = "经纬度", type = EditType.INPUT, show = false, notNull = true,
//		inputType = @InputType))
//	@Comment("经纬度")
//	@ApiModelProperty("经纬度")
//	private String longitudeAndLatitude;
//
//	@EruptField(
//		views = @View(title = "经度", show = false),
//		edit = @Edit(title = "经度", type = EditType.INPUT, show = false,
//		inputType = @InputType))
//	@Comment("经度")
//	@ApiModelProperty("经度")
//	private String longitude;
//
//	@EruptField(
//		views = @View(title = "纬度", show = false),
//		edit = @Edit(title = "纬度", type = EditType.INPUT, show = false,
//		inputType = @InputType))
//	@Comment("纬度")
//	@ApiModelProperty("纬度")
//	private String latitude;

    @EruptField(
            views = @View(title = "地图", show = false),
            edit = @Edit(title = "地图", type = EditType.MAP, show = true))
    @Comment("地图")
    @ApiModelProperty("地图")
    @Lob
    private String map;

}
