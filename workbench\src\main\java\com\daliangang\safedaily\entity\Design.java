/*
 * Copyright © 2020-2035 yundingyun All rights reserved.
 */
package com.daliangang.safedaily.entity;

import com.daliangang.core.CompanyRenderHandler;
import com.daliangang.safedaily.proxy.DesignDataProxy;
import com.daliangang.workbench.handler.EmployeeExternalRenderHandler;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import xyz.erupt.annotation.Erupt;
import xyz.erupt.annotation.EruptField;
import xyz.erupt.annotation.expr.ExprBool;
import xyz.erupt.annotation.sub_erupt.Power;
import xyz.erupt.annotation.sub_erupt.RowOperation;
import xyz.erupt.annotation.sub_field.Edit;
import xyz.erupt.annotation.sub_field.EditType;
import xyz.erupt.annotation.sub_field.Readonly;
import xyz.erupt.annotation.sub_field.View;
import xyz.erupt.annotation.sub_field.sub_edit.*;
import xyz.erupt.toolkit.attachment.AttachmentDownloadHandler;
import xyz.erupt.toolkit.db.Comment;
import xyz.erupt.toolkit.handler.DelOperationHandler;
import xyz.erupt.toolkit.handler.EditOperationHandler;
import xyz.erupt.toolkit.handler.RemoteEntityChoiceFetchHandler;
import xyz.erupt.toolkit.utils.TplUtils;
import xyz.erupt.upms.model.auth.DataAuthHandler;
import xyz.erupt.upms.model.auth.DataAuthModel;

import javax.persistence.Entity;
import javax.persistence.Lob;
import javax.persistence.Table;
import javax.persistence.Transient;

@Erupt(name = "设施设计审查", power = @Power(add = true, delete = false, export = false, importable = true, viewDetails = true, edit = false)
        , dataProxy = DesignDataProxy.class
        , rowOperation = {
        @RowOperation(title = "编辑", icon = "fa fa-edit", code = TplUtils.EDIT_OPER_CODE, eruptClass = Design.class,show = @ExprBool( exprHandler = EmployeeExternalRenderHandler.class),
                operationHandler = EditOperationHandler.class,  ifExprBehavior = RowOperation.IfExprBehavior.HIDE, mode = RowOperation.Mode.SINGLE),
        @RowOperation(title = "删除", icon = "fa fa-trash-o",show = @ExprBool( exprHandler = EmployeeExternalRenderHandler.class), operationHandler = DelOperationHandler.class,  ifExprBehavior = RowOperation.IfExprBehavior.HIDE, mode = RowOperation.Mode.SINGLE),
//        @RowOperation(title = "上报", icon = "fa fa-arrow-circle-o-up", operationHandler = DesignEscalationHandler.class, ifExpr = "item.submitted=='未上报'", mode = RowOperation.Mode.SINGLE),
        @RowOperation(title = "下载附件", icon = "fa fa-download", operationHandler = AttachmentDownloadHandler.class, mode = RowOperation.Mode.SINGLE)
})
@Table(name = "tb_design")
@Entity
@Getter
@Setter
@Comment("设施设计审查")
@ApiModel("设施设计审查")
public class Design extends DataAuthModel {
    @EruptField(
            views = @View(title = "企业名称"),
            edit = @Edit(title = "企业名称",search = @Search(ifRender = @ExprBool(exprHandler = CompanyRenderHandler.class) ), readonly = @Readonly(exprHandler = DataAuthHandler.class), type = EditType.CHOICE, notNull = true,
                    choiceType = @ChoiceType(fullSpan = true,fetchHandler = RemoteEntityChoiceFetchHandler.class, fetchHandlerParams = {"main", "Enterprise", "org_code,name"})))
    @Comment("企业名称")
    @ApiModelProperty("企业名称")
    private String company;

    @EruptField(
            views = @View(title = "上报状态",show = false),
            edit = @Edit(title = "上报状态", type = EditType.BOOLEAN, show = false, /*notNull = true, */
                    boolType = @BoolType(trueText = "已上报", falseText = "未上报")))
    @Comment("上报状态")
    @ApiModelProperty("上报状态")
    private Boolean submitted;

    @EruptField(
            views = @View(title = "项目名称"),
            edit = @Edit(title = "项目名称", type = EditType.INPUT, notNull = true,search = @Search(vague = true),
                    inputType = @InputType))
    @Comment("项目名称")
    @ApiModelProperty("项目名称")
    private String grade;

    @Lob
    @EruptField(
            views = @View(title = "项目概况"),
            edit = @Edit(title = "项目概况", type = EditType.TEXTAREA, notNull = true,
                    inputType = @InputType))
    @Comment("项目概况")
    @ApiModelProperty("项目概况")
    private String issueDate;

    @EruptField(
            views = @View(title = "上传附件", show = false),
            edit = @Edit(title = "上传附件", type = EditType.DIVIDE))
    @Transient
    @Comment("上传附件")
    @ApiModelProperty("上传附件")
    private String fileDivide;

    @EruptField(
            views = @View(title = "附件上传", show = false),
            edit = @Edit(title = "附件上传", type = EditType.ATTACHMENT, notNull = true,
                    attachmentType = @AttachmentType(type = AttachmentType.Type.BASE,
                            maxLimit = 20,
                            tipMsg = "<t><font color='green'>港口建设项目安全设施设计审查申请书及文件，港口建设项目安全条件审查意见书，设计单位的设计资质证明文件，港口建设项目安全设施设计专篇，港口建设项目批准（或核准、备案）文件和选址意见书（或规划许可证或土地使用证），港口建设项目安全设施设计审查意见书，其他文件、资料</font>",
                            fileTypes = {".pdf", ".doc", ".docx", ".xlsx", ".png", ".jpg", ".xls", ".ppt", ".pptx", ".zip", ".rar"})))
    @Comment("附件上传")
    @ApiModelProperty("附件上传")
    @Lob
    private String fileUpload;

//	@EruptField(
//		views = @View(title = "港口建设项目安全设施设计审查申请书及文件", show = false),
//		edit = @Edit(title = "港口建设项目安全设施设计审查申请书及文件", type = EditType.ATTACHMENT, notNull = true,
//		attachmentType = @AttachmentType(type = AttachmentType.Type.BASE, maxLimit = 20, fileTypes = {".pdf", ".doc", ".docx", ".xlsx", ".png", ".jpg", ".xls", ".ppt", ".pptx", ".zip", ".rar"})))
//	@Comment("港口建设项目安全设施设计审查申请书及文件")
//	@ApiModelProperty("港口建设项目安全设施设计审查申请书及文件")
//	private String design;
//
//	@EruptField(
//		views = @View(title = "港口建设项目安全条件审查意见书", show = false),
//		edit = @Edit(title = "港口建设项目安全条件审查意见书", type = EditType.ATTACHMENT, notNull = true,
//		attachmentType = @AttachmentType(type = AttachmentType.Type.BASE, maxLimit = 20, fileTypes = {".pdf", ".doc", ".docx", ".xlsx", ".png", ".jpg", ".xls", ".ppt", ".pptx", ".zip", ".rar"})))
//	@Comment("港口建设项目安全条件审查意见书")
//	@ApiModelProperty("港口建设项目安全条件审查意见书")
//	private String review;
//
//	@EruptField(
//		views = @View(title = "设计单位的设计资质证明文件", show = false),
//		edit = @Edit(title = "设计单位的设计资质证明文件", type = EditType.ATTACHMENT, notNull = true,
//		attachmentType = @AttachmentType(type = AttachmentType.Type.BASE, maxLimit = 20, fileTypes = {".pdf", ".doc", ".docx", ".xlsx", ".png", ".jpg", ".xls", ".ppt", ".pptx", ".zip", ".rar"})))
//	@Comment("设计单位的设计资质证明文件")
//	@ApiModelProperty("设计单位的设计资质证明文件")
//	private String qualification;
//
//	@EruptField(
//		views = @View(title = "港口建设项目安全设施设计专篇", show = false),
//		edit = @Edit(title = "港口建设项目安全设施设计专篇", type = EditType.ATTACHMENT, notNull = true,
//		attachmentType = @AttachmentType(type = AttachmentType.Type.BASE, maxLimit = 20, fileTypes = {".pdf", ".doc", ".docx", ".xlsx", ".png", ".jpg", ".xls", ".ppt", ".pptx", ".zip", ".rar"})))
//	@Comment("港口建设项目安全设施设计专篇")
//	@ApiModelProperty("港口建设项目安全设施设计专篇")
//	private String facility;
//
//	@EruptField(
//		views = @View(title = "港口建设项目批准（或核准、备案）文件和选址意见书（或规划许可证或土地使用证）", show = false),
//		edit = @Edit(title = "港口建设项目批准（或核准、备案）文件和选址意见书（或规划许可证或土地使用证）", type = EditType.ATTACHMENT, notNull = true,
//		attachmentType = @AttachmentType(type = AttachmentType.Type.BASE, maxLimit = 20, fileTypes = {".pdf", ".doc", ".docx", ".xlsx", ".png", ".jpg", ".xls", ".ppt", ".pptx", ".zip", ".rar"})))
//	@Comment("港口建设项目批准（或核准、备案）文件和选址意见书（或规划许可证或土地使用证）")
//	@ApiModelProperty("港口建设项目批准（或核准、备案）文件和选址意见书（或规划许可证或土地使用证）")
//	private String approve;
//
//	@EruptField(
//		views = @View(title = "港口建设项目安全设施设计审查意见书", show = false),
//		edit = @Edit(title = "港口建设项目安全设施设计审查意见书", type = EditType.ATTACHMENT, notNull = true,
//		attachmentType = @AttachmentType(type = AttachmentType.Type.BASE, maxLimit = 20, fileTypes = {".pdf", ".doc", ".docx", ".xlsx", ".png", ".jpg", ".xls", ".ppt", ".pptx", ".zip", ".rar"})))
//	@Comment("港口建设项目安全设施设计审查意见书")
//	@ApiModelProperty("港口建设项目安全设施设计审查意见书")
//	private String opinion;
//
//	@EruptField(
//		views = @View(title = "其他文件、资料", show = false),
//		edit = @Edit(title = "其他文件、资料", type = EditType.ATTACHMENT,
//		attachmentType = @AttachmentType(type = AttachmentType.Type.BASE, maxLimit = 20, fileTypes = {".pdf", ".doc", ".docx", ".xlsx", ".png", ".jpg", ".xls", ".ppt", ".pptx", ".zip", ".rar"})))
//	@Comment("其他文件、资料")
//	@ApiModelProperty("其他文件、资料")
//	private String otherFile;

    @EruptField(
            views = @View(title = "是否上报", show = false),
            edit = @Edit(title = "是否上报", type = EditType.BOOLEAN, show = false,
                    boolType = @BoolType(trueText = "是", falseText = "否")))
    @Comment("是否上报")
    @ApiModelProperty("是否上报")
    private Boolean isReport;


}
