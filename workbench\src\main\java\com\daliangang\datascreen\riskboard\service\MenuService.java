package com.daliangang.datascreen.riskboard.service;

import org.springframework.stereotype.Service;
import xyz.erupt.jpa.dao.EruptDao;
import xyz.erupt.toolkit.utils.SqlUtils;
import xyz.erupt.upms.model.EruptMenu;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * @Author: chongmenglin
 * @Date: 2024/11/8 9:24
 */
@Service
public class MenuService {
    @Resource
    private EruptDao eruptDao;

    public List<EruptMenu> getSubMenus(String code) {
        // 创建一个结果列表
        List<EruptMenu> result = new ArrayList<>();
        // 查询直接子菜单
        EruptMenu eruptMenu = eruptDao.queryEntity(EruptMenu.class, "code = " + SqlUtils.wrapStr(code));
        List<EruptMenu> subMenus = eruptDao.queryEntityList(EruptMenu.class, "parent_menu_id = '" + eruptMenu.getId() + "' and (type != 'button' or type is null)");
        for (EruptMenu menu : subMenus) {
            result.add(menu); // 将当前子菜单加入结果
            result.addAll(getSubMenus(menu.getCode())); // 递归获取下一级子菜单
        }
        return result;
    }

    public List<String> getSubMenusCode(String code) {
        List<EruptMenu> subMenus = getSubMenus(code);
        List<String> result = new ArrayList<>();
        for (EruptMenu menu : subMenus) {
            if(menu.getValue() != null)
            result.add(menu.getValue());
        }
        return result;
    }
    public List<String> getSubMenusCode(List<EruptMenu> subMenus) {
        List<String> result = new ArrayList<>();
        for (EruptMenu menu : subMenus) {
            if(menu.getValue() != null)
                result.add(menu.getValue());
        }
        return result;
    }

    public List<String> getSubMenusName(List<EruptMenu> subMenus) {
        List<String> result = new ArrayList<>();
        for (EruptMenu menu : subMenus) {
            if(menu.getName() != null)
                result.add(menu.getName());
        }
        return result;
    }

    public String getSubMenusName(List<EruptMenu> subMenus,String className) {
        EruptMenu eruptMenu = subMenus.stream().filter(menu -> menu.getValue().equals(className)).findFirst().get();
        return eruptMenu.getName();
    }
}
