package com.daliangang.device.controller;

import com.daliangang.core.DaliangangContext;
import com.daliangang.device.entity.Yard;
import com.google.gson.internal.LinkedTreeMap;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import xyz.erupt.core.annotation.EruptRouter;
import xyz.erupt.core.module.MetaUserinfo;
import xyz.erupt.core.view.EruptApiModel;
import xyz.erupt.jpa.dao.EruptDao;
import xyz.erupt.toolkit.remote.RemoteProxyService;
import xyz.erupt.toolkit.utils.EruptDaoUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @since :2023/3/30:13:20
 */
@RestController
public class YardController {

    @Resource
   private EruptDao eruptDao;
    @Resource
   private RemoteProxyService remoteProxyService;

    /**
     * 获取企业附征货种名称
     * @param
     * @return
     */
    @RequestMapping("erupt-api/get/yardName")
    @EruptRouter(verifyType = EruptRouter.VerifyType.LOGIN)
    public EruptApiModel getGoodsType() {
        MetaUserinfo remoteUserInfo = remoteProxyService.getRemoteUserInfo();
        if(DaliangangContext.isDepartmentUser()){
            List<Yard> yards = EruptDaoUtils.selectOnes("select * from tb_yard", Yard.class);
            if (ObjectUtils.isNotEmpty(yards)) {
                List<LinkedTreeMap> list1 = new ArrayList<>();
                yards.forEach(v->{
                    LinkedTreeMap map = new LinkedTreeMap();
                    map.put("code",v.getId());
                    map.put("name",v.getName());
                    list1.add(map);
                });
                return EruptApiModel.successApi(list1);
            }
        }
        List<Yard> yards = EruptDaoUtils.selectOnes("select * from tb_yard where org_code='" + remoteUserInfo.getOrg()+"'", Yard.class);
        if (ObjectUtils.isNotEmpty(yards)) {
            List<LinkedTreeMap> list1 = new ArrayList<>();
            yards.forEach(v->{
                LinkedTreeMap map = new LinkedTreeMap();
                map.put("code",v.getId());
                map.put("name",v.getName());
                list1.add(map);
            });

            return EruptApiModel.successApi(list1);
        }

        return EruptApiModel.successApi();
    }
}
