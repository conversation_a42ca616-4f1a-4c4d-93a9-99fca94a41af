version: '3.8'

services:
  # MySQL数据库服务
  mysql:
    image: mysql:8.0
    container_name: daliangang-mysql
    restart: unless-stopped
    environment:
      MYSQL_ROOT_PASSWORD: ${MYSQL_ROOT_PASSWORD}
      MYSQL_DATABASE: ${MYSQL_DATABASE}
      MYSQL_USER: ${MYSQL_USER}
      MYSQL_PASSWORD: ${MYSQL_PASSWORD}
      TZ: Asia/Shanghai
    ports:
      - "${MYSQL_PORT}:3306"
    volumes:
      - mysql_data:/var/lib/mysql
      - ./docker/mysql/conf.d:/etc/mysql/conf.d
      - ./docker/mysql/init:/docker-entrypoint-initdb.d
    command: --default-authentication-plugin=mysql_native_password
    networks:
      - daliangang-network
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost", "-u", "root", "-p${MYSQL_ROOT_PASSWORD}"]
      timeout: 20s
      retries: 10

  # Redis缓存服务
  redis:
    image: redis:7-alpine
    container_name: daliangang-redis
    restart: unless-stopped
    environment:
      TZ: Asia/Shanghai
    ports:
      - "${REDIS_PORT}:6379"
    volumes:
      - redis_data:/data
      - ./docker/redis/redis.conf:/usr/local/etc/redis/redis.conf
    command: redis-server /usr/local/etc/redis/redis.conf --requirepass ${REDIS_PASSWORD}
    networks:
      - daliangang-network
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "incr", "ping"]
      timeout: 3s
      retries: 5

  # MinIO对象存储服务
  minio:
    image: minio/minio:latest
    container_name: daliangang-minio
    restart: unless-stopped
    environment:
      MINIO_ROOT_USER: ${MINIO_ACCESS_KEY}
      MINIO_ROOT_PASSWORD: ${MINIO_SECRET_KEY}
      TZ: Asia/Shanghai
    ports:
      - "${MINIO_PORT}:9000"
      - "${MINIO_CONSOLE_PORT}:9001"
    volumes:
      - minio_data:/data
    command: server /data --console-address ":9001"
    networks:
      - daliangang-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:9000/minio/health/live"]
      timeout: 3s
      retries: 5

  # 大连港工作台应用
  daliangang-app:
    image: ${APP_IMAGE:-daliangang/workbench:latest}
    build:
      context: ./workbench
      dockerfile: Dockerfile
    container_name: daliangang-workbench
    restart: unless-stopped
    environment:
      # 服务器配置
      SERVER_PORT: ${SERVER_PORT:-8080}
      
      # 数据库配置
      MYSQL_HOST: mysql
      MYSQL_PORT: 3306
      MYSQL_DB: ${MYSQL_DATABASE}
      MYSQL_USER: ${MYSQL_USER}
      MYSQL_PWD: ${MYSQL_PASSWORD}
      
      # Redis配置
      REDIS_HOST: redis
      REDIS_PORT: 6379
      REDIS_PWD: ${REDIS_PASSWORD}
      REDIS_DATABASE: ${REDIS_DATABASE}
      
      # Spring Security配置
      SPRING_SECURITY_USER_NAME: ${SPRING_SECURITY_USER_NAME}
      SPRING_SECURITY_USER_PASSWORD: ${SPRING_SECURITY_USER_PASSWORD}
      
      # MinIO配置
      MINIO_ENDPOINT: http://minio:9000
      MINIO_ACCESS_KEY: ${MINIO_ACCESS_KEY}
      MINIO_SECRET_KEY: ${MINIO_SECRET_KEY}
      MINIO_BUCKET_NAME: ${MINIO_BUCKET_NAME}
      
      # Erupt配置
      ERUPT_DOMAIN: ${ERUPT_DOMAIN}
      ERUPT_FILE_DOMAIN: ${ERUPT_FILE_DOMAIN}
      ERUPT_TITLE: ${ERUPT_TITLE}
      ERUPT_DESC: ${ERUPT_DESC}
      ERUPT_AMAP_KEY: ${ERUPT_AMAP_KEY}
      
      # 其他配置
      TZ: Asia/Shanghai
      JAVA_OPTS: "-Xms512m -Xmx1024m -Dfile.encoding=UTF-8"
      
    ports:
      - "${APP_PORT}:${SERVER_PORT:-8080}"
    volumes:
      - app_logs:/app/logs
      - app_uploads:/app/uploads
    depends_on:
      mysql:
        condition: service_healthy
      redis:
        condition: service_healthy
      minio:
        condition: service_healthy
    networks:
      - daliangang-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/actuator/health"]
      timeout: 10s
      retries: 5
      start_period: 60s

# 网络配置
networks:
  daliangang-network:
    driver: bridge

# 数据卷配置
volumes:
  mysql_data:
    driver: local
  redis_data:
    driver: local
  minio_data:
    driver: local
  app_logs:
    driver: local
  app_uploads:
    driver: local
