package com.daliangang.statistics.handler;

import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.stereotype.Component;
import xyz.erupt.bi.fun.EruptBiHandler;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * 问题个数处理类
 * @Author: z<PERSON><PERSON><PERSON>
 * @Description:
 * @Date: Created in 2023/5/9 10:07
 * @Modified By
 */
@Component
public class RndpubInspectTotalCountHandler  implements EruptBiHandler {

    @Resource
    private CommonHandler commonHandler;
    @Override
    public String exprHandler(String param, Map<String, Object> condition, String expr) {
        expr = commonHandler.exprHandlerSearchDate(param,condition,expr);

//        expr = commonHandler.exprHandlerAuthority(param,condition,expr);
        expr = commonHandler.exprHandlerAuthorityCheckObject(param,condition,expr);

        return expr ;
    }

    @Override
    public void resultHandler(String param, Map<String, Object> condition, List<Map<String, Object>> result) {

    }

    @Override
    public void exportHandler(String param, Map<String, Object> condition, Workbook workbook) {

    }
}
