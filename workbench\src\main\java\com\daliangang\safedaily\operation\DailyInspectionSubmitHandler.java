package com.daliangang.safedaily.operation;

import cn.hutool.json.JSONObject;
import com.daliangang.safedaily.entity.DailyInspection;
import org.springframework.stereotype.Service;
import xyz.erupt.annotation.fun.OperationHandler;
import xyz.erupt.common.http.EruptRemoteUtils;
import xyz.erupt.jpa.dao.EruptDao;
import xyz.erupt.toolkit.remote.RemoteProxyService;

import javax.annotation.Resource;
import javax.transaction.Transactional;
import java.util.List;
import java.util.Map;

@Service
public class DailyInspectionSubmitHandler implements OperationHandler<DailyInspection,Void> {

    @Resource
    private RemoteProxyService remoteProxyService;

    @Resource
    private EruptDao eruptDao;

    @Override
    @Transactional
    public String exec(List<DailyInspection> data, Void unused, String[] param) {
        data.get(0).setSubmitted(true);
        eruptDao.merge(data.get(0));

        JSONObject inputData = new JSONObject();
        inputData.set("clazz", "DailyInspection");
        inputData.set("insertData",data.get(0));
        EruptRemoteUtils.doPost(remoteProxyService.getMainServerUrl() + "/erupt-api/exchange/data/exchange", inputData, Map.class);
        return null;
    }
}
