package com.daliangang.statistics.handler;

import org.springframework.stereotype.Component;
import xyz.erupt.bi.fun.EruptBiHandler;

import java.util.ArrayList;
import java.util.Map;

@Component
public class CompanyAllMenuHandler implements EruptBiHandler {

    @Override
    public String exprHandler(String param, Map<String, Object> condition, String expr) {
        Object date = condition.get("searchDate");
        if(null != date){

            ArrayList searchDate = (ArrayList) date;
            expr = expr.replaceAll("#DATE1","'"+(String)searchDate.get(0)+"'") ;
            expr = expr.replaceAll("#DATE2","'"+(String)searchDate.get(1)+"'") ;
            expr = expr.replaceAll("#","") ;
        }
        return EruptBiHandler.super.exprHandler(param, condition, expr);
    }
}
