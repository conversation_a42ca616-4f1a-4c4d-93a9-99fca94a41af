/*
* Copyright © 2020-2035 yundingyun All rights reserved.
*/
package com.daliangang.safedaily.proxy;

import com.daliangang.core.DaliangangContext;
import com.daliangang.safedaily.entity.Design;
import org.springframework.stereotype.Service;
import xyz.erupt.annotation.fun.DataProxy;
import xyz.erupt.annotation.query.Condition;

import java.util.List;

@Service
public class DesignDataProxy implements DataProxy<Design> {

    @Override
    public String beforeFetch(List<Condition> conditions) {
        if (DaliangangContext.isDepartmentUser()) return "submitted=true";
        return null;
    }

    @Override
    public void beforeAdd(Design design) {
        design.setSubmitted(true);
    }

    @Override
    public void beforeUpdate(Design design) {
//        if (design.getSubmitted()) {
//            NotifyUtils.showErrorMsg("审查已经上报，请勿修改！");
//        }
    }

    @Override
    public void beforeDelete(Design design) {
//        if (design.getSubmitted()) {
//            NotifyUtils.showErrorMsg("审查已经上报，请勿删除！");
//        }
    }
}
