/*
* Copyright © 2020-2035 yundingyun All rights reserved.
*/
package com.daliangang.majorisk.entity;

import com.daliangang.core.CompanyRenderHandler;
import com.daliangang.majorisk.proxy.DcReserveReportingDataProxy;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import xyz.erupt.annotation.Erupt;
import xyz.erupt.annotation.EruptField;
import xyz.erupt.annotation.expr.ExprBool;
import xyz.erupt.annotation.sub_erupt.Power;
import xyz.erupt.annotation.sub_field.Edit;
import xyz.erupt.annotation.sub_field.EditType;
import xyz.erupt.annotation.sub_field.Readonly;
import xyz.erupt.annotation.sub_field.View;
import xyz.erupt.annotation.sub_field.sub_edit.ChoiceType;
import xyz.erupt.annotation.sub_field.sub_edit.Search;
import xyz.erupt.toolkit.db.Comment;
import xyz.erupt.toolkit.handler.RemoteCallChoiceFetchHandler;
import xyz.erupt.toolkit.handler.RemoteEntityChoiceFetchHandler;
import xyz.erupt.upms.model.auth.DataAuthModel;

import javax.persistence.Entity;
import javax.persistence.Lob;
import javax.persistence.Table;

@Erupt(name = "堆场每日储量上报", power = @Power(add = false, delete = false, export = false, importable = false, viewDetails = true, edit = true)
, dataProxy = DcReserveReportingDataProxy.class
, rowOperation = {
//		@RowOperation(
//				title = "上报",
//				icon = "fa fa-arrow-circle-o-up",
//				operationHandler = DcReserveReportingEscalationHandler.class,
//				ifExpr = "item.submitted=='未上报'",
//				mode = RowOperation.Mode.SINGLE
//		)
})
@Table(name = "tb_dc_reserve_reporting")
@Entity
@Getter
@Setter
@Comment("堆场每日储量上报")
@ApiModel("堆场每日储量上报")
public class DcReserveReporting extends DataAuthModel {

	@EruptField(
		views = @View(title = "企业名称"),
		edit = @Edit(title = "企业名称",search = @Search(ifRender = @ExprBool(exprHandler = CompanyRenderHandler.class)), readonly = @Readonly(add=true) , type = EditType.CHOICE, notNull = true,
		choiceType = @ChoiceType(fullSpan = true,fetchHandler = RemoteEntityChoiceFetchHandler.class, fetchHandlerParams ={"main","Enterprise","org_code,name"})))
	@Comment("企业名称")
	@ApiModelProperty("企业名称")
	private String company;

//	@EruptField(
//		views = @View(title = "现存货种"),
//			edit = @Edit(
//					title = "现存货种",
//					type = EditType.INPUT,
//					notNull = true
//			)
//	)
//	@Comment("现存货种")
//	@ApiModelProperty("现存货种")
//	private String yardType;

	@EruptField(
		views = @View(title = "堆场编号"),
		edit = @Edit(title = "堆场编号", type = EditType.CHOICE,readonly = @Readonly, notNull = true,
		choiceType = @ChoiceType(fetchHandler = RemoteCallChoiceFetchHandler.class, fetchHandlerParams ={"main","erupt-api/get/yardName"})))
	@Comment("堆场编号")
	@ApiModelProperty("堆场编号")
	private String yardNum;

//	@OneToMany(cascade = CascadeType.ALL, orphanRemoval = true)
//	@JoinColumn(name = "dc_memory_id")
//	@EruptField(views = @View(title = "储存情况"), edit = @Edit(title = "储存情况", type = EditType.TAB_TABLE_ADD))
//	@OrderBy
//	private List<Memory> scores;

//	@EruptField(
//		views = @View(title = "当前存储量"),
//		edit = @Edit(title = "当前存储量", type = EditType.INPUT,
//		inputType = @InputType))
//	@Comment("当前存储量")
//	@ApiModelProperty("当前存储量")
//	private String yardNow;

//	@EruptField(
//			views = @View(title = "状态"),
//			edit = @Edit(title = "状态",
//					type = EditType.BOOLEAN,
//					show = false,
//					boolType = @BoolType(trueText = "已上报", falseText = "未上报")
//			)
//	)
//	@CheckSubmit(linkProperty = "year", checkTimeDiffPeriod = 1)
//	@Comment("状态")
//	@ApiModelProperty("状态")
//	private Boolean submitted;


	@EruptField(
			views = @View(title = "现存货种"),
			edit = @Edit(
					title = "现存货种",
					type = EditType.INPUT,
					notNull = true
			)
	)
	@Comment("现存货种")
	@ApiModelProperty("现存货种")
	private String yardType;

	@EruptField(
			views = @View(title = "当前储存量"),
			edit = @Edit(title = "当前储存量",
					type = EditType.INPUT,
					notNull = true
					//choiceType = @ChoiceType(fetchHandler = EruptDictCodeChoiceFetchHandler.class, fetchHandlerParams = "yardNow")
			)
	)
	@Comment("当前储存量")
	@ApiModelProperty("当前储存量")
	private String yardNow;

	@EruptField(
			views = @View(title = "货种存储情况",show = false),
			edit = @Edit(title = "货种存储情况", type = EditType.TEXTAREA, notNull = true))
	@Comment("货种存储情况")
	@ApiModelProperty("货种存储情况")
	private @Lob String storageGoods;

	@EruptField(
			views = @View(title = "更新时间"),
			edit = @Edit(title = "更新时间", show = false))
	@Comment("更新时间")
	@ApiModelProperty("更新时间")
	private java.util.Date modifyTime;

}
