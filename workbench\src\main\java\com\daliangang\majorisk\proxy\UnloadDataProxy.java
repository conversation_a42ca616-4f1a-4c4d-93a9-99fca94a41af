/*
 * Copyright © 2020-2035 yundingyun All rights reserved.
 */
package com.daliangang.majorisk.proxy;

import com.daliangang.core.DaliangangContext;
import com.daliangang.device.entity.StorageTank;
import com.daliangang.majorisk.entity.Unload;
import com.daliangang.majorisk.entity.WorkAddress;
import com.daliangang.majorisk.enums.WorkAddressType;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import xyz.erupt.annotation.fun.DataProxy;
import xyz.erupt.annotation.query.Condition;
import xyz.erupt.jpa.dao.EruptDao;
import xyz.erupt.toolkit.service.EruptCacheRedis;
import xyz.erupt.toolkit.utils.NotifyUtils;
import xyz.erupt.upms.service.EruptUserService;

import javax.annotation.Resource;
import javax.transaction.Transactional;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

@Service
@Slf4j
public class UnloadDataProxy implements DataProxy<Unload> {
    @Resource
    private EruptDao eruptDao;

    @Resource
    private EruptUserService eruptUserService;
    @Resource
    private EruptCacheRedis eruptCacheRedis;

    @Override
    @Transactional
    public String beforeFetch(List<Condition> conditions) {
//        try {
//            this.prepare();//准备数据，临时方案：每次都清表重查，应改成定时从主系统同步
//        } catch (Exception ex) {
//            ex.printStackTrace();
//        }
        AtomicReference<String> returnStr = new AtomicReference<>(" 1 = 1 ");
        if (DaliangangContext.isDepartmentUser()) returnStr.set(returnStr.get() + " and submitted=true ");

        //作业状态
        Optional<Condition> checkObjectFirst = conditions.stream().filter(condition ->
                "workState".equals(condition.getKey())
        ).findFirst();
        checkObjectFirst.ifPresent(f -> {
            LocalDateTime now = LocalDateTime.now();
            String workState = (String) f.getValue();
            if ("NOT_STARTED".equals(workState)) {
                //未开始

                returnStr.set(returnStr.get() + " and DATE_FORMAT(ab_time,'%Y-%m-%d %H:%i:%S') > DATE_FORMAT('" + now + "','%Y-%m-%d %H:%i:%S')");
            } else if ("ENDED".equals(workState)) {
                //已结束
                returnStr.set(returnStr.get() + " and DATE_FORMAT(ae_time,'%Y-%m-%d %H:%i:%S') <DATE_FORMAT('" + now + "','%Y-%m-%d %H:%i:%S')");
            } else {
                //进行中
                returnStr.set(returnStr.get() + " and DATE_FORMAT('" + now + "','%Y-%m-%d %H:%i:%S') between DATE_FORMAT(ab_time,'%Y-%m-%d %H:%i:%S') and DATE_FORMAT(ae_time,'%Y-%m-%d %H:%i:%S')");
            }
            conditions.remove(f);
        });
        return returnStr.get();

    }


    @Transactional
    public void prepare() {
//        EruptUser eruptUser = eruptUserService.getCurrentEruptUser();
//        String orgCode = eruptUser != null && eruptUser.getEruptOrg() != null ? eruptUser.getEruptOrg().getCode() : null;
//        if (orgCode == null) return;

//        String deleteSql = "delete from tb_work_address";
//        EruptDaoUtils.updateNoForeignKeyChecks(deleteSql);
//        int sum = 0;

        List<StorageTank> tanks = eruptDao.queryEntityList(StorageTank.class);
//        tanks.forEach(tank -> eruptDao.persist(new WorkAddress(tank.getOrgCode(), WorkAddressType.TANK, tank.getId(), tank.getTankName())));
        List<WorkAddress> tanksLocal = eruptDao.queryEntityList(WorkAddress.class, "type=" + WorkAddressType.TANK.ordinal());
        //如果本地没有则追加
        tanks.forEach(tank -> {
            List<WorkAddress> addr = tanksLocal.stream().filter(local -> local.getAddrId() == tank.getId()).collect(Collectors.toList());
            if (addr.isEmpty())
                eruptDao.persist(new WorkAddress(tank.getOrgCode(), WorkAddressType.TANK, tank.getId(), tank.getName()));
            else {
                addr.get(0).setName(tank.getName());
                eruptDao.merge(addr.get(0));
            }
        });
        //如果已经没有则删除
        List<WorkAddress> tanksRemove = new ArrayList<>();
        tanksLocal.forEach(local -> {
            List<StorageTank> addr = tanks.stream().filter(tank -> tank.getId() == local.getAddrId()).collect(Collectors.toList());
            if (addr.isEmpty()) tanksRemove.add(local);
        });
        tanksRemove.forEach(addr -> eruptDao.delete(addr));

//        List<Yard> yards = eruptDao.queryEntityList(Yard.class);
//        yards.forEach(yard -> eruptDao.merge(new WorkAddress(yard.getOrgCode(), WorkAddressType.YARD, yard.getId(), yard.getYardName())));
//        sum += yards.size();
//        log.info("堆场信息 -> " + yards.size());

//        List<Warehouse> wares = eruptDao.queryEntityList(Warehouse.class);
//        wares.forEach(warehouse -> eruptDao.merge(new WorkAddress(warehouse.getOrgCode(), WorkAddressType.WARE, warehouse.getId(), warehouse.getWarehouseName())));
//        sum += wares.size();
//        log.info("仓库信息 -> " + wares.size());


//        List<Berth> berths = eruptDao.queryEntityList(Berth.class);
//        berths.forEach(berth -> eruptDao.merge(new WorkAddress(berth.getOrgCode(), WorkAddressType.BERTH, berth.getId(), berth.getBerthName())));
//        sum += berths.size();
//        log.info("泊位信息 -> " + berths.size());

//        List<Wharf> wharfs = eruptDao.queryEntityList(Wharf.class);
//        wharfs.forEach(wharf -> eruptDao.merge(new WorkAddress(wharf.getOrgCode(), WorkAddressType.WHARF, wharf.getId(), wharf.getWharfName())));
//        sum += wharfs.size();
//        log.info("码头信息 -> " + wharfs.size());
//        log.info("总计 -> " + sum);
    }

    @Override
    public Unload queryDataById(long id) {
        Unload unload = eruptDao.queryEntity(Unload.class, "id=" + id);
//        MetaContext.setAttribute(Unload.ATTR_UNLOAD, unload);
        List<String> maps = eruptDao.getJdbcTemplate().queryForList("select t.`name` from tb_unload_tanks st,tb_storage_tank t\n" +
                " where st.tanks_id = t.id  and st.unload_id = " +id +
                " union all\n" +
                " select t.`name` from tb_unload_yards st,tb_yard t\n" +
                " where st.yards_id = t.id and st.unload_id = " +id +
                " union all\n" +
                " select t.`name` from tb_unload_wares st,tb_warehouse t\n" +
                " where st.wares_id = t.id and st.unload_id = " +id +
                " union all\n" +
                " select t.`name` from tb_unload_berths st,tb_berth t\n" +
                " where st.berths_id = t.id and st.unload_id = " +id +
                " union all\n" +
                " select t.`name` from tb_unload_wharfs st,tb_loading_dock t\n" +
                " where st.wharfs_id = t.id and st.unload_id = " +id ,String.class);
        if(maps!=null && maps.size()!=0){
            String join = StringUtils.join(maps, "|");
            unload.setPosition(join);
        }
        eruptCacheRedis.getStringRedisTemplate().opsForValue().set(Unload.ATTR_UNLOAD,unload.getOrgCode(), 1, TimeUnit.MINUTES);
        return unload;
    }

    @Override
    @Transactional
    public void afterFetch(Collection<Map<String, Object>> list) {
        LocalDateTime now = LocalDateTime.now();
        list.forEach(vo -> {
            LocalDateTime abTime = LocalDateTime.parse(vo.get("abTime").toString());
            LocalDateTime aeTime = LocalDateTime.parse(vo.get("aeTime").toString());
            if (abTime.isAfter(now)) vo.put("workState", "NOT_STARTED");
            else if (aeTime.isBefore(now)) vo.put("workState", "ENDED");
            else vo.put("workState", "STARTED");
            //如果是政府用户
//            if(DaliangangContext.isDepartmentUser()){
//                Object riskDatabase = vo.get("riskDatabase");
//                log.info("风险数据库:"+riskDatabase);
//                if(riskDatabase!=null){
//                    Long riskDatabaseId = Long.valueOf((String) riskDatabase);
//                    EruptResultMap eruptResultMap = EruptDaoUtils.selectMap("select risk_name riskName from tb_risk_database_enterprise where id=" + riskDatabaseId);
//                    String riskName = (String) eruptResultMap.get("riskName");
//                    vo.put("riskDatabase",riskName);
//                }
//            }


        });

//        List<Unload> unloads = EruptDaoUtils.convert(list, Unload.class);
//        if (ObjectUtils.isNotEmpty(unloads)) {
//            LocalDateTime now = LocalDateTime.now();
//            unloads.forEach(v -> {
//                if (v.getAbTime().isAfter(now)) {
//                    v.setWorkState("NOT_STARTED");// 未开始
//                } else if (v.getAeTime().isBefore(now)) {
//                    v.setWorkState("ENDED");// 已结束
//                } else {
//                    v.setWorkState("STARTED");// 进行中
//                }
//                eruptDao.merge(v);
//            });
//        }
    }

    @Override
    public void beforeAdd(Unload unload) {
        if (ObjectUtils.isEmpty(unload.getTanks()) &&
                ObjectUtils.isEmpty(unload.getYards()) &&
                ObjectUtils.isEmpty(unload.getWares()) &&
                ObjectUtils.isEmpty(unload.getBerths()) &&
                ObjectUtils.isEmpty(unload.getWharfs())
        ) {
            NotifyUtils.showErrorDialog("请选择至少一个作业地点！");
        }

        Date now = new Date();
        if (unload.getBeginTime().compareTo(unload.getEndTime()) == 0) {
            NotifyUtils.showErrorDialog("计划开始时间不能等于计划结束时间！");
        } else if (unload.getBeginTime().compareTo(unload.getEndTime()) > 0) {
            NotifyUtils.showErrorDialog("计划结束时间不能小于计划开始时间！");
        }

        //实际结束时间不能小于实际开始时间
        if (unload.getAbTime() != null && unload.getAeTime() != null) {
            if (unload.getAbTime().compareTo(unload.getAeTime()) == 0) {
                NotifyUtils.showErrorDialog("实际开始时间不能等于实际结束时间！");
            } else if (unload.getAbTime().compareTo(unload.getAeTime()) > 0) {
                NotifyUtils.showErrorDialog("实际结束时间不能小于实际开始时间！");
            }
        }

        //设置实际开始时间
        if (unload.getBeginTime() != null) {
            unload.setAbTime(unload.getBeginTime().toInstant()
                    .atZone(ZoneId.systemDefault())
                    .toLocalDateTime());
        }
        //设置实际结束时间
        if (unload.getEndTime() != null) {
            unload.setAeTime(unload.getEndTime().toInstant()
                    .atZone(ZoneId.systemDefault())
                    .toLocalDateTime());
        }


        if (unload.getBeginTime().after(now)) {
            unload.setWorkState("NOT_STARTED");// 未开始
        } else if (unload.getEndTime().before(now)) {
            unload.setWorkState("ENDED");// 已结束
        } else {
            unload.setWorkState("STARTED");// 进行中
        }

//       if (unload.getAbTime().isAfter(now)) {
//               unload.setWorkState("NOT_STARTED");// 未开始
//       } else if (unload.getAeTime().isBefore(now)) {
//               unload.setWorkState("ENDED");// 已结束
//       } else {
//               unload.setWorkState("STARTED");// 进行中
//       }


    }

    @Override
    public void afterUpdate(Unload unload) {
//        String[] deleteSql = new String[]{
//                "delete from tb_unload_tanks where unload_id=" + unload.getId()
//        };
//        EruptDaoUtils.updateNoForeignKeyChecks(deleteSql);
//
//        List<String> insertSqls = new ArrayList<>();
//        unload.getTanks().forEach(tank -> {
//            String sql = "insert into tb_unload_tanks(unload_id,tanks_id)values(" + unload.getId() + "," + tank.getAddrId() + ")";
//            insertSqls.add(sql);
//        });
//        String[] finalSqls = new String[insertSqls.size()];
//        insertSqls.toArray(finalSqls);
//        EruptDaoUtils.updateNoForeignKeyChecks(finalSqls);
    }

    @Override
    public void beforeUpdate(Unload unload) {

        if (ObjectUtils.isEmpty(unload.getTanks()) &&
                ObjectUtils.isEmpty(unload.getYards()) &&
                ObjectUtils.isEmpty(unload.getWares()) &&
                ObjectUtils.isEmpty(unload.getBerths()) &&
                ObjectUtils.isEmpty(unload.getWharfs())
        ) {
            NotifyUtils.showErrorDialog("请选择至少一个作业地点！");
        }

        Date now = new Date();
        if (unload.getBeginTime().compareTo(unload.getEndTime()) == 0) {
            NotifyUtils.showErrorDialog("计划开始时间不能等于计划结束时间！");
        } else if (unload.getBeginTime().compareTo(unload.getEndTime()) > 0) {
            NotifyUtils.showErrorDialog("计划结束时间不能小于计划开始时间！");
        }

        //实际结束时间不能小于实际开始时间
        if (unload.getAbTime() != null && unload.getAeTime() != null) {
            if (unload.getAbTime().compareTo(unload.getAeTime()) == 0) {
                NotifyUtils.showErrorDialog("实际开始时间不能等于实际结束时间！");
            } else if (unload.getAbTime().compareTo(unload.getAeTime()) > 0) {
                NotifyUtils.showErrorDialog("实际结束时间不能小于实际开始时间！");
            }
        }

        //设置实际开始时间
        if (unload.getBeginTime() != null && unload.getAbTime() == null) {
            unload.setAbTime(unload.getBeginTime().toInstant()
                    .atZone(ZoneId.systemDefault())
                    .toLocalDateTime());
        }
        //设置实际结束时间
        if (unload.getEndTime() != null && unload.getAeTime() == null) {
            unload.setAeTime(unload.getEndTime().toInstant()
                    .atZone(ZoneId.systemDefault())
                    .toLocalDateTime());
        }


        if (unload.getSubmitted()) {
            NotifyUtils.showErrorMsg("报告单已经上报，请勿修改！");
        }
    }

    @Override
    public void beforeDelete(Unload report) {
        if (report.getSubmitted()) {
            NotifyUtils.showErrorMsg("报告单已经上报，请勿删除！");
        }
    }


}
