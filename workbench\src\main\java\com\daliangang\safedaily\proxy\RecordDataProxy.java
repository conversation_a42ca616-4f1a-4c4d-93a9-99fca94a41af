/*
* Copyright © 2020-2035 yundingyun All rights reserved.
*/
package com.daliangang.safedaily.proxy;

import com.daliangang.core.DaliangangContext;
import com.daliangang.safedaily.entity.Record;
import org.springframework.stereotype.Service;
import xyz.erupt.annotation.fun.DataProxy;
import xyz.erupt.annotation.query.Condition;

import java.util.List;

@Service
public class RecordDataProxy implements DataProxy<Record> {

    @Override
    public void beforeAdd(Record record) {
        record.setSubmitted(true);
    }

    @Override
    public String beforeFetch(List<Condition> conditions) {
        if (DaliangangContext.isDepartmentUser()) return "submitted=true";
        return null;
    }

    @Override
    public void beforeUpdate(Record record) {
//        if (record.getSubmitted()) {
//            NotifyUtils.showErrorMsg("报告单已经上报，请勿修改！");
//        }
    }

    @Override
    public void beforeDelete(Record record) {
//        if (record.getSubmitted()) {
//            NotifyUtils.showErrorMsg("报告单已经上报，请勿删除！");
//        }
    }
}
