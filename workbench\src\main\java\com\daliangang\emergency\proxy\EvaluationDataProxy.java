/*
 * Copyright © 2020-2035 yundingyun All rights reserved.
 */
package com.daliangang.emergency.proxy;

import com.daliangang.emergency.entity.Evaluation;
import org.springframework.stereotype.Service;
import xyz.erupt.annotation.fun.DataProxy;

@Service
public class EvaluationDataProxy implements DataProxy<Evaluation> {
    @Override
    public void excelExport(Object workbook) {
//        EruptDaoUtils.truncate(Evaluation.class);
    }
}
