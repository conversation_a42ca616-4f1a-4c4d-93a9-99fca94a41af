/*
 * Copyright © 2020-2035 yundingyun All rights reserved.
 */
package com.daliangang.safedaily.entity;

import com.daliangang.core.CompanyRenderHandler;
import com.daliangang.safedaily.operation.StandardizationEscalationHandler;
import com.daliangang.safedaily.operation.StandardizationPowerHandler;
import com.daliangang.safedaily.proxy.StandardizationDataProxy;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import xyz.erupt.annotation.Erupt;
import xyz.erupt.annotation.EruptField;
import xyz.erupt.annotation.expr.ExprBool;
import xyz.erupt.annotation.sub_erupt.Drill;
import xyz.erupt.annotation.sub_erupt.Link;
import xyz.erupt.annotation.sub_erupt.Power;
import xyz.erupt.annotation.sub_erupt.RowOperation;
import xyz.erupt.annotation.sub_field.Edit;
import xyz.erupt.annotation.sub_field.EditType;
import xyz.erupt.annotation.sub_field.Readonly;
import xyz.erupt.annotation.sub_field.View;
import xyz.erupt.annotation.sub_field.sub_edit.*;
import xyz.erupt.mns.core.DelayMsg;
import xyz.erupt.mns.core.DelayMsgProxy;
import xyz.erupt.toolkit.attachment.AttachmentDownloadHandler;
import xyz.erupt.toolkit.colorfont.ColorStateTimeFont;
import xyz.erupt.toolkit.colorfont.ColorStateTimeFontDataProxy;
import xyz.erupt.toolkit.db.Comment;
import xyz.erupt.toolkit.handler.DelOperationHandler;
import xyz.erupt.toolkit.handler.EruptDictCodeChoiceFetchHandler;
import xyz.erupt.toolkit.handler.RemoteEntityChoiceFetchHandler;
import xyz.erupt.toolkit.submit.CheckSubmit;
import xyz.erupt.upms.model.auth.DataAuthHandler;
import xyz.erupt.upms.model.auth.DataAuthModel;

import javax.persistence.*;
import java.util.List;

@Erupt(name = "港口企业安全生产标准化管理",orderBy = "createTime desc",
        power = @Power(
                powerHandler= StandardizationPowerHandler.class
        ),
        drills = {@Drill(
        title = "第二年自评", icon = "fa fa-list-alt",
        link = @Link(
                linkErupt = StandardizationSenond.class, joinColumn = "standardization.id"
        )),
         @Drill(
         title = "第三年自评", icon = "fa fa-list-alt",
         link = @Link(
         linkErupt = StandardizationThird.class, joinColumn = "standardization.id"
         ))
}
        , dataProxy = {StandardizationDataProxy.class, DelayMsgProxy.class, ColorStateTimeFontDataProxy.class},
       rowOperation = {
//        @RowOperation(title = "编辑", icon = "fa fa-edit", code = TplUtils.EDIT_OPER_CODE, eruptClass = Standardization.class,
//                operationHandler = EditOperationHandler.class, ifExpr = "item.submitted=='未上报'", ifExprBehavior = RowOperation.IfExprBehavior.HIDE, mode = RowOperation.Mode.SINGLE),
        @RowOperation(title = "删除", icon = "fa fa-trash-o", operationHandler = DelOperationHandler.class,
                //ifExpr = "item.submitted=='已上报'", ifExprBehavior = RowOperation.IfExprBehavior.HIDE,
                mode = RowOperation.Mode.SINGLE),
               //@RowOperation(title = "上报", icon = "fa fa-arrow-circle-o-up", operationHandler = StandardizationEscalationHandler.class, ifExpr = "item.submitted=='未上报'", mode = RowOperation.Mode.SINGLE),

        @RowOperation(title = "下载附件", icon = "fa fa-download", operationHandler = AttachmentDownloadHandler.class, mode = RowOperation.Mode.SINGLE)
})
@Table(name = "tb_standardization")
@Entity
@Getter
@Setter
@Comment("港口企业安全生产标准化管理")
@ApiModel("港口企业安全生产标准化管理")
@ColorStateTimeFont(stateKey = "stateStr", timeKey = "effectiveDate", interval = 30)
@DelayMsg(timeKey = "effectiveDate")
public class Standardization extends DataAuthModel implements ExprBool.ExprHandler{


    @EruptField(
            views = @View(title = "企业名称",width = "260px"),
            edit = @Edit(title = "企业名称", search = @Search(ifRender = @ExprBool(exprHandler = CompanyRenderHandler.class) ), readonly = @Readonly(exprHandler = DataAuthHandler.class), type = EditType.CHOICE, notNull = true,
                    choiceType = @ChoiceType(fullSpan = true,fetchHandler = RemoteEntityChoiceFetchHandler.class, fetchHandlerParams = {"main", "Enterprise", "org_code,name"})))
    @Comment("企业名称")
    @ApiModelProperty("企业名称")
    private String company;

    @EruptField(
            views = @View(title = "年度", sortable = true),
            edit = @Edit(title = "年度", readonly = @Readonly(add = false, edit = true), type = EditType.DATE, search = @Search, notNull = true,
                    dateType = @DateType(type = DateType.Type.YEAR)))
    @Comment("年度")
    @ApiModelProperty("年度")
    private String year;



    @EruptField(
            views = @View(title = "上报状态",show = false),
            edit = @Edit(title = "上报状态", type = EditType.BOOLEAN, show = false, /*notNull = true, */
                    boolType = @BoolType(trueText = "已上报", falseText = "未上报")))

    @CheckSubmit(linkProperty = "year", checkTimeDiffPeriod = 1)
    @Comment("上报状态")
    @ApiModelProperty("上报状态")
    private Boolean submitted;

    @EruptField(
            views = @View(title = "安全标准化等级"),
            edit = @Edit(title = "安全标准化等级", type = EditType.CHOICE, notNull = true,search = @Search,
                    choiceType = @ChoiceType(fetchHandler = EruptDictCodeChoiceFetchHandler.class, fetchHandlerParams = "grade")))

    @Comment("安全标准化等级")
    @ApiModelProperty("安全标准化等级")
    private String grade;

    @EruptField(
            views = @View(title = "发证日期"),
            edit = @Edit(title = "发证日期", type = EditType.DATE, notNull = true,
                    dateType = @DateType))

    @Comment("发证日期")
    @ApiModelProperty("发证日期")
    private java.util.Date issueDate;

    @EruptField(
            views = @View(title = "有效期至"),
            edit = @Edit(title = "有效期至", type = EditType.DATE, search = @Search(vague = true), notNull = true,
                    dateType = @DateType))

    @Comment("有效期至")
    @ApiModelProperty("有效期至")
    private java.util.Date effectiveDate;

    @EruptField(
            views = @View(title = "状态", show = false),
            edit = @Edit(title = "状态", type = EditType.CHOICE, notNull = false, show = false,search = @Search,
                    choiceType = @ChoiceType(fetchHandler = EruptDictCodeChoiceFetchHandler.class, fetchHandlerParams = "expiredState")))

    @Comment("状态")
    @ApiModelProperty("状态")
    private String state;

    @EruptField(
            views = @View(title = "状态", show = true),
            edit = @Edit(title = "状态", type = EditType.INPUT, notNull = false, show = false))
    @Transient
    private String stateStr;


    @EruptField(
            views = @View(title = "上传附件", show = false),
            edit = @Edit(title = "上传附件", type = EditType.DIVIDE))
    @Transient

    @Comment("上传附件")
    @ApiModelProperty("上传附件")
    private String fileDivide;

    @EruptField(
            views = @View(title = "安全标准化证书", show = false),
            edit = @Edit(title = "安全标准化证书", type = EditType.ATTACHMENT, notNull = true,
                    attachmentType = @AttachmentType(type = AttachmentType.Type.BASE, maxLimit = 20, fileTypes = {".pdf", ".doc", ".docx", ".xlsx", ".png", ".jpg", ".xls", ".ppt", ".pptx", ".zip", ".rar"})))

    @Comment("安全标准化证书")
    @ApiModelProperty("安全标准化证书")
    private String standardFile;

    @EruptField(
            views = @View(title = "自评报告", show = false),
            edit = @Edit(title = "自评报告", type = EditType.ATTACHMENT, notNull = true,
                    attachmentType = @AttachmentType(type = AttachmentType.Type.BASE, maxLimit = 20, fileTypes = {".pdf", ".doc", ".docx", ".xlsx", ".png", ".jpg", ".xls", ".ppt", ".pptx", ".zip", ".rar"})))

    @Comment("自评报告")
    @ApiModelProperty("自评报告")
    private String selfEvaluation;

    @EruptField(
            views = @View(title = "其他文件、资料", show = false),
            edit = @Edit(title = "其他文件、资料", type = EditType.ATTACHMENT,
                    attachmentType = @AttachmentType(type = AttachmentType.Type.BASE, maxLimit = 20, fileTypes = {".pdf", ".doc", ".docx", ".xlsx", ".png", ".jpg", ".xls", ".ppt", ".pptx", ".zip", ".rar"})))

    @Comment("其他文件、资料")
    @ApiModelProperty("其他文件、资料")
    private String otherFile;

    @EruptField(
            views = @View(title = "第二年是否上报",show = false),
            edit = @Edit(title = "第二年是否上报", type = EditType.BOOLEAN, show = false,
                    boolType = @BoolType(trueText = "是", falseText = "否")))
    @Comment("第二年是否上报")
    @ApiModelProperty("第二年是否上报")
    private Boolean isReportSecond;


    @EruptField(
            views = @View(title = "第三年是否上报",show = false),
            edit = @Edit(title = "第三年是否上报", type = EditType.BOOLEAN, show = false,
                    boolType = @BoolType(trueText = "是", falseText = "否")))
    @Comment("第三年是否上报")
    @ApiModelProperty("第三年是否上报")
    private Boolean isReportThird;


    @EruptField(
            views = @View(title = "分数", show = false),
            edit = @Edit(title = "分数", type = EditType.DIVIDE))
    @Transient
    private String scoreDivide;

    @EruptField(
            views = @View(title = "实际总得分"),
            edit = @Edit(title = "实际总得分", readonly = @Readonly, type = EditType.NUMBER, numberType = @NumberType(min = 0, max = 100),desc = "1、实际得分\n" +
                    "标准化评价中，理论分值减去不符合要求要素内容对应分值后得到的分数，即为实际得分。\n" +
                    "2、实际总得分\n" +
                    "标准化各要素的实际得分汇总后，得到的总分为实际总得分。"))
    @Comment("实际总得分")
    @ApiModelProperty("实际总得分")
    private Integer finalScore;

    @EruptField(
            views = @View(title = "理论总得分"),
            edit = @Edit(title = "理论总得分", readonly = @Readonly, type = EditType.NUMBER, numberType = @NumberType(min = 0, max = 100),desc = "1、标准分值\n" +
                    "标准分值为标准化16个要素中各自理论分值，其综和为1000分。\n" +
                    "2、在标准化评价中，部分要素内容在某家企业标准化评价资料中属于空白项，即该家企业资料中不包含该部分内容，理论上应减去该要素内容对应的分数，即理论分值减去不涉及要素内容对应分值。\n" +
                    "3、理论总得分\n" +
                    "标准化各要素理论分值汇总后总分为理论总得分。"))
    @Comment("理论总得分")
    @ApiModelProperty("理论总得分")
    private Integer gateScore;

    @EruptField(
            views = @View(title = "最终得分"),
            edit = @Edit(title = "最终得分", readonly = @Readonly, type = EditType.NUMBER, numberType = @NumberType(min = 0, max = 100),desc = "1、最终得分\n" +
                    "最终得分=实际总得分/理论总得分*1000"))
    @Comment("最终得分")
    @ApiModelProperty("最终得分")
    private String mostTotalScore;

    @OneToMany(cascade = CascadeType.ALL, orphanRemoval = true)
    @JoinColumn(name = "standard_id")
    @EruptField(edit = @Edit(title = "单项得分", type = EditType.TAB_TABLE_ADD))
    @OrderBy
    private List<StandardizationScore> scores;


    @Override
    public boolean handler(boolean expr, String[] params) {
        return false;
    }
}
