package com.daliangang.majorisk.operation;

import cn.hutool.http.HttpUtil;
import com.daliangang.majorisk.entity.FiveChecklistsMonitor;
import com.daliangang.majorisk.entity.FiveDetail;
import com.daliangang.majorisk.form.CurrentJobForm;
import com.daliangang.majorisk.form.WeatherForm;
import com.daliangang.majorisk.form.WorkInfoForm;
import com.google.gson.Gson;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import xyz.erupt.annotation.fun.OperationHandler;
import xyz.erupt.common.http.EruptRemoteUtils;
import xyz.erupt.core.annotation.EruptRouter;
import xyz.erupt.core.util.EruptSpringUtil;
import xyz.erupt.core.view.EruptApiModel;
import xyz.erupt.jpa.dao.EruptDao;
import xyz.erupt.toolkit.db.EruptResultMap;
import xyz.erupt.toolkit.remote.RemoteProxyService;
import xyz.erupt.toolkit.utils.EruptDaoUtils;
import xyz.erupt.toolkit.utils.NotifyUtils;
import xyz.erupt.upms.platform.EruptOption;
import xyz.erupt.upms.platform.EruptOptions;
import xyz.erupt.upms.service.EruptPlatformService;

import javax.annotation.Resource;
import javax.transaction.Transactional;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @since :2023/4/4:11:14
 */
@Service
@Slf4j
@EruptOptions(
        {
                @EruptOption(name = FiveChecklistsMonitorHandler.WEATHER, value = "http://172.24.19.26:20049/v3/weather/now.json?key=SswzIooTGlsdaFF-P&location=辽宁省大连市&language=zh-Hans", desc = "获取天气的接口"),
        }


)
public class FiveChecklistsMonitorHandler implements OperationHandler<FiveDetail, FiveChecklistsMonitor> {

    @Resource
    private EruptDao eruptDao;

    public static final String WEATHER = "WEATHER" ;

    @Resource
    private EruptPlatformService eruptPlatformService ;

    @Override
    @Transactional
    public String exec(List<FiveDetail> data, FiveChecklistsMonitor fiveChecklistsMonitor, String[] param) {
        //fiveChecklistsMonitor.setFiveChecklistsFoundation(data.get(0));
        if (ObjectUtils.isEmpty(fiveChecklistsMonitor.getMonitoring())) {
            NotifyUtils.showErrorMsg("存在数据未填写！");
        }
        eruptDao.merge(fiveChecklistsMonitor);
        return NotifyUtils.getSuccessNotify("保存成功！");
    }

    public FiveChecklistsMonitor detail(Long id) {
        FiveChecklistsMonitorController messageController = EruptSpringUtil.getBean(FiveChecklistsMonitorController.class);
        FiveChecklistsMonitor fiveChecklistsMonitor = messageController.initValue(id);
        return fiveChecklistsMonitor;
    }

    @RestController
    @Transactional
    public static class FiveChecklistsMonitorController {


        @Resource
        private EruptPlatformService eruptPlatformService ;

        @Value("${xinz.gy}")
        private String gy;
        @Value("${xinz.sy}")
        private String sy;


        @RequestMapping("erupt-api/data/FiveDetail/operator/monitor")
        @EruptRouter(verifyType = EruptRouter.VerifyType.LOGIN)
        public String recreate(@RequestBody EruptResultMap reuquest) {
//            if (ObjectUtils.isEmpty(reuquest.get("param"))) {
//                return EruptSpringUtil.getBean(FiveChecklistsMonitorHandler.class).exec(null, null, null);
//            }
            List<String> ids = reuquest.getAsList("ids", String.class);
            long id = Double.valueOf(ids.get(0)).longValue();
            EruptResultMap fiveDetail = EruptDaoUtils.selectOne("select * from tb_five_detail where id=" + id, EruptResultMap.class);
            FiveChecklistsMonitor monitor = (FiveChecklistsMonitor) reuquest.getAs("param", FiveChecklistsMonitor.class);
            monitor.setId(fiveDetail.getLong("five_checklists_monitor_id"));
            if (ObjectUtils.isEmpty(monitor.getMonitoring())) {
                NotifyUtils.showErrorMsg("存在数据未填写！");
            }
            EruptDaoUtils.getEruptDao().merge(monitor);
            return NotifyUtils.getSuccessNotify("保存成功！");
        }

        @RequestMapping("/erupt-api/data/FiveChecklistsMonitor/{id}")
        @EruptRouter(verifyType = EruptRouter.VerifyType.LOGIN)
        public FiveChecklistsMonitor initValue(@PathVariable("id") Long id) {
            EruptResultMap fiveDetail = EruptDaoUtils.selectOne("select * from tb_five_detail where id=" + id, EruptResultMap.class);
            FiveChecklistsMonitor fiveChecklistsMonitor1 = EruptDaoUtils.selectOne("select * from tb_fivechecklists_monitor where id=" + fiveDetail.getInt("five_checklists_monitor_id"), FiveChecklistsMonitor.class);

            FiveChecklistsMonitor fiveChecklistsMonitor = EruptSpringUtil.getBean(FiveChecklistsMonitor.class);

            if (ObjectUtils.isNotEmpty(fiveChecklistsMonitor1)) {
                fiveChecklistsMonitor.setMonitoring(fiveChecklistsMonitor1.getMonitoring());
            }

            // 今日装卸船作业
            List<CurrentJobForm> currentJobForm = EruptDaoUtils.selectOnes("SELECT id  from tb_unload_ship where CURRENT_DATE() between CONVERT(ab_time,DATE)  and CONVERT(ae_time,DATE) and org_code ='" + fiveDetail.getString("org_code") + "'", CurrentJobForm.class);
            fiveChecklistsMonitor.setShipWorkNum("0");
            fiveChecklistsMonitor.setShipWorkAddress("无");
            // 根据作业id查询作业地点名称
            if (ObjectUtils.isNotEmpty(currentJobForm)) {
                List addressList = new ArrayList();
                currentJobForm.forEach(v -> {
                    // 泊位
                    List<WorkInfoForm> workInfoForms1 = EruptDaoUtils.selectOnes("select 'BW' as type,tb.name from tb_unload_ship_berths tub left join tb_berth tb on tub.berths_id = tb.id where tub.unload_id =" + v.getId(), WorkInfoForm.class);
                    if (ObjectUtils.isNotEmpty(workInfoForms1)) {
                        workInfoForms1.forEach(m -> {
                            addressList.add(m.getName());
                        });
                    }
                    // 堆场
                    List<WorkInfoForm> workInfoForms2 = EruptDaoUtils.selectOnes("select 'DC' as type,tb.name from tb_unload_ship_yards tub left join tb_yard tb on tub.yards_id = tb.id where tub.unload_id =" + v.getId(), WorkInfoForm.class);
                    if (ObjectUtils.isNotEmpty(workInfoForms2)) {
                        workInfoForms2.forEach(m -> {
                            addressList.add(m.getName());
                        });
                    }
                    // 仓库
                    List<WorkInfoForm> workInfoForms3 = EruptDaoUtils.selectOnes("select 'CK' as type,tb.name from tb_unload_ship_wares tub left join tb_warehouse tb on tub.wares_id = tb.id where tub.unload_id =" + v.getId(), WorkInfoForm.class);
                    if (ObjectUtils.isNotEmpty(workInfoForms3)) {
                        workInfoForms3.forEach(m -> {
                            addressList.add(m.getName());
                        });
                    }
                    // 栈台
                    List<WorkInfoForm> workInfoForms4 = EruptDaoUtils.selectOnes("select 'ZT' as type,tb.name from tb_unload_ship_wharfs tub left join tb_loading_dock tb on tub.wharfs_id = tb.id where tub.unload_id =" + v.getId(), WorkInfoForm.class);
                    if (ObjectUtils.isNotEmpty(workInfoForms4)) {
                        workInfoForms4.forEach(m -> {
                            addressList.add(m.getName());
                        });
                    }
                    // 储罐
                    List<WorkInfoForm> workInfoForms5 = EruptDaoUtils.selectOnes("select 'CG' as type,tb.name from tb_unload_ship_tanks tub left join tb_storage_tank tb on tub.tanks_id = tb.id where tub.unload_id =" + v.getId(), WorkInfoForm.class);
                    if (ObjectUtils.isNotEmpty(workInfoForms5)) {
                        workInfoForms5.forEach(m -> {
                            addressList.add(m.getName());
                        });
                    }

                });
                // 地点赋值
                String join = "无";
                if (ObjectUtils.isNotEmpty(addressList)) {
                    join = String.join(",", addressList);
                }
                fiveChecklistsMonitor.setShipWorkNum(String.valueOf(currentJobForm.size()));
                fiveChecklistsMonitor.setShipWorkAddress(join);
            }

            // 今日装卸车/火车
            List<CurrentJobForm> currentJobFormCar = EruptDaoUtils.selectOnes("SELECT id ,work_type as type from tb_unload where CURRENT_DATE() between CONVERT(ab_time,DATE)  and CONVERT(ae_time,DATE) and work_type = 'CAR'  and org_code ='" + fiveDetail.getString("org_code") + "'", CurrentJobForm.class);
            fiveChecklistsMonitor.setCarWorkNum("0");
            fiveChecklistsMonitor.setCarWorkAddress("无");
            if (ObjectUtils.isNotEmpty(currentJobFormCar)) {
                List addressCar = new ArrayList();
                currentJobFormCar.forEach(v -> {
                    // 泊位
                    List<WorkInfoForm> workInfoForms1 = EruptDaoUtils.selectOnes("select 'BW' as type,tb.name  from tb_unload_berths tub left join tb_berth tb on tub.berths_id = tb.id where tub.unload_id =" + v.getId(), WorkInfoForm.class);
                    if (ObjectUtils.isNotEmpty(workInfoForms1)) {
                        workInfoForms1.forEach(m -> {
                            addressCar.add(m.getName());
                        });
                    }
                    // 堆场
                    List<WorkInfoForm> workInfoForms2 = EruptDaoUtils.selectOnes("select 'DC' as type,tb.name  from tb_unload_berths tub left join tb_berth tb on tub.berths_id = tb.id where tub.unload_id =" + v.getId(), WorkInfoForm.class);
                    if (ObjectUtils.isNotEmpty(workInfoForms2)) {
                        workInfoForms2.forEach(m -> {
                            addressCar.add(m.getName());
                        });
                    }

                    // 仓库
                    List<WorkInfoForm> workInfoForms3 = EruptDaoUtils.selectOnes("select 'CK' as type,tb.name  from tb_unload_berths tub left join tb_berth tb on tub.berths_id = tb.id where tub.unload_id =" + v.getId(), WorkInfoForm.class);
                    if (ObjectUtils.isNotEmpty(workInfoForms3)) {
                        workInfoForms3.forEach(m -> {
                            addressCar.add(m.getName());
                        });
                    }

                    // 栈台
                    List<WorkInfoForm> workInfoForms4 = EruptDaoUtils.selectOnes("select 'ZT' as type,tb.name  from tb_unload_berths tub left join tb_berth tb on tub.berths_id = tb.id where tub.unload_id =" + v.getId(), WorkInfoForm.class);
                    if (ObjectUtils.isNotEmpty(workInfoForms4)) {
                        workInfoForms4.forEach(m -> {
                            addressCar.add(m.getName());
                        });
                    }

                    // 储罐
                    List<WorkInfoForm> workInfoForms5 = EruptDaoUtils.selectOnes("select 'CG' as type,tb.name  from tb_unload_berths tub left join tb_berth tb on tub.berths_id = tb.id where tub.unload_id =" + v.getId(), WorkInfoForm.class);
                    if (ObjectUtils.isNotEmpty(workInfoForms5)) {
                        workInfoForms5.forEach(m -> {
                            addressCar.add(m.getName());
                        });
                    }
                });
                // 地点赋值
                String join = "无";
                if (ObjectUtils.isNotEmpty(addressCar)) {
                    join = String.join(",", addressCar);
                }
                fiveChecklistsMonitor.setCarWorkNum(String.valueOf(currentJobFormCar.size()));
                fiveChecklistsMonitor.setCarWorkAddress(join);
            }

            // 火车
            List<CurrentJobForm> currentJobFormTrain = EruptDaoUtils.selectOnes("SELECT id ,work_type as type from tb_unload where CURRENT_DATE() between CONVERT(ab_time,DATE)  and CONVERT(ae_time,DATE) and work_type = 'TRAIN'  and org_code ='" + fiveDetail.getString("org_code") + "'", CurrentJobForm.class);
            fiveChecklistsMonitor.setTrainWorkNum("0");
            fiveChecklistsMonitor.setTrainWorkAddress("无");
            if (ObjectUtils.isNotEmpty(currentJobFormTrain)) {
                List addressTrain = new ArrayList();
                currentJobFormCar.forEach(v -> {
                    // 泊位
                    List<WorkInfoForm> workInfoForms1 = EruptDaoUtils.selectOnes("select 'BW' as type,tb.name  from tb_unload_berths tub left join tb_berth tb on tub.berths_id = tb.id where tub.unload_id =" + v.getId(), WorkInfoForm.class);
                    if (ObjectUtils.isNotEmpty(workInfoForms1)) {
                        workInfoForms1.forEach(m -> {
                            addressTrain.add(m.getName());
                        });
                    }
                    // 堆场
                    List<WorkInfoForm> workInfoForms2 = EruptDaoUtils.selectOnes("select 'DC' as type,tb.name  from tb_unload_berths tub left join tb_berth tb on tub.berths_id = tb.id where tub.unload_id =" + v.getId(), WorkInfoForm.class);
                    if (ObjectUtils.isNotEmpty(workInfoForms2)) {
                        workInfoForms2.forEach(m -> {
                            addressTrain.add(m.getName());
                        });
                    }

                    // 仓库
                    List<WorkInfoForm> workInfoForms3 = EruptDaoUtils.selectOnes("select 'CK' as type,tb.name  from tb_unload_berths tub left join tb_berth tb on tub.berths_id = tb.id where tub.unload_id =" + v.getId(), WorkInfoForm.class);
                    if (ObjectUtils.isNotEmpty(workInfoForms3)) {
                        workInfoForms3.forEach(m -> {
                            addressTrain.add(m.getName());
                        });
                    }

                    // 栈台
                    List<WorkInfoForm> workInfoForms4 = EruptDaoUtils.selectOnes("select 'ZT' as type,tb.name  from tb_unload_berths tub left join tb_berth tb on tub.berths_id = tb.id where tub.unload_id =" + v.getId(), WorkInfoForm.class);
                    if (ObjectUtils.isNotEmpty(workInfoForms4)) {
                        workInfoForms4.forEach(m -> {
                            addressTrain.add(m.getName());
                        });
                    }

                    // 储罐
                    List<WorkInfoForm> workInfoForms5 = EruptDaoUtils.selectOnes("select 'CG' as type,tb.name  from tb_unload_berths tub left join tb_berth tb on tub.berths_id = tb.id where tub.unload_id =" + v.getId(), WorkInfoForm.class);
                    if (ObjectUtils.isNotEmpty(workInfoForms5)) {
                        workInfoForms5.forEach(m -> {
                            addressTrain.add(m.getName());
                        });
                    }
                });
                // 地点赋值
                String join = "无";
                if (ObjectUtils.isNotEmpty(addressTrain)) {
                    join = String.join(",", addressTrain);
                }
                fiveChecklistsMonitor.setTrainWorkNum(String.valueOf(currentJobFormCar.size()));
                fiveChecklistsMonitor.setTrainWorkAddress(join);
            }


            // 动火/受限
            List<CurrentJobForm> currentJobForms = EruptDaoUtils.selectOnes("SELECT COUNT(*) as num,type from tb_maintenance where  CURRENT_DATE() between CONVERT(star_date,DATE)  and CONVERT(end_date,DATE) and org_code ='" + fiveDetail.getString("org_code") + "' GROUP by type ", CurrentJobForm.class);
            if (ObjectUtils.isNotEmpty(currentJobForms)) {
                currentJobForms.forEach(v -> {
                    if (v.getType().equals("DH")) {
                        fiveChecklistsMonitor.setFireWorkNum(v.getNum());
                    }
                    if (v.getType().equals("SX")) {
                        fiveChecklistsMonitor.setRestrictedWorkNum(v.getNum());
                    }

                });
            } else {
                fiveChecklistsMonitor.setFireWorkNum("0");

            }
            if (ObjectUtils.isEmpty(fiveChecklistsMonitor.getRestrictedWorkNum())) {
                fiveChecklistsMonitor.setRestrictedWorkNum("0");
            }
            if (ObjectUtils.isEmpty(fiveChecklistsMonitor.getFireWorkNum())) {
                fiveChecklistsMonitor.setFireWorkNum("0");
            }
            // 消防设施检测/防雷装置检测/压力表检测
            RemoteProxyService remoteProxyService = EruptSpringUtil.getBean(RemoteProxyService.class);
            EruptApiModel eruptApiModel = EruptRemoteUtils.doGet(remoteProxyService.getMainServerUrl() + "/erupt-api/get/selectFacilityInspectionInfo/" + fiveDetail.getString("org_code"), EruptApiModel.class);
            if (ObjectUtils.isNotEmpty(eruptApiModel.getData())) {
                fiveChecklistsMonitor.setDeviceStatus(eruptApiModel.getData().toString());
            }


//            StringBuffer stringBuffer = new StringBuffer();
//
//            // 消防设施检测
//            CurrentJobForm currentJobForm2 = EruptDaoUtils.selectOne("SELECT COUNT(*) as num  from tb_facility_inspection  where CURRENT_DATE() > validity_period and org_code ='" + fiveDetail.getString("org_code") + "'", CurrentJobForm.class);
//            if (!currentJobForm2.getNum().equals("0")) {
//                stringBuffer.append("消防设施已过期!");
//            }
//            // 防雷装置检测
//            CurrentJobForm currentJobForm3 = EruptDaoUtils.selectOne("SELECT COUNT(*) as num  from tb_lightning_protection  where CURRENT_DATE() > validity_period and org_code ='" + fiveDetail.getString("org_code") + "'", CurrentJobForm.class);
//            if (!currentJobForm3.getNum().equals("0")) {
//                stringBuffer.append("防雷装置已过期!");
//            }
//            // 压力表检测
//            CurrentJobForm currentJobForm4 = EruptDaoUtils.selectOne("SELECT COUNT(*) as num  from tb_pressure_gauge  where CURRENT_DATE() > validity_period and org_code ='" + fiveDetail.getString("org_code") + "'", CurrentJobForm.class);
//            if (!currentJobForm4.getNum().equals("0")) {
//                stringBuffer.append("压力表已过期!");
//            }
//            //安全阀检测
//            CurrentJobForm currentJobForm5 = EruptDaoUtils.selectOne("SELECT COUNT(*) as num  from tb_safety_valve  where CURRENT_DATE() > validity_period and org_code ='" + fiveDetail.getString("org_code") + "'", CurrentJobForm.class);
//            if (!currentJobForm5.getNum().equals("0")) {
//                stringBuffer.append("安全阀已过期!");
//            }
//            //可燃气体报警检测
//            CurrentJobForm currentJobForm6 = EruptDaoUtils.selectOne("SELECT COUNT(*) as num  from tb_combustible_gas  where CURRENT_DATE() > validity_period and org_code ='" + fiveDetail.getString("org_code") + "'", CurrentJobForm.class);
//            if (!currentJobForm6.getNum().equals("0")) {
//                stringBuffer.append("可燃气体报警已过期!");
//            }
//            if (stringBuffer.length() == 0) {
//                stringBuffer.append("本企业港口设施维护管理系统中录入的设备设施均在检测有效期内");
//            }
//            fiveChecklistsMonitor.setDeviceStatus(String.valueOf(stringBuffer));

            // 天气
            String weather = "";
            try {
                String url = eruptPlatformService.getOption(FiveChecklistsMonitorHandler.WEATHER).getAsString() ;
//                String url = "https://api.seniverse.com/v3/weather/now.json?key=" + sy + "&location=辽宁省大连市&language=zh-Hans";
                String result = HttpUtil.get(url);
                Gson gson = new Gson();
                WeatherForm weatherForm = gson.fromJson(result, WeatherForm.class);
                fiveChecklistsMonitor.setWeatherConditions(weatherForm.getResults().get(0).getNow().getText() + "," + weatherForm.getResults().get(0).getNow().getTemperature() + "度");
                if (ObjectUtils.isNotEmpty(weatherForm.getResults().get(0).getNow().getWind_scale()) && Integer.parseInt(weatherForm.getResults().get(0).getNow().getWind_scale()) >= 6) {
                    weather = "注意：大风天气，海岸可出现高潮、巨浪，破坏力大，谨防港口大型机械倾覆，谨防港口设施遭到破坏。";
                } else if (weatherForm.getResults().get(0).getNow().getText().contains("雷")) {
                    weather = "注意：今日可能有雷电发生，确保设备设施防雷接地有效，做好可燃物控制。";
                } else if (weatherForm.getResults().get(0).getNow().getText().contains("雨")) {
                    weather = "注意：今日有雨，谨防电气设备发生故障，谨防发生内涝影响设施安全。";
                } else if (weatherForm.getResults().get(0).getNow().getText().contains("雾")) {
                    weather = "注意：今日有雾，能见度低，影响作业安全。";
                } else {
                    weather = "注意：无";
                }
            } catch (Exception ex) {
                log.error("访问天气信息报错 -> " + ex.getMessage());
            }
            fiveChecklistsMonitor.setWeatherWarning(weather);


            return fiveChecklistsMonitor;
        }
    }

}
