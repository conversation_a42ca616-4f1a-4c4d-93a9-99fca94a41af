package com.daliangang.safedaily.operation;

import com.daliangang.core.DaliangangContext;
import org.springframework.stereotype.Service;
import xyz.erupt.annotation.fun.PowerHandler;
import xyz.erupt.annotation.fun.PowerObject;
@Service
public class StandardizationPowerHandler implements PowerHandler {

    @Override
    public void handler(PowerObject power) {
        //如果是企业用户
        if(DaliangangContext.isEnterpriseUser()){
            power.setViewDetails(true);
            power.setQuery(true);
            power.setAdd(true);
            power.setDelete(false);
            power.setEdit(true);
            power.setImportable(false);
        }
        //如果是政府用户
        if(DaliangangContext.isDepartmentUser()){
            power.setViewDetails(true);
            power.setQuery(true);
            power.setAdd(false);
            power.setDelete(false);
            power.setEdit(false);
            power.setImportable(false);
        }
    }
}
