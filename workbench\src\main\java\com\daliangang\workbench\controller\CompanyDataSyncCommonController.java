package com.daliangang.workbench.controller;

import com.daliangang.emergency.entity.EmergencyDuty;
import com.daliangang.majorisk.controller.RiskDatabaseEnterpriseController;
import com.daliangang.majorisk.entity.CkReserveReporting;
import com.daliangang.majorisk.entity.DcReserveReporting;
import com.daliangang.majorisk.entity.ReserveReporting;
import com.daliangang.safedaily.entity.*;
import com.daliangang.safedaily.operation.StandardizationEscalationHandler;
import com.daliangang.safedaily.operation.StandardizationEscalationSecondHandler;
import com.daliangang.workbench.entity.EmployeeCertificate;
import com.daliangang.workbench.entity.EmployeeInformation;
import com.daliangang.workbench.entity.EnterpriseInfo;
import com.daliangang.workbench.entity.ZhihuiyunDataSync;
import com.google.gson.Gson;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import xyz.erupt.core.annotation.EruptRecordOperate;
import xyz.erupt.core.annotation.EruptRouter;
import xyz.erupt.core.config.GsonFactory;
import xyz.erupt.core.controller.EruptDataController;
import xyz.erupt.core.controller.EruptFileController;
import xyz.erupt.core.controller.EruptModifyController;
import xyz.erupt.core.exception.EruptApiErrorTip;
import xyz.erupt.core.naming.EruptRecordNaming;
import xyz.erupt.core.util.EruptSpringUtil;
import xyz.erupt.core.view.EruptApiModel;
import xyz.erupt.jpa.dao.EruptDao;
import xyz.erupt.toolkit.utils.EruptDaoUtils;
import xyz.erupt.toolkit.utils.NotifyUtils;
import xyz.erupt.toolkit.utils.SqlUtils;
import xyz.erupt.upms.model.EruptUser;
import xyz.erupt.upms.model.auth.DataAuthModel;
import xyz.erupt.upms.model.auth.DataAuthVoProxy;
import xyz.erupt.upms.model.template.EruptDeptTemplate;
import xyz.erupt.upms.service.EruptUserService;

import javax.annotation.Resource;
import javax.transaction.Transactional;
import java.text.SimpleDateFormat;
import java.util.*;

@Slf4j
@RestController
@RequestMapping({"/open-api/data/modify"})
public class CompanyDataSyncCommonController {


    @Resource
    private EruptModifyController eruptModifyController ;
    @Resource
    private EruptDataController eruptDataController ;
    @Resource
    private EruptFileController eruptFileController ;
    @Resource
    private EruptDao eruptDao ;
    @Resource
    private DataAuthVoProxy dataAuthVoProxy ;
    @Resource
    private StandardizationEscalationHandler standardizationEscalationHandler ;
    @Resource
    private StandardizationEscalationSecondHandler standardizationEscalationSecondHandler ;
    @Resource
    private EruptUserService eruptUserService  ;

    @Resource
    private RiskDatabaseEnterpriseController riskDatabaseEnterpriseController ;
    /**
     * 新增通用接口
     */
    @PostMapping("/{erupt}")
    @EruptRecordOperate(value = "新增通用接口", dynamicConfig = EruptRecordNaming.class)
    @EruptRouter(skipAuthIndex = 3, authIndex = 1, verifyType = EruptRouter.VerifyType.ERUPT,verifyMethod = EruptRouter.VerifyMethod.HEADER_PARAM)
    @Transactional
    public EruptApiModel add(@PathVariable("erupt") String erupt, @RequestBody JsonObject data){
        //插入到表中
        ZhihuiyunDataSync zhihuiyunDataSync  = this.addLogData(erupt,data,"新增通用接口");

        preAddHandle(erupt,data);
        try{
            EruptApiModel eruptApiModel = eruptModifyController.addEruptDataCtrl(erupt, data);
            if(EruptApiModel.Status.SUCCESS.equals(eruptApiModel.getStatus())){
                Object obj = eruptApiModel.getData();
                Gson gson = GsonFactory.getGson();
                Map map = gson.fromJson(gson.toJson(obj), Map.class);
//                Map<String, Object> map = EruptDaoUtils.castMap(obj);
                Object id = map.get("id");
                eruptApiModel = EruptApiModel.successApi(id) ;
                //更新到表中
                EruptApiModel eruptApiModel1 = this.afterAddHandle(erupt, obj,data);
                if(null != eruptApiModel1){
                    eruptApiModel = eruptApiModel1 ;
                }
                this.modifyLogData(zhihuiyunDataSync,obj,eruptApiModel.getMessage());


                return eruptApiModel;
            }else {
                this.modifyLogData(zhihuiyunDataSync,null,eruptApiModel.getMessage());
                return eruptApiModel;
            }
        }catch (EruptApiErrorTip e){
            this.modifyLogData(zhihuiyunDataSync,null,e.getMessage());
            NotifyUtils.showWarnMsg(e.getMessage());
        }
        return null ;

    }

    /**
     * 修改通用接口
     */
    @PostMapping("/{erupt}/update")
    @EruptRecordOperate(value = "修改通用接口", dynamicConfig = EruptRecordNaming.class)
    @EruptRouter(skipAuthIndex = 3, authIndex = 1, verifyType = EruptRouter.VerifyType.ERUPT,verifyMethod = EruptRouter.VerifyMethod.HEADER_PARAM)
    @Transactional
    public EruptApiModel update(@PathVariable("erupt") String erupt, @RequestBody JsonObject data) throws IllegalAccessException {
        //插入到表中
        ZhihuiyunDataSync zhihuiyunDataSync  = this.addLogData(erupt,data,"修改通用接口");
        JsonObject jsonObject = preUpdateHandle(erupt, data);
        if(null != jsonObject){
            data = jsonObject ;
        }
        try{
            EruptApiModel eruptApiModel = eruptModifyController.putUpdateEruptDataImpl(erupt, data);
            if(EruptApiModel.Status.SUCCESS.equals(eruptApiModel.getStatus())){
                Object obj = eruptApiModel.getData();
                Gson gson = GsonFactory.getGson();
                Map map = gson.fromJson(gson.toJson(obj), Map.class);
//                Map<String, Object> map = EruptDaoUtils.castMap(obj);
                Object id = map.get("id");
                eruptApiModel = EruptApiModel.successApi(id) ;
                //更新到表中
                EruptApiModel eruptApiModel1 = this.afterUpdateHandle(erupt,obj) ;
                if(null != eruptApiModel1){
                    eruptApiModel = eruptApiModel1 ;
                }
                this.modifyLogData(zhihuiyunDataSync,obj,eruptApiModel.getMessage());


                return eruptApiModel;
            }else {
                this.modifyLogData(zhihuiyunDataSync,null,eruptApiModel.getMessage());
                return eruptApiModel;
            }
        }catch (EruptApiErrorTip e){
            this.modifyLogData(zhihuiyunDataSync,null,e.getMessage());
            NotifyUtils.showWarnMsg(e.getMessage());
        }
        return null ;
    }

    /**
     * 查询 通用接口
     */
    @GetMapping("/{erupt}/query")
    @EruptRecordOperate(value = "查询通用接口", dynamicConfig = EruptRecordNaming.class)
//    @EruptRouter(skipAuthIndex = 3, authIndex = 1, verifyType = EruptRouter.VerifyType.ERUPT,verifyMethod = EruptRouter.VerifyMethod.HEADER_PARAM)
    @Transactional
    public EruptApiModel query(@PathVariable("erupt") String eruptName){
        EruptUser currentEruptUser = EruptSpringUtil.getBean(EruptUserService.class).getCurrentEruptUser();
        String code = currentEruptUser.getEruptOrg().getCode();
        String sql = "";
        switch (eruptName) {
            case "PortArea"://港区管理
                sql = " select id as id,portarea_name as name from tb_port_area " ;
                break ;
            case "Wharf"://码头管理
                sql = " select id as id,name as name from tb_wharf where org_code ="+SqlUtils.wrapStr(code) ;
                break ;
            case "TankGroup"://罐组管理
                sql = " select id as id,tank_group name,tank_farm as tankFarm,port_area as portArea  from tb_tank_group where org_code ="+SqlUtils.wrapStr(code) ;
                break ;
            case "TankFarm"://罐区管理
                sql = " select id as id,tank_farm as name  from tb_tank_farm where org_code ="+SqlUtils.wrapStr(code) ;
                break ;
            case "ReserveReporting"://储罐每日储量上报
                sql = " select * from tb_reserve_reporting where org_code ="+SqlUtils.wrapStr(code) ;
                break ;
            case "CkReserveReporting"://仓库每日储量上报
                sql = " select * from tb_ck_reserve_reporting where org_code ="+SqlUtils.wrapStr(code) ;
                break ;
            case "DcReserveReporting"://堆场每日储量上报 tb_dc_reserve_reporting
                sql = " select * from tb_dc_reserve_reporting where org_code ="+SqlUtils.wrapStr(code) ;
                break ;
            case "StandardizationScore"://标准化得分 tb_standardization_score
                sql = " select * from tb_standardization_score " ;
                break ;
            case "EmployeeInformation":
                List<EmployeeInformation> employeeInformations = eruptDao.queryEntityList(EmployeeInformation.class, "orgCode =" + SqlUtils.wrapStr(code));
                return EruptApiModel.successApi(employeeInformations);

        }
        if(StringUtils.isNotEmpty(sql)){
            List<Map<String, Object>> maps = eruptDao.getJdbcTemplate().queryForList(sql);
            return EruptApiModel.successApi(maps);
        }else{
            return EruptApiModel.successApi();
        }


    }

    /**
     * 查询 初始化数据通用接口
     */
    @GetMapping("/{erupt}/queryInit")
    @EruptRecordOperate(value = "查询初始化数据通用接口", dynamicConfig = EruptRecordNaming.class)
    @EruptRouter(skipAuthIndex = 3, authIndex = 1, verifyType = EruptRouter.VerifyType.ERUPT,verifyMethod = EruptRouter.VerifyMethod.HEADER_PARAM)
    @Transactional
    public EruptApiModel queryInitValue(@PathVariable("erupt") String eruptName) throws IllegalAccessException, InstantiationException {
        Map<String, Object> map = eruptDataController.initEruptValues(eruptName, null);
        return EruptApiModel.successApi(map);

    }

    /**
     * 上传文件接口
     * @param eruptName
     * @param fieldName
     * @param file
     * @return
     */
    @SneakyThrows
    @PostMapping("/upload/{erupt}/{field}")
    @EruptRouter(skipAuthIndex = 4, authIndex = 1, verifyType = EruptRouter.VerifyType.ERUPT,verifyMethod = EruptRouter.VerifyMethod.HEADER_PARAM)
    public EruptApiModel upload(@PathVariable("erupt") String eruptName, @PathVariable("field") String fieldName, @RequestParam("file") MultipartFile file) {
        return eruptFileController.upload(eruptName,fieldName,file,false) ;
    }

    /**
     * 获取重大风险数据库信息
     * @param
     * @return
     */
    @RequestMapping("/get/riskDatabase")
    @EruptRouter(verifyType = EruptRouter.VerifyType.LOGIN)
    public EruptApiModel getPortareaName() {
        EruptApiModel portareaName = riskDatabaseEnterpriseController.getPortareaName();
        return portareaName ;
    }

    /**
     * 新增前处理数据
     * @param erupt
     * @param data
     */
    public void preAddHandle(String erupt, JsonObject data)  {
        Gson gson = new Gson();
        //安全承诺公告Release、可靠性报告单 Report 不走addbehavior
        switch (erupt) {
            case "Release"://安全承诺公告
            case "Report": //可靠性报告单
                break;
            case "EmployeeInformation":
                JsonElement orgCode = data.get("orgCode");
                String deptSql = "select * from e_upms_template_dept where org_code='" + orgCode.getAsString() + "'";
                List<EruptDeptTemplate> depts = EruptDaoUtils.selectOnes(deptSql, EruptDeptTemplate.class);
                if (!depts.isEmpty()) {
                    data.add("department",gson.toJsonTree(depts.get(0)));

                }
                break;
            case "EnterpriseCertificate":
                EruptUser currentEruptUser = eruptUserService.getCurrentEruptUser();
                String orgId = currentEruptUser.getEruptOrg().getCode();//获取当前登录人的公司code
                data.addProperty("orgCode",orgId);
                data.addProperty("company",orgId);
                break;
            default:
                this.addBehavior(erupt, data);
        }
    }

    /**
     * 修改前处理数据
     * @param erupt
     * @param data
     */
    public JsonObject preUpdateHandle(String erupt,JsonObject data){
//        Long id = data.get("id").getAsLong();//id 不能放在这上面，因为企业信息是没有不会传id的
        Gson gson = GsonFactory.getGson();

        Class eruptClass = null;
        String fieldName = "" ;
        switch (erupt) {
            case "ReserveReporting"://储罐每日储量上报
                eruptClass = ReserveReporting.class ;
                fieldName = "number" ;
                break ;
            case "CkReserveReporting": //仓库每日储量上报
                eruptClass = CkReserveReporting.class ;
                fieldName = "yardNum" ;
                break ;
            case "DcReserveReporting"://堆场每日储量上报
                eruptClass = DcReserveReporting.class ;
                fieldName = "yardNum" ;
                break ;
            case "EmployeeInformation"://员工信息
                //查出员工证书子表，再存到主表中，以免删除子表数据
                Long id = data.get("id").getAsLong();
                List<EmployeeCertificate> employeeCertificates = eruptDao.queryEntityList(EmployeeCertificate.class, "employee_information_id= " + id);
                data.add("certificates",gson.toJsonTree(employeeCertificates));
                break;
            case "EnterpriseInfo"://企业信息
                //查出企业附证子表，再存到主表中，以免删除子表数据
//                List<EnterpriseCertificate> enterpriseCertificates = eruptDao.queryEntityList(EnterpriseCertificate.class, "enterprise_id= " + id);
//                data.add("certificates",gson.toJsonTree(enterpriseCertificates));
                String orgCode = data.get("orgCode").getAsString();
                Map<String, Object> map = gson.fromJson(gson.toJson(data),Map.class);
                EnterpriseInfo enterpriseInfo1 = EruptDaoUtils.cast(map, EnterpriseInfo.class);

                EnterpriseInfo enterpriseInfo = eruptDao.queryEntity(EnterpriseInfo.class, " org_code = " + SqlUtils.wrapStr(orgCode));
                enterpriseInfo.setPhone(enterpriseInfo1.getPhone());
                enterpriseInfo.setType(enterpriseInfo1.getType());
                enterpriseInfo.setPortArea(enterpriseInfo1.getPortArea());
                enterpriseInfo.setRepresentative(enterpriseInfo1.getRepresentative());
                enterpriseInfo.setSocialCode(enterpriseInfo1.getSocialCode());
                enterpriseInfo.setTermOperation(enterpriseInfo1.getTermOperation());
                enterpriseInfo.setRegisteredAddress(enterpriseInfo1.getRegisteredAddress());
                enterpriseInfo.setBusinessScope(enterpriseInfo1.getBusinessScope());
                enterpriseInfo.setPortNo(enterpriseInfo1.getPortNo());
                enterpriseInfo.setValidityPeriod(enterpriseInfo1.getValidityPeriod());

                return gson.toJsonTree(enterpriseInfo).getAsJsonObject() ;
            default:
                break;
        }
        if(null != eruptClass){
            Object o = eruptDao.queryEntity(eruptClass, fieldName + " = " + data.get(fieldName).getAsLong());
            if(null == o){
                NotifyUtils.showWarnMsg("无该数据");
            }
            Long id = ((DataAuthModel)o).getId();
            data.addProperty("id",id);
        }
        return null ;

    }

    public EruptApiModel afterAddHandle(String erupt,Object data,JsonObject jsonObject){
        EruptApiModel eruptApiModel = null ;
        switch (erupt) {

            case "Standardization": //标准化管理第一年,过来的是已经上报的数据，在上报按钮中，会生成第二年上报的数据
                Standardization standardization = (Standardization) data;
                List<Standardization> list = new ArrayList<>();
                list.add(standardization);
                standardizationEscalationHandler.exec(list,null,null);

                List<StandardizationSenond> standardizationSenonds = eruptDao.queryEntityList(StandardizationSenond.class, "tb_standardization_id = " + standardization.getId());
                StandardizationSenond standardizationSenond1 = standardizationSenonds.get(0);
                List<StandardizationScoreSecond> scores = eruptDao.queryEntityList(StandardizationScoreSecond.class, "standard_id = "+standardizationSenond1.getId());
                standardizationSenond1.setScores(scores);
                Map<String,Object> returnMap = new HashMap<>();
                returnMap.put("id",standardization.getId());
                returnMap.put("StandardizationSenond",standardizationSenond1);
                eruptApiModel = EruptApiModel.successApi(returnMap);
                break ;
            case "EmergencyDuty"://应急值守管理 有子表，需返回子表的数据
                EmergencyDuty emergencyDuty = (EmergencyDuty) data;
                eruptApiModel = EruptApiModel.successApi(emergencyDuty);
                break;
            case "EmployeeCertificate":
                long employeeInformationId = jsonObject.get("employeeInformationId").getAsLong();
                EmployeeCertificate employeeCertificate = (EmployeeCertificate) data;
                Long id = employeeCertificate.getId();
                String sql = "update tb_employee_certificate set employee_information_id = "+employeeInformationId + " where id="+id;
                eruptDao.getJdbcTemplate().execute(sql);
                break ;
            default:
                break;
        }
        return eruptApiModel ;

    }

    public EruptApiModel afterUpdateHandle(String erupt,Object data){
        EruptApiModel eruptApiModel = null ;
        switch (erupt) {
            case "StandardizationSenond": //标准化管理第二年
                StandardizationSenond standardizationSenond = (StandardizationSenond)data;
                List<StandardizationSenond> list1 = new ArrayList<>();
                list1.add(standardizationSenond);
                standardizationEscalationSecondHandler.exec(list1,null,null);

                StandardizationSenond standardizationSenond1 = eruptDao.queryEntity(StandardizationSenond.class, "id = " + standardizationSenond.getId());
                StandardizationThird standardizationThird = eruptDao.queryEntity(StandardizationThird.class, "tb_standardization_id = " + standardizationSenond1.getStandardization().getId());
                List<StandardizationScoreThird> scores = eruptDao.queryEntityList(StandardizationScoreThird.class, "standard_id = "+standardizationThird.getId());
                standardizationThird.setScores(scores);
                Map<String,Object> returnMap = new HashMap<>();
                returnMap.put("id",standardizationSenond.getId());
                returnMap.put("StandardizationThird",standardizationThird);
                eruptApiModel =  EruptApiModel.successApi(returnMap);
                break ;
            case "EmergencyDuty"://应急值守管理 有子表，需返回子表的数据
                EmergencyDuty emergencyDuty = (EmergencyDuty) data;
                eruptApiModel = EruptApiModel.successApi(emergencyDuty);
            default:
                break;
        }
        return eruptApiModel ;
    }


    /**
     * 执行 addBehavior
     * @param data
     */
    public void addBehavior(String erupt,JsonObject data) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd"); // 指定日期格式
        Gson gson = new Gson();
        try {
            Map<String, Object> map = eruptDataController.initEruptValues(erupt, EruptDaoUtils.castMap(data));
            Set<String> keySet = map.keySet();
            if(!keySet.isEmpty()){
                keySet.forEach(key ->{
                    Object o = map.get(key);
                    if(o instanceof  Date){
                        String format = sdf.format(o);
                        JsonElement jsonElement = gson.toJsonTree(format);
                        data.add(key,jsonElement);
                    }else {
                        JsonElement jsonElement = gson.toJsonTree(o);
                        data.add(key,jsonElement);
                    }


                });
            }
        } catch (IllegalAccessException e) {

            throw new RuntimeException(e);
        } catch (InstantiationException e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 增加日志
     * @param erupt
     * @param data
     * @param operate
     * @return
     */
    @Transactional
    public ZhihuiyunDataSync addLogData(String erupt,  JsonObject data,String operate){
        ZhihuiyunDataSync zhihuiyunDataSync = new ZhihuiyunDataSync();
        zhihuiyunDataSync.setBeforeData(data.toString());
        zhihuiyunDataSync.setEruptName(erupt);
        zhihuiyunDataSync.setOperate(operate);
        dataAuthVoProxy.beforeAdd(zhihuiyunDataSync);
        eruptDao.persist(zhihuiyunDataSync);
        return zhihuiyunDataSync;
    }

    /**
     * 日志 增加结果和入库的数据
     * @param zhihuiyunDataSync
     * @param obj
     * @param result
     */
    @Transactional
    public void modifyLogData(ZhihuiyunDataSync zhihuiyunDataSync,Object obj,String result){
        try{
            Gson gson = GsonFactory.getGson();
            //更新到表中
            if(obj != null ){
                zhihuiyunDataSync.setAfterData(gson.toJson(obj));
            }
            zhihuiyunDataSync.setResult(result);
            eruptDao.merge(zhihuiyunDataSync) ;
        }catch (Exception e){
            this.modifyLogData(zhihuiyunDataSync,null,e.getMessage());
        }

    }



}
