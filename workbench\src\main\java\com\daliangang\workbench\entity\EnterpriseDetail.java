/*
 * Copyright © 2020-2035 yundingyun All rights reserved.
 */
package com.daliangang.workbench.entity;

import cn.hutool.core.lang.RegexPool;
import com.daliangang.core.DaliangangContext;
import com.daliangang.workbench.handler.DepartmentViewRenderHandler;
import com.daliangang.workbench.operation.EnterpriseDetailOperationHandler;
import com.daliangang.workbench.operation.EnterpriseImportAttachedCertificateHandler;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import xyz.erupt.annotation.Erupt;
import xyz.erupt.annotation.EruptField;
import xyz.erupt.annotation.config.EruptSmartSkipSerialize;
import xyz.erupt.annotation.expr.ExprBool;
import xyz.erupt.annotation.fun.DataProxy;
import xyz.erupt.annotation.sub_erupt.Power;
import xyz.erupt.annotation.sub_erupt.RowOperation;
import xyz.erupt.annotation.sub_field.Edit;
import xyz.erupt.annotation.sub_field.EditType;
import xyz.erupt.annotation.sub_field.Readonly;
import xyz.erupt.annotation.sub_field.View;
import xyz.erupt.annotation.sub_field.sub_edit.*;
import xyz.erupt.toolkit.db.Comment;
import xyz.erupt.toolkit.handler.EruptDictCodeChoiceFetchHandler;
import xyz.erupt.toolkit.handler.SqlChoiceFetchHandler;
import xyz.erupt.upms.model.auth.DataAuthModel;
import xyz.erupt.upms.model.template.EruptRoleTemplate;

import javax.persistence.*;
import java.util.Set;

@Erupt(
        name = "企业信息",  //importTruncate = true,
        power = @Power(add = false,delete = false,edit = false),
        dataProxy = EnterpriseDetail.class, rowOperation = {
        @RowOperation(
                confirm = false,
                title = "导入附证",
                icon = "fa fa-arrow-down",
                operationHandler = EnterpriseImportAttachedCertificateHandler.class,
                mode = RowOperation.Mode.BUTTON
        ),
//        @RowOperation(
//                title = "导出附证",
//                icon = "fa fa-arrow-up",
//                operationHandler = EnterpriseExportAttachedCertificateHandler.class,
//                mode = RowOperation.Mode.BUTTON
//        ),
        @RowOperation(
                title = "修改信息",
                icon = "fa fa-edit",
                operationHandler = EnterpriseDetailOperationHandler.class,
                mode = RowOperation.Mode.SINGLE
        ),
})
@Table(name = "tb_enterprise", uniqueConstraints = @UniqueConstraint(columnNames = "name"))
@Entity
@Getter
@Setter
@Comment("企业列表")
@ApiModel("企业列表")
//@SystemExcelTemplate(erupt = Enterprise.class, template = "企业列表-模板.xls")
@Slf4j
public class EnterpriseDetail extends DataAuthModel implements DataProxy<EnterpriseDetail> {

    @EruptField(
            views = @View(title = "状态", show = true),
            edit = @Edit(title = "状态", readonly = @Readonly(exprHandler = Enterprise.class), type = EditType.BOOLEAN, show = false,
                    boolType = @BoolType(trueText = "启用", falseText = "禁用")))
    @Comment("状态")
    @ApiModelProperty("状态")
    private Boolean state = true;

    @EruptField(
            views = @View(title = "企业名称"),
            edit = @Edit(title = "企业名称", type = EditType.INPUT, notNull = true,
                    readonly = @Readonly(exprHandler = Enterprise.class), inputType = @InputType))
    @Comment("企业名称")
    @ApiModelProperty("企业名称")
    private String name;

    @EruptField(
            views = @View(title = "管理员用户名", show = false),
            edit = @Edit(title = "管理员用户名", type = EditType.INPUT, notNull = true,
                    readonly = @Readonly(exprHandler = Enterprise.class), inputType = @InputType))
    @Comment("管理员用户名")
    @ApiModelProperty("管理员用户名")
    private String administrator;

    @EruptField(
            views = @View(title = "电话号码", show = false),
            edit = @Edit(title = "电话号码", type = EditType.INPUT, notNull = true,
                    inputType = @InputType(regex= RegexPool.MOBILE)))
    @Comment("电话号码")
    @ApiModelProperty("电话号码")
    private String phone;

    @EruptField(
            views = @View(title = "企业类别"),
            edit = @Edit(title = "企业类别", type = EditType.CHOICE, notNull = true,
                    choiceType = @ChoiceType(fetchHandler = EruptDictCodeChoiceFetchHandler.class, fetchHandlerParams = "type")))
    @Comment("企业类别")
    @ApiModelProperty("企业类别")
    private String type;

    @EruptField(
            views = @View(title = "角色", column = "name", ifRender = @ExprBool(value = false)),
            edit = @Edit(title = "角色", type = EditType.REFERENCE_TREE, notNull = true,
//                    readonly = @Readonly(exprHandler = Enterprise.DepartmentReadOnlyHandler.class)
                    /*(exprHandler = Enterprise.class)*/
                    readonly = @Readonly,
                    referenceTreeType = @ReferenceTreeType(id = "id", label = "name")))
    @ManyToOne
    @Comment("角色")
    @ApiModelProperty("角色")
    private EruptRoleTemplate role;

    @EruptField(
            views = @View(title = "所属港区",show = true /**ifRender = @ExprBool(exprHandler = DepartmentViewRenderHandler.class)**/),
            edit = @Edit(title = "所属港区", type = EditType.CHOICE,  notNull = true,
                    choiceType = @ChoiceType(fetchHandler = SqlChoiceFetchHandler.class, fetchHandlerParams = {"select id as code ,portarea_name as name from tb_port_area"})))
//                    choiceType = @ChoiceType(fetchHandler = EruptDictCodeChoiceFetchHandler.class, fetchHandlerParams = "portArea")))
    @Comment("所属港区")
    @ApiModelProperty("所属港区")
    private Long portArea;

    @EruptField(
            views = @View(title = "法定代表人", ifRender = @ExprBool(exprHandler = DepartmentViewRenderHandler.class)),
            edit = @Edit(title = "法定代表人", type = EditType.INPUT,
                    inputType = @InputType))
    @Comment("法定代表人")
    @ApiModelProperty("法定代表人")
    private String representative;

    @EruptField(
            views = @View(title = "统一社会信用代码", ifRender = @ExprBool(exprHandler = DepartmentViewRenderHandler.class)),
            edit = @Edit(title = "统一社会信用代码", type = EditType.INPUT,
                    inputType = @InputType))
    @Comment("统一社会信用代码")
    @ApiModelProperty("统一社会信用代码")
    private String socialCode;

    @EruptField(
            views = @View(title = "营业期限", ifRender = @ExprBool(exprHandler = DepartmentViewRenderHandler.class)),
            edit = @Edit(title = "营业期限", type = EditType.INPUT,
                    inputType = @InputType))
    @Comment("营业期限")
    @ApiModelProperty("营业期限")
    private String termOperation;

    @EruptField(
            views = @View(title = "注册地址", ifRender = @ExprBool(exprHandler = DepartmentViewRenderHandler.class)),
            edit = @Edit(title = "注册地址", type = EditType.INPUT,
                    inputType = @InputType(fullSpan = true)))
    @Comment("注册地址")
    @ApiModelProperty("注册地址")
    private String registeredAddress;

    @EruptField(
            views = @View(title = "经营范围", ifRender = @ExprBool(exprHandler = DepartmentViewRenderHandler.class)),
            edit = @Edit(title = "经营范围", type = EditType.INPUT,
                    inputType = @InputType))
    @Comment("经营范围")
    @ApiModelProperty("经营范围")
    private String businessScope;

    @EruptField(
            views = @View(title = "港经证编号", ifRender = @ExprBool(exprHandler = DepartmentViewRenderHandler.class)),
            edit = @Edit(title = "港经证编号", type = EditType.INPUT, notNull = true,
                    inputType = @InputType))
    @Comment("港经证编号")
    @ApiModelProperty("港经证编号")
    private String portNo;

    @EruptField(
            views = @View(title = "有效期限", ifRender = @ExprBool(exprHandler = DepartmentViewRenderHandler.class)),
            edit = @Edit(title = "有效期限", type = EditType.DATE, notNull = true,
                    dateType = @DateType))
    @Comment("有效期限")
    @ApiModelProperty("有效期限")
    private java.util.Date validityPeriod;

//    @EruptField(
//            edit = @Edit(title = "企业附证", type = EditType.TAB_TABLE_ADD))
//    @OneToMany(cascade = CascadeType.ALL, orphanRemoval = true)
//    @OrderBy
//    @JoinColumn(name = "enterprise_id")
//    @Comment("企业附证")
//    @ApiModelProperty("企业附证")
//    private Set<EnterpriseCertificate> certificates;

    @EruptField(
            views = @View(title = "地图", show = false, ifRender = @ExprBool(value = false)),
            edit = @Edit(title = "地图", type = EditType.MAP, show = false))
    @Comment("地图")
    @ApiModelProperty("地图")
    @Lob
    private String map;

    @EruptField(views = @View(title = "组织编码", show = false, ifRender = @ExprBool(exprHandler = DepartmentViewRenderHandler.class)), edit = @Edit(title = "组织编码", show = false))
    @EruptSmartSkipSerialize
    private String orgCode;

    @EruptField(views = @View(title = "初始组织编码", show = false,
            ifRender = @ExprBool(exprHandler = DepartmentViewRenderHandler.class)),
            edit = @Edit(title = "初始组织编码", show = false))
    private String initOrgCode;

    @EruptField(
            views = @View(title = "主管部门", column = "name", show = true, ifRender = @ExprBool(value = true)),
            edit = @Edit(title = "主管部门", readonly = @Readonly(exprHandler = DepartmentReadOnlyHandler.class), type = EditType.REFERENCE_TREE, notNull = true,
                    referenceTreeType = @ReferenceTreeType(pid = "parent.id", label = "name")))
    @ManyToOne
    @Comment("主管部门")
    @ApiModelProperty("主管部门")
    private Department imDepartment;

    @EruptField(
            edit = @Edit(title = "企业附证", type = EditType.TAB_TABLE_ADD))
    @OneToMany(cascade = CascadeType.ALL, orphanRemoval = true)
    @OrderBy
    @JoinColumn(name = "enterprise_id")
    @Comment("企业附证")
    @ApiModelProperty("企业附证")
    private Set<EnterpriseCertificate> certificates;

    @Service
    public static class DepartmentReadOnlyHandler implements Readonly.ReadonlyHandler {

        @Override
        public boolean add(boolean add, String[] params) {

            return this.edit(add, params);
        }

        @Override
        public boolean edit(boolean edit, String[] params) {
            return !DaliangangContext.isDepartmentAdmin();
        }
    }
}
