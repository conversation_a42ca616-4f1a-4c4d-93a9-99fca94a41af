package com.daliangang.majorisk.operation;

import com.daliangang.majorisk.entity.FiveChecklistsFoundation;
import com.daliangang.majorisk.entity.FiveChecklistsPreventionAndControl;
import com.daliangang.majorisk.entity.FiveChecklistsResponsibility;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import xyz.erupt.annotation.fun.OperationHandler;
import xyz.erupt.core.annotation.EruptRouter;
import xyz.erupt.core.util.EruptSpringUtil;
import xyz.erupt.toolkit.db.EruptResultMap;
import xyz.erupt.toolkit.utils.EruptDaoUtils;

import javax.transaction.Transactional;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since :2023/4/15:10:40
 */
@Service
public class FiveChecklistsResponsibilityHandler implements OperationHandler<FiveChecklistsFoundation, FiveChecklistsResponsibility> {

    @Override
    public String exec(List<FiveChecklistsFoundation> data, FiveChecklistsResponsibility fiveChecklistsResponsibility, String[] param) {
        return null;
    }

    public Set<FiveChecklistsResponsibility> detail(Long id) {
        SendMessageController messageController = EruptSpringUtil.getBean(SendMessageController.class);
        Set<FiveChecklistsResponsibility> fiveChecklistsResponsibilities = messageController.initValue(id);
        return fiveChecklistsResponsibilities;
    }

    @RestController
    @Transactional
    public static class SendMessageController {


        @RequestMapping("/erupt-api/data/FiveChecklistsResponsibilitys/{id}")
        @EruptRouter(verifyType = EruptRouter.VerifyType.LOGIN)
        public Set<FiveChecklistsResponsibility> initValue(@PathVariable("id") Long id) {
            List<FiveChecklistsResponsibility> fiveChecklistsResponsibilities = EruptDaoUtils.selectOnes("select * from tb_fivechecklists_responsibility where tb_five_detail_id=" + id, FiveChecklistsResponsibility.class);
            Set<FiveChecklistsResponsibility> collect = fiveChecklistsResponsibilities.stream().collect(Collectors.toSet());
            return collect;
        }
    }
}
