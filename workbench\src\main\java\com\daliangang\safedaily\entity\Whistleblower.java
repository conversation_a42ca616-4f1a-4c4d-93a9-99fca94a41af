package com.daliangang.safedaily.entity;

import com.daliangang.core.CompanyRenderHandler;
import com.daliangang.safedaily.operation.ButtonShowHandler;
import com.daliangang.safedaily.operation.DepartPowerHandler;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import xyz.erupt.annotation.Erupt;
import xyz.erupt.annotation.EruptField;
import xyz.erupt.annotation.expr.ExprBool;
import xyz.erupt.annotation.fun.DataProxy;
import xyz.erupt.annotation.query.Condition;
import xyz.erupt.annotation.sub_erupt.Power;
import xyz.erupt.annotation.sub_erupt.RowOperation;
import xyz.erupt.annotation.sub_field.Edit;
import xyz.erupt.annotation.sub_field.EditType;
import xyz.erupt.annotation.sub_field.Readonly;
import xyz.erupt.annotation.sub_field.View;
import xyz.erupt.annotation.sub_field.sub_edit.*;
import xyz.erupt.toolkit.db.Comment;
import xyz.erupt.toolkit.handler.DelOperationHandler;
import xyz.erupt.toolkit.handler.EditOperationHandler;
import xyz.erupt.toolkit.handler.EruptDictCodeChoiceFetchHandler;
import xyz.erupt.toolkit.handler.RemoteEntityChoiceFetchHandler;
import xyz.erupt.toolkit.submit.CheckSubmit;
import xyz.erupt.toolkit.utils.TplUtils;
import xyz.erupt.upms.model.auth.DataAuthHandler;
import xyz.erupt.upms.model.auth.DataAuthModel;

import javax.persistence.Entity;
import javax.persistence.Table;
import java.util.Date;
import java.util.List;

/**
 * @Title: Whistleblower
 * <AUTHOR>
 * @Package com.daliangang.safedaily.entity
 * @Date 2024/3/7 14:17
 * @description: 安全吹哨人管理
 */
@Erupt(name = "安全吹哨人管理",
        power = @Power(powerHandler = DepartPowerHandler.class),
        dataProxy = Whistleblower.class,
        orderBy = "Whistleblower.updateTime desc",
        rowOperation = {
//                @RowOperation(
//                        title = "上报",
//                        icon = "fa fa-arrow-circle-o-up",
//                        operationHandler = WhistleblowerEscalationHandler.class,
//                        ifExpr = "item.submitted=='未上报'",
//                        mode = RowOperation.Mode.SINGLE,
//                        show = @ExprBool(exprHandler = ButtonShowHandler.class)
//                ),
                @RowOperation(
                        title = "编辑",
                        icon = "fa fa-edit",
                        code = TplUtils.EDIT_OPER_CODE,
                        eruptClass = Whistleblower.class,
                        operationHandler = EditOperationHandler.class,
//                        ifExpr = "item.submitted=='未上报'",
                        ifExprBehavior = RowOperation.IfExprBehavior.HIDE,
                        mode = RowOperation.Mode.SINGLE,
                        show = @ExprBool(exprHandler = ButtonShowHandler.class)
                ),
                @RowOperation(
                        title = "删除",
                        icon = "fa fa-trash-o",
                        operationHandler = DelOperationHandler.class,
//                        ifExpr = "item.submitted=='未上报'",
                        ifExprBehavior = RowOperation.IfExprBehavior.HIDE,
                        mode = RowOperation.Mode.SINGLE,
                        show = @ExprBool(exprHandler = ButtonShowHandler.class)
                )
        })
@Table(name = "tb_whist_blower_management")
@Entity
@Getter
@Setter
@Comment("安全吹哨人管理")
public class Whistleblower extends DataAuthModel implements DataProxy<Whistleblower> {

    @EruptField(
            views = @View(title = "单位名称",width = "260px"),
            edit = @Edit(title = "单位名称",search = @Search(ifRender = @ExprBool(exprHandler = CompanyRenderHandler.class)), readonly = @Readonly(exprHandler = DataAuthHandler.class), type = EditType.CHOICE, notNull = true,
                    choiceType = @ChoiceType(fullSpan = true,fetchHandler = RemoteEntityChoiceFetchHandler.class, fetchHandlerParams = {"main", "Enterprise", "org_code,name"}, reload = true)))
    @Comment("单位名称")
    @ApiModelProperty("单位名称")
    private String company;

    @EruptField(
            views = @View(title = "姓名",width = "150px"),
            edit = @Edit(title = "姓名", type = EditType.INPUT, notNull = true,
                    inputType = @InputType))
    @Comment("姓名")
    @ApiModelProperty("姓名")
    private String name;

    @EruptField(
            views = @View(title = "责任区域",width = "150px"),
            edit = @Edit(title = "责任区域", type = EditType.TEXTAREA, notNull = true))
    @Comment("责任区域")
    @ApiModelProperty("责任区域")
    private String areaOfResponsibility;

    @EruptField(
            views = @View(title = "学历",width = "100px"),
            edit = @Edit(title = "学历", type = EditType.CHOICE, search = @Search, notNull = true,
                    choiceType = @ChoiceType(fetchHandler = EruptDictCodeChoiceFetchHandler.class, fetchHandlerParams = "educational")))
    @Comment("学历")
    @ApiModelProperty("学历")
    private String educational;

    @EruptField(
            views = @View(title = "职称/职务",width = "150px"),
            edit = @Edit(title = "职称/职务", type = EditType.INPUT, notNull = true,
                    inputType = @InputType))
    @Comment("职称/职务")
    @ApiModelProperty("职称/职务")
    private String duty;

    @EruptField(
            views = @View(title = "上报时间",width = "100px",show = false),
            edit = @Edit(title = "上报时间", type = EditType.DATE, search = @Search, show = false,
                    dateType = @DateType))
    @Comment("上报时间")
    @ApiModelProperty("上报时间")
    private java.util.Date fillingDate;

    @EruptField(
            views = @View(title = "上报状态",width = "100px",show = false),
            edit = @Edit(title = "上报状态", type = EditType.BOOLEAN, show = false,
                    boolType = @BoolType(trueText = "已上报", falseText = "未上报")))
    @CheckSubmit(linkProperty = "createTime", checkTimeDiffPeriod = 1)
    @Comment("上报状态")
    @ApiModelProperty("上报状态")
    private Boolean submitted;

    @Override
    public void addBehavior(Whistleblower whistleblower) {
//        whistleblower.setSubmitted(Boolean.FALSE);
        whistleblower.setSubmitted(Boolean.TRUE);
        whistleblower.setFillingDate(new Date());
    }

    @Override
    public String beforeFetch(List<Condition> conditions) {
//        if (DaliangangContext.isDepartmentUser()) return "submitted=true";
        return DataProxy.super.beforeFetch(conditions);
    }
}
