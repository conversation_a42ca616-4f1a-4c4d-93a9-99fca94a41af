/*
 * Copyright © 2020-2035 yundingyun All rights reserved.
 */
package com.daliangang.emergency.entity;

import com.daliangang.emergency.proxy.HospitalDataProxy;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import xyz.erupt.annotation.Erupt;
import xyz.erupt.annotation.EruptField;
import xyz.erupt.annotation.sub_erupt.Power;
import xyz.erupt.annotation.sub_field.Edit;
import xyz.erupt.annotation.sub_field.EditType;
import xyz.erupt.annotation.sub_field.View;
import xyz.erupt.annotation.sub_field.sub_edit.BoolType;
import xyz.erupt.annotation.sub_field.sub_edit.InputType;
import xyz.erupt.annotation.sub_field.sub_edit.Search;
import xyz.erupt.jpa.model.MetaModel;
import xyz.erupt.toolkit.db.Comment;

import javax.persistence.Entity;
import javax.persistence.Lob;
import javax.persistence.Table;

@Erupt(name = "医院管理",importTruncate = true, power = @Power(add = true, delete = true, export = false, importable = true, viewDetails = true, edit = true)
		, dataProxy = HospitalDataProxy.class
		, rowOperation = {})
@Table(name = "tb_hospital")
@Entity
@Getter
@Setter
@Comment("医院管理")
@ApiModel("医院管理")
public class Hospital extends MetaModel {
	@EruptField(
			views = @View(title = "医院名称"),
			edit = @Edit(title = "医院名称", type = EditType.INPUT, search = @Search(vague = true), notNull = true,
					inputType = @InputType))
	@Comment("医院名称")
	@ApiModelProperty("医院名称")
	private String hospitalName;

	@EruptField(
			views = @View(title = "医院级别"),
			edit = @Edit(title = "医院级别", type = EditType.INPUT, search = @Search(vague = true), notNull = true,
					inputType = @InputType))
	@Comment("医院级别")
	@ApiModelProperty("医院级别")
	private String hospitalLevel;

	@EruptField(
			views = @View(title = "医院性质"),
			edit = @Edit(title = "医院性质", type = EditType.BOOLEAN, notNull = true,search = @Search,
					boolType = @BoolType(trueText = "公立", falseText = "私立")))
	@Comment("医院性质")
	@ApiModelProperty("医院性质")
	private Boolean property;

	@EruptField(
			views = @View(title = "联系电话"),
			edit = @Edit(title = "联系电话", type = EditType.INPUT, notNull = true))
	@Comment("联系电话")
	@ApiModelProperty("联系电话")
	private String contactNumber;

	@EruptField(
			views = @View(title = "规模与专长"),
			edit = @Edit(title = "规模与专长", type = EditType.TEXTAREA, notNull = true))
	@Comment("规模与专长")
	@ApiModelProperty("规模与专长")
	private @Lob String scale;

	@EruptField(
			views = @View(title = "地址"),
			edit = @Edit(title = "地址", type = EditType.INPUT, notNull = true,
					inputType = @InputType))
	@Comment("地址")
	@ApiModelProperty("地址")
	private String address;


	@EruptField(
			views = @View(title = "地图", show = false),
			edit = @Edit(title = "地图", type = EditType.MAP, show = true))
	@Comment("地图")
	@ApiModelProperty("地图")
	@Lob private String map;

}
