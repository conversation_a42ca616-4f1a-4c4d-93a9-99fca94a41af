package com.daliangang.training.service;

import com.daliangang.training.models.UserSyncEntity;
import com.daliangang.workbench.entity.EmployeeInformation;
import org.springframework.stereotype.Service;
import xyz.erupt.core.view.EruptApiModel;
import xyz.erupt.job.handler.EruptJobHandler;
import xyz.erupt.jpa.dao.EruptDao;
import xyz.erupt.toolkit.redismq.RedisMQService;
import xyz.erupt.upms.model.EruptUser;
import xyz.erupt.upms.model.template.EruptRoleTemplateUser;


import javax.annotation.Resource;
import java.util.List;

/**
 * @Author: chongmenglin
 * @Date: 2024/8/20 15:58
 */
@Service
public class UserDeptJobHandler implements EruptJobHandler {
    @Resource
    private RedisMQService redisMQService;

    @Resource
    private EruptDao eruptDao;

    @Override
    public String exec(String code, String param) {
        //同步全部的员工 带岗位
        List<EmployeeInformation> users = eruptDao.queryEntityList(EmployeeInformation.class);
        for (EmployeeInformation user : users) {
            String userSql = " account = '" + user.getPhone() + "'";
            EruptUser userEmployee = eruptDao.queryEntity(EruptUser.class, userSql);
            if(userEmployee == null)
                continue;
            UserSyncEntity userSync = UserSyncEntity.builder()
                    .id(userEmployee.getId())
                    .name(user.getName())
                    .orgCode(user.getOrgCode())
                    .phone(user.getPhone())
                    .sex(user.getSex())
                    .state(user.getState())
                    .posts(user.getJobTitle())
                    .manager(user.getManager())
                    .templateId(4L)
                    .posts(user.getJobTitle())
                    .code(user.getDepartment().getCode())
                    .department(user.getDepartment().getName())
                    .updateFlag(true)
                    .delFlag(false)
                    .build();
            redisMQService.produce("REDIS_MQ_USER_DEPT", userSync);
        }
        return "同步成功";
    }
}
