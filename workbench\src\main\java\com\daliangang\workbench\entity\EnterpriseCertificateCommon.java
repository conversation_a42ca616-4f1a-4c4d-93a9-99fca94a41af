package com.daliangang.workbench.entity;


import com.daliangang.core.CompanyRenderHandler;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import xyz.erupt.annotation.EruptField;
import xyz.erupt.annotation.expr.ExprBool;
import xyz.erupt.annotation.sub_field.Edit;
import xyz.erupt.annotation.sub_field.EditType;
import xyz.erupt.annotation.sub_field.Readonly;
import xyz.erupt.annotation.sub_field.View;
import xyz.erupt.annotation.sub_field.sub_edit.AttachmentType;
import xyz.erupt.annotation.sub_field.sub_edit.ChoiceType;
import xyz.erupt.annotation.sub_field.sub_edit.DateType;
import xyz.erupt.annotation.sub_field.sub_edit.InputType;
import xyz.erupt.jpa.model.MetaModel;
import xyz.erupt.toolkit.db.Comment;
import xyz.erupt.toolkit.handler.SqlChoiceFetchHandler;
import xyz.erupt.upms.model.auth.DataAuthHandler;

import javax.persistence.Lob;
import javax.persistence.MappedSuperclass;

@Getter
@Setter
@MappedSuperclass
public class EnterpriseCertificateCommon extends MetaModel {

    @EruptField(
            views = @View(title = "企业名称",ifRender = @ExprBool(exprHandler = CompanyRenderHandler.class)),
            edit = @Edit(title = "企业名称", ifRender = @ExprBool(exprHandler = CompanyRenderHandler.class), readonly = @Readonly(exprHandler = DataAuthHandler.class), type = EditType.CHOICE, //notNull = true,
                    choiceType = @ChoiceType(fetchHandler = SqlChoiceFetchHandler.class, fetchHandlerParams = "select org_code,name from tb_enterprise")))
    @Comment("企业名称")
    @ApiModelProperty("企业名称")
    private String company;

    @EruptField(
            views = @View(title = "附证编号"),
            edit = @Edit(title = "附证编号", type = EditType.INPUT, notNull = true,
                    inputType = @InputType))
    @Comment("附证编号")
    @ApiModelProperty("附证编号")
    private String certificateNo;

    @EruptField(
            views = @View(title = "状态",width = "100px"),
            edit = @Edit(title = "状态",
//                    type = EditType.CHOICE,
                    notNull = false,
                    show = false
//                    choiceType = @ChoiceType(fullSpan = true, fetchHandler = EruptDictCodeChoiceFetchHandler.class, fetchHandlerParams = "expiredState")
            )
    )
    @Comment("状态")
    @ApiModelProperty("状态")
    private String state;

    @EruptField(
            views = @View(title = "作业范围"),
            edit = @Edit(title = "作业范围", type = EditType.INPUT, notNull = true,
                    inputType = @InputType))
    @Comment("作业范围")
    @ApiModelProperty("作业范围")
    private String workRange;

    @EruptField(
            views = @View(title = "作业方式"),
            edit = @Edit(title = "作业方式", type = EditType.INPUT, notNull = true,
                    inputType = @InputType))
    @Comment("作业方式")
    @ApiModelProperty("作业方式")
    private String operationMode;

    //	@EruptField(
//		views = @View(title = "货种名称"),
//		edit = @Edit(title = "货种名称", type = EditType.TAGS, notNull = true,
//		tagsType = @TagsType))
//	@Comment("货种名称")
//	@ApiModelProperty("货种名称")
//	private String goodsType;
    @EruptField(
            views = @View(title = "货种名称"),
            edit = @Edit(
                    title = "货种名称",
                    type = EditType.INPUT,
                    notNull = true
            )
    )
    @Comment("货种名称")
    @ApiModelProperty("货种名称")
    @Lob
    private String goodsType;

    @EruptField(
            views = @View(title = "发证日期"),
            edit = @Edit(title = "发证日期", type = EditType.DATE, notNull = true,
                    dateType = @DateType))
    @Comment("发证日期")
    @ApiModelProperty("发证日期")
    private java.util.Date issueDate;

    @EruptField(
            views = @View(title = "发证机关"),
            edit = @Edit(title = "发证机关", type = EditType.INPUT, notNull = true,
                    inputType = @InputType))
    @Comment("发证机关")
    @ApiModelProperty("发证机关")
    private String issuingAuthority;

    @EruptField(
            views = @View(title = "到期时间"),
            edit = @Edit(title = "到期时间", type = EditType.DATE, notNull = true,
                    dateType = @DateType))
    @Comment("到期时间")
    @ApiModelProperty("到期时间")
    private java.util.Date expirationTime;

    @Lob
    @EruptField(
            views = @View(title = "附证扫描件", show = false),
            edit = @Edit(title = "附证扫描件", type = EditType.ATTACHMENT, notNull = false,
                    attachmentType = @AttachmentType(type = AttachmentType.Type.BASE, maxLimit = 20)))
    @Comment("附证扫描件")
    @ApiModelProperty("附证扫描件")
    private String scannedFile ;
}
