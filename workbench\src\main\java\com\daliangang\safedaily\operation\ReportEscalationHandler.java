/*
* Copyright © 2020-2035 yundingyun All rights reserved.
*/
package com.daliangang.safedaily.operation;

import cn.hutool.json.JSONObject;
import com.daliangang.safedaily.entity.Report;
import org.springframework.stereotype.Service;
import xyz.erupt.annotation.fun.OperationHandler;
import xyz.erupt.common.http.EruptRemoteUtils;
import xyz.erupt.jpa.dao.EruptDao;
import xyz.erupt.toolkit.remote.RemoteProxyService;
import xyz.erupt.toolkit.utils.NotifyUtils;

import javax.annotation.Resource;
import javax.transaction.Transactional;
import java.util.List;
import java.util.Map;

@Service
public class ReportEscalationHandler implements OperationHandler<Report, Void> {
    @Resource
    private RemoteProxyService remoteProxyService;
    @Resource
    private EruptDao eruptDao;

   @Override
   @Transactional
   public String exec(List<Report> data, Void unused, String[] param) {
      data.forEach(v->{
          v.setIsReport(true);
          v.setSubmitted(true);
          eruptDao.merge(v);

          JSONObject inputData = new JSONObject();
          inputData.set("clazz", "Report");
          inputData.set("insertData",v);
          EruptRemoteUtils.doPost(remoteProxyService.getMainServerUrl() + "/erupt-api/exchange/data/exchange", inputData, Map.class);
      });

       return NotifyUtils.getSuccessNotify("上报成功！");
	}
}
