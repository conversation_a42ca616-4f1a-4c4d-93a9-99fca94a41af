/*
* Copyright © 2020-2035 yundingyun All rights reserved.
*/
package com.daliangang.rndpub.entity;

import com.daliangang.rndpub.operation.ExpertauditByHandler;
import com.daliangang.rndpub.operation.ExpertauditDoNotPassHandler;
import com.daliangang.rndpub.proxy.ExpertauditDataProxy;
import io.swagger.annotations.ApiModel;
import lombok.Getter;
import lombok.Setter;
import xyz.erupt.annotation.Erupt;
import xyz.erupt.annotation.sub_erupt.Filter;
import xyz.erupt.annotation.sub_erupt.Power;
import xyz.erupt.annotation.sub_erupt.RowOperation;
import xyz.erupt.toolkit.db.Comment;

import javax.persistence.Entity;
import javax.persistence.Table;

@Erupt(name = "专家审核", power = @Power(add = false, delete = true, export = false, importable = true, viewDetails = true, edit = true)
//        ,filter = @Filter("Expertaudit.source = false")
, dataProxy = ExpertauditDataProxy.class
, rowOperation = {
//@RowOperation(title = "审核", icon = "fa fa-check", operationHandler = ExpertauditAuditHandler.class, mode = RowOperation.Mode.SINGLE),
@RowOperation(title = "通过", icon = "fa fa-check",  ifExpr = "item.expertAudidtState=='未审核'",operationHandler = ExpertauditByHandler.class, mode = RowOperation.Mode.SINGLE),
@RowOperation(title = "不通过", icon = "fa fa-times", ifExpr = "item.expertAudidtState=='未审核'",operationHandler = ExpertauditDoNotPassHandler.class, mode = RowOperation.Mode.SINGLE)
})
@Table(name = "tb_expert")
@Entity
@Getter
@Setter
@Comment("专家审核")
@ApiModel("专家审核")
public class Expertaudit extends ExpertInfo {
//	@EruptField(
//		views = @View(title = "专家姓名"),
//		edit = @Edit(title = "专家姓名", type = EditType.INPUT, search = @Search(vague = true), notNull = true,
//		inputType = @InputType))
//	@Comment("专家姓名")
//	@ApiModelProperty("专家姓名")
//	private String name;
//
//	@EruptField(
//		views = @View(title = "性别"),
//		edit = @Edit(title = "性别", type = EditType.BOOLEAN, search = @Search(vague = true), notNull = true,
//		boolType = @BoolType(trueText = "男", falseText = "女")))
//	@Comment("性别")
//	@ApiModelProperty("性别")
//	private Boolean sex;
//
//	@EruptField(
//		views = @View(title = "出生日期"),
//		edit = @Edit(title = "出生日期", type = EditType.DATE, notNull = true,
//		dateType = @DateType))
//	@Comment("出生日期")
//	@ApiModelProperty("出生日期")
//	private java.util.Date date;
//
//	@EruptField(
//		views = @View(title = "学历"),
//		edit = @Edit(title = "学历", type = EditType.CHOICE, notNull = true,
//		choiceType = @ChoiceType(fetchHandler = SqlChoiceFetchHandler.class, fetchHandlerParams = "select '测试项','测试项'")))
//	@Comment("学历")
//	@ApiModelProperty("学历")
//	private String educational;
//
//	@EruptField(
//		views = @View(title = "专业类别"),
//		edit = @Edit(title = "专业类别", type = EditType.INPUT, notNull = true,
//		inputType = @InputType))
//	@Comment("专业类别")
//	@ApiModelProperty("专业类别")
//	private String category;
//
//	@EruptField(
//		views = @View(title = "职称"),
//		edit = @Edit(title = "职称", type = EditType.CHOICE, notNull = true,
//		choiceType = @ChoiceType(fetchHandler = SqlChoiceFetchHandler.class, fetchHandlerParams = "select '测试项','测试项'")))
//	@Comment("职称")
//	@ApiModelProperty("职称")
//	private String technical;
//
//	@EruptField(
//		views = @View(title = "企业名称"),
//		edit = @Edit(title = "企业名称",readonly = @Readonly(exprHandler = DataAuthInvoker.class), type = EditType.CHOICE, notNull = true,
//		choiceType = @ChoiceType(fetchHandler = RemoteEntityChoiceFetchHandler.class, fetchHandlerParams ={"main","Enterprise","org_code,name"})))
//	@Comment("企业名称")
//	@ApiModelProperty("企业名称")
//	private String company;
//
//	@EruptField(
//		views = @View(title = "提交时间"),
//		edit = @Edit(title = "提交时间", type = EditType.DATE, notNull = true,
//		dateType = @DateType(type = DateType.Type.DATE_TIME)))
//	@Comment("提交时间")
//	@ApiModelProperty("提交时间")
//	private Timestamp creationTime;
//
//	@EruptField(
//		views = @View(title = "工作年限"),
//		edit = @Edit(title = "工作年限", type = EditType.NUMBER, notNull = true,
//		numberType = @NumberType))
//	@Comment("工作年限")
//	@ApiModelProperty("工作年限")
//	private Integer workYear;
//
//	@EruptField(
//		views = @View(title = "检查范围"),
//		edit = @Edit(title = "检查范围", type = EditType.CHOICE, notNull = true,
//		choiceType = @ChoiceType(fetchHandler = EruptDictCodeChoiceFetchHandler.class, fetchHandlerParams = "checkScope")))
//	@Comment("检查范围")
//	@ApiModelProperty("检查范围")
//	private String checkScope;
//
//	@EruptField(
//		views = @View(title = "审核结果"),
//		edit = @Edit(title = "审核结果", type = EditType.CHOICE, notNull = true,
//		choiceType = @ChoiceType(fetchHandler = EruptDictCodeChoiceFetchHandler.class, fetchHandlerParams = "audit")))
//	@Comment("审核结果")
//	@ApiModelProperty("审核结果")
//	private String audit;
//
//	@EruptField(
//		views = @View(title = "驳回原因"),
//		edit = @Edit(title = "驳回原因", type = EditType.TEXTAREA))
//	@Comment("驳回原因")
//	@ApiModelProperty("驳回原因")
//	private @Lob String reason;
//
//	@EruptField(
//		edit = @Edit(title = "资质证书", type = EditType.TAB_TABLE_ADD, notNull = true))
//	@OneToMany(cascade = CascadeType.ALL, orphanRemoval = true)
//        @OrderBy
//        @JoinColumn(name = "expertaudit_id")
//	@Comment("资质证书")
//	@ApiModelProperty("资质证书")
//	private Set<Auditcertificate> file;

}
