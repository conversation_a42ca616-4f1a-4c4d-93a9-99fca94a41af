/*
 * Copyright © 2020-2035 yundingyun All rights reserved.
 */
package com.daliangang.workbench.entity;

import cn.hutool.core.lang.RegexPool;
import com.daliangang.core.CompanyRenderHandler;
import com.daliangang.core.DaliangangContext;
import com.daliangang.workbench.handler.EmployeeViewCompanyHandler;
import com.daliangang.workbench.proxy.EmployeeInfoJobTitleDataProxy;
import com.google.gson.internal.LinkedTreeMap;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import xyz.erupt.annotation.EruptField;
import xyz.erupt.annotation.expr.ExprBool;
import xyz.erupt.annotation.fun.PowerHandler;
import xyz.erupt.annotation.fun.PowerObject;
import xyz.erupt.annotation.fun.TagsFetchHandler;
import xyz.erupt.annotation.sub_field.Edit;
import xyz.erupt.annotation.sub_field.EditType;
import xyz.erupt.annotation.sub_field.Readonly;
import xyz.erupt.annotation.sub_field.View;
import xyz.erupt.annotation.sub_field.sub_edit.*;
import xyz.erupt.common.http.EruptRemoteUtils;
import xyz.erupt.core.context.MetaContext;
import xyz.erupt.core.util.EruptSpringUtil;
import xyz.erupt.core.view.EruptApiModel;
import xyz.erupt.toolkit.db.Comment;
import xyz.erupt.toolkit.handler.EruptDictCodeChoiceFetchHandler;
import xyz.erupt.toolkit.handler.EruptDictTagFetchHandler;
import xyz.erupt.toolkit.handler.SqlChoiceFetchHandler;
import xyz.erupt.toolkit.handler.SqlTagFetchHandler;
import xyz.erupt.toolkit.remote.RemoteProxyService;
import xyz.erupt.toolkit.service.EruptCacheRedis;
import xyz.erupt.upms.handler.DictChoiceFetchHandler;
import xyz.erupt.upms.model.EruptMenu;
import xyz.erupt.upms.model.EruptRole;
import xyz.erupt.upms.model.EruptUser;
import xyz.erupt.upms.model.auth.DataAuthHandler;
import xyz.erupt.upms.model.auth.DataAuthModelVo;
import xyz.erupt.upms.model.template.EruptDeptTemplate;
import xyz.erupt.upms.service.EruptContextService;
import xyz.erupt.upms.service.EruptUserService;

import javax.persistence.ManyToOne;
import javax.persistence.MappedSuperclass;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;


@Getter
@Setter
@MappedSuperclass
public class EmployeeView extends DataAuthModelVo implements Readonly.ReadonlyHandler, PowerHandler, TagsFetchHandler {

    @EruptField(
            views = @View(title = "状态", show = true),
            edit = @Edit(title = "状态", type = EditType.BOOLEAN,
                    show = false,
                    boolType = @BoolType(trueText = "启用", falseText = "禁用")))
    @Comment("状态")
    @ApiModelProperty("状态")
    private Boolean state = false;

    @EruptField(sort = 1,
            views = @View(title = "姓名"),
            edit = @Edit(title = "姓名", type = EditType.INPUT, search = @Search(vague = true), notNull = true,
                    readonly = @Readonly(exprHandler = EmployeeView.class), inputType = @InputType))
    @Comment("姓名")
    @ApiModelProperty("姓名")
    private String name;

    @EruptField(
            views = @View(title = "是否外部员工", show = false),
            edit = @Edit(title = "是否外部员工", type = EditType.BOOLEAN, show = true,
                    //ifRender = @ExprBool(exprHandler = EmployeeExternalRenderHandler.class),
                    readonly = @Readonly(exprHandler = EmployeeView.class),
                    boolType = @BoolType(trueText = "外部员工", falseText = "内部员工")))
    @Comment("是否外部员工")
    @ApiModelProperty("是否外部员工")
    private Boolean external;

    @EruptField(sort = 2,
            views = @View(title = "单位名称", show = true),
            edit = @Edit(title = "单位名称", type = EditType.CHOICE, show = true, notNull = true,
                    search = @Search(ifRender = @ExprBool(exprHandler = EmployeeViewCompanyHandler.class)),
                    readonly = @Readonly(exprHandler = DataAuthHandler.class),
                    choiceType = @ChoiceType(
                            fullSpan = true,
                            fetchHandler = SqlChoiceFetchHandler.class, fetchHandlerParams = "select code,name from e_upms_org")))
    @Comment("单位名称")
    @ApiModelProperty("单位名称")
    private String company;

    @ManyToOne
    @EruptField(
            views = @View(title = "部门", column = "name"),
            edit = @Edit(title = "部门", type = EditType.REFERENCE_TREE, readonly = @Readonly(exprHandler = EmployeeView.class)
                    , referenceTreeType = @ReferenceTreeType(pid = "parentDept.id"), notNull = true
            )
    )
    @Comment("部门")
    @ApiModelProperty("部门")
    private EruptDeptTemplate department;

    @EruptField(sort = 5,
            views = @View(title = "所属角色"),
            edit = @Edit(title = "所属角色",
                    notNull = false,
                    type = EditType.TAGS, readonly = @Readonly(exprHandler = EmployeeView.class),
                    tagsType = @TagsType(allowExtension = false, fetchHandler = EmployeeView.class)
            )
    )
    @Comment("所属角色")
    @ApiModelProperty("所属角色")
    private String ownerRole;

    @EruptField(
            views = @View(title = "岗位",show = false),
            edit = @Edit(title = "岗位", type = EditType.TAGS,search = @Search(ifRender =@ExprBool(exprHandler = EmployeeInformation.class)),
                    notNull = true, readonly = @Readonly(exprHandler = EmployeeView.class),
                    tagsType = @TagsType(allowExtension = false, fetchHandler = EmployeeInfoJobTitleDataProxy.class)))
    @Comment("岗位")
    @ApiModelProperty("岗位")
    private String jobTitle;

//	@EruptField(
//			views = @View(title = "此岗位是否用于培训"),
//			edit = @Edit(title = "此岗位是否用于培训", type = EditType.CHOICE, notNull = true,
//					 choiceType = @ChoiceType(fetchHandler = EmployeeInfoJobTitleDataProxy.class, fetchHandlerParams = {"1"})))
//	@Comment("此岗位是否用于培训")
//	@ApiModelProperty("此岗位是否用于培训")
//	private String exclusiveJobTitle;

    @EruptField(
            views = @View(title = "性别"),
            edit = @Edit(title = "性别", type = EditType.BOOLEAN, notNull = true,
                    boolType = @BoolType(trueText = "男", falseText = "女")))
    @Comment("性别")
    @ApiModelProperty("性别")
    private Boolean sex;

    @EruptField(
            views = @View(title = "出生日期",show = false),
            edit = @Edit(title = "出生日期", type = EditType.DATE, notNull = false,
                    dateType = @DateType(pickerMode = DateType.PickerMode.RANGE, startToday = "1940-01-01 00:00:00", endToday = "now")))
    @Comment("出生日期")
    @ApiModelProperty("出生日期")
    private java.util.Date birthday;

    @EruptField(
            views = @View(title = "手机号"),
            edit = @Edit(title = "手机号",
                    type = EditType.INPUT,
                    notNull = true,
//                    desc = "该手机号即为登录账号，一经保存后禁止修改",
                    inputType = @InputType(fullSpan = true,regex= RegexPool.MOBILE),
                    readonly = @Readonly(exprHandler = PhoneReadOnlyHandler.class)
            )
    )
    @Comment("手机号")
    @ApiModelProperty("手机号")
    private String phone;


    @Comment("修改后手机号")
    @ApiModelProperty("修改后手机号")
    private String afterPhone;

    @EruptField(
            views = @View(title = "学历"),
            edit = @Edit(title = "学历", type = EditType.CHOICE, notNull = true,
                    choiceType = @ChoiceType(fetchHandler = EruptDictCodeChoiceFetchHandler.class, fetchHandlerParams = "educational")))
    @Comment("学历")
    @ApiModelProperty("学历")
    private String educational;

    @EruptField(
            views = @View(title = "专业"),
            edit = @Edit(title = "专业", type = EditType.INPUT, notNull = true,
                    inputType = @InputType))
    @Comment("专业")
    @ApiModelProperty("专业")
    private String major;

    @EruptField(
            views = @View(title = "是否主管"),
            edit = @Edit(title = "是否主管",
                    type = EditType.BOOLEAN,
                    readonly = @Readonly(exprHandler = EmployeeView.class),
                    notNull = true, desc = "主管用于【岗位胜任力】他评问卷"
            )
    )
    @Comment("是否主管")
    @ApiModelProperty("是否主管")
    private Boolean manager;

    @EruptField(sort = 3,
            views = @View(title = "职称"),
            edit = @Edit(
                    title = "职称",
                    type = EditType.CHOICE,
                    search = @Search,
                    notNull = true,
                    inputType = @InputType,
                    choiceType = @ChoiceType(fetchHandler = DictChoiceFetchHandler.class, fetchHandlerParams = "postTitle")
            )
    )
    @Comment("职称")
    @ApiModelProperty("职称")
    private String professional;

    @EruptField( sort = 4,
            views = @View(title = "是否检查人员",ifRender = @ExprBool(exprHandler = CompanyRenderHandler.class)),
            edit = @Edit(title = "是否检查人员",
                    type = EditType.BOOLEAN,
                    notNull = false, readonly = @Readonly(exprHandler = EmployeeView.class),
                    search = @Search(ifRender = @ExprBool(exprHandler = CompanyRenderHandler.class)),
                    show = false,
                    //ifRender = @ExprBool(exprHandler = CheckPersonRenderHandler.class),
                    boolType = @BoolType
            )
    )
    @Comment("是否检查人员")
    @ApiModelProperty("是否检查人员")
    private Boolean checkPerson;

    @EruptField(
            views = @View(title = "工作年限"),
            edit = @Edit(title = "工作年限", type = EditType.INPUT, notNull = true,
                    inputType = @InputType))
    @Comment("工作年限")
    @ApiModelProperty("工作年限")
    private String seniority;

    @EruptField(
            views = @View(title = "检查范围", show = false),
            edit = @Edit(title = "检查范围", type = EditType.TAGS, //notNull = true,
                    showBy = @ShowBy(dependField = "ownerRole", expr = "value!=null && value.includes('" + LAW_ENFORCEMENT_INSPECTORS_ROLE + "')"),
                    tagsType = @TagsType(allowExtension = false, fetchHandler = EruptDictTagFetchHandler.class, fetchHandlerParams = "type")))
    @Comment("检查范围")
    @ApiModelProperty("检查范围")
    private String checkScope;

    @EruptField(
            views = @View(title = "执法证号", show = false),
            edit = @Edit(title = "执法证号", type = EditType.INPUT, //notNull = true,
                    showBy = @ShowBy(dependField = "ownerRole", expr = "value!=null && value.includes('" + LAW_ENFORCEMENT_INSPECTORS_ROLE + "')"),
                    inputType = @InputType))
    @Comment("执法证号")
    @ApiModelProperty("执法证号")
    private String certificateNo;

    @EruptField(
            views = @View(title = "规避企业", show = false),
            edit = @Edit(title = "规避企业", type = EditType.TAGS, //notNull = true,
                    showBy = @ShowBy(dependField = "ownerRole", expr = "value!=null && value.includes('" + LAW_ENFORCEMENT_INSPECTORS_ROLE + "')"),
                    tagsType = @TagsType(allowExtension = false, fetchHandler = SqlTagFetchHandler.class, fetchHandlerParams = "select name from tb_enterprise")))
    @Comment("规避企业")
    @ApiModelProperty("规避企业")
    private String EvadeEnterprise;

    @Override
    public boolean add(boolean add, String[] params) {
        return isSelfInfoMenu();
    }

    public static boolean isSelfInfoMenu() {
        if (StringUtils.isEmpty(MetaContext.getToken())) return false;
        EruptContextService eruptContextService = EruptSpringUtil.getBean(EruptContextService.class);
        EruptMenu menu = eruptContextService.getCurrentEruptMenu();
        //LogUtils.printErrors(this, "edit -> readOnly");
        if (menu == null) {
            return true;
        } else return menu.getValue().equals(EmployeeSelfInfo.class.getSimpleName());
    }

    @Service
    public static class PhoneReadOnlyHandler implements Readonly.ReadonlyHandler {

        @Override
        public boolean add(boolean add, String[] params) {
//            EruptMenu menu = EruptSpringUtil.getBean(EruptContextService.class).getCurrentEruptMenu() ;
//            if(menu == null){
//                return true ;
//            } else{
//                return menu.getValue().equals(EmployeeSelfInfo.class.getSimpleName()) ;
//            }
            return false ;
        }

        @Override
        public boolean edit(boolean edit, String[] params) {
//            return true;
            return false ;
        }
    }

    @Override
    public boolean edit(boolean edit, String[] params) {
        return isSelfInfoMenu();
    }

    @Override
    public void handler(PowerObject power) {
        //演示环境暂时关闭
        EruptUserService eruptUserService = EruptSpringUtil.getBean(EruptUserService.class);
        EruptUser eruptUser = eruptUserService.getCurrentEruptUser();
        boolean isAdmin = eruptUser != null && eruptUser.getIsAdmin();

        power.setAdd(isAdmin);
        power.setDelete(isAdmin);
        power.setImportable(isAdmin);

        if (eruptUser != null) {
            eruptUser.getRoles().forEach(role -> {
                role.getMenus().forEach(menu -> {
                    if (menu.getCode().equals("EmployeeInformation@ADD")) power.setAdd(true);
                    if (menu.getCode().equals("EmployeeInformation@EDIT")) power.setEdit(true);
                    if (menu.getCode().equals("EmployeeInformation@DELETE")) power.setDelete(true);
                });
            });
        }

        power.setViewDetails(true);
        //power.setEdit(isSelfInfoMenu() ? true : true);
    }

//    @Override
//    public List<VLModel> fetch(String[] params) {
//        List<VLModel> models = new ArrayList<>();
//        //仅能选择公共角色+创建人是自己企业的管理员的角色
//        String publicSql = "select * from e_upms_role where share=true and reserved=true";
//        List<EruptRole> roles = EruptDaoUtils.selectOnes(publicSql, EruptRole.class);
//        //当前用户所在企业ID
//        EruptUserService eruptUserService = EruptSpringUtil.getBean(EruptUserService.class);
//        EruptUser eruptUser = eruptUserService.getCurrentEruptUser();
//        if (eruptUser != null && eruptUser.getEruptOrg() != null) {
//            String companySql = "select * from e_upms_role where share=true and create_user_id=(select e_upms_user.id from e_upms_org,e_upms_user where e_upms_org.id=e_upms_user.erupt_org_id and " +
//                    "e_upms_org.code='" + eruptUser.getEruptOrg().getCode() + "')";
//            List<EruptRole> companyRoles = EruptDaoUtils.selectOnes(companySql, EruptRole.class);
//            roles.addAll(companyRoles);
//        }
//        roles.forEach(role -> models.add(new VLModel(role.getCode(), role.getName())));
//        return models;
//    }

    public static final String EMPLOYEE_FETCH_ROLES = "EMPLOYEE_FETCH_ROLES";
    public static final String EMPLOYEE_JOB_TITLES = "EMPLOYEE_JOB_TITLES";

    public static final String DEFAULT_ROLE = "员工";
    public static final String DEFAULT_ROLE_DEPARTMENT = "市级员工";
    public static final String DEFAULT_ROLE_AREA = "区县员工";

    //public static final String SAFE_SUPERVISE_ROLE = "安全监管人员";

    public static final String LAW_ENFORCEMENT_INSPECTORS_ROLE = "执法检查人员";

    private static final boolean CACHE_SESSION = true;

    public static List<LinkedTreeMap> getShareRolesVo(EruptUser eruptUser) {
        RemoteProxyService remoteProxyService = EruptSpringUtil.getBean(RemoteProxyService.class);
        String url = remoteProxyService.getMainServerUrl() + "/erupt-api/get-share-roles";
        if (eruptUser != null && eruptUser.getEruptOrg() != null)
            url += "?orgCode=" + eruptUser.getEruptOrg().getCode();

        EruptApiModel result = EruptRemoteUtils.doGet(url, EruptApiModel.class);
        List<LinkedTreeMap> roleVos = (List<LinkedTreeMap>) result.getData();
        return roleVos;
    }

    public List<String> getShareRoles(EruptUser eruptUser, boolean cache) {
        Set<String> roles = new HashSet<>();
        List<LinkedTreeMap> roleVos = getShareRolesVo(eruptUser);

        //取得员工隔离角色
        List<EruptRole> empRoles = eruptUser.getRoles().stream().filter(role -> role.getCode().contains(".")).collect(Collectors.toList());
        EruptRole empRole = empRoles.size() > 0 ? empRoles.get(0) : null;
        roleVos.forEach(vo -> {
            String role = vo.get("name").toString();
            //去掉【员工】角色的显示
            if (!DEFAULT_ROLE.equals(role)) {
                Object ident_ = vo.get("ident");
                String ident = ident_ == null ? "" : ident_.toString();

                //检查用户是否有新增/修改员工信息的权限
                boolean hasRole = empRole != null && empRole.getMenus().stream().filter(menu -> menu.getCode().equals("EmployeeInformation@ADD") || menu.getCode().equals("EmployeeInformation@EDIT")).collect(Collectors.toList()).size() > 0;

                if (StringUtils.isNotEmpty(ident)) {
                    if (ident.equalsIgnoreCase("ALL")) roles.add(role);
                    if ((DaliangangContext.isDepartmentAdmin() || DaliangangContext.isDepartmentEmployee()) && ident.equalsIgnoreCase("GOV"))
                        roles.add(role);
                    if ((DaliangangContext.isDepartmentArea() || DaliangangContext.isAreaEmployee()) && ident.equalsIgnoreCase("AREA"))
                        roles.add(role);
                    if (hasRole && (DaliangangContext.isEnterpriseUser() || DaliangangContext.isEnterpriseEmployee()) && ident.equalsIgnoreCase("ENTERPRISE"))
                        roles.add(role);
                } else {
                    roles.add(role);
                }
            }
        });

        if (cache) {
            if (CACHE_SESSION) {
                MetaContext.setAttribute(EmployeeView.EMPLOYEE_FETCH_ROLES, roleVos);
            }
            EmployeeView.cacheRoleVos(roleVos);
        }

        return new ArrayList<>(roles);
    }

    @Override
    public List<String> fetchTags(String[] params) {
//        Set<String> roles = new HashSet<>();
//        RemoteProxyService remoteProxyService = EruptSpringUtil.getBean(RemoteProxyService.class);
//        String url = remoteProxyService.getMainServerUrl() + "/erupt-api/get-share-roles";

        EruptUserService eruptUserService = EruptSpringUtil.getBean(EruptUserService.class);
        EruptUser eruptUser = eruptUserService.getCurrentEruptUser();
        return this.getShareRoles(eruptUser, true);

//        if (eruptUser != null && eruptUser.getEruptOrg() != null)
//            url += "?orgCode=" + eruptUser.getEruptOrg().getCode();
//
//        EruptApiModel result = EruptRemoteUtils.doGet(url, EruptApiModel.class);
//        List<LinkedTreeMap> roleVos = (List<LinkedTreeMap>) result.getData();
//        roleVos.forEach(vo -> {
////            if (eruptUser.getIsAdmin()) {
////                roles.add(vo.get("name").toString());
////            } else {
//            String role = vo.get("name").toString();
//            //去掉【员工】角色的显示
//            if (!DEFAULT_ROLE.equals(role)) {
//                Object ident_ = vo.get("ident");
//                String ident = ident_ == null ? "" : ident_.toString();
//
//                if (StringUtils.isNotEmpty(ident)) {
//                    if (ident.equalsIgnoreCase("ALL"))
//                        roles.add(role);
//                    if (DaliangangContext.isDepartmentAdmin() && ident.equalsIgnoreCase("GOV"))
//                        roles.add(role);
//                    if (DaliangangContext.isDepartmentArea() && ident.equalsIgnoreCase("AREA"))
//                        roles.add(role);
//                    if (DaliangangContext.isEnterpriseUser() && ident.equalsIgnoreCase("ENTERPRISE"))
//                        roles.add(role);
//                } else {
//                    roles.add(role);
//                }
//            }
////            }
//        });

//        if (CACHE_SESSION) {
//            MetaContext.setAttribute(EmployeeView.EMPLOYEE_FETCH_ROLES, roleVos);
//        }
//        EmployeeView.cacheRoleVos(roleVos);
//        return new ArrayList<>(roles);
    }

    private static String getRoleVoCacheKey() {
        EruptUserService eruptUserService = EruptSpringUtil.getBean(EruptUserService.class);
        //EruptCacheRedis eruptCacheRedis = EruptSpringUtil.getBean(EruptCacheRedis.class);
        EruptUser eruptUser = eruptUserService.getCurrentEruptUser();
        String cacheKey = EmployeeView.EMPLOYEE_FETCH_ROLES + "_" + eruptUser.getId();
        return cacheKey;
    }

    public static void cacheRoleVos(List<LinkedTreeMap> roleVos) {
        EruptCacheRedis eruptCacheRedis = EruptSpringUtil.getBean(EruptCacheRedis.class);
        eruptCacheRedis.put(getRoleVoCacheKey(), roleVos, TimeUnit.SECONDS.toMillis(1800));
    }

    public static List<LinkedTreeMap> getCacheRoleVos() {
        if (CACHE_SESSION) {
            List<LinkedTreeMap> roles = (List<LinkedTreeMap>) MetaContext.getAttribute(EmployeeView.EMPLOYEE_FETCH_ROLES);
            if (roles == null) {
                EruptSpringUtil.getBean(EmployeeView.class).fetchTags(null);
                return getCacheRoleVos();
            }
            return roles;
        }
        EruptCacheRedis eruptCacheRedis = EruptSpringUtil.getBean(EruptCacheRedis.class);
        return (List<LinkedTreeMap>) eruptCacheRedis.get(getRoleVoCacheKey());
    }
}
