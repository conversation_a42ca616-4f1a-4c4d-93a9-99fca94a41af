package com.daliangang.emergency.proxy;

import com.daliangang.emergency.entity.EmergencyStatistics;
import com.daliangang.rndpub.entity.CompanyInspect;
import com.daliangang.workbench.entity.Enterprise;
import org.springframework.stereotype.Service;
import xyz.erupt.annotation.fun.DataProxy;
import xyz.erupt.jpa.dao.EruptDao;
import xyz.erupt.toolkit.db.EruptResultMap;
import xyz.erupt.toolkit.utils.EruptDaoUtils;

import javax.annotation.Resource;
import javax.transaction.Transactional;
import java.text.SimpleDateFormat;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2023/11/3
 */
@Service
public class EmergencyStatisticsDataProxy implements DataProxy<EmergencyStatistics> {

    @Resource
    private EruptDao eruptDao;

    @Override
    @Transactional
    public void searchCondition(Map<String, Object> condition) {
        //清空表内容
        String truncateSql = "truncate table tb_emergency_statistics";
        eruptDao.getJdbcTemplate().execute(truncateSql);
        //查询企业列表
        String companySql = "select * from tb_enterprise";
        List<Enterprise> enterprises = EruptDaoUtils.selectOnes(companySql, Enterprise.class);
        for (Enterprise enterprise : enterprises) {
            //创建实体类
            EmergencyStatistics emergencyStatistics = new EmergencyStatistics();
            //设置总数默认值
            Integer sum = 0;
            //设置企业名称
            emergencyStatistics.setCompany(enterprise.getOrgCode());
            //设置应急救援队伍数量
            String rescueteamCountSql = String.format("SELECT IFNULL(COUNT(org_code),0) rescueteamCount FROM `tb_rescueteam` WHERE company in ('%s','%s')", enterprise.getOrgCode(),enterprise.getName());
            EruptResultMap rescueteamMap = EruptDaoUtils.selectMap(rescueteamCountSql);
            Integer rescueteamCount = Integer.parseInt(String.valueOf(rescueteamMap.get("rescueteamCount")));
            emergencyStatistics.setRescueteamCount(rescueteamCount);
            sum+=rescueteamCount;
            //设置应急集合点数量
            String emergencyMusterCountSql = String.format("SELECT IFNULL(COUNT(org_code),0) emergencyMusterCount FROM `tb_emergency_muster` WHERE company in ('%s','%s')", enterprise.getOrgCode(),enterprise.getName());
            EruptResultMap emergencyMusterMap = EruptDaoUtils.selectMap(emergencyMusterCountSql);
            Integer emergencyMusterCount = Integer.parseInt(String.valueOf(emergencyMusterMap.get("emergencyMusterCount")));
            emergencyStatistics.setEmergencyMusterCount(emergencyMusterCount);
            sum+=emergencyMusterCount;
            //设置事故应急池数量
            String emergencyPoolCountSql = String.format("SELECT IFNULL(COUNT(org_code),0) emergencyPoolCount FROM `tb_emergency_pool` WHERE company in ('%s','%s')", enterprise.getOrgCode(),enterprise.getName());
            EruptResultMap emergencyPoolMap = EruptDaoUtils.selectMap(emergencyPoolCountSql);
            Integer emergencyPoolCount = Integer.parseInt(String.valueOf(emergencyPoolMap.get("emergencyPoolCount")));
            emergencyStatistics.setEmergencyPoolCount(emergencyPoolCount);
            sum+=emergencyPoolCount;
            //设置应急物资储备点数量
            String materialReserveCountSql = String.format("SELECT IFNULL(COUNT(org_code),0) materialReserveCount FROM `tb_material_reserve` WHERE company in ('%s','%s')", enterprise.getOrgCode(),enterprise.getName());
            EruptResultMap materialReserveMap = EruptDaoUtils.selectMap(materialReserveCountSql);
            Integer materialReserveCount = Integer.parseInt(String.valueOf(materialReserveMap.get("materialReserveCount")));
            emergencyStatistics.setMaterialReserveCount(materialReserveCount);
            sum+=materialReserveCount;
            //设置总数
            emergencyStatistics.setSumTotal(sum);
            //插入数据
            eruptDao.merge(emergencyStatistics);
        }
    }
}
