/*
* Copyright © 2020-2035 yundingyun All rights reserved.
*/
package com.daliangang.rndpub.entity;

import com.daliangang.rndpub.proxy.LawenForcementDataProxy;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import xyz.erupt.annotation.Erupt;
import xyz.erupt.annotation.EruptField;
import xyz.erupt.annotation.sub_erupt.Power;
import xyz.erupt.annotation.sub_field.Edit;
import xyz.erupt.annotation.sub_field.EditType;
import xyz.erupt.annotation.sub_field.View;
import xyz.erupt.annotation.sub_field.sub_edit.ChoiceType;
import xyz.erupt.annotation.sub_field.sub_edit.Search;
import xyz.erupt.toolkit.db.Comment;
import xyz.erupt.toolkit.handler.RemoteEntityChoiceFetchHandler;
import xyz.erupt.toolkit.handler.SqlChoiceFetchHandler;
import xyz.erupt.upms.model.auth.DataAuthModel;

import javax.persistence.Entity;
import javax.persistence.Table;

@Erupt(name = "执法信息", power = @Power(add = true, delete = true, export = false, importable = true, viewDetails = true, edit = true)
, dataProxy = LawenForcementDataProxy.class
, rowOperation = {})
@Table(name = "tb_lawen_forcement")
@Entity
@Getter
@Setter
@Comment("执法信息")
@ApiModel("执法信息")
public class LawenForcement extends DataAuthModel {
	@EruptField(
		views = @View(title = "检查范围"),
		edit = @Edit(title = "检查范围", type = EditType.CHOICE, notNull = true,
		choiceType = @ChoiceType(fetchHandler = SqlChoiceFetchHandler.class, fetchHandlerParams = "select '测试项','测试项'")))
	@Comment("检查范围")
	@ApiModelProperty("检查范围")
	private String checkScope;

	@EruptField(
		views = @View(title = "规避企业"),
		edit = @Edit(title = "规避企业", type = EditType.CHOICE, notNull = true,
		choiceType = @ChoiceType(fetchHandler = RemoteEntityChoiceFetchHandler.class, fetchHandlerParams ={"main","Enterprise","org_code,name"})))
	@Comment("规避企业")
	@ApiModelProperty("规避企业")
	private String evade;

	@EruptField(
		views = @View(title = "专家姓名"),
		edit = @Edit(title = "专家姓名", type = EditType.CHOICE, search = @Search, notNull = true,
		choiceType = @ChoiceType(fetchHandler = SqlChoiceFetchHandler.class, fetchHandlerParams = "select '测试项','测试项'")))
	@Comment("专家姓名")
	@ApiModelProperty("专家姓名")
	private String nameOfExpert;

}
