/*
 * Copyright © 2020-2035 yundingyun All rights reserved.
 */
package com.daliangang.safedaily.entity;

import com.daliangang.core.CompanyRenderHandler;
import com.daliangang.safedaily.operation.CheckFillSubmissionHandler;
import com.daliangang.safedaily.proxy.CheckFillDataProxy;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import xyz.erupt.annotation.Erupt;
import xyz.erupt.annotation.EruptField;
import xyz.erupt.annotation.expr.ExprBool;
import xyz.erupt.annotation.sub_erupt.Power;
import xyz.erupt.annotation.sub_erupt.RowOperation;
import xyz.erupt.annotation.sub_field.Edit;
import xyz.erupt.annotation.sub_field.EditType;
import xyz.erupt.annotation.sub_field.Readonly;
import xyz.erupt.annotation.sub_field.View;
import xyz.erupt.annotation.sub_field.sub_edit.*;
import xyz.erupt.toolkit.attachment.AttachmentDownloadHandler;
import xyz.erupt.toolkit.db.Comment;
import xyz.erupt.toolkit.handler.DelOperationHandler;
import xyz.erupt.toolkit.handler.EditOperationHandler;
import xyz.erupt.toolkit.handler.RemoteEntityChoiceFetchHandler;
import xyz.erupt.toolkit.submit.CheckSubmit;
import xyz.erupt.toolkit.submit.CheckUpdate;
import xyz.erupt.toolkit.utils.TplUtils;
import xyz.erupt.upms.model.auth.DataAuthHandler;
import xyz.erupt.upms.model.auth.DataAuthModel;

import javax.persistence.Entity;
import javax.persistence.Table;

@Erupt(name = "安全生产责任制", power = @Power(delete = false, edit = false)
        , dataProxy = CheckFillDataProxy.class
        ,orderBy = "CheckFill.modifyTime desc",
        rowOperation = {
        @RowOperation(title = "编辑", icon = "fa fa-edit", code = TplUtils.EDIT_OPER_CODE,
                eruptClass = CheckFill.class, operationHandler = EditOperationHandler.class,
                ifExpr = "item.submitted=='未上报'", ifExprBehavior = RowOperation.IfExprBehavior.HIDE,
                mode = RowOperation.Mode.SINGLE),
        @RowOperation(title = "删除", icon = "fa fa-trash-o",
                operationHandler = DelOperationHandler.class, ifExpr = "item.submitted=='未上报'", ifExprBehavior = RowOperation.IfExprBehavior.HIDE, mode = RowOperation.Mode.SINGLE),
        @RowOperation(title = "上报", icon = "fa fa-arrow-circle-o-up", operationHandler = CheckFillSubmissionHandler.class, ifExpr = "item.submitted=='未上报'", ifExprBehavior = RowOperation.IfExprBehavior.HIDE, mode = RowOperation.Mode.SINGLE),
        @RowOperation(title = "下载附件", icon = "fa fa-download", operationHandler = AttachmentDownloadHandler.class, mode = RowOperation.Mode.SINGLE)
})
@Table(name = "tb_check_fill")
@Entity
@Getter
@Setter
@Comment("安全生产责任制")
@ApiModel("安全生产责任制")
//@PreDataProxy(CheckUpdateService.class)
public class CheckFill extends DataAuthModel {
    @EruptField(
            views = @View(title = "企业名称",width = "360px"),
            edit = @Edit(title = "企业名称",search = @Search(ifRender = @ExprBool(exprHandler = CompanyRenderHandler.class)), readonly = @Readonly(exprHandler = DataAuthHandler.class), type = EditType.CHOICE, notNull = true,
                    choiceType = @ChoiceType(fullSpan = true,fetchHandler = RemoteEntityChoiceFetchHandler.class, fetchHandlerParams = {"main", "Enterprise", "org_code,name"})))
    @Comment("企业名称")
    @ApiModelProperty("企业名称")
    private String company;

    @EruptField(
            views = @View(title = "年度", sortable = true),
            edit = @Edit(title = "年度", readonly = @Readonly(add = false, edit = true), type = EditType.DATE, search = @Search, notNull = true,
                    dateType = @DateType(type = DateType.Type.YEAR)))
    @Comment("年度")
    @ApiModelProperty("年度")
    private String year;

    @EruptField(
            views = @View(title = "上报状态", show = true,width = "170px"),
            edit = @Edit(title = "上报状态", type = EditType.BOOLEAN, show = false, /*notNull = true,*/
                    boolType = @BoolType(trueText = "已上报", falseText = "未上报")))
    @CheckSubmit(linkProperty = "year", checkTimeDiffPeriod = 1)
    @Comment("上报状态")
    @ApiModelProperty("上报状态")
    private Boolean submitted;



    @EruptField(
            views = @View(title = "全员安全生产责任制", show = false),
            edit = @Edit(title = "全员安全生产责任制", type = EditType.ATTACHMENT, notNull = true,
                    attachmentType = @AttachmentType(type = AttachmentType.Type.BASE, maxLimit = 1, fileTypes = {".pdf", ".doc", ".docx", ".xlsx", ".png", ".jpg", ".xls", ".ppt", ".pptx", ".zip", ".rar"})))
    @Comment("全员安全生产责任制")
    @ApiModelProperty("全员安全生产责任制")
    @CheckUpdate
    private String responseiFile;

    @EruptField(
            views = @View(title = "安全生产责任书扫描件", show = false),
            edit = @Edit(title = "安全生产责任书扫描件", type = EditType.ATTACHMENT, notNull = true,
                    attachmentType = @AttachmentType(type = AttachmentType.Type.BASE, maxLimit = 20, fileTypes = {".pdf", ".doc", ".docx", ".xlsx", ".png", ".jpg", ".xls", ".ppt", ".pptx", ".zip", ".rar"})))
    @Comment("安全生产责任书扫描件")
    @ApiModelProperty("安全生产责任书扫描件")
    @CheckUpdate
    private String responseiCopy;

    @EruptField(
            views = @View(title = "更新时间"),
            edit = @Edit(title = "更新时间", show = false))
    @Comment("更新时间")
    @ApiModelProperty("更新时间")
    private java.util.Date modifyTime;

}
