package com.daliangang.safedaily.operation;

import cn.hutool.json.JSONObject;
import com.daliangang.safedaily.entity.InspectExpertDaily;
import com.daliangang.safedaily.entity.SafetyAlertDayManagement;
import org.springframework.stereotype.Service;
import xyz.erupt.annotation.fun.OperationHandler;
import xyz.erupt.common.http.EruptRemoteUtils;
import xyz.erupt.jpa.dao.EruptDao;
import xyz.erupt.toolkit.remote.RemoteProxyService;
import xyz.erupt.toolkit.utils.NotifyUtils;

import javax.annotation.Resource;
import javax.transaction.Transactional;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * @Title: SafetyAlertEscalationHandler
 * <AUTHOR>
 * @Package com.daliangang.safedaily.operation
 * @Date 2024/3/7 19:45
 * @description: 安全警示日上报
 */
@Service
public class SafetyAlertEscalationHandler implements OperationHandler<SafetyAlertDayManagement,Void> {

    @Resource
    private RemoteProxyService remoteProxyService;

    @Resource
    private EruptDao eruptDao;

    @Override
    @Transactional
    public String exec(List<SafetyAlertDayManagement> data, Void unused, String[] param) {
        for (SafetyAlertDayManagement safetyAlertDayManagement:data) {
            safetyAlertDayManagement.setSubmitted(true);
            safetyAlertDayManagement.setUpdateTime(LocalDateTime.now());
            eruptDao.merge(safetyAlertDayManagement);

            JSONObject inputData = new JSONObject();
            inputData.set("clazz", "SafetyAlertDayManagement");
            inputData.set("insertData",safetyAlertDayManagement);
            EruptRemoteUtils.doPost(remoteProxyService.getMainServerUrl() + "/erupt-api/exchange/data/exchange", inputData, Map.class);
        }

        return NotifyUtils.getSuccessNotify("上报成功！");
    }
}
