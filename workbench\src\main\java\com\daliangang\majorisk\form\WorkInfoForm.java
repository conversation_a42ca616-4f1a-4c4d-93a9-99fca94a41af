package com.daliangang.majorisk.form;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import xyz.erupt.annotation.EruptField;
import xyz.erupt.annotation.sub_field.Edit;
import xyz.erupt.annotation.sub_field.EditType;
import xyz.erupt.annotation.sub_field.Readonly;
import xyz.erupt.annotation.sub_field.View;
import xyz.erupt.annotation.sub_field.sub_edit.InputType;
import xyz.erupt.toolkit.db.Comment;

import java.util.List;

/**
 * <AUTHOR>
 * @since :2023/5/4:16:25
 */
@Data
public class WorkInfoForm {


    private String name;

    private String type;

    private String company;


    private List<String> map;
}
