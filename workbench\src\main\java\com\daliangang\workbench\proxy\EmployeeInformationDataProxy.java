/*
 * Copyright © 2020-2035 yundingyun All rights reserved.
 */
package com.daliangang.workbench.proxy;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.lang.Validator;
import com.daliangang.core.DaliangangContext;
import com.daliangang.training.models.UserSyncEntity;
import com.daliangang.workbench.entity.EmployeeInformation;
import com.daliangang.workbench.entity.EmployeeView;
import com.daliangang.workbench.entity.EruptRoleTemplatePost;
import com.daliangang.workbench.service.EruptRoleTemplatePostService;
import com.daliangang.workbench.service.PlatformService;
import com.google.gson.internal.LinkedTreeMap;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import xyz.erupt.annotation.fun.DataProxy;
import xyz.erupt.annotation.query.Condition;
import xyz.erupt.core.context.MetaContext;
import xyz.erupt.core.module.MetaUserinfo;
import xyz.erupt.core.util.EruptSpringUtil;
import xyz.erupt.jpa.dao.EruptDao;
import xyz.erupt.rider.RiderAutoConfiguration;
import xyz.erupt.toolkit.db.EruptResultMap;
import xyz.erupt.toolkit.redismq.RedisMQConst;
import xyz.erupt.toolkit.redismq.RedisMQService;
import xyz.erupt.toolkit.remote.RemoteProxyService;
import xyz.erupt.toolkit.utils.AssertUtils;
import xyz.erupt.toolkit.utils.EruptDaoUtils;
import xyz.erupt.toolkit.utils.NotifyUtils;
import xyz.erupt.toolkit.utils.SqlUtils;
import xyz.erupt.upms.model.EruptMenu;
import xyz.erupt.upms.model.EruptRole;
import xyz.erupt.upms.model.EruptUser;
import xyz.erupt.upms.model.EruptUserVo;
import xyz.erupt.upms.model.template.*;
import xyz.erupt.upms.service.EruptContextService;
import xyz.erupt.upms.service.EruptRoleService;
import xyz.erupt.upms.service.EruptUserService;

import javax.annotation.Resource;
import javax.transaction.Transactional;
import java.sql.Timestamp;
import java.util.*;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

@Service
@Slf4j
public class EmployeeInformationDataProxy implements DataProxy<EmployeeView> {

    @Resource
    private EruptUserService eruptUserService;
    @Resource
    private RemoteProxyService remoteProxyService;
    @Resource
    private RedisMQService redisMQService;

    @Resource
    private EruptRoleTemplatePostService eruptRoleTemplatePostService;

    @Resource
    private EruptDao eruptDao;

    @Resource
    private EruptRoleService eruptRoleService;

    @Resource
    private RiderAutoConfiguration riderAutoConfiguration;

    @Resource
    private EruptRoleTemplateUserProxy eruptRoleTemplateUserProxy;

    @Resource
    private EruptRoleTemplateUserService eruptRoleTemplateUserService;

    @Override
    public String beforeFetch(List<Condition> conditions) {
        EruptUser currentEruptUser = EruptSpringUtil.getBean(EruptUserService.class).getCurrentEruptUser();
        if (currentEruptUser.getIsAdmin()) {
            return null;
        } else {    // 新需求管理员要查看自己的员工
            conditions.forEach(v->{
                if(v.getKey().equals("orgCode")) {
                    List list = new ArrayList();
                    list.add(currentEruptUser.getEruptOrg().getCode());
                    v.setValue(list);
                }
            });
        }

        if (EmployeeView.isSelfInfoMenu()) {
            return "EmployeeSelfInfo.name=" + SqlUtils.wrapStr(MetaContext.getUser().getName());
        }

        EruptContextService eruptContextService = EruptSpringUtil.getBean(EruptContextService.class);
        EruptMenu menu = eruptContextService.getCurrentEruptMenu();
        //LogUtils.printErrors(this, "edit -> readOnly");
        //员工管理菜单，只展示本企业员工
        if (menu.getValue().equals(EmployeeInformation.class.getSimpleName())) {
            return "company = " + SqlUtils.wrapStr(currentEruptUser.getEruptOrg().getCode());
        }

        return DataProxy.super.beforeFetch(conditions);
    }

    @SneakyThrows
    public  <T> List<T> objToList(Object obj, Class<T> cla) {
        List<T> list = new ArrayList<T>();
        if (obj instanceof ArrayList<?>) {
            for (Object o : (List<?>) obj) {
                list.add(cla.cast(o));
            }
            return list;
        }
        return null;
    }

    @Resource
    private PlatformService platformService;

    @Override
    public void addBehavior(EmployeeView employee) {
        MetaUserinfo remoteUserInfo = remoteProxyService.getRemoteUserInfo();
        if (ObjectUtils.isNotEmpty(remoteUserInfo.getOrg())) {
            employee.setCompany(remoteUserInfo.getOrg());
        }
        employee.setExternal(false);
        employee.setSex(true);
        employee.setCheckPerson(false);
        employee.setBirthday(new Date(Timestamp.valueOf("1980-01-01 00:00:00").getTime()));
        //将员工默认添加，现在要去掉展示在前台
//        employee.setOwnerRole("员工");
        //政府端新增员工角色默认【安全监管人员】
//        if (DaliangangContext.isDepartmentUser()) {
//            employee.setOwnerRole(EmployeeView.SAFE_SUPERVISE_ROLE);
//        }
        String deptSql = "select * from e_upms_template_dept where org_code='" + employee.getOrgCode() + "'";
        List<EruptDeptTemplate> depts = EruptDaoUtils.selectOnes(deptSql, EruptDeptTemplate.class);
        if (!depts.isEmpty()) {
            employee.setDepartment(depts.get(0));
        }
    }

    @Override
    public void beforeAdd(EmployeeView employeeInformation) {
        //检验所属公司与当前是否相同
        //获取当前登录的用户
        EruptUser eruptUser = eruptUserService.getCurrentEruptUser();
        if (!DaliangangContext.isDepartmentAdmin() && StringUtils.isNotEmpty(employeeInformation.getCompany()) && StringUtils.isNotEmpty(eruptUser.getEruptOrg().getCode()) && !employeeInformation.getCompany().equals(eruptUser.getEruptOrg().getCode())) {
            NotifyUtils.showErrorDialog("所属企业与当前企业不一致，请核对后重新导入！");
        }
        //添加默认角色-员工
        this.addEmployeeRole(employeeInformation);
        //设置是否检查人员的值
        this.setCheckPersonValue(employeeInformation);
//        String ownerRole = employeeInformation.getOwnerRole();
//        if (ownerRole.contains("|")){
//            String[] split = ownerRole.split("\\|");
//            ArrayList<String> roleLists = new ArrayList<>();
//            for (String s : split) {
//                if(s.equals("员工")){
//                    roleLists.add("104");
//                }
//                roleLists.add(s);
//            }
//            String roles = roleLists.stream().collect(Collectors.joining("|"));
//            employeeInformation.setOwnerRole(roles);
//        }else if(ownerRole.equals("员工")){
//            employeeInformation.setOwnerRole("104");
//        }
        employeeInformation.setState(true);//创建员工默认为启用状态
        employeeInformation.setExternal(false);
        String phone = employeeInformation.getPhone();
        if (!Validator.isMobile(phone))
            NotifyUtils.showErrorMsg("你输入的手机号格式不正确！");
        String selectSql = "select * from tb_employee_information where phone='" + phone + "'";
        List<EmployeeInformation> employeeInformations = EruptDaoUtils.selectOnes(selectSql, EmployeeInformation.class);
        if (CollectionUtil.isNotEmpty(employeeInformations)) {
            NotifyUtils.showErrorMsg("员工【" + employeeInformation.getName() + "】的手机号【" + phone + "】已被注册过，请换一个！");
        }
        judgeData(employeeInformation);
        checkMultiPost(employeeInformation);
        checkIsolateRole(employeeInformation);
    }

    @Override
    @Transactional
    public void afterAdd(EmployeeView employee) {
        //判断是否是内部员工，为其创建账号分配角色
        if (!employee.getExternal()) {
            //共享角色改为不直接关联，而在创建完用户后合并其权限至隔离角色中
            //Set<String> shareRoles = this.checkShareRoles(employee);
            EruptUser eruptUser = platformService.afterAdd((EmployeeInformation) employee,
                    this.checkIsolateRole(employee)
                    //,shareRoles.toArray(new String[shareRoles.size()])
            );
            this.mergeShareRolesIntoIsolateRole(employee, eruptUser, true);
            EruptUser empUser = eruptUserService.findEruptUserByAccount(employee.getPhone());
            //因为EruptUser默认为启用，如果是禁用状态，需要自己刷新一下
            if (!employee.getState()) {
                AssertUtils.notNull(empUser, "没有这个用户");
                String sql = "update e_upms_user set status=" + employee.getState() + " where id=" + empUser.getId();
                EruptDaoUtils.updateNoForeignKeyChecks(sql);
            }
            //加一下手机号
            platformService.updatePhone(employee.getPhone(),employee.getPhone(),true);
//            String sql = "update e_upms_user set phone= '" + employee.getPhone() + "' where id=" + empUser.getId();
//            EruptDaoUtils.updateNoForeignKeyChecks(sql);
//            eruptUserService.flushEruptUserCache(empUser);
            riderAutoConfiguration.initRiderRoleUser();
        }
    }

    public EruptRole getIsolateRoleFromEmployee(EmployeeView employee, EruptUser eruptUser, boolean autoCreate) {
//        int baseRoleCode = 104;
//        if (DaliangangContext.isDepartmentEmployee()) baseRoleCode = 108;
//        if (DaliangangContext.isAreaEmployee()) baseRoleCode = 109;
//        String code = baseRoleCode + "." + employee.getPhone();//隔离角色的code
//


        String sql = "select * from e_upms_role where code like '%" + employee.getPhone() + "%'";
        List<EruptRole> empRoles = EruptDaoUtils.selectOnes(sql, EruptRole.class);
        String code = "104." + employee.getPhone();
        if (empRoles.size() > 0) {
            code = empRoles.get(0).getCode();
        }
        AtomicReference<EruptRole> isolateRole = new AtomicReference<>();
        String finalCode = code;
        eruptUser.getRoles().forEach(role -> {
            if (role.getCode().equalsIgnoreCase(finalCode)) {
                isolateRole.set(role);
            }
        });
        if (!autoCreate)
            AssertUtils.notNull(isolateRole.get(), "找不到隔离角色[" + employee.getName() + "]");
        return isolateRole.get();
    }

    @Transactional
    public void mergeShareRolesIntoIsolateRole(EmployeeView employee, EruptUser eruptUser, boolean autoCreate) {
        Set<String> shareRoles = this.checkShareRoles(employee);
        EruptRole isolateRole = this.getIsolateRoleFromEmployee(employee, eruptUser, autoCreate);
        if (isolateRole != null && autoCreate) {
            AtomicBoolean needUpdate = new AtomicBoolean(false);
            shareRoles.forEach(roleCode -> {
                //定位组织内的共享角色
                EruptRole shareRole = eruptDao.queryEntity(EruptRole.class, "code='" + roleCode + "'");
                shareRole.getMenus().forEach(menu -> {
                    if (!isolateRole.getMenus().contains(menu)) {
                        isolateRole.getMenus().add(menu);
                        needUpdate.set(true);
                    }
                });
            });
            if (needUpdate.get()) eruptDao.merge(isolateRole);
        }
    }

    @Override
    public void afterFetch(Collection<Map<String, Object>> list) {
        DataProxy.super.afterFetch(list);
    }

    public EruptRole getShareRoleBySelectedRoleName(String name) {
        return this.getShareRoleBySelectedRoleName(EmployeeView.getCacheRoleVos(), name);
    }

    public EruptRole getShareRoleBySelectedRoleName(EruptUser eruptUser, String name) {
        return this.getShareRoleBySelectedRoleName(EmployeeView.getShareRolesVo(eruptUser), name);
    }

    public EruptRole getShareRoleBySelectedRoleName(List<LinkedTreeMap> roleVos, String name) {
        List<LinkedTreeMap> roleFilters = roleVos.stream().filter(vo -> vo.containsKey("name") && vo.get("name").equals(name)).collect(Collectors.toList());
        AssertUtils.isFalse(roleFilters.isEmpty(), "找不到目标角色[" + name + "]");
        LinkedTreeMap roleVo = roleFilters.get(0);
        String code = roleVo.get("code").toString();
        EruptRole role = eruptDao.queryEntity(EruptRole.class, "code='" + code + "'");
        AssertUtils.notNull(role, "找不到目标角色[" + code + "]");
        return role;
    }

    @Transactional
    public void checkUpdateForShareRolesFromEmployee(EmployeeView employee, EruptUser eruptUser) {
        EruptRole employeeRole = this.getIsolateRoleFromEmployee(employee, eruptUser, false);
        eruptDao.getEntityManager().clear();
        EmployeeInformation prevEmployee = eruptDao.queryEntity(EmployeeInformation.class, "id=" + employee.getId());
        String prevRole = prevEmployee.getOwnerRole();
        String currRole = employee.getOwnerRole();
        Set<String> prevRoles = Arrays.stream(prevRole.split("\\|")).collect(Collectors.toSet());
        Set<String> currRoles = Arrays.stream(currRole.split("\\|")).collect(Collectors.toSet());
        Set<String> needAdds = new HashSet<>();
        Set<String> needRemoves = new HashSet<>();
        AtomicBoolean needUpdate = new AtomicBoolean(false);
        //删除了什么
        prevRoles.forEach(role -> {
            if (!currRoles.contains(role) && !needRemoves.contains(role)) {
                needRemoves.add(role);
                log.info(employee.getName() + " 移除了角色 -> " + role);
            }
        });
        //新增了什么
        currRoles.forEach(role -> {
            if (!prevRoles.contains(role) && !needAdds.contains(role)) {
                needAdds.add(role);
                log.info(employee.getName() + " 新增了角色 - >" + role);
                needUpdate.set(true);
            }
        });

        //当前角色原来所有的菜单code
        Set<String> employeeCodes = employeeRole.getMenus().stream().collect(Collectors.toMap(EruptMenu::getCode, menu -> menu)).keySet();

        //计算剩下多少角色
        needRemoves.forEach(role -> {
            if (currRoles.contains(role)) currRoles.remove(role);
        });
        //根据剩下的角色提取合并的所有权限
        Set<String> leftCodes = new HashSet<>();
        if (!needRemoves.isEmpty()) {
            currRoles.forEach(roleName -> {
                if (roleName.equals(EmployeeView.DEFAULT_ROLE)) {
                    //如果是市级或区县用户，基准角色进行替换
                    if (DaliangangContext.isDepartmentAdmin()) roleName = EmployeeView.DEFAULT_ROLE_DEPARTMENT;
                    if (DaliangangContext.isDepartmentArea()) roleName = EmployeeView.DEFAULT_ROLE_AREA;
                }
                //先搜全局的
                EruptRole role = eruptDao.queryEntity(EruptRole.class, "name='" + roleName + "' and reserved=1");
                if (role == null) {
                    //如果全局搜不到，根据名字和企业的orgCode搜索企业的
                    role = eruptDao.queryEntity(EruptRole.class, "name='" + roleName + "' and org_code='" + employee.getOrgCode() + "'");
                }
                if (role != null) {
                    role.getMenus().forEach(menu -> {
                        if (!leftCodes.contains(menu.getCode())) leftCodes.add(menu.getCode());
                    });
                }
            });
        }

        //实际移除操作
        needRemoves.forEach(role -> {
            Set<String> removeCodes = new HashSet<>();
            EruptRole removeRole = this.getShareRoleBySelectedRoleName(role);
            StringBuffer removeMenus = new StringBuffer();
            removeRole.getMenus().forEach(menu -> {
                //要去除的角色权限中，只有不包含在剩下权限列表里的权限才可删除，只要该菜单被另外的角色引用则不可删除
                if (!leftCodes.contains(menu.getCode())) {
                    removeMenus.append(/*"\n" +*/ menu.getName() + "[" + menu.getCode() + "]");
                    removeCodes.add(menu.getCode());
                }
            });
            log.info("要删除的菜单为 -> {" + removeMenus.toString() + "}");
            //Set<String> removeCodes = removeRole.getMenus().stream().collect(Collectors.toMap(EruptMenu::getCode, menu -> menu)).keySet();
            StringBuffer sb = new StringBuffer();
            removeCodes.forEach(menuCode -> {
                if (employeeCodes.contains(menuCode)) {
                    List<EruptMenu> menus = employeeRole.getMenus().stream().filter(menu -> menu.getCode().equals(menuCode)).collect(Collectors.toList());
                    if (!menus.isEmpty()) {
                        EruptMenu removeMenu = menus.get(0);
                        employeeRole.getMenus().remove(removeMenu);
                        sb.append(/*"\n" +*/ removeMenu.getCode() + "[" + removeMenu.getName() + "]");
                        needUpdate.set(true);
                    }
                }
            });
            log.info(employee.getName() + " 移除了菜单 -> {" + sb.toString() + "}");
        });

        //实际添加操作
        needAdds.forEach(role -> {
            EruptRole addRole = this.getShareRoleBySelectedRoleName(role);
            StringBuffer sb = new StringBuffer();
            addRole.getMenus().forEach(menu -> {
                if (!employeeCodes.contains(menu.getCode())) {
                    employeeRole.getMenus().add(menu);
                    sb.append(/*"\n" +*/ menu.getCode() + "[" + menu.getName() + "]");
                    needUpdate.set(true);
                }
            });
            log.info(employee.getName() + " 新增了菜单 -> {" + sb.toString() + "}");
        });

        if (needUpdate.get())
            eruptDao.merge(employeeRole);
        eruptUserService.flushEruptUserCache(eruptUser);
    }

    @Override
    @Transactional
    public void beforeUpdate(EmployeeView employee) {
        //获取修改前员工电话，和现在的的电话进行对比,AfterPhone 字段只有在修改手机号码才会有值
        EmployeeInformation beforeEmployee = EruptDaoUtils.selectOne("select * from tb_employee_information where id = " + employee.getId(),EmployeeInformation.class);
        if(!beforeEmployee.getPhone().equals(employee.getPhone())){
            employee.setAfterPhone(employee.getPhone());
            employee.setPhone(beforeEmployee.getPhone());
        }

        //添加默认角色-员工
        this.addEmployeeRole(employee);
        //设置是否检查人员的值
        this.setCheckPersonValue(employee);

        String phone = employee.getAfterPhone();
        if (StringUtils.isNotEmpty(phone) && !Validator.isMobile(phone))
            NotifyUtils.showErrorMsg("你输入的手机号格式不正确！");
        checkMultiPost(employee);
        checkIsolateRole(employee);

        //如果不选中了哪些共享类角色，需要从隔离角色中移除对应的权限，反之应该添加
        EruptUser eruptUser = eruptUserService.findEruptUserByAccount(employee.getPhone());
        this.checkUpdateForShareRolesFromEmployee(employee, eruptUser);

//        EruptUser eruptUser = eruptUserService.findEruptUserByAccount(employee.getPhone());
//        //从共享角色里抽离
//        EmployeeInformation prev = EruptDaoUtils.selectOne("select * from tb_employee_information where id=" + employee.getId(), EmployeeInformation.class);
//        Set<String> prevShares = this.checkShareRoles(prev);
//        Set<String> currShares = this.checkShareRoles(employee);
//
//        //如果原来有现在没有，要删除
//        prevShares.forEach(role -> {
//            if (!currShares.contains(role)) {
//                eruptRoleService.removeShareRole(eruptUser, role);
//            }
//        });
//
//        //如果现在有原来没有，要加入
//        currShares.forEach(role -> {
//            if (!prevShares.contains(role)) {
//                EruptUser eruptUser1 = eruptRoleService.addShareRole(eruptUser, role);
//            }
//        });

        //定位隔离角色
//        EruptRole tempRole = EruptDaoUtils.selectOne("select * from e_upms_role where code like '%" + eruptUser.getPhone() + "%'", EruptRole.class);
//        EruptRoleTemplate prevTemp = this.checkIsolateRole(prev);
//        EruptRoleTemplate currTemp = this.checkIsolateRole(employee);
//        if (currTemp == null) {
//            //如果现在从隔离角色里出来了，要删除
//            if (tempRole != null) {
//                String roleSql = "delete from u_upms_role where id=" + tempRole.getId();
//                String roleUserSql = "delete from u_upms_user_role where role_id=" + tempRole.getId();
//                EruptDaoUtils.updateNoForeignKeyChecks(roleSql, roleUserSql);
//            }
//        }
//        //如果切换基准角色了，要重复制菜单
//        if (tempRole != null && currTemp != null && currTemp.getBaseRole().equals(prevTemp.getBaseRole())) {
//            //String resetSql = "delete from u_upms_user_role where role_id=" + tempRole.getId();
//            EruptRole baseRole = eruptDao.queryEntity(EruptRole.class, "code='" + currTemp.getBaseRole() + "'");
//            EruptRole employeeRole = eruptUser.getRoles().stream().filter(role -> role.getCode().contains(eruptUser.getPhone())).collect(Collectors.toList()).get(0);
//            employeeRole.getMenus().clear();
//            baseRole.getMenus().forEach(menu -> employeeRole.getMenus().add(menu));
//            eruptDao.merge(baseRole);
//        }
//        //如果原来没有基准角色，要新建一个隔离角色
//        if (tempRole == null && currTemp != null) {
//            EruptRole newRole = new EruptRole();
//        }
    }

    /**
     * 新增【员工】角色
     *
     * @param employee
     */
    private void addEmployeeRole(EmployeeView employee) {
        String ownerRole = employee.getOwnerRole();
        if (StringUtils.isNotEmpty(ownerRole)) {
            if (!ownerRole.contains(EmployeeView.DEFAULT_ROLE)) {
                employee.setOwnerRole(EmployeeView.DEFAULT_ROLE + "|" + ownerRole);
            }
        } else {
            employee.setOwnerRole(EmployeeView.DEFAULT_ROLE);
        }

    }

    /**
     * 设置 是否检查人员 的值，用于同步数据
     *
     * @param employee
     */
    private void setCheckPersonValue(EmployeeView employee) {
        employee.setCheckPerson(employee.getOwnerRole().contains(EmployeeView.LAW_ENFORCEMENT_INSPECTORS_ROLE));
    }

    private EruptRoleTemplate checkIsolateRole(EmployeeView employee) {
        //员工选择的角色，根据这个角色的基准数据来判断是加入还是隔离
        //List<LinkedTreeMap> roleVos = (List<LinkedTreeMap>) MetaContext.getAttribute(EmployeeView.EMPLOYEE_FETCH_ROLES);
        List<LinkedTreeMap> roleVos = EmployeeView.getCacheRoleVos();
        String employeeRole = EmployeeView.DEFAULT_ROLE;//由于默认赋予员工角色，因此这里的隔离角色就为默认的员工角色（其他可选的都应为共享类不可复制的非隔离角色）

        //如果是市级或区县用户，基准角色进行替换
        if (DaliangangContext.isDepartmentAdmin()) employeeRole = EmployeeView.DEFAULT_ROLE_DEPARTMENT;
        if (DaliangangContext.isDepartmentArea()) employeeRole = EmployeeView.DEFAULT_ROLE_AREA;

        StringTokenizer st = new StringTokenizer(employeeRole, "|");
        EruptRoleTemplate roleTemplate = null;
        while (st.hasMoreTokens()) {
            String roleName = st.nextToken().trim();
            if (roleVos == null) NotifyUtils.showErrorDialog("请求已过期，请重新刷新页面");

            //现在员工不从共享角色里发了，直接查库即可
            EruptRoleTemplate template = EruptDaoUtils.selectOne("select * from e_upms_role_template where name=" + SqlUtils.wrapStr(roleName), EruptRoleTemplate.class);
//            List<LinkedTreeMap> targetRole = roleVos.stream().filter(vo -> vo.get("name").equals(roleName)).collect(Collectors.toList());
//            if (targetRole.isEmpty()) continue;
//            String roleCode = targetRole.get(0).get("code").toString();
//            boolean reserved = (Boolean) targetRole.get(0).get("reserved");
//            if (!reserved) continue;
            //如果是系统保留角色，查询基准角色，看看要不要复制
//            EruptRoleTemplate template = EruptDaoUtils.selectOne("select * from e_upms_role_template where base_role=" + SqlUtils.wrapStr(roleCode), EruptRoleTemplate.class);
            if (template == null) continue;
            if (template.isDuplicateRole() && roleTemplate != null) NotifyUtils.showErrorDialog("只能选择一个基准角色");
            roleTemplate = template;
        }
        if (roleTemplate == null)
//            NotifyUtils.showErrorDialog("必须选择一个基准角色，如[" + (DaliangangContext.isDepartmentUser() ? "安全检查人员" : "企业员工") + "]");
            NotifyUtils.showErrorDialog("员工角色必须选择，不可删除！！！");
        return roleTemplate;
    }

    //查询员工所选的角色中，哪些是共享角色
    private Set<String> checkShareRoles(EmployeeView employee) {
        Set<String> shares = new HashSet<>();
        //List<LinkedTreeMap> roleVos = (List<LinkedTreeMap>) MetaContext.getAttribute(EmployeeView.EMPLOYEE_FETCH_ROLES);
        List<LinkedTreeMap> roleVos = EmployeeView.getCacheRoleVos();
        StringTokenizer st = new StringTokenizer(employee.getOwnerRole(), "|");
        while (st.hasMoreTokens()) {
            if (roleVos == null) NotifyUtils.showErrorDialog("请求已过期，请重新刷新页面");
            String roleName = st.nextToken().trim();
            //员工公共角色不加入
            if (roleName.equals(EmployeeView.DEFAULT_ROLE))
                continue;
            List<LinkedTreeMap> targetRole = roleVos.stream().filter(vo -> vo.get("name").equals(roleName)).collect(Collectors.toList());
            if (targetRole.isEmpty()) continue;
            //原来只添加共享角色，现在只要能选上，就添加上
//            boolean share = (Boolean) targetRole.get(0).get("share");
//            if (share) {
            String roleCode = targetRole.get(0).get("code").toString();
            shares.add(roleCode);
//            }
        }
        return shares;
    }

    @Override
    public void afterUpdate(EmployeeView employee) {
        //EruptOrg org = eruptDao.queryEntity(EruptOrg.class,"code="+ SqlUtils.wrapStr(employeeInformation.getOrgCode()));
        EruptUser eruptUser = eruptUserService.findEruptUserByAccount(employee.getPhone());
        //修改用户姓名
        eruptUser.setName(employee.getName());
        eruptDao.merge(eruptUser);
        eruptUserService.flushEruptUserCache(eruptUser);

        //若修改之前的电话号不为空，则说明修改电话了，则需要修改员工账号为修改后的手机号码,并把员工之后的电话号码置空
        if(StringUtils.isNotEmpty(employee.getAfterPhone())){
            platformService.updatePhone(eruptUser.getAccount(),employee.getAfterPhone(),true);
            //修改隔离角色名称和code
            String sql = "update e_upms_role set code =replace(code,'%s','%s') ,name  =replace(name,'%s','%s') " +
                    "where code like '%"+employee.getPhone()+"%';";

            sql = String.format(sql,employee.getPhone(),employee.getAfterPhone(),employee.getPhone(),employee.getAfterPhone());
            EruptDaoUtils.updateNoForeignKeyChecks(sql);

            EruptRoleTemplateUser beforeUser = eruptRoleTemplateUserService.queryByAccount(employee.getPhone());
            sql = "update e_upms_role_template_user set account =replace(account,'%s','%s') " +
                    "where account = '"+employee.getPhone()+"';";
            sql = String.format(sql,employee.getPhone(),employee.getAfterPhone());
            EruptDaoUtils.updateNoForeignKeyChecks(sql);

            eruptRoleTemplateUserService.flushCache(String.valueOf(beforeUser.getId()));

//            eruptUser.setAccount(employee.getAfterPhone());
            employee.setPhone(employee.getAfterPhone());
            employee.setAfterPhone(null);
            eruptDao.merge(employee);

        }



        //当前菜单不为员工信息时
        EruptMenu menu = EruptSpringUtil.getBean(EruptContextService.class).getCurrentEruptMenu();
        if (menu != null && !(menu.getCode().equalsIgnoreCase("EmployeeSelfInfo"))) {
            //更新角色选择状态
            //platformService.setShareRoles((EmployeeInformation) employeeInformation, eruptUser);

            //员工选择的角色，根据这个角色的基准数据来判断是加入还是隔离
//            EruptRoleTemplate roleTemplate = this.checkIsolateRole(employee);
//            platformService.afterUpdate(employee.getPhone(),
//                    employee.getName(), null, roleTemplate);
        }
//        eruptUserService.flushEruptUserCache(eruptUser);
        EruptDeptTemplate eruptDeptTemplate = eruptDao.queryEntity(EruptDeptTemplate.class, " id = '" + employee.getDepartment().getId() + "'");
        UserSyncEntity userSync = UserSyncEntity.builder()
                .id(eruptUser.getId())
                .name(employee.getName())
                .orgCode(employee.getOrgCode())
                .phone(employee.getPhone())
                .sex(employee.getSex())
                .state(employee.getState())
                .posts(employee.getJobTitle())
                .manager(employee.getManager())
                .department(eruptDeptTemplate.getName())
                .code(eruptDeptTemplate.getCode())
                .updateFlag(true)
                .delFlag(false)
                .build();
        redisMQService.produce(RedisMQConst.DEFAULT_TOPIC + "_USER", userSync);
    }

    @Override
    public void afterDelete(EmployeeView employeeInformation) {
        EruptUserVo eruptUser = eruptDao.queryEntity(EruptUserVo.class, "account='" + employeeInformation.getPhone() + "'");
        try{
            platformService.afterDelete(employeeInformation.getPhone());
        }catch (Exception e){
            e.printStackTrace();
        }

        //更新角色选择状态
        //EruptUser eruptUser = eruptUserService.findEruptUserByAccount(employeeInformation.getPhone());
//        String sql = "select * from e_upms_user where account='" + employeeInformation.getPhone() + "'";
//        EruptUser eruptUser = EruptDaoUtils.selectOne(sql, EruptUser.class);

        UserSyncEntity userSync = UserSyncEntity.builder()
                .id(eruptUser.getId())
                .name(employeeInformation.getName())
                .orgCode(employeeInformation.getOrgCode())
                .phone(employeeInformation.getPhone())
                .sex(employeeInformation.getSex())
                .state(employeeInformation.getState())
                .posts(employeeInformation.getJobTitle())
                .updateFlag(true)
                .delFlag(true)
                .build();
        redisMQService.produce(RedisMQConst.DEFAULT_TOPIC + "_USER", userSync);
    }

    private void checkMultiPost(EmployeeView employeeInformation) {
        String jobTitle = employeeInformation.getJobTitle();
        if (StringUtils.isNotEmpty(jobTitle)) {
            //判断是否选择了多个排它的岗位
            List<EruptRoleTemplatePost> eruptRoleTemplatePosts = eruptRoleTemplatePostService.queryExclusivePostCount(jobTitle.split("\\|"));
            if (eruptRoleTemplatePosts.size() > 1) {
                String s = StringUtils.join(eruptRoleTemplatePosts.stream().map(EruptRoleTemplatePost::getName).toArray(), ",");
                NotifyUtils.showWarnDialog(s + "  岗位只能选择一个");
            }
        }
    }

    @Override
    public void searchCondition(Map<String, Object> condition) {
        EruptUser user = eruptUserService.getCurrentEruptUser();
        if (user != null && user.getEruptOrg() != null)
            condition.put("company", user.getEruptOrg().getCode());
    }

    @Override
    public void editBehavior(EmployeeView employeeView) {
        String jobTitle = employeeView.getJobTitle();
        if (jobTitle != null) {
            if (jobTitle.contains("|")) {
                String[] split = jobTitle.split("\\|");
                ArrayList<String> nameList = new ArrayList<>();
                for (String s : split) {
                    String selectSql = "select distinct name from e_upms_post_template where code=" + SqlUtils.wrapStr(s);
                    EruptResultMap eruptResultMap = EruptDaoUtils.selectMap(selectSql);
                    nameList.add((String) eruptResultMap.get("name"));

                }
                String job = nameList.stream().collect(Collectors.joining("|"));
                employeeView.setJobTitle(job);
            } else {
                String selectSql = "select * from e_upms_post_template where code=" + SqlUtils.wrapStr(jobTitle);
                EruptRoleTemplatePost templatePost = EruptDaoUtils.selectOne(selectSql, EruptRoleTemplatePost.class);
                if (templatePost != null) {
                    employeeView.setJobTitle(templatePost.getName());
                } else {
                    employeeView.setJobTitle("");

                }

            }


        }
    }

    //校验数据
    public void judgeData(EmployeeView employeeView) {
        //获取当前用户
        EruptUser currentEruptUser = eruptUserService.getCurrentEruptUser();
        //查询部门
        String departmentSql = "select * from e_upms_template_dept where org_code=" + SqlUtils.wrapStr(currentEruptUser.getEruptOrg().getCode());
        List<EruptDeptTemplate> eruptDeptTemplates = EruptDaoUtils.selectOnes(departmentSql, EruptDeptTemplate.class);
        //部门Map
        Map<Long, String> deptTemplateMap = eruptDeptTemplates.stream().collect(Collectors.toMap(
                EruptDeptTemplate::getId,
                EruptDeptTemplate::getName,
                (d1, d2) -> d1
        ));
        Map<String, Long> deptTemplateNameMap = eruptDeptTemplates.stream().collect(Collectors.toMap(
                EruptDeptTemplate::getName,
                EruptDeptTemplate::getId,
                (d1, d2) -> d1
        ));
        if (employeeView.getDepartment() != null && !(deptTemplateMap.containsKey(employeeView.getDepartment().getId()))) {
            boolean flag = false ;
            //查找是否有对应的部门
            EruptDeptTemplate eruptDeptTemplate = eruptDao.queryEntity(EruptDeptTemplate.class, " id = " + employeeView.getDepartment().getId());
            for (String deptName : deptTemplateNameMap.keySet()) {
                if(deptName.equals(eruptDeptTemplate.getName())){
                    EruptDeptTemplate currentDept = eruptDao.queryEntity(EruptDeptTemplate.class, " id = " +deptTemplateNameMap.get(deptName));
                    employeeView.setDepartment(currentDept);
                    flag = true ;
                }
            }
            if(!flag) {
                NotifyUtils.showErrorDialog("员工【" + employeeView.getName() + "】的部门不存在！请核对后重新导入！");
            }

        }
        //查询角色
        String roleSql = "select * from e_upms_role";
        List<EruptRole> companyRoles = EruptDaoUtils.selectOnes(roleSql, EruptRole.class);
        //角色map
        Map<String, String> rolesMap = companyRoles.stream().collect(Collectors.toMap(
                EruptRole::getName,
                EruptRole::getCode,
                (r1, r2) -> r1
        ));
        if (employeeView.getOwnerRole() != null) {
            String[] roleArr = employeeView.getOwnerRole().split("\\|");
            for (String role : roleArr) {
                if (!rolesMap.containsKey(role)) {
                    NotifyUtils.showErrorDialog("员工【" + employeeView.getName() + "】的角色【" + role + "】不存在！请核对后重新导入！");
                }
            }
        }
        //查询岗位
        String jobSql = "select * from e_upms_post_template";
        List<EruptRoleTemplatePost> eruptRoleTemplatePosts = EruptDaoUtils.selectOnes(jobSql, EruptRoleTemplatePost.class);
        Map<String, String> jobMap = eruptRoleTemplatePosts.stream().collect(Collectors.toMap(
                EruptRoleTemplatePost::getCode,
                EruptRoleTemplatePost::getName,
                (t1, t2) -> t1
        ));
        if (employeeView.getJobTitle() != null) {
            String[] jobs = employeeView.getJobTitle().split("\\|");
            for (String job : jobs) {
                if (!jobMap.containsKey(job)) {
                    NotifyUtils.showErrorDialog("员工【" + employeeView.getName() + "】的岗位【" + job + "】不存在！请核对后重新导入！");
                }
            }
        }
    }
}
