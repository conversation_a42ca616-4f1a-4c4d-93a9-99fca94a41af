/*
 * Copyright © 2020-2035 yundingyun All rights reserved.
 */
package com.daliangang.workbench.entity;

import com.daliangang.core.DaliangangContext;
import com.daliangang.workbench.service.PlatformService;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import xyz.erupt.annotation.Erupt;
import xyz.erupt.annotation.EruptField;
import xyz.erupt.annotation.expr.ExprBool;
import xyz.erupt.annotation.fun.DataProxy;
import xyz.erupt.annotation.query.Condition;
import xyz.erupt.annotation.sub_erupt.Power;
import xyz.erupt.annotation.sub_field.Edit;
import xyz.erupt.annotation.sub_field.EditType;
import xyz.erupt.annotation.sub_field.Readonly;
import xyz.erupt.annotation.sub_field.View;
import xyz.erupt.annotation.sub_field.sub_edit.ReferenceTreeType;
import xyz.erupt.annotation.sub_field.sub_edit.Search;
import xyz.erupt.core.util.EruptSpringUtil;
import xyz.erupt.toolkit.db.Comment;
import xyz.erupt.toolkit.utils.SqlUtils;
import xyz.erupt.upms.model.EruptUser;
import xyz.erupt.upms.service.EruptUserService;

import javax.persistence.*;
import java.util.List;
import java.util.Set;

@Erupt(name = "企业信息", closeTreeView = true, power = @Power(edit = true, export = true, importable = true, powerHandler = EnterpriseInfo.class),
        dataProxy = EnterpriseInfo.class, rowOperation = {
        //@RowOperation(confirm = false, title = "导入附证", icon = "fa fa-arrow-down", operationHandler = EnterpriseInfoImportAttachedCertificateHandler.class, mode = RowOperation.Mode.BUTTON),
        //@RowOperation(title = "导出附证", icon = "fa fa-arrow-up", operationHandler = EnterpriseExportAttachedCertificateHandler.class, mode = RowOperation.Mode.BUTTON),
        //@RowOperation(confirm = false, show = @ExprBool(exprHandler = EnterpriseInfo.class), title = "启用", icon = "fa fa-toggle-off", ifExpr = "item.state=='禁用'", ifExprBehavior = RowOperation.IfExprBehavior.HIDE, operationHandler = EnterpriseEnabledHandler.class, operationParam = "on", mode = RowOperation.Mode.MULTI),
        //@RowOperation(confirm = false, show = @ExprBool(exprHandler = EnterpriseInfo.class), title = "禁用", icon = "fa fa-toggle-on", ifExpr = "item.state=='启用'", ifExprBehavior = RowOperation.IfExprBehavior.HIDE, operationHandler = EnterpriseEnabledHandler.class, operationParam = "off", mode = RowOperation.Mode.MULTI),
        //@RowOperation(title = "重置密码", icon = "fa fa-key", operationHandler = EnterpriseResetPasswordHandler.class, mode = RowOperation.Mode.SINGLE),
})
@Table(name = "tb_enterprise", uniqueConstraints = @UniqueConstraint(columnNames = "name"))
@Entity
@Getter
@Setter
@Comment("企业信息")
@ApiModel("企业信息")
@Slf4j
public class EnterpriseInfo extends EnterpriseView implements DataProxy<EnterpriseInfo> {

    @EruptField(
            views = @View(title = "主管部门", column = "name", show = true, ifRender = @ExprBool(value = false)),
            edit = @Edit(title = "主管部门", readonly = @Readonly(exprHandler = EnterpriseInfo.class), type = EditType.REFERENCE_TREE, notNull = true, search = @Search(vague = true),
                    referenceTreeType = @ReferenceTreeType(pid = "parent.id", label = "name")))
    @ManyToOne
    @Comment("主管部门")
    @ApiModelProperty("主管部门")
    private Department imDepartment;

    @EruptField(
            edit = @Edit(title = "企业附证", type = EditType.TAB_TABLE_ADD))
    @OneToMany(cascade = CascadeType.ALL, orphanRemoval = true)
    @OrderBy
    @JoinColumn(name = "enterprise_id")
    @Comment("企业附证")
    @ApiModelProperty("企业附证")
    private Set<EnterpriseCertificate> certificates;

    @Override
    public String beforeFetch(List<Condition> conditions) {
        if (EruptSpringUtil.getBean(EruptUserService.class).getCurrentEruptUser().getIsAdmin())
            return null;
        if (DaliangangContext.isEnterpriseUser() || DaliangangContext.isEnterpriseEmployee()){
            EruptUser eruptUser = EruptSpringUtil.getBean(EruptUserService.class).getCurrentEruptUser();
            return "EnterpriseInfo.orgCode = " + SqlUtils.wrapStr(eruptUser.getEruptOrg().getCode() );
        }

        return null;
    }

    @Override
    public void beforeUpdate(EnterpriseInfo enterpriseInfo) {
        Set<EnterpriseCertificate> certificates1 = enterpriseInfo.getCertificates();
        if(null != certificates1){
            enterpriseInfo.getCertificates().forEach(item->{
                if(StringUtils.isEmpty(item.getCompany())){
                    item.setCompany(enterpriseInfo.getOrgCode());
                }
            });
        }

    }

    @Override
    public void afterUpdate(EnterpriseInfo enterpriseInfo) {
        PlatformService platformService = EruptSpringUtil.getBean(PlatformService.class);
        platformService.updatePhone(enterpriseInfo.getAdministrator(),enterpriseInfo.getPhone(),false) ;

    }
}
