package com.daliangang.majorisk.entity;

import com.daliangang.core.DaliangangContext;
import com.daliangang.majorisk.operation.RiskDatabaseAddCategoryHandler;
import com.daliangang.majorisk.operation.RiskDatabaseDeployHandler;
import com.daliangang.majorisk.operation.RiskDatabaseSelectHandler;
import com.daliangang.majorisk.proxy.RiskDatabaseCompanyDataProxy;
import com.daliangang.majorisk.proxy.RiskDatabaseDepartmentDataProxy;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import xyz.erupt.annotation.Erupt;
import xyz.erupt.annotation.EruptField;
import xyz.erupt.annotation.config.Comment;
import xyz.erupt.annotation.expr.ExprBool;
import xyz.erupt.annotation.sub_erupt.Power;
import xyz.erupt.annotation.sub_erupt.RowOperation;
import xyz.erupt.annotation.sub_field.Edit;
import xyz.erupt.annotation.sub_field.EditType;
import xyz.erupt.annotation.sub_field.View;
import xyz.erupt.annotation.sub_field.sub_edit.BoolType;
import xyz.erupt.annotation.sub_field.sub_edit.Search;

import javax.persistence.Entity;
import javax.persistence.Table;
import javax.persistence.Transient;

@Erupt(name = "重大风险数据库", importTruncate = true, power = @Power(add = true, delete = true, export = true, importable = true, viewDetails = true, edit = true),
        dataProxy = RiskDatabaseCompanyDataProxy.class,
        rowOperation = {
//                @RowOperation(title = "添加分类", icon = "fa fa-address-book", operationHandler = RiskDatabaseAddCategoryHandler.class, mode = RowOperation.Mode.BUTTON),
//                @RowOperation(title = "发布", icon = "fa fa-arrow-circle-up", ifExpr = "item.state=='未发布'", ifExprBehavior = RowOperation.IfExprBehavior.HIDE, operationHandler = RiskDatabaseDeployHandler.class, mode = RowOperation.Mode.MULTI, operationParam = "true"),
//                @RowOperation(title = "不发布", icon = "fa fa-arrow-circle-down", ifExpr = "item.state=='已发布'", ifExprBehavior = RowOperation.IfExprBehavior.HIDE, operationHandler = RiskDatabaseDeployHandler.class, mode = RowOperation.Mode.SINGLE, operationParam = "false"),
                @RowOperation(title = "添加到企业数据库",
                        icon = "fa fa-plus-square",
                        eruptClass = RiskDatabaseSelectHandler.EnterpriseSelectionForm.class,
                        operationHandler = RiskDatabaseSelectHandler.class,
                        mode = RowOperation.Mode.MULTI
                )
        })
@Table(name = "tb_risk_database_department")
@Entity
@Getter
@Setter
public class RiskDatabaseCompany extends RiskDatabaseCommon implements ExprBool.ExprHandler{

    @EruptField(
            views = @View(title = "状态", sortable = true),
            edit = @Edit(title = "状态", type = EditType.BOOLEAN, notNull = true, search = @Search(vague = true),
                    boolType = @BoolType(trueText = "已发布", falseText = "未发布")))
    @Comment("状态")
    @ApiModelProperty("状态")
    private Boolean state;

    @EruptField(edit = @Edit(title = "div1", type = EditType.EMPTY))
    @Transient
    private String div1;

    @EruptField(edit = @Edit(title = "div2", type = EditType.EMPTY))
    @Transient
    private String div2;

    @Override
    public boolean handler(boolean expr, String[] params) {
        return DaliangangContext.isEnterpriseUser();
    }
}
