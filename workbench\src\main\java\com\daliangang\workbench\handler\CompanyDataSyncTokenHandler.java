package com.daliangang.workbench.handler;

import com.google.gson.Gson;
import com.google.gson.JsonObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import xyz.erupt.core.config.GsonFactory;
import xyz.erupt.openapi.core.OpenApi;
import xyz.erupt.openapi.core.OpenApiHandler;
import xyz.erupt.openapi.core.OpenApiModel;
import xyz.erupt.toolkit.db.EruptResultMap;
import xyz.erupt.upms.base.LoginModel;
import xyz.erupt.upms.controller.EruptUserController;
import xyz.erupt.upms.model.EruptUser;
import xyz.erupt.upms.service.EruptUserService;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

@Service
@Slf4j
@OpenApi(api = "companyDataSyncGetToken",name = "智慧云获取token",handler = CompanyDataSyncTokenHandler.class)
public class CompanyDataSyncTokenHandler  implements OpenApiHandler {

    @Resource
    private EruptUserService eruptUserService ;
    @Resource
    private EruptUserController eruptUserCtrl;

    @Override
    public OpenApiModel exec(JsonObject request, String params) {
        log.info("接收智慧云token原始数据 -> " + request);
        Gson gson = GsonFactory.getGson();
        Map<String,Object> map = gson.fromJson(request, Map.class);
        String company = (String)map.get("company");
        List<String> paramsList = Arrays.asList(params.split(","));
        //获取哪些企业能登录
        if(!paramsList.contains(company)){
            EruptResultMap vo = new EruptResultMap();
            vo.put("消息", "用户不存在");
            vo.put("时间", LocalDateTime.now());
            return new OpenApiModel().setData(vo);
        }
        EruptUser eruptUser = eruptUserService.findEruptUserByAccount((String) company);
        if (eruptUser == null) {
            EruptResultMap vo = new EruptResultMap();
            vo.put("消息", "用户不存在");
            vo.put("时间", LocalDateTime.now());
            return new OpenApiModel().setData(vo);
        }
        //强制登录
        LoginModel loginModel = eruptUserCtrl.login(eruptUser);
        log.info("返回智慧云token -> " + loginModel.getToken());
        EruptResultMap vo = new EruptResultMap();
        vo.put("token", loginModel.getToken());
        vo.put("时间", LocalDateTime.now());
        return new OpenApiModel().setData(vo);
    }
}
