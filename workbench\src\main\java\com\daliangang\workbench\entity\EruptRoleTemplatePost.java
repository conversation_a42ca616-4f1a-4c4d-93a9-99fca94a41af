package com.daliangang.workbench.entity;

import com.daliangang.workbench.proxy.EruptRoleTemplatePostProxy;
import lombok.Getter;
import lombok.Setter;
import org.springframework.stereotype.Component;
import xyz.erupt.annotation.Erupt;
import xyz.erupt.annotation.EruptField;
import xyz.erupt.annotation.config.Comment;
import xyz.erupt.annotation.constant.AnnotationConst;
import xyz.erupt.annotation.sub_field.Edit;
import xyz.erupt.annotation.sub_field.EditType;
import xyz.erupt.annotation.sub_field.Readonly;
import xyz.erupt.annotation.sub_field.View;
import xyz.erupt.annotation.sub_field.sub_edit.ChoiceType;
import xyz.erupt.annotation.sub_field.sub_edit.NumberType;
import xyz.erupt.annotation.sub_field.sub_edit.Search;
import xyz.erupt.core.util.EruptSpringUtil;
import xyz.erupt.jpa.model.MetaModel;
import xyz.erupt.toolkit.handler.SqlChoiceFetchHandler;
import xyz.erupt.upms.handler.DictCodeChoiceFetchHandler;
import xyz.erupt.upms.model.EruptPost;
import xyz.erupt.upms.model.EruptUser;
import xyz.erupt.upms.service.EruptUserService;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

/**
 * @Author：chb
 * @Package：xyz.erupt.upms.model.template
 * @Project：erupt
 * @name：EruptPostTemplate
 * @Date：2023/3/7 14:27
 * @Filename：EruptPostTemplate
 */
@Erupt(name = "用户岗位设置", orderBy = "weight desc", dataProxy = EruptRoleTemplatePostProxy.class)
@Entity
@Getter
@Setter
@Table(name = "e_upms_post_template")
@Comment("用户岗位设置")
public class EruptRoleTemplatePost extends MetaModel implements Readonly.ReadonlyHandler {

    @Column(length = AnnotationConst.CODE_LENGTH)
    @EruptField(
            views = @View(title = "岗位编码", show = false, sortable = true),
            edit = @Edit(title = "岗位编码", show = false, notNull = true)
    )
    private String code;

    @EruptField(
            views = @View(title = "岗位名称", sortable = true),
            edit = @Edit(title = "岗位名称", notNull = true, search = @Search(vague = true))
    )
    private String name;

    @Component
    public static class PostTypeReadOnlyHandler implements Readonly.ReadonlyHandler {

        @Override
        public boolean add(boolean add, String[] params) {
            EruptUserService eruptUserService = EruptSpringUtil.getBean(EruptUserService.class);
            EruptUser eruptUser = eruptUserService.getCurrentEruptUser();
            if (eruptUser != null && eruptUser.getIsAdmin()) return false;
            return true;
        }

        @Override
        public boolean edit(boolean edit, String[] params) {
            return this.add(edit, params);
        }
    }

    @EruptField(
            views = @View(title = "岗位类别", sortable = true),
            edit = @Edit(
                    title = "岗位类别",
                    desc = "在字典里创建的关于岗位使用场景的标识",
                    type = EditType.CHOICE, readonly = @Readonly(exprHandler = PostTypeReadOnlyHandler.class),
                    choiceType = @ChoiceType(fetchHandler = DictCodeChoiceFetchHandler.class, fetchHandlerParams = EruptPost.POST_TYPE)
            )
    )
    private String type;

    @EruptField(
            views = @View(title = "岗位权重", sortable = true),
            edit = @Edit(title = "岗位权重", type=EditType.NUMBER,numberType = @NumberType(precision = 0),desc = "数值越高，岗位级别越高")
    )
    private Integer weight;

//    @ManyToOne
//    @EruptField(
//            views = @View(title = "所属组织", column = "name"),
//            edit = @Edit(title = "所属组织", ifRender = @ExprBool(exprHandler = EruptRoleTemplateUserRenderHandler.class), type = EditType.REFERENCE_TREE, notNull = true, search = @Search, referenceTreeType = @ReferenceTreeType(pid = "parentOrg.id", label = "name"))
//    )
//    private EruptOrg eruptOrg;

    @EruptField(views = @View(title = "此岗位是否用于培训", sortable = true),
            edit = @Edit(title = "此岗位是否用于培训",
                    readonly = @Readonly(add = false,edit = true),
                    search = @Search, notNull = true,
                    type = EditType.BOOLEAN,
                    desc = "是否用于培训岗位,一经确定，禁止更改"))
    @Comment("此岗位是否用于培训")
    private Boolean exclusive;

    //初始化角色是此值设置为false
    @EruptField(edit = @Edit(title = "是否为保留记录", notNull = false, show = false))
    private Boolean reserved = false;

    @EruptField(views = @View(title = "所属组织"), sort = 1,
            edit = @Edit(title = "所属组织", type = EditType.CHOICE, readonly = @Readonly(exprHandler = EruptRoleTemplatePost.class),
                    //search = @Search,
                    choiceType = @ChoiceType(
                            fullSpan = true,
                            fetchHandler = SqlChoiceFetchHandler.class,
                            fetchHandlerParams = "select code,name from e_upms_org")))
    private String orgCode;

    @Override
    public boolean add(boolean add, String[] params) {
        return this.edit(add, params);
    }

    @Override
    public boolean edit(boolean edit, String[] params) {
        EruptUserService eruptUserService = EruptSpringUtil.getBean(EruptUserService.class);
        EruptUser eruptUser = eruptUserService.getCurrentEruptUser();
        if (eruptUser != null && eruptUser.getIsAdmin()) return false;
        return true;
    }
}
