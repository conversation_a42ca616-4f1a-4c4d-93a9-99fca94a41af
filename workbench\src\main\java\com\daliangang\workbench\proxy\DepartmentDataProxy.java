/*
 * Copyright © 2020-2035 yundingyun All rights reserved.
 */
package com.daliangang.workbench.proxy;

import com.daliangang.workbench.entity.Department;
import com.daliangang.workbench.entity.Enterprise;
import com.daliangang.workbench.service.PlatformService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.stereotype.Service;
import xyz.erupt.annotation.fun.DataProxy;
import xyz.erupt.jpa.dao.EruptDao;
import xyz.erupt.toolkit.utils.AssertUtils;
import xyz.erupt.toolkit.utils.EruptDaoUtils;
import xyz.erupt.toolkit.utils.SqlUtils;
import xyz.erupt.upms.model.EruptOrg;
import xyz.erupt.upms.service.EruptUserService;

import javax.annotation.Resource;
import javax.transaction.Transactional;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

@Service
@Slf4j
public class DepartmentDataProxy implements DataProxy<Department> {
    @Resource
    private EruptDao eruptDao;
    @Resource
    private EruptUserService eruptUserService;
    @Resource
    private PlatformService platformService ;

    @Override
    public Department queryDataById(long id) {
        Department department = eruptDao.queryEntity(Department.class, "id=" + id);
        List<Enterprise> enterprises = EruptDaoUtils.selectOnes("select * from tb_enterprise where im_department_id=" + id, Enterprise.class);
        if (ObjectUtils.isNotEmpty(enterprises)) {
            enterprises.forEach(v -> {
                v.setImDepartment(department);
            });
        }
        Set<Enterprise> enterpriseSet = enterprises.stream().collect(Collectors.toSet());
        department.setEnterprises(enterpriseSet);
        return department;
    }

    @Override
    public void afterFetch(Collection<Map<String, Object>> list) {
        //AnnotationUtil.setPower(Enterprise.class, false, false, false);
    }

    @Override
    public void beforeAdd(Department department) {
        AssertUtils.isNull(getDepartment(department.getName()), department.getName() + "已存在");
        department.setEruptOrgId(0L);
    }

    @Override
    @Transactional
    public void afterAdd(Department department) {
        //同步增加到组织表中
        EruptOrg org = new EruptOrg();
        org.setName(department.getName());
        org.setCode(department.getInitOrgCode());
//        org.randomOrgCode();

//        String parentOrgCode = null; //默认当前部门无父级
//        boolean highestFlag = true; //默认当前部门为市

        if (department.getParent() != null) {
//            highestFlag = false;
            EruptOrg parent = this.getEruptOrg(department.getParent().getName());
            org.setParentOrg(parent);
//            parentOrgCode = parent.getCode();
        }
        EruptOrg ret = eruptDao.merge(org);
        org.setId(ret.getId());
        department.setEruptOrgId(org.getId());
        eruptDao.merge(department);
        // TODO: 2023/5/19 同步新增区县机构
//        OrganSyncEntity organSync = OrganSyncEntity.builder()
//                .orgId(org.getId())
//                .orgName(department.getName())
//                .orgType(highestFlag ? 0 : 1) //若无组织父级 则为市 否则为区县
//                .parentOrgCode(Objects.isNull(parentOrgCode)? null : parentOrgCode)
//                .orgCode(ret.getCode())
//                .updateFlag(false)
//                .delFlag(false)
//                .build();
//        redisMQService.produce(RedisMQConst.DEFAULT_TOPIC + "_ORGAN", organSync);

        //因为没有新增功能，所以这里先不做填写手机号码功能
    }

    @Override
    @Transactional
    public void afterUpdate(Department department) {
        EruptOrg org = this.getEruptOrg(department.getName());
        AssertUtils.notNull(org, "同步组织里的对象为空[" + department.getName() + "]");
        org.setName(department.getName());
        org.setParentOrg(department.getParent() == null ? null : this.getEruptOrg(department.getParent().getName()));
        eruptDao.merge(org);

        //更新手机号码
        platformService.updatePhone(department.getAccount(),department.getPhone(),false);

        //todo 更新区县机构
//        String parentOrgCode = null; //默认当前部门无父级
//        boolean highestFlag = true; //默认当前部门为市

//        if (Objects.nonNull(org.getParentOrg())) {
//            EruptOrg parent = org.getParentOrg();
//            highestFlag = false;
//            parentOrgCode = parent.getCode();
//        }

//        OrganSyncEntity organSync = OrganSyncEntity.builder()
//                .orgId(org.getId())
//                .orgName(department.getName())
//                .orgType(highestFlag ? 0 : 1) //若无组织父级 则为市 否则为区县
//                .parentOrgCode(Objects.isNull(parentOrgCode)? null : parentOrgCode)
//                .orgCode(org.getCode())
//                .updateFlag(true)
//                .delFlag(false)
//                .build();
//        redisMQService.produce(RedisMQConst.DEFAULT_TOPIC + "_ORGAN", organSync);

    }

    @Override
    @Transactional
    public void afterDelete(Department department) {
        EruptOrg org = this.getEruptOrg(department.getName());
        AssertUtils.notNull(org, "同步组织里的对象为空[" + department.getName() + "]");
        eruptDao.delete(org);
//        //todo 同步删除市/区县机构
//        OrganSyncEntity organSync = OrganSyncEntity.builder()
//                .orgId(org.getId())
//                .orgName(department.getName())
//                .orgCode(org.getCode())
//                .updateFlag(true)
//                .delFlag(true) //删除机构
//                .build();
//        redisMQService.produce(RedisMQConst.DEFAULT_TOPIC + "_ORGAN", organSync);

    }

    public EruptOrg getEruptOrg(String name) {
        return eruptDao.queryEntity(EruptOrg.class, "name=" + SqlUtils.wrapStr(name));
    }

    public Department getDepartment(String name) {
        return eruptDao.queryEntity(Department.class, "name=" + SqlUtils.wrapStr(name));
    }


}
