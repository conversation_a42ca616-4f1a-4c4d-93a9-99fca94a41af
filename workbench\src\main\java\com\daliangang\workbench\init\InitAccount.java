package com.daliangang.workbench.init;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import xyz.erupt.excel.model.ExcelHeader;

/**
 * @Author：chb
 * @Package：com.daliangang.workbench.init
 * @Project：erupt
 * @name：InitDepartmentAccount
 * @Date：2023/3/5 22:05
 * @Filename：InitDepartmentAccount
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class InitAccount extends InitTemplate{

    @ExcelHeader(col = 0, name = "登录账号")
    private String account;

    @ExcelHeader(col = 1, name = "用户名称")
    private String name;

    @ExcelHeader(col = 2, name = "所属部门")
    private String department;

    public static InitAccount valueOf(InitTemplate template) {
        return InitAccount.builder().account(template.getParam1())
                .name(template.getParam2())
                .department(template.getParam3()).build();
    }
}
