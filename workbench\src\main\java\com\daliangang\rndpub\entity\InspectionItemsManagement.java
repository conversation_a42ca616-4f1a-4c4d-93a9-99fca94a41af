/*
 * Copyright © 2020-2035 yundingyun All rights reserved.
 */
package com.daliangang.rndpub.entity;

import com.daliangang.rndpub.proxy.InspectionItemsManagementDataProxy;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import xyz.erupt.annotation.Erupt;
import xyz.erupt.annotation.EruptField;
import xyz.erupt.annotation.sub_erupt.Power;
import xyz.erupt.annotation.sub_field.Edit;
import xyz.erupt.annotation.sub_field.EditType;
import xyz.erupt.annotation.sub_field.View;
import xyz.erupt.annotation.sub_field.sub_edit.ChoiceType;
import xyz.erupt.annotation.sub_field.sub_edit.InputType;
import xyz.erupt.annotation.sub_field.sub_edit.Search;
import xyz.erupt.jpa.model.MetaModel;
import xyz.erupt.toolkit.db.Comment;
import xyz.erupt.toolkit.handler.SqlChoiceFetchHandler;

import javax.persistence.Entity;
import javax.persistence.Lob;
import javax.persistence.Table;

@Erupt(name = "检查事项管理", importTruncate = true, power = @Power(add = true, delete = true, export = false, importable = true, viewDetails = true, edit = true)
        , dataProxy = InspectionItemsManagementDataProxy.class
        , rowOperation = {
        //@RowOperation(title = "新增分类", icon = "fa fa-address-book", operationHandler = InspectionItemsManagementAddedClassificationHandler.class, mode = RowOperation.Mode.BUTTON)
})
@Table(name = "tb_inspection_items_management")
@Entity
@Getter
@Setter
@Comment("检查事项管理")
@ApiModel("检查事项管理")
public class InspectionItemsManagement extends MetaModel {

//    @EruptField(
//            views = @View(title = "编号"),
//            edit = @Edit(title = "编号", type = EditType.INPUT, notNull = true,
//                    inputType = @InputType))
//    @Comment("编号")
//    @ApiModelProperty("编号")
//    private Long seriesId;

    @EruptField(
            views = @View(title = "检查事项"),
            edit = @Edit(title = "检查事项", type = EditType.INPUT, notNull = true,
                    inputType = @InputType))
    @Comment("检查事项")
    @ApiModelProperty("检查事项")
    private String inspectionItems;

    @EruptField(
            edit = @Edit(title = " 检查事项 ", type = EditType.CHOICE, search = @Search, show = false,
                    choiceType = @ChoiceType(anewFetch = true, fetchHandler = SqlChoiceFetchHandler.class, fetchHandlerParams = "select distinct inspection_items_search,inspection_items_search from tb_inspection_items_management ")))
    private String inspectionItemsSearch;

    @EruptField(
            views = @View(title = "检查内容一级"),
            edit = @Edit(title = "检查内容一级", type = EditType.INPUT, notNull = true,
                    inputType = @InputType))
    @Comment("检查内容一级")
    @ApiModelProperty("检查内容一级")
    private String checkFirst;

    @EruptField(
            edit = @Edit(title = " 检查内容一级 ", type = EditType.CHOICE, search = @Search, show = false,
                    choiceType = @ChoiceType(anewFetch = true, fetchHandler = SqlChoiceFetchHandler.class, fetchHandlerParams = "select distinct check_first_search,check_first_search from tb_inspection_items_management ")))
    private String checkFirstSearch;

    @EruptField(
            views = @View(title = "检查内容二级"),
            edit = @Edit(title = "检查内容二级", type = EditType.TEXTAREA, notNull = true))
    @Comment("检查内容二级")
    @ApiModelProperty("检查内容二级")
    private @Lob String checkSecond;

    @EruptField(
            views = @View(title = "检查依据", show = false),
            edit = @Edit(title = "检查依据", type = EditType.TEXTAREA, notNull = true))
    @Comment("检查依据")
    @ApiModelProperty("检查依据")
    private @Lob String basis;

    @EruptField(
            views = @View(title = "检查方法"),
            edit = @Edit(title = "检查方法", type = EditType.TEXTAREA, notNull = true))
    @Comment("检查方法")
    @ApiModelProperty("检查方法")
    private @Lob String method;

    @EruptField(
            views = @View(title = "检查标准", show = false),
            edit = @Edit(title = "检查标准", type = EditType.TEXTAREA, notNull = true))
    @Comment("检查标准")
    @ApiModelProperty("检查标准")
    private @Lob String standard;

    @EruptField(
            views = @View(title = "适用企业类型"),
            edit = @Edit(title = "适用企业类型", type = EditType.INPUT, notNull = true))
    @Comment("适用企业类型")
    @ApiModelProperty("适用企业类型")
    private String type;

}
