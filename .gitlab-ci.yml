variables:
  GIT_CEILING_DIRECTORIES: /home/<USER>/builds/
  GIT_STRATEGY: "fetch"

.template: &template
  tags:
    - erupt

stages:
  - build
  - notify

build-job:
  <<: *template
  variables:
    GIT_STRATEGY: "fetch"
  stage: build
  only:
    - /^master/
  script:
    - echo "开始构建...."
    - chown -R $USER:$USER .  # 将所有文件的所有权更改为当前用户
    - chmod -R u+w .          # 确保所有文件对当前用户有写权限
    - echo '当前工作目录:' "$PWD"
    - echo "直接在本地执行构建..."
    - cd workbench && sh build-java.sh && sh build-image.sh $DOCKER_REGISTRY_PWD
