package com.daliangang.safedaily.operation;

import cn.hutool.json.JSONObject;
import com.daliangang.safedaily.entity.MonthlyScheduling;
import org.springframework.stereotype.Service;
import xyz.erupt.annotation.fun.OperationHandler;
import xyz.erupt.common.http.EruptRemoteUtils;
import xyz.erupt.jpa.dao.EruptDao;
import xyz.erupt.toolkit.remote.RemoteProxyService;

import javax.annotation.Resource;
import javax.transaction.Transactional;
import java.util.List;
import java.util.Map;

@Service
public class MonthlySchedulingSubmitHandler implements OperationHandler<MonthlyScheduling,Void> {

    @Resource
    private EruptDao eruptDao;
    @Resource
    private RemoteProxyService remoteProxyService;

    @Override
    @Transactional
    public String exec(List<MonthlyScheduling> data, Void unused, String[] param) {
        data.get(0).setSubmitted(true);
        eruptDao.merge(data.get(0));

        data.forEach(v->{
            //推送数据
            JSONObject inputData = new JSONObject();
            inputData.set("clazz", "MonthlyScheduling");
            inputData.set("insertData",v);
            EruptRemoteUtils.doPost(remoteProxyService.getMainServerUrl() + "/erupt-api/exchange/data/exchange", inputData, Map.class);
        });
        return null;
    }
}
