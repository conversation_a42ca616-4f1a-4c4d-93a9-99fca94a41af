#!/bin/bash
PROJECT_NAME=daliangang-workbench
PROJECT_DESC=大连港主站

echo '当前项目名称为:' "$PROJECT_NAME" - "$PROJECT_DESC"

# 从命令行参数中获取密码
password=$1

# 检查是否提供了密码
if [ -z "$password" ]; then
  echo "Error: 请提供Docker密码作为第一个参数。"
  exit 1
fi

version=$(grep --max-count=1 '<version>' pom.xml | awk -F '>' '{ print $2 }' | awk -F '<' '{ print $1 }')-$(date '+%Y%m%d%H%M%S')
echo "待构建的 $PROJECT_DESC 镜像版本为: $version"

# 确保目标目录存在
mkdir -p ../../target/$PROJECT_NAME
# 修改目标目录权限
chmod 777 -R ../../target/$PROJECT_NAME
# 修改当前目录权限
chmod 777 -R .
# 复制Dockerfile到目标目录
cp -f Dockerfile ../../target/$PROJECT_NAME/ || { echo "复制Dockerfile失败，请检查权限"; exit 1; }

echo "正在构建 $PROJECT_DESC 镜像..."
registry=registry.cn-wulanchabu.aliyuncs.com
imagePro=registry.cn-wulanchabu.aliyuncs.com/ydy-lowcode/$PROJECT_NAME

cd ../../target/$PROJECT_NAME/
echo '当前工作目录为:' "$PWD"
# 检查Dockerfile是否存在
if [ ! -f Dockerfile ]; then
  echo "错误：Dockerfile不存在，无法构建镜像"
  exit 1
fi
docker build -t $imagePro:$version -t $imagePro . || { echo "Docker构建失败"; exit 1; }
docker login --username=candycloud $registry --password=$password
docker push $imagePro:$version

echo "正在清理 $PROJECT_DESC 构建环境..."
docker rmi $imagePro:$version
docker rmi $imagePro:latest
find . -type d -name "target" -exec rm -rf {} +

echo "$PROJECT_DESC 构建完成！"

echo "正在更新 $PROJECT_DESC 镜像..."
#kubectl set image deploy/dlg-workbench workbench=$imagePro:$version
