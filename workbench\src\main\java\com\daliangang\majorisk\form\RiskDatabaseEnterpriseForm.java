package com.daliangang.majorisk.form;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import xyz.erupt.annotation.EruptField;
import xyz.erupt.annotation.config.Comment;
import xyz.erupt.annotation.config.EruptSmartSkipSerialize;
import xyz.erupt.annotation.expr.ExprBool;
import xyz.erupt.annotation.sub_field.Edit;
import xyz.erupt.annotation.sub_field.EditType;
import xyz.erupt.annotation.sub_field.View;
import xyz.erupt.annotation.sub_field.sub_edit.ChoiceType;
import xyz.erupt.annotation.sub_field.sub_edit.InputType;
import xyz.erupt.annotation.sub_field.sub_edit.Search;
import xyz.erupt.toolkit.handler.EruptDictCodeChoiceFetchHandler;
import xyz.erupt.upms.model.auth.DataAuthHandler;

import javax.persistence.Lob;
import javax.persistence.Transient;

/**
 * <AUTHOR>
 * @since :2023/8/22:15:12
 */
@Data
public class RiskDatabaseEnterpriseForm {


    @ApiModelProperty("风险类型")
    private String riskType;


    @ApiModelProperty("风险名称")
    private String riskName;


    @ApiModelProperty("风险辨识范围")
    private @Lob
    String riskRange;


    @ApiModelProperty("作业单元")
    private @Lob String work;


    @ApiModelProperty("风险事件")
    private @Lob String event;


    @ApiModelProperty("事故类型")
    private @Lob String accidentType;


    @ApiModelProperty("致险因素")
    private String factorDiv;


    @ApiModelProperty("致险因素_人的因素")
    private @Lob String factorPerson;


    @ApiModelProperty("致险因素_设备设施因素")
    private @Lob String factorDevice;


    @ApiModelProperty("致险因素_环境因素")
    private @Lob String factorEnv;


    @ApiModelProperty("致险因素_管理因素")
    private @Lob String factorManage;


    @ApiModelProperty("风险管控措施")
    private String measureDivide;


    @ApiModelProperty("风险管控措施")
    private @Lob String measure;


    @ApiModelProperty("变更说明")
    private @Lob String description;



    @Comment("公共风险编号")
    @ApiModelProperty("公共风险编号")
    private Long commonRiskId;


    private String orgCode;
}
