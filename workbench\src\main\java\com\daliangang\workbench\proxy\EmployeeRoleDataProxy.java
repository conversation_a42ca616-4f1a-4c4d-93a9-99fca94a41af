package com.daliangang.workbench.proxy;

import cn.hutool.core.lang.UUID;
import com.daliangang.core.DaliangangContext;
import com.daliangang.workbench.entity.EmployeeInformation;
import com.daliangang.workbench.entity.EmployeeRole;
import com.daliangang.workbench.entity.EmployeeView;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import xyz.erupt.annotation.fun.DataProxy;
import xyz.erupt.annotation.query.Condition;
import xyz.erupt.core.util.EruptSpringUtil;
import xyz.erupt.jpa.dao.EruptDao;
import xyz.erupt.toolkit.utils.AssertUtils;
import xyz.erupt.toolkit.utils.SqlUtils;
import xyz.erupt.upms.model.EruptMenu;
import xyz.erupt.upms.model.EruptOrg;
import xyz.erupt.upms.model.EruptRole;
import xyz.erupt.upms.model.EruptUser;
import xyz.erupt.upms.service.EruptUserService;
import xyz.erupt.upms.util.EruptMenuUtils;

import javax.annotation.Resource;
import javax.transaction.Transactional;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
public class EmployeeRoleDataProxy  implements DataProxy<EmployeeRole> {


    @Resource
    private EruptDao eruptDao ;

    @Override
    public EmployeeRole queryDataById(long id) {

        EmployeeRole role = eruptDao.queryEntity(EmployeeRole.class, "id=" + id);
        if (role != null) {
            EruptRole source = this.getSourceRole(role);
            if (source != null && source.getMenus() != null && !source.getMenus().isEmpty()) {
                source.getMenus().forEach(menu -> role.getMenus().add(menu));
            }
        }
        return role;
    }

    @Override
    public void addBehavior(EmployeeRole employeeRole) {
        employeeRole.setCode(UUID.fastUUID().toString().split("-")[0]);
    }

    @Override
    @Transactional
    public void afterAdd(EmployeeRole employeeRole) {
        //同步到系统角色表创建角色
        EruptRole newRole = new EruptRole();
        newRole.setName(employeeRole.getName());
        newRole.setCode(employeeRole.getCode());
        newRole.setReserved(false);
        newRole.setShare(true);
        //获取org
        EruptOrg org = eruptDao.queryEntity(EruptOrg.class, "code=" + SqlUtils.wrapStr(employeeRole.getOrgCode()));
        newRole.getDepts().add(org);
        newRole.setOrgCode(org.getCode());
        employeeRole.getMenus().forEach(menu -> newRole.getMenus().add(menu));
        eruptDao.persist(newRole);
        //清除本表数据
        employeeRole.getMenus().clear();
        eruptDao.merge(employeeRole);
    }

    @Override
    @Transactional
    public void afterUpdate(EmployeeRole employeeRole) {
        EruptRole role = this.getSourceRole(employeeRole);
        AssertUtils.notNull(role, "没有此角色 -> " + employeeRole.getCode() + " / " + employeeRole.getName());

        //原始角色原来的菜单code
        Set<String> srcCodes = role.getMenus().stream().collect(Collectors.toMap(EruptMenu::getCode, menu -> menu)).keySet();
        Set<String> newCodes = employeeRole.getMenus().stream().collect(Collectors.toMap(EruptMenu::getCode, menu -> menu)).keySet();

        //计算需要增加或删除的菜单
        Set<String> needAdds = new HashSet<>();
        Set<String> needRemoves = new HashSet<>();

        //如果现在有原来没有要添加
        StringBuffer sbAdd = new StringBuffer();
        newCodes.forEach(menuCode -> {
            if (!srcCodes.contains(menuCode)) {
                needAdds.add(menuCode);
                sbAdd.append(" ").append(menuCode).append("[").append(EruptMenuUtils.getMenuByCode(menuCode).getName()).append("]");
            }
        });
        log.info(employeeRole.getName() + " 需要添加菜单 -> {" + sbAdd.toString() + "}");

        //如果现在没有原来有要删除
        StringBuffer sbDel = new StringBuffer();
        srcCodes.forEach(menuCode -> {
            if (!newCodes.contains(menuCode)) {
                needRemoves.add(menuCode);
                sbDel.append(" ").append(menuCode).append("[").append(EruptMenuUtils.getMenuByCode(menuCode).getName()).append("]");
            }
        });
        log.info(employeeRole.getName() + " 需要删除菜单 -> {" + sbDel.toString() + "}");

        //刷新数据给角色
        //role.sync(employeeRole.getMenus(), null);//组织不用同步
        //eruptDao.merge(employeeRole);
        needAdds.forEach(menuCode -> {
            if (!containsMenu(role, menuCode)) {
                EruptMenu menu = EruptMenuUtils.getMenuByCode(menuCode);
                role.getMenus().add(menu);
            }
        });
        needRemoves.forEach(menuCode -> {
            if (containsMenu(role, menuCode)) {
                EruptMenu menu = EruptMenuUtils.getMenuByCode(menuCode);
                role.getMenus().remove(menu);
            }
        });
        String oldRoleName = String.valueOf(role.getName());
        role.setName(employeeRole.getName());
        eruptDao.merge(role);

        //反推给公司内关联到此角色的员工
        EruptUserService eruptUserService = EruptSpringUtil.getBean(EruptUserService.class);
        List<EmployeeInformation> employees = eruptDao.queryEntityList(EmployeeInformation.class, "org_code='" + employeeRole.getOrgCode() + "'");
        employees.forEach(employee -> {
            EruptUser eruptUser = eruptUserService.findEruptUserByAccount(employee.getPhone());
            if (eruptUser != null && employee.hasRole(oldRoleName)) {
                //替换名称
                employee.setOwnerRole(employee.getOwnerRole().replace(oldRoleName,employeeRole.getName()));
                eruptDao.merge(employee);
                eruptUserService.flushEruptUserCache(eruptUser);
                //计算用户除了当前角色以外的其他所有菜单权限
                Set<String> leftCodes = new HashSet<>();
                StringTokenizer st = new StringTokenizer(employee.getOwnerRole(), "|");
                while (st.hasMoreTokens()) {
                    String roleName = st.nextToken();
                    if (roleName.equals(EmployeeView.DEFAULT_ROLE)) {
                        //如果是市级或区县用户，基准角色进行替换
                        if (DaliangangContext.isDepartmentAdmin()) roleName = EmployeeView.DEFAULT_ROLE_DEPARTMENT;
                        if (DaliangangContext.isDepartmentArea()) roleName = EmployeeView.DEFAULT_ROLE_AREA;
                    }
                    //跳过当前角色
                    if (roleName.equals(oldRoleName)) continue;
                    this.searchLeftCodes(eruptDao, leftCodes, roleName, employee);
                }

                //获取隔离角色
                EruptRole empRole = null;
                for (EruptRole currRole : eruptUser.getRoles()) {
                    if (currRole.getCode().contains(employee.getPhone())) {
                        empRole = currRole;
                        break;
                    }
                }

                if (empRole != null) {
                    //添加菜单
                    StringBuffer actAdd = new StringBuffer();
                    for (String menuCode : needAdds) {
                        EruptMenu menu = EruptMenuUtils.getMenuByCode(menuCode);
                        boolean containsMenu = containsMenu(empRole, menuCode);
                        //log.info("containsMenu(" + menuCode + ")=" + containsMenu + ", ");
                        if (!containsMenu) {
                            empRole.getMenus().add(menu);
                            actAdd.append(" ").append(menuCode).append("[").append(menu.getName()).append("]");
                        }
                    }
                    log.info(employee.getName() + " 实际添加菜单 -> {" + actAdd.toString() + "}");

                    //删除菜单
                    StringBuffer actDel = new StringBuffer();
                    for (String menuCode : needRemoves) {
                        boolean containsMenu = containsMenu(empRole, menuCode);
                        boolean containsLeft = leftCodes.contains(menuCode);
                        if (containsMenu && !containsLeft) {
                            removeMenu(empRole, menuCode);
                            EruptMenu menu = EruptMenuUtils.getMenuByCode(menuCode);
                            actDel.append(" ").append(menuCode).append("[").append(menu.getName()).append("]");
                        }
                    }
                    log.info(employee.getName() + " 实际删除菜单 -> {" + actDel.toString() + "}");

                    eruptDao.merge(empRole);
                    eruptUserService.flushEruptUserCache(eruptUser);

                } else {
                    log.error("找不到目标角色 -> " + employee.getName());
                }

            }
        });
    }

    private void removeMenu(EruptRole role, String code) {
        for (EruptMenu menu : role.getMenus()) {
            if (menu.getCode().equals(code)) {
                role.getMenus().remove(menu);
                return;
            }
        }
    }

    private boolean containsMenu(EruptRole role, String code) {
        for (EruptMenu menu : role.getMenus()) {
            if (menu.getCode().equals(code)) return true;
        }
        return false;
    }

    private void searchLeftCodes(EruptDao eruptDao, Set<String> leftCodes, String roleName, EmployeeInformation employee) {
        //先搜全局的
        EruptRole role = eruptDao.queryEntity(EruptRole.class, "name='" + roleName + "' and reserved=1");
        if (role == null) {
            //如果全局搜不到，根据名字和企业的orgCode搜索企业的
            role = eruptDao.queryEntity(EruptRole.class, "name='" + roleName + "' and org_code='" + employee.getOrgCode() + "'");
        }
        if (role != null) {
            role.getMenus().forEach(menu -> {
                if (!leftCodes.contains(menu.getCode())) leftCodes.add(menu.getCode());
            });
        }
    }

    @Override
    @Transactional
    public void afterDelete(EmployeeRole employeeRole) {
        //删除关联角色
        EruptRole role = this.getSourceRole(employeeRole);
        if (role != null) eruptDao.delete(role);
    }

    private EruptRole getSourceRole(EmployeeRole employeeRole) {
        return eruptDao.queryEntity(EruptRole.class, "code=" + SqlUtils.wrapStr(employeeRole.getCode()) + " and org_code=" + SqlUtils.wrapStr(employeeRole.getOrgCode()));
    }

    @Override
    public void searchCondition(Map<String, Object> condition) {
        EruptUser currentEruptUser = EruptSpringUtil.getBean(EruptUserService.class).getCurrentEruptUser();
        if (currentEruptUser != null)
            condition.put("company", currentEruptUser.getEruptOrg().getCode());
    }

    @Override
    public String beforeFetch(List<Condition> conditions) {
        EruptUser currentEruptUser = EruptSpringUtil.getBean(EruptUserService.class).getCurrentEruptUser();
        return "company = " + SqlUtils.wrapStr(currentEruptUser.getEruptOrg().getCode());
    }


}
