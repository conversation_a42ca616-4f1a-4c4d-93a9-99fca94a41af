package com.daliangang.device.proxy;

import com.daliangang.device.entity.OtherTests;
import com.daliangang.device.entity.WharfStructureInspection;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import xyz.erupt.annotation.fun.DataProxy;
import xyz.erupt.annotation.query.Condition;
import xyz.erupt.toolkit.colorfont.ColorStateTimeFontDataProxy;

import javax.annotation.Resource;
import java.util.List;

@Service
@Slf4j
public class OtherTestsDataProxy implements DataProxy<OtherTests> {

    @Resource
    private ColorStateTimeFontDataProxy colorStateTimeFontDataProxy ;

    @Override
    public void beforeAdd(OtherTests otherTests) {
//        if (otherTests.getDetectionRange().contains("其他")) {
//            if(otherTests.getDescription()==null){
//                NotifyUtils.showErrorMsg("说明不能为空!");
//            }
//            if(otherTests.getDescription().length()>15){
//                NotifyUtils.showErrorMsg("说明应在15字以内!");
//            }
//        }
        otherTests.setCheckStatus(otherTests.getState());
    }

    @Override
    public void beforeUpdate(OtherTests otherTests) {
//        if (otherTests.getDetectionRange().contains("其他")) {
//            if(otherTests.getDescription()==null){
//                NotifyUtils.showErrorMsg("说明不能为空!");
//            }
//            if(otherTests.getDescription().length()>15){
//                NotifyUtils.showErrorMsg("说明应在15字以内!");
//            }
//        }
    }

    @Override
    public String beforeFetch(List<Condition> conditions) {
        String sql = " 1= 1 " ;
        sql = sql + colorStateTimeFontDataProxy.searchByState(WharfStructureInspection.class,"checkStatus" ,conditions);
        return sql;
    }
}
