/*
 * Copyright © 2020-2035 yundingyun All rights reserved.
 */
package com.daliangang.emergency.operation;

import cn.hutool.core.io.FileUtil;
import com.daliangang.rndpub.entity.InspectionResults;
import com.daliangang.rndpub.entity.Procedure;
import com.daliangang.workbench.entity.Enterprise;
import org.apache.poi.hssf.usermodel.HSSFCell;
import org.apache.poi.hssf.usermodel.HSSFRow;
import org.apache.poi.hssf.usermodel.HSSFSheet;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.util.CellRangeAddress;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.*;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import xyz.erupt.annotation.fun.*;

import java.io.*;
import java.net.HttpURLConnection;
import java.net.URL;
import java.net.URLEncoder;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.zip.ZipOutputStream;

import com.daliangang.emergency.entity.*;
import xyz.erupt.toolkit.utils.FileUtils;
import xyz.erupt.toolkit.utils.ZipUtils;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.transaction.Transactional;

@Service
public class EvaluationManageEvaluationFormHandler implements OperationHandler<EvaluationManage, Void> {

    @Override
    public String exec(List<EvaluationManage> data, Void unused, String[] param) {
        EvaluationManage model = data.get(0);
//        String link = model.getClass().getSimpleName() + "/" + model.getId();
//        String link = "应急救援能力评估表";
        String url = "window.open('/erupt-api/download/evaluationManage ','_blank')";
        return url;
    }

    @RestController
    @Transactional
    public static class InspectionrResultsExportController {
        @Value("${minio.endpoint}")
        private String endpoint;

        @Value("${minio.bucketName}")
        private String bucketName;

        @GetMapping("/erupt-api/download/evaluationManage")
        public void getFaRenWeiTuoShu(HttpServletRequest req, HttpServletResponse resp) throws UnsupportedEncodingException {
            //设置请求的编码格式
            req.setCharacterEncoding("UTF-8");
            //防止乱码，客户端和服务端解析编码要相同
            resp.setContentType("text/html;charset=UTF-8");
            //获取文件地址
//            String attachments = "/2023-05-10/应急救援能力评估表-5287a.doc";
            URL url = null;
            InputStream inputStream =null;
            BufferedInputStream bis = null;
            BufferedOutputStream bos=null;
            try {
//                url = new URL(endpoint + "/" + bucketName + attachments);
//                // 打开链接
//                HttpURLConnection connection = (HttpURLConnection)url.openConnection();
//                // get请求
//                connection.setRequestMethod("GET");
//                // 超时响应时间
//                connection.setConnectTimeout(5*1000);
//                //打开连接
//                connection.connect();
//                //获取文件流
//                inputStream=connection.getInputStream();
                inputStream = FileUtils.readInputStream("tpl/pgTable.doc");
                //文件输出流
                bis= new BufferedInputStream(inputStream);
                resp.setContentType("application/vnd.openxmlformats-officedocument.wordprocessingml.document");
                resp.setHeader("Content-Disposition","attachment;fileName="+ java.net.URLEncoder.encode("应急救援能力评估表" + ".doc",  "UTF-8"));
                bos= new BufferedOutputStream(resp.getOutputStream());
                int byteRead = 0;
                byte[] buffer = new byte[8192];
                while ((byteRead = bis.read(buffer, 0, 8192)) != -1) {
                    bos.write(buffer, 0, byteRead);
                }
            }catch (Exception e){
                System.out.println(e);
            }finally {
                try {
                    if(inputStream!=null){
                        inputStream.close();
                    }
                    if(bis!=null){
                        bis.close();
                    }
                    if(bos!=null){
                        bos.close();
                    }
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }

    }

}
