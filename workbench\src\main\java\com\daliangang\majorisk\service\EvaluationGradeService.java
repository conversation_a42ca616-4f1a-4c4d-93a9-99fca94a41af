package com.daliangang.majorisk.service;

import com.daliangang.emergency.entity.EvaluationGrade;
import org.junit.jupiter.api.Order;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.stereotype.Service;
import xyz.erupt.jpa.dao.EruptDao;
import xyz.erupt.toolkit.utils.SqlUtils;

import javax.annotation.Resource;
import javax.transaction.Transactional;
import java.util.ArrayList;
import java.util.List;

@Service
@Order(Integer.MAX_VALUE)
public class EvaluationGradeService implements ApplicationRunner {

    @Resource
    private EruptDao eruptDao;

    @Override
    @Transactional
    public void run(ApplicationArguments args) throws Exception {
        List<EvaluationGrade> initGrades = new ArrayList<>();
        initGrades.add(new EvaluationGrade("A", 91, 100));
        initGrades.add(new EvaluationGrade("B", 71, 90));
        initGrades.add(new EvaluationGrade("C", 0, 70));
        for (EvaluationGrade initGrade : initGrades) {
            EvaluationGrade grade = eruptDao.queryEntity(EvaluationGrade.class, "name=" + SqlUtils.wrapStr(initGrade.getName()));
            if (grade == null)
                eruptDao.persist(initGrade);
        }
    }
}
