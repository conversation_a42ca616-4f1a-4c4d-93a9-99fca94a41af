/*
 * Copyright © 2020-2035 yundingyun All rights reserved.
 */
package com.daliangang.rndpub.entity;

import com.daliangang.rndpub.RndpubConst;
import com.daliangang.rndpub.operation.ExpertApplyQRCodeHandler;
import com.daliangang.rndpub.operation.ExpertEnableHandler;
import com.daliangang.rndpub.proxy.ExpertDataProxy;
import io.swagger.annotations.ApiModel;
import lombok.Getter;
import lombok.Setter;
import xyz.erupt.annotation.Erupt;
import xyz.erupt.annotation.sub_erupt.Filter;
import xyz.erupt.annotation.sub_erupt.Power;
import xyz.erupt.annotation.sub_erupt.RowOperation;
import xyz.erupt.toolkit.db.Comment;
import xyz.erupt.toolkit.utils.TplUtils;

import javax.persistence.Entity;
import javax.persistence.Table;

@Erupt(name = "专家列表", power = @Power(add = true, delete = true, export = false, importable = true, viewDetails = true, edit = true)
        , filter = @Filter("Expert.expertAudidtState = '" + RndpubConst.EXPERT_STATUS_PASS + "'")
        , dataProxy = ExpertDataProxy.class
        , rowOperation = {
        //@RowOperation(confirm = false, title = "复制申请链接", icon = "fa fa-copy", operationHandler = ExpertCopyApplicationQRCodeHandler.class, mode = RowOperation.Mode.BUTTON),
        @RowOperation(title = "申请二维码", icon = "fa fa-qrcode", code = TplUtils.REMOTE_ERUPT_CODE + "expertApplyQrcode", eruptClass = ExpertApplyQRCodeHandler.ExpertApplyQRCode.class,
                operationHandler = ExpertApplyQRCodeHandler.class, mode = RowOperation.Mode.BUTTON),
        @RowOperation(title = "启用", icon = "fa fa-toggle-off", ifExpr = "item.useState=='禁用'", operationHandler = ExpertEnableHandler.class, operationParam = "true", mode = RowOperation.Mode.SINGLE),
        @RowOperation(title = "禁用", icon = "fa fa-toggle-on", ifExpr = "item.useState=='启用'", operationHandler = ExpertEnableHandler.class, operationParam = "false", mode = RowOperation.Mode.SINGLE),
//@RowOperation(title = "专家二维码", icon = "fa fa-qrcode", operationHandler = ExpertExpertQRCodeHandler.class, mode = RowOperation.Mode.SINGLE),
//@RowOperation(title = "审核", icon = "fa fa-check-square-o", operationHandler = ExpertAuditHandler.class, mode = RowOperation.Mode.SINGLE)
})
@Table(name = "tb_expert")
@Entity
@Getter
@Setter
@Comment("专家列表")
@ApiModel("专家列表")
public class Expert extends ExpertInfo {

//	@EruptField(
//		views = @View(title = "姓名"),
//		edit = @Edit(title = "姓名", type = EditType.INPUT, search = @Search(vague = true), notNull = true,
//		inputType = @InputType))
//	@Comment("姓名")
//	@ApiModelProperty("姓名")
//	private String name;
//
//	@EruptField(
//		views = @View(title = "性别"),
//		edit = @Edit(title = "性别", type = EditType.BOOLEAN, search = @Search(vague = true), notNull = true,
//		boolType = @BoolType(trueText = "男", falseText = "女")))
//	@Comment("性别")
//	@ApiModelProperty("性别")
//	private Boolean sex;
//
//	@EruptField(
//		views = @View(title = "出生日期"),
//		edit = @Edit(title = "出生日期", type = EditType.DATE, notNull = true,
//		dateType = @DateType))
//	@Comment("出生日期")
//	@ApiModelProperty("出生日期")
//	private java.util.Date date;
//
//	@EruptField(
//		views = @View(title = "学历"),
//		edit = @Edit(title = "学历", type = EditType.CHOICE, notNull = true,
//				choiceType = @ChoiceType(fetchHandler = EruptDictCodeChoiceFetchHandler.class, fetchHandlerParams = "educational")))
//	@Comment("学历")
//	@ApiModelProperty("学历")
//	private String educational;
//
//	@EruptField(
//		views = @View(title = "检查范围"),
//		edit = @Edit(title = "检查范围", type = EditType.CHOICE, notNull = false,
//		choiceType = @ChoiceType(fetchHandler = EruptDictCodeChoiceFetchHandler.class, fetchHandlerParams = "checkScope",reload=true)))
//	@Comment("检查范围")
//	@ApiModelProperty("检查范围")
//	private String checkScope;
//
//	@EruptField(
//		views = @View(title = "专业类别"),
//		edit = @Edit(title = "专业类别", type = EditType.INPUT, notNull = true,
//		inputType = @InputType))
//	@Comment("专业类别")
//	@ApiModelProperty("专业类别")
//	private String category;
//
//	@EruptField(
//		views = @View(title = "职称"),
//		edit = @Edit(title = "职称", type = EditType.CHOICE, notNull = true,
//				choiceType = @ChoiceType(fetchHandler = EruptDictCodeChoiceFetchHandler.class, fetchHandlerParams = "technical")))
//	@Comment("职称")
//	@ApiModelProperty("职称")
//	private String technical;
//
//	@EruptField(
//			views = @View(title = "企业名称",ifRender = @ExprBool(exprHandler = CompanyRenderHandler.class)),
//			edit = @Edit(title = "企业名称", type = EditType.INPUT, show = true, notNull = true))
//	@Comment("企业名称")
//	@ApiModelProperty("企业名称")
//	private String expertCompany;
//
//	@EruptField(
//		views = @View(title = "规避企业"),
//		edit = @Edit(title = "规避企业", type = EditType.TAGS, notNull = false,
//				tagsType = @TagsType(fetchHandler = RemoteEntityTagFetchHandler.class, fetchHandlerParams = {"main","Enterprise","administrator"})))
//	@Comment("规避企业")
//	@ApiModelProperty("规避企业")
//	private String evadeCompany;
//
//	@EruptField(
//		edit = @Edit(title = "资质证书", type = EditType.TAB_TABLE_ADD, notNull = true))
//	@OneToMany(cascade = CascadeType.ALL, orphanRemoval = true)
//        @OrderBy
//        @JoinColumn(name = "expert_id")
//	@Comment("资质证书")
//	@ApiModelProperty("资质证书")
//	private Set<Qualification> file;
//
//	@EruptField(
//		edit = @Edit(title = "执法信息", type = EditType.TAB_TABLE_ADD, notNull = true))
//	@OneToMany(cascade = CascadeType.ALL, orphanRemoval = true)
//        @OrderBy
//        @JoinColumn(name = "expert_id")
//	@Comment("执法信息")
//	@ApiModelProperty("执法信息")
//	private Set<LawenForcement> law;
//
//	@EruptField(
//		views = @View(title = "审批状态"),
//		edit = @Edit(title = "审批状态", type = EditType.CHOICE, notNull = false,
//		choiceType = @ChoiceType(fetchHandler = EruptDictCodeChoiceFetchHandler.class, fetchHandlerParams = "auditState")))
//	@Comment("审批状态")
//	@ApiModelProperty("审批状态")
//	private String expertAudidtState;
//
//
//	@EruptField(
//			views = @View(title = "状态", show = true),
//			edit = @Edit(title = "状态", type = EditType.BOOLEAN, notNull = true, show = false,
//					boolType = @BoolType(trueText = "启用", falseText = "禁用")))
//	@Comment("状态")
//	@ApiModelProperty("状态")
//	private Boolean useState;

}
