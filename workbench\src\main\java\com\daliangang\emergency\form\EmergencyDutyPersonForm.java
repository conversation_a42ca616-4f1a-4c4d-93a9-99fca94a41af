package com.daliangang.emergency.form;

import com.daliangang.emergency.entity.EmergencyDutyPerson;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;
import xyz.erupt.annotation.EruptField;
import xyz.erupt.annotation.expr.ExprBool;
import xyz.erupt.annotation.sub_field.Edit;
import xyz.erupt.annotation.sub_field.EditType;
import xyz.erupt.annotation.sub_field.Readonly;
import xyz.erupt.annotation.sub_field.View;
import xyz.erupt.annotation.sub_field.sub_edit.ChoiceType;
import xyz.erupt.annotation.sub_field.sub_edit.DateType;
import xyz.erupt.annotation.sub_field.sub_edit.InputType;
import xyz.erupt.jpa.model.BaseModel;
import xyz.erupt.toolkit.db.Comment;
import xyz.erupt.toolkit.handler.RemoteEntityChoiceFetchHandler;
import xyz.erupt.upms.model.auth.DataAuthHandler;

import java.sql.Timestamp;

/**
 * <AUTHOR>
 * @since :2023/4/7:16:29
 */
@Data
public class EmergencyDutyPersonForm extends BaseModel {


    @Comment("开始值守时间（年月日时分）")
    @ApiModelProperty("开始值守时间（年月日时分）")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm", timezone = "GMT+8")
    private Timestamp dutyTime;


    @Comment("值守人")
    @ApiModelProperty("值守人")
    private String personOnDuty;


    @Comment("联系方式")
    @ApiModelProperty("联系方式")
    private String tel;


    @Comment("所属单位")
    @ApiModelProperty("所属单位")
    private String company;


    @Comment("结束值守时间（年月日时分）")
    @ApiModelProperty("结束值守时间（年月日时分）")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm", timezone = "GMT+8")
    private Timestamp endDutyTime;

}
