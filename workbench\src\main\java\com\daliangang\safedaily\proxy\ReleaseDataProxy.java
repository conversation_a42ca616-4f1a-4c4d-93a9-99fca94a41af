/*
 * Copyright © 2020-2035 yundingyun All rights reserved.
 */
package com.daliangang.safedaily.proxy;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.date.LocalDateTimeUtil;
import com.daliangang.core.DaliangangContext;
import com.daliangang.safedaily.entity.Release;
import com.google.gson.internal.LinkedTreeMap;
import lombok.SneakyThrows;
import lombok.var;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import xyz.erupt.annotation.fun.DataProxy;
import xyz.erupt.annotation.query.Condition;
import xyz.erupt.core.module.MetaUserinfo;
import xyz.erupt.toolkit.remote.RemoteProxyService;
import xyz.erupt.toolkit.remote.RemoteQuery;
import xyz.erupt.toolkit.utils.EruptDaoUtils;
import xyz.erupt.toolkit.utils.NotifyUtils;
import xyz.erupt.upms.service.EruptUserService;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Date;
import java.util.List;

@Service
public class ReleaseDataProxy implements DataProxy<Release> {

    @Resource
    private RemoteProxyService remoteProxyService;
    @Resource
    private EruptUserService eruptUserService;

    @Override
    @SneakyThrows
    public void addBehavior(Release release) {
        MetaUserinfo remoteUserInfo = remoteProxyService.getRemoteUserInfo();
        List<LinkedTreeMap> linkedTreeMaps = remoteProxyService.queryEntity("Enterprise", RemoteQuery.builder().condition("org_code", remoteUserInfo.getOrg()));
        if (ObjectUtils.isNotEmpty(linkedTreeMaps)) {
            String type = String.valueOf(linkedTreeMaps.get(0).get("type"));
            release.setType(type);
        }

        Release release1 = EruptDaoUtils.selectOne("select * FROM tb_release where org_code ='" + remoteUserInfo.getOrg() + "' ORDER BY id DESC LIMIT 0,1", Release.class);
        if (ObjectUtils.isNotEmpty(release1)) {
            release.setResPerson(release1.getResPerson());
        }

        if (eruptUserService.getCurrentEruptUser().getIsAdmin() && StringUtils.isNotEmpty(release.getCompany())) {
            List<LinkedTreeMap> enterprise = remoteProxyService.queryEntity("Enterprise", RemoteQuery.builder().condition("orgCode", release.getCompany()));
            if (ObjectUtils.isNotEmpty(enterprise)) {
                String type = String.valueOf(enterprise.get(0).get("type"));
                release.setType(type);
            }

        }

        release.setFillingTime(LocalDateTime.now());
        release.setFillingDate(new Date());
        release.setContent(Release.DEFAULT_CONTENT);
    }

    // 添加前判断当日是否已经添加
    @Override
    public void beforeAdd(Release release) {
        String fillDate = DateUtil.format(release.getFillingDate(), "yyyy-MM-dd");
        String sql = "select * FROM tb_release where org_code ='" + release.getOrgCode() + "' and DATE_FORMAT(filling_date,'%Y-%m-%d')  = '"+fillDate+"' limit 0,1" ;
        Release release1 = EruptDaoUtils.selectOne(sql, Release.class);
        if (ObjectUtils.isNotEmpty(release1)) {
            boolean today = DateUtil.isSameDay(release1.getFillingDate(),release.getFillingDate());
            if (today) {
                NotifyUtils.showErrorMsg(fillDate+" 公告已经添加，请勿重复添加！");
            }
        }
        release.setSubmitted(false);
    }

    @Override
    public String beforeFetch(List<Condition> conditions) {
        if (DaliangangContext.isDepartmentUser()) return "submitted=true";
        return DataProxy.super.beforeFetch(conditions);
    }

    @Override
    public void beforeUpdate(Release release) {
        if (release.getSubmitted()) {
            NotifyUtils.showErrorMsg("公告已经上报，请勿修改！");
        }
    }

    @Override
    public void beforeDelete(Release release) {
        if (release.getSubmitted()) {
            NotifyUtils.showErrorMsg("公告已经上报，请勿删除！");
        }
    }

    public static boolean isTheSameDay(LocalDateTime first, LocalDateTime second) {
        LocalDate firstDay = first.toLocalDate();
        LocalDate secondDay = second.toLocalDate();
        return firstDay.equals(secondDay);
    }

    public static boolean isToday(LocalDateTime dateTime) {
        return isTheSameDay(LocalDateTime.now(), dateTime);
    }


}
