package com.daliangang.workbench.config;

import com.daliangang.workbench.entity.*;
import com.daliangang.workbench.form.EnterpriseStatForm;
import com.daliangang.workbench.handler.BaseRoleMenusCheckJobHandler;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.annotation.Order;
import xyz.erupt.core.constant.MenuTypeEnum;
import xyz.erupt.core.module.EruptModule;
import xyz.erupt.core.module.EruptModuleInvoke;
import xyz.erupt.core.module.MetaMenu;
import xyz.erupt.core.module.ModuleInfo;
import xyz.erupt.core.util.EruptSpringUtil;
import xyz.erupt.job.model.EruptJob;
import xyz.erupt.job.model.data_proxy.EruptJobDataProxy;
import xyz.erupt.jpa.dao.EruptDao;
import xyz.erupt.openapi.entity.OpenApiCredit;
import xyz.erupt.toolkit.utils.EruptDaoUtils;
import xyz.erupt.toolkit.utils.SqlUtils;
import xyz.erupt.upms.enums.DataRange;
import xyz.erupt.upms.model.EruptMenu;
import xyz.erupt.upms.model.EruptPost;
import xyz.erupt.upms.model.EruptRole;
import xyz.erupt.upms.model.template.EruptDeptTemplate;
import xyz.erupt.upms.model.template.EruptRoleTemplate;
import xyz.erupt.upms.model.template.EruptRoleTemplateUser;
import xyz.erupt.upms.service.EruptMenuService;
import xyz.erupt.upms.util.EruptDictUtils;
import xyz.erupt.upms.util.EruptMenuUtils;

import javax.annotation.Resource;
import javax.transaction.Transactional;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * <AUTHOR>
 * date 2021/3/28 18:51
 */
@Configuration
@Slf4j
@Order(Integer.MAX_VALUE)
//@RTools({
//        @RButton(text = "消息管理", icon = "fa fa-commenting-o", routeClick = "/#/build/table/SystemMsg"),
//        @RButton(text = "待办管理", icon = "fa fa-check-square-o", routeClick = "/#/build/table/TodoList")
//})
//@EruptOption(name = WorkbenchConfiguration.WORKBENCH_INITED, value = "false", desc = "工作台初始化标记")
public class WorkbenchConfiguration implements EruptModule, ApplicationRunner {

    public static final String WORKBENCH_INITED = "WORKBENCH_INITED";

    static {
        EruptModuleInvoke.addEruptModule(WorkbenchConfiguration.class);
    }

    @Override
    public ModuleInfo info() {
        return ModuleInfo.builder().name("daliangang-workbench").build();
    }

    @Resource
    private EruptDao eruptDao;

    @Resource
    private EruptJobDataProxy eruptJobDataProxy;

    @Override
    public List<MetaMenu> initMenus() {
        EruptMenuService eruptMenuService = EruptSpringUtil.getBean(EruptMenuService.class);
        String moduleId = "workbench";
        eruptMenuService.registerModule(moduleId, "危险货物港区重大安全风险管控平台", 1);

        List<MetaMenu> menus = new ArrayList<>();
//        menus.add(EruptMenuUtils.createRootMenu(null, "$dlg-workbench", "企业管理", "fa fa-shopping-bag text-blue", new AtomicInteger(100), moduleId));
        AtomicInteger sort = new AtomicInteger();

        menus.add(EruptMenuUtils.createMenu(Department.class, null, sort, MenuTypeEnum.TREE, "主管部门", "Department", moduleId, null).hide());
        menus.add(EruptMenuUtils.createMenu(EmployeeSelfInfo.class, null, sort, MenuTypeEnum.TREE, "员工信息", "EmployeeSelfInfo", moduleId, "fa fa-address-card-o"));
        menus.add(EruptMenuUtils.createMenu(EnterpriseDetail.class, null, sort, MenuTypeEnum.TABLE, "企业信息", "EnterpriseDetail", moduleId, "fa fa-book"));
        menus.add(EruptMenuUtils.createMenu(EnterpriseInfo.class, null, sort, MenuTypeEnum.TREE, "企业详情", "EnterpriseInfo", moduleId, "fa fa-book").hide());
        menus.add(EruptMenuUtils.createMenu(EruptDeptTemplate.class, null, sort, MenuTypeEnum.TREE, "部门管理", "EnterpriseDeptInfo", moduleId, "fa fa-briefcase"));
        menus.add(EruptMenuUtils.createMenu(EruptRoleTemplatePost.class, null, sort, MenuTypeEnum.TABLE, "岗位管理", "EnterprisePostInfo", moduleId, "fa fa-clone"));//岗位管理，使用内置的岗位
        menus.add(EruptMenuUtils.createMenu(EmployeeInformation.class, null, sort, MenuTypeEnum.TABLE, "员工管理", "EmployeeInfo", moduleId, "fa fa-address-book-o"));
        menus.add(EruptMenuUtils.createMenu(Enterprise.class, null, sort, MenuTypeEnum.TABLE, "企业列表", "Enterprise", moduleId, "fa fa-bank"));
        menus.add(EruptMenuUtils.createMenu(EruptRoleTemplateUser.class, null, sort, MenuTypeEnum.TABLE, "用户权限管理", "EnterprisePrivilegeInfo", moduleId, "fa fa-edit"));
        menus.add(EruptMenuUtils.createMenu(EmployeeRole.class, null, sort, MenuTypeEnum.TABLE, "员工角色管理", "EmployeeRole", moduleId, "fa fa-gears"));
        menus.add(EruptMenuUtils.createMenu(EnterpriseRelevancy.class, null, sort, MenuTypeEnum.TABLE, "三方系统企业关联", "EnterpriseRelevancy", moduleId, null));
        menus.add(EruptMenuUtils.createMenu(EnterpriseCertificate.class, null, sort, MenuTypeEnum.TREE, "企业附证", "EnterpriseCertificate", moduleId, null).hide());
        menus.add(EruptMenuUtils.createMenu(EnterpriseCertificateImport.class, null, sort, MenuTypeEnum.TABLE, "企业附证导入", "EnterpriseCertificateImport", moduleId, null).hide());
        menus.add(EruptMenuUtils.createMenu(EnterpriseStatForm.class, null, sort, MenuTypeEnum.TABLE, "企业列表统计下钻", "EnterpriseStatForm", moduleId, null).hide());
        menus.add(EruptMenuUtils.createMenu(EnterpriseStats.class, null, sort, MenuTypeEnum.TABLE, "企业统计列表", "EnterpriseStats", moduleId, null));
        menus.add(EruptMenuUtils.createMenu(EmployeeCertificate.class, null, sort, MenuTypeEnum.TABLE, "员工证书", "EmployeeCertificate", moduleId, null).hide());
        //首页
        menus.add(MetaMenu.createSimpleMenu("home-government", "政府首页", "/home-government/index.html", null, sort.getAndIncrement(), MenuTypeEnum.LINK.getCode()).hide());
        menus.add(MetaMenu.createSimpleMenu("home-enterprise", "企业首页", "/home-enterprise/index.html", null, sort.getAndIncrement(), MenuTypeEnum.LINK.getCode()).hide());

        //日程管理
        menus.add(MetaMenu.createSimpleMenu("date-man", "日程管理", "/scheduleManagement/index.html#/", null, sort.getAndIncrement(), MenuTypeEnum.LINK.getCode()).icon("fa fa-calendar").module(moduleId));
        return menus;
    }

    /**
     * 菜单更换父菜单
     *
     * @param menuName
     * @param parentName
     */
    public static void changeParentMenu(String menuName, String parentName) {
        EruptMenu parentMenu = EruptMenuUtils.getMenu(parentName);
        EruptMenu currentMenu = EruptMenuUtils.getMenu(menuName);
        String sql = "update e_upms_menu set parent_menu_id=" + parentMenu.getId() + " where id=" + currentMenu.getId();
        EruptDaoUtils.updateNoForeignKeyChecks(sql);
    }

    public void changeParentMenuByCode(String menuCode, String parentCode) {
        EruptMenu parentMenu = EruptMenuUtils.getMenuByCode(parentCode);
        EruptMenu currentMenu = EruptMenuUtils.getMenuByCode(menuCode);
        if (parentMenu != null && currentMenu != null) {
            String sql = "update e_upms_menu set parent_menu_id=" + parentMenu.getId() + " where id=" + currentMenu.getId();
            EruptDaoUtils.updateNoForeignKeyChecks(sql);
        }
    }

    @Transactional
    public void changeMenuModule(EruptMenu menu, String moduleId) {
        if (menu == null) return;
        if (StringUtils.isEmpty(menu.getModule()) || !menu.getModule().equals(moduleId)) {
            String sql = "update e_upms_menu set module=" + SqlUtils.wrapStr(moduleId) + " where id=" + menu.getId();
            EruptDaoUtils.updateNoForeignKeyChecks(sql);
        }
    }

    @Override
    @Transactional
    public void run(ApplicationArguments args) throws Exception {
        //注册岗位的预处理回调
        //EruptSpringUtil.getBean(EruptRoleTemplatePostService.class).registerPrepareHandler(EmployeePostDataPrepareHandler.class);

        //判断工作台初始化标识
//        EruptPlatformService platformService = EruptSpringUtil.getBean(EruptPlatformService.class);
//        PlatformOption option = platformService.getOption(WORKBENCH_INITED);
//        if (option.getAsBool()) return;//已初始化完成，不用重复初始化

        //创建政府和企业两个字典项
        EruptDictUtils.getDictItem(EruptRole.ROLE_IDNETITY, "角色身份", "GOV", "政府", true);
        EruptDictUtils.getDictItem(EruptRole.ROLE_IDNETITY, "角色身份", "ENTERPRISE", "企业", true);
        EruptDictUtils.getDictItem(EruptRole.ROLE_IDNETITY, "角色身份", "AREA", "区县", true);

        EruptDictUtils.getDictItem(EruptPost.POST_TYPE, "岗位类别", "GOV", "政府", true);
        EruptDictUtils.getDictItem(EruptPost.POST_TYPE, "岗位类别", "ENTERPRISE", "企业", true);

        //创建风控对接API凭证
        String ak = "f97e48babfe14956a07c140dc5724ec0";
        //EruptDao eruptDao = EruptDaoUtils.getEruptDao();
        OpenApiCredit apiCredit = eruptDao.queryEntity(OpenApiCredit.class, "access_key=" + SqlUtils.wrapStr(ak));
        if (apiCredit == null) {
            apiCredit = new OpenApiCredit();
            apiCredit.setAccessKey(ak);
            apiCredit.setAccessSecret("c7c7aad7f9b743f9ace586124eecadcc");
            apiCredit.setName("对接风控");
            apiCredit.setInterfaceInfo("对接作业地点|对接装卸船");
            eruptDao.persist(apiCredit);
        }

        //将角色管理的详情设置为隐藏
        EruptMenuUtils.hideMenu("EruptRole@VIEW_DETAIL");
        EruptMenuUtils.hideMenu(EruptDeptTemplate.class);
        //EruptMenuUtils.hideMenu(EruptRoleTemplatePost.class);
        EruptMenuUtils.hideMenu(EruptRoleTemplateUser.class);
        //将门户管理的菜单设置为系统1
        String moduleId = "workbench";
        EruptMenu siteMenu = EruptMenuUtils.getMenu("门户管理");
        if (siteMenu == null) return;
        this.changeMenuModule(siteMenu, moduleId);
        //log.info("siteMenu -> " + LogUtils.toJson(siteMenu));
        String menuSql = "select * from e_upms_menu where parent_menu_id=" + siteMenu.getId();
        List<EruptMenu> siteMenus = EruptDaoUtils.selectOnes(menuSql, EruptMenu.class);
        siteMenus.forEach(menu -> {
            this.changeMenuModule(menu, moduleId);
            //log.info("subMenu -> " + LogUtils.toJson(menu));
            String subSql = "select * from e_upms_menu where parent_menu_id=" + menu.getId();
            List<EruptMenu> subMenus = EruptDaoUtils.selectOnes(subSql, EruptMenu.class);
            subMenus.forEach(sub -> {
                this.changeMenuModule(sub, moduleId);
                //log.info("subBtn -> " + LogUtils.toJson(sub));
            });
        });

        //删除补丁的错误记录
        //String countPatchSql = "select count(*) as count from e_platform_patch where bind_class='com.daliangang.workbench.patch.EmployeeRolePatch'";

        //检查基准角色的定时器是否创建
        this.checkBaseRoleMenusJob();
    }

    @Transactional
    public void checkBaseRoleMenusJob() {
        String code = "baseRoleMenus";
        EruptJob job = eruptDao.queryEntity(EruptJob.class, "code=" + SqlUtils.wrapStr(code));
        if (job == null) {
            job = new EruptJob();
            job.setCode(code);
            job.setRemark("检查基准角色菜单是否正常的定时器");
            job.setName("基准角色菜单检查");
            job.setHandler(BaseRoleMenusCheckJobHandler.class.getName());
            job.setCron("0 0/30 * * * ? ");//每30分钟执行一次
            job.setStatus(true);
            eruptJobDataProxy.beforeAdd(job);
            eruptDao.persist(job);
            eruptJobDataProxy.afterAdd(job);
        }
    }

}
