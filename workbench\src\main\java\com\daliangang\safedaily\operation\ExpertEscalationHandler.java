package com.daliangang.safedaily.operation;

import cn.hutool.json.JSONObject;
import com.daliangang.safedaily.entity.InspectExpertDaily;
import com.daliangang.safedaily.entity.Release;
import org.springframework.stereotype.Service;
import xyz.erupt.annotation.fun.OperationHandler;
import xyz.erupt.common.http.EruptRemoteUtils;
import xyz.erupt.jpa.dao.EruptDao;
import xyz.erupt.toolkit.remote.RemoteProxyService;
import xyz.erupt.toolkit.utils.NotifyUtils;

import javax.annotation.Resource;
import javax.transaction.Transactional;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * @Title: ExpertEscalationHandler
 * <AUTHOR>
 * @Package com.daliangang.safedaily.operation
 * @Date 2024/3/7 18:48
 * @description: 专家检查日上报
 */
@Service
public class ExpertEscalationHandler implements OperationHandler<InspectExpertDaily,Void> {

    @Resource
    private RemoteProxyService remoteProxyService;

    @Resource
    private EruptDao eruptDao;


    @Override
    @Transactional
    public String exec(List<InspectExpertDaily> data, Void unused, String[] param) {
        for (InspectExpertDaily inspectExpertDaily:data) {
            inspectExpertDaily.setSubmitted(true);
            inspectExpertDaily.setUpdateTime(LocalDateTime.now());
            eruptDao.merge(inspectExpertDaily);

            JSONObject inputData = new JSONObject();
            inputData.set("clazz", "InspectExpertDaily");
            inputData.set("insertData",inspectExpertDaily);
            EruptRemoteUtils.doPost(remoteProxyService.getMainServerUrl() + "/erupt-api/exchange/data/exchange", inputData, Map.class);
        }

        return NotifyUtils.getSuccessNotify("上报成功！");
    }
}
