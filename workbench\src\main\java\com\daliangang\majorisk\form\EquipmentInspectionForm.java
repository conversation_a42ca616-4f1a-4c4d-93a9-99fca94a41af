package com.daliangang.majorisk.form;

import com.daliangang.majorisk.proxy.FiveChecklistsFoundationDataProxy;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import xyz.erupt.annotation.Erupt;
import xyz.erupt.annotation.EruptField;
import xyz.erupt.annotation.sub_erupt.Power;
import xyz.erupt.annotation.sub_field.Edit;
import xyz.erupt.annotation.sub_field.EditType;
import xyz.erupt.annotation.sub_field.View;
import xyz.erupt.annotation.sub_field.sub_edit.*;
import xyz.erupt.toolkit.db.Comment;
import xyz.erupt.upms.model.auth.DataAuthModel;

import javax.persistence.Entity;

/**
 * <AUTHOR>
 * @since :2023/4/7:13:40
 */
@Erupt(name = "设备设施检验检测状态", power = @Power(add = false, delete = false, export = false, importable = true, viewDetails = false, edit = false)
        , dataProxy = FiveChecklistsFoundationDataProxy.class)

@Getter
@Setter
@Comment("设备设施检验检测状态")
@ApiModel("设备设施检验检测状态")
//@Table(name = "tb_equipmentinspectionform_overdue")
public  class EquipmentInspectionForm extends DataAuthModel {

    @EruptField(views = @View(title = "消防设施检测"),
            edit = @Edit(title = "消防设施检测", type = EditType.INPUT,  inputType = @InputType))
    @Comment("消防设施检测")
    @ApiModelProperty("消防设施检测")
    private String facilityInspectionName;


    @EruptField(views = @View(title = "防雷装置检测"),
            edit = @Edit(title = "防雷装置检测", type = EditType.INPUT,  inputType = @InputType))
    @Comment("防雷装置检测")
    @ApiModelProperty("防雷装置检测")
    private String lightningProtectionName;

    @EruptField(views = @View(title = "压力表检测"),
            edit = @Edit(title = "压力表检测", type = EditType.INPUT,  inputType = @InputType))
    @Comment("压力表检测")
    @ApiModelProperty("压力表检测")
    private String pressureGaugeName;

    @EruptField(views = @View(title = "安全阀检测"),
            edit = @Edit(title = "安全阀检测", type = EditType.INPUT,  inputType = @InputType))
    @Comment("安全阀检测")
    @ApiModelProperty("安全阀检测")
    private String safetyValveName;

    @EruptField(views = @View(title = "可燃气体报警检测"),
            edit = @Edit(title = "可燃气体报警检测", type = EditType.INPUT,  inputType = @InputType))
    @Comment("可燃气体报警检测")
    @ApiModelProperty("可燃气体报警检测")
    private String combustibleGasName;
}
