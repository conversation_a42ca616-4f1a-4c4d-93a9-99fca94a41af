package com.daliangang.datascreen.riskboard.service;

import cn.hutool.json.JSONUtil;
import com.daliangang.datascreen.annotation.TokenCheck;
import com.daliangang.majorisk.entity.Maintenance;
import com.daliangang.majorisk.entity.Unload;
import com.daliangang.majorisk.entity.UnloadShip;
import com.daliangang.datascreen.riskboard.entity.RiskBoardNum;
import com.daliangang.datascreen.riskboard.entity.response.*;


import com.daliangang.datascreen.utils.DateUtil;
import com.daliangang.datascreen.utils.DictUtils;
import com.daliangang.workbench.entity.Enterprise;
import org.springframework.stereotype.Service;
import xyz.erupt.core.exception.EruptApiErrorTip;
import xyz.erupt.jpa.dao.EruptDao;
import xyz.erupt.upms.service.EruptUserService;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

import static com.daliangang.datascreen.utils.OrgUtils.getCompanyName;
import static com.daliangang.datascreen.utils.OrgUtils.getStartUpOrgCode;

/**
 * @Author: chongmenglin
 * @Date: 2024/11/13 9:27
 */
@Service
@TokenCheck
public class RiskBoardService {

    @Resource
    private EruptUserService eruptUserService;
    @Resource
    private EruptDao eruptDao;


    /**
     * 获取风险类型数量
     * @return
     */
    public RiskTypeResponse getRiskTypeNum() {
        RiskBoardNum num = eruptDao.queryEntity(RiskBoardNum.class, " account = '" + eruptUserService.getCurrentAccount() + "'");
        if(num == null){
            throw new EruptApiErrorTip("未查询到该账号下的风险数据");
        }
        return RiskTypeResponse.builder()
                .highRisk(num.getHighRisk())
                .middleRisk(num.getMiddleRisk())
                .lowRisk(num.getLowRisk())
                .total(num.getTotal()).build();
    }


    /**
     * 获取企业风险列表
     * @return
     */
    public List<RiskTypeForm> getRiskTypeForm() {
        RiskBoardNum num = eruptDao.queryEntity(RiskBoardNum.class, " account = '" + eruptUserService.getCurrentAccount() + "'");
        if(num == null){
            return new ArrayList<>();
//            throw new EruptApiErrorTip("未查询到该账号下的风险数据");
        }
        // 获取 riskForm JSON 字符串
        String riskForm = num.getRiskForm();
        if (riskForm == null || riskForm.isEmpty()) {
            return new ArrayList<>(); // riskForm 为空则返回空列表
        }

        // 使用 Hutool 将 JSON 字符串转换为 JSONObject
        cn.hutool.json.JSONObject jsonObject = JSONUtil.parseObj(riskForm);

        // 将 JSONObject 转换为 List<RiskTypeForm>
        List<RiskTypeForm> riskTypeFormList = new ArrayList<>();
        jsonObject.forEach((key, value) -> {
            riskTypeFormList.add(RiskTypeForm.builder()
                    .companyName(key)
                    .riskLevel(value.toString())
                    .build());
        });
        // 自定义排序规则
        return riskTypeFormList.stream()
                .sorted((a, b) -> {
                    // 定义风险等级的优先级
                    Map<String, Integer> riskPriority = new HashMap<>();
                    riskPriority.put("高风险", 1);
                    riskPriority.put("中风险", 2);
                    riskPriority.put("低风险", 3);

                    // 获取风险等级的优先级进行比较
                    int priorityA = riskPriority.getOrDefault(a.getRiskLevel(), 999);
                    int priorityB = riskPriority.getOrDefault(b.getRiskLevel(), 999);

                    // 按优先级排序
                    return Integer.compare(priorityA, priorityB);
                })
                .collect(Collectors.toList());
    }

    /**
     * 获取当日作业统计
     * @param code
     * @return
     */
    public WorkTypeNum getWorkStatistics(String code) {
        List<Enterprise> enterprises = getStartUpOrgCode();
        List<String> orgCodes = enterprises.stream()
                .map(Enterprise::getOrgCode)
                .collect(Collectors.toList());
        WorkTypeNum num = new WorkTypeNum();


        //当日装卸船作业数量
        int unloadShipNum = getUnloadShipNum(orgCodes);
        //当日装卸车作业数量
        int unloadCarNum = getUnloadCarNum(orgCodes);
        //当日检维修作业数量
        int maintenanceNum = getMaintenanceNum(orgCodes);
        num.setUnloadShip(unloadShipNum);
        num.setUnloadCar(unloadCarNum);
        num.setMaintenance(maintenanceNum);
        num.total = unloadShipNum + unloadCarNum + maintenanceNum;

        //获取菜单
        return num;
    }

    /**
     * 当日检维修作业数量
     * @param orgCodes
     */
    private int getMaintenanceNum(List<String> orgCodes) {
        int totalMaintenance = 0;
        for (String orgCode : orgCodes) {
            // 获取当前日期的 Date 对象
            java.sql.Date currentDate = DateUtil.getCurrentDayDate();
            // 查询条件
            String queryCondition = "orgCode = :orgCode and DATE(starDate) <= :currentDate and DATE(endDate) >= :currentDate and submitted = '1'";
            // 执行查询
            List<Maintenance> maintenances = eruptDao.queryEntityList(
                    Maintenance.class,
                    queryCondition,
                    new HashMap<String, Object>() {{
                        put("orgCode", orgCode);
                        put("currentDate", currentDate);
                    }}
            );
            int size = maintenances.size();
            totalMaintenance += size;
        }
        return totalMaintenance;
    }

    /**
     * 当日装卸车作业数量
     * @param orgCodes
     */
    private int getUnloadCarNum(List<String> orgCodes) {
        int totalUnloadCar = 0;
        for (String orgCode : orgCodes) {
            // 获取当前日期的 Date 对象
            java.sql.Date currentDate = DateUtil.getCurrentDayDate();
            // 查询条件
            String queryCondition = "orgCode = :orgCode and DATE(aeTime) >= :currentDate and DATE(abTime) <= :currentDate and submitted = '1'";
            // 执行查询
            List<Unload> unload = eruptDao.queryEntityList(
                    Unload.class,
                    queryCondition,
                    new HashMap<String, Object>() {{
                        put("orgCode", orgCode);
                        put("currentDate", currentDate);
                    }}
            );
            int size = unload.size();
            totalUnloadCar += size;
        }
        return totalUnloadCar;
    }

    /**
     * 当日装卸船作业数量
     * @param orgCodes
     */
    private int getUnloadShipNum(List<String> orgCodes) {
        int totalUnloadShip = 0;
        for (String orgCode : orgCodes) {
            // 获取当前日期的 Date 对象
            java.sql.Date currentDate = DateUtil.getCurrentDayDate();
            // 查询条件
            String queryCondition = "orgCode = :orgCode and DATE(:currentDate) >= DATE(abTime) and DATE(:currentDate) <= DATE(aeTime) and cancelState = 0";
            // 执行查询
            List<UnloadShip> unloadShips = eruptDao.queryEntityList(
                    UnloadShip.class,
                    queryCondition,
                    new HashMap<String, Object>() {{
                        put("orgCode", orgCode);
                        put("currentDate", currentDate);
                    }}
            );
            int size = unloadShips.size();
            totalUnloadShip += size;
        }
        return totalUnloadShip;
    }

    /**
     * 获取当日作业详情
     * @return
     */
    public List<WorkDetailsResponse> getDailyWorkDetails() {
        List<Enterprise> enterprises = getStartUpOrgCode();
        List<String> orgCodes = enterprises.stream()
                .map(Enterprise::getOrgCode)
                .collect(Collectors.toList());

        // 获取当天日期
        java.sql.Date currentDate = DateUtil.getCurrentDayDate();

        // 创建一个列表来存储结果
        List<WorkDetailsResponse> workDetailsList = new ArrayList<>();

        // 查询装卸船作业
        for (String orgCode : orgCodes) {
            String unloadShipQuery = "orgCode = :orgCode and DATE(abTime) <= :currentDate and DATE(aeTime) >= :currentDate and cancelState = 0";
            List<UnloadShip> unloadShips = eruptDao.queryEntityList(
                    UnloadShip.class,
                    unloadShipQuery,
                    new HashMap<String, Object>() {{
                        put("orgCode", orgCode);
                        put("currentDate", currentDate);
                    }}
            );
            for (UnloadShip ship : unloadShips) {
                String workType = DictUtils.getDictItemValue("workTypeShip", ship.getWorkType());
                workDetailsList.add(new WorkDetailsResponse(getCompanyName(orgCode, enterprises), workType,ship.getName(), ship.getGoodsName() != null ? ship.getGoodsName() : "--"));
            }
        }

        // 查询装卸车作业
        for (String orgCode : orgCodes) {
            String unloadCarQuery = "orgCode = :orgCode and DATE(abTime) <= :currentDate and DATE(aeTime) >= :currentDate and submitted = '1'";
            List<Unload> unloadCars = eruptDao.queryEntityList(
                    Unload.class,
                    unloadCarQuery,
                    new HashMap<String, Object>() {{
                        put("orgCode", orgCode);
                        put("currentDate", currentDate);
                    }}
            );
            for (Unload car : unloadCars) {
                String workType = DictUtils.getDictItemValue("workType", car.getWorkType());
                workDetailsList.add(new WorkDetailsResponse(getCompanyName(orgCode, enterprises),workType,car.getName(), car.getGoodsName() != null ? car.getGoodsName() : "--"));
            }
        }

        // 查询检维修作业
        for (String orgCode : orgCodes) {
            String maintenanceQuery = "orgCode = :orgCode and DATE(starDate) <= :currentDate and DATE(endDate) >= :currentDate and submitted = '1'";
            List<Maintenance> maintenances = eruptDao.queryEntityList(
                    Maintenance.class,
                    maintenanceQuery,
                    new HashMap<String, Object>() {{
                        put("orgCode", orgCode);
                        put("currentDate", currentDate);
                    }}
            );
            for (Maintenance maintenance : maintenances) {
                String maintenanceType = DictUtils.getDictItemValue("MaintenanceType", maintenance.getType());
                workDetailsList.add(new WorkDetailsResponse(getCompanyName(orgCode, enterprises), maintenanceType,maintenance.getWorkName(), "--"));
            }
        }
        return workDetailsList;
    }



    /**
     * 当日作业数量前五企业
     * @return
     */
    public List<WorkTopOrgResponse> getTopOrgWorkStatistics() {
        List<Enterprise> enterprises = getStartUpOrgCode();
        List<String> orgCodes = enterprises.stream()
                .map(Enterprise::getOrgCode)
                .collect(Collectors.toList());

        // 创建一个 Map 来存储每个 orgCode 的作业类型数量
        Map<String, WorkTypeNum> orgCodeWorkStatsMap = new HashMap<>();

        // 统计每个 orgCode 的作业数量
        for (String orgCode : orgCodes) {
            int unloadShipNum = getUnloadShipNum(Collections.singletonList(orgCode));
            int unloadCarNum = getUnloadCarNum(Collections.singletonList(orgCode));
            int maintenanceNum = getMaintenanceNum(Collections.singletonList(orgCode));

            WorkTypeNum workTypeNum = new WorkTypeNum();
            workTypeNum.setUnloadShip(unloadShipNum);
            workTypeNum.setUnloadCar(unloadCarNum);
            workTypeNum.setMaintenance(maintenanceNum);
            workTypeNum.total = unloadShipNum + unloadCarNum + maintenanceNum;

            orgCodeWorkStatsMap.put(orgCode, workTypeNum);
        }

        // 排序并取前五名
        List<Map.Entry<String, WorkTypeNum>> topFiveEntries = orgCodeWorkStatsMap.entrySet().stream()
                .sorted((entry1, entry2) -> Integer.compare(entry2.getValue().total, entry1.getValue().total))
                .limit(5)
                .collect(Collectors.toList());

        // 封装返回结果
        List<WorkTopOrgResponse> topOrgWorkStatisticsList = new ArrayList<>();
        for (Map.Entry<String, WorkTypeNum> entry : topFiveEntries) {
            String orgCode = entry.getKey();
            WorkTypeNum workTypeNum = entry.getValue();

            // 获取 orgCode 对应的企业名称
            String companyName = getCompanyName(orgCode, enterprises);

            WorkTopOrgResponse topOrgWorkStats = new WorkTopOrgResponse(
                    companyName, workTypeNum.getUnloadShip(), workTypeNum.getUnloadCar(), workTypeNum.getMaintenance()
            );
            topOrgWorkStatisticsList.add(topOrgWorkStats);
        }

        return topOrgWorkStatisticsList;
    }
}
