package com.daliangang.workbench.patch;

import com.daliangang.workbench.entity.EmployeeInformation;
import com.daliangang.workbench.proxy.EmployeeInformationDataProxy;
import com.daliangang.workbench.proxy.InitMenuProxy;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import xyz.erupt.core.config.GsonFactory;
import xyz.erupt.devtools.patch.PlatformPatchHandler;
import xyz.erupt.jpa.dao.EruptDao;
import xyz.erupt.toolkit.db.EruptResultMap;
import xyz.erupt.toolkit.utils.EruptDaoUtils;
import xyz.erupt.upms.model.EruptMenu;
import xyz.erupt.upms.model.EruptRole;
import xyz.erupt.upms.model.EruptUser;
import xyz.erupt.upms.model.EruptUserByRoleView;
import xyz.erupt.upms.model.template.EruptRoleTemplateUser;
import xyz.erupt.upms.model.template.EruptRoleTemplateUserProxy;
import xyz.erupt.upms.service.EruptUserService;

import javax.annotation.Resource;
import javax.transaction.Transactional;
import java.util.Arrays;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

@Service
@Slf4j
public class AreaEntRoleDataPatch implements PlatformPatchHandler {

    @Override
    public String getPatchId() {
        return "1.11.7.4-patch-03";
    }

    @Override
    public String getName() {
        return "区县/企业管理员角色权限数据修复补丁";
    }

    @Override
    public String getDesc() {
        return "1.拔除错误的全局角色<br>";
    }

    @Resource
    private EruptDao eruptDao;

    @Resource
    private EruptRoleTemplateUserProxy eruptRoleTemplateUserProxy;

    @Resource
    private EmployeeInformationDataProxy employeeInformationDataProxy;

    @Resource
    private EruptUserService eruptUserService;

    @Resource
    private InitMenuProxy initMenuProxy;

    @Override
    @Transactional
    public boolean execute() {
        //所有员工数据缓存重置一下
        this.clearEruptUsers();

        //修复错误数据
        this.fixErrorEmployeeRoles(2);
        this.fixErrorEmployeeRoles(3);

        this.clearEruptUsers();


        return true;
    }

    private void clearEruptUsers() {
        //所有员工数据缓存重置一下
        List<EruptUser> eruptUsers = eruptDao.queryEntityList(EruptUser.class);
        eruptUsers.forEach(eruptUser -> {
            eruptUserService.flushEruptUserCache(eruptUser);
        });
    }

    @Transactional
    public void fixErrorEmployeeRoles(int roleId) {
        String userRoleSql = "select * from e_upms_user_role where role_id=" + roleId + ";";
        List<EruptResultMap> errorUsers = EruptDaoUtils.selectOnes(userRoleSql, EruptResultMap.class);
        EruptRole employeeGlobalRole = eruptDao.queryEntity(EruptRole.class, "id=" + roleId);

        //回收错误角色
        errorUsers.forEach(vo -> {
            long userId = vo.getLong("user_id");
            EruptUserByRoleView userView = eruptDao.queryEntity(EruptUserByRoleView.class, "id=" + userId);
            if (userView != null && employeeGlobalRole.getUsers().contains(userView)) {
                employeeGlobalRole.getUsers().remove(userView);
                log.info("回收错误角色 -> " + GsonFactory.getGson().toJson(userView));
            }
        });
        eruptDao.merge(employeeGlobalRole);
    }
}
