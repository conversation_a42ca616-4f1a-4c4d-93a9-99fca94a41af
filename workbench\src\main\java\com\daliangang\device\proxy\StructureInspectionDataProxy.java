package com.daliangang.device.proxy;

import com.daliangang.device.entity.WharfStructureInspection;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import xyz.erupt.annotation.fun.DataProxy;
import xyz.erupt.annotation.query.Condition;
import xyz.erupt.toolkit.colorfont.ColorStateTimeFontDataProxy;
import xyz.erupt.toolkit.utils.NotifyUtils;

import javax.annotation.Resource;
import java.util.List;
@Service
@Slf4j
public class StructureInspectionDataProxy implements DataProxy<WharfStructureInspection> {

    @Resource
    private ColorStateTimeFontDataProxy colorStateTimeFontDataProxy ;
    @Override
    public void beforeAdd(WharfStructureInspection wharfStructureInspection) {
//        if ("其他".equals(wharfStructureInspection.getDetectionRange())) {
//            if(wharfStructureInspection.getDescription()==null){
//                NotifyUtils.showErrorMsg("说明不能为空!");
//            }
//            if(wharfStructureInspection.getDescription().length()>15){
//                NotifyUtils.showErrorMsg("说明应在15字以内!");
//            }
//        }
      wharfStructureInspection.setCheckStatus(wharfStructureInspection.getState());
    }

    @Override
    public void beforeUpdate(WharfStructureInspection wharfStructureInspection) {
        if ("其他".equals(wharfStructureInspection.getDetectionRange())) {
            if(wharfStructureInspection.getDescription()==null){
                NotifyUtils.showErrorMsg("说明不能为空!");
            }
            if(wharfStructureInspection.getDescription().length()>15){
                NotifyUtils.showErrorMsg("说明应在15字以内!");
            }
        }
    }

    @Override
    public String beforeFetch(List<Condition> conditions) {
        String sql = " 1= 1 " ;
        sql = sql + colorStateTimeFontDataProxy.searchByState(WharfStructureInspection.class,"checkStatus" ,conditions);
        return sql;
    }
}
