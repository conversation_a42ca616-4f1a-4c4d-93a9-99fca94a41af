package com.daliangang.datascreen.regulatoryboard.controller;

import com.daliangang.datascreen.regulatoryboard.response.SecurityDetail;
import com.daliangang.datascreen.regulatoryboard.response.UnreportedCompanyResponse;
import com.daliangang.datascreen.regulatoryboard.service.RegulatoryService;
import com.daliangang.datascreen.response.AmountCountResponse;
import com.daliangang.datascreen.response.StatisticsGroup;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import xyz.erupt.core.annotation.EruptRouter;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * @Author: chongmenglin
 * @Date: 2024/11/13 13:55
 */

@RestController
public class RegulatoryController {
    @Resource
    private RegulatoryService regulatoryService;

    /**
     * 获取当日安全承诺统计
     * @param orgCode
     * @return
     */
    @RequestMapping("/regulatory/board/security/count")
    @EruptRouter(verifyType = EruptRouter.VerifyType.LOGIN)
    public List<AmountCountResponse> getSecurityCommitment(@RequestParam(required = false) String orgCode){
        return regulatoryService.getSecurityCommitment(orgCode);
    }


    /**
     * 获取安全承诺详情
     * @param orgCode
     * @return
     */
    @RequestMapping("/regulatory/board/security/detail")
    @EruptRouter(verifyType = EruptRouter.VerifyType.LOGIN)
    public List<SecurityDetail> getSecurityDetail(@RequestParam(required = false) String orgCode){
        return regulatoryService.getSecurityDetail(orgCode);
    }

    /**
     * 获取上报统计
     * @param orgCode
     * @return
     */
    @RequestMapping("/regulatory/board/report/count")
    @EruptRouter(verifyType = EruptRouter.VerifyType.LOGIN)
    public List<AmountCountResponse> getReportCount(@RequestParam(required = false) String orgCode){
        return regulatoryService.getReportCount(orgCode);
    }

    /**
     * 获取未上报企业
     *
     * @param orgCode
     * @return
     */
    @RequestMapping("/regulatory/board/unreported/companies")
    @EruptRouter(verifyType = EruptRouter.VerifyType.LOGIN)
    public List<UnreportedCompanyResponse> getUnreportedCompanies(@RequestParam(required = false) String orgCode,@RequestParam(required = false) String code){
        return regulatoryService.getUnreportedCompanies(orgCode,code);
    }

    /**
     * 获取设施检测报告
     * @param orgCode
     * @return
     */
    @RequestMapping("/regulatory/board/facility/report")
    @EruptRouter(verifyType = EruptRouter.VerifyType.LOGIN)
    public StatisticsGroup getFacilityReport (@RequestParam(required = false) String orgCode){
        return regulatoryService.getFacilityReport(orgCode);
    }

    /**
     * 获取备案管理数据
     * @param orgCode
     * @return
     */
    @RequestMapping("/regulatory/board/record/management")
    @EruptRouter(verifyType = EruptRouter.VerifyType.LOGIN)
    public StatisticsGroup getRecordManagement(@RequestParam(required = false) String orgCode){
        return regulatoryService.getRecordManagement(orgCode);
    }

    /**
     * 获取企业证照数据
     * @param orgCode
     * @return
     */
    @RequestMapping("/regulatory/board/enterprise/certificate")
    @EruptRouter(verifyType = EruptRouter.VerifyType.LOGIN)
    public StatisticsGroup getEnterpriseCertificate(@RequestParam(required = false) String orgCode){
        return regulatoryService.getEnterpriseCertificate(orgCode);
    }

    /**
     * 获取年度执法检查数据
     * @param orgCode
     * @return
     */
    @RequestMapping("/regulatory/board/enforcement/inspection")
    @EruptRouter(verifyType = EruptRouter.VerifyType.LOGIN)
    public AmountCountResponse getEnforcementInspection(@RequestParam(required = false) String orgCode){
        return regulatoryService.getEnforcementInspection(orgCode);
    }

    /**
     * 获取未提交企业
     * @param orgCode
     * @return
     */
    @RequestMapping("/regulatory/board/uncommitted/companies")
    @EruptRouter(verifyType = EruptRouter.VerifyType.LOGIN)
    public List<Map<String, String>> getUncommittedCompanies(@RequestParam(required = false) String orgCode){
        return regulatoryService.getUncommittedCompanies(orgCode);
    }

    /**
     * 从业人员证书
     */
    @RequestMapping("/regulatory/board/practitioner/certificate")
    @EruptRouter(verifyType = EruptRouter.VerifyType.LOGIN)
    public List<AmountCountResponse.amountContent> getPractitionerCertificate(@RequestParam(required = false) String orgCode){
        return regulatoryService.getPractitionerCertificate(orgCode);
    }

    /**
     * 获取已启用公司列表
     */
    @RequestMapping("/regulatory/board/enable/companies")
    @EruptRouter(verifyType = EruptRouter.VerifyType.LOGIN)
    public List<Map<String, String>> getEnableCompanies(@RequestParam(required = false) String orgCode){
        return regulatoryService.getEnableCompanies(orgCode);
    }
}
