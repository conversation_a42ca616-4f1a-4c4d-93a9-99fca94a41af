package com.daliangang.majorisk.sql;

import org.springframework.stereotype.Repository;

/**
 * <AUTHOR>
 * @since :2023/4/21:17:26
 */
@Repository
public class MajoriskWorkbenchSql {

    // 统计进行中装卸船作业，进行中已提交 装卸汽车/火车作业之和
    public String selectWork(String orgCode) {
        String sql = "SELECT SUM(num) as num from ( SELECT COUNT(*) as num from tb_unload tu where NOW() between tu.ab_time and tu.ae_time";

            sql += " and tu.submitted = 1 and tu.org_code "+orgCode+"";

        sql+="union all SELECT COUNT(*) as num from tb_unload_ship ts where NOW() between ts.ab_time and ts.ae_time";


            sql += " and ts.org_code "+orgCode+"";

        sql+=") as t";
        return sql;
    }


    // 统计检维修作业之和：进行中，已提交
    public String selectMaintenanceWork(String orgCode) {
        String sql = "SELECT COUNT(*) as num from tb_maintenance where CURRENT_DATE() between CONVERT(star_date,DATE)  and CONVERT(end_date,DATE) ";

            sql += " and submitted = 1  and org_code "+orgCode+"";

        return sql;
    }


    // 查询大屏作业数据
    public String selectWorkNum(String orgCode) {
        String sql = "select  name  ,work_type as type,company  from tb_unload tu where";
        sql += " org_code "+orgCode+"";

        sql+="UNION all select  name ,work_type as type,company from tb_unload_ship tus where";
        sql += " org_code "+orgCode+"";

        sql+="UNION all select  work_name ,type,company from tb_maintenance tm where";
        sql += " org_code "+orgCode+"";

        return sql;
    }

    // 查询仓库每日上报

    //第二大屏数据
    public String selectWorksNum(String orgCode) {
        String sql = "select id, name ,(SELECT risk_name from tb_risk_database_enterprise trde WHERE trde.id = risk_database ) as riskDatabase, (CASE when work_type= 'CAR' then 'ZXQC' when work_type= 'TRAIN' then 'ZXHC' end) as type,company ,ab_time as starTime,ae_time as endTime,(CASE when (now() < ab_time ) then '未开始' when (NOW() between ab_time and ae_time) then '进行中'  end) as state from tb_unload tu where tu.submitted = 1 and (NOW() between ab_time and ae_time) or (now() < ab_time ) and ";
        sql += " org_code "+orgCode+"";
        sql+="UNION all select  id, name , (SELECT risk_name from tb_risk_database_enterprise trde WHERE trde.id = risk_database ) as riskDatabase,(CASE when work_type= 'ship' then 'ZXC'  end) as type,company ,ab_time as starTime,ae_time as endTime, (CASE when (now() < ab_time ) then '未开始' when (NOW() between ab_time and ae_time) then '进行中'  end) as state from tb_unload_ship tus where (NOW() between ab_time and ae_time) or (now() < ab_time ) and ";
        sql += " org_code "+orgCode+"";
        sql += "UNION all  select  id, work_name as name ,(SELECT risk_name from tb_risk_database_enterprise trde WHERE trde.id = risk_database ) as riskDatabase, (CASE when type= 'DH' then 'DHZY' when type= 'SX' then 'SXZY'  end) as type,company ,star_date as starTime,end_date as endTime,(CASE WHEN ( now() < star_date ) THEN '未开始'  WHEN ( NOW() BETWEEN star_date AND end_date ) THEN '进行中' END ) as state from tb_maintenance tm where tm.submitted = 1  and ((NOW() between star_date and end_date) or (now() < star_date )) and ";
        sql += " org_code "+orgCode+"";

        return sql;
    }


    // 查询大屏风险数据分组
    public String selectWorkRiskDatabase(String orgCode,String riskName) {
        String sql = "SELECT `work`,risk_range as riskRange FROM tb_risk_database_enterprise WHERE ";
        sql += " org_code ='"+orgCode+"'";

        sql += " and risk_name = '"+riskName+"'";

        sql += " GROUP by `work`,risk_range";

        return sql;
    }

    // 查询大屏风险数据详细数据
    public String selectWorkRiskDatabaseInfo(String orgCode,String riskName,String work,String riskRange) {
        String sql = " SELECT * FROM tb_risk_database_enterprise WHERE ";
        sql += " org_code = '"+orgCode+"'";

        sql += " and risk_name = '"+riskName+"'";

        sql += " and `work` = '"+work+"'";

        sql += " and risk_range = '"+riskRange+"'";

        return sql;
    }

}
