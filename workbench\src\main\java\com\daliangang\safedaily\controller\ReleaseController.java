package com.daliangang.safedaily.controller;

import com.daliangang.safedaily.entity.Release;
import com.daliangang.safedaily.form.NumForm;
import com.daliangang.safedaily.sql.ReleaseSql;
import com.daliangang.workbench.entity.Enterprise;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import xyz.erupt.core.annotation.EruptRouter;
import xyz.erupt.core.module.MetaUserinfo;
import xyz.erupt.core.view.EruptApiModel;
import xyz.erupt.toolkit.db.EruptResultMap;
import xyz.erupt.toolkit.remote.RemoteProxyService;
import xyz.erupt.toolkit.utils.EruptDaoUtils;
import xyz.erupt.toolkit.utils.SqlUtils;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since :2023/4/23:13:30
 */
@RestController
public class ReleaseController {
    @Resource
    private RemoteProxyService remoteProxyService;
    @Resource
    private ReleaseSql releaseSql;
    // 统计上报
    @RequestMapping("erupt-api/get/selectReleaseFillNum")
    @EruptRouter(verifyType = EruptRouter.VerifyType.LOGIN)
    public EruptApiModel selectReleaseFillNum() {
        MetaUserinfo remoteUserInfo = remoteProxyService.getRemoteUserInfo();
        String orgCode = SqlUtils.wrapIn(remoteUserInfo.getAuth());
        String sql = releaseSql.selectReleaseNum(orgCode);
        NumForm employeeCertificateForm = EruptDaoUtils.selectOne(sql, NumForm.class);
        return EruptApiModel.successApi(employeeCertificateForm);
    }
    //门户网站的安全承诺公告的板块
    @RequestMapping("/erupt-api/selectSubmitRelease")
    public EruptApiModel selectSubmitRelease(){
        //查询全部企业的已经提交的安全承诺公告
        String selectSql="select * from tb_release where submitted=1 order by filling_date desc";
        List<Release> releaseList = EruptDaoUtils.selectOnes(selectSql, Release.class);
        //查询所有的企业
        String selectSql2="select * from tb_enterprise";
        List<Enterprise> enterpriseList = EruptDaoUtils.selectOnes(selectSql2, Enterprise.class);
        Map<String, String> enterpriseMap = enterpriseList.stream().collect(Collectors.toMap(
                Enterprise::getOrgCode,
                Enterprise::getName,
                (v1, v2) -> v1
        ));
        for (Release release : releaseList) {
            release.setCompany(enterpriseMap.get(release.getCompany()));
        }
        return EruptApiModel.successApi(releaseList);
    }
    //门户网站的安全承诺公告的板块
    @RequestMapping("/erupt-api/selectSubmitRelease2")
    public EruptApiModel selectSubmitRelease2(@RequestBody Map map){
        //获取当前页数
        Integer pageNum = (Integer) map.get("pageNum");
        //获取条数
        Integer recordNum = (Integer) map.get("recordNum");
        //根据页数和条数查询全部企业的已经提交的安全承诺公告
        String selectSql="select * from tb_release where submitted=1 order by filling_date desc limit "+ (pageNum-1)*recordNum + ","+ recordNum;
        List<Release> releaseList = EruptDaoUtils.selectOnes(selectSql, Release.class);
        //查询所有的企业
        String selectSql2="select * from tb_enterprise";
        List<Enterprise> enterpriseList = EruptDaoUtils.selectOnes(selectSql2, Enterprise.class);
        Map<String, String> enterpriseMap = enterpriseList.stream().collect(Collectors.toMap(
                Enterprise::getOrgCode,
                Enterprise::getName,
                (v1, v2) -> v1
        ));
        //查询安全承诺公告的总数
        String selectSql3="select count(*) as count from tb_release where submitted=1";
        EruptResultMap eruptResultMap = EruptDaoUtils.selectMap(selectSql3);
        Object count = eruptResultMap.get("count");
        for (Release release : releaseList) {
            release.setCompany(enterpriseMap.get(release.getCompany()));
        }
        HashMap<String,Object> result = new HashMap<>();
        result.put("releaseList",releaseList);
        result.put("total",count);
        return EruptApiModel.successApi(result);
    }
    //门户网站的安全承诺公告的板块具体的某一条
    @RequestMapping("/erupt-api/selectSubmitReleaseDetail/{releaseId}")
    public EruptApiModel selectSubmitReleaseDetail(@PathVariable("releaseId")Long releaseId){
        //查询全部企业的已经提交的安全承诺公告
        String selectSql="select * from tb_release where id="+releaseId;
        Release release = EruptDaoUtils.selectOne(selectSql, Release.class);
        //根据企业编码查询企业名字
        String selectSql2="select * from tb_enterprise where org_code='"+release.getOrgCode()+"'";
        Enterprise enterprise = EruptDaoUtils.selectOne(selectSql2, Enterprise.class);
        release.setCompany(enterprise.getName());
        return EruptApiModel.successApi(release);
    }

    // 第二大屏统计今日上报
    @RequestMapping("erupt-api/get/selectReleaseFillNumTwo/{orgCode}")
    //@EruptRouter(verifyType = EruptRouter.VerifyType.LOGIN)
    public EruptApiModel selectReleaseFillNumTwo(@PathVariable("orgCode") String orgCode) {
      //  MetaUserinfo remoteUserInfo = remoteProxyService.getRemoteUserInfo();
       // String orgCode = SqlUtils.wrapIn(remoteUserInfo.getAuth());
        String sql = releaseSql.selectReleaseNumTwo(orgCode);
        NumForm employeeCertificateForm = EruptDaoUtils.selectOne(sql, NumForm.class);
        return EruptApiModel.successApi(employeeCertificateForm);
    }

}
