package com.daliangang.datascreen.serveboard.service;


import com.daliangang.datascreen.annotation.TokenCheck;
import com.daliangang.datascreen.response.AmountCountResponse;
import com.daliangang.datascreen.response.amountResult;
import com.daliangang.datascreen.utils.DictUtils;
import com.daliangang.datascreen.utils.HttpUtils;
import com.daliangang.datascreen.utils.OrgUtils;
import com.daliangang.emergency.entity.*;
import com.daliangang.majorisk.entity.RiskDatabaseEnterprise;
import com.daliangang.safedaily.entity.MSDS;
import com.daliangang.safedaily.entity.PortSafeWikiDir;
import com.daliangang.safedaily.entity.PortSafeWikiEntry;
import com.daliangang.workbench.entity.EmployeeInformation;
import com.daliangang.workbench.entity.Enterprise;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.springframework.stereotype.Service;
import xyz.erupt.core.context.MetaContext;
import xyz.erupt.jpa.dao.EruptDao;
import xyz.erupt.upms.model.template.EruptRoleTemplateUser;
import xyz.erupt.upms.service.EruptPlatformService;
import xyz.erupt.upms.service.EruptUserService;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.*;
import java.util.stream.Collectors;


@Service
@TokenCheck
public class ServeBoardService {
    @Resource
    private EruptDao eruptDao;
    @Resource
    private HttpServletRequest request;
    @Resource
    private EruptPlatformService eruptPlatformService;
    @Resource
    private EruptUserService eruptUserService;


    private static final Map<String, String> RISK_TYPE_CODE_TO_NUMBER = new HashMap<>();
    private static final String TKAQ_IP = "TKAQ_DOMAIN_URL";
    static {
        RISK_TYPE_CODE_TO_NUMBER.put("RISK_OF_POISONING__SUFFOCATION__FIRE_AND_EXPLOSION_IN_LOADING_AN", "12");
        RISK_TYPE_CODE_TO_NUMBER.put("RISK_OF_LEAKAGE__POISONING__FIRE_AND_EXPLOSION_IN_DANGEROUS_GOOD", "13");
        RISK_TYPE_CODE_TO_NUMBER.put("RISK_OF_LEAKAGE__POISONING__FIRE_AND_EXPLOSION_IN_LOADING_AND_UN", "14");
        RISK_TYPE_CODE_TO_NUMBER.put("RISK_OF_LEAKAGE__POISONING__FIRE_AND_EXPLOSION_IN_THE_TANK_FARM_", "15");
        RISK_TYPE_CODE_TO_NUMBER.put("RISK_OF_POISONING__SUFFOCATION__FIRE_AND_EXPLOSION_IN_INSPECTION", "16");
    }

    /**
     * 企业数量统计
     *
     * @return
     */

    public List<AmountCountResponse.amountContent> getEnterpriseCount() {
        // 获取所有企业
        List<Enterprise> enterprises = OrgUtils.getStartUpOrgCode();

        // 初始化返回内容列表
        List<AmountCountResponse.amountContent> contents = new ArrayList<>();

        // 添加企业总数统计
        contents.add(AmountCountResponse.amountContent.builder()
                .title("企业总数")
                .count(String.valueOf(enterprises.size()))
                .unit("家")
                .code("ENTERPRISE_COUNT")
                .build());

        // 统计不同类型的数量
        Map<String, Long> typeCountMap = enterprises.stream()
                .collect(Collectors.groupingBy(Enterprise::getType, Collectors.counting()));

        // 将每种类型的统计结果添加到列表
        typeCountMap.forEach((type, count) -> {
            // 将 type 转换为中文
            String chineseType = DictUtils.getDictItemValue("type", type);
            contents.add(AmountCountResponse.amountContent.builder()
                    .title(chineseType)  // 中文名称
                    .count(String.valueOf(count))  // 数量
                    .unit("家")
                    .code(type)// 单位
                    .build());
        });
        return contents;
    }

    /**
     * 应急救援资源
     *
     * @return
     */
    public List<AmountCountResponse.amountContent> getEmergencRrescue() {
        List<AmountCountResponse.amountContent> contents = new ArrayList<>();
        String code = eruptUserService.getCurrentEruptUser().getEruptOrg().getCode();
        // 获取所有企业
        List<String> orgCodes = OrgUtils.getCodes();
        orgCodes.add(code);
        //应急救援队伍
        List<Rescueteam> rescueteams = eruptDao.queryEntityList(Rescueteam.class, " orgCode IN :orgCodes", new HashMap<String, Object>() {{
            put("orgCodes", orgCodes);
        }});
        //敏感目标
        List<Sensitivetargets> sensitivetargets = eruptDao.queryEntityList(Sensitivetargets.class);
        //应急集合点
        List<EmergencyMuster> emergencyMusters = eruptDao.queryEntityList(EmergencyMuster.class, " orgCode IN :orgCodes", new HashMap<String, Object>() {{
            put("orgCodes", orgCodes);
        }});
        //医院
        List<Hospital> hospitals = eruptDao.queryEntityList(Hospital.class);
        //事故应急地
        List<EmergencyPool> emergencyPools = eruptDao.queryEntityList(EmergencyPool.class, "orgCode IN :orgCodes", new HashMap<String, Object>() {{
            put("orgCodes", orgCodes);
        }});
        //应急物资储备点
        List<MaterialReserve> materialReserves = eruptDao.queryEntityList(MaterialReserve.class, " orgCode IN :orgCodes", new HashMap<String, Object>() {{
            put("orgCodes", orgCodes);
        }});
        //应急专家
        List<EmergencyExpert> emergencyExperts = eruptDao.queryEntityList(EmergencyExpert.class);

        contents.add(new AmountCountResponse.amountContent("应急救援队伍", String.valueOf(rescueteams.size()), "个", "Rescueteam"));
        contents.add(new AmountCountResponse.amountContent("敏感目标", String.valueOf(sensitivetargets.size()), "个", "Sensitivetargets"));
        contents.add(new AmountCountResponse.amountContent("应急集合点", String.valueOf(emergencyMusters.size()), "个", "EmergencyMuster"));
        contents.add(new AmountCountResponse.amountContent("医院", String.valueOf(hospitals.size()), "个", "Hospital"));
        contents.add(new AmountCountResponse.amountContent("事故应急地", String.valueOf(emergencyPools.size()), "个", "EmergencyPool"));
        contents.add(new AmountCountResponse.amountContent("应急物资储备点", String.valueOf(materialReserves.size()), "个", "MaterialReserve"));
        contents.add(new AmountCountResponse.amountContent("应急专家", String.valueOf(emergencyExperts.size()), "个", "EmergencyExpert"));

        return contents;
    }

    /**
     * 安全法规统计
     *
     * @return
     */
    public List<AmountCountResponse.amountContent> getSafeLawCount() {
        List<AmountCountResponse.amountContent> contents = new ArrayList<>();

        // 先获取所有的目录信息
        List<PortSafeWikiDir> wikiDirs = eruptDao.queryEntityList(PortSafeWikiDir.class," id in (1,2,3,4,5,6)");
        Map<Long, String> dirNameMap = wikiDirs.stream()
                .collect(Collectors.toMap(PortSafeWikiDir::getId, PortSafeWikiDir::getName));

        // 获取所有条目
        List<PortSafeWikiEntry> safeWikiEntries = eruptDao.queryEntityList(PortSafeWikiEntry.class);

        // 统计不同类型的数量
        Map<Long, Long> riskType = safeWikiEntries.stream()
                .collect(Collectors.groupingBy(
                        entry -> entry.getWikiDir().getId(),
                        Collectors.counting()
                ));

        // 将每种类型的统计结果添加到列表
        riskType.forEach((dirId, count) -> {
            String dirName = dirNameMap.get(dirId); // 使用 ID 获取对应的名称
            if (dirName != null) {  // 只统计能找到目录名的记录
                contents.add(AmountCountResponse.amountContent.builder()
                        .title(dirName)  // 使用目录名称
                        .count(String.valueOf(count))
                        .unit("个")
                        .build());
            }
        });


        //MSDS
        List<MSDS> msds = eruptDao.queryEntityList(MSDS.class);
        contents.add(new AmountCountResponse.amountContent("MSDS", String.valueOf(msds.size()), "个", null));

        //应急知识
        List<EmergencyWiki> emergencyWikis = eruptDao.queryEntityList(EmergencyWiki.class);
        contents.add(new AmountCountResponse.amountContent("应急知识", String.valueOf(emergencyWikis.size()), "个", null));
        return contents;
    }

    /**
     * 事故案例
     *
     * @return
     */
    public List<AmountCountResponse.amountContent> getAccidentCase() {
        List<AmountCountResponse.amountContent> contents = new ArrayList<>();
        List<CaseBase> bases = eruptDao.queryEntityList(CaseBase.class);
        // 统计不同事故类型的数量
        Map<String, Long> riskType = bases.stream()
                .collect(Collectors.groupingBy(CaseBase::getAccidentType, Collectors.counting()));

        // 将每种类型的统计结果添加到列表
        riskType.forEach((type, count) -> {
            // 将 type 转换为中文
            String chineseType = DictUtils.getDictItemValue("accidentType", type);
            contents.add(AmountCountResponse.amountContent.builder()
                    .title(chineseType)  // 中文名称
                    .count(String.valueOf(count))  // 数量
                    .unit("个")  // 单位
                    .build());
        });
        return contents;
    }

    /**
     * 根据国内外统计事故案例数量
     *
     * @return 国内外统计列表
     */
    public List<AmountCountResponse.amountContent> getAccidentCaseByCountry() {
        List<AmountCountResponse.amountContent> contents = new ArrayList<>();
        List<CaseBase> bases = eruptDao.queryEntityList(CaseBase.class);

        // 国内外事故案例统计
        long domesticCount = bases.stream()
                .filter(base -> "中国".equals(base.getCountry()))
                .count();
        long foreignCount = bases.stream()
                .filter(base -> !"中国".equals(base.getCountry()))
                .count();

        contents.add(new AmountCountResponse.amountContent(
                "国内案例收纳数", String.valueOf(domesticCount), "个", null
        ));
        contents.add(new AmountCountResponse.amountContent(
                "国外案例收纳数", String.valueOf(foreignCount), "个", null
        ));

        return contents;
    }


    /**
     * 本年度应急能力评估
     *
     * @return
     */
    public List<AmountCountResponse.amountContent> getEmergencyAbility() {
        List<AmountCountResponse.amountContent> contents = new ArrayList<>();
        List<String> codes = OrgUtils.getCodes();
        List<EvaluationManage> manages = eruptDao.queryEntityList(EvaluationManage.class,
                " orgCode IN :orgCodes AND YEAR(evaDate) = :year AND publicState = '1'",
                new HashMap<String, Object>() {{
                    put("orgCodes", OrgUtils.getDeptCodes(null));
                    put("year", Calendar.getInstance().get(Calendar.YEAR));
                }});

        // 对 orgCode 去重
        Map<String, EvaluationManage> uniqueManages = manages.stream()
                .collect(Collectors.toMap(
                        EvaluationManage::getOrgCode, // 使用 orgCode 作为唯一标识
                        m -> m,
                        (existing, replacement) -> existing // 如果重复，保留已有的
                ));

        // 转为去重后的集合
        List<EvaluationManage> distinctManages = new ArrayList<>(uniqueManages.values());

        // 统计C等级的数量
        long cCount = distinctManages.stream()
                .filter(m -> m.getGrade() != null && "C".equals(m.getGrade().getName()))
                .count();

        contents.add(new AmountCountResponse.amountContent("已评估企业数", String.valueOf(distinctManages.size()), "个", "IS_EVALUATED"));
        contents.add(new AmountCountResponse.amountContent("不合格企业数", String.valueOf(cCount), "个", "NOT_STANDAR"));
        contents.add(new AmountCountResponse.amountContent("未评估企业数", String.valueOf(codes.size() - distinctManages.size()), "个", "NOT_EVALUATED"));

        return contents;
    }

    /**
     * 未评估企业列表
     * @return
     */
    public List<Map<String, String>> getNotEvaluatedCompanies() {
        // 获取所有企业的 orgCodes
        List<String> allOrgCodes = OrgUtils.getCodes();

        // 查询已评估的企业 orgCodes
        List<EvaluationManage> evaluatedManages = eruptDao.queryEntityList(
                EvaluationManage.class,
                "orgCode IN :orgCodes AND YEAR(evaDate) = :year AND publicState = '1'",
                new HashMap<String, Object>() {{
                    put("orgCodes", allOrgCodes);
                    put("year", Calendar.getInstance().get(Calendar.YEAR));
                }}
        );

        // 提取已评估的 orgCodes
        Set<String> evaluatedOrgCodes = evaluatedManages.stream()
                .map(EvaluationManage::getOrgCode)
                .collect(Collectors.toSet());

        // 筛选出未评估的 orgCodes
        List<String> notEvaluatedOrgCodes = allOrgCodes.stream()
                .filter(orgCode -> !evaluatedOrgCodes.contains(orgCode))
                .collect(Collectors.toList());

        // 查询未评估企业的名称
        List<Enterprise> enterprises = eruptDao.queryEntityList(
                Enterprise.class,
                "orgCode IN :orgCodes",
                new HashMap<String, Object>() {{
                    put("orgCodes", notEvaluatedOrgCodes);
                }}
        );

        // 创建 orgCode -> companyName 映射
        Map<String, String> orgCodeToNameMap = enterprises.stream()
                .collect(Collectors.toMap(Enterprise::getOrgCode, Enterprise::getName));

        // 构建结果集合
        List<Map<String, String>> result = notEvaluatedOrgCodes.stream()
                .map(orgCode -> {
                    Map<String, String> map = new HashMap<>();
                    map.put("orgCode", orgCode);
                    map.put("companyName", orgCodeToNameMap.getOrDefault(orgCode, "未知"));
                    return map;
                })
                .collect(Collectors.toList());

        return result;
    }



    /**
     * 用户总数
     * @return
     */
    public List<AmountCountResponse.amountContent> getUserCount() {
        List<AmountCountResponse.amountContent> contents = new ArrayList<>();
        List<String> codes = OrgUtils.getDeptCodes(null);
        //用户总数
        List<EruptRoleTemplateUser> users = eruptDao.queryEntityList(EruptRoleTemplateUser.class, " orgCode IN :code",
                new HashMap<String, Object>() {{
                    put("code", codes);
                }}
        );
        contents.add(AmountCountResponse.amountContent.builder()
                .title("用户总数")
                .count(String.valueOf(users.size()))
                .unit("个")
                .code("USER_COUNT")
                .build());
        //政府用户
        int deptCount = 0;
        for (EruptRoleTemplateUser user : users) {
            String baseRole = user.getTemplate().getBaseRole();
            if (baseRole.equals("101") || baseRole.equals("102") || baseRole.equals("108") || baseRole.equals("109"))
                deptCount++;
        }
        contents.add(AmountCountResponse.amountContent.builder()
                .title("政府用户")
                .count(String.valueOf(deptCount))
                .unit("个")
                .code("GOVERNMENT_USER")
                .build());
        //执法人员
        List<EmployeeInformation> checkPersons = eruptDao.queryEntityList(EmployeeInformation.class, " checkPerson = '1'");
        contents.add(AmountCountResponse.amountContent.builder()
                .title("执法人员")
                .count(String.valueOf(checkPersons.size()))
                .unit("个")
                .code("CHECK_PERSON")
                .build());
        //企业用户
        contents.add(AmountCountResponse.amountContent.builder()
                .title("企业用户")
                .count(String.valueOf(users.size() - deptCount))
                .unit("个")
                .code("ENTERPRISE_USER")
                .build());
        return contents;
    }

    /**
     * 重大风险总数
     * @return
     */
    public List<AmountCountResponse.amountContent> getRiskCount() {
        List<AmountCountResponse.amountContent> contents = new ArrayList<>();
        List<String> codes = OrgUtils.getDeptCodes(null);
        //重大风险总数
        List<RiskDatabaseEnterprise> riskDatas = eruptDao.queryEntityList(RiskDatabaseEnterprise.class, " orgCode IN :orgCode",
                new HashMap<String, Object>() {{
                    put("orgCode", codes);
                }});
        contents.add(AmountCountResponse.amountContent.builder()
                .title("重大风险总数")
                .count(String.valueOf(riskDatas.size()))
                .unit("个")
                .code("RISK_COUNT")
                .build());

        // 统计不同类型的数量
        Map<String, Long> riskType = riskDatas.stream()
                .collect(Collectors.groupingBy(RiskDatabaseEnterprise::getRiskType, Collectors.counting()));

        // 将每种类型的统计结果添加到列表
        riskType.forEach((type, count) -> {
            // 将 type 转换为中文
            // 从映射表中获取编号
            String riskNumber = RISK_TYPE_CODE_TO_NUMBER.getOrDefault(type, "未知");
            contents.add(AmountCountResponse.amountContent.builder()
                    .title("风险"+riskNumber)  // 中文名称
                    .count(String.valueOf(count))  // 数量
                    .unit("个")
                    .code(type)// 单位
                    .build());
        });
        return contents;
    }

    /**
     * 岗位胜任力评估
     * @return
     */
    public List<amountResult> getPositionCompetence() {
        // 确保在调用服务方法前注册 token
        MetaContext.registerToken(request.getHeader("token"));
        List<String> codes = OrgUtils.getCodes();
        String ip = eruptPlatformService.getOption(TKAQ_IP).getAsString();
        String ret = HttpUtils.doPost(ip + "/post/competence/assessment/query ",codes, request.getHeader("token"));
        // 使用 Jackson 解析返回的 JSON 字符串
        ObjectMapper objectMapper = new ObjectMapper();
        List<amountResult> result = null;
        try {
            result = objectMapper.readValue(ret, new TypeReference<List<amountResult>>() {
            });
        } catch (Exception ignored) {

        }finally {
            MetaContext.remove();
        }
        return result;
    }


    /**
     * 获取最近一次评估
     * @return
     */
    public List<amountResult> getLastEvaluation() {
        // 确保在调用服务方法前注册 token
        MetaContext.registerToken(request.getHeader("token"));
        List<String> codes = OrgUtils.getCodes();
        String ip = eruptPlatformService.getOption(TKAQ_IP).getAsString();
        String ret = HttpUtils.doPost(ip + "/recent/assessment/histogram/query ",codes, request.getHeader("token"));
        // 使用 Jackson 解析返回的 JSON 字符串
        ObjectMapper objectMapper = new ObjectMapper();
        List<amountResult> result = null;
        try {
            result = objectMapper.readValue(ret, new TypeReference<List<amountResult>>() {
            });
        } catch (Exception ignored) {

        }finally {
            MetaContext.remove();
        }
        return result;

    }

    /**
     * 获取教育培训
     * @return
     */
    public List<AmountCountResponse.amountContent> getEducationTraining() {
        // 确保在调用服务方法前注册 token
        MetaContext.registerToken(request.getHeader("token"));
        List<String> codes = OrgUtils.getCodes();
        String ip = eruptPlatformService.getOption(TKAQ_IP).getAsString();
        String ret = HttpUtils.doPost(ip + "/education/training/query ",codes, request.getHeader("token"));
        // 使用 Jackson 解析返回的 JSON 字符串
        ObjectMapper objectMapper = new ObjectMapper();
        List<AmountCountResponse.amountContent> result = null;
        try {
            result = objectMapper.readValue(ret, new TypeReference<List<AmountCountResponse.amountContent>>() {
            });
        } catch (Exception ignored) {
        }finally {
            MetaContext.remove();
        }
        return result;
    }

    /**
     * 获取最近一次培训
     * @return
     */
    public List<AmountCountResponse.amountContent> getLastTraining() {
        // 确保在调用服务方法前注册 token
        MetaContext.registerToken(request.getHeader("token"));
        List<String> codes = OrgUtils.getCodes();
        String ip = eruptPlatformService.getOption(TKAQ_IP).getAsString();
        String ret = HttpUtils.doPost(ip + "/recent/training/situation/query ",codes, request.getHeader("token"));
        // 使用 Jackson 解析返回的 JSON 字符串
        ObjectMapper objectMapper = new ObjectMapper();
        List<AmountCountResponse.amountContent> result = null;
        try {
            result = objectMapper.readValue(ret, new TypeReference<List<AmountCountResponse.amountContent>>() {
            });
        } catch (Exception ignored) {
        }finally {
            MetaContext.remove();
        }
        return result;

    }
}

