/*
 * Copyright © 2020-2035 yundingyun All rights reserved.
 */
package com.daliangang.rndpub.proxy;

import com.daliangang.rndpub.entity.Rectification;
import org.springframework.stereotype.Service;
import xyz.erupt.annotation.fun.DataProxy;
import xyz.erupt.annotation.query.Condition;
import xyz.erupt.core.util.EruptSpringUtil;
import xyz.erupt.toolkit.utils.EruptDaoUtils;
import xyz.erupt.upms.model.EruptUser;
import xyz.erupt.upms.service.EruptUserService;

import java.time.LocalDate;
import java.time.ZoneId;
import java.util.*;
import java.util.concurrent.atomic.AtomicReference;

@Service
public class RectificationDataProxy implements DataProxy<Rectification> {

    @Override
    public String beforeFetch(List<Condition> conditions) {
        AtomicReference<String> returnStr = new AtomicReference<>(" 1 = 1 and publish_status = 1");
        EruptUser currentEruptUser = EruptSpringUtil.getBean(EruptUserService.class).getCurrentEruptUser();
        if (currentEruptUser != null && currentEruptUser.getEruptOrg() != null) {
            returnStr.set(returnStr.get() + " and check_object = '" + currentEruptUser.getEruptOrg().getCode() + "'");
        }
        //是否逾期查询条件
        Optional<Condition> beOverdueFirst = conditions.stream().filter(condition ->
                "beOverdue".equals(condition.getKey())
        ).findFirst();
        beOverdueFirst.ifPresent(f -> {
            Object value = f.getValue();
            if (value instanceof List) {
                List<?> list = (List<?>) value;
                Set<String> statusSet = new HashSet<>(list.size());
                for (Object item : list) {
                    if (item instanceof String) {
                        statusSet.add((String) item);
                    } else {
                        throw new IllegalArgumentException("Condition 'beOverdue' list contains non-string elements");
                    }
                }

                if (statusSet.contains("0") && statusSet.contains("1") && statusSet.contains("2")) {
                    // 如果是全选或者包含所有状态，不添加额外条件
                } else {
                    List<String> conditionsList = new ArrayList<>();
                    if (statusSet.contains("0")) {
                        conditionsList.add("date(rectification_time) <= date(deadline)");
                    }
                    if (statusSet.contains("1")) {
                        conditionsList.add("date(rectification_time) > date(deadline) and rectification_audit_status != 'Audited'");
                    }
                    if (statusSet.contains("2")) {
                        conditionsList.add("date(rectification_time) > date(deadline) and rectification_audit_status = 'Audited'");
                    }
                    if (!conditionsList.isEmpty()) {
                        returnStr.set(returnStr.get() + " and (" + String.join(" or ", conditionsList) + ")");
                    }
                }

                conditions.remove(f);
            } else if (value instanceof String) {
                String overdueStatus = (String) value;
                switch (overdueStatus) {
                    case "0":
                        returnStr.set(returnStr.get() + " and date(rectification_time) <= date(deadline)");
                        break;
                    case "1":
                        returnStr.set(returnStr.get() + " and date(rectification_time) > date(deadline) and rectification_audit_status != 'Audited'");
                        break;
                    case "2":
                        returnStr.set(returnStr.get() + " and date(rectification_time) > date(deadline) and rectification_audit_status = 'Audited'");
                        break;
                    default:
                        break;
                }
                conditions.remove(f);
            } else {
                throw new IllegalArgumentException("Condition 'beOverdue' should be a String or a List containing Strings");
            }
        });
        /*beOverdueFirst.ifPresent(f -> {
            String value = (String) f.getValue();
            switch (value) {
                case "0":
                    returnStr.set(returnStr.get() + " and date(now()) > date(deadline)");
                    break;
                case "1":
                    returnStr.set(returnStr.get() + " and date(now()) <= date(deadline)");
                    break;
                case "all":
                    // 如果状态是"all"，则不添加额外条件
                    break;
                default:
                    throw new IllegalArgumentException("Unknown beOverdue status: " + value);
            }
            //returnStr.set((Boolean) f.getValue() ? returnStr.get() + " and date(now()) <=date(deadline)" : returnStr.get() + " and date(now()) >date(deadline)");
            conditions.remove(f);
        });*/
        return returnStr.get();
    }

    @Override
    public void afterFetch(Collection<Map<String, Object>> list) {
//        Date now = new Date(System.currentTimeMillis());
        LocalDate localDateTime = LocalDate.now();
        ZoneId zoneId = ZoneId.systemDefault();
        Date now = Date.from(localDateTime.atStartOfDay().atZone(zoneId).toInstant());
        List<Rectification> fields = EruptDaoUtils.convert(list, Rectification.class);
        for (Rectification field : fields) {
            if (null != field.getDeadline()) {
                //拿到当前日期，判断是否逾期
                //如果当前日期小于等于截止日期

                if ((field.getRectificationTime()==null?now:field.getRectificationTime()).compareTo(field.getDeadline()) <= 0) {
                    // 未逾期
                    EruptDaoUtils.updateAfterFetch(list, field.getId(), "beOverdue", "0");
                } else {
                    // 逾期
                    if (field.getRectificationAuditStatus() == null || !field.getRectificationAuditStatus().equals("Audited")) {
                        // 审查结果为空或未通过
                        EruptDaoUtils.updateAfterFetch(list, field.getId(), "beOverdue", "1");
                    } else {
                        // 逾期已整改
                        EruptDaoUtils.updateAfterFetch(list, field.getId(), "beOverdue", "2");
                    }
                }
                //EruptDaoUtils.updateAfterFetch(list, field.getId(), "beOverdue", now.compareTo(field.getDeadline()) <= 0);
            }

        }
    }
}
