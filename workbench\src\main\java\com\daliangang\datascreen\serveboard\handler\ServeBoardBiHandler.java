package com.daliangang.datascreen.serveboard.handler;

import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.stereotype.Service;
import xyz.erupt.bi.fun.EruptBiHandler;

import java.util.List;
import java.util.Map;

/**
 * @Author: chongmenglin
 * @Date: 2024/11/18 16:28
 */
@Service
public class ServeBoardBiHandler implements EruptBiHandler {
    @Override
    public void resultHandler(String param, Map<String, Object> condition, List<Map<String, Object>> result) {
        EruptBiHandler.super.resultHandler(param, condition, result);
    }

    @Override
    public void exportHandler(String param, Map<String, Object> condition, Workbook workbook) {
        EruptBiHandler.super.exportHandler(param, condition, workbook);
    }

    @Override
    public String exprHandler(String param, Map<String, Object> condition, String expr) {
        return EruptBiHandler.super.exprHandler(param, condition, expr);
    }
}
