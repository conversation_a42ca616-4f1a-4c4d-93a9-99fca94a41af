/*
 * Copyright © 2020-2035 yundingyun All rights reserved.
 */
package com.daliangang.safedaily.entity;

import com.daliangang.core.CompanyRenderHandler;
import com.daliangang.safedaily.proxy.QualificationManageDataProxy;
import com.daliangang.workbench.handler.EmployeeExternalRenderHandler;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import xyz.erupt.annotation.Erupt;
import xyz.erupt.annotation.EruptField;
import xyz.erupt.annotation.expr.ExprBool;
import xyz.erupt.annotation.sub_erupt.Power;
import xyz.erupt.annotation.sub_erupt.RowOperation;
import xyz.erupt.annotation.sub_field.Edit;
import xyz.erupt.annotation.sub_field.EditType;
import xyz.erupt.annotation.sub_field.Readonly;
import xyz.erupt.annotation.sub_field.View;
import xyz.erupt.annotation.sub_field.sub_edit.*;
import xyz.erupt.mns.core.DelayMsg;
import xyz.erupt.mns.core.DelayMsgProxy;
import xyz.erupt.toolkit.attachment.AttachmentDownloadHandler;
import xyz.erupt.toolkit.colorfont.ColorStateTimeFont;
import xyz.erupt.toolkit.colorfont.ColorStateTimeFontDataProxy;
import xyz.erupt.toolkit.db.Comment;
import xyz.erupt.toolkit.handler.DelOperationHandler;
import xyz.erupt.toolkit.handler.EditOperationHandler;
import xyz.erupt.toolkit.handler.EruptDictCodeChoiceFetchHandler;
import xyz.erupt.toolkit.handler.RemoteEntityChoiceFetchHandler;
import xyz.erupt.toolkit.utils.TplUtils;
import xyz.erupt.upms.model.auth.DataAuthHandler;
import xyz.erupt.upms.model.auth.DataAuthModel;

import javax.persistence.Entity;
import javax.persistence.Lob;
import javax.persistence.Table;
import javax.persistence.Transient;

@Erupt(name = "作业资质年度检查管理", power = @Power(add = true, delete = false, export = false, importable = true, viewDetails = true, edit = false)
        , dataProxy = {QualificationManageDataProxy.class, DelayMsgProxy.class, ColorStateTimeFontDataProxy.class}
        , rowOperation = {
        @RowOperation(title = "编辑", icon = "fa fa-edit", code = TplUtils.EDIT_OPER_CODE, eruptClass = QualificationManage.class,show = @ExprBool( exprHandler = EmployeeExternalRenderHandler.class),
                operationHandler = EditOperationHandler.class,  ifExprBehavior = RowOperation.IfExprBehavior.HIDE, mode = RowOperation.Mode.SINGLE),
        @RowOperation(title = "删除",show = @ExprBool( exprHandler = EmployeeExternalRenderHandler.class), icon = "fa fa-trash-o", operationHandler = DelOperationHandler.class, ifExprBehavior = RowOperation.IfExprBehavior.HIDE, mode = RowOperation.Mode.SINGLE),
//        @RowOperation(title = "上报", icon = "fa fa-arrow-circle-o-up", operationHandler = QualificationManageEscalationHandler.class, ifExpr = "item.submitted=='未上报'", mode = RowOperation.Mode.SINGLE),
//@RowOperation(title = "上传附证", icon = "fa fa-arrow-circle-o-up", operationHandler = QualificationManageUploadAttachedCertificateHandler.class, mode = RowOperation.Mode.SINGLE),
        @RowOperation(title = "下载附件", icon = "fa fa-download", operationHandler = AttachmentDownloadHandler.class, mode = RowOperation.Mode.SINGLE)
})
@Table(name = "tb_qualification_manage")
@Entity
@Getter
@Setter
@Comment("作业资质年度检查管理")
@ApiModel("作业资质年度检查管理")
@DelayMsg(timeKey = "effectiveDate")
@ColorStateTimeFont(stateKey = "stateStr", timeKey = "checkDate", interval = 1, unit = ColorStateTimeFont.TimeUnit.MONTH)
public class QualificationManage extends DataAuthModel {
    @EruptField(
            views = @View(title = "附证编号", show = false),
            edit = @Edit(title = "附证编号", type = EditType.INPUT, show = false,
                    inputType = @InputType))
    @Comment("附证编号")
    @ApiModelProperty("附证编号")
    private String number;

    @EruptField(
            views = @View(title = "企业名称"),
            edit = @Edit(title = "企业名称", search = @Search(ifRender = @ExprBool(exprHandler = CompanyRenderHandler.class)),
                    readonly = @Readonly(exprHandler = DataAuthHandler.class), type = EditType.CHOICE, notNull = true,
                    choiceType = @ChoiceType(fullSpan = true,fetchHandler = RemoteEntityChoiceFetchHandler.class, fetchHandlerParams = {"main", "Enterprise", "org_code,name"})))
    @Comment("企业名称")
    @ApiModelProperty("企业名称")
    private String company;


    @EruptField(
            views = @View(title = "年度审验日期"),
            edit = @Edit(title = "年度审验日期", type = EditType.DATE, notNull = true,
                    search = @Search(vague = true),
                    dateType = @DateType))
    @Comment("年度审验日期")
    @ApiModelProperty("年度审验日期")
    private java.util.Date checkDate;


    @Lob
    @EruptField(
            views = @View(title = "审验证书"),
            edit = @Edit(title = "审验证书", type = EditType.TEXTAREA, notNull = true))
    @Comment("审验证书")
    @ApiModelProperty("审验证书")
    private String certificate;

    @EruptField(
            views = @View(title = "上报状态",show = false),
            edit = @Edit(title = "上报状态", type = EditType.BOOLEAN, show = false, /*notNull = true, */
                    boolType = @BoolType(trueText = "已上报", falseText = "未上报")))
    @Comment("上报状态")
    @ApiModelProperty("上报状态")
    private Boolean submitted;

//	@EruptField(
//		views = @View(title = "经营范围"),
//		edit = @Edit(title = "经营范围", type = EditType.INPUT, notNull = true,
//		inputType = @InputType))
//	@Comment("经营范围")
//	@ApiModelProperty("经营范围")
//	private String businessScope;

//	@EruptField(
//		views = @View(title = "作业方式"),
//		edit = @Edit(title = "作业方式", type = EditType.INPUT, notNull = true,
//		inputType = @InputType))
//	@Comment("作业方式")
//	@ApiModelProperty("作业方式")
//	private String work;

//	@EruptField(
//		views = @View(title = "发证日期"),
//		edit = @Edit(title = "发证日期", type = EditType.DATE, notNull = true,
//		dateType = @DateType))
//	@Comment("发证日期")
//	@ApiModelProperty("发证日期")
//	private java.util.Date date;

//	@EruptField(
//		views = @View(title = "有效期至"),
//		edit = @Edit(title = "有效期至", type = EditType.DATE, notNull = true, dateType = @DateType))
//	@Comment("有效期至")
//	@ApiModelProperty("有效期至")
//	private java.util.Date effectiveDate;

    @EruptField(
            views = @View(title = "状态", show = false),
            edit = @Edit(title = "状态", type = EditType.CHOICE, notNull = false, show = false, readonly = @Readonly(exprHandler = DataAuthHandler.class),
                    search = @Search,
                    choiceType = @ChoiceType(fetchHandler = EruptDictCodeChoiceFetchHandler.class, fetchHandlerParams = "expiredState")))
    @Comment("状态")
    @ApiModelProperty("状态")
    private String state;

    @EruptField(
            views = @View(title = "状态", show = true),
            edit = @Edit(title = "状态", type = EditType.INPUT, notNull = false, show = false))
    @Transient
    private String stateStr;


    @EruptField(
            views = @View(title = "港口危险货物作业附证（加注年审意见并盖章）", show = false),
            edit = @Edit(title = "港口危险货物作业附证（加注年审意见并盖章）", type = EditType.ATTACHMENT, notNull = true,
                    attachmentType = @AttachmentType(type = AttachmentType.Type.BASE, maxLimit = 20, fileTypes = {".pdf", ".doc", ".docx", ".xlsx", ".png", ".jpg", ".xls", ".ppt", ".pptx", ".zip", ".rar"})))
    @Comment("港口危险货物作业附证（加注年审意见并盖章）")
    @ApiModelProperty("港口危险货物作业附证（加注年审意见并盖章）")
    private String file;

    @EruptField(
            views = @View(title = "是否上报", show = false),
            edit = @Edit(title = "是否上报", type = EditType.BOOLEAN, show = false,
                    boolType = @BoolType(trueText = "是", falseText = "否")))
    @Comment("是否上报")
    @ApiModelProperty("是否上报")
    private Boolean isReport;


}
