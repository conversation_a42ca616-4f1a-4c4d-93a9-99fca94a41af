
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <base href="/">
    <title>erupt</title>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
<link rel="stylesheet" href="./css/element-ui.css"/>
  <style>
    #app {
      font-family: 'Avenir', Helvetica, Arial, sans-serif;
      -webkit-font-smoothing: antialiased;
      -moz-osx-font-smoothing: grayscale;
      color: #2c3e50;
    }

    body,
    html {
      margin: 0;
      padding: 0;
    }

    .inspection-process {
      padding: 24px;

    }

    .process-one {
      display: flex;
      padding-bottom: 30px;

    }

    .list {
      width: 33.3%;
      padding: 0 8px;
      box-sizing: border-box;

    }

    .label {
      padding-bottom: 8px;
      font-weight: bold;
    }

    .el-collapse-item__header {
      font-size: 16px;
      font-weight: bold;
      padding-left: 6px;
    }

    .el-pagination {
      text-align: center;
      margin-top: 6px;
    }

    .el-collapse-item__header {
      background: #fafafa;
    }
  </style>
</head>

<body>
  <div id="app">
    <div class="inspection-process">
      <div class="process-one">
        <div class="list" v-for="item,index in processList" :key="index">
          <div class="label">{{ item.label }}</div>
          <div class="content">
            <el-input type="text" disabled :value="item.value"></el-input>
          </div>
        </div>
      </div>
      <el-collapse v-model="activeNames">
        <el-collapse-item title="抽取企业" name="1">
          <el-table :data="firmList" border style="width: 100%">
            <el-table-column prop="enterpriseName" label="企业名称">
            </el-table-column>
            <el-table-column prop="type" label="企业类别">
            </el-table-column>
            <el-table-column prop="lastSpotCheck" label="上次抽查时间">
            </el-table-column>
          </el-table>
          <el-pagination @size-change="firmSizeChange" @current-change="firmCurrentChange" :current-page="firmPage"
            :page-sizes="[10, 20, 30, 40]" :page-size="10" layout="total, sizes, prev, pager, next, jumper"
            :total="firmTotal">
          </el-pagination>
        </el-collapse-item>
        <el-collapse-item title="抽取专家" name="2">
          <el-table :data="expertList" border style="width: 100%">
            <el-table-column prop="name" label="专家姓名">
            </el-table-column>
            <el-table-column prop="sex" label="性别">
            </el-table-column>
            <el-table-column prop="technical" label="职称">
            </el-table-column>
            <el-table-column prop="expertCompany" label="企业名称">
            </el-table-column>
            <el-table-column prop="checkScope" label="检查范围">
            </el-table-column>
          </el-table>
          <el-pagination @size-change="expertSizeChange" @current-change="expertCurrentChange"
            :current-page="expertPage" :page-sizes="[10, 20, 30, 40]" :page-size="10"
            layout="total, sizes, prev, pager, next, jumper" :total="expertTotal">
          </el-pagination>
        </el-collapse-item>
        <el-collapse-item title="抽取检查人员" name="3">
          <el-table :data="inspectorList" border style="width: 100%">
            <el-table-column prop="name" label="检查人员姓名">
            </el-table-column>
            <el-table-column prop="sex" label="性别">
            </el-table-column>
            <el-table-column prop="phone" label="手机号">
            </el-table-column>
            <el-table-column prop="company" label="企业名称">
            </el-table-column>
            <el-table-column prop="checkScope" label="检查范围">
            </el-table-column>
          </el-table>
          <el-pagination @size-change="inspectorSizeChange" @current-change="inspectorCurrentChange"
            :current-page="inspectorPage" :page-sizes="[10, 20, 30, 40]" :page-size="10"
            layout="total, sizes, prev, pager, next, jumper" :total="inspectorTotal">
          </el-pagination>
        </el-collapse-item>
      </el-collapse>

    </div>
  </div>
</body>
<script src="./js/vue.min.js"></script>
<script src="./js/element-ui.js"></script>
<script src="./js/axios.min.js"></script>
<script>
  let baseUrl = window.location.origin+'/erupt-api/';
  // let baseUrl = 'http://dlg.yundingyun.net:8080';
  var app = new Vue({
    el: '#app',
    data: function () {
      return {
        activeNames: ["1", "2", "3"],
        processList: [
          {
            label: "检查名称",
            value: "",
          },
          {
            label: "检查开始日期",
            value: "",
          },
        ],
        firmList: [],
        firmSize: 10,
        firmPage: 1,
        firmTotal: 0,
        expertList: [],
        expertSize: 10,
        expertPage: 1,
        expertTotal: 0,
        inspectorList: [],
        inspectorSize: 10,
        inspectorPage: 1,
        inspectorTotal: 0,
        id: '15',
        token:''
      }
    },
    created() {
      let params = new URLSearchParams(window.location.search);
      this.id = params.get('ids')
      this.token=JSON.parse(window.localStorage.getItem('_token')).token||''
      // 获取字段
      this.getFields()
      // // 获取抽取专家
       this.getExpertList()
      // // 获取抽取人员
       this.getInspectorList()
      // // 获取抽取企业
       this.getFirmList()
    },
    methods: {
      getFields() {
        axios({
          url: baseUrl + 'data/Procedure/'+this.id,
          method: 'get',
          headers: {
            'Erupt': 'Procedure',
            'Content-Type': 'application/json;charset=UTF-8', // 配置请求头
            'token': this.token
          },
        }).then(data => {
          if (data.data) {
            this.processList[0].value = data.data.name
            this.processList[1].value = data.data.inspectionDate ? data.data.inspectionDate.split(' ')[0] : ''
          }
        })
      },
      getFirmList() {
        axios({
          url: baseUrl + 'data/Procedure/drill/EnterpriseInformation/'+this.id,
          method: 'post',
          headers: {
            'Erupt': 'Procedure',
            'Content-Type': 'application/json;charset=UTF-8', // 配置请求头
            'token': this.token
          },
          data: {
            pageIndex: this.firmPage || 1,
            pageSize: this.firmSize || 10,
            condition: [{ "key": "state", "value": true }],
            sort: ""
          }
        }).then(data => {
          this.firmList = data.data.list
          this.firmTotal = Number(data.data.total) || 0
        })

      },
      firmSizeChange(val) {
        this.firmSize = val
        this.getFirmList()
      },
      firmCurrentChange(val) {
        this.firmPage = val
        this.getFirmList()
      },

      getExpertList() {
        axios({
          url: baseUrl + 'data/Procedure/drill/ExpertInformation/'+this.id,
          method: 'post',
          headers: {
            'Erupt': 'Procedure',
            'Content-Type': 'application/json;charset=UTF-8', // 配置请求头
            'token': this.token
          },
          data: {
            pageIndex: this.expertPage || 1,
            pageSize: this.expertSize || 10,
            condition: [{ "key": "state", "value": true }],
            sort: ""
          }
        }).then(data => {
          console.log(data, '123')
          this.expertList = data.data.list
          this.expertTotal = Number(data.data.total) || 0
        })
      },
      expertSizeChange(val) {
        this.expertSize = val
        this.getExpertList()
      },
      expertCurrentChange(val) {
        this.expertPage = val
        this.getExpertList()
      },

      getInspectorList() {
        axios({
          url: baseUrl + 'data/Procedure/drill/Inspector/'+this.id,
          method: 'post',
          headers: {
            'Erupt': 'Procedure',
            'Content-Type': 'application/json;charset=UTF-8', // 配置请求头
            'token': this.token
          },
          data: {
            pageIndex: this.inspectorPage || 1,
            pageSize: this.inspectorSize || 10,
            condition: [{ "key": "state", "value": true }],
            sort: ""
          }
        }).then(data => {
          this.inspectorList = data.data.list
          this.inspectorTotal = Number(data.data.total) || 0
        })
      },
      inspectorSizeChange(val) {
        this.inspectorSize = val
        this.getInspectorList()
      },
      inspectorCurrentChange(val) {
        this.inspectorPage = val
        this.getInspectorList()
      },




      //获取专业
      getMajor() {
        axios({
          url: baseUrl + 'data/Expert/getData',
          method: 'get'
        }).then(res => {
          this.EducationalList = res.data.data.educational;
          this.ProfessionalList = res.data.data.technical;
        })
      },

    }

  })
</script>

</html>
