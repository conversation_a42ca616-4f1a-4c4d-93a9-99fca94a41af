/*
 * Copyright © 2020-2035 yundingyun All rights reserved.
 */
package com.daliangang.device.entity;

import com.daliangang.core.CompanyRenderHandler;
import com.daliangang.device.proxy.StorageTankDataProxy;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import xyz.erupt.annotation.Erupt;
import xyz.erupt.annotation.EruptField;
import xyz.erupt.annotation.expr.ExprBool;
import xyz.erupt.annotation.sub_erupt.Power;
import xyz.erupt.annotation.sub_field.Edit;
import xyz.erupt.annotation.sub_field.EditType;
import xyz.erupt.annotation.sub_field.Readonly;
import xyz.erupt.annotation.sub_field.View;
import xyz.erupt.annotation.sub_field.sub_edit.*;
import xyz.erupt.toolkit.db.Comment;
import xyz.erupt.toolkit.handler.RemoteCallChoiceFetchHandler;
import xyz.erupt.toolkit.handler.RemoteEntityChoiceFetchHandler;
import xyz.erupt.toolkit.handler.SqlChoiceFetchHandler;
import xyz.erupt.upms.model.auth.DataAuthHandler;
import xyz.erupt.upms.model.auth.DataAuthModel;

import javax.persistence.Entity;
import javax.persistence.Lob;
import javax.persistence.Table;

@Erupt(name = "储罐管理", importTruncate = true, power = @Power(add = true, delete = true, export = false, importable = true, viewDetails = true, edit = true)
        , dataProxy = StorageTankDataProxy.class
        , rowOperation = {})
@Table(name = "tb_storage_tank")
@Entity
@Getter
@Setter
@Comment("储罐管理")
@ApiModel("储罐管理")
public class StorageTank extends DataAuthModel {
    @EruptField(
            views = @View(title = "企业名称"),
            edit = @Edit(title = "企业名称", search = @Search(ifRender = @ExprBool(exprHandler = CompanyRenderHandler.class)),
                    readonly = @Readonly(exprHandler = DataAuthHandler.class), type = EditType.CHOICE, notNull = true,
                    choiceType = @ChoiceType(fullSpan = true,reload = true, fetchHandler = RemoteEntityChoiceFetchHandler.class, fetchHandlerParams = {"main", "Enterprise", "org_code,name"})))
    @Comment("企业名称")
    @ApiModelProperty("企业名称")
    private String company;

    @EruptField(
            views = @View(title = "储罐名称"),
            edit = @Edit(title = "储罐名称", type = EditType.INPUT, search = @Search(vague = true), notNull = true,
                    inputType = @InputType))
    @Comment("储罐名称")
    @ApiModelProperty("储罐名称")
    private String name;

    @EruptField(
            views = @View(title = "所属罐区"),
            edit = @Edit(title = "所属罐区",
                    type = EditType.CHOICE,
                    search = @Search(), notNull = true,
                    choiceType = @ChoiceType(fetchHandler = RemoteCallChoiceFetchHandler.class, fetchHandlerParams = {"main", "erupt-api/get/tankFarm"})))
    @Comment("所属罐区")
    @ApiModelProperty("所属罐区")
    private String tankFarm;

//    @EruptField(
//            views = @View(title = "所属罐区"),
//            edit = @Edit(title = "所属罐区", type = EditType.REFERENCE_TREE,  notNull = true,
//                    referenceTreeType = @ReferenceTreeType(type = ReferenceTreeType.Type.CHOICE, dependField = "company", dependColumn = "company"),
//                    choiceType = @ChoiceType(fetchHandler = SqlChoiceFetchHandler.class, fetchHandlerParams = {"select id as code,tank_farm as name from tb_tank_farm ", "5000", " and 1=1" })))
//    @Comment("所属罐区")
//    @ApiModelProperty("所属罐区")
//    private String tankFarm;

//	@EruptField(
//		views = @View(title = "所属罐组"),
//		edit = @Edit(title = "所属罐组", type = EditType.CHOICE, search = @Search(vague = true), notNull = true,
//				choiceType = @ChoiceType(fetchHandler = RemoteCallChoiceFetchHandler.class, fetchHandlerParams ={"main","erupt-api/get/tankGroup"})))

    @EruptField(
            views = @View(title = "所属罐组"),
            edit = @Edit(title = "所属罐组", type = EditType.REFERENCE_TREE, notNull = true,
                    referenceTreeType = @ReferenceTreeType(type = ReferenceTreeType.Type.CHOICE, dependField = "tankFarm", dependColumn = "tank_farm"),
                    choiceType = @ChoiceType(fetchHandler = SqlChoiceFetchHandler.class,
                            fetchHandlerParams = {"select id,tank_group from tb_tank_group", "5000", "and 1=1"})))
    @Comment("所属罐组")
    @ApiModelProperty("所属罐组")
    private String tankGroup;


//	@EruptField(
//		views = @View(title = "所属港区"),
//		edit = @Edit(title = "所属港区", type = EditType.CHOICE, search = @Search(vague = true), notNull = true,
//		choiceType = @ChoiceType(fetchHandler = RemoteCallChoiceFetchHandler.class, fetchHandlerParams ={"main","erupt-api/get/portareaName"})))
//
//	@Comment("所属港区")
//	@ApiModelProperty("所属港区")
//	private String portArea;

    @EruptField(
            views = @View(title = "所属港区"),
            edit = @Edit(title = "所属港区", type = EditType.CHOICE, notNull = true, readonly = @Readonly,search = @Search,
                    choiceType = @ChoiceType(fetchHandler = SqlChoiceFetchHandler.class, fetchHandlerParams = {"select id,portarea_name from tb_port_area"})))
    @Comment("所属港区")
    @ApiModelProperty("所属港区")
    private String portArea;

    @EruptField(
            views = @View(title = "储罐形状"),
            edit = @Edit(title = "储罐形状", type = EditType.INPUT, notNull = true,
                    inputType = @InputType))
    @Comment("储罐形状")
    @ApiModelProperty("储罐形状")
    private String tankShape;

    @EruptField(
            views = @View(title = "储罐形式"),
            edit = @Edit(title = "储罐形式", type = EditType.INPUT, notNull = true,
                    inputType = @InputType))
    @Comment("储罐形式")
    @ApiModelProperty("储罐形式")
    private String tankType;

    @EruptField(
            views = @View(title = "储罐材质"),
            edit = @Edit(title = "储罐材质", type = EditType.INPUT, notNull = true,
                    inputType = @InputType))
    @Comment("储罐材质")
    @ApiModelProperty("储罐材质")
    private String tankMaterial;

    @EruptField(
            views = @View(title = "现存货物名称"),
            edit = @Edit(title = "现存货物名称", type = EditType.INPUT, notNull = true,
                    inputType = @InputType))
    @Comment("现存货物名称")
    @ApiModelProperty("现存货物名称")
    private String existingGoods;

    @EruptField(
            views = @View(title = "可存货类"),
            edit = @Edit(title = "可存货类", type = EditType.INPUT, notNull = true,
                    inputType = @InputType))
    @Comment("可存货类")
    @ApiModelProperty("可存货类")
    private String inventoryable;

    @EruptField(
            views = @View(title = "容积（m3)"),
            edit = @Edit(title = "容积（m3)", type = EditType.INPUT, notNull = true,
                    inputType = @InputType))
    @Comment("容积（m3)")
    @ApiModelProperty("容积（m3)")
    private String volume;

    @EruptField(
            views = @View(title = "设计压力"),
            edit = @Edit(title = "设计压力", type = EditType.INPUT, notNull = true,
                    inputType = @InputType))
    @Comment("设计压力")
    @ApiModelProperty("设计压力")
    private String designPressure;

    @EruptField(
            views = @View(title = "设计温度"),
            edit = @Edit(title = "设计温度", type = EditType.INPUT, notNull = true,
                    inputType = @InputType))
    @Comment("设计温度")
    @ApiModelProperty("设计温度")
    private String designTemperature;

    @EruptField(
            views = @View(title = "建成年份"),
            edit = @Edit(title = "建成年份",
                    type = EditType.INPUT,
                    notNull = true
            )
    )
    @Comment("建成年份")
    @ApiModelProperty("建成年份")
    private String yearBuilt;

    @EruptField(
            views = @View(title = "设计使用寿命"),
            edit = @Edit(title = "设计使用寿命", type = EditType.INPUT, notNull = true,
                    inputType = @InputType))
    @Comment("设计使用寿命")
    @ApiModelProperty("设计使用寿命")
    private String serviceLife;

    @EruptField(
            views = @View(title = "安全负责人姓名"),
            edit = @Edit(title = "安全负责人姓名", type = EditType.INPUT, notNull = true,
                    inputType = @InputType))
    @Comment("安全负责人姓名")
    @ApiModelProperty("安全负责人姓名")
    private String safetyDirector;

    @EruptField(
            views = @View(title = "联系电话"),
            edit = @Edit(title = "联系电话", type = EditType.INPUT, notNull = true,
                    inputType = @InputType))
    @Comment("联系电话")
    @ApiModelProperty("联系电话")
    private String contactNumber;

//	@EruptField(
//		views = @View(title = "经纬度"),
//		edit = @Edit(title = "经纬度", type = EditType.INPUT,
//		inputType = @InputType))
//	@Comment("经纬度")
//	@ApiModelProperty("经纬度")
//	private String longitudeAndLatitude;
//
//	@EruptField(
//		views = @View(title = "经度", show = false),
//		edit = @Edit(title = "经度", type = EditType.INPUT, show = false,
//		inputType = @InputType))
//	@Comment("经度")
//	@ApiModelProperty("经度")
//	private String longitude;
//
//	@EruptField(
//		views = @View(title = "纬度", show = false),
//		edit = @Edit(title = "纬度", type = EditType.INPUT, show = false,
//		inputType = @InputType))
//	@Comment("纬度")
//	@ApiModelProperty("纬度")
//	private String latitude;

    @EruptField(
            views = @View(title = "地图", show = false),
            edit = @Edit(title = "地图", type = EditType.MAP, show = true,notNull = true))
    @Comment("地图")
    @ApiModelProperty("地图")
    @Lob
    private String map;

    @EruptField(
            views = @View(title = "三方系统位置id", show = false),
            edit = @Edit(title = "三方系统位置id", type = EditType.INPUT, show = false,
                    inputType = @InputType))
    @Comment("三方系统位置id")
    @ApiModelProperty("三方系统位置id")
    private String positionId;

    @EruptField(
            views = @View(title = "excel导入标识", show = false),
            edit = @Edit(title = "excel导入标识", type = EditType.INPUT, show = false,
                    inputType = @InputType))
    @Comment("excel导入标识")
    @ApiModelProperty("excel导入标识")
    private String isImportable;

}
