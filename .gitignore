/target/
/log/
/.idea/
/.erupt/
/.idea/libraries/
/.idea/.gitignore
/.idea/compiler.xml
/.idea/daliangang-workbench.iml
/.idea/encodings.xml
/.idea/jarRepositories.xml
/.idea/misc.xml
/.idea/modules.xml
/.idea/uiDesigner.xml
/.idea/vcs.xml
/.idea/libraries/
/daliangang-workbench.iml

# 安全配置文件 - 包含敏感信息，不应提交到版本控制
**/application-security.yml
**/application-prod.yml
**/application-local.yml

# 环境变量文件
.env
.env.local
.env.production
.env.staging

# Docker相关
docker-compose.override.yml
.dockerignore

# 密钥和证书文件
*.key
*.pem
*.p12
*.jks
*.keystore

# 配置文件中的敏感信息备份
**/application.yml.bak
**/application.properties.bak

# SpecStory explanation file
.specstory/.what-is-this.md
