package com.daliangang.workbench.handler;

import com.daliangang.core.DaliangangContext;
import org.springframework.stereotype.Component;
import xyz.erupt.annotation.expr.ExprBool;


@Component
public class EmployeeExternalRenderHandler implements ExprBool.ExprHandler{

    @Override
    public boolean handler(boolean expr, String[] params) {
        return !DaliangangContext.isDepartmentUser();
    }
}
