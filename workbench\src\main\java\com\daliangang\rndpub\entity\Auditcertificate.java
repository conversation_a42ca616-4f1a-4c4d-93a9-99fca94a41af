/*
* Copyright © 2020-2035 yundingyun All rights reserved.
*/
package com.daliangang.rndpub.entity;

import javax.persistence.*;
import xyz.erupt.annotation.*;
import io.swagger.annotations.*;
import xyz.erupt.annotation.sub_erupt.*;
import xyz.erupt.annotation.sub_field.*;
import xyz.erupt.annotation.sub_field.sub_edit.*;
import xyz.erupt.upms.model.auth.*;
import xyz.erupt.toolkit.handler.*;
import java.util.*;
import java.sql.*;
import xyz.erupt.toolkit.db.Comment;
import xyz.erupt.toolkit.submit.*;
import com.daliangang.rndpub.proxy.*;
import lombok.*;

@Erupt(name = "审核资质证书", power = @Power(add = true, delete = true, export = false, importable = true, viewDetails = true, edit = true)
, dataProxy = AuditcertificateDataProxy.class
, rowOperation = {})
@Table(name = "tb_auditcertificate")
@Entity
@Getter
@Setter
@Comment("审核资质证书")
@ApiModel("审核资质证书")
public class Auditcertificate extends DataAuthModel {
	@EruptField(
		views = @View(title = "证书名称"),
		edit = @Edit(title = "证书名称", type = EditType.INPUT, notNull = true,
		inputType = @InputType))
	@Comment("证书名称")
	@ApiModelProperty("证书名称")
	private String name;

	@EruptField(
		views = @View(title = "附件名称"),
		edit = @Edit(title = "附件名称", type = EditType.ATTACHMENT, notNull = true,
		attachmentType = @AttachmentType(type = AttachmentType.Type.BASE, maxLimit = 20, fileTypes = {".pdf", ".doc", ".docx", ".xlsx", ".png", ".jpg", ".xls", ".ppt", ".pptx", ".zip", ".rar"})))
	@Comment("附件名称")
	@ApiModelProperty("附件名称")
	private String file;

	@EruptField(
		views = @View(title = "专家姓名"),
		edit = @Edit(title = "专家姓名", type = EditType.CHOICE, search = @Search, notNull = true,
		choiceType = @ChoiceType(fetchHandler = SqlChoiceFetchHandler.class, fetchHandlerParams = "select '测试项','测试项'")))
	@Comment("专家姓名")
	@ApiModelProperty("专家姓名")
	private String nameOfExpert;

}
