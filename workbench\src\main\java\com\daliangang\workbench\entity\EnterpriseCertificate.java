/*
 * Copyright © 2020-2035 yundingyun All rights reserved.
 */
package com.daliangang.workbench.entity;

import com.daliangang.workbench.proxy.EnterpriseCertificateDataProxy;
import io.swagger.annotations.ApiModel;
import lombok.Getter;
import lombok.Setter;
import xyz.erupt.annotation.Erupt;
import xyz.erupt.annotation.sub_erupt.Power;
import xyz.erupt.toolkit.colorfont.ColorStateTimeFont;
import xyz.erupt.toolkit.colorfont.ColorStateTimeFontDataProxy;
import xyz.erupt.toolkit.db.Comment;

import javax.persistence.Entity;
import javax.persistence.Table;

@Erupt(name = "企业附证",
        power = @Power(add = true, delete = true, export = true, importable = true, viewDetails = true, edit = true),
        dataProxy = {EnterpriseCertificateDataProxy.class, ColorStateTimeFontDataProxy.class}
        , rowOperation = {})
@Table(name = "tb_enterprise_certificate")
@Entity
@Getter
@Setter
@Comment("企业附证")
@ApiModel("企业附证")
@ColorStateTimeFont(stateKey = "state", timeKey = "expirationTime", interval = 90)
public class EnterpriseCertificate extends EnterpriseCertificateCommon {


}
