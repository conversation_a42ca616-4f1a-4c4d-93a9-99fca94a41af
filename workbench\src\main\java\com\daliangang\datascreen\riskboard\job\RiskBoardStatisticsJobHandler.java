package com.daliangang.datascreen.riskboard.job;

import com.daliangang.datascreen.riskboard.entity.RiskBoardNum;
import com.daliangang.datascreen.riskboard.service.RiskCalculateService;
import com.daliangang.workbench.entity.Enterprise;
import lombok.extern.slf4j.Slf4j;
import org.json.JSONObject;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import xyz.erupt.job.handler.EruptJobHandler;
import xyz.erupt.jpa.dao.EruptDao;
import xyz.erupt.upms.model.EruptUser;
import xyz.erupt.upms.model.template.EruptRoleTemplateUser;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.locks.ReentrantLock;
import java.util.stream.Collectors;

import static com.daliangang.datascreen.utils.OrgUtils.getCompanyName;
import static com.daliangang.datascreen.utils.OrgUtils.getStartUpOrgCode;

/**
 * @Author: chongmenglin
 * @Date: 2024/11/12 18:12
 */
@Service
@Slf4j
public class RiskBoardStatisticsJobHandler implements EruptJobHandler {
    @Resource
    private EruptDao eruptDao;
    @Resource
    private RiskCalculateService riskCalculateService;

    private final ReentrantLock lock = new ReentrantLock();

    @Transactional
    @Override
    public String exec(String code, String param) {
        if (lock.tryLock()) { // 尝试获取锁
            log.info("开始更新风险类型");
            try {
                List<EruptRoleTemplateUser> users = eruptDao.queryEntityList(EruptRoleTemplateUser.class, "template_id not in ('3','4')");
                log.info("开始计算风险类型");
                for (EruptRoleTemplateUser user : users) {
                    EruptUser eruptUser = eruptDao.queryEntity(EruptUser.class, " account = '" + user.getAccount() + "'");
                    this.getRiskTypeAccount(eruptUser);
                }
                log.info("风险统计更新完成");
                return null;
            } finally {
                lock.unlock(); // 执行完成后释放锁
            }
        } else {
            System.out.println("上一次任务尚未执行完毕，跳过本次执行");
            return null;
        }
    }
    /**
     * 获取风险
     *
     * @return
     */
    @Transactional
    public void getRiskTypeAccount(EruptUser user) {
        List<Enterprise> enterprises = getStartUpOrgCode(user);
        List<String> orgCodes = enterprises.stream()
                .map(Enterprise::getOrgCode)
                .collect(Collectors.toList());
        Map<String, String> riskTypeMap = new LinkedHashMap<>();
        Set<String> highRiskOrgCodes = new HashSet<>();
        Set<String> lowRiskOrgCodes = new HashSet<>();
        Set<String> middleRiskOrgCodes = new HashSet<>();
        int total = orgCodes.size();

        // 获取风险评分，同时填充高风险的 riskTypeFormList
        Map<String, Integer> riskScore = riskCalculateService.getRiskScore(highRiskOrgCodes, orgCodes, riskTypeMap, enterprises);
        // 按照分数从高到低排序 riskScore
        List<Map.Entry<String, Integer>> sortedRiskScoreList = riskScore.entrySet()
                .stream()
                .sorted(Map.Entry.<String, Integer>comparingByValue().reversed()) // 从高到低排序，如果需要从低到高去掉 .reversed()
                .collect(Collectors.toList());
        // 根据扣分结果计算风险类型（排除已经识别为高风险的 orgCode）
        for (Map.Entry<String, Integer> entry : sortedRiskScoreList) {
            String orgCode = entry.getKey();
            String companyName = getCompanyName(orgCode, enterprises);

            int score = 100 - entry.getValue();
            if (score >= 80) {
                lowRiskOrgCodes.add(orgCode);
                riskTypeMap.put(companyName, "低风险");
            } else if (score >= 60) {
                middleRiskOrgCodes.add(orgCode);
                riskTypeMap.put(companyName, "中风险");
            }else {
                highRiskOrgCodes.add(orgCode);
                riskTypeMap.put(companyName, "高风险");
            }
        }
        // 使用 org.json.JSONObject 将 Map 转换为 JSON 字符串
        JSONObject jsonObject = new JSONObject(riskTypeMap);
        String riskTypeJson = jsonObject.toString();
        RiskBoardNum riskBoardNum = eruptDao.queryEntity(RiskBoardNum.class, " account = '" + user.getAccount() + "'");
        riskBoardNum = riskBoardNum == null ? new RiskBoardNum() : riskBoardNum;
        riskBoardNum.setHighRisk(highRiskOrgCodes.size());
        riskBoardNum.setMiddleRisk(middleRiskOrgCodes.size());
        riskBoardNum.setLowRisk(lowRiskOrgCodes.size());
        riskBoardNum.setTotal(total);
        riskBoardNum.setAccount(user.getAccount());
        riskBoardNum.setName(user.getName());
        riskBoardNum.setRiskForm(riskTypeJson);
        eruptDao.merge(riskBoardNum);

    }

}
