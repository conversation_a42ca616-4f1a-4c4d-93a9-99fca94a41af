package com.daliangang.statistics.handler;

import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.stereotype.Component;
import xyz.erupt.bi.fun.EruptBiHandler;
import xyz.erupt.core.util.EruptSpringUtil;
import xyz.erupt.upms.model.EruptOrg;
import xyz.erupt.upms.service.EruptUserService;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * @Author: zengwensi
 * @Description:
 * @Date: Created in 2023/5/8 9:57
 * @Modified By
 */
@Component
public class CompanyHandler implements EruptBiHandler {

    @Resource
    private CommonHandler commonHandler;

    @Override
    public String exprHandler(String param, Map<String, Object> condition, String expr) {
        expr= commonHandler.exprHandlerSearchDate(param, condition, expr);
        EruptOrg eruptOrg = EruptSpringUtil.getBean(EruptUserService.class).getCurrentEruptUser().getEruptOrg();
        if(null != eruptOrg){
            String orgCode = eruptOrg.getCode();
            expr = expr.replaceAll("#ORGCODE",orgCode) ;
            return (expr.replaceAll("#REPLACESQL","and company = '"+orgCode+"'"));
        }

        return expr ;

    }

    @Override
    public void resultHandler(String param, Map<String, Object> condition, List<Map<String, Object>> result) {

    }

    @Override
    public void exportHandler(String param, Map<String, Object> condition, Workbook workbook) {

    }
}
