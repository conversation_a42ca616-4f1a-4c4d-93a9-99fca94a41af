package com.daliangang.workbench.sql;

import org.springframework.stereotype.Repository;

/**
 * <AUTHOR>
 * @since :2023/4/23:9:57
 */
@Repository
public class EmployeeInformationSql {

    // 统计员工证书
    public String selectEmployeeCertificate (String orgCode) {

        String sql = "SELECT COUNT(tec.id) as num, tec.certificate_type as type from tb_employee_information tei  left join tb_employee_certificate tec on tei .id = tec.employee_information_id where   CURRENT_DATE() <= tec.validity_period";

        sql += " and tei.org_code "+orgCode+"";

           sql+= "GROUP by tec.certificate_type";
        return sql;
    }

    // 统计员工人数
    public String selectEmployeeInformationNum (String orgCode) {

        String sql = "select COUNT(*) as num  from tb_employee_information where";

        sql += "  org_code = '"+orgCode+"' and owner_role not like '%执法检查人员%'";
//        sql += "  org_code = '"+orgCode+"'";

        return sql;
    }

    // 统计检查员工人数
    public String selectJcEmployeInformationNum (String orgCode) {

        String sql = "select COUNT(*) as num  from tb_employee_information where  check_person = 1";

        sql += " and org_code = '"+orgCode+"'";

        return sql;
    }

    // 统计员工人数
    public String selectQyEmployeeInformationNum (String jobTitle,String orgCode) {

        String sql = "select COUNT(*) as num   from tb_employee_information where ";

        sql += "FIND_IN_SET('"+jobTitle+"',replace(job_title, '|', ','))";

        sql += " and org_code = '"+orgCode+"'";

        return sql;
    }

     // 第二大屏证书
    public String selectEmployeeInformationNumTwo (String orgCode) {

        String sql = "SELECT COUNT(tec.id) as num, '总数' as type from tb_employee_information tei  left join tb_employee_certificate tec on tei .id = tec.employee_information_id where";

        sql += " tei.org_code = '"+orgCode+"'";

        sql += "UNION all SELECT COUNT(tec.id) as num, '过期' as type from tb_employee_information tei  left join tb_employee_certificate tec on tei .id = tec.employee_information_id where   CURRENT_DATE() > tec.validity_period";

        sql += " and tei.org_code = '"+orgCode+"'";
        return sql;
    }

}
