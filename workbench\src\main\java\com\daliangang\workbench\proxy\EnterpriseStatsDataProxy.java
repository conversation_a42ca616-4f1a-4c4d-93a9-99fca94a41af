package com.daliangang.workbench.proxy;

import com.daliangang.device.form.NumForms;
import com.daliangang.emergency.entity.EmergencyDuty;
import com.daliangang.workbench.entity.EnterpriseStats;
import com.daliangang.workbench.form.EnterpriseStatsForm;
import com.daliangang.workbench.sql.EnterpriseCertificateSql;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.stereotype.Service;
import xyz.erupt.annotation.fun.DataProxy;
import xyz.erupt.annotation.query.Condition;
import xyz.erupt.core.module.MetaUserinfo;
import xyz.erupt.core.util.EruptSpringUtil;
import xyz.erupt.jpa.dao.EruptDao;
import xyz.erupt.toolkit.remote.RemoteProxyService;
import xyz.erupt.toolkit.utils.EruptDaoUtils;
import xyz.erupt.toolkit.utils.SqlUtils;
import xyz.erupt.upms.model.template.EruptRoleTemplateUser;
import xyz.erupt.upms.model.template.EruptRoleTemplateUserService;

import javax.annotation.Resource;
import javax.transaction.Transactional;
import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @since :2023/5/20:13:53
 */
@Service
public class EnterpriseStatsDataProxy implements DataProxy<EnterpriseStats> {

@Resource
    EruptDao eruptDao;
@Resource
    RemoteProxyService remoteProxyService;
    @Resource
    EnterpriseCertificateSql enterpriseCertificateSql;
    @Override
    @Transactional
    public String beforeFetch(List<Condition> conditions) {
//        EnterpriseStats enterpriseStats = EruptSpringUtil.getBean(EnterpriseStats.class);
//        MetaUserinfo remoteUserInfo = remoteProxyService.getRemoteUserInfo();
//        List<String> orgCodeLists = remoteUserInfo.getAuth();
//        orgCodeLists.forEach(v->{
//            String sql = enterpriseCertificateSql.selectNumThree(v);
//            List<EnterpriseStatsForm> enterpriseStatsForms = EruptDaoUtils.selectOnes(sql, EnterpriseStatsForm.class);
//            if (ObjectUtils.isNotEmpty(enterpriseStatsForms)) {
//                enterpriseStatsForms.forEach(m->{
//                    switch (m.getType()) {
//                        case "泊位数量":
//                            enterpriseStats.setBerthNum(m.getNum());
//
//                        case "罐区数量":
//                            enterpriseStats.setTankFarmNum(m.getNum());
//
//                        case "罐组数量":
//                            enterpriseStats.setTankGroupNum(m.getNum());
//
//                        case "储罐数量":
//                            enterpriseStats.setStorageTankNum(m.getNum());
//                            enterpriseStats.setStorageTankVolume(m.getVolume());
//
//                        case "港经证有效期":
//                            enterpriseStats.setHongKongCertificateValidity(m.getHongKongCertificateValidity());
//
//                        case "水路运输从业资格证":
//                            enterpriseStats.setWaterTransport(m.getZsnum());
//
//                        case "特种设备操作证书":
//                            enterpriseStats.setSpecialEquipment(m.getZsnum());
//
//                        case "特种作业操作证书":
//                            enterpriseStats.setSpecialOperation(m.getZsnum());
//
//                        case "其他证书":
//                            enterpriseStats.setOtherCertificates(m.getZsnum());
//                        case "重大风险源备案":
//                            enterpriseStats.setRiskSourceFilingNum(m.getZdnum());
//
//                    }
//                });
//                enterpriseStats.setOrgCode(v);
//                eruptDao.merge(enterpriseStats);
//              //  enterpriseStatsList.add(enterpriseStats);
//            }
//
//            //查询附征有效期
//            // 预案备案
//            // 标准化达标等级
//            // 安全评价备案
//            // 港口设施保安
//
//        });
        return null;
    }

    @Override
    public void afterFetch(Collection<Map<String, Object>> list) {

    }
}
