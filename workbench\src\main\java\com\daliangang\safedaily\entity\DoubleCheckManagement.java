package com.daliangang.safedaily.entity;

import com.daliangang.core.CompanyRenderHandler;
import com.daliangang.safedaily.operation.ButtonShowHandler;
import com.daliangang.safedaily.operation.DepartPowerHandler;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import xyz.erupt.annotation.Erupt;
import xyz.erupt.annotation.EruptField;
import xyz.erupt.annotation.expr.ExprBool;
import xyz.erupt.annotation.fun.DataProxy;
import xyz.erupt.annotation.query.Condition;
import xyz.erupt.annotation.sub_erupt.Power;
import xyz.erupt.annotation.sub_erupt.RowOperation;
import xyz.erupt.annotation.sub_field.Edit;
import xyz.erupt.annotation.sub_field.EditType;
import xyz.erupt.annotation.sub_field.Readonly;
import xyz.erupt.annotation.sub_field.View;
import xyz.erupt.annotation.sub_field.sub_edit.*;
import xyz.erupt.toolkit.db.Comment;
import xyz.erupt.toolkit.handler.DelOperationHandler;
import xyz.erupt.toolkit.handler.EditOperationHandler;
import xyz.erupt.toolkit.handler.EruptDictCodeChoiceFetchHandler;
import xyz.erupt.toolkit.handler.RemoteEntityChoiceFetchHandler;
import xyz.erupt.toolkit.submit.CheckSubmit;
import xyz.erupt.toolkit.utils.TplUtils;
import xyz.erupt.upms.model.auth.DataAuthHandler;
import xyz.erupt.upms.model.auth.DataAuthModel;

import javax.persistence.Entity;
import javax.persistence.Table;
import java.util.Date;
import java.util.List;

/**
 * @Title: doubleCheckManagement
 * <AUTHOR>
 * @Package com.daliangang.safedaily.entity
 * @Date 2024/3/7 13:34
 * @description: 双倒查管理
 */
@Erupt(name = "双倒查管理",
        power = @Power(powerHandler = DepartPowerHandler.class),
        dataProxy = DoubleCheckManagement.class,
        orderBy = "DoubleCheckManagement.updateTime desc",
        rowOperation = {
//                @RowOperation(
//                        title = "上报",
//                        icon = "fa fa-arrow-circle-o-up",
//                        operationHandler = DoubleCheckEscalationHandler.class,
//                        ifExpr = "item.submitted=='未上报'",
//                        mode = RowOperation.Mode.SINGLE,
//                        show = @ExprBool(exprHandler = ButtonShowHandler.class)
//                ),
                @RowOperation(
                        title = "编辑",
                        icon = "fa fa-edit",
                        code = TplUtils.EDIT_OPER_CODE,
                        eruptClass = DoubleCheckManagement.class,
                        operationHandler = EditOperationHandler.class,
//                        ifExpr = "item.submitted=='未上报'",
                        ifExprBehavior = RowOperation.IfExprBehavior.HIDE,
                        mode = RowOperation.Mode.SINGLE,
                        show = @ExprBool(exprHandler = ButtonShowHandler.class)
                ),
                @RowOperation(
                        title = "删除",
                        icon = "fa fa-trash-o",
                        operationHandler = DelOperationHandler.class,
//                        ifExpr = "item.submitted=='未上报'",
                        ifExprBehavior = RowOperation.IfExprBehavior.HIDE,
                        mode = RowOperation.Mode.SINGLE,
                        show = @ExprBool(exprHandler = ButtonShowHandler.class)
                )
        })
@Table(name = "tb_double_check_management")
@Entity
@Getter
@Setter
@Comment("双倒查管理")
public class DoubleCheckManagement extends DataAuthModel implements DataProxy <DoubleCheckManagement> {

    @EruptField(
            views = @View(title = "单位名称",width = "260px"),
            edit = @Edit(title = "单位名称",search = @Search(ifRender = @ExprBool(exprHandler = CompanyRenderHandler.class)), readonly = @Readonly(exprHandler = DataAuthHandler.class), type = EditType.CHOICE, notNull = true,
                    choiceType = @ChoiceType(fullSpan = true,fetchHandler = RemoteEntityChoiceFetchHandler.class, fetchHandlerParams = {"main", "Enterprise", "org_code,name"}, reload = true)))
    @Comment("单位名称")
    @ApiModelProperty("单位名称")
    private String company;

    @EruptField(
            views = @View(title = "检查类型",width = "100px"),
            edit = @Edit(title = "检查类型", type = EditType.CHOICE, search = @Search, notNull = true,
                    choiceType = @ChoiceType(fetchHandler = EruptDictCodeChoiceFetchHandler.class, fetchHandlerParams = "inspectType")))
    @Comment("检查类型")
    @ApiModelProperty("检查类型")
    private String inspectType;

    @EruptField(
            views = @View(title = "排查问题",width = "150px"),
            edit = @Edit(title = "排查问题", type = EditType.TEXTAREA, notNull = true))
    @Comment("排查问题")
    @ApiModelProperty("排查问题")
    private String troubleShootIssues;

    @EruptField(
            views = @View(title = "排查责任人",width = "150px"),
            edit = @Edit(title = "排查责任人", type = EditType.INPUT, notNull = true,
                    inputType = @InputType))
    @Comment("排查责任人")
    @ApiModelProperty("排查责任人")
    private String personResponsible;

    @EruptField(
            views = @View(title = "考核举措",width = "150px"),
            edit = @Edit(title = "考核举措", type = EditType.TEXTAREA, notNull = true))
    @Comment("考核举措")
    @ApiModelProperty("考核举措")
    private String assessmentInitiatives;

    @EruptField(
            views = @View(title = "上报时间",width = "100px",show = false),
            edit = @Edit(title = "上报时间", type = EditType.DATE, search = @Search, show = false,
                    dateType = @DateType))
    @Comment("上报时间")
    @ApiModelProperty("上报时间")
    private java.util.Date fillingDate;

    @EruptField(
            views = @View(title = "上报状态",width = "100px",show = false),
            edit = @Edit(title = "上报状态", type = EditType.BOOLEAN, show = false,
                    boolType = @BoolType(trueText = "已上报", falseText = "未上报")))
    @CheckSubmit(linkProperty = "createTime", checkTimeDiffPeriod = 1)
    @Comment("上报状态")
    @ApiModelProperty("上报状态")
    private Boolean submitted;

    @Override
    public void addBehavior(DoubleCheckManagement doubleCheckManagement) {
        doubleCheckManagement.setFillingDate(new Date());
//        doubleCheckManagement.setSubmitted(Boolean.FALSE);
        doubleCheckManagement.setSubmitted(Boolean.TRUE);
    }

    @Override
    public String beforeFetch(List<Condition> conditions) {
//        if (DaliangangContext.isDepartmentUser()) return "submitted=true";
        return DataProxy.super.beforeFetch(conditions);
    }

}
