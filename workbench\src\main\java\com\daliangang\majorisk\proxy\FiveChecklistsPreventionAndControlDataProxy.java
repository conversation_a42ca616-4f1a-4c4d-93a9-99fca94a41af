package com.daliangang.majorisk.proxy;

import com.daliangang.majorisk.entity.FiveChecklistsFoundation;
import com.daliangang.majorisk.entity.FiveChecklistsPreventionAndControl;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.stereotype.Service;
import xyz.erupt.annotation.fun.DataProxy;
import xyz.erupt.toolkit.utils.NotifyUtils;

/**
 * <AUTHOR>
 * @since :2023/4/4:11:14
 */
@Service
public class FiveChecklistsPreventionAndControlDataProxy implements DataProxy<FiveChecklistsPreventionAndControl> {

    @Override
    public void beforeAdd(FiveChecklistsPreventionAndControl fiveChecklistsPreventionAndControl) {
        if (ObjectUtils.isNotEmpty(fiveChecklistsPreventionAndControl.getRiskControlSystem())) {
            NotifyUtils.showErrorMsg("请填写完整！");
        }
    }
}
