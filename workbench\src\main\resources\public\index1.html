<!--
 * @Author: your name
 * @Date: 2024-10-29 09:45:48
 * @LastEditTime: 2024-10-29 09:51:47
 * @LastEditors: WIN-K9E4GHCA175
 * @Description: In User Settings Edit
 * @FilePath: \erupt-webc:\Users\<USER>\Downloads\font\font\index copy.html
-->
<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8"/>
    <title></title>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
    <meta
            name="viewport"
            content="width=device-width, initial-scale=1, maximum-scale=1"
    />
    <meta http-equiv="X-UA-Compatible" content="IE=Edge"/>
</head>
<body>
<div id="root" >欢迎登录</div>
<script type="text/javascript">
		// 获取URL中的参数
		function getQueryParam(param){
			const urlParams = new URLSearchParams(window.location.search);
			return urlParams.get(param);
		}

		// 将token存储到localStorage
		function storeTokenInLocalStorage(token,account) {
			localStorage.setItem('_token', JSON.stringify({"token":token,"account":account}));
			window.location.href=window.location.origin
		}

		// 主执行逻辑
		const token = getQueryParam('token');
		const account = getQueryParam('account');
		if (token) {
			storeTokenInLocalStorage(token,account);
		} else {
			alert('系统初始化已完成，请关闭此页面后重新进入');
		}
</script>
</body>
</html>
