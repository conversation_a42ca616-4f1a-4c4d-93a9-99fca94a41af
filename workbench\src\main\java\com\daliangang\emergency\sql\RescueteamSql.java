package com.daliangang.emergency.sql;

import org.springframework.stereotype.Repository;

/**
 * <AUTHOR>
 * @since :2023/4/24:15:14
 */
@Repository
public class RescueteamSql {

    //
    public String selectRescueteamNum (String orgCode) {

        String sql = "select COUNT(*) as num  ,'dw' as type from tb_rescueteam tr where ";
        sql += "  org_code "+orgCode+"";

        sql += "  UNION all select COUNT(*) as num ,'wz' as type from tb_material_reserve tmr where";
        sql += "  org_code "+orgCode+"";

        sql += "  UNION all select COUNT(*) as num ,'jhd' as type from tb_emergency_muster tem where";
        sql += "  org_code "+orgCode+"";

        return sql;
    }
}
