package com.daliangang.safedaily.sql;

import org.springframework.stereotype.Repository;

/**
 * <AUTHOR>
 * @since :2023/4/23:13:46
 */
@Repository
public class ReportSql {
    //
    public String selectReportNum (String orgCode) {

        String sql = "select  COUNT(*) as num  from tb_report where submitted = 1  and ";
        sql += " org_code "+orgCode+" and modify_time=date_format(now(), '%Y-%m')";
        return sql;
    }
}
