package com.daliangang.statistics.handler;

import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.stereotype.Component;
import xyz.erupt.bi.fun.EruptBiHandler;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * 从业人员一体化-考生统计
 * @Author: zengwensi
 * @Description:
 * @Date: Created in 2023/6/1 9:37
 * @Modified By
 */
@Component
public class ExamineeHandler  implements EruptBiHandler {

    @Resource
    private CommonHandler commonHandler;

    @Override
    public String exprHandler(String param, Map<String, Object> condition, String expr) {
        //权限
        String s = commonHandler.exprHandlerAuthoritySql(param, condition, expr);
        if(StringUtils.isNotEmpty(s)){
            String replaceSql =  " and org_code "+ s;
            expr = expr.replaceAll("#REPLACESQL",replaceSql) ;
        }
        String searchSql = "";

        //企业名称查询条件
        if(condition.containsKey("companyName")){
            String companyName = (String)condition.get("companyName");
            if (StringUtils.isNotEmpty( companyName)) {
                searchSql += " and organ.`name` like '%"+companyName+"%'";
            }

        }
        //考生名称查询条件
        if(condition.containsKey("name")){
            String name = (String)condition.get("name");
            if (StringUtils.isNotEmpty( name)) {
                searchSql += " and  `user`.`real_name` like '%"+name+"%'";
            }

        }
        expr = expr.replaceAll("#SEARCHSQL",searchSql) ;

        return expr;
    }

    @Override
    public void resultHandler(String param, Map<String, Object> condition, List<Map<String, Object>> result) {

    }

    @Override
    public void exportHandler(String param, Map<String, Object> condition, Workbook workbook) {

    }
}
