/*
 * Copyright © 2020-2035 yundingyun All rights reserved.
 */
package com.daliangang.emergency.entity;

import com.daliangang.emergency.proxy.GradeDataProxy;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import xyz.erupt.annotation.Erupt;
import xyz.erupt.annotation.EruptField;
import xyz.erupt.annotation.sub_erupt.Power;
import xyz.erupt.annotation.sub_field.Edit;
import xyz.erupt.annotation.sub_field.EditType;
import xyz.erupt.annotation.sub_field.View;
import xyz.erupt.annotation.sub_field.sub_edit.NumberType;
import xyz.erupt.jpa.model.MetaModel;
import xyz.erupt.toolkit.db.Comment;

import javax.persistence.Entity;
import javax.persistence.Table;

@Erupt(name = "评估等级管理", power = @Power(add = true, delete = true, export = false, importable = true, viewDetails = true, edit = true), dataProxy = GradeDataProxy.class, rowOperation = {})
@Table(name = "tb_evaluation_grade")
@Entity
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@Comment("评估等级管理")
@ApiModel("评估等级管理")
public class EvaluationGrade extends MetaModel {

    @EruptField(views = @View(title = "等级"), edit = @Edit(title = "等级", type = EditType.INPUT))
    @Comment("等级")
    @ApiModelProperty("等级")
    private String name;

    @EruptField(views = @View(title = "最低分数线"), edit = @Edit(title = "最低分数线", type = EditType.NUMBER, notNull = true, numberType = @NumberType))
    @Comment("最低分数线")
    @ApiModelProperty("最低分数线")
    private Integer minScore;

    @EruptField(views = @View(title = "最高分数线"), edit = @Edit(title = "最高分数线", type = EditType.NUMBER, notNull = true, numberType = @NumberType))
    @Comment("最高分数线")
    @ApiModelProperty("最高分数线")
    private Integer maxScore;

    public boolean eval(Double score) {
        return score.intValue() >= minScore && score.intValue() <= maxScore;
    }

//	@EruptField(
//		views = @View(title = "合格最低分数线"),
//		edit = @Edit(title = "合格最低分数线", type = EditType.NUMBER, notNull = true,
//		numberType = @NumberType))
//	@Comment("合格最低分数线")
//	@ApiModelProperty("合格最低分数线")
//	private Integer qualifiedMinScore;
//
//	@EruptField(
//		views = @View(title = "合格最高分数线"),
//		edit = @Edit(title = "合格最高分数线", type = EditType.NUMBER, notNull = true,
//		numberType = @NumberType))
//	@Comment("合格最高分数线")
//	@ApiModelProperty("合格最高分数线")
//	private Integer qualifiedMaxScore;
//
//	@EruptField(
//		views = @View(title = "不合格最低分数线"),
//		edit = @Edit(title = "不合格最低分数线", type = EditType.NUMBER, notNull = true,
//		numberType = @NumberType))
//	@Comment("不合格最低分数线")
//	@ApiModelProperty("不合格最低分数线")
//	private Integer unQqualifiedMinScore;
//
//	@EruptField(
//		views = @View(title = "不合格最高分数线"),
//		edit = @Edit(title = "不合格最高分数线", type = EditType.NUMBER, notNull = true,
//		numberType = @NumberType))
//	@Comment("不合格最高分数线")
//	@ApiModelProperty("不合格最高分数线")
//	private Integer unQqualifiedMaxScore;

}
