/*
* Copyright © 2020-2035 yundingyun All rights reserved.
*/
package com.daliangang.safedaily.proxy;

import com.daliangang.core.DaliangangContext;
import com.daliangang.safedaily.entity.WeeklyReport;
import org.springframework.stereotype.Service;
import xyz.erupt.annotation.fun.DataProxy;
import xyz.erupt.annotation.query.Condition;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.temporal.WeekFields;
import java.util.*;
import java.util.concurrent.atomic.AtomicReference;


@Service
public class WeeklyReportDataProxy implements DataProxy<WeeklyReport> {

    @Override
    public void addBehavior(WeeklyReport weeklyReport) {
        weeklyReport.setInspectionTime(new Date(System.currentTimeMillis()));
        //weeklyReport.setReportMonth(DateUtil.getFormatDate(new java.util.Date(), DateUtil.YEAR_MONTH) );
    }

    @Override
    public String beforeFetch(List<Condition> conditions) {

        AtomicReference<String> returnStr = new AtomicReference<>(" 1 = 1 ") ;
        if (DaliangangContext.isDepartmentUser()) {
            returnStr.set(  returnStr.get() + " and submitted=true ");
        }

        //检查时间查询条件
        Optional<Condition> inspectionTimeWeek = conditions.stream().filter(condition ->
                "inspectionTimeWeek".equals(condition.getKey())
        ).findFirst();
        inspectionTimeWeek.ifPresent(f -> {
            List list = (List) f.getValue();
            String sql = " and inspection_time between '" +list.get(0) + "' and '"+list.get(1)+"' ";
            returnStr.set(  returnStr.get() + sql);
            conditions.remove(f);
        });
        return returnStr.get();
    }

    @Override
    public void searchCondition(Map<String, Object> condition) {
//        String yearWeek = (String)f.getValue();
//        String[] yearWeekSplit = yearWeek.split("-");
//        Integer year = Integer.valueOf(yearWeekSplit[0]);
//        Integer week = Integer.valueOf(yearWeekSplit[1]);
        //根据周获取周开始时间和结束时间
        WeekFields weekFields= WeekFields.ISO;
        LocalDate now = LocalDate.now();
//        LocalDate localDate = now.withYear(year).with(weekFields.weekOfYear(),week);
        //周一
        LocalDate localDate1 = now.with(weekFields.dayOfWeek(), 1L);
        //周日（第七天）
        LocalDate localDate7 = now.with(weekFields.dayOfWeek(), 7L);

        Date startDay = null;
        Date endDay = null ;
        try {
            startDay = new SimpleDateFormat("yyyy-MM-dd").parse(localDate1.toString());
            endDay = new SimpleDateFormat("yyyy-MM-dd").parse(localDate7.toString());
        } catch (ParseException e) {
            throw new RuntimeException(e);
        }
        condition.put("inspectionTimeWeek", new Date[]{
                startDay,endDay
        });
    }
}
