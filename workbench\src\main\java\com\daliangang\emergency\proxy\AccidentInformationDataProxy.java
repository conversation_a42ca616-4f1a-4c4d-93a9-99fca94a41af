package com.daliangang.emergency.proxy;


import com.daliangang.device.entity.PortArea;
import com.daliangang.emergency.entity.AccidentInformation;
import com.daliangang.emergency.form.MapForm;
import com.google.gson.Gson;
import com.google.gson.internal.LinkedTreeMap;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.stereotype.Service;
import xyz.erupt.annotation.fun.DataProxy;
import xyz.erupt.core.config.GsonFactory;
import xyz.erupt.toolkit.remote.RemoteProxyService;
import xyz.erupt.toolkit.remote.RemoteQuery;
import xyz.erupt.toolkit.utils.EruptDaoUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @since :2023/4/26:10:58
 */
@Service
public class AccidentInformationDataProxy implements DataProxy<AccidentInformation> {

    @Resource
    private RemoteProxyService remoteProxyService;

    @Override
    public void addBehavior(AccidentInformation accidentInformation) {
        if (ObjectUtils.isNotEmpty(accidentInformation.getCompany())) {
            List<LinkedTreeMap> enterprise = remoteProxyService.queryEntity("Enterprise", RemoteQuery.builder().condition("orgCode", accidentInformation.getCompany()));
            if (ObjectUtils.isNotEmpty(enterprise)) {
                //  Integer portArea1 = (int) Float.parseFloat((String) enterprise.get(0).get("portArea"));
                //   String portArea = String.valueOf(enterprise.get(0).get("portArea"));
                String portArea=new BigDecimal(enterprise.get(0).get("portArea").toString()).stripTrailingZeros().toPlainString();
                accidentInformation.setPortArea(portArea);
            }
        }
    }

    @Override
    public void beforeAdd(AccidentInformation accidentInformation) {
//        PortArea portArea = EruptDaoUtils.selectOne("selcet * from tb_port_area where id = " + accidentInformation.getPortArea(), PortArea.class);
//        accidentInformation.setPortAreaName(portArea.getPortareaName());
    }

    @Override
    public void afterFetch(Collection<Map<String, Object>> list) {
        if (ObjectUtils.isNotEmpty(list)) {
            list.forEach(v->{
                if (ObjectUtils.isNotEmpty(v.get("accidentType"))) {
                    String accidentType = String.valueOf(v.get("accidentType")).replace(",","|");
                    v.put("accidentType",accidentType);
                }
                if (ObjectUtils.isNotEmpty(v.get("goodsName"))) {
                    String goodsName = String.valueOf(v.get("goodsName")).replace(",","|");
                    v.put("goodsName",goodsName);
                }

            });
        }
    }
}
