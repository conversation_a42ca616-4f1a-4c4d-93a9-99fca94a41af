package com.daliangang.workbench.proxy;

import com.daliangang.workbench.entity.CompanyProblemList;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import xyz.erupt.annotation.fun.DataProxy;
import xyz.erupt.jpa.dao.EruptDao;
import xyz.erupt.toolkit.utils.EruptDaoUtils;

import javax.annotation.Resource;
import javax.transaction.Transactional;
import java.util.List;
import java.util.Map;

@Service
@Slf4j

public class CompanyProblemListProxy implements DataProxy<CompanyProblemList> {

    @Resource
    private EruptDao eruptDao ;

    @Transactional
    @Override
    public void searchCondition(Map<String, Object> condition) {
        String sql =
      " SELECT "+
        " tb_enterprise.id companyId,"+
        " tb_enterprise.org_code companyCode,"+
        " tb_enterprise.NAME companyName,"+
        " IF( check_fill.company IS NULL, '未上报', '已上报' ) checkFillSubmited,"+
        " IF( tb_release.company IS NULL, '未上报', '已上报' ) releaseSubmited,"+
        " IF( tb_report.company IS NULL, '未上报', '已上报' ) reportSubmited,"+
        " IF( tb_daily_inspection.company IS NULL, '未上报', '已上报' ) dailyInspectionSubmited,"+
        " IF( tb_weekly_report.company IS NULL, '未上报', '已上报' ) weeklyReportSubmited,"+
        " IF( tb_monthly_scheduling.company IS NULL, '未上报', '已上报' ) monthlySchedulingSubmited,"+
        " ifnull( tb_wharf_structure_inspection.count, 0 ) wharfStructureInspectionCount,"+
        " ifnull( tb_facility_inspection.count, 0 ) facilityInspectionCount,"+
        " ifnull( tb_lightning_protection.count, 0 ) lightningProtectionCount,"+
        " ifnull( tb_pressure_vessel.count, 0 ) pressureVesselCount,"+
        " ifnull( tb_tank_inspection.count, 0 ) tankInspectionCount,"+
        " ifnull( tb_penstock.count, 0 ) penstockCount,"+
        " ifnull( tb_hose.count, 0 ) hoseCount,"+
        " ifnull( tb_pressure_gauge.count, 0 ) pressureGaugeCount,"+
        " ifnull( tb_safety_valve.count, 0 ) safetyValveCount,"+
        " ifnull( tb_combustible_gas.count, 0 ) combustibleGasCount,"+
        " ifnull( tb_hoisting_machinery.count, 0 ) hoistingMachineryCount,"+
        " ifnull( tb_other_tests.count, 0 ) otherTestsCount,"+
        " '已过期' checkStatus "+
        " FROM"+
        " tb_enterprise"+
        " LEFT JOIN ( SELECT DISTINCT company FROM tb_check_fill WHERE YEAR = DATE_FORMAT( now(), '%Y' ) AND submitted = 1 ) check_fill ON tb_enterprise.org_code = check_fill.company"+
        " LEFT JOIN ( SELECT DISTINCT company FROM tb_release WHERE filling_date = DATE_FORMAT( now(), '%Y-%m-%d' ) AND submitted = 1 ) tb_release ON tb_enterprise.org_code = tb_release.company"+
        " LEFT JOIN ( SELECT DISTINCT company FROM tb_report WHERE modify_time = DATE_FORMAT( now(), '%Y-%m' ) AND is_report = 1 ) tb_report ON tb_enterprise.org_code = tb_report.company"+
        " LEFT JOIN ( SELECT DISTINCT company FROM tb_daily_inspection WHERE inspection_time = DATE_FORMAT( now(), '%Y-%m-%d' ) AND submitted = 1 ) tb_daily_inspection ON tb_enterprise.org_code = tb_daily_inspection.company"+
        " LEFT JOIN ( SELECT DISTINCT company FROM tb_weekly_report WHERE WEEKOFYEAR( inspection_time ) = WEEKOFYEAR( now()) AND submitted = 1 ) tb_weekly_report ON tb_enterprise.org_code = tb_weekly_report.company"+
        " LEFT JOIN ( SELECT DISTINCT company FROM tb_monthly_scheduling WHERE report_month = DATE_FORMAT( now(), '%Y-%m' ) AND submitted = 1 ) tb_monthly_scheduling ON tb_enterprise.org_code = tb_monthly_scheduling.company"+
        " LEFT JOIN ( SELECT company, count( company ) count FROM tb_wharf_structure_inspection where DATE_FORMAT( validity_period, '%Y-%m-%d' ) <=DATE_FORMAT( now(), '%Y-%m-%d' ) GROUP BY company ) tb_wharf_structure_inspection ON tb_enterprise.org_code = tb_wharf_structure_inspection.company"+
        " LEFT JOIN ( SELECT company, count( company ) count FROM tb_facility_inspection where DATE_FORMAT( validity_period, '%Y-%m-%d' ) <=DATE_FORMAT( now(), '%Y-%m-%d' ) GROUP BY company ) tb_facility_inspection ON tb_enterprise.org_code = tb_facility_inspection.company"+
        " LEFT JOIN ( SELECT company, count( company ) count FROM tb_lightning_protection where DATE_FORMAT( validity_period, '%Y-%m-%d' ) <=DATE_FORMAT( now(), '%Y-%m-%d' )  GROUP BY company ) tb_lightning_protection ON tb_enterprise.org_code = tb_lightning_protection.company"+
        " LEFT JOIN ( SELECT company, count( company ) count FROM tb_pressure_vessel where DATE_FORMAT( validity_period, '%Y-%m-%d' ) <=DATE_FORMAT( now(), '%Y-%m-%d' ) GROUP BY company ) tb_pressure_vessel ON tb_enterprise.org_code = tb_pressure_vessel.company"+
        " LEFT JOIN ( SELECT company, count( company ) count FROM tb_tank_inspection where DATE_FORMAT( validity_period, '%Y-%m-%d' ) <=DATE_FORMAT( now(), '%Y-%m-%d' ) GROUP BY company ) tb_tank_inspection ON tb_enterprise.org_code = tb_tank_inspection.company"+
        " LEFT JOIN ( SELECT company, count( company ) count FROM tb_penstock where DATE_FORMAT( validity_period, '%Y-%m-%d' ) <=DATE_FORMAT( now(), '%Y-%m-%d' ) GROUP BY company ) tb_penstock ON tb_enterprise.org_code = tb_penstock.company"+
        " LEFT JOIN ( SELECT company, count( company ) count FROM tb_hose where DATE_FORMAT( validity_period, '%Y-%m-%d' ) <=DATE_FORMAT( now(), '%Y-%m-%d' ) GROUP BY company ) tb_hose ON tb_enterprise.org_code = tb_hose.company"+
        " LEFT JOIN ( SELECT company, count( company ) count FROM tb_pressure_gauge where DATE_FORMAT( validity_period, '%Y-%m-%d' ) <=DATE_FORMAT( now(), '%Y-%m-%d' ) GROUP BY company ) tb_pressure_gauge ON tb_enterprise.org_code = tb_pressure_gauge.company"+
        " LEFT JOIN ( SELECT company, count( company ) count FROM tb_safety_valve where DATE_FORMAT( validity_period, '%Y-%m-%d' ) <=DATE_FORMAT( now(), '%Y-%m-%d' ) GROUP BY company ) tb_safety_valve ON tb_enterprise.org_code = tb_safety_valve.company"+
        " LEFT JOIN ( SELECT company, count( company ) count FROM tb_combustible_gas where DATE_FORMAT( validity_period, '%Y-%m-%d' ) <=DATE_FORMAT( now(), '%Y-%m-%d' ) GROUP BY company ) tb_combustible_gas ON tb_enterprise.org_code = tb_combustible_gas.company"+
        " LEFT JOIN ( SELECT company, count( company ) count FROM tb_hoisting_machinery where DATE_FORMAT( validity_period, '%Y-%m-%d' ) <=DATE_FORMAT( now(), '%Y-%m-%d' ) GROUP BY company ) tb_hoisting_machinery ON tb_enterprise.org_code = tb_hoisting_machinery.company"+
        " LEFT JOIN ( SELECT company, count( company ) count FROM tb_other_tests where DATE_FORMAT( validity_period, '%Y-%m-%d' ) <=DATE_FORMAT( now(), '%Y-%m-%d' ) GROUP BY company ) tb_other_tests ON tb_enterprise.org_code = tb_other_tests.company";
        List<CompanyProblemList> companyProblemLists = EruptDaoUtils.selectOnes(sql, CompanyProblemList.class);

        String truncateSql="truncate table tb_company_problem_list";
        eruptDao.getJdbcTemplate().execute(truncateSql);

        for(CompanyProblemList companyProblemList : companyProblemLists){
            eruptDao.persist(companyProblemList);
        }

    }
}
