# ===========================================
# 大连港工作台 Docker Compose 环境配置模板
# ===========================================
# 请复制此文件为 .env 并填入真实的配置值
# 注意：.env 文件包含敏感信息，请勿提交到版本控制系统

# ===========================================
# 应用服务配置
# ===========================================
APP_PORT=8080
SERVER_PORT=8080
APP_IMAGE=daliangang/workbench:latest

# ===========================================
# MySQL数据库配置
# ===========================================
MYSQL_PORT=3306
MYSQL_ROOT_PASSWORD=your_mysql_root_password
MYSQL_DATABASE=daliangang
MYSQL_USER=daliangang_user
MYSQL_PASSWORD=your_mysql_user_password

# ===========================================
# Redis配置
# ===========================================
REDIS_PORT=6379
REDIS_PASSWORD=your_redis_password
REDIS_DATABASE=0

# ===========================================
# MinIO对象存储配置
# ===========================================
MINIO_PORT=9000
MINIO_CONSOLE_PORT=9001
MINIO_ACCESS_KEY=your_minio_access_key
MINIO_SECRET_KEY=your_minio_secret_key
MINIO_BUCKET_NAME=daliangang-bucket

# ===========================================
# Spring Security配置
# ===========================================
SPRING_SECURITY_USER_NAME=admin
SPRING_SECURITY_USER_PASSWORD=your_admin_password

# ===========================================
# Erupt框架配置
# ===========================================
ERUPT_DOMAIN=http://localhost:8080
ERUPT_FILE_DOMAIN=http://localhost:9000/daliangang-bucket
ERUPT_TITLE=大连港
ERUPT_DESC=危险货物港区重大安全风险管控平台
ERUPT_AMAP_KEY=your_amap_key_here

# ===========================================
# 其他第三方服务配置
# ===========================================
# 心知天气
XINZ_GY=your_xinz_gy_key
XINZ_SY=your_xinz_sy_key

# XXL-JOB配置
XXL_JOB_HOST=http://localhost:8888
XXL_JOB_APPNAME=daliangang
XXL_JOB_USERNAME=admin
XXL_JOB_PASSWORD=123456

# 一网协同配置
ONELINE_APPID=your_oneline_appid
ONELINE_SECURITY=your_oneline_security_key

# COS存储配置（如果使用）
COS_ACCESS_KEY=your_cos_access_key
COS_SECRET_KEY=your_cos_secret_key
COS_BUCKET=your_cos_bucket
COS_REGION=ap-beijing

# ===========================================
# 日志和监控配置
# ===========================================
GRAYLOG_HOST=localhost
GRAYLOG_PORT=12201
GRAYLOG_ENV=docker

# ===========================================
# 开发/调试配置
# ===========================================
# 是否启用调试模式
DEBUG_MODE=false
# 是否显示SQL日志
SHOW_SQL=false
# 是否包含异常信息
INCLUDE_EXCEPTION=false
INCLUDE_STACKTRACE=NEVER
INCLUDE_MESSAGE=false
# Actuator端点配置
ACTUATOR_ENDPOINTS=health,info
HEALTH_SHOW_DETAILS=when-authorized
