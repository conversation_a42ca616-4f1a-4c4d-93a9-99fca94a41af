/*
* Copyright © 2020-2035 yundingyun All rights reserved.
*/
package com.daliangang.rndpub.entity;

import com.daliangang.rndpub.proxy.QualificationDataProxy;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import xyz.erupt.annotation.Erupt;
import xyz.erupt.annotation.EruptField;
import xyz.erupt.annotation.sub_erupt.Power;
import xyz.erupt.annotation.sub_field.Edit;
import xyz.erupt.annotation.sub_field.EditType;
import xyz.erupt.annotation.sub_field.View;
import xyz.erupt.annotation.sub_field.sub_edit.AttachmentType;
import xyz.erupt.annotation.sub_field.sub_edit.InputType;
import xyz.erupt.toolkit.db.Comment;
import xyz.erupt.upms.model.auth.DataAuthModel;

import javax.persistence.Entity;
import javax.persistence.Table;

@Erupt(name = "资质证书", authVerify = false,power = @Power(add = true, delete = true, export = false, importable = true, viewDetails = true, edit = true)
, dataProxy = QualificationDataProxy.class
, rowOperation = {})
@Table(name = "tb_qualification")
@Entity
@Getter
@Setter
@Comment("资质证书")
@ApiModel("资质证书")
public class Qualification extends DataAuthModel {
	@EruptField(
		views = @View(title = "证书名称"),
		edit = @Edit(title = "证书名称", type = EditType.INPUT, notNull = true,
		inputType = @InputType))
	@Comment("证书名称")
	@ApiModelProperty("证书名称")
	private String name;

	@EruptField(
		views = @View(title = "附件名称"),
		edit = @Edit(title = "附件名称", type = EditType.ATTACHMENT, notNull = true,
		attachmentType = @AttachmentType(type = AttachmentType.Type.BASE, maxLimit = 1, fileTypes = {".pdf", ".doc", ".docx",".xlsx"})))
	@Comment("附件名称")
	@ApiModelProperty("附件名称")
	private String file;

//	@EruptField(
//		views = @View(title = "专家姓名"),
//		edit = @Edit(title = "专家姓名", type = EditType.CHOICE, search = @Search(vague = true), notNull = true,
//		choiceType = @ChoiceType(fetchHandler = SqlChoiceFetchHandler.class, fetchHandlerParams = "select '测试项','测试项'")))
//	@Comment("专家姓名")
//	@ApiModelProperty("专家姓名")
//	private String nameOfExpert;

}
