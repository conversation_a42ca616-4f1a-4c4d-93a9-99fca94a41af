package com.daliangang.rndpub.operation;


import cn.hutool.extra.qrcode.QrCodeUtil;
import com.daliangang.rndpub.RndpubConst;
import com.daliangang.rndpub.entity.Expert;
import lombok.Data;
import lombok.experimental.SuperBuilder;
import org.hibernate.annotations.GenericGenerator;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import xyz.erupt.annotation.Erupt;
import xyz.erupt.annotation.EruptField;
import xyz.erupt.annotation.fun.OperationHandler;
import xyz.erupt.annotation.sub_field.Edit;
import xyz.erupt.annotation.sub_field.EditType;
import xyz.erupt.annotation.sub_field.Readonly;
import xyz.erupt.annotation.sub_field.View;
import xyz.erupt.annotation.sub_field.sub_edit.AttachmentType;
import xyz.erupt.annotation.sub_field.sub_edit.InputType;
import xyz.erupt.core.annotation.EruptRouter;
import xyz.erupt.core.util.EruptSpringUtil;
import xyz.erupt.core.util.EruptUtil;
import xyz.erupt.toolkit.service.EruptCacheRedis;
import xyz.erupt.upms.platform.EruptOption;
import xyz.erupt.upms.platform.EruptOptions;
import xyz.erupt.upms.service.EruptPlatformService;

import javax.annotation.Resource;
import javax.persistence.Column;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Transient;
import javax.transaction.Transactional;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * @Author: zengwensi
 * @Description:
 * @Date: Created in 2023/4/6 17:24
 * @Modified By
 */
@Service
@EruptOptions(
        @EruptOption(name = "EXPERT_APPLY_QRCODE", value = "http://www.163.com", desc = "专家申请链接")
)
public class ExpertApplyQRCodeHandler implements OperationHandler<Expert, ExpertApplyQRCodeHandler.ExpertApplyQRCode> {


    @Data
    @SuperBuilder
    @Erupt(name = "二维码")
    public static class ExpertApplyQRCode {
        @Id
        @GeneratedValue(generator = "generator")
        @GenericGenerator(name = "generator", strategy = "native")
        @Column(name = "id")
        @EruptField
        private Long id;

        @Transient
        @EruptField(
                edit = @Edit(title = "申请链接", type = EditType.INPUT, readonly = @Readonly, inputType = @InputType(fullSpan = true,copy = false))
        )
        private String url;

        @Transient
        @EruptField(
                views = @View(title = "二维码"),
                edit = @Edit(title = "二维码", type = EditType.ATTACHMENT, readonly = @Readonly,//(add = false, edit = false),
                        attachmentType = @AttachmentType(type = AttachmentType.Type.IMAGE, maxLimit = 1))
        )
        private String img;

    }


    @RestController
    @Transactional
    public static class SendMessageController {
        @Resource
        private EruptCacheRedis eruptCacheRedis;

        @RequestMapping("/erupt-api/data/ExpertApplyQRCode/{id}")
        @EruptRouter(verifyType = EruptRouter.VerifyType.LOGIN)
        public ExpertApplyQRCodeHandler.ExpertApplyQRCode initValue() throws IOException {
            String expertLink = EruptSpringUtil.getBean(EruptPlatformService.class).getOption("EXPERT_APPLY_QRCODE").getAsString() ;
            String redisKey = RndpubConst.EXPERT_APPLY_KEY + expertLink;

            Object value = eruptCacheRedis.getAndSet(redisKey, TimeUnit.DAYS.toMillis(30), () -> {
                byte[] bytes = QrCodeUtil.generatePng(expertLink, 500, 500);
                InputStream sbs = new ByteArrayInputStream(bytes);
                return EruptUtil.findAttachmentProxy().upLoad(sbs, RndpubConst.EXPERT_APPLY_QRCODE_NAME);
            });

            return ExpertApplyQRCode.builder()
                    .img(String.valueOf(value))
                    .url(expertLink)
                    .build();
        }
    }


    @Override
    public String exec(List<Expert> data, ExpertApplyQRCodeHandler.ExpertApplyQRCode unused, String[] param) {

        return "";
    }
}
