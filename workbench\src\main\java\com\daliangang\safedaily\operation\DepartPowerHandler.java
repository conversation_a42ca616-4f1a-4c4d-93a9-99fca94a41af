package com.daliangang.safedaily.operation;

import com.daliangang.core.DaliangangContext;
import org.springframework.stereotype.Service;
import xyz.erupt.annotation.fun.PowerHandler;
import xyz.erupt.annotation.fun.PowerObject;

/**
 * @Title: DepartPowerHandler
 * <AUTHOR>
 * @Package com.daliangang.safedaily.operation
 * @Date 2024/3/7 16:52
 * @description: 动态控制增删改查
 */
@Service
public class DepartPowerHandler implements PowerHandler {

    @Override
    public void handler(PowerObject power) {
        /*企业上报政府查看*/
        /*判断当前用户是否为政府用户*/
       /* if (DaliangangContext.isDepartmentUser()) {
            power.setAdd(false);
            power.setDelete(false);
            power.setEdit(false);
            power.setQuery(true);
            power.setViewDetails(true);
            power.setImportable(false);
        }*/
        /*判断当前用户是否为企业用户:只能查看*/
        if(DaliangangContext.isEnterpriseUser()){
            power.setAdd(true);
            power.setDelete(false);
            power.setEdit(false);
            power.setQuery(true);
            power.setViewDetails(true);
            power.setImportable(false);
        }
    }
}
