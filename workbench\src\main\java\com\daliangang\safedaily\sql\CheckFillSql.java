package com.daliangang.safedaily.sql;

import org.springframework.stereotype.Repository;

/**
 * <AUTHOR>
 * @since :2023/4/23:11:27
 */
@Repository
public class CheckFillSql {

    //
    public String selectCheckFillNum (String orgCode) {

        String sql = "SELECT COUNT(*) as num  from tb_check_fill cf where cf.submitted = 1 and  `year`= YEAR(NOW())";
        sql += "and org_code "+orgCode+"";
        return sql;
    }
}
