/*
 * Copyright © 2020-2035 yundingyun All rights reserved.
 */
package com.daliangang.device.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import xyz.erupt.annotation.Erupt;
import xyz.erupt.annotation.EruptField;
import xyz.erupt.annotation.sub_erupt.Power;
import xyz.erupt.annotation.sub_field.Edit;
import xyz.erupt.annotation.sub_field.EditType;
import xyz.erupt.annotation.sub_field.View;
import xyz.erupt.annotation.sub_field.sub_edit.InputType;
import xyz.erupt.jpa.model.MetaModel;
import xyz.erupt.toolkit.db.Comment;

import javax.persistence.Entity;
import javax.persistence.Lob;
import javax.persistence.Table;

@Erupt(name = "第三方位置数据关联", authVerify = false,
        power = @Power(add = true, delete = true, export = false, importable = true, viewDetails = true, edit = true))
@Table(name = "tb_position_docking")
@Entity
@Getter
@Setter
@Comment("第三方位置数据关联")
@ApiModel("第三方位置数据关联")
public class PositionDocking extends MetaModel {

    @EruptField(
            views = @View(title = "本系统位置id"),
            edit = @Edit(title = "本系统位置id", type = EditType.INPUT,
                    inputType = @InputType))
    @Comment("本系统位置id")
    @ApiModelProperty("本系统位置id")
    private Long bId;

    @EruptField(
            views = @View(title = "位置类型"),
            edit = @Edit(title = "位置类型", type = EditType.INPUT,
                    inputType = @InputType))
    @Comment("位置类型")
    @ApiModelProperty("位置类型")
    private String positionType;

    @EruptField(
            views = @View(title = "位置名称"),
            edit = @Edit(title = "位置名称", type = EditType.INPUT,
                    inputType = @InputType))
    @Comment("位置名称")
    @ApiModelProperty("位置名称")
    private String positionName;

    @EruptField(
            views = @View(title = "三方系统位置id"),
            edit = @Edit(title = "三方系统位置id", type = EditType.INPUT,
                    inputType = @InputType))
    @Comment("三方系统位置id")
    @ApiModelProperty("三方系统位置id")
    private String positionId;

    @EruptField(
            views = @View(title = "地点位置", show = false),
            edit = @Edit(title = "地点位置", type = EditType.INPUT, show = false))
    @Comment("地点位置")
    @ApiModelProperty("地点位置")
    @Lob
    private String map;
}
