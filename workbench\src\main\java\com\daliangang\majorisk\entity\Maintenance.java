/*
 * Copyright © 2020-2035 yundingyun All rights reserved.
 */
package com.daliangang.majorisk.entity;

import com.daliangang.core.CompanyRenderHandler;
import com.daliangang.majorisk.operation.MaintenanceEscalationHandler;
import com.daliangang.majorisk.proxy.MaintenanceDataProxy;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import xyz.erupt.annotation.Erupt;
import xyz.erupt.annotation.EruptField;
import xyz.erupt.annotation.expr.ExprBool;
import xyz.erupt.annotation.sub_erupt.Power;
import xyz.erupt.annotation.sub_erupt.RowOperation;
import xyz.erupt.annotation.sub_field.Edit;
import xyz.erupt.annotation.sub_field.EditType;
import xyz.erupt.annotation.sub_field.Readonly;
import xyz.erupt.annotation.sub_field.View;
import xyz.erupt.annotation.sub_field.sub_edit.*;
import xyz.erupt.toolkit.db.Comment;
import xyz.erupt.toolkit.handler.*;
import xyz.erupt.toolkit.submit.CheckSubmit;
import xyz.erupt.toolkit.utils.TplUtils;
import xyz.erupt.upms.model.auth.DataAuthHandler;
import xyz.erupt.upms.model.auth.DataAuthModel;

import javax.persistence.Entity;
import javax.persistence.Table;
import java.time.LocalDateTime;

@Erupt(name = "检维修作业", power = @Power(delete = false, edit = false)
        , dataProxy = MaintenanceDataProxy.class
        , orderBy = "Maintenance.starDate desc",
        rowOperation = {
        @RowOperation(title = "编辑", icon = "fa fa-edit", code = TplUtils.EDIT_OPER_CODE, eruptClass = Maintenance.class,
                operationHandler = EditOperationHandler.class, ifExpr = "item.submitted=='未上报'", ifExprBehavior = RowOperation.IfExprBehavior.HIDE, mode = RowOperation.Mode.SINGLE),
        @RowOperation(title = "删除", icon = "fa fa-trash-o",
                operationHandler = DelOperationHandler.class, ifExpr = "item.submitted=='未上报'", ifExprBehavior = RowOperation.IfExprBehavior.HIDE, mode = RowOperation.Mode.SINGLE),
        @RowOperation(title = "上报", icon = "fa fa-arrow-circle-o-up", operationHandler = MaintenanceEscalationHandler.class, ifExpr = "item.submitted=='未上报'", ifExprBehavior = RowOperation.IfExprBehavior.HIDE, mode = RowOperation.Mode.SINGLE)
})
@Table(name = "tb_maintenance")
@Entity
@Getter
@Setter
@Comment("检维修作业")
@ApiModel("检维修作业")
public class Maintenance extends DataAuthModel {
    @EruptField(
            views = @View(title = "企业名称"),
            edit = @Edit(title = "企业名称", readonly = @Readonly(exprHandler = DataAuthHandler.class), type = EditType.CHOICE, notNull = true,search = @Search(ifRender = @ExprBool(exprHandler = CompanyRenderHandler.class)),
                    choiceType = @ChoiceType(fullSpan = true,fetchHandler = RemoteEntityChoiceFetchHandler.class, fetchHandlerParams = {"main", "Enterprise", "org_code,name"})))
    @Comment("企业名称")
    @ApiModelProperty("企业名称")
    private String company;

    @EruptField(
            views = @View(title = "上报状态", show = true),
            edit = @Edit(title = "上报状态", type = EditType.BOOLEAN, show = false, /*notNull = true,*/
                    boolType = @BoolType(trueText = "已上报", falseText = "未上报")))
    @CheckSubmit(linkProperty = "year", checkTimeDiffPeriod = 1)
    @Comment("上报状态")
    @ApiModelProperty("上报状态")
    private Boolean submitted;

    @EruptField(
            views = @View(title = "作业类型"),
            edit = @Edit(title = "作业类型", type = EditType.CHOICE, search = @Search, notNull = true,
                    //choiceType = @ChoiceType(fetchHandler = SqlChoiceFetchHandler.class, fetchHandlerParams = "select '测试项','测试项'")))
                    choiceType = @ChoiceType(fetchHandler = EruptDictCodeChoiceFetchHandler.class, fetchHandlerParams = "MaintenanceType")))

    @Comment("作业类型")
    @ApiModelProperty("作业类型")
    private String type;

    @EruptField(
            views = @View(title = "作业风险", show = false),
            edit = @Edit(title = "作业风险", type = EditType.CHOICE,notNull = true,
                    choiceType = @ChoiceType(
                            fullSpan = true,
                            fetchHandler = RemoteCallChoiceFetchHandler.class,
                            fetchHandlerParams = {"main", "erupt-api/get/riskDatabase"}
                    )
            )
    )
    @Comment("作业风险")
    @ApiModelProperty("作业风险")
    private String riskDatabase;

    @EruptField(
            views = @View(title = "开始时间"),
            edit = @Edit(title = "开始时间", type = EditType.DATE, notNull = true,
                    dateType = @DateType(type = DateType.Type.DATE_TIME)))
    @Comment("开始时间")
    @ApiModelProperty("开始时间")
    private LocalDateTime starDate;

    @EruptField(
            views = @View(title = "结束时间"),
            edit = @Edit(title = "结束时间", type = EditType.DATE, notNull = true,
                    dateType = @DateType(type = DateType.Type.DATE_TIME)))
    @Comment("结束时间")
    @ApiModelProperty("结束时间")
    private LocalDateTime endDate;


    @EruptField(
            views = @View(title = "作业地点_经度", show = false),
            edit = @Edit(title = "作业地点_经度", type = EditType.NUMBER, show = false,
                    numberType = @NumberType))
    @Comment("作业地点_经度")
    @ApiModelProperty("作业地点_经度")
    private Integer addressLongitude;

    @EruptField(
            views = @View(title = "作业地点_纬度", show = false),
            edit = @Edit(title = "作业地点_纬度", type = EditType.NUMBER, show = false,
                    numberType = @NumberType))
    @Comment("作业地点_纬度")
    @ApiModelProperty("作业地点_纬度")
    private Integer addressLatitude;

    @EruptField(
            views = @View(title = "作业票", show = false),
            edit = @Edit(title = "作业票", type = EditType.ATTACHMENT, notNull = true,
                    attachmentType = @AttachmentType(type = AttachmentType.Type.BASE, maxLimit = 20, fileTypes = {".pdf", ".doc", ".docx", ".xlsx", ".png", ".jpg", ".xls", ".ppt", ".pptx", ".zip", ".rar"})))
    @Comment("作业票")
    @ApiModelProperty("作业票")
    private String file;

    @EruptField(
            views = @View(title = "作业状态"),
            edit = @Edit(title = "作业状态", type = EditType.CHOICE, search = @Search, show = false,
                    choiceType = @ChoiceType(fetchHandler = EruptDictCodeChoiceFetchHandler.class, fetchHandlerParams = "state")))
    @Comment("作业状态")
    @ApiModelProperty("作业状态")
    private String state;

    @EruptField(
            views = @View(title = "作业名称"),
            edit = @Edit(title = "作业名称", type = EditType.INPUT, search = @Search, notNull = true,
                    inputType = @InputType))
    @Comment("作业名称")
    @ApiModelProperty("作业名称")
    private String workName;

    @EruptField(
            views = @View(title = "作业地点", show = false),
            edit = @Edit(title = "作业地点", type = EditType.MAP, show = true
            ))
    @Comment("作业地点")
    @ApiModelProperty("作业地点")
    private String address;

//	@EruptField(
//			views = @View(title = "作业地点", show = false),
//			edit = @Edit(title = "作业地点", type = EditType.MAP, show = true))
//	@Comment("作业地点")
//	@ApiModelProperty("作业地点")
//	@Lob private String map;


}
