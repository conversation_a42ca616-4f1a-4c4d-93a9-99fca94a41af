package com.daliangang.emergency.entity;

import cn.hutool.json.JSONObject;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import xyz.erupt.annotation.Erupt;
import xyz.erupt.annotation.EruptField;
import xyz.erupt.annotation.expr.ExprBool;
import xyz.erupt.annotation.fun.DataProxy;
import xyz.erupt.annotation.sub_erupt.Power;
import xyz.erupt.annotation.sub_field.*;
import xyz.erupt.annotation.sub_field.sub_edit.ChoiceType;
import xyz.erupt.annotation.sub_field.sub_edit.DateType;
import xyz.erupt.annotation.sub_field.sub_edit.InputType;
import xyz.erupt.common.http.EruptRemoteUtils;
import xyz.erupt.core.util.EruptSpringUtil;
import xyz.erupt.jpa.model.MetaModel;
import xyz.erupt.toolkit.db.Comment;
import xyz.erupt.toolkit.handler.RemoteEntityChoiceFetchHandler;
import xyz.erupt.toolkit.remote.RemoteProxyService;
import xyz.erupt.upms.model.auth.DataAuthHandler;
import xyz.erupt.upms.model.auth.DataAuthModel;

import javax.annotation.Resource;
import javax.persistence.Entity;
import javax.persistence.Table;
import java.sql.Timestamp;
import java.util.Map;

/**
 * @Author: zengwensi
 * @Description:
 * @Date: Created in 2023/3/22 10:21
 * @Modified By
 */
@Erupt(name = "应急值守管理人", power = @Power(add = true, delete = true, export = false, importable = true, viewDetails = true, edit = true)
        , dataProxy = EmergencyDutyPerson.class
        , rowOperation = {})
@Table(name = "tb_emergency_duty_person")
@Entity
@Getter
@Setter
@Comment("应急值守管理人")
@ApiModel("应急值守管理人")
public class EmergencyDutyPerson extends MetaModel implements DataProxy<EmergencyDutyPerson>, ExprBool.ExprHandler {

    @EruptField(
            views = @View(title = "开始值守时间（年月日时分）", show = false, ifRender = @ExprBool(exprHandler = EmergencyDutyPerson.class)),
            edit = @Edit(title = "开始值守时间（年月日时分）", type = EditType.DATE, show = false,
                    dateType = @DateType(type = DateType.Type.DATE_TIME)))
    @Comment("开始值守时间（年月日时分）")
    @ApiModelProperty("开始值守时间（年月日时分）")
    private Timestamp dutyTime;

    @EruptField(
            views = @View(title = "结束值守时间（年月日时分）", show = false, ifRender = @ExprBool(exprHandler = EmergencyDutyPerson.class)),
            edit = @Edit(title = "结束值守时间（年月日时分）", type = EditType.DATE, show = false,
                    dateType = @DateType(type = DateType.Type.DATE_TIME)))
    @Comment("结束值守时间（年月日时分）")
    @ApiModelProperty("结束值守时间（年月日时分）")
    private Timestamp endDutyTime;

    @EruptField(
            views = @View(title = "值守人"),
            edit = @Edit(title = "值守人", type = EditType.INPUT, notNull = true,
                    inputType = @InputType))
    @Comment("值守人")
    @ApiModelProperty("值守人")
    private String personOnDuty;

    @EruptField(
            views = @View(title = "联系方式"),
            edit = @Edit(title = "联系方式", type = EditType.INPUT, notNull = true,
                    inputType = @InputType))
    @Comment("联系方式")
    @ApiModelProperty("联系方式")
    private String tel;

    @EruptField(
            views = @View(title = "所属单位", show = false, ifRender = @ExprBool(exprHandler = EmergencyDutyPerson.class)),
            edit = @Edit(title = "所属单位", show = false, readonly = @Readonly(exprHandler = DataAuthHandler.class), type = EditType.CHOICE, //notNull = true,
                    choiceType = @ChoiceType(fetchHandler = RemoteEntityChoiceFetchHandler.class, fetchHandlerParams = {"main", "EruptOrg", "code,name"})))
    @Comment("所属单位")
    @ApiModelProperty("所属单位")
    private String company;

    @Override
    public boolean handler(boolean expr, String[] params) {
        return false;
    }

    @Override
    public void afterAdd(EmergencyDutyPerson emergencyDutyPerson) {
        RemoteProxyService remoteProxyService = EruptSpringUtil.getBean(RemoteProxyService.class);
        //推送传递
        JSONObject inputData = new JSONObject();
        inputData.set("clazz", "EmergencyDutyPerson");
        inputData.set("insertData",emergencyDutyPerson);
        EruptRemoteUtils.doPost(remoteProxyService.getMainServerUrl() + "/erupt-api/exchange/data/exchange", inputData, Map.class);
    }

    @Override
    public void afterUpdate(EmergencyDutyPerson emergencyDutyPerson) {
        RemoteProxyService remoteProxyService = EruptSpringUtil.getBean(RemoteProxyService.class);
        //推送数据
        JSONObject inputData = new JSONObject();
        inputData.set("clazz", "EmergencyDutyPerson");
        inputData.set("insertData",emergencyDutyPerson);
        EruptRemoteUtils.doPost(remoteProxyService.getMainServerUrl() + "/erupt-api/exchange/data/exchange", inputData, Map.class);
    }
}
