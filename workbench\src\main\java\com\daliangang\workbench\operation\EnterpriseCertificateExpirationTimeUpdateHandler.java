package com.daliangang.workbench.operation;

import com.daliangang.workbench.entity.EnterpriseCertificateView;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.stereotype.Service;
import xyz.erupt.annotation.Erupt;
import xyz.erupt.annotation.EruptField;
import xyz.erupt.annotation.fun.OperationHandler;
import xyz.erupt.annotation.sub_field.Edit;
import xyz.erupt.annotation.sub_field.EditType;
import xyz.erupt.annotation.sub_field.View;
import xyz.erupt.annotation.sub_field.sub_edit.DateType;
import xyz.erupt.jpa.dao.EruptDao;
import xyz.erupt.jpa.model.BaseModel;
import xyz.erupt.toolkit.db.Comment;

import javax.annotation.Resource;
import javax.transaction.Transactional;
import java.util.List;

@Service
public class EnterpriseCertificateExpirationTimeUpdateHandler implements OperationHandler<EnterpriseCertificateView, EnterpriseCertificateExpirationTimeUpdateHandler.ExpirationTimeUpdate> {

    @Erupt(name = "修改到期日期")
    @Data
    public static class ExpirationTimeUpdate extends BaseModel {
        @EruptField(
                views = @View(title = "到期时间"),
                edit = @Edit(title = "到期时间", type = EditType.DATE, notNull = true,
                        dateType = @DateType))
        @Comment("到期时间")
        @ApiModelProperty("到期时间")
        private java.util.Date expirationTime;
    }
    @Resource
    private EruptDao eruptDao ;

    @Transactional
    @Override
    public String exec(List<EnterpriseCertificateView> data, EnterpriseCertificateExpirationTimeUpdateHandler.ExpirationTimeUpdate expirationTimeUpdate, String[] param) {
        for(EnterpriseCertificateView view : data){
            view.setExpirationTime(expirationTimeUpdate.getExpirationTime());
            eruptDao.mergeAndFlush(view);
        }
        return null ;
    }
}
