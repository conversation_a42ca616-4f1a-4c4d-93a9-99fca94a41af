/*
 * Copyright © 2020-2035 yundingyun All rights reserved.
 */
package com.daliangang.rndpub.operation;

import com.daliangang.rndpub.entity.CheckPerson;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import xyz.erupt.annotation.Erupt;
import xyz.erupt.annotation.EruptField;
import xyz.erupt.annotation.expr.ExprBool;
import xyz.erupt.annotation.fun.OperationHandler;
import xyz.erupt.annotation.sub_field.Edit;
import xyz.erupt.annotation.sub_field.EditType;
import xyz.erupt.annotation.sub_field.sub_edit.ChoiceType;
import xyz.erupt.annotation.sub_field.sub_edit.Search;
import xyz.erupt.jpa.dao.EruptDao;
import xyz.erupt.jpa.model.BaseModel;
import xyz.erupt.toolkit.handler.SqlChoiceFetchHandler;

import javax.annotation.Resource;
import javax.transaction.Transactional;
import java.util.List;

@Service
public class CheckpersonAddHandler implements OperationHandler<CheckPerson, CheckpersonAddHandler.CheckPersionAddForm> {

    @Erupt(name = "添加检查人员")
    @Data
    public static class CheckPersionAddForm extends BaseModel implements ExprBool.ExprHandler {
        @EruptField(
                edit = @Edit(title = "检查人员", type = EditType.CHOICE, search = @Search(vague = true),
                        ifRender = @ExprBool(exprHandler = CheckPersionAddForm.class, params = "checkPerson"),
                        choiceType = @ChoiceType(type = ChoiceType.Type.RADIO, anewFetch = true, fetchHandler = SqlChoiceFetchHandler.class, fetchHandlerParams = "select name,name from tb_checkperson where check_person=1 and state=0")))
        private String checkPerson;

        @EruptField(
                edit = @Edit(title = "非检查人员", type = EditType.CHOICE, search = @Search(vague = true),
                        ifRender = @ExprBool(exprHandler = CheckPersionAddForm.class, params = "notCheckPerson"),
                        choiceType = @ChoiceType(type = ChoiceType.Type.RADIO, anewFetch = true, fetchHandler = SqlChoiceFetchHandler.class, fetchHandlerParams = "select name,name from tb_checkperson where check_person=0 and state=0")))
        private String notCheckPerson;

        @Override
        public boolean handler(boolean expr, String[] params) {
//            if (params[0].equalsIgnoreCase("checkPerson")) {
//                String sql = "select count(*) as count from tb_checkperson where check_person=1 and state=0";
//                if (EruptDaoUtils.selectMap(sql).getInt("count") > 0) return true;
//            } else if (params[0].equalsIgnoreCase("notCheckPerson")) {
//                String sql = "select count(*) as count from tb_checkperson where check_person=0 and state=0";
//                if (EruptDaoUtils.selectMap(sql).getInt("count") > 0) return true;
//            }
            return true;
        }
    }

    @Resource
    private EruptDao eruptDao;

    @Override
    @Transactional
    public String exec(List<CheckPerson> data, CheckPersionAddForm form, String[] param) {
        if (StringUtils.isNotEmpty(form.getCheckPerson())) {
            CheckPerson checkperson = eruptDao.queryEntity(CheckPerson.class, "name='" + form.getCheckPerson() + "'");
            checkperson.setState(true);
            eruptDao.merge(checkperson);
        }
        if (StringUtils.isNotEmpty(form.getNotCheckPerson())) {
            CheckPerson checkperson = eruptDao.queryEntity(CheckPerson.class, "name='" + form.getNotCheckPerson() + "'");
            checkperson.setState(true);
            eruptDao.merge(checkperson);
        }
        return null;
    }
}
