package com.daliangang.workbench.init;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import xyz.erupt.excel.model.ExcelHeader;

/**
 * @Author：chb
 * @Package：com.daliangang.workbench.init
 * @Project：erupt
 * @name：InitRole
 * @Date：2023/3/5 22:04
 * @Filename：InitRole
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class InitRole extends InitTemplate {
    @ExcelHeader(col = 0, name = "编码")
    private String code;

    @ExcelHeader(col = 1, name = "名称")
    private String name;

    @ExcelHeader(col = 2, name = "展示顺序")
    private int sort;

    @ExcelHeader(col = 3, name = "共享角色")
    private String share;

    @ExcelHeader(col = 4, name = "角色身份")
    private String ident;

    public static InitRole valueOf(InitTemplate template) {
        return InitRole.builder().code(template.getParam1())
                .name(template.getParam2())
                .sort(Integer.valueOf(template.getParam3()))
                .share(template.getParam4())
                .ident(template.getParam5())
                .build();
    }
}
