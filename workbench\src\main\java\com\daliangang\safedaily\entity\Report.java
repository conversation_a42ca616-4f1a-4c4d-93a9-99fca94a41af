/*
 * Copyright © 2020-2035 yundingyun All rights reserved.
 */
package com.daliangang.safedaily.entity;

import com.daliangang.core.CompanyRenderHandler;
import com.daliangang.safedaily.operation.ReportEscalationHandler;
import com.daliangang.safedaily.proxy.ReportDataProxy;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import xyz.erupt.annotation.Erupt;
import xyz.erupt.annotation.EruptField;
import xyz.erupt.annotation.expr.ExprBool;
import xyz.erupt.annotation.sub_erupt.Power;
import xyz.erupt.annotation.sub_erupt.RowOperation;
import xyz.erupt.annotation.sub_field.Edit;
import xyz.erupt.annotation.sub_field.EditType;
import xyz.erupt.annotation.sub_field.Readonly;
import xyz.erupt.annotation.sub_field.View;
import xyz.erupt.annotation.sub_field.sub_edit.*;
import xyz.erupt.toolkit.db.Comment;
import xyz.erupt.toolkit.handler.DelOperationHandler;
import xyz.erupt.toolkit.handler.EditOperationHandler;
import xyz.erupt.toolkit.handler.RemoteEntityChoiceFetchHandler;
import xyz.erupt.toolkit.submit.CheckSubmit;
import xyz.erupt.toolkit.utils.TplUtils;
import xyz.erupt.upms.model.auth.DataAuthHandler;
import xyz.erupt.upms.model.auth.DataAuthModel;

import javax.persistence.Entity;
import javax.persistence.Lob;
import javax.persistence.Table;

@Erupt(name = "可靠性报告单", power = @Power(add = true, delete = false, export = true, importable = true, viewDetails = true, edit = false)
        , dataProxy = ReportDataProxy.class
        ,orderBy = "Report.modifyTime desc",
        rowOperation = {
        @RowOperation(title = "上报", icon = "fa fa-arrow-circle-o-up", operationHandler = ReportEscalationHandler.class, ifExpr = "item.submitted=='未上报'", mode = RowOperation.Mode.SINGLE),
        @RowOperation(title = "编辑", icon = "fa fa-edit", code = TplUtils.EDIT_OPER_CODE,
                eruptClass = Report.class, operationHandler = EditOperationHandler.class,
                ifExpr = "item.submitted=='未上报'", ifExprBehavior = RowOperation.IfExprBehavior.HIDE,
                mode = RowOperation.Mode.SINGLE),
        @RowOperation(title = "删除", icon = "fa fa-trash-o",
                operationHandler = DelOperationHandler.class, ifExpr = "item.submitted=='未上报'", ifExprBehavior = RowOperation.IfExprBehavior.HIDE, mode = RowOperation.Mode.SINGLE),
})
@Table(name = "tb_report")
@Entity
@Getter
@Setter
@Comment("可靠性报告单")
@ApiModel("可靠性报告单")
public class Report extends DataAuthModel {

    public static final String DEFAULT_CONTENT =
            "某年某月，我公司共有几处安全风险，其中红色几处、橙色几处、黄色几处、蓝色几处。\n" +
            "针对红色风险中几处生产作业风险，采取了几项措施；几处特殊作业风险，采取了几项措施。\n" +
            "针对橙色风险中几处生产作业风险，采取了几项措施；几处特殊作业风险，采取了几项措施。\n" +
            "针对黄色风险中几处生产作业风险，采取了几项措施；几处特殊作业风险，采取了几项措施。\n" +
            "针对蓝色风险中几处生产作业风险，采取了几项措施；几处特殊作业风险，采取了几项措施。\n" +
            "上述安全风险辨识及防控措施，经过企业组织专业技术人员（专家）或某某专业机构评估安全可靠。";
    @EruptField(
            views = @View(title = "企业名称",width = "270px"),
            edit = @Edit(title = "企业名称",search = @Search(ifRender = @ExprBool(exprHandler = CompanyRenderHandler.class)), readonly = @Readonly(exprHandler = DataAuthHandler.class), type = EditType.CHOICE, notNull = true,
                    choiceType = @ChoiceType(fullSpan = true,fetchHandler = RemoteEntityChoiceFetchHandler.class, fetchHandlerParams = {"main", "Enterprise", "org_code,name"})))
    @Comment("企业名称")
    @ApiModelProperty("企业名称")
    private String company;

    @EruptField(
            views = @View(title = "上报状态",width = "170px"),
            edit = @Edit(title = "上报状态", type = EditType.BOOLEAN, show = false, /*notNull = true, */
                    boolType = @BoolType(trueText = "已上报", falseText = "未上报")))
    @CheckSubmit(linkProperty = "mouth", checkTimeDiffPeriod = 1)
    @Comment("上报状态")
    @ApiModelProperty("上报状态")
    private Boolean submitted;

    @Lob
    @EruptField(
            views = @View(title = "内容",show = false),
            edit = @Edit(title = "内容", type = EditType.TEXTAREA,show = true,notNull = true,
                    inputType = @InputType(length = 1000)))
    @Comment("内容")
    @ApiModelProperty("内容")
    private String content ;

    @EruptField(
            views = @View(title = "董事长或总经理",width = "250px"),
            edit = @Edit(title = "董事长或总经理", type = EditType.INPUT,  notNull = true,
                    inputType = @InputType))
    @Comment("董事长或总经理")
    @ApiModelProperty("董事长或总经理")
    private String resPerson;

    @EruptField(
            views = @View(title = "附件", show = false),
            edit = @Edit(title = "附件", type = EditType.ATTACHMENT, notNull = false,
                    attachmentType = @AttachmentType(type = AttachmentType.Type.BASE, maxLimit = 20, fileTypes = {".pdf", ".doc", ".docx", ".xlsx", ".png", ".jpg", ".xls", ".ppt", ".pptx", ".zip", ".rar"})))
    @Comment("附件")
    @ApiModelProperty("附件")
    private String reportAttachments;

//    @EruptField(
//            views = @View(title = "月份"),
//            edit = @Edit(title = "月份", type = EditType.DATE, notNull = true,
//                    dateType = @DateType(type = DateType.Type.MONTH)))
//    @Comment("月份")
//    @ApiModelProperty("月份")
//    private String mouth;

//    @EruptField(
//            views = @View(title = "安全风险总数", show = false),
//            edit = @Edit(title = "安全风险总数", type = EditType.INPUT, notNull = true,
//                    inputType = @InputType))
//    @Comment("安全风险总数")
//    @ApiModelProperty("安全风险总数")
//    private String securityriskNo;
//
//    @EruptField(
//            views = @View(title = "红色风险数量"),
//            edit = @Edit(title = "红色风险数量", type = EditType.INPUT, notNull = true,
//                    inputType = @InputType))
//    @Comment("红色风险数量")
//    @ApiModelProperty("红色风险数量")
//    private String redriskNo;
//
//    @EruptField(
//            views = @View(title = "橙色风险数量"),
//            edit = @Edit(title = "橙色风险数量", type = EditType.INPUT, notNull = true,
//                    inputType = @InputType))
//    @Comment("橙色风险数量")
//    @ApiModelProperty("橙色风险数量")
//    private String orariskNo;
//
//    @EruptField(
//            views = @View(title = "黄色风险数量"),
//            edit = @Edit(title = "黄色风险数量", type = EditType.INPUT, notNull = true,
//                    inputType = @InputType))
//    @Comment("黄色风险数量")
//    @ApiModelProperty("黄色风险数量")
//    private String yellowriskNo;
//
//    @EruptField(
//            views = @View(title = "蓝色风险数量"),
//            edit = @Edit(title = "蓝色风险数量", type = EditType.INPUT, notNull = true,
//                    inputType = @InputType))
//    @Comment("蓝色风险数量")
//    @ApiModelProperty("蓝色风险数量")
//    private String buleriskNo;
//
//    @EruptField(
//            views = @View(title = "评估机构"),
//            edit = @Edit(title = "评估机构", type = EditType.INPUT, notNull = true,
//                    inputType = @InputType))
//    @Comment("评估机构")
//    @ApiModelProperty("评估机构")
//    private String evaluationBody;
//
//    @EruptField(
//            views = @View(title = "报告人"),
//            edit = @Edit(title = "报告人", type = EditType.INPUT, search = @Search(vague = true), notNull = true,
//                    inputType = @InputType))
//    @Comment("报告人")
//    @ApiModelProperty("报告人")
//    private String reporter;
//
//    @EruptField(
//            views = @View(title = "红色风险", show = false),
//            edit = @Edit(title = "红色风险", type = EditType.DIVIDE))
//    @Transient
//    @Comment("红色风险")
//    @ApiModelProperty("红色风险")
//    private String redDivide;
//
//    @EruptField(
//            views = @View(title = "生产作业风险数量", show = false),
//            edit = @Edit(title = "生产作业风险数量", type = EditType.INPUT, notNull = true,
//                    inputType = @InputType))
//    @Comment("生产作业风险数量")
//    @ApiModelProperty("生产作业风险数量")
//    private String redworkNo;
//
//    @EruptField(
//            views = @View(title = "生产作业风险采取的措施", show = false),
//            edit = @Edit(title = "生产作业风险采取的措施", type = EditType.INPUT, notNull = true,
//                    inputType = @InputType))
//    @Comment("生产作业风险采取的措施")
//    @ApiModelProperty("生产作业风险采取的措施")
//    private String rwmeasureNo;
//
//    @EruptField(
//            views = @View(title = "特殊作业风险数量", show = false),
//            edit = @Edit(title = "特殊作业风险数量", type = EditType.INPUT, notNull = true,
//                    inputType = @InputType))
//    @Comment("特殊作业风险数量")
//    @ApiModelProperty("特殊作业风险数量")
//    private String redsperiskNo;
//
//    @EruptField(
//            views = @View(title = "特殊作业风险采取的措施", show = false),
//            edit = @Edit(title = "特殊作业风险采取的措施", type = EditType.INPUT, notNull = true,
//                    inputType = @InputType))
//    @Comment("特殊作业风险采取的措施")
//    @ApiModelProperty("特殊作业风险采取的措施")
//    private String rriskmeasureNo;
//
//    @EruptField(
//            views = @View(title = "橙色风险", show = false),
//            edit = @Edit(title = "橙色风险", type = EditType.DIVIDE))
//    @Transient
//    @Comment("橙色风险")
//    @ApiModelProperty("橙色风险")
//    private String orDivide;
//
//    @EruptField(
//            views = @View(title = "生产作业风险数量", show = false),
//            edit = @Edit(title = "生产作业风险数量", type = EditType.INPUT, notNull = true,
//                    inputType = @InputType))
//    @Comment("生产作业风险数量")
//    @ApiModelProperty("生产作业风险数量")
//    private String oraworkNo;
//
//    @EruptField(
//            views = @View(title = "生产作业风险采取的措施", show = false),
//            edit = @Edit(title = "生产作业风险采取的措施", type = EditType.INPUT, notNull = true,
//                    inputType = @InputType))
//    @Comment("生产作业风险采取的措施")
//    @ApiModelProperty("生产作业风险采取的措施")
//    private String owmeasureNo;
//
//    @EruptField(
//            views = @View(title = "特殊作业风险数量", show = false),
//            edit = @Edit(title = "特殊作业风险数量", type = EditType.INPUT, notNull = true,
//                    inputType = @InputType))
//    @Comment("特殊作业风险数量")
//    @ApiModelProperty("特殊作业风险数量")
//    private String orasperiskNo;
//
//    @EruptField(
//            views = @View(title = "特殊作业风险采取的措施", show = false),
//            edit = @Edit(title = "特殊作业风险采取的措施", type = EditType.INPUT, notNull = true,
//                    inputType = @InputType))
//    @Comment("特殊作业风险采取的措施")
//    @ApiModelProperty("特殊作业风险采取的措施")
//    private String oriskmeasureNo;
//
//    @EruptField(
//            views = @View(title = "黄色风险", show = false),
//            edit = @Edit(title = "黄色风险", type = EditType.DIVIDE))
//    @Transient
//    @Comment("黄色风险")
//    @ApiModelProperty("黄色风险")
//    private String yelDivide;
//
//    @EruptField(
//            views = @View(title = "生产作业风险数量", show = false),
//            edit = @Edit(title = "生产作业风险数量", type = EditType.INPUT, notNull = true,
//                    inputType = @InputType))
//    @Comment("生产作业风险数量")
//    @ApiModelProperty("生产作业风险数量")
//    private String yelworkNo;
//
//    @EruptField(
//            views = @View(title = "生产作业风险采取的措施", show = false),
//            edit = @Edit(title = "生产作业风险采取的措施", type = EditType.INPUT, notNull = true,
//                    inputType = @InputType))
//    @Comment("生产作业风险采取的措施")
//    @ApiModelProperty("生产作业风险采取的措施")
//    private String ywmeasureNo;
//
//    @EruptField(
//            views = @View(title = "特殊作业风险数量", show = false),
//            edit = @Edit(title = "特殊作业风险数量", type = EditType.INPUT, notNull = true,
//                    inputType = @InputType))
//    @Comment("特殊作业风险数量")
//    @ApiModelProperty("特殊作业风险数量")
//    private String yelsperiskNo;
//
//    @EruptField(
//            views = @View(title = "特殊作业风险采取的措施", show = false),
//            edit = @Edit(title = "特殊作业风险采取的措施", type = EditType.INPUT, notNull = true,
//                    inputType = @InputType))
//    @Comment("特殊作业风险采取的措施")
//    @ApiModelProperty("特殊作业风险采取的措施")
//    private String yriskmeasureNo;
//
//    @EruptField(
//            views = @View(title = "蓝色风险", show = false),
//            edit = @Edit(title = "蓝色风险", type = EditType.DIVIDE))
//    @Transient
//    @Comment("蓝色风险")
//    @ApiModelProperty("蓝色风险")
//    private String blueDivide;
//
//    @EruptField(
//            views = @View(title = "生产作业风险数量", show = false),
//            edit = @Edit(title = "生产作业风险数量", type = EditType.INPUT, notNull = true,
//                    inputType = @InputType))
//    @Comment("生产作业风险数量")
//    @ApiModelProperty("生产作业风险数量")
//    private String blueworkNo;
//
//    @EruptField(
//            views = @View(title = "生产作业风险采取的措施", show = false),
//            edit = @Edit(title = "生产作业风险采取的措施", type = EditType.INPUT, notNull = true,
//                    inputType = @InputType))
//    @Comment("生产作业风险采取的措施")
//    @ApiModelProperty("生产作业风险采取的措施")
//    private String bwmeasureNo;
//
//    @EruptField(
//            views = @View(title = "特殊作业风险数量", show = false),
//            edit = @Edit(title = "特殊作业风险数量", type = EditType.INPUT, notNull = true,
//                    inputType = @InputType))
//    @Comment("特殊作业风险数量")
//    @ApiModelProperty("特殊作业风险数量")
//    private String belsperiskNo;
//
//    @EruptField(
//            views = @View(title = "特殊作业风险采取的措施", show = false),
//            edit = @Edit(title = "特殊作业风险采取的措施", type = EditType.INPUT, notNull = true,
//                    inputType = @InputType))
//    @Comment("特殊作业风险采取的措施")
//    @ApiModelProperty("特殊作业风险采取的措施")
//    private String briskmeasureNo;


//	@EruptField(
//		views = @View(title = "岗位", show = false),
//		edit = @Edit(title = "岗位", type = EditType.INPUT, show = false, notNull = true,
//		inputType = @InputType))
//	@Comment("岗位")
//	@ApiModelProperty("岗位")
//	private String jobTitle;

    @EruptField(
            views = @View(title = "是否上报", show = false),
            edit = @Edit(title = "是否上报", type = EditType.BOOLEAN, show = false,
                    boolType = @BoolType(trueText = "是", falseText = "否")))
    @Comment("是否上报")
    @ApiModelProperty("是否上报")
    private Boolean isReport;

    @EruptField(
            views = @View(title = "更新时间"),
            edit = @Edit(title = "更新时间",
                    type = EditType.DATE,
                    show = false,
                    search = @Search(),
                    dateType = @DateType(type = DateType.Type.MONTH)
            )
    )
    @Comment("更新时间")
    @ApiModelProperty("更新时间")
    private String modifyTime;

}
