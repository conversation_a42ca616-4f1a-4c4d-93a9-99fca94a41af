package com.daliangang.training.models;

import lombok.*;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Date 2023/5/19
 * @Description: 用户同步实体
 */
//@Entity
@Getter
@Setter
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class UserSyncEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    //用户id
    private Long id;

    //用户名称
    private String name;

    //用户手机
    private String phone;

    //用户性别
    private Boolean sex;

    //用户状态
    private Boolean state;

    //用户orgCode
    private String orgCode;

    //用户岗位 | 分割
    private String posts;

    //用户角色模板id
    private Long templateId;

    //是否主管
    private Boolean manager;

    //删除标识
    private Boolean delFlag;

    //更新/新增标识
    private Boolean updateFlag;

    //用户部门
    private String department;

    //用户编码
    private String code;
}
