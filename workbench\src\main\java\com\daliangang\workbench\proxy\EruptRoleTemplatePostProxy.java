package com.daliangang.workbench.proxy;

import com.daliangang.core.DaliangangContext;
import com.daliangang.training.models.PostSyncEntity;
import com.daliangang.workbench.entity.EruptRoleTemplatePost;
import com.daliangang.workbench.service.EruptRoleTemplatePostService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import xyz.erupt.annotation.fun.DataProxy;
import xyz.erupt.annotation.query.Condition;
import xyz.erupt.core.toolkit.TimeRecorder;
import xyz.erupt.jpa.dao.EruptDao;
import xyz.erupt.toolkit.redismq.RedisMQConst;
import xyz.erupt.toolkit.redismq.RedisMQService;
import xyz.erupt.toolkit.utils.AssertUtils;
import xyz.erupt.toolkit.utils.EruptDaoUtils;
import xyz.erupt.toolkit.utils.NotifyUtils;
import xyz.erupt.toolkit.utils.SqlUtils;
import xyz.erupt.upms.model.EruptOrg;
import xyz.erupt.upms.model.EruptPost;
import xyz.erupt.upms.model.EruptUser;
import xyz.erupt.upms.service.EruptUserService;
import xyz.erupt.upms.util.MetaUtil;

import javax.annotation.Resource;
import javax.transaction.Transactional;
import java.util.List;
import java.util.UUID;

/**
 * @Author：chb
 * @Package：xyz.erupt.upms.model.template
 * @Project：erupt
 * @name：EruptRoleTemplatePostProxy
 * @Date：2023/3/8 03:35
 * @Filename：EruptRoleTemplatePostProxy
 */
@Component
public class EruptRoleTemplatePostProxy implements DataProxy<EruptRoleTemplatePost> {

    @Resource
    private EruptUserService eruptUserService;

    @Resource
    private EruptRoleTemplatePostService eruptRoleTemplatePostService;

    @Resource
    private EruptDao eruptDao;

    @Resource
    private RedisMQService redisMQService;

    @Override
    public void addBehavior(EruptRoleTemplatePost eruptRoleTemplatePost) {
        eruptRoleTemplatePost.setCode(UUID.randomUUID().toString().split("-")[0]);
        eruptRoleTemplatePost.setExclusive(false);
        eruptRoleTemplatePost.setType(EruptPost.POST_TYPE_DEFAULT);
        MetaUtil.prepareMetaInfo(eruptRoleTemplatePost, true, true);
        this.checkOrgCode(eruptRoleTemplatePost);
        if (DaliangangContext.isDepartmentUser()) eruptRoleTemplatePost.setType("GOV");
        if (DaliangangContext.isEnterpriseUser()) eruptRoleTemplatePost.setType("ENTERPRISE");
        if (DaliangangContext.isEnterpriseEmployee()) eruptRoleTemplatePost.setType("ENTERPRISE");
    }

    private void checkOrgCode(EruptRoleTemplatePost eruptRoleTemplatePost) {
        if (StringUtils.isEmpty(eruptRoleTemplatePost.getOrgCode())) {
            EruptOrg eruptOrg = eruptUserService.getCurrentEruptUser().getEruptOrg();
            if (eruptOrg != null)
                eruptRoleTemplatePost.setOrgCode(eruptOrg.getCode());
        }
    }

    @Override
    public void beforeUpdate(EruptRoleTemplatePost post) {
        //有.，说明是初始化岗位，则不可修改
        if(post.getCode().contains(".")){
            NotifyUtils.showErrorMsg("初始化岗位不可修改");
        }
        if (!DaliangangContext.isDepartmentAdmin() && StringUtils.isNotEmpty(post.getType()) && post.getType().equals("GOV")) {
            NotifyUtils.showErrorMsg("政府发布的岗位不可修改");
        }
    }

    @Override
    public void beforeAdd(EruptRoleTemplatePost eruptRoleTemplatePost) {
        eruptRoleTemplatePostService.beforeAdd(eruptRoleTemplatePost);
        this.checkOrgCode(eruptRoleTemplatePost);
    }

    @Override
    @Transactional //exclusive 为true 则为培训 需要同步
    public void afterAdd(EruptRoleTemplatePost eruptRoleTemplatePost) {

        if (eruptRoleTemplatePost.getExclusive()) {
            PostSyncEntity postSync = PostSyncEntity.builder()
                    .code(eruptRoleTemplatePost.getCode())
                    .orgCode(eruptRoleTemplatePost.getOrgCode())
                    .id(eruptRoleTemplatePost.getId())
                    .name(eruptRoleTemplatePost.getName())
                    .weight(eruptRoleTemplatePost.getWeight())
                    .reserved(eruptRoleTemplatePost.getReserved())
                    .delFlag(false)
                    .updateFlag(false)
                    .type(eruptRoleTemplatePost.getType())
                    .build();
            redisMQService.produce(RedisMQConst.DEFAULT_TOPIC + "_POST", postSync);
        }
        //如果是市级发布培训岗位，发布到基准岗位表
        if (DaliangangContext.isDepartmentAdmin() && eruptRoleTemplatePost.getExclusive()) {
            EruptPost post = new EruptPost();
            post.setCode(eruptRoleTemplatePost.getCode());
            post.setExclusive(eruptRoleTemplatePost.getExclusive());
            post.setName(eruptRoleTemplatePost.getName());
            post.setWeight(eruptRoleTemplatePost.getWeight());
            post.setType(eruptRoleTemplatePost.getType());
            eruptDao.persist(post);

            //所有企业隔离一份
            List<EruptOrg> eruptOrgs = EruptDaoUtils.selectOnes("select * from e_upms_org where parent_org_id>0", EruptOrg.class);
            eruptOrgs.forEach(eruptOrg -> {
                EruptRoleTemplatePost entPost = new EruptRoleTemplatePost();
                entPost.setName(post.getName());
                entPost.setExclusive(post.isExclusive());
                entPost.setWeight(post.getWeight());
                entPost.setReserved(true);
                entPost.setCode(post.getCode());
                entPost.setOrgCode(eruptOrg.getCode());
                entPost.setType(post.getType());
                eruptDao.persist(entPost);
            });

            String sql = " code = '"+ eruptRoleTemplatePost.getCode()  + "'";
            List<EruptRoleTemplatePost> eruptRoleTemplatePosts = eruptDao.queryEntityList(EruptRoleTemplatePost.class,sql);
            for (EruptRoleTemplatePost subPost : eruptRoleTemplatePosts) {
                PostSyncEntity subPostSync = PostSyncEntity.builder()
                        .code(subPost.getCode())
                        .orgCode(subPost.getOrgCode())
                        .id(subPost.getId())
                        .name(subPost.getName())
                        .delFlag(false)
                        .updateFlag(false)
                        .type(subPost.getType())
                        .build();
                redisMQService.produce(RedisMQConst.DEFAULT_TOPIC + "_POST", subPostSync);
            }

        }


    }

    @Override
    public void afterDelete(EruptRoleTemplatePost eruptRoleTemplatePost) {

        PostSyncEntity postSync = PostSyncEntity.builder()
                .code(eruptRoleTemplatePost.getCode())
                .orgCode(eruptRoleTemplatePost.getOrgCode())
                .id(eruptRoleTemplatePost.getId())
                .name(eruptRoleTemplatePost.getName())
                .delFlag(true)
                .updateFlag(true)
                .type(eruptRoleTemplatePost.getType())
                .build();
        redisMQService.produce(RedisMQConst.DEFAULT_TOPIC + "_POST", postSync);

        //如果是市级发布的培训岗位，要级联删除
        if (DaliangangContext.isDepartmentAdmin() && eruptRoleTemplatePost.getExclusive()) {

            String sql = " code = '"+ eruptRoleTemplatePost.getCode()  + "'";
            List<EruptRoleTemplatePost> eruptRoleTemplatePosts = eruptDao.queryEntityList(EruptRoleTemplatePost.class,sql);
            for (EruptRoleTemplatePost post : eruptRoleTemplatePosts) {
                PostSyncEntity subPostSync = PostSyncEntity.builder()
                        .code(post.getCode())
                        .orgCode(post.getOrgCode())
                        .id(post.getId())
                        .name(post.getName())
                        .delFlag(true)
                        .updateFlag(true)
                        .type(post.getType())
                        .build();
                redisMQService.produce(RedisMQConst.DEFAULT_TOPIC + "_POST", subPostSync);
            }


            String sysPostDelSql = "delete from e_upms_post where code=" + SqlUtils.wrapStr(eruptRoleTemplatePost.getCode());
            String entPostDelSql = "delete from e_upms_post_template where code=" + SqlUtils.wrapStr(eruptRoleTemplatePost.getCode());
            EruptDaoUtils.updateNoForeignKeyChecks(sysPostDelSql, entPostDelSql);
        }



    }

    @Override
    public void editBehavior(EruptRoleTemplatePost eruptRoleTemplatePost) {
//        this.beforeUpdate(eruptRoleTemplatePost);
    }

    @Override
    public void afterUpdate(EruptRoleTemplatePost eruptRoleTemplatePost) {
        //如果是市级发布的培训岗位，要级联更新
        if (DaliangangContext.isDepartmentAdmin() && eruptRoleTemplatePost.getExclusive()) {
            String sql = "update e_upms_post_template set name=" + SqlUtils.wrapStr(eruptRoleTemplatePost.getName())
                    + ", weight=" + eruptRoleTemplatePost.getWeight()
                    + " where code=" + SqlUtils.wrapStr(eruptRoleTemplatePost.getCode());
            EruptDaoUtils.updateNoForeignKeyChecks(sql);
        }

        PostSyncEntity postSync = PostSyncEntity.builder()
                .code(eruptRoleTemplatePost.getCode())
                .orgCode(eruptRoleTemplatePost.getOrgCode())
                .id(eruptRoleTemplatePost.getId())
                .name(eruptRoleTemplatePost.getName())
                .weight(eruptRoleTemplatePost.getWeight())
                .reserved(eruptRoleTemplatePost.getReserved())
                .delFlag(false)
                .updateFlag(true)
                .type(eruptRoleTemplatePost.getType())
                .build();
        redisMQService.produce(RedisMQConst.DEFAULT_TOPIC + "_POST", postSync);

    }

    @Override
    public void beforeDelete(EruptRoleTemplatePost eruptRoleTemplatePost) {
        AssertUtils.isFalse(eruptRoleTemplatePost.getReserved(), "系统默认岗位不能删除");
        this.beforeUpdate(eruptRoleTemplatePost);
        eruptRoleTemplatePostService.beforeDelete(eruptRoleTemplatePost);
    }

    @Override
    public String beforeFetch(List<Condition> conditions) {
        TimeRecorder timeRecorder = TimeRecorder.builder().name("getCurrentEruptUser").build();
        EruptUser eruptUser = eruptUserService.getCurrentEruptUser();
        timeRecorder.printRecorder();
        if (eruptUser == null) return null;
        EruptOrg eruptOrg = eruptUser.getEruptOrg();
        if (eruptOrg == null) return null;
        return "org_code='" + eruptOrg.getCode() + "'";
    }
}
