package com.daliangang.datascreen.response;

import lombok.Data;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import java.util.Map;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.ArrayList;

@Data
@Builder(toBuilder = true)
@NoArgsConstructor
@AllArgsConstructor
public class StatisticsGroup {
    // 分组标题（如"备案管理"）
    private String groupTitle;

    private String content;
    // 统计项列表
    @Builder.Default
    private List<StatisticsItem> items = new ArrayList<>();

    @Data
    @Builder(toBuilder = true)
    @NoArgsConstructor
    @AllArgsConstructor
    public static class StatisticsItem {
        // 主标题（如"应急预案"）
        private String title;
        // 主数量
        private Integer count;
        // 单位
        private String unit;
        private String description;
        // 子项统计数据
        private List<SubStatValue> subStats;
    }
    @Data
    @Builder(toBuilder = true)
    @NoArgsConstructor
    @AllArgsConstructor
    public static class SubStatValue {
        private String subTitle;
        private Integer count;
        private String unit;
        private String description;
    }
}