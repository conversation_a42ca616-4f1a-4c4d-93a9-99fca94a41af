package com.daliangang.workbench.onelines;

import cn.hutool.json.JSONUtil;
import com.dlywxt.external.sso.sdk.bean.TokenDetail;
import com.dlywxt.external.sso.sdk.constants.AuthorizationConstants;
import com.dlywxt.external.sso.sdk.util.SsoUtils;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.*;
import xyz.erupt.core.util.EruptSpringUtil;
import xyz.erupt.devtools.env.EruptSiteConfig;
import xyz.erupt.devtools.env.EruptSiteConfigController;
import xyz.erupt.jpa.dao.EruptDao;
import xyz.erupt.toolkit.service.EruptCacheRedis;
import xyz.erupt.toolkit.utils.SqlUtils;
import xyz.erupt.upms.base.LoginModel;
import xyz.erupt.upms.controller.EruptUserController;
import xyz.erupt.upms.model.EruptUser;
import xyz.erupt.upms.platform.EruptOption;
import xyz.erupt.upms.platform.EruptOptions;
import xyz.erupt.upms.platform.PlatformOption;
import xyz.erupt.upms.service.EruptPlatformService;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.transaction.Transactional;
import java.lang.reflect.Field;
import java.net.InetAddress;
import java.net.MalformedURLException;
import java.net.URL;
import java.net.UnknownHostException;
import java.time.LocalDateTime;
import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Slf4j
@RestController
@EruptOptions(
        {
                @EruptOption(name =OnesLineController.ONELINE_SECURITY, value = "6952a155a4c614a4f0cc536c3be92e0df39417560c9d17849e33887fd40e57bf", desc = "一网协同解密密钥"),
                @EruptOption(name =OnesLineController.INTERNAL_IP, value = "xxx", desc = "系统内网ip"),
                @EruptOption(name =OnesLineController.FILE_DOMAIN_INTERNAL_IP, value = "xxx", desc = "系统文件内网ip"),
                @EruptOption(name =OnesLineController.INTERNAL_IP_FLAG, value = "false", desc = "系统内网ip开关"),
                @EruptOption(name =OnesLineController.ONELINE_TEST_TOKEN_FLAG, value = "false", desc = "一网协同测试token开关"),
                @EruptOption(name =OnesLineController.ONELINE_TEST_TOKEN, value = "xxx", desc = "一网协同测试token"),
                @EruptOption(name =OnesLineController.ONELINE_TEST_USER, value = "xxx", desc = "一网协同测试USER"),
                @EruptOption(name =OnesLineController.ONELINE_443_FLAG, value = "false", desc = "一网协同443开关"),
                @EruptOption(name =OnesLineController.ONELINE_TEST_URL_FLAG, value = "false", desc = "一网协同test接口打开开关"),
        }
)
public class OnesLineController {

    public final static String ONELINE_SECURITY = "ONELINE_SECURITY" ;

    public final static String INTERNAL_IP = "INTERNAL_IP" ;

    public final static String FILE_DOMAIN_INTERNAL_IP = "FILE_DOMAIN_INTERNAL_IP" ;


    public final static String INTERNAL_IP_FLAG = "INTERNAL_IP_FLAG" ;

    public final static String ONELINE_TEST_TOKEN_FLAG = "ONELINE_TEST_TOKEN_FLAG" ;

    public final static String ONELINE_TEST_TOKEN= "ONELINE_TEST_TOKEN" ;

    public final static String ONELINE_TEST_USER = "ONELINE_TEST_USER" ;


    public final static String ONELINE_443_FLAG = "ONELINE_443_FLAG" ;

    public final static String ONELINE_TEST_URL_FLAG = "ONELINE_TEST_URL_FLAG" ;



    @Resource
    private EruptDao eruptDao ;

    @Resource
    private EruptUserController eruptUserController ;
    @Resource
    private EruptPlatformService eruptPlatformService ;

    @Resource
    private EruptSiteConfig eruptSiteConfig;

    @Resource
    private EruptCacheRedis eruptCacheRedis;

    @Resource
    private EruptSiteConfigController eruptSiteConfigController ;

    /**
     * 通过code换取accessToken，如果换取成功，跳转到oauth_callback对应的地址
     * @param request
     * @param response
     * @throws Exception
     */
    @Transactional
    @RequestMapping(value =  "/redirect/getToken", method = RequestMethod.GET)
    public void getToken(final HttpServletRequest request, final HttpServletResponse response) throws Exception {
        OneLinesLog oneLinesLog = new OneLinesLog();
        oneLinesLog.setUrl(request.getQueryString());
        oneLinesLog.setCreateTime(LocalDateTime.now());
        oneLinesLog.setLog("进来了");
        eruptDao.merge(oneLinesLog);
        try{
            final String authCode = SsoUtils.getAuthCode(request);
            String oauth_callback = request.getParameter(AuthorizationConstants.OAUTH_CALLBACK);
            final boolean success = SsoUtils.exchangeToken(request, response, authCode);

            LoginModel login = null ;

            if (success) {
                TokenDetail tokenDetail = SsoUtils.checkToken(request, response);
                log.info("tokenDetail : "+ JSONUtil.toJsonStr(tokenDetail));
                if(tokenDetail == null){
                    saveLog(oneLinesLog,"tokenDetail is null ");
                    return ;
                }else if(StringUtils.isEmpty(tokenDetail.getAccountUuid())){
                    saveLog(oneLinesLog,"tokenDetail.getAccountUuid() is null ");
                    return ;
                }
                List<OnlineUser> onlineUsers = eruptDao.queryEntityList(OnlineUser.class, " phoneNumber is not null and personUuid = " + SqlUtils.wrapStr(tokenDetail.getPersonUuid()));
                if(onlineUsers == null || onlineUsers.isEmpty()){
                    saveLog(oneLinesLog,"onlineUsers is null or onlineUsers.size() == 0 ");
                    return ;
                }
                OnlineUser onlineUser = onlineUsers.get(0);
                //OnlineUser onlineUser = eruptDao.queryEntity(OnlineUser.class, "accountName = " + SqlUtils.wrapStr(tokenDetail.getUserName()));
                if(onlineUser == null || StringUtils.isEmpty(onlineUser.getPhoneNumber())){
                    saveLog(oneLinesLog,"onlineUser is null or onlineUser.getPhoneNumber() is null ");
                    return ;
                }else {
                    EruptUser eruptUser = eruptDao.queryEntity(EruptUser.class, "phone = "+SqlUtils.wrapStr(onlineUser.getPhoneNumber())) ;
                    if(eruptUser == null){
                        saveLog(oneLinesLog,"eruptUser is null ");
                        return ;
                    }
                    login = eruptUserController.login(eruptUser);
                    oneLinesLog.setCallbackResult("login success : "+JSONUtil.toJsonStr(login));
                    eruptDao.merge(oneLinesLog);
                }
                // OAUTH_CALLBACK作用就是把要跳转的地址传递给sso单点服务器，单点登录成功之后，会传递给当前服务的/redirect/getToken
                // 当前服务/redirect/getToken获取到token之后，再跳转到OAUTH_CALLBACK对应的地址
                // 这样就可以做到，用户访问什么页面，然后经过单点登录成功，再跳转到用户原先访问的页面
                if(login != null){
                    String tokenFlag = eruptPlatformService.getOption(ONELINE_TEST_TOKEN_FLAG).getParam();
                    String account = null ;
                    String token = null ;

                    if(Boolean.parseBoolean(tokenFlag)){
                        token = eruptPlatformService.getOption(ONELINE_TEST_TOKEN).getParam();
                        account = eruptPlatformService.getOption(ONELINE_TEST_USER).getParam() ;
                    }else {
                        if(login.getUser() == null){
                            saveLog(oneLinesLog,"login.getUser() is null ");
                            return ;
                        }
                        account = login.getUser().getAccount();
                        token = login.getToken();
                    }
                    String callback = oauth_callback + "?token=" + token + "&account=" + account;
                    response.sendRedirect(callback);
                    saveLog(oneLinesLog,"redirect to : "+callback);
                }

            } else {
                saveLog(oneLinesLog,"通过code从认证服务器换取token失败！");
            }
        }catch (Exception e){
            saveLog(oneLinesLog,"报错了："+e.getMessage());
        }

    }


    @Transactional
    public void saveLog(OneLinesLog oneLinesLog, String log) {
        oneLinesLog.setLog(log);
        eruptDao.merge(oneLinesLog);
    }

    /**
     * 通过code换取accessToken，如果换取成功，跳转到oauth_callback对应的地址
     * @param request
     * @param response
     * @throws Exception
     */
    @Transactional
    @RequestMapping(value =  "/test", method = RequestMethod.GET)
    public void test(final HttpServletRequest request, final HttpServletResponse response) throws Exception {
        String tokenFlag = eruptPlatformService.getOption(ONELINE_TEST_URL_FLAG).getParam();
        if (!Boolean.parseBoolean(tokenFlag)) {
            return ;
        }
        String oauth_callback = request.getParameter(AuthorizationConstants.OAUTH_CALLBACK);
        EruptUser eruptUser = eruptDao.queryEntity(EruptUser.class, "phone = "+SqlUtils.wrapStr("***********")) ;
        if(eruptUser == null){
            log.info("eruptUser is null ");
            return ;
        }

        LoginModel login = eruptUserController.login(eruptUser);
        String account = null ;
        String token = null ;

        if(login != null){
            account = login.getUser().getAccount();
            token = login.getToken();
        }
        String callback = oauth_callback + "?token=" + token + "&account=" + account;
        response.sendRedirect(callback);
        log.info("redirect to : "+callback);

    }



    @Transactional
    @RequestMapping(value = "/oneLines/getPersonData", method = RequestMethod.POST)
    public OneLinesResponse getPersonData(@RequestBody PushData pushData)  {
        log.info("/oneLines/getPersonData 接口获取到数据："+pushData);
        try{
            PlatformOption option = eruptPlatformService.getOption(ONELINE_SECURITY);
            String security = option.getParam();
            //注意：！!!  DataType.PERSON 的uuid 等于 DataType.ACCOUNT 的 personUuid
            String decryptData = SM2Utils.decrypt(security,pushData.getData());
            log.info("/oneLines/getPersonData 接口解密后的数据："+decryptData) ;

            OnlineUser onlineUser = JSONUtil.toBean(decryptData, OnlineUser.class);
            if(DataType.PERSON.equals(pushData.getDataType())){
                onlineUser.setPersonUuid(onlineUser.getUuid());
            }
            //保存数据
            this.saveData(onlineUser,pushData.getWorkType());

            //正确返回
            return OneLinesResponse.success() ;
        }catch (Exception e){
            log.error("/oneLines/getPersonData 接口报错了：",e);
            //错误返回
            return OneLinesResponse.failure("更新失败");
        }

    }

    public void saveData(OnlineUser onlineUser,WorkType workType) {
        OnlineUser onlineUser1 = eruptDao.queryEntity(OnlineUser.class, "uuid = " + SqlUtils.wrapStr(onlineUser.getUuid()));
        if (WorkType.DELETE.equals(workType)) {

            if(onlineUser1 !=null){
                // 删除操作
                log.info("/oneLines/getPersonData 删除数据：" + onlineUser);
                eruptDao.delete(onlineUser1);
            }

        } else {
            // 查询现有的在线用户实体

            boolean updateFlag = true ;
            if(onlineUser1 == null){
                onlineUser1 = new OnlineUser();
                updateFlag = false ;
            }
            // 更新现有实体的属性
            Field[] fields = onlineUser.getClass().getDeclaredFields();
            for (Field field : fields) {
                if("id".equals(field.getName())){
                    continue;
                }
                if (java.lang.reflect.Modifier.isStatic(field.getModifiers()) || java.lang.reflect.Modifier.isFinal(field.getModifiers())) {
                    // 跳过静态字段和final字段
                    continue;
                }
                field.setAccessible(true); // 允许访问私有字段
                try {
                    // 获取源对象的字段值
                    Object value = field.get(onlineUser);
                    // 将值赋给目标对象的对应字段
                    if(value != null){
                        field.set(onlineUser1, value);
                    }

                } catch (IllegalAccessException e) {
                    log.error("Error accessing field: " + field.getName(), e);
                } catch (IllegalArgumentException e) {
                    log.error("Error setting field: " + field.getName(), e);
                }
            }
            log.info("/oneLines/getPersonData 更新数据：" + onlineUser1);
            if(updateFlag){
                // 使用merge方法更新实体
                eruptDao.merge(onlineUser1);
            } else {
                log.info("/oneLines/getPersonData 新增数据：" + onlineUser);
                // 新增操作
                onlineUser.setId(null);
                eruptDao.persistAndFlush(onlineUser);
            }
        }


    }

    @GetMapping("/app.js")
    public String appJs(HttpServletRequest request) throws UnknownHostException{
//        String ipAddr = IpUtil.getIpAddr(request);
//        log.info("app.js 获取到 ipAddr : "+ipAddr);
        String flag = eruptPlatformService.getOption(INTERNAL_IP_FLAG).getParam();
        log.info("app.js 获取到 内网开关 : "+flag);

        String appJs = (String) eruptCacheRedis.getAndSet("app.js", TimeUnit.HOURS.toMillis(24), () -> {
            return eruptSiteConfigController.appJsImpl(false);
        });
        //要替换值,否则就原数据返回
        if(Boolean.parseBoolean(flag)){
            String oldDomain = extractValue(appJs,"domain");
            String newDomain =  getProtocolIpPort(request);
            log.info("app.js 获取到 domain : "+newDomain);
            boolean internalIP = isInternalIP(newDomain);
            log.info("app.js 获取到 internalIP : "+internalIP);
            if(internalIP){
                // 替换值 - 指定新的值
                String newFileDomain = eruptPlatformService.getOption(FILE_DOMAIN_INTERNAL_IP).getParam();
                appJs = replaceValue(appJs, "fileDomain", newFileDomain);
            }

            if(StringUtils.isNotEmpty(oldDomain)){
                appJs = appJs.replaceAll(oldDomain,newDomain);
            }

        }

        return appJs;

    }


    // 获取用户请求的协议、IP和端口号
    public static String getProtocolIpPort(HttpServletRequest request) {
        String scheme = request.getScheme(); // 获取协议，如 http 或 https
        String serverName = request.getServerName(); // 获取服务器的IP地址或主机名
        int serverPort = request.getServerPort(); // 获取端口号
        EruptPlatformService eruptPlatformService = EruptSpringUtil.getBean(EruptPlatformService.class);
        if(eruptPlatformService.getOption(ONELINE_443_FLAG).getParam().equals("true")){
            if(serverPort == 80 || serverPort == 443){
                return scheme + "://" + serverName;
            }
        }


        return scheme + "://" + serverName + ":" + serverPort;
    }

    // 提取值的方法
    public static String extractValue(String input, String key) {
        String regex = key + "\\s*:\\s*'([^']*)'";
        Pattern pattern = Pattern.compile(regex);
        Matcher matcher = pattern.matcher(input);
        if (matcher.find()) {
            return matcher.group(1);
        }
        return null;
    }

    // 替换值的方法
    public static String replaceValue(String input, String key, String newValue) {
        String regex = key + "\\s*:\\s*'([^']*)'";
        return input.replaceAll(regex, key + ": '" + newValue + "'");
    }
    public static boolean isInternalIP(String ip) throws UnknownHostException {
        InetAddress address  ;
        try {

            // 解析 URL，提取主机名
            URL url = new URL(ip);
            String host = url.getHost();

            // 通过主机名获取InetAddress
            address = InetAddress.getByName(host);


        } catch (MalformedURLException e) {
            System.out.println("URL 格式不正确: " + e.getMessage());
            return false ;
        } catch (java.net.UnknownHostException e) {
            System.out.println("无法解析主机名: " + e.getMessage());
            return false ;
        }

        byte[] byteAddress = address.getAddress();

        // 转换为无符号整数
        int firstByte = byteAddress[0] & 0xFF;
        int secondByte = byteAddress[1] & 0xFF;

        // 10.x.x.x
        if (firstByte == 10) {
            return true;
        }

        // 172.16.x.x - 172.31.x.x
        if (firstByte == 172 && (secondByte >= 16 && secondByte <= 31)) {
            return true;
        }

        // 192.168.x.x
        if (firstByte == 192 && secondByte == 168) {
            return true;
        }

        // 不是内网IP
        return false;
    }

    @Data
    @AllArgsConstructor
    public static class PushData{

        //推送时间戳
        private Long timestamp	;
        //加密字符串
        private String data	;
        //数据类型
        private DataType dataType ;
        //操作类型
        private WorkType workType	;

    }

    @Data
    public static class OneLinesResponse {
        //状态
        private ResponseStatus status ;
        //200（成功），500（失败）
        private Integer code ;
        //错误信息
        private String errMsg ;

        public static OneLinesResponse failure(String errMsg){
            OneLinesResponse response = new OneLinesResponse();
            response.status = ResponseStatus.failure ;
            response.code = 500 ;
            response.errMsg = errMsg ;
            return response ;
        }

        public static OneLinesResponse success(){
            OneLinesResponse response = new OneLinesResponse();
            response.status = ResponseStatus.success ;
            response.code = 200 ;
            return response ;
        }

    }
    public enum DataType{
        //机构
        ORG,
        //部门
        DEP,
        //用户
        PERSON,
        //账号
        ACCOUNT
    }

    public enum WorkType{
        //新增
        INSERT,
        //更新
        UPDATE,
        //删除
        DELETE
    }

    public enum ResponseStatus {
        //成功
        success,
        //失败
        failure
    }
}
