/*
 * Copyright © 2020-2035 yundingyun All rights reserved.
 */
package com.daliangang.rndpub.proxy;

import com.daliangang.rndpub.entity.InspectionResults;
import com.daliangang.rndpub.entity.Procedure;
import com.daliangang.workbench.entity.Enterprise;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.stereotype.Service;
import xyz.erupt.annotation.fun.DataProxy;
import xyz.erupt.annotation.query.Condition;
import xyz.erupt.core.context.MetaContext;
import xyz.erupt.toolkit.utils.EruptDaoUtils;
import xyz.erupt.toolkit.utils.NotifyUtils;
import xyz.erupt.toolkit.utils.SqlUtils;

import java.time.LocalDate;
import java.time.ZoneId;
import java.util.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Service
@Slf4j
public class InspectionrResultsDataProxy implements DataProxy<InspectionResults> {

    public static String inspectionName;

    @Override
    public void addBehavior(InspectionResults inspectionrResults) {
        //待整改
        inspectionrResults.setRectificationStatus("TO_BE_RECTIFIED");
        //待审核
        inspectionrResults.setRectificationAuditStatus("NOT_Audited") ;
       // inspectionrResults.setDeadline(new Date());
        if (ObjectUtils.isNotEmpty(inspectionName)) {
          //  InspectorForm inspectorForm = EruptDaoUtils.selectOne("select name from tb_procedure where id =" + inspectionName, InspectorForm.class);
            inspectionrResults.setInspectionName(inspectionName);
        }

    }


    @Override
    public String beforeFetch(List<Condition> conditions) {
        //只能查看自己的数据
        AtomicReference<String> returnStr = new AtomicReference<>(" 1 = 1 and create_by = '"+MetaContext.getUser().getName()+"'") ;
        //检查对象查询条件
        Optional<Condition> checkObjectFirst = conditions.stream().filter(condition ->
            "company".equals(condition.getKey())
        ).findFirst();
        checkObjectFirst.ifPresent( f ->{
            returnStr.set(returnStr.get()+"and check_object like CONCAT('%'," + SqlUtils.wrapStr((String) f.getValue())+",'%')");
            conditions.remove(f) ;
        });
        //是否逾期查询条件
        Optional<Condition> beOverdueFirst = conditions.stream().filter(condition ->
                "beOverdue".equals(condition.getKey())
        ).findFirst();
        beOverdueFirst.ifPresent( f ->{
            returnStr.set((Boolean)f.getValue() ? returnStr.get()+" and date(now()) <=date(deadline)":returnStr.get()+" and date(now()) >date(deadline)");
            conditions.remove(f) ;
        });

        conditions.forEach(v->{
            if (v.getKey().equals("inspectionName")) {
                inspectionName = String.valueOf(v.getValue());
            }
        });

        return returnStr.get();
    }

    public static String getInspectionName() {
        return inspectionName;
    }

    @Override
    public void afterFetch(Collection<Map<String, Object>> list) {
//        Date now = new Date(System.currentTimeMillis());
        LocalDate localDateTime = LocalDate.now();
        ZoneId zoneId = ZoneId.systemDefault();
        Date now = Date.from(localDateTime.atStartOfDay().atZone(zoneId).toInstant());
        List<InspectionResults> fields = EruptDaoUtils.convert(list, InspectionResults.class);
        for (InspectionResults field : fields) {
//            String checkObject = field.getCheckObject();
//            System.out.println("检查对象为:"+checkObject);
            if(null != field.getDeadline()){
                EruptDaoUtils.updateAfterFetch(list, field.getId(), "beOverdue", now.compareTo(field.getDeadline()) <= 0);
//                EruptDaoUtils.updateAfterFetch(list, field.getId(), "checkObject", field.getCheckObject());
            }
        }

    }


//    @Override
//    @Transactional
//    public void excelImport(Object workbook) {
//        Workbook wb = (Workbook) workbook;
//        Sheet sheet = wb.getSheetAt(0);
//        EruptDao eruptDao = EruptDaoUtils.getEruptDao();
//        String entSql = "select distinct org_code,enterprise_name from tb_enterprise_information ";
//        List<EnterpriseInformation> enterprises = EruptDaoUtils.selectOnes(entSql, EnterpriseInformation.class);
//        Map<String, String> enterpriseMap = enterprises.stream().collect(Collectors.toMap(EnterpriseInformation::getEnterpriseName, EnterpriseInformation::getOrgCode));
//        for (int i = sheet.getFirstRowNum() + 1; i <= sheet.getLastRowNum(); i++) {
//            Row row = sheet.getRow(i);
//            String inspName = row.getCell(0).getStringCellValue();
//            String inspectionItems = row.getCell(2).getStringCellValue();  //检查事项
//            String checkFirst = row.getCell(3).getStringCellValue();  // 检查内容
//            Procedure procedure = eruptDao.queryEntity(Procedure.class, "name=" + SqlUtils.wrapStr(inspName));
//            AssertUtils.notNull(procedure, "无效的检查名称");
//            InspectionResultNumForm o = EruptDaoUtils.selectOne("select count(*) as num from tb_inspection_items where inspection_items= '" + inspectionItems + "'", InspectionResultNumForm.class);
//            if (o.getNum().equals("0")) {
//                NotifyUtils.showErrorDialog("无效的检查事项");
//            }
//            InspectionResultNumForm o1 = EruptDaoUtils.selectOne("select count(*) as num from tb_inspection_items where check_first= '" + checkFirst + "'", InspectionResultNumForm.class);
//            if (o1.getNum().equals("0")) {
//                NotifyUtils.showErrorDialog("无效的检查内容");
//            }
//            String orgCode = enterpriseMap.get(row.getCell(1).getStringCellValue());
//            String sql = "delete from tb_inspection_results where check_object=" + SqlUtils.wrapStr(orgCode)
//                    + " and inspection_name=" + procedure.getId()
//                    + " and inspection_items=" + SqlUtils.wrapStr(row.getCell(2).getStringCellValue());
//            eruptDao.getJdbcTemplate().execute(sql);
//        }
//    }

    @Override
    public void beforeUpdate(InspectionResults inspectionResults) {
        String checkObject = inspectionResults.getCheckObject();
        if(checkObject!=null){
            Pattern p = Pattern.compile("[\u4e00-\u9fa5]");
            Matcher m = p.matcher(checkObject);
            if(m.find()){
                String selectSql="select * from tb_enterprise where name='"+checkObject+"'";
                List<Enterprise> enterpriseList = EruptDaoUtils.selectOnes(selectSql, Enterprise.class);
                if(!enterpriseList.isEmpty()){
                    inspectionResults.setCheckObject(enterpriseList.get(0).getOrgCode());
                }

            }
        }

    }


    @Override
    public void beforeAdd(InspectionResults inspectionResults) {
//        if(inspectionResults.getInspectionResult()==null||inspectionResults.getInspectionResult().equals("")){
//            inspectionResults.setRectificationStatus("TO_BE_RECTIFIED");
//        }
        //待整改
        inspectionResults.setRectificationStatus("TO_BE_RECTIFIED");
        //待审核
        inspectionResults.setRectificationAuditStatus("NOT_Audited") ;
        //未发布
        inspectionResults.setPublishStatus(Boolean.FALSE);

        inspectionResults.setSubmitPerson(inspectionResults.getCreateBy());
        //判断逻辑
        judgeExist(inspectionResults);

    }


    /**
     * 判断是否可操作该条数据：只有自己提交的，才能操作
     * @param inspectionResults
     */
    public void checkOperable(InspectionResults inspectionResults){

        if(!MetaContext.getUser().getName().equals( inspectionResults.getCreateBy())){
            NotifyUtils.showWarnMsg("只能操作自己提交的数据");
        }

    }

    public void judgeExist(InspectionResults inspectionrResults){
        //获取检查名称
        String inspectionName = inspectionrResults.getInspectionName();
        //查找相应的检查流程
        String procedureSql=String.format("select * from tb_procedure where id=%s",Long.parseLong(inspectionName));
        Procedure procedure = EruptDaoUtils.selectOne(procedureSql,Procedure.class);
        if(procedure==null){
            NotifyUtils.showErrorDialog("检查名称不存在，请检查后重试！");
        }
//        String enterpriseName = DaliangangContext.getEnterpriseName(inspectionrResults.getCheckObject());
        //查找相应的检查流程的抽查对象
//        String enterpriseInformationSql=String.format("select * from tb_enterprise_information where procedure_id=%s and state=1",procedure.getId());
//        List<EnterpriseInformation> enterpriseInformations = EruptDaoUtils.selectOnes(enterpriseInformationSql, EnterpriseInformation.class);
//        Map<String, Long> enterpriseMap = enterpriseInformations.stream().collect(Collectors.toMap(EnterpriseInformation::getEnterpriseName, EnterpriseInformation::getId, (e1, e2) -> e1));
//        if(!enterpriseMap.containsKey(enterpriseName)){
//            NotifyUtils.showErrorDialog("无效的检查对象，请检查后重试！");
//        }
    }
}
