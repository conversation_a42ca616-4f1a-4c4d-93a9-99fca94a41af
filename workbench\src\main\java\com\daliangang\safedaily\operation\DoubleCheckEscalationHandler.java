package com.daliangang.safedaily.operation;

import cn.hutool.json.JSONObject;
import com.daliangang.emergency.entity.Drill;
import com.daliangang.safedaily.entity.DoubleCheckManagement;
import org.springframework.stereotype.Service;
import xyz.erupt.annotation.fun.OperationHandler;
import xyz.erupt.common.http.EruptRemoteUtils;
import xyz.erupt.jpa.dao.EruptDao;
import xyz.erupt.toolkit.remote.RemoteProxyService;
import xyz.erupt.toolkit.utils.NotifyUtils;

import javax.annotation.Resource;
import javax.transaction.Transactional;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * @Title: DoubleCheckEscalationHandler
 * <AUTHOR>
 * @Package com.daliangang.safedaily.operation
 * @Date 2024/3/7 19:28
 * @description: 双倒查管理上报类
 */
@Service
public class DoubleCheckEscalationHandler implements OperationHandler<DoubleCheckManagement,Void> {

    @Resource
    private RemoteProxyService remoteProxyService;

    @Resource
    private EruptDao eruptDao;

    @Override
    @Transactional
    public String exec(List<DoubleCheckManagement> data, Void unused, String[] param) {
        for (DoubleCheckManagement doubleCheckManagement:data) {
            doubleCheckManagement.setSubmitted(true);
            doubleCheckManagement.setUpdateTime(LocalDateTime.now());
            eruptDao.merge(doubleCheckManagement);

            JSONObject inputData = new JSONObject();
            inputData.set("clazz", "DoubleCheckManagement");
            inputData.set("insertData",doubleCheckManagement);
            EruptRemoteUtils.doPost(remoteProxyService.getMainServerUrl() + "/erupt-api/exchange/data/exchange", inputData, Map.class);
        }
        return NotifyUtils.getSuccessNotify("上报成功！");
    }
}
