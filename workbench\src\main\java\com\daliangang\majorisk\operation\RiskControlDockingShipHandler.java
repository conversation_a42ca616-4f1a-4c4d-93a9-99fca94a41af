package com.daliangang.majorisk.operation;

import com.daliangang.majorisk.entity.UnloadShip;
import com.daliangang.majorisk.form.PositionDockingForm;
import com.daliangang.majorisk.form.RiskControlDockingShipForm;
import com.daliangang.workbench.entity.EnterpriseRelevancy;
import com.google.gson.Gson;
import com.google.gson.JsonObject;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.stereotype.Service;
import xyz.erupt.core.config.GsonFactory;
import xyz.erupt.core.util.EruptSpringUtil;
import xyz.erupt.jpa.dao.EruptDao;
import xyz.erupt.openapi.core.OpenApi;
import xyz.erupt.openapi.core.OpenApiHandler;
import xyz.erupt.openapi.core.OpenApiModel;
import xyz.erupt.toolkit.db.EruptResultMap;
import xyz.erupt.toolkit.remote.RemoteProxyService;
import xyz.erupt.toolkit.utils.EruptDaoUtils;
import xyz.erupt.toolkit.utils.SqlUtils;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @since :2023/5/16:15:10
 */
@Service
@Slf4j
@OpenApi(api = "loadingShip",name = "对接装卸船",handler = RiskControlDockingShipHandler.class)
public class RiskControlDockingShipHandler implements OpenApiHandler {
    @Resource
    EruptDao eruptDao;
    @Resource
    private RemoteProxyService remoteProxyService;

    @Override
    @SneakyThrows
    public OpenApiModel exec(JsonObject request,String params) {

        log.info("接收装卸船原始数据 -> " + request);
        // 数据转换
        Gson gson = GsonFactory.getGson();

        DateTimeFormatter df = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

        RiskControlDockingShipForm shipForm = gson.fromJson(request, RiskControlDockingShipForm.class);
        log.info("接收装卸船处理后数据 -> " + shipForm);
        boolean isNew = false ;
        UnloadShip unloadShip = eruptDao.queryEntity(UnloadShip.class, "job_id=" + SqlUtils.wrapStr(shipForm.getJob_id()));
        if (!ObjectUtils.isNotEmpty(unloadShip)) {
            unloadShip = EruptSpringUtil.getBean(UnloadShip.class);
            unloadShip.setCancelState("0");
            isNew = true ;
            unloadShip.setCreateTime(LocalDateTime.now());
        }
        unloadShip.setUpdateTime(LocalDateTime.now());

        // 根据企业uuid获取对应企业数据
        EnterpriseRelevancy linkedTreeMaps = eruptDao.queryEntity(EnterpriseRelevancy.class, " sm_id = " + SqlUtils.wrapStr(shipForm.getCompanyId()));
        if (ObjectUtils.isNotEmpty(linkedTreeMaps)) {
            String orgCode = String.valueOf(linkedTreeMaps.getOrgCode());
            unloadShip.setCompany(orgCode);
            unloadShip.setOrgCode(orgCode);
        } else {
            EruptResultMap vo = new EruptResultMap();
            vo.put("提示：", "没有该企业信息！");
            return new OpenApiModel().setData(vo);
        }

        unloadShip.setName(shipForm.getName()!=null?shipForm.getName():"");
        if (isNew && ObjectUtils.isNotEmpty(shipForm.getBegin_time())) {
            Date parse = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").parse(shipForm.getBegin_time());
            unloadShip.setBeginTime(parse);
        }
        if (isNew && ObjectUtils.isNotEmpty(shipForm.getEnd_time())) {
            Date parse = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").parse(shipForm.getEnd_time());
            unloadShip.setEndTime(parse);
        }
        if (ObjectUtils.isNotEmpty(shipForm.getBegin_time())) {
            unloadShip.setAbTime(LocalDateTime.parse(shipForm.getBegin_time(),df));
        }

        if (ObjectUtils.isNotEmpty(shipForm.getEnd_time())) {
            unloadShip.setAeTime(LocalDateTime.parse(shipForm.getEnd_time(),df));
        }



        unloadShip.setGoodsName(shipForm.getGoods_name()!=null?shipForm.getGoods_name():"");
        unloadShip.setWeight(Double.valueOf(shipForm.getWeight()!=null?shipForm.getWeight():"0.0"));
        unloadShip.setWorkMode(shipForm.getWork_mode()!=null?shipForm.getWork_mode():"");
        unloadShip.setWorkType(shipForm.getWork_type()!=null?shipForm.getWork_type():"ship");
        unloadShip.setWorkState(shipForm.getWork_state()!=null?shipForm.getWork_state():"");

        if(isNew){
            unloadShip.setRiskDatabase(shipForm.getRisk_database() != null ?shipForm.getRisk_database():"");
        }

        unloadShip.setShip(shipForm.getShip()!=null?shipForm.getShip():"");
       // unloadShip.setAddress(shipForm.get);

        unloadShip.setGId(shipForm.getG_id()!= null?shipForm.getG_id():"");
        unloadShip.setGType(shipForm.getG_type()!=null?shipForm.getG_type():"");
        unloadShip.setJobId(shipForm.getJob_id());


        // 判断作业是否存在，存在修改没有添加
        UnloadShip merge = eruptDao.merge(unloadShip);

        // 删除作业和地点关联
        String sql1 = "delete from tb_unload_ship_tanks where unload_id = "+merge.getId();
        EruptDaoUtils.updateNoForeignKeyChecks(sql1);

        String sql2 = "delete from tb_unload_ship_yards where unload_id = "+merge.getId();
        EruptDaoUtils.updateNoForeignKeyChecks(sql2);

        String sql3 = "delete from tb_unload_ship_wares where unload_id = "+merge.getId();
        EruptDaoUtils.updateNoForeignKeyChecks(sql3);

        String sql4 = "delete from tb_unload_ship_berths where unload_id = "+merge.getId();
        EruptDaoUtils.updateNoForeignKeyChecks(sql4);

        String sql5 = "delete from tb_unload_ship_wharfs where unload_id = "+merge.getId();
        EruptDaoUtils.updateNoForeignKeyChecks(sql5);

        // 处理作业地点数据
        // 查询作业地点关联表，获取本系统作业地点id和类型
        if (ObjectUtils.isNotEmpty(unloadShip.getGId())) {   // 判断作业地点是否存在
            List<String> collect = Stream.of(unloadShip.getGId().split(",")).collect(Collectors.toList());
            String positionId = SqlUtils.wrapIn(collect);
            List<PositionDockingForm> positionDockingForms = EruptDaoUtils.selectOnes("select * from tb_position_docking where position_id "+positionId, PositionDockingForm.class);
            // 根据类型分别添加作业与作业地点的关联表
            if (ObjectUtils.isNotEmpty(positionDockingForms)) {

                positionDockingForms.forEach(v->{

                    switch (v.getPositionType()) {
                        case "1":  //储罐
                            String sqla1 = "insert into tb_unload_ship_tanks (unload_id,tanks_id) values ("+merge.getId()+","+v.getBId()+") ";
                            EruptDaoUtils.updateNoForeignKeyChecks(sqla1);
                            break;
                        case "2":  // 泊位
                            String sqla2 = "insert into tb_unload_ship_berths (unload_id,berths_id) values ("+merge.getId()+","+v.getBId()+") ";
                            EruptDaoUtils.updateNoForeignKeyChecks(sqla2);
                            break;
                        case "3":  //栈台
                            String sqla3 = "insert into tb_unload_ship_wharfs (unload_id,wharfs_id) values ("+merge.getId()+","+v.getBId()+") ";
                            EruptDaoUtils.updateNoForeignKeyChecks(sqla3);
                            break;
                        case "5":  // 堆场
                            String sqla5 = "insert into tb_unload_ship_yards (unload_id,yards_id) values ("+merge.getId()+","+v.getBId()+") ";
                            EruptDaoUtils.updateNoForeignKeyChecks(sqla5);
                            break;
                        case "6":  // 仓库
                            String sqla6 = "insert into tb_unload_ship_wares (unload_id,wares_id) values ("+merge.getId()+","+v.getBId()+") ";
                            EruptDaoUtils.updateNoForeignKeyChecks(sqla6);

                            break;
                    }
                });
            }
        }

        LocalDateTime now = LocalDateTime.now();

        EruptResultMap vo = new EruptResultMap();
        vo.put("企业id", merge.getId());
        vo.put("返回时间", now);
        log.info("装卸船处理后数据-> 企业id：" + merge.getId()+",返回时间："+now +",对应数据:"+shipForm);
        return new OpenApiModel().setData(vo);
    }
}
